service: service-rejections-handler

provider:
  stage: ${self:custom.file.provider.environment}
  name: aws
  runtime: java11
  region: us-east-1
  profile: ${self:custom.file.provider.profile}
  versionFunctions: false
  deploymentBucket:
    name: simon-dev-local-serverless

custom:
  localstack:
    stages:
      - dev-local
    host: http://localstack-svc.${env:OKTETO_NAMESPACE}
    edgePort: 4566
    debug: true
  file: ${file(../config/okteto.yml)}
  newRelic:
    javaNewRelicHandler: handleStreamsRequest


plugins:
  - serverless-localstack
  - serverless-deployment-bucket

package:
  artifact: target/serverless-global-rejections-event-handler-uber.jar

functions:
  api:
    handler: com.simonmarkets.networks.rejections.GlobalRejectionReasonsEventHandler
    # memorySize: 1024
    timeout: 300
    events:
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusGeneralRejectionReasons}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"
            detail:
              ns:
                db:
                  - ${self:custom.file.dbGeneralRejectionReasons}
                coll:
                  - ${self:custom.file.collectionGeneralRejectionReasons}
          inputTransformer:
            inputPathsMap:
              id: "$.id"
              time: "$.time"
              detail: "$.detail"
            inputTemplate: '{"id": <id>, "time": <time>, "detail":<detail>}'
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=okteto.conf
