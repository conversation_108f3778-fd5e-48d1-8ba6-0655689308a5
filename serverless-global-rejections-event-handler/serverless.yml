service: service-rejections-handler

provider:
  stage: ${opt:stage, 'sandbox'}
  name: aws
  runtime: java11
  region: us-east-1
  profile: ${self:custom.file.provider.profile}
  iam:
    role: ${self:custom.file.provider.iam.role}
  versionFunctions: false
  tracing:
    lambda: true
  vpc:
    securityGroupIds: ${self:custom.file.provider.securityGroupIds}
    subnetIds: ${self:custom.file.provider.subnetIds}
  deploymentBucket:
    name: simon-${self:custom.file.provider.profile}-serverless
  logRetentionInDays: ${self:custom.file.logRetentionInDays}

custom:
  file: ${file(../config/${opt:stage}.yml)}
  logSubscription: ${self:custom.file.logSubscription}
  newRelic:
    javaNewRelicHandler: handleStreamsRequest


plugins:
  - serverless-plugin-log-subscription

package:
  artifact: target/serverless-global-rejections-event-handler-uber.jar

functions:
  api:
    handler: com.simonmarkets.networks.rejections.GlobalRejectionReasonsEventHandler
    memorySize: 1024
    timeout: 60
    events:
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusGeneralRejectionReasons}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"
            detail:
              ns:
                db:
                  - ${self:custom.file.dbGeneralRejectionReasons}
                coll:
                  - ${self:custom.file.collectionGeneralRejectionReasons}
          inputTransformer:
            inputPathsMap:
              id: "$.id"
              time: "$.time"
              detail: "$.detail"
            inputTemplate: '{"id": <id>, "time": <time>, "detail":<detail>}'
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=${opt:stage}.conf ${self:custom.file.proxySystemProperty, ''}