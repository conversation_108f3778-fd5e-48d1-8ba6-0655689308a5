package com.simonmarkets.networks.rejections.repository.mongo

import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.rejections.domain.{NetworkRejectionReasons, RejectionReason}
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.model.Filters
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, Matchers, OptionValues}

import java.time.Instant
import java.time.temporal.ChronoUnit

import scala.concurrent.Await
import scala.concurrent.duration._

class MongoNetworkRejectionReasonsRepositorySpec extends AsyncWordSpec with EmbeddedMongoLike with BeforeAndAfterEach
  with Matchers with OptionValues {

  implicit val traceId: TraceId = TraceId("mongo-rejection-reason-repo-spec")

  private val codecRegistry = fromRegistries(fromProviders(classOf[NetworkRejectionReasons], classOf[RejectionReason]),
    DEFAULT_CODEC_REGISTRY)

  private lazy val collection = db.getCollection[NetworkRejectionReasons]("networkRejections")
    .withCodecRegistry(codecRegistry)

  private lazy val repository = new MongoNetworkRejectionReasonsRepository(collection)

  private val duration = 5.seconds
  private val userId = "user-id"
  private val now = Instant.now().truncatedTo(ChronoUnit.MILLIS)

  private val reason1 = RejectionReason(
    id = "reason-id-1",
    reason = "Some reason",
    group = "Some group",
    userCreated = userId,
    timeCreated = now,
    userLastUpdated = userId,
    timeLastUpdated = now,
    version = 1,
    traceId = traceId.toString
  )

  private val reason2 = reason1.copy(
    id = "reason-id-2",
    reason = "Some reason 2",
    group = "Some group 2",
  )

  private val reason3 = reason1.copy(
    id = "reason-id-custom",
    reason = "Some reason 3",
    group = "Some group 3",
  )

  private val networkRejection1 = NetworkRejectionReasons(
    network = "network-1",
    reasons = Seq(
      reason1,
      reason2
    ),
  )

  private val networkRejection2 = NetworkRejectionReasons(
    network = "network-2",
    reasons = Seq(
      reason1,
      reason2,
      reason3
    ),
  )

  private val updatedReason = reason1.copy(
    id = "reason-id-2",
    reason = "My reason upd",
    group = "My group upd",
    userLastUpdated = "user-2",
    timeLastUpdated = Instant.now.truncatedTo(ChronoUnit.MILLIS)
  )

  override def beforeEach(): Unit = {
    Await.result(collection.drop().toFuture, duration)
    Await.result(collection.insertMany(Seq(networkRejection1, networkRejection2)).toFuture, duration)
  }

  "MongoNetworkRejectionReasonsRepository" can {
    "update" should {
      "update reasons for all networks" in {

        for {
          updatedCount <- repository.update("reason-id-2", updatedReason)
          net1 <- collection.find(networkCondition(networkRejection1.network)).headOption()
          net2 <- collection.find(networkCondition(networkRejection2.network)).headOption()
        } yield {
          updatedCount should be(2)
          net1.value.reasons should contain only(reason1, updatedReason)
          net2.value.reasons should contain only(reason1, updatedReason, reason3)
        }
      }

      "do nothing if reason with id does not exists" in {
        for {
          updatedCount <- repository.update("non-existing", updatedReason)
          net1 <- collection.find(networkCondition(networkRejection1.network)).headOption()
          net2 <- collection.find(networkCondition(networkRejection2.network)).headOption()
        } yield {
          updatedCount should be(0)
          net1.value should equal (networkRejection1)
          net2.value should equal(networkRejection2)
        }
      }
    }

    "delete" should {
      "delete reason from all networks" in {
        for {
          updatedCount <- repository.delete(reason2.id)
          net1 <- collection.find(networkCondition(networkRejection1.network)).headOption()
          net2 <- collection.find(networkCondition(networkRejection2.network)).headOption()
        } yield {
          updatedCount shouldBe 2
          net1.value.reasons should contain only reason1
          net2.value.reasons should contain only(reason1, reason3)
        }
      }

      "do nothing if reason with id does not exists" in {
        for {
          updatedCount <- repository.delete("non-existing")
          net1 <- collection.find(networkCondition(networkRejection1.network)).headOption()
          net2 <- collection.find(networkCondition(networkRejection2.network)).headOption()
        } yield {
          updatedCount should be(0)
          net1.value should equal (networkRejection1)
          net2.value should equal(networkRejection2)
        }
      }
    }
  }

  private def networkCondition(networkId: String) = Filters.equal("network", networkId)
}