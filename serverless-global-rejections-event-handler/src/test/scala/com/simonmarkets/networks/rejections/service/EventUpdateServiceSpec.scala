package com.simonmarkets.networks.rejections.service

import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.mongodb.config.{Authentication, MongoClientConfig, MongoConnectionConfig}
import com.simonmarkets.networks.rejections.api._
import com.simonmarkets.networks.rejections.config.MongoConfig
import com.simonmarkets.networks.rejections.repository.NetworkRejectionReasonsRepository
import com.simonmarkets.networks.rejections.service.EventUpdateService.{UnexpectedCollection, UnexpectedOperation}
import org.mockito.Mockito._
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, Matchers}

import scala.concurrent.Future

class EventUpdateServiceSpec extends AsyncWordSpec with TraceLogging with MockitoSugar with Matchers with BeforeAndAfterEach {

  private implicit val traceId: TraceId = TraceId(getClass.getSimpleName)

  private val networkRejectionsRepo = mock[NetworkRejectionReasonsRepository]
  private val mongoConfig = MongoConfig(databaseName = "pipg", networkRejectionReasonsCollection = "netRejections",
    generalRejectionReasonsCollection = "globalRejectionReasons", client = MongoClientConfig("app", MongoConnectionConfig("url",
      Authentication.None)))

  private val service = new EventUpdateService(networkRejectionsRepo, mongoConfig)

  override def beforeEach(): Unit = {
    reset(networkRejectionsRepo)
  }

  private def generateEvent(db: String = "pipg", coll: String = "globalRejectionReasons", operation: String) =
    Event(
      id = "event-id",
      time = "2020-07-03T17:31:01Z",
      detail = ReasonEventPayload(
        operationType = operation,
        fullDocument = if (operation == "delete") None else Some(RejectionReasonDocument(
          _id = "rej-id",
          group = "Rejection group",
          reason = "Reason 1",
          userCreated = "user-1",
          userLastUpdated = "user-2",
          timeCreated = "2020-07-03T10:20:32.927Z",
          timeLastUpdated = "2020-07-03T10:20:32.927Z",
          version = 1,
          traceId = traceId.toString()
        )),
        ns = MongoNs(
          db = db,
          coll = coll
        ),
        documentKey = DocumentKey(_id = "rej-id")
      )
    )

  "EventUpdateService" can {
    "handleEvent" should {
      "return error when collection in event is not expected" in {
        service.handleEvent(generateEvent(db = "learn", operation = "update")).map { r =>
          r should matchPattern { case Left(UnexpectedCollection(_)) => }
        }

        service.handleEvent(generateEvent(coll = "attestations", operation = "update")).map { r =>
          r should matchPattern { case Left(UnexpectedCollection(_)) => }
        }
      }

      "return error when operation is not expected" in {
        service.handleEvent(generateEvent(operation = "insert")).map { r =>
          r should matchPattern { case Left(UnexpectedOperation(_)) => }
        }
      }

      "delete rejection reason from networks if operation is delete" in {
        val event = generateEvent(operation = "delete")
        when(networkRejectionsRepo.delete(event.detail.documentKey._id)).thenReturn(Future.successful[Long](20))

        service.handleEvent(event).map { r =>
          r should be(Right(20))
        }
      }

      "update rejection reason when operation is update" in {
        val event = generateEvent(operation = "update")

        when(networkRejectionsRepo.update(event.detail.documentKey._id, event.detail.fullDocument.map(_.toDomain).get))
          .thenReturn(Future.successful[Long](21))

        service.handleEvent(event).map { r =>
          r should be(Right(21))
        }
      }

      "update rejection reason when operation is replace" in {
        val event = generateEvent(operation = "replace")

        when(networkRejectionsRepo.update(event.detail.documentKey._id, event.detail.fullDocument.map(_.toDomain).get))
          .thenReturn(Future.successful[Long](22))

        service.handleEvent(event).map { r =>
          r should be(Right(22))
        }
      }
    }
  }

}
