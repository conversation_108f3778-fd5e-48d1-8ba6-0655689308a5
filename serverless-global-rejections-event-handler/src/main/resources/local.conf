include classpath("common.conf")

mongo-db {
  database-name = "pipg"
  network-rejection-reasons-collection = "rejectionReasons"
  general-rejection-reasons-collection = "generalRejectionReasons"
  client {
    app-name = "serverless-rejection-event-handler"
    connection {
      url = "mongodb://localhost:27017/pipg?retryWrites=true&w=majority&readPreference=secondaryPreferred"
      authentication {
        type = none
      }
    }
  }
}
