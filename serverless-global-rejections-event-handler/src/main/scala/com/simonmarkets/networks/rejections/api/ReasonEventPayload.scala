package com.simonmarkets.networks.rejections.api

import com.simonmarkets.networks.rejections.domain.RejectionReason

import java.time.Instant

case class Event(
    id: String,
    time: String, // "2020-07-03T17:31:01Z",
    detail: ReasonEventPayload
)

// see documentation here:  https://docs.mongodb.com/manual/reference/change-events/
case class ReasonEventPayload(
    operationType: String,
    fullDocument: Option[RejectionReasonDocument],
    ns: MongoNs,
    documentKey: DocumentKey
)

case class RejectionReasonDocument(
    _id: String,
    reason: String,
    group: String,
    userCreated: String,
    timeCreated: String, // "2020-07-03T10:20:32.927Z"
    userLastUpdated: String,
    timeLastUpdated: String,
    traceId: String,
    version: Int
) {

  def toDomain: RejectionReason = {
    RejectionReason(
      id = _id,
      reason = reason,
      group = group,
      userCreated = userCreated,
      timeCreated = Instant.parse(timeCreated),
      userLastUpdated = userLastUpdated,
      timeLastUpdated = Instant.parse(timeLastUpdated),
      version = version,
      traceId = traceId
    )
  }
}

case class MongoNs(
    db: String,
    coll: String
)

case class DocumentKey(_id: String)

