package com.simonmarkets.networks.rejections.repository.mongo

import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.networks.rejections.domain.{NetworkRejectionReasons, RejectionReason}
import com.simonmarkets.networks.rejections.repository.NetworkRejectionReasonsRepository
import org.mongodb.scala.bson.conversions.Bson
import org.mongodb.scala.bson.{BsonDocument, Document}
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.Updates._
import org.mongodb.scala.{MongoClient, MongoCollection}

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class MongoNetworkRejectionReasonsRepository(collection: MongoCollection[NetworkRejectionReasons])
  extends NetworkRejectionReasonsRepository with TraceLogging {

  override def update(id: String, reason: RejectionReason): Future[Long] = {

    collection.updateMany(
      equal("reasons._id", id),
      combine(
        set("reasons.$.group", reason.group),
        set("reasons.$.reason", reason.reason),
        set("reasons.$.timeCreated", reason.timeCreated),
        set("reasons.$.timeLastUpdated", reason.timeLastUpdated),
        set("reasons.$.userCreated", reason.userCreated),
        set("reasons.$.userLastUpdated", reason.userLastUpdated),
        set("reasons.$.traceId", reason.traceId),
        set("reasons.$.version", reason.version)
      )
    ).toFuture().map(_.getModifiedCount)
  }

  override def delete(id: String): Future[Long] = {
    val reasonsIdsCond: Bson = equal("_id", id)
    val reasonsCond = Document("reasons" -> reasonsIdsCond.toBsonDocument(classOf[BsonDocument], MongoClient.DEFAULT_CODEC_REGISTRY))

    val pullExpression: Bson = pullByFilter(reasonsCond.toBsonDocument)

    collection.updateMany(
      equal("reasons._id", id),
      pullExpression
    ).toFuture().map(_.getModifiedCount)
  }
}
