package com.simonmarkets.networks.rejections


import com.amazonaws.services.lambda.runtime.{Context, RequestStreamHandler}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.mongodb.Client
import com.simonmarkets.networks.rejections.api.Event
import com.simonmarkets.networks.rejections.config.EventHandlerConfig
import com.simonmarkets.networks.rejections.domain.NetworkRejectionReasons
import com.simonmarkets.networks.rejections.repository.NetworkRejectionReasonsRepository
import com.simonmarkets.networks.rejections.repository.mongo.MongoNetworkRejectionReasonsRepository
import com.simonmarkets.networks.rejections.service.EventUpdateService
import com.simonmarkets.utils.config.Resolvers.SecretsConfigOps
import com.typesafe.config.ConfigFactory
import io.circe.generic.auto._
import io.circe.parser.decode
import org.bson.codecs.configuration.CodecRegistries.fromRegistries
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.{MongoCollection, MongoDatabase}
import pureconfig._
import pureconfig.generic.auto._

import java.io.{InputStream, OutputStream}

import scala.concurrent.Await
import scala.concurrent.duration._


class GlobalRejectionReasonsEventHandler extends RequestStreamHandler with TraceLogging {
  private implicit val traceId: TraceId = TraceId("initialization")
  private val timeout: FiniteDuration = 10.seconds

  log.info("Loading config file")
  private val rawConfig = ConfigFactory.load().resolveSecrets()

  log.info("Parsing config")
  private val config = loadConfigOrThrow[EventHandlerConfig](rawConfig)

  private val codecRegistry = fromRegistries(DEFAULT_CODEC_REGISTRY)

  private val mongoDB: MongoDatabase = Client.create(config.mongoDB.client)
    .getDatabase(config.mongoDB.databaseName)
    .withCodecRegistry(codecRegistry)

  private val networkRejectionReasonsCollection: MongoCollection[NetworkRejectionReasons] =
    mongoDB.getCollection[NetworkRejectionReasons](config.mongoDB.networkRejectionReasonsCollection)

  private val networkRejectionReasonsRepository: NetworkRejectionReasonsRepository =
    new MongoNetworkRejectionReasonsRepository(networkRejectionReasonsCollection)

  private val service: EventUpdateService = new EventUpdateService(networkRejectionReasonsRepository, config.mongoDB)

  override def handleRequest(input: InputStream, output: OutputStream, context: Context): Unit = {
    implicit val traceId: TraceId = TraceId(context.getAwsRequestId)

    log.info("Received request")

    val bufferedSource = scala.io.Source.fromInputStream(input)
    val eventStr = bufferedSource.mkString
    bufferedSource.close()

    log.info(s"Event: $eventStr")

    decode[Event](eventStr) match {
      case Left(error) =>
        log.error(s"Cannot parse event: $eventStr", error)
      case Right(event) =>
        log.info(s"Event parsed. Id: ${event.id} Time: ${event.time}")
        Await.result(service.handleEvent(event)(event.detail.fullDocument.map(fullDoc => TraceId(fullDoc.traceId)).getOrElse(traceId)), timeout) match {
          case Right(updatedCount: Long) =>
            log.info(s"Successfully updated $updatedCount networks")
          case Left(error) =>
            log.error(s"Unexpected error: $error")
        }
    }
  }
}
