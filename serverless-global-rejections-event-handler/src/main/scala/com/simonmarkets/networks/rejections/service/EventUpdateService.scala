package com.simonmarkets.networks.rejections.service

import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.rejections.api.Event
import com.simonmarkets.networks.rejections.config.MongoConfig
import com.simonmarkets.networks.rejections.repository.NetworkRejectionReasonsRepository
import com.simonmarkets.networks.rejections.service.EventUpdateService.{InvalidPayload, ServiceError, UnexpectedCollection, UnexpectedOperation}

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class EventUpdateService(repository: NetworkRejectionReasonsRepository, mongoConfig: MongoConfig) extends TraceLogging {

  def handleEvent(event: Event)(implicit traceId: TraceId): Future[Either[ServiceError, Long]] = {
    if (event.detail.ns.db == mongoConfig.databaseName &&
      event.detail.ns.coll == mongoConfig.generalRejectionReasonsCollection) {
      val documentId = event.detail.documentKey._id
      event.detail.operationType match {
        case "delete" =>
          log.info(s"Removing event with id $documentId")
          repository.delete(documentId).map(Right(_))
        case "replace" =>
          log.info(s"Updating (replace operation) event with id $documentId")
          update(documentId, event)
        case "update" =>
          log.info(s"Updating (update operation) event with id $documentId")
          update(documentId, event)
        case op =>
          log.error(s"Unexpected operation: $op")
          Future.successful(Left(UnexpectedOperation(s"$op is not expected operation")))
      }
    } else {
      log.error(s"Unexpected collection: db=${event.detail.ns.db} " +
        s"collection=${event.detail.ns.coll}")
      Future.successful(Left(UnexpectedCollection(s"Event for unexpected collection received db=${event.detail.ns.db} " +
        s"collection=${event.detail.ns.coll}")))
    }
  }

  private def update(id: String, event: Event): Future[Either[ServiceError, Long]] = {
    event.detail.fullDocument.map { fullDoc =>
      repository.update(id, fullDoc.toDomain)
        .map(Right(_))
    } getOrElse (Future.successful(Left(InvalidPayload("Full document is absent"))))
  }
}

object EventUpdateService {

  sealed trait ServiceError

  case class UnexpectedOperation(message: String) extends ServiceError

  case class UnexpectedCollection(message: String) extends ServiceError

  case class InvalidPayload(message: String) extends ServiceError

}
