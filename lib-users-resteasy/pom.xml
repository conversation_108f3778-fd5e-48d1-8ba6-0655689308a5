<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.simonmarkets</groupId>
        <artifactId>users-networks</artifactId>
        <version>sandbox-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>lib-users-resteasy</artifactId>
    <version>sandbox-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
        </dependency>

        <dependency>
            <groupId>com.simonmarkets.resteasy</groupId>
            <artifactId>lib-resteasy-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.mongodb.scala</groupId>
                    <artifactId>mongo-scala-driver_2.12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.scalaland</groupId>
            <artifactId>chimney_${scala.version.short}</artifactId>
        </dependency>

        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-utils-scala</artifactId>
        </dependency>

        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>om-networks</artifactId>
        </dependency>

        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-http-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-asset-models</artifactId>
        </dependency>


        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-utils-mongo-casbah</artifactId>
        </dependency>

        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-logging-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.simon</groupId>
            <artifactId>lib-openapi-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets.quantcommon</groupId>
            <artifactId>lib-date</artifactId>
        </dependency>

        <!-- TEST DEPENDENCIES-->
        <dependency>
            <groupId>com.gs.marquee.pipg</groupId>
            <artifactId>test-support</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>de.flapdoodle.embed</groupId>
            <artifactId>de.flapdoodle.embed.mongo</artifactId>
            <!--BOM is on higher version which is not compatible-->
            <version>3.0.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>2.5.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.jsuereth</groupId>
            <artifactId>scala-arm_${scala.version.short}</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
                <configuration>
                    <args>
                        <arg>-deprecation</arg>
                        <arg>-feature</arg>
                        <arg>-language:experimental.macros</arg>
                        <arg>-language:higherKinds</arg>
                        <arg>-unchecked</arg>
                        <arg>-Xfatal-warnings</arg>
                        <arg>-Xlint:adapted-args</arg>
                        <arg>-Xlint:by-name-right-associative</arg>
                        <arg>-Xlint:constant</arg>
                        <arg>-Xlint:delayedinit-select</arg>
                        <arg>-Xlint:doc-detached</arg>
                        <arg>-Xlint:inaccessible</arg>
                        <arg>-Xlint:infer-any</arg>
                        <arg>-Xlint:missing-interpolator</arg>
                        <arg>-Xlint:nullary-override</arg>
                        <arg>-Xlint:nullary-unit</arg>
                        <arg>-Xlint:option-implicit</arg>
                        <arg>-Xlint:package-object-classes</arg>
                        <arg>-Xlint:poly-implicit-overload</arg>
                        <arg>-Xlint:private-shadow</arg>
                        <arg>-Xlint:stars-align</arg>
                        <arg>-Xlint:type-parameter-shadow</arg>
                        <arg>-Xlint:unsound-match</arg>
                        <arg>-Yno-adapted-args</arg>
                        <arg>-Ypartial-unification</arg>
                        <arg>-Ywarn-dead-code</arg>
                        <arg>-Ywarn-extra-implicit</arg>
                        <arg>-Ywarn-inaccessible</arg>
                        <arg>-Ywarn-infer-any</arg>
                        <arg>-Ywarn-nullary-override</arg>
                        <arg>-Ywarn-nullary-unit</arg>
                        <arg>-Ywarn-macros:after</arg>
                        <arg>-Ywarn-unused:implicits</arg>
                        <arg>-Ywarn-unused:imports</arg>
                        <arg>-Ywarn-unused:locals</arg>
                        <arg>-Ywarn-unused:params</arg>
                        <arg>-Ywarn-unused:patvars</arg>
                        <arg>-Ywarn-unused:privates</arg>
                    </args>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
                <configuration>
                    <parallel>false</parallel>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
