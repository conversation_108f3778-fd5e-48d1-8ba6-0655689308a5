<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="RunNetworksServiceLocal" type="Application" factoryName="Application">
    <option name="MAIN_CLASS_NAME" value="com.simonmarkets.networks.RunNetworksServiceLocal" />
    <module name="service-networks" />
    <option name="VM_PARAMETERS" value="-Daws.profile=default -Dconfig.file=service-networks/src/main/resources/local.conf" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.simonmarkets.networks.api.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <extension name="software.aws.toolkits.jetbrains.core.execution.JavaAwsConnectionExtension">
      <option name="credential" />
      <option name="region" />
      <option name="useCurrentConnection" value="false" />
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>