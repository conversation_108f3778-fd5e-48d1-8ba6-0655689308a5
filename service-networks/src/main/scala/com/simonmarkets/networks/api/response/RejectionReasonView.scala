package com.simonmarkets.networks.api.response

import com.simonmarkets.networks.rejections.domain.RejectionReason


case class RejectionReasonView(
                                id: String,
                                reason: String,
                                group: String,
                                version: Int
                              )

object RejectionReasonView {
  def apply(r: RejectionReason): RejectionReasonView =
    RejectionReasonView(
      id = r.id,
      reason = r.reason,
      group = r.group,
      version = r.version
    )
}

