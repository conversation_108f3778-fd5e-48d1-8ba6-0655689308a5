package com.simonmarkets.networks.api.response

import com.simonmarkets.networks.domain.ProductNomenclature

case class ProductNomenclatureView(
    id: String,
    name: String,
    networkId: String,
    version: Int
)

object ProductNomenclatureView {
  def apply(p: ProductNomenclature): ProductNomenclatureView =
    ProductNomenclatureView(
      id = p.id,
      name = p.name,
      networkId = p.networkId,
      version = p.version
    )
}