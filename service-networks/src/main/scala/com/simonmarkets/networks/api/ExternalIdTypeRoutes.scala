package com.simonmarkets.networks.api

import akka.http.scaladsl.server.Directives.complete
import akka.http.scaladsl.server.{ExceptionHandler, Route}
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.api.ExternalIdTypeRoutes.customExceptionHandler
import com.simonmarkets.networks.common.ExternalIdType
import com.simonmarkets.networks.common.encoders.ExternalIdTypeCodecs
import com.simonmarkets.networks.common.service.{ExternalIdTypeError, ExternalIdTypeService}
import com.simonmarkets.resteasy.{DirectivesWithCirce, HttpErrorHandler}
import com.simonmarkets.useracl.{UserAclAuthorizedDirective, UserAclAuthorizedDirectives}

case class ExternalIdTypeRoutes(
    service: ExternalIdTypeService,
    authorizationDirective: UserAclAuthorizedDirective
) extends TraceLogging with DirectivesWithCirce with ExternalIdTypeCodecs with UserAclAuthorizedDirectives {

  lazy val routes: Route = {
    handleExceptions(customExceptionHandler) {
      pathPrefix("simon" / "api" / "v1" / "external-id-type") {
        admin { (_traceId, _) =>
          implicit val traceId: TraceId = _traceId
          pathEnd {
            concat(
              get {
                complete(service.list)
              },
              put {
                entity(as[ExternalIdType]) { request => complete(service.upsert(request)) }
              }
            )
          } ~
            pathPrefix(Segment) { name =>
              concat(
                get {
                  complete(service.get(name))
                },
                delete {
                  complete(service.delete(name))
                }
              )
            }
        }
      }
    }
  }
}

object ExternalIdTypeRoutes {

  implicit val customExceptionHandler: ExceptionHandler = HttpErrorHandler.errorHandler.withFallback(
    ExceptionHandler {
      case ex: ExternalIdTypeError.NotFound => complete(HttpError.notFound(ex.pretty).httpResponse)
      case ex: ExternalIdTypeError.Internal => complete(HttpError.internalServerError(ex.pretty).httpResponse)
    }
  )
}
