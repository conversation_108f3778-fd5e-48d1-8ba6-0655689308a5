package com.simonmarkets.networks.api

import akka.http.scaladsl.server.{Directive0, Directive1, Route}
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.domain.NetworkVisuals
import com.simonmarkets.networks.common.service.NetworkVisualsService
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.useracl.{UserAclAuthorizedDirective, UserAclAuthorizedDirectives}
import simon.Id.NetworkId

case class NetworkVisualsRoutes(networkVisualsService: NetworkVisualsService,
    authorizationDirective: UserAclAuthorizedDirective)
  extends UserAclAuthorizedDirectives
    with DirectivesWithCirce
    with JsonCodecs
    with TraceLogging {

  def `GET /:network_id`: Directive1[String] = get & pathPrefix(Segment) & pathEndOrSingleSlash

  def `PUT /`: Directive0 = put

  def `DELETE /:network_id`: Directive1[String] = delete & pathPrefix(Segment) & pathEndOrSingleSlash

  val NETWORK_VISUALS: Directive0 = pathPrefix("simon" / "api" / "v1" / "network-visuals")

  lazy val routes: Route = {
    NETWORK_VISUALS {
      concat(
        admin { (_traceId, _) =>
          implicit val traceId: TraceId = _traceId
          concat(
            `PUT /` {
              entity(as[NetworkVisuals]) {
                request =>
                  complete(networkVisualsService.upsert(request))
              }
            },
            `DELETE /:network_id` {
              networkId =>
                complete(networkVisualsService.delete(NetworkId(networkId)))
            })
        },
        authorized { (_traceId, _) =>
          implicit val traceId: TraceId = _traceId
          `GET /:network_id` {
            networkId =>
              complete(networkVisualsService.get(NetworkId(networkId)))
            }
          })
    }
  }
}
