package com.simonmarkets.networks.api.response

import com.simonmarkets.networks.domain.Attestation

case class AttestationView(
    id: String,
    attestationText: String,
    customRoles: Option[Set[String]]
)

object AttestationView {
  def apply(r: Attestation): AttestationView =
    AttestationView(
      id = r.id,
      attestationText = r.attestationText,
      customRoles = r.customRoles
    )
}
