package com.simonmarkets.networks.api.request

import io.simon.openapi.annotation.Field.{MinLength, Required}

case class UpdateAttestationRequest(
                                     @Required
                                     @MinLength(1)
                                     attestationText: String,
                                     customRoles: Option[Set[String]] = None
                                   )
