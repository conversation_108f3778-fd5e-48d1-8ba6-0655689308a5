package com.simonmarkets.networks.api.response

import com.goldmansachs.marquee.pipg.ContactInfo
import com.simonmarkets.networks.domain.{NetworkEntities, ProprietaryIndex}

case class FlattenedNetworkEntitiesView(
    networkId: String,
    key: String,
    symbol: String,
    contractTypeWrapper: Option[String] = None,
    fullName: Option[String] = None,
    shortName: Option[String] = None,
    isRegS: Boolean = false,
    is3a2: Boolean = false,
    isNonNativeIssuerKeyOrLegacy: Boolean = false, //for NBC_GS and all
    contactInfo: Option[ContactInfo] = None,
    proprietaryIndices: Option[List[ProprietaryIndex]] = None
)

object FlattenedNetworkEntitiesView {
  def apply(networkEntities: NetworkEntities, keysOpt: Option[Set[String]], symbolsOpt: Option[Set[String]]): Set[FlattenedNetworkEntitiesView] = {
    val filteredEntities = (keysOpt, symbolsOpt) match {
      case (Some(keys), Some(symbols)) => networkEntities.entities.map(_.filter(entity => keys.contains(entity.key) || symbols.contains(entity.symbol)))
      case (Some(keys), None) => networkEntities.entities.map(_.filter(entity => keys.contains(entity.key)))
      case (None, Some(symbols)) => networkEntities.entities.map(_.filter(entity =>  symbols.contains(entity.symbol)))
      case (None, None) => networkEntities.entities
    }
    filteredEntities.getOrElse(Set.empty).map(entity =>
      FlattenedNetworkEntitiesView(
        networkId = networkEntities.networkId,
        key = entity.key,
        symbol = entity.symbol,
        contractTypeWrapper = entity.contractTypeWrapper,
        fullName = entity.fullName,
        shortName = entity.shortName,
        isRegS = entity.isRegS,
        is3a2 = entity.is3a2,
        isNonNativeIssuerKeyOrLegacy = entity.isNonNativeIssuerKeyOrLegacy,
        contactInfo = entity.contactInfo,
        proprietaryIndices = networkEntities.proprietaryIndices
      )
    )
  }
}
