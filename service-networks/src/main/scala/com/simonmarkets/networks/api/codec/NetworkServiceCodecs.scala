package com.simonmarkets.networks.api.codec

import com.simonmarkets.circe.{CirceNullAsEmptyDecoders, JsonCodecs}
import com.simonmarkets.networks.common.{ChangelogItem, NetworkView}
import com.simonmarkets.networks.common.api.{Page, UpdateLocationsRequest, UpsertNetworkRequest}
import io.circe.{Codec, Decoder, Json}
import io.circe.generic.semiauto.deriveCodec

import scala.annotation.nowarn

@nowarn("cat=deprecation")
trait NetworkServiceCodecs extends JsonCodecs with CirceNullAsEmptyDecoders {

  implicit lazy val networkViewCodec: Codec[NetworkView] = deriveCodec

  implicit lazy val networkViewPageCodec: Codec[Page[NetworkView]] = deriveCodec

  implicit lazy val changelogItemCodec: Codec[ChangelogItem] = deriveCodec

  implicit lazy val upsertNetworkRequestCodec: Codec[UpsertNetworkRequest] = deriveCodec

  implicit lazy val updateLocationsRequestCodec: Codec[UpdateLocationsRequest] = deriveCodec

  implicit def annuityEAppProviderParamsDecoder[A <: Map[String, Any]]: Decoder[Map[String, Any]] =
    Decoder.decodeJsonObject.map(x => x.toMap.map { case (k, json) => k -> fold(json) })

  private def fold(json: Json): Any = {
    json.fold(
      jsonNull = () => null,
      jsonBoolean = identity,
      jsonNumber = n => n.toLong.getOrElse(n.toDouble),
      jsonString = identity,
      jsonArray = a => a.map(js => fold(js)),
      jsonObject = x => x.toMap.map { case (k, js) => k -> fold(js) }
    )
  }

}
