package com.simonmarkets.networks.api

import akka.http.scaladsl.server.{Directive, Directive0, Directive1, Route}
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.networks.api.codec.ProductNomenclatureServiceCodecs
import com.simonmarkets.networks.api.request.{AddProductNomenclatureRequest, UpdateProductNomenclatureRequest}
import com.simonmarkets.networks.api.response.ProductNomenclatureView
import com.simonmarkets.networks.service.ProductNomenclatureService
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.useracl.{UserAclAuthorizedDirective, UserAclAuthorizedDirectives}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext

case class ProductNomenclatureServiceRoutes(productNomenclatureService: ProductNomenclatureService,
    authorizationDirective: UserAclAuthorizedDirective)
  (implicit ec: ExecutionContext)
  extends UserAclAuthorizedDirectives
    with DirectivesWithCirce
    with ProductNomenclatureServiceCodecs
    with JsonCodecs
    with TraceLogging {

  private final val productNomenclature = "product-nomenclature"

  def `GET /:network_id/product-nomenclature`: Directive1[String] = get & pathPrefix(Segment / productNomenclature) & pathEndOrSingleSlash

  def `POST /:network_id/product-nomenclature`: Directive1[String] = post & pathPrefix(Segment / productNomenclature) & pathEndOrSingleSlash

  def `GET /:network_id/product-nomenclature/:id`: Directive[(String, String)] = get & pathPrefix(Segment / productNomenclature / Segment) & pathEndOrSingleSlash

  def `PUT /:network_id/product-nomenclature/:id`: Directive[(String, String)] = put & pathPrefix(Segment / productNomenclature / Segment) & pathEndOrSingleSlash

  def `DELETE /:network_id/product-nomenclature/:id`: Directive[(String, String)] = delete & pathPrefix(Segment / productNomenclature / Segment) & pathEndOrSingleSlash

  val V2_NETWORKS: Directive0 = pathPrefix("simon" / "api" / "v2" / "networks")

  lazy val routes: Route = {
    authorized { (_, userAcl: UserACL) =>
      rejectEmptyResponse(
        V2_NETWORKS {
          `GET /:network_id/product-nomenclature` { networkId =>
            complete(productNomenclatureService.list(NetworkId(networkId))(userAcl).map(seq => seq.map(ProductNomenclatureView(_))))
          } ~
            `POST /:network_id/product-nomenclature` { networkId =>
              entity(as[AddProductNomenclatureRequest]) { request =>
                complete(productNomenclatureService.add(NetworkId(networkId), request.name)(userAcl).map(ProductNomenclatureView(_)))
              }
            } ~
            `GET /:network_id/product-nomenclature/:id` { (networkId, productNomenclatureId) =>
              complete(
                productNomenclatureService.get(NetworkId(networkId), productNomenclatureId)(userAcl)
                  .map(opt => opt.map(ProductNomenclatureView(_)))
              )
            } ~
            `PUT /:network_id/product-nomenclature/:id` { (networkId, productNomenclatureId) =>
              entity(as[UpdateProductNomenclatureRequest]) { request =>
                complete(
                  productNomenclatureService.update(NetworkId(networkId), productNomenclatureId, request.name)(userAcl)
                    .map(opt => opt.map(ProductNomenclatureView(_)))
                )
              }
            } ~
            `DELETE /:network_id/product-nomenclature/:id` { (networkId, productNomenclatureId) =>
              complete(
                productNomenclatureService.delete(NetworkId(networkId), productNomenclatureId)(userAcl).map(b => if (b) Some(Unit) else None)
              )
            }
        })
    }
  }
}
