package com.simonmarkets.networks.api

import akka.http.scaladsl.server.Route
import akka.http.scaladsl.unmarshalling.PredefinedFromStringUnmarshallers.CsvSeq
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.api.codec.NetworkServiceCodecs
import com.simonmarkets.networks.common.api.{EnsureEncryptionRequest, UpdateLocationsRequest, UpsertNetworkRequest}
import com.simonmarkets.networks.common.service.IcnKmsService.AccessToken
import com.simonmarkets.networks.common.service.networkservice.NetworkService
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.resteasy.authn.User
import com.simonmarkets.useracl.{UserAclAuthorizedDirective, UserAclAuthorizedDirectives}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext

case class NetworkServiceRoutes(
    networkService: NetworkService,
    authorizationDirective: UserAclAuthorizedDirective)
  (implicit ec: ExecutionContext)
  extends UserAclAuthorizedDirectives
    with TraceLogging
    with DirectivesWithCirce
    with NetworkServiceCodecs {

  // @formatter:off
  lazy val routes: Route =
    authorizedUser { (trace: TraceId, user: User, acl: UserACL) =>
      implicit val (tid, userAcl) = (trace, acl)
      implicit val token: AccessToken = AccessToken(user.token)
      (rejectEmptyResponse & pathPrefix("simon" / "api" / "v2" / "networks")) {
        (get & path("get-sparse-networks") & pathEnd) {
          complete(networkService.getNetworksWithPurview(userAcl))
        } ~
        (get & path("ids")) {
          /**
           * Called by `sp-contracts-offerings` service for admin users
           */
          complete(
            networkService
              .getNetworkIds(userAcl)
          )
        } ~
        (get & path("names")) {
          parameters("id".repeated) { ids =>
            val networkIds = ids.map(NetworkId(_)).toSet
            complete(networkService.getNetworkNames(userAcl, networkIds).map(_.map { case (id, name) => NetworkId.unwrap(id) -> name }))
          }
        } ~
          (patch & path("encryptionClientKey")) {
            entity(as[EnsureEncryptionRequest]) { request: EnsureEncryptionRequest =>
              complete(networkService.ensureEncryption(userAcl, request))
            }
          } ~
          (get & path("encryptionClientKey")) {
            parameters("id".repeated) { ids =>
              val networkIds = ids.map(NetworkId(_)).toSet
              complete(networkService.getEncryptionClientKeys(networkIds, userAcl))
            }
          } ~
          (get & path(Segment) & pathEnd) { id =>
            parameters("external".withDefault[Boolean](false), "subject".withDefault[String](userAcl.networkId.toString)) {
              (externalIdRequest: Boolean, subject: String) =>
                if (externalIdRequest) {
                  val externalId = ExternalId(subject, id)
                  complete(networkService.getNetworkViewByExternalId(userAcl, externalId))
                } else
                  complete(networkService.getNetworkViewById(userAcl, NetworkId(id)))
            }
          } ~
          (get & pathEnd) {
            parameters("limit".as[Int].?, "next".?, "ids".as(CsvSeq[String]).?, "subject".as[String].?, "locationsView".as[Boolean].?) {
              (limit: Option[Int], next: Option[String], ids: Option[Seq[String]], subject: Option[String], locationsView: Option[Boolean]) => {
                (ids, subject, locationsView) match {
                  case (None, None, None) => complete(networkService.getNetworkViewPage(userAcl, limit, next))
                  case (Some(ids), None, _) => complete(networkService.getNetworkViewPageByIds(userAcl, ids.toSet))
                  case (None, Some(subject), _) => complete(networkService.getNetworkViewPageBySubject(userAcl, subject))
                  case (Some(_), Some(subject), _) =>
                    log.info(s"Querying by subject. Cannot query by both subject and id.")
                    complete(networkService.getNetworkViewPageBySubject(userAcl, subject))
                  case (None, None, Some(_)) => complete(networkService.getNetworksNamesLocations(userAcl))
                }
              }
            }
          } ~
          (get & path(Segment / "snapshots") & pathEnd) { networkId =>
            complete(networkService.getChangelogs(NetworkId(networkId)))
          } ~
          (get & path(Segment / "snapshots" / Segment) & pathEnd) { (_, snapId) =>
            complete(networkService.getSnapshot(snapId))
          } ~
          (post & pathEndOrSingleSlash) {
            entity(as[UpsertNetworkRequest]) { request: UpsertNetworkRequest =>
              complete(networkService.handleInsertRequest(userAcl, request))
            }
          } ~
          (put & path(Segment) & pathEnd) { networkId =>
            entity(as[UpsertNetworkRequest]) { request: UpsertNetworkRequest =>
              complete(networkService.handleUpdateRequest(userAcl, NetworkId(networkId), request))
            }
          } ~
          (put & path(Segment / "locations") & pathEnd) { networkId =>
            entity(as[UpdateLocationsRequest]) { request: UpdateLocationsRequest =>
              complete(networkService.updateNetworkLocations(userAcl, NetworkId(networkId), request))
            }
          }

      }
    }
  // @formatter:on

}
