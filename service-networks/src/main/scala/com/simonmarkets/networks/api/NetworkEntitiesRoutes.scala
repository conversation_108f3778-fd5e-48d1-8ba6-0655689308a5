package com.simonmarkets.networks.api

import akka.http.scaladsl.server.{Directive0, Directive1, Route}
import akka.http.scaladsl.unmarshalling.FromStringUnmarshaller
import akka.http.scaladsl.unmarshalling.PredefinedFromStringUnmarshallers.CsvSeq
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.api.request.{AddNetworkEntitiesRequest, UpdateNetworkEntitiesRequest}
import com.simonmarkets.networks.service.NetworkEntitiesService
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.useracl.{UserAclAuthorizedDirective, UserAclAuthorizedDirectives}

case class NetworkEntitiesRoutes(networkEntitiesService: NetworkEntitiesService,
    authorizationDirective: UserAclAuthorizedDirective)
  extends UserAclAuthorizedDirectives with DirectivesWithCirce with JsonCodecs with TraceLogging {

  implicit def CsvSet[T](implicit unmarshaller: FromStringUnmarshaller[T]): FromStringUnmarshaller[Set[T]] =
    CsvSeq[T].map(_.toSet)

  def `GET /`: Directive0 = get

  def `GET /:network_id`: Directive1[String] = get & pathPrefix(Segment) & pathEndOrSingleSlash

  def `POST /:network_id`: Directive1[String] = post & pathPrefix(Segment) & pathEndOrSingleSlash

  def `PUT /:network_id`: Directive1[String] = put & pathPrefix(Segment) & pathEndOrSingleSlash

  def `DELETE /:network_id`: Directive1[String] = delete & pathPrefix(Segment) & pathEndOrSingleSlash

  def `GET /key/:id`: Directive1[String] = get & pathPrefix("key" / Segment) & pathEndOrSingleSlash

  def `GET /proprietary-indices`: Directive0 = get & pathPrefix("proprietary-indices") & pathEndOrSingleSlash

  val NETWORK_ENTITIES: Directive0 = pathPrefix("simon" / "api" / "v1" / "network-entities")

  lazy val routes: Route = {
    authorized { (_traceId: TraceId, _userAcl: UserACL) =>
      implicit val (traceId, userACL) = (_traceId, _userAcl)
      rejectEmptyResponse(
        NETWORK_ENTITIES {
          `GET /proprietary-indices` {
            complete(networkEntitiesService.getAllProprietaryIndices)
          } ~
          `GET /:network_id` { networkId =>
            complete(networkEntitiesService.getByNetworkId(networkId))
          } ~
          `POST /:network_id` { networkId =>
            entity(as[AddNetworkEntitiesRequest]) { request =>
              complete(networkEntitiesService.add(request, networkId))
            }
          } ~
          `PUT /:network_id` { networkId =>
            entity(as[UpdateNetworkEntitiesRequest]) { request =>
              complete(networkEntitiesService.update(request, networkId))
            }
          } ~
          `DELETE /:network_id` { networkId =>
            complete(networkEntitiesService.delete(networkId))
          } ~
          `GET /key/:id` { key =>
            complete(networkEntitiesService.getByKey(key))
          } ~
          `GET /` {
            parameters('networkIds.as[Set[String]].?, 'keys.as[Set[String]].?, 'symbols.as[Set[String]].?, 'isFlattenedView.as[Boolean].withDefault(false)) {
              (networkIdsOpt: Option[Set[String]], keysOpt: Option[Set[String]], symbolsOpt: Option[Set[String]], isFlattenedView: Boolean) => {
                if (isFlattenedView) {
                  complete(networkEntitiesService.listFlattenedView(networkIdsOpt, keysOpt, symbolsOpt))
                } else {
                  complete(networkEntitiesService.list(networkIdsOpt, keysOpt, symbolsOpt))
                }
              }
            }
          }
        })
    }
  }
}
