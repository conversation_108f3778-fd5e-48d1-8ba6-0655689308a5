package com.simonmarkets.networks.api

import com.simonmarkets.networks.common.NetworkView
import com.simonmarkets.networks.common.api.Page
import io.simon.openapi.annotation.{ClassReference, OpenApiType}
import io.simon.openapi.annotation.Field.{Description, Maximum, Minimum, Ref, Type, TypeArgRef}
import io.simon.openapi.definitions.CommonDefinitions.NetworkName

object OpenApiDefinitions {
  @Ref(ClassReference(classOf[Page[NetworkView]]))
  case object NetworkPageDefinition

  @Type(OpenApiType.Map)
  @TypeArgRef(NetworkName)
  case object NetworkIdToNameMap

  @Minimum(1)
  @Maximum(2000)
  @Type(OpenApiType.Int)
  @Description("Number of networks which should be returned on one page")
  case object Limit
}
