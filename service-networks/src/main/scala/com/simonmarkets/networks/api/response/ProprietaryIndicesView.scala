package com.simonmarkets.networks.api.response

import com.simonmarkets.networks.domain.{NetworkEntities, ProprietaryIndex}

case class ProprietaryIndicesView(
    networkId: String,
    proprietaryIndices: List[ProprietaryIndex]
)

object ProprietaryIndicesView {
  def apply(networkEntities: NetworkEntities): ProprietaryIndicesView =
    ProprietaryIndicesView(
      networkId = networkEntities.networkId,
      proprietaryIndices = networkEntities.proprietaryIndices.getOrElse(List.empty)
    )
}
