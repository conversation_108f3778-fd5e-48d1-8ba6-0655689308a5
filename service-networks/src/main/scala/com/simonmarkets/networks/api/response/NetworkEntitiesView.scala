package com.simonmarkets.networks.api.response

import com.simonmarkets.networks.domain.{Entity, NetworkEntities, ProprietaryIndex}

case class NetworkEntitiesView(
    networkId: String,
    entities: Set[Entity],
    version: Int,
    proprietaryIndices: Option[List[ProprietaryIndex]] = None,
)

object NetworkEntitiesView {
  def apply(networkEntity: NetworkEntities): NetworkEntitiesView = {
    NetworkEntitiesView(
      networkId = networkEntity.networkId,
      entities = networkEntity.entities.getOrElse(Set.empty),
      version = networkEntity.version,
      proprietaryIndices = networkEntity.proprietaryIndices
    )
  }
}
