package com.simonmarkets.networks.api.codec

import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.networks.api.request.{AddProductNomenclatureRequest, UpdateProductNomenclatureRequest}
import com.simonmarkets.networks.api.response.ProductNomenclatureView
import io.circe.Codec
import io.circe.generic.semiauto.deriveCodec

trait ProductNomenclatureServiceCodecs extends JsonCodecs {

  implicit lazy val productNomenclatureViewCodec: Codec[ProductNomenclatureView] = deriveCodec

  implicit lazy val addProductNomenclatureRequestCodec: Codec[AddProductNomenclatureRequest] = deriveCodec

  implicit lazy val updateProductNomenclatureRequestCodec: Codec[UpdateProductNomenclatureRequest] = deriveCodec

}
