package com.simonmarkets.networks.api

import akka.http.scaladsl.server.Route
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.circe.{CirceDecoders, JsonCodecs}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.api.request.{CreateAttestationRequest, UpdateAttestationRequest, UpdateDisclaimerRequest}
import com.simonmarkets.networks.api.response.{AttestationView, DisclaimerView}
import com.simonmarkets.networks.domain.DisclaimerType
import com.simonmarkets.networks.service.{NetworkAttestationService, NetworkDisclaimerService}
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.useracl.{UserAclAuthorizedDirective, UserAclAuthorizedDirectives}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext

class NetworkAttestationsRoutes(service: NetworkAttestationService,
                                disclaimerService: NetworkDisclaimerService,
                                override val authorizationDirective: UserAclAuthorizedDirective)
      (implicit ec: ExecutionContext) extends TraceLogging with
  DirectivesWithCirce with CirceDecoders with UserAclAuthorizedDirectives with JsonCodecs {

  def routes: Route = {
    authorized { (trace, acl) =>
      implicit val (traceId: TraceId, user: UserACL) = (trace, acl)
        pathPrefix("simon" / "api" / "v1" / "networks" / Segment) { networkId =>
          pathPrefix("attestations") {
            pathEnd {
              get {
                complete {
                  service.list(NetworkId(networkId), user)
                    .map(seq => seq.map(r => AttestationView(r)))
                }
              } ~
              post {
                entity(as[CreateAttestationRequest]) { request =>
                  complete {
                    service.create(NetworkId(networkId), request.attestationText, request.customRoles, user)
                      .map(attestation => AttestationView(attestation))
                  }
                }
              }
            } ~
            path(Segment) { attestationId =>
              delete {
                complete {
                  service.delete(NetworkId(networkId), attestationId, user).map(b => if (b) Some(Unit) else None)
                }
              } ~
              post {
                entity(as[UpdateAttestationRequest]) { request =>
                  complete {
                    service.update(NetworkId(networkId), attestationId, request.attestationText, request.customRoles, user)
                  }
                }
              }
            }
          } ~
          (pathPrefix("disclaimers") & parameter("type".as[DisclaimerType])) { disclaimerType =>
            pathEnd {
              get {
                rejectEmptyResponse(complete(
                  disclaimerService.get(NetworkId(networkId), disclaimerType).map(_.map(DisclaimerView(_)))
                ))
              } ~
              post {
                entity(as[UpdateDisclaimerRequest]) { request =>
                  rejectEmptyResponse(complete(
                    disclaimerService.update(NetworkId(networkId), disclaimerType, request.disclaimer)
                      .map(_.map(DisclaimerView(_)))
                  ))
                }
              } ~
              delete {
                rejectEmptyResponse(complete(
                  disclaimerService.delete(NetworkId(networkId), disclaimerType)
                ))
              }
            }
          }
        }
    }
  }
}
