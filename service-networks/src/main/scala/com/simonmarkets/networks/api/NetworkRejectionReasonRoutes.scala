package com.simonmarkets.networks.api

import akka.http.scaladsl.server.{Route, StandardRoute}
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.api.request.{CustomRejection, RejectionReasonIdList}
import com.simonmarkets.networks.service.NetworkRejectionReasonService
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.useracl.{UserAclAuthorizedDirective, UserAclAuthorizedDirectives}
import simon.Id.NetworkId
import io.circe.generic.auto._
import com.simonmarkets.networks.api.response.AddCustomRejectionResponse
import com.simonmarkets.networks.api.response.AddRejectionReasonsResponse
import com.simonmarkets.networks.api.response.RejectionReasonView


import scala.concurrent.{ExecutionContext, Future}

case class NetworkRejectionReasonRoutes(service: NetworkRejectionReasonService,
                                        authorizationDirective: UserAclAuthorizedDirective)
                                       (implicit ec: ExecutionContext) extends UserAclAuthorizedDirectives with TraceLogging with DirectivesWithCirce {

  // @formatter:off
  lazy val routes: Route =
    authorized { (trace: TraceId, user: UserACL) =>
      pathPrefix("simon" / "api" / "v1" / "networks" / Segment / "rejection-reasons") {
        networkId =>
          implicit val traceId = trace
          pathEnd {
            get(list(networkId, user)) ~
              post(add(networkId, user))
          } ~
            (pathPrefix("custom") & pathEnd & post) {
              addCustom(networkId, user)
            } ~
            (pathPrefix(Segment) & pathEnd & delete) {
              rejectionId =>
                deleteReason(networkId, rejectionId, user)
            }
      }
    }

  // @formatter:on
  def addCustom(networkId: String, user: UserACL)(implicit traceId: TraceId): Route =
    entity(as[CustomRejection]) { request: CustomRejection =>
      complete(
        service.addCustom(NetworkId(networkId), request.reason, request.group, user)
          .map(id => AddCustomRejectionResponse(id))
      )
    }

  def add(networkId: String, user: UserACL)(implicit traceId: TraceId): Route =
    entity(as[RejectionReasonIdList]) { request =>
      complete(
        service.add(NetworkId(networkId), request.ids, user)
          .map(missingRejections =>
            AddRejectionReasonsResponse(
              message = if (missingRejections.isEmpty) "Rejection reasons successfully added"
              else "Cannot find some of rejection reasons",
              errors = missingRejections.map(id => s"Cannot find general rejection reason with id: $id")
            )
          )
      )
    }

  def deleteReason(networkId: String, rejectionId: String, user: UserACL)(implicit traceId: TraceId): Route =
    complete(
      optToNotFound(service.delete(NetworkId(networkId), Seq(rejectionId), user).map(b => if (b) Some(Unit) else None))
    )

  def list(networkId: String, user: UserACL)(implicit traceId: TraceId): StandardRoute =
    complete(
      service.list(NetworkId(networkId), user)
        .map(seq => seq.map(r => RejectionReasonView(r)))
    )

  private def optToNotFound[T](r: Future[Option[T]]): Future[T] = {
    r.flatMap {
      case Some(r) => Future.successful(r)
      case None => Future.failed(HttpError.notFound("Not found"))
    }
  }
}
