package com.simonmarkets.networks.domain

import akka.http.scaladsl.unmarshalling.Unmarshaller
import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues

@EnumValues("pdf", "VideoSharingEmail", "LifecycleEventsEmail", "PortfolioPDF")
sealed trait DisclaimerType extends EnumEntry

object DisclaimerType extends ProductEnums[DisclaimerType] {

  case object Pdf extends DisclaimerType

  case object VideoSharingEmail extends DisclaimerType

  case object LifecycleEventsEmail extends DisclaimerType

  case object PortfolioPDF extends DisclaimerType

  override def Values: Seq[DisclaimerType] = VideoSharingEmail :: Pdf :: LifecycleEventsEmail :: PortfolioPDF :: Nil

  implicit val unmarshaller: Unmarshaller[String, DisclaimerType] = Unmarshaller.strict[String, DisclaimerType](
    DisclaimerType(_)
  )
}
