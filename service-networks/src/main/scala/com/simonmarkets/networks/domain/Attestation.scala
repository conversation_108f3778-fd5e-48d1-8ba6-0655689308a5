package com.simonmarkets.networks.domain

import org.mongodb.scala.bson.annotations.BsonProperty

import java.time.Instant

case class Attestation(@BsonProperty("_id") id: String,
                       attestationText: String,
                       customRoles: Option[Set[String]] = None,
                       version: Int,
                       userCreated: String,
                       timeCreated: Instant,
                       userLastUpdated: String,
                       timeLastUpdated: Instant)
