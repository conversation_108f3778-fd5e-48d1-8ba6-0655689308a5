package com.simonmarkets.networks.domain

import com.goldmansachs.marquee.pipg.ContactInfo

import java.time.Instant

case class NetworkEntities(
    networkId: String,
    proprietaryIndices: Option[List[ProprietaryIndex]] = None,
    entities: Option[Set[Entity]] = None,
    userCreated: String,
    timeCreated: Instant,
    userLastUpdated: String,
    timeLastUpdated: Instant,
    version: Int
)

case class Entity(
    key: String,
    symbol: String,
    contractTypeWrapper: Option[String] = None,
    fullName: Option[String] = None,
    shortName: Option[String] = None,
    isRegS: Boolean = false,
    is3a2: Boolean = false,
    isNonNativeIssuerKeyOrLegacy: Boolean = false, //for NBC_GS and all
    contactInfo: Option[ContactInfo] = None
)

case class ProprietaryIndex(
    shortName: String,
    identifiers: ProprietaryIndexIdentifiers
)

case class ProprietaryIndexIdentifiers(
    mqId: String,
    bbId: String
)
