package com.simonmarkets.networks

import com.simonmarkets.api.users.HttpACLClient
import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.mongodb.Client
import com.simonmarkets.networks.api.ProductNomenclatureServiceRoutes
import com.simonmarkets.networks.config.AppConfiguration
import com.simonmarkets.networks.domain.ProductNomenclature
import com.simonmarkets.networks.repository.mongo.MongoProductNomenclatureRepository
import com.simonmarkets.networks.service.ProductNomenclatureService
import com.simonmarkets.resteasy.framework.{Environment, RestEasyModule}
import com.simonmarkets.resteasy.framework.RestEasyModule.RestEasyLambda
import com.simonmarkets.useracl.UserAclAuthorizedDirective
import io.simon.openapi.generator.OpenApiGenerator
import org.mongodb.scala.MongoCollection
import pureconfig.generic.auto._


object ProductNomenclatureServiceApp extends RestEasyModule[AppConfiguration] with App with TraceLogging {

  override def serviceName: String = "product-nomenclature"

  override def servicePath: String = "simon/api/v2/networks"
  override def init(env: Environment): Unit = {

    val httpACLClient = HttpACLClient(config.aclClientConfig)
    val userACLDirective = UserAclAuthorizedDirective(httpACLClient)

    log.info("Creating mongo client")
    val mongoClient = Client.create(config.mongoDB.client)

    val productNomenclatureCollection: MongoCollection[ProductNomenclature] = mongoClient
      .getDatabase(config.mongoDB.productNomenclature.database)
      .getCollection[ProductNomenclature](config.mongoDB.productNomenclature.collection)
    val productNomenclatureRepository = new MongoProductNomenclatureRepository(productNomenclatureCollection)
    val productNomenclatureService = new ProductNomenclatureService(productNomenclatureRepository)

    val routes = ProductNomenclatureServiceRoutes(productNomenclatureService, userACLDirective).routes


    env.addRoutes(routes)
  }


  OpenApiGenerator.generateOpenApiDocumentation
  object EntryPoint extends RestEasyLambda
}
