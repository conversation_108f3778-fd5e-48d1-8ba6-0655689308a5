package com.simonmarkets.networks

import akka.http.scaladsl.Http
import com.simonmarkets.api.users.HttpACLClient
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.mongodb.Client
import com.simonmarkets.mongodb.codec.IdTypeCodecProvider
import com.simonmarkets.networks.api.{ExternalIdTypeRoutes, NetworkAttestationsRoutes, NetworkRejectionReasonRoutes, NetworkServiceRoutes, NetworkVisualsRoutes}
import com.simonmarkets.networks.common.ExternalIdType
import com.simonmarkets.networks.common.domain.NetworkVisuals
import com.simonmarkets.networks.common.repository.mongo.MongoNetworkVisualsRepository
import com.simonmarkets.networks.common.repository.{ExternalIdTypeRepository, MongoNetworkRepository}
import com.simonmarkets.networks.common.service.IcnKmsService.IcnKmsServiceImpl
import com.simonmarkets.networks.common.service.networkservice.NetworkService
import com.simonmarkets.networks.common.service.{ExternalIdTypeService, NetworkVisualsService}
import com.simonmarkets.networks.config.AppConfiguration
import com.simonmarkets.networks.domain.{Disclaimer, Network, NetworkAttestations}
import com.simonmarkets.networks.rejections.domain.{NetworkRejectionReasons, RejectionReason}
import com.simonmarkets.networks.repository.mongo.{MongoGeneralRejectionReasonRepository, MongoNetworkAttestationRepository, MongoNetworkRejectionReasonRepository}
import com.simonmarkets.networks.repository.{DisclaimerRepository, GeneralRejectionReasonRepository, mongo}
import com.simonmarkets.networks.service.{NetworkAttestationService, NetworkDisclaimerService, NetworkRejectionReasonService}
import com.simonmarkets.okta.client.api.HttpOktaClient
import com.simonmarkets.okta.client.sdk.OktaClientFactory
import com.simonmarkets.okta.service.{OktaRepository, OktaService, OktaServiceStub}
import com.simonmarkets.resteasy.framework.RestEasyModule.RestEasyLambda
import com.simonmarkets.resteasy.framework.{Environment, RestEasyModule}
import com.simonmarkets.ui.assets.utils.PathHandler
import com.simonmarkets.useracl.UserAclAuthorizedDirective
import io.simon.openapi.generator.OpenApiGenerator
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.{Document, MongoCollection}
import simon.Id.NetworkId
import pureconfig.generic.auto._

object NetworkServiceApp extends RestEasyModule[AppConfiguration] with App with TraceLogging {
  override def serviceName: String = "networks"

  override def servicePath: String = "simon/api/v2/networks"

  override def init(environment: Environment): Unit = {
    val httpACLClient = HttpACLClient(config.aclClientConfig)
    val noAuthHttpClient = new FutureHttpClient(Http(), config.noAuthClientConfig)

    val oktaService = if (config.oktaConfig.enabled) {
      log.info("Okta Client has been created")
      OktaService(
        config = config.oktaConfig.serviceConfig,
        repository = OktaRepository(
          client = OktaClientFactory.getOktaClient(config.oktaConfig.clientConfig),
          httpClient = HttpOktaClient(config.oktaConfig.clientConfig)
        )
      )
    } else {
      log.info("Okta Client not running for local dev")
      OktaServiceStub
    }

    log.info("Creating mongo client")
    val mongoClient = Client.create(config.mongoDB.client)

    val networkCollection: MongoCollection[Document] = mongoClient
      .getDatabase(config.mongoDB.networks.database)
      .withCodecRegistry(DEFAULT_CODEC_REGISTRY)
      .getCollection[Document](config.mongoDB.networks.collection)

    val networkVisualsCollection: MongoCollection[NetworkVisuals] = {
      mongoClient
        .getDatabase(config.mongoDB.networkVisuals.database)
        .withCodecRegistry(MongoNetworkVisualsRepository.registry)
        .getCollection[NetworkVisuals](config.mongoDB.networkVisuals.collection)
    }

    val networksSnapshotsCollection: MongoCollection[Document] = mongoClient
      .getDatabase(config.mongoDB.networksSnapshots.database)
      .withCodecRegistry(DEFAULT_CODEC_REGISTRY)
      .getCollection[Document](config.mongoDB.networksSnapshots.collection)

    val externalIdTypeCollection: MongoCollection[ExternalIdType] = {
      val registry = fromRegistries(fromProviders(
        IdTypeCodecProvider(NetworkId),
        Macros.createCodecProvider[ExternalIdType]),
        DEFAULT_CODEC_REGISTRY)
      mongoClient
        .getDatabase(config.mongoDB.externalIdTypes.database)
        .withCodecRegistry(registry)
        .getCollection[ExternalIdType](config.mongoDB.externalIdTypes.collection)
    }


    val networkAttestationCollection: MongoCollection[NetworkAttestations] = {
      mongoClient.getDatabase(config.mongoDB.networkAttestations.database)
        .getCollection[NetworkAttestations](config.mongoDB.networkAttestations.collection)
    }

    val disclaimersCollection: MongoCollection[Disclaimer] = {
      mongoClient.getDatabase(config.mongoDB.networkDisclaimers.database)
        .getCollection[Disclaimer](config.mongoDB.networkDisclaimers.collection)
    }

    val networkAttestationsRepository = new MongoNetworkAttestationRepository(networkAttestationCollection)
    val networkAttestationService = new NetworkAttestationService(networkAttestationsRepository)

    val disclaimersRepository = new DisclaimerRepository.Mongo(disclaimersCollection)

    val disclaimerService: NetworkDisclaimerService = new NetworkDisclaimerService.Impl(disclaimersRepository)

    val rejectionReasonRegistry = fromRegistries(
      fromProviders(classOf[RejectionReason], classOf[NetworkRejectionReasons], classOf[Network]),
      DEFAULT_CODEC_REGISTRY)


    val networkRejectionReasonCollection: MongoCollection[NetworkRejectionReasons] = {
      mongoClient.getDatabase(config.mongoDB.networkRejectionReasons.database)
        .withCodecRegistry(rejectionReasonRegistry)
        .getCollection[NetworkRejectionReasons](config.mongoDB.networkRejectionReasons.collection)
    }

    val generalRejectionReasonCollection: MongoCollection[RejectionReason] = {
      mongoClient.getDatabase(config.mongoDB.generalRejectionReasons.database)
        .withCodecRegistry(rejectionReasonRegistry)
        .getCollection[RejectionReason](config.mongoDB.generalRejectionReasons.collection)
    }

    val networkCollectionRr: MongoCollection[Network] = {
      mongoClient.getDatabase(config.mongoDB.networks.database)
        .withCodecRegistry(rejectionReasonRegistry)
        .getCollection[Network](config.mongoDB.networks.collection)
    }

    val repository = new MongoNetworkRejectionReasonRepository(networkRejectionReasonCollection)
    val generalRejectionReasonRepository: GeneralRejectionReasonRepository =
      new MongoGeneralRejectionReasonRepository(generalRejectionReasonCollection)

    val networkRepositoryRr = new mongo.MongoNetworkRepository(networkCollectionRr)

    val networkRejectionReasonService = new NetworkRejectionReasonService(repository, generalRejectionReasonRepository, networkRepositoryRr)


    val uiAssetsPathHandler = new PathHandler(config.cloudfrontURLConfig)

    //repos
    val networkRepository = new MongoNetworkRepository(networkCollection, networksSnapshotsCollection, mongoClient)
    val externalIdTypeRepository = new ExternalIdTypeRepository.V1(externalIdTypeCollection)
    val networkVisualsRepository = new MongoNetworkVisualsRepository(networkVisualsCollection)

    //services
    val kmsService = new IcnKmsServiceImpl(config.icnKmsConfig.endpoint, config.icnKmsConfig.icnEnvironment, noAuthHttpClient)
    val networkService = new NetworkService(networkRepository, kmsService, oktaService, config.serviceConfig)
    val externalIdTypeService = new ExternalIdTypeService.V1(externalIdTypeRepository)
    val networkVisualsService = new NetworkVisualsService(networkVisualsRepository, uiAssetsPathHandler)
    val userAclDirective = UserAclAuthorizedDirective(httpACLClient)

    //routes
    val networkServiceRoutes = NetworkServiceRoutes(networkService, userAclDirective).routes
    val externalIdTypeServiceRoutes = ExternalIdTypeRoutes(externalIdTypeService, userAclDirective).routes
    val networkVisualsServiceRoutes = NetworkVisualsRoutes(networkVisualsService, userAclDirective).routes
    val rejectionReasonRoutes = NetworkRejectionReasonRoutes(networkRejectionReasonService, userAclDirective).routes
    val networkAttestationRoutes = new NetworkAttestationsRoutes(networkAttestationService, disclaimerService, userAclDirective).routes


    environment.addRoutes(networkServiceRoutes)
    environment.addRoutes(externalIdTypeServiceRoutes)
    environment.addRoutes(networkVisualsServiceRoutes)
    environment.addRoutes(rejectionReasonRoutes)
    environment.addRoutes(networkAttestationRoutes)
  }

  OpenApiGenerator.generateOpenApiDocumentation
  RestEasy.start

  object EntryPoint extends RestEasyLambda
}
