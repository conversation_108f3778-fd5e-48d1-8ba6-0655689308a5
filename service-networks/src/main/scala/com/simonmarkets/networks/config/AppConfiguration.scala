package com.simonmarkets.networks.config

import com.simonmarkets.api.users.config.AclClientConfig
import com.simonmarkets.http.HttpClientConfig
import com.simonmarkets.mongodb.config.MongoClientConfig
import com.simonmarkets.networks.common.config.NetworkServiceConfig
import com.simonmarkets.okta.config.OktaConfiguration
import com.simonmarkets.resteasy.framework.RunMode
import com.simonmarkets.resteasy.framework.config.RestEasyAppConfiguration
import com.simonmarkets.resteasy.framework.config.RestEasyAppConfiguration.SystemRoutesConfig
import com.simonmarkets.resteasy.framework.config.authn.AuthenticationConfiguration
import com.simonmarkets.resteasy.framework.info.InfoConfig
import com.simonmarkets.ui.assets.utils.config.CloudfrontURLConfig
import com.typesafe.config.Config
import pureconfig.{CamelCase, ConfigFieldMapping, KebabCase}
import pureconfig.generic.ProductHint
import pureconfig.generic.auto._

case class AppConfiguration(
    aclClientConfig: AclClientConfig,
    noAuthClientConfig: HttpClientConfig,
    authentication: Option[AuthenticationConfiguration],
    mongoDB: MongoConfiguration,
    runMode: RunMode,
    serviceConfig: NetworkServiceConfig,
    icnKmsConfig: IcnKmsConfig,
    oktaConfig: OktaConfiguration,
    cloudfrontURLConfig: CloudfrontURLConfig,
    systemRoutes: Option[SystemRoutesConfig],
    info: Option[InfoConfig]
) extends RestEasyAppConfiguration

case class MongoConfiguration(
    networks: DbConfiguration,
    networksSnapshots: DbConfiguration,
    productNomenclature: DbConfiguration,
    externalIdTypes: DbConfiguration,
    networkEntities: DbConfiguration,
    networkVisuals: DbConfiguration,
    networkRejectionReasons: DbConfiguration,
    generalRejectionReasons: DbConfiguration,
    networkAttestations: DbConfiguration,
    networkDisclaimers: DbConfiguration,
    client: MongoClientConfig
)

case class DbConfiguration(
    collection: String,
    database: String
)

case class IcnKmsConfig(
    icnEnvironment: String,
    endpoint: String,
)

object AppConfiguration {
  def apply(config: Config): AppConfiguration = {
    implicit def hint[T]: ProductHint[T] = ProductHint(ConfigFieldMapping(CamelCase, KebabCase))

    pureconfig.loadConfigOrThrow[AppConfiguration](config)
  }
}
