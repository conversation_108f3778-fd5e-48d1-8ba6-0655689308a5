package com.simonmarkets.networks


import com.simonmarkets.api.users.HttpACLClient
import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.mongodb.Client
import com.simonmarkets.networks.api.NetworkEntitiesRoutes
import com.simonmarkets.networks.config.AppConfiguration
import com.simonmarkets.networks.domain.NetworkEntities
import com.simonmarkets.networks.repository.mongo.MongoNetworkEntitiesRepository
import com.simonmarkets.networks.service.NetworkEntitiesService
import com.simonmarkets.resteasy.framework.{Environment, RestEasyModule}
import com.simonmarkets.resteasy.framework.RestEasyModule.RestEasyLambda
import com.simonmarkets.useracl.UserAclAuthorizedDirective
import io.simon.openapi.generator.OpenApiGenerator
import org.mongodb.scala.MongoCollection
import pureconfig.generic.auto._

object NetworkEntitiesApp extends RestEasyModule[AppConfiguration] with App with TraceLogging {
  override def serviceName: String = "network-entities"
  override def servicePath: String = "simon/api/v1/network-entities"

  override def init(env: Environment): Unit = {
    val httpACLClient = HttpACLClient(config.aclClientConfig)
    val userACLDirective = UserAclAuthorizedDirective(httpACLClient)

    log.info("Creating mongo client")
    val mongoClient = Client.create(config.mongoDB.client)

    val networkEntitiesCollection: MongoCollection[NetworkEntities] = mongoClient
      .getDatabase(config.mongoDB.networkEntities.database)
      .getCollection[NetworkEntities](config.mongoDB.networkEntities.collection)
    val networkEntitiesRepository = new MongoNetworkEntitiesRepository(networkEntitiesCollection)
    val networkEntitiesService = new NetworkEntitiesService(networkEntitiesRepository)

    val routes = NetworkEntitiesRoutes(networkEntitiesService, userACLDirective).routes

    env.addRoutes(routes)
  }
  

  OpenApiGenerator.generateOpenApiDocumentation
  RestEasy.start()
  
  object EntryPoint extends RestEasyLambda
}

