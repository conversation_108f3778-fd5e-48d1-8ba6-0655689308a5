package com.simonmarkets.networks.repository.mongo

import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.rejections.domain.{NetworkRejectionReasons, RejectionReason}
import com.simonmarkets.networks.repository.NetworkRejectionReasonRepository
import org.mongodb.scala.bson.conversions.Bson
import org.mongodb.scala.bson.{BsonDocument, Document}
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.Updates._
import org.mongodb.scala.{MongoClient, MongoCollection}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class MongoNetworkRejectionReasonRepository
(collection: MongoCollection[NetworkRejectionReasons]) extends NetworkRejectionReasonRepository with TraceLogging {

  override def get(networkId: NetworkId)(implicit traceId: TraceId): Future[Option[NetworkRejectionReasons]] = {
    collection.find(equal("network", NetworkId.unwrap(networkId))).headOption()
  }

  override def list(networkId: NetworkId)(implicit traceId: TraceId): Future[Seq[RejectionReason]] = {
    collection.find(equal("network", NetworkId.unwrap(networkId)))
      .headOption()
      .map { networkRejections => networkRejections.toSeq.flatMap(_.reasons) }
  }

  override def add(networkId: NetworkId, rejections: Seq[RejectionReason])
    (implicit traceId: TraceId): Future[Unit] = {
    for {
      modifiedCount <- collection.updateOne(
        equal("network", NetworkId.unwrap(networkId)),
        addEachToSet("reasons", rejections: _*)
      ).toFuture().map(r => r.getModifiedCount)
    } yield {
      if (modifiedCount == 0)
        collection.insertOne(NetworkRejectionReasons(
          network = NetworkId.unwrap(networkId),
          reasons = rejections
        )).toFuture
      else {
        () => Future.unit
      }
    }
  }

  override def delete(networkId: NetworkId, rejectionIds: Seq[String])(implicit traceId: TraceId): Future[Boolean] = {
    val reasonsIdsCond: Bson = in("_id", rejectionIds: _*)
    val reasonsCond = Document("reasons" -> reasonsIdsCond.toBsonDocument(classOf[BsonDocument], MongoClient.DEFAULT_CODEC_REGISTRY))

    val pullExpression: Bson = pullByFilter(reasonsCond.toBsonDocument)

    collection.updateOne(equal("network", NetworkId.unwrap(networkId)),
      pullExpression
    ).toFuture().map(r => r.getModifiedCount == 1)
  }
}
