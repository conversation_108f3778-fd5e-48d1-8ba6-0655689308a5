package com.simonmarkets.networks.repository

import com.simonmarkets.networks.domain.ProductNomenclature
import simon.Id.NetworkId

import scala.concurrent.Future

trait ProductNomenclatureRepository {

  def list(networkId: NetworkId): Future[Seq[ProductNomenclature]]

  def add(networkId: NetworkId, name: String, userId: String): Future[ProductNomenclature]

  def get(networkId: NetworkId, productNomenclatureId: String): Future[Option[ProductNomenclature]]

  def update(networkId: NetworkId, productNomenclatureId: String, newName: String,
      userId: String): Future[Option[ProductNomenclature]]

  def delete(networkId: NetworkId, productNomenclatureId: String): Future[Boolean]

  def exists(networkId: NetworkId, name: String): Future[Boolean]
}
