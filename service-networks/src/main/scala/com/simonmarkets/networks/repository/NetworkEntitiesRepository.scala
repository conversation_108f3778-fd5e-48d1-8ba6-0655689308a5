package com.simonmarkets.networks.repository

import com.simonmarkets.networks.domain.NetworkEntities

import scala.concurrent.Future

trait NetworkEntitiesRepository {

  def list(networkIds: Option[Set[String]], keys: Option[Set[String]], symbols: Option[Set[String]]): Future[Seq[NetworkEntities]]

  def add(networkEntity: NetworkEntities): Future[NetworkEntities]

  def update(networkEntity: NetworkEntities): Future[Option[NetworkEntities]]

  def getByKey(key: String): Future[Option[NetworkEntities]]

  def getByNetworkId(networkId: String): Future[Option[NetworkEntities]]

  def getAllProprietaryIndices: Future[Seq[NetworkEntities]]

  def delete(networkId: String): Future[Boolean]
}
