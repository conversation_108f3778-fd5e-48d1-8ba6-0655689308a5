package com.simonmarkets.networks.repository

import com.mongodb.client.model.{FindOneAndReplaceOptions, ReturnDocument}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.mongodb.codec.EnumEntryCodecProvider
import com.simonmarkets.networks.domain.{Disclaimer, DisclaimerType}
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.bson.conversions.Bson
import org.mongodb.scala.model.Filters._
import simon.Id.NetworkId

import java.time.Instant
import scala.concurrent.{ExecutionContext, Future}

trait DisclaimerRepository {
  def get(networkId: NetworkId, disclaimerType: DisclaimerType)(implicit traceId: TraceId): Future[Option[Disclaimer]]

  def add(networkId: NetworkId, disclaimerType: DisclaimerType, text: String, userId: String)
    (implicit traceId: TraceId): Future[Option[Disclaimer]]

  def delete(networkId: NetworkId, disclaimerType: DisclaimerType)(implicit traceId: TraceId): Future[Option[Disclaimer]]
}

object DisclaimerRepository {

  class Mongo(disclaimersCollection: MongoCollection[Disclaimer])
    (implicit ec: ExecutionContext) extends DisclaimerRepository with TraceLogging {

    val codecRegistry: CodecRegistry = fromRegistries(
      fromProviders(
        classOf[Disclaimer],
        EnumEntryCodecProvider[DisclaimerType]),
      DEFAULT_CODEC_REGISTRY)

    private val collection = disclaimersCollection.withCodecRegistry(codecRegistry)

    override def get(networkId: NetworkId, disclaimerType: DisclaimerType)
      (implicit traceId: TraceId): Future[Option[Disclaimer]] = {
      collection.find(networkAndTypeCondition(networkId, disclaimerType)).toFuture().map(_.headOption)
    }

    private def networkAndTypeCondition(networkId: NetworkId, disclaimerType: DisclaimerType): Bson =
      and(
        equal("networkId", NetworkId.unwrap(networkId)),
        equal("disclaimerType", disclaimerType.productPrefix)
      )

    override def add(networkId: NetworkId, disclaimerType: DisclaimerType, text: String, userId: String)
      (implicit traceId: TraceId): Future[Option[Disclaimer]] = {

      val disclaimer = Disclaimer(
        disclaimerType = disclaimerType,
        networkId = NetworkId.unwrap(networkId),
        text = text,
        userCreated = userId,
        timeCreated = Instant.now()
      )

      val withUpsert = new FindOneAndReplaceOptions().upsert(true).returnDocument(ReturnDocument.AFTER)

      collection.findOneAndReplace(networkAndTypeCondition(networkId, disclaimerType), disclaimer, withUpsert).toFutureOption()
    }

    override def delete(networkId: NetworkId, disclaimerType: DisclaimerType)
      (implicit traceId: TraceId): Future[Option[Disclaimer]] = {
      collection.findOneAndDelete(networkAndTypeCondition(networkId, disclaimerType)).toFutureOption()
    }
  }
}
