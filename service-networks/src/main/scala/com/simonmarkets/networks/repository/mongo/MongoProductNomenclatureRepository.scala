package com.simonmarkets.networks.repository.mongo

import com.simonmarkets.networks.domain.ProductNomenclature
import com.simonmarkets.networks.repository.ProductNomenclatureRepository
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.Updates.{combine, inc, set}
import org.mongodb.scala.model.{FindOneAndUpdateOptions, ReturnDocument}
import simon.Id.NetworkId

import java.time.Instant
import java.util.UUID

import scala.concurrent.{ExecutionContext, Future}

class MongoProductNomenclatureRepository(collection: MongoCollection[ProductNomenclature])
  (implicit ec: ExecutionContext) extends ProductNomenclatureRepository {

  private final val coll = collection.withCodecRegistry(fromRegistries(fromProviders(classOf[ProductNomenclature]), DEFAULT_CODEC_REGISTRY))

  object Fields {
    final val Id = "_id"
    final val NetworkId = "networkId"
    final val Name = "name"
    final val TimeLastUpdated = "timeLastUpdated"
    final val UserLastUpdated = "userLastUpdated"
    final val Version = "version"
  }

  override def list(networkId: NetworkId): Future[Seq[ProductNomenclature]] = {
    coll.find(equal(Fields.NetworkId, NetworkId.unwrap(networkId))).toFuture()
  }

  override def add(networkId: NetworkId, name: String, userId: String): Future[ProductNomenclature] = {
    val now = Instant.now()

    val productNomenclature = ProductNomenclature(
      id = UUID.randomUUID().toString,
      name = name,
      networkId = NetworkId.unwrap(networkId),
      userCreated = userId,
      timeCreated = now,
      userLastUpdated = userId,
      timeLastUpdated = now,
      version = 1
    )

    coll.insertOne(productNomenclature).toFuture.map(_ => productNomenclature)
  }

  override def get(networkId: NetworkId, productNomenclatureId: String): Future[Option[ProductNomenclature]] = {
    coll.find(
      and(equal(Fields.Id, productNomenclatureId), equal(Fields.NetworkId, NetworkId.unwrap(networkId)))).headOption()
  }

  override def update(networkId: NetworkId, productNomenclatureId: String,
      newName: String, userId: String): Future[Option[ProductNomenclature]] = {
    val returnOption = new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)

    val updateCriteria = and(equal(Fields.Id, productNomenclatureId),
      equal(Fields.NetworkId, NetworkId.unwrap(networkId)))

    val setExpression = Seq(
      set(Fields.Name, newName),
      set(Fields.TimeLastUpdated, Instant.now()),
      set(Fields.UserLastUpdated, userId),
      inc(Fields.Version, 1)
    )

    coll.findOneAndUpdate(updateCriteria, combine(setExpression: _*), returnOption).toFuture().map(Option(_))
  }

  override def delete(networkId: NetworkId, productNomenclatureId: String): Future[Boolean] = {
    coll.deleteOne(and(
      equal(Fields.NetworkId, NetworkId.unwrap(networkId)), equal(Fields.Id, productNomenclatureId)))
      .toFuture().map(_.getDeletedCount > 0)
  }

  override def exists(networkId: NetworkId, name: String): Future[Boolean] = {
    coll.countDocuments(and(
      equal(Fields.NetworkId, NetworkId.unwrap(networkId)), equal(Fields.Name, name)
    )).toFuture.map(_ > 0)
  }
}
