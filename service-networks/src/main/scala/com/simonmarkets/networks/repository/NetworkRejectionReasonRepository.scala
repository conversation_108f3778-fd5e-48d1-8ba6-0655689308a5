package com.simonmarkets.networks.repository

import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.rejections.domain.{NetworkRejectionReasons, RejectionReason}
import simon.Id.NetworkId

import scala.concurrent.Future

trait NetworkRejectionReasonRepository {
  def get(networkId: NetworkId)(implicit traceId: TraceId): Future[Option[NetworkRejectionReasons]]

  def list(networkId: NetworkId)(implicit traceId: TraceId): Future[Seq[RejectionReason]]

  def add(networkId: NetworkId, rejections: Seq[RejectionReason])(implicit traceId: TraceId): Future[Unit]

  def delete(networkId: NetworkId, rejections: Seq[String])(implicit traceId: TraceId): Future[Boolean]
}
