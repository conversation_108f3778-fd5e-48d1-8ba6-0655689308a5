package com.simonmarkets.networks.repository.mongo

import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.networks.domain.Network
import com.simonmarkets.networks.repository.NetworkRepository
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.model.Filters._
import simon.Id.NetworkId

import scala.concurrent.Future

class MongoNetworkRepository(collection: MongoCollection[Network]) extends NetworkRepository with TraceLogging {

  override def entitledFind(networkId: NetworkId, capabilities: Set[String]): Future[Option[Network]] = {
    collection.find(and(
      equal("id", NetworkId.unwrap(networkId)),
      in("entitlements", (capabilities.toSeq): _*)
    )).headOption()
  }
}
