package com.simonmarkets.networks.repository

import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.domain.Attestation
import simon.Id.NetworkId

import scala.concurrent.Future

trait NetworkAttestationsRepository {

  def list(networkId: NetworkId)(implicit traceId: TraceId): Future[Seq[Attestation]]

  def delete(networkId: NetworkId, attestationId: String)(implicit traceId: TraceId): Future[Boolean]

  def create(networkId: NetworkId, attestation: Attestation)(implicit traceId: TraceId): Future[Attestation]

  def update(networkId: NetworkId, id: String, attestationText: String, customRoles: Option[Set[String]], userId: String)(implicit traceId: TraceId): Future[Long]

}
