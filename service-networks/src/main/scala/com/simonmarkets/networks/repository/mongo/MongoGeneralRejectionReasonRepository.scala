package com.simonmarkets.networks.repository.mongo

import com.simonmarkets.networks.rejections.domain.RejectionReason
import com.simonmarkets.networks.repository.GeneralRejectionReasonRepository
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.model.Filters._

import scala.concurrent.Future

class MongoGeneralRejectionReasonRepository(
    collection: MongoCollection[RejectionReason]) extends GeneralRejectionReasonRepository {

  override def list(ids: Seq[String]): Future[Seq[RejectionReason]] = {
    collection.find(in("_id", ids: _*)).toFuture()
  }
}
