package com.simonmarkets.networks.repository.mongo

import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.domain.{Attestation, NetworkAttestations}
import com.simonmarkets.networks.repository.NetworkAttestationsRepository
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.{MongoClient, MongoCollection}
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.bson.conversions.Bson
import org.mongodb.scala.bson.{BsonDocument, Document}
import org.mongodb.scala.model.Filters.{and, equal, in}
import org.mongodb.scala.model.Updates._
import simon.Id.NetworkId

import java.time.Instant
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class MongoNetworkAttestationRepository(networkAttestationsCollection: MongoCollection[NetworkAttestations])
  extends NetworkAttestationsRepository with TraceLogging {

  val codecRegistry: CodecRegistry = fromRegistries(fromProviders(classOf[NetworkAttestations], classOf[Attestation]), DEFAULT_CODEC_REGISTRY)

  private val collection = networkAttestationsCollection.withCodecRegistry(codecRegistry)

  override def list(networkId: NetworkId)(implicit traceId: TraceId): Future[Seq[Attestation]] = {
    collection.find(equal("network", NetworkId.unwrap(networkId)))
      .headOption()
      .map { networkAttestations => networkAttestations.toSeq.flatMap(_.attestations) }
  }

  override def create(networkId: NetworkId, attestation: Attestation)
    (implicit traceId: TraceId): Future[Attestation] = {
    for {
      modifiedCount <- collection.updateOne(
        equal("network", NetworkId.unwrap(networkId)),
        addEachToSet("attestations", attestation)
      ).toFuture().map(r => r.getModifiedCount)
      att <- if (modifiedCount == 0) {
        collection.insertOne(NetworkAttestations(
          network = NetworkId.unwrap(networkId),
          attestations = Seq(attestation)
        )).toFuture().map(_ => attestation)
      }
      else {
        Future.successful(attestation)
      }
    } yield att
  }

  override def delete(networkId: NetworkId, attestationId: String)(implicit traceId: TraceId): Future[Boolean] = {
    val attestationIdCond: Bson = in("_id", attestationId)
    val attestationCond = Document("attestations" -> attestationIdCond.toBsonDocument(classOf[BsonDocument], MongoClient.DEFAULT_CODEC_REGISTRY))
    val pullExpression: Bson = pullByFilter(attestationCond.toBsonDocument)

    collection.updateOne(equal("network", NetworkId.unwrap(networkId)),
      pullExpression
    ).toFuture().map(r => r.getModifiedCount == 1)
  }

  override def update(networkId: NetworkId, attestationId: String, attestationText: String, customRoles: Option[Set[String]], userId: String)
    (implicit traceId: TraceId): Future[Long] = {
    collection.updateOne(
      and(
        equal("network", NetworkId.unwrap(networkId)),
        equal("attestations._id", attestationId)),
      combine(
        set("attestations.$.attestationText", attestationText),
        customRoles match {
          case Some(roles) => set("attestations.$.customRoles", roles)
          case None => unset("attestations.$.customRoles")
        },
        set("attestations.$.timeLastUpdated", Instant.now()),
        set("attestations.$.userLastUpdated", userId),
        inc("attestations.$.version", 1)
      )
    ).toFuture().map(_.getModifiedCount)
  }
}
