package com.simonmarkets.networks.repository.mongo

import com.goldmansachs.marquee.pipg.{ContactInfo, DistributionList}
import com.simonmarkets.networks.domain.{Entity, NetworkEntities, ProprietaryIndex, ProprietaryIndexIdentifiers}
import com.simonmarkets.networks.repository.NetworkEntitiesRepository
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.Projections.exclude
import org.mongodb.scala.model.{FindOneAndReplaceOptions, ReturnDocument}

import scala.concurrent.{ExecutionContext, Future}

class MongoNetworkEntitiesRepository(collection: MongoCollection[NetworkEntities])
  (implicit ec: ExecutionContext) extends NetworkEntitiesRepository {

  protected[mongo] final val codecRegistry = fromRegistries(fromProviders(classOf[NetworkEntities], classOf[Entity], classOf[ContactInfo], classOf[DistributionList], classOf[ProprietaryIndex], classOf[ProprietaryIndexIdentifiers]),
    DEFAULT_CODEC_REGISTRY)
  private final val coll = collection.withCodecRegistry(codecRegistry)

  object Fields {
    final val NetworkId = "networkId"
    final val EntityKey = "entities.key"
    final val EntitySymbol = "entities.symbol"
    final val Version = "version"
  }

  override def getByKey(key: String): Future[Option[NetworkEntities]] = {
    coll.find(equal(Fields.EntityKey, key)).headOption()
  }

  override def getByNetworkId(networkId: String): Future[Option[NetworkEntities]] = {
    coll.find(equal(Fields.NetworkId, networkId)).headOption()
  }

  override def list(networkIds: Option[Set[String]] = None, keys: Option[Set[String]] = None,
      symbols: Option[Set[String]] = None): Future[Seq[NetworkEntities]] = {
    if (networkIds.isEmpty && keys.isEmpty && symbols.isEmpty) {
      coll.find().toFuture()
    } else {
      val networksCondition = networkIds.map(ids => in(Fields.NetworkId, ids.toSeq: _*))
      val keysCondition = keys.map(ks => in(Fields.EntityKey, ks.toSeq: _*))
      val symbolsCondition = symbols.map(s => in(Fields.EntitySymbol, s.toSeq: _*))
      coll.find(and(Seq(networksCondition, keysCondition, symbolsCondition).flatten: _*)).toFuture()
    }
  }

  override def add(networkEntities: NetworkEntities): Future[NetworkEntities] = {
    coll.insertOne(networkEntities).toFuture.map(_ => networkEntities)
  }

  override def update(networkEntities: NetworkEntities): Future[Option[NetworkEntities]] = {
    val returnOption = new FindOneAndReplaceOptions().returnDocument(ReturnDocument.AFTER)

    val updateCriteria = and(equal(Fields.NetworkId, networkEntities.networkId),
      equal(Fields.Version, networkEntities.version - 1))

    coll.findOneAndReplace(updateCriteria, networkEntities, returnOption)
      .toFuture().map(Option(_))
  }

  def getAllProprietaryIndices: Future[Seq[NetworkEntities]] = {
    coll.find.projection(exclude("entities")).toFuture()
  }

  override def delete(networkId: String): Future[Boolean] = {
    coll.deleteOne(equal(Fields.NetworkId, networkId))
      .toFuture().map(_.getDeletedCount > 0)
  }
}
