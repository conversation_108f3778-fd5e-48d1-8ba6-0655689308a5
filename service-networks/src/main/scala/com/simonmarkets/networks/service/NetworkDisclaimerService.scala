package com.simonmarkets.networks.service

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.domain.{Disclaimer, DisclaimerType}
import com.simonmarkets.networks.repository.DisclaimerRepository
import simon.Id.NetworkId

import scala.concurrent.{ExecutionContext, Future}

/**
 * There is single disclaimer of particular type per network, so we can identify it by `disclaimerType` + `networkId`
 */
trait NetworkDisclaimerService {

  def get(networkId: NetworkId, disclaimerType: DisclaimerType)
    (implicit user: UserACL, traceId: TraceId): Future[Option[Disclaimer]]

  def update(networkId: NetworkId, disclaimerType: DisclaimerType, disclaimer: String)
    (implicit user: UserACL, traceId: TraceId): Future[Option[Disclaimer]]

  def delete(networkId: NetworkId, disclaimerType: DisclaimerType)(implicit user: UserACL, traceId: TraceId): Future[Option[Disclaimer]]
}

object NetworkDisclaimerService {
  class Impl(repository: DisclaimerRepository)
    (implicit ec: ExecutionContext) extends NetworkDisclaimerService with TraceLogging {

    override def get(networkId: NetworkId, disclaimerType: DisclaimerType)
      (implicit user: UserACL, traceId: TraceId): Future[Option[Disclaimer]] = {

      for {
        _ <- if (user.networkId == networkId || user.userPurviewIds.contains(networkId)
          || user.capabilities.contains(Capabilities.Admin)) Future.unit
        else HttpError.forbidden("You don't have permissions for this action.").future

        disclaimerOpt <- repository.get(networkId, disclaimerType)
      } yield disclaimerOpt
    }

    override def update(networkId: NetworkId, disclaimerType: DisclaimerType, disclaimer: String)
      (implicit user: UserACL, traceId: TraceId): Future[Option[Disclaimer]] = {

      for {
        _ <- if (user.capabilities.contains(Capabilities.Admin)) Future.unit else
          HttpError.forbidden("You don't have permissions for this action.").future

        disclaimer <- repository.add(networkId, disclaimerType, disclaimer, user.userId)
      } yield disclaimer
    }

    override def delete(networkId: NetworkId, disclaimerType: DisclaimerType)
      (implicit user: UserACL, traceId: TraceId): Future[Option[Disclaimer]] = {

      for {
        _ <- if (user.capabilities.contains(Capabilities.Admin)) Future.unit else
          HttpError.forbidden("You don't have permissions for this action.").future

        deletedDisclaimer <- repository.delete(networkId, disclaimerType)
      } yield deletedDisclaimer
    }
  }
}
