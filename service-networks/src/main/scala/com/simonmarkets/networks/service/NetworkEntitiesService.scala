package com.simonmarkets.networks.service

import com.goldmansachs.marquee.pipg.{UserACL, UserRole}
import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.api.request.{AddNetworkEntitiesRequest, UpdateNetworkEntitiesRequest}
import com.simonmarkets.networks.api.response.{FlattenedNetworkEntitiesView, NetworkEntitiesView, ProprietaryIndicesView}
import com.simonmarkets.networks.domain.NetworkEntities
import com.simonmarkets.networks.repository.mongo.MongoNetworkEntitiesRepository
import simon.Id.NetworkId

import java.time.Instant

import scala.concurrent.{ExecutionContext, Future}

class NetworkEntitiesService(networkEntitiesRepository: MongoNetworkEntitiesRepository)
  (implicit ec: ExecutionContext) extends TraceLogging {

  def list(networkIds: Option[Set[String]] = None, keys: Option[Set[String]] = None, symbols: Option[Set[String]] = None)
    (implicit traceId: TraceId): Future[Seq[NetworkEntitiesView]] = {
    log.info("Starting to get network entities", networkIds, keys, symbols)
    for {
      r <- networkEntitiesRepository.list(networkIds, keys, symbols)
    } yield r.map(NetworkEntitiesView(_))
  }

  def listFlattenedView(networkIds: Option[Set[String]] = None, keys: Option[Set[String]] = None, symbols: Option[Set[String]] = None)
    (implicit traceId: TraceId): Future[Seq[FlattenedNetworkEntitiesView]] = {
    log.info("Starting to get flattened network entities", networkIds, keys, symbols)
    for {
      r <- networkEntitiesRepository.list(networkIds, keys, symbols)
    } yield r.flatMap(FlattenedNetworkEntitiesView(_, keys, symbols))
  }

  def add(request: AddNetworkEntitiesRequest, networkId: String)(implicit user: UserACL, traceId: TraceId): Future[NetworkEntitiesView] = {
    log.info(s"Starting to add new network entity $networkId")
    for {
      _ <- isAdmin(user)
      now = Instant.now
      networkEntities = NetworkEntities(
        networkId = networkId,
        proprietaryIndices = request.proprietaryIndices,
        entities = Some(request.entities),
        userCreated = user.userId,
        timeCreated = now,
        userLastUpdated = user.userId,
        timeLastUpdated = now,
        version = 1
      )
      r <- networkEntitiesRepository.add(networkEntities)
    } yield NetworkEntitiesView(r)
  }

  def getByNetworkId(networkId: String)(implicit traceId: TraceId): Future[Option[NetworkEntitiesView]] = {
    log.info("Getting network entities by networkId", networkId)
    for {
      r <- networkEntitiesRepository.getByNetworkId(networkId)
    } yield r.map(NetworkEntitiesView(_))
  }

  def getByKey(entityKey: String)(implicit traceId: TraceId): Future[Option[FlattenedNetworkEntitiesView]] = {
    log.info("Getting network entities by key", entityKey)
    for {
      r <- networkEntitiesRepository.getByKey(entityKey)
      view = r.map(entity => FlattenedNetworkEntitiesView(entity, Some(Set(entityKey)), None)).getOrElse(Set.empty[FlattenedNetworkEntitiesView])
    } yield view.headOption
  }

  def getAllProprietaryIndices(implicit user: UserACL, traceId: TraceId): Future[Seq[ProprietaryIndicesView]] = {
    log.info("Get proprietary indices endpoint called")
    for {
      _ <- if (user.roles.contains(UserRole.EqPIPGSIMONSystemUser) || (user.networkId == NetworkId("SIMON Admin") && user.roles.contains(UserRole.EqPIPGGSAdmin))) {
        Future.unit
      }
      else {
        val message = "User has no capability to call the endpoint"
        log.error(message)
        Future.failed(HttpError.forbidden(message))
      }
      allNetworkEntities <- networkEntitiesRepository.getAllProprietaryIndices
    } yield allNetworkEntities.map(ProprietaryIndicesView(_))
  }

  def update(request: UpdateNetworkEntitiesRequest, networkId: String)
    (implicit user: UserACL, traceId: TraceId): Future[Option[NetworkEntitiesView]] = {
    log.info(s"Updating network entity $networkId")
    for {
      _ <- isAdmin(user)
      existingRecord <- networkEntitiesRepository.getByNetworkId(networkId)
      updateRecord <- existingRecord match {
        case Some(record) => Future.successful(record.copy(
            entities = Some(request.entities),
            userLastUpdated = user.userId,
            timeLastUpdated = Instant.now(),
            version = record.version + 1,
            proprietaryIndices = request.proprietaryIndices
          ))
        case None => Future.failed(HttpError.notFound(s"Entity record not found for $networkId"))
      }
      r <- networkEntitiesRepository.update(updateRecord)
    } yield r.map(NetworkEntitiesView(_))
  }

  def delete(id: String)(implicit user: UserACL, traceId: TraceId): Future[Boolean] = {
    for {
      _ <- isAdmin(user)
      r <- networkEntitiesRepository.delete(id)
    } yield r
  }

  private def isAdmin(user: UserACL)(implicit traceId: TraceId): Future[Unit] = for {
    _ <- if (user.capabilities.contains(Capabilities.Admin)) Future.unit else {
      val message = "User has no capability to manage network entities"
      log.error(message)
      Future.failed(HttpError.forbidden(message))
    }
  } yield ()
}
