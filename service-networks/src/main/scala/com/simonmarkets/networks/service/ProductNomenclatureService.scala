package com.simonmarkets.networks.service

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.ProductNomenclatureCapabilities
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.networks.domain.ProductNomenclature
import com.simonmarkets.networks.repository.ProductNomenclatureRepository
import simon.Id.NetworkId

import scala.concurrent.{ExecutionContext, Future}

class ProductNomenclatureService(productNomenclatureRepository: ProductNomenclatureRepository)
  (implicit ec: ExecutionContext) extends TraceLogging {

  def list(networkId: NetworkId)(user: UserACL): Future[Seq[ProductNomenclature]] = {
    for {
      _ <- canView(user)
      r <- productNomenclatureRepository.list(networkId)
    } yield r
  }

  def add(networkId: NetworkId, name: String)(user: UserACL): Future[ProductNomenclature] = {
    for {
      _ <- canEdit(user)
      _ <- if (name.trim.nonEmpty) Future.unit else Future.failed(HttpError.badRequest("Please provide non-empty product nomenclature name"))
      alreadyExists <- productNomenclatureRepository.exists(networkId, name)
      _ <- if (alreadyExists) Future.failed(HttpError.conflict("Product nomenclature with this name already exists")) else Future.unit
      r <- productNomenclatureRepository.add(networkId, name, user.userId)
    } yield r
  }

  def get(networkId: NetworkId, productNomenclatureId: String)(user: UserACL): Future[Option[ProductNomenclature]] = {
    for {
      _ <- canView(user)
      r <- productNomenclatureRepository.get(networkId, productNomenclatureId)
    } yield r
  }

  def update(networkId: NetworkId, productNomenclatureId: String, newName: String)
    (user: UserACL): Future[Option[ProductNomenclature]] = {
    for {
      _ <- canEdit(user)
      _ <- if (newName.trim.nonEmpty) Future.unit else Future.failed(HttpError.badRequest("Please provide non-empty product nomenclature name"))
      alreadyExists <- productNomenclatureRepository.exists(networkId, newName)
      _ <- if (alreadyExists) Future.failed(HttpError.conflict("Product nomenclature with this name already exists")) else Future.unit
      r <- productNomenclatureRepository.update(networkId, productNomenclatureId, newName, user.userId)
    } yield r
  }

  def delete(networkId: NetworkId, productNomenclatureId: String)(user: UserACL): Future[Boolean] = {
    for {
      _ <- canEdit(user)
      r <- productNomenclatureRepository.delete(networkId, productNomenclatureId)
    } yield r
  }

  private def canEdit(user: UserACL): Future[Unit] = {
    if (ProductNomenclatureCapabilities.AvailableAccessKeysGenerator
      .getAvailableAccessKeysForCapabilities(ProductNomenclatureCapabilities.EditCapabilities, user).nonEmpty) {
      Future.unit
    } else {
      Future.failed(HttpError.forbidden("User has no capability to manage product nomenclature"))
    }
  }

  private def canView(user: UserACL): Future[Unit] = {
    if (ProductNomenclatureCapabilities.AvailableAccessKeysGenerator
      .getAvailableAccessKeysForCapabilities(ProductNomenclatureCapabilities.ViewCapabilities, user).nonEmpty) {
      Future.unit
    } else {
      Future.failed(HttpError.forbidden("User has no capability to view product nomenclature"))
    }
  }
}
