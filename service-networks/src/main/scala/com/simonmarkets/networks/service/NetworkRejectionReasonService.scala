package com.simonmarkets.networks.service

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.NetworksCapabilities._
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.domain.Network
import com.simonmarkets.networks.rejections.domain.RejectionReason
import com.simonmarkets.networks.repository.{GeneralRejectionReasonRepository, NetworkRejectionReasonRepository, NetworkRepository}
import com.simonmarkets.resteasy.DirectivesWithCirce
import simon.Id.NetworkId

import java.time.Instant
import java.util.UUID
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class NetworkRejectionReasonService(networkRejectionsRepo: NetworkRejectionReasonRepository,
    generalRejectionsRepo: GeneralRejectionReasonRepository, networksRepo: NetworkRepository)
  extends DirectivesWithCirce {

  def list(networkId: NetworkId, user: UserACL)(implicit traceId: TraceId): Future[Seq[RejectionReason]] = {
    val capabilities: Set[String] = getAvailableAccessKeysForCapabilities(ViewCapabilities, user)

    for {
      _ <- toNotFound(networksRepo.entitledFind(networkId, capabilities))
      rejections <- networkRejectionsRepo.list(networkId)
    } yield rejections
  }

  /**
   * Adds rejection reason to the network. If record with rejection reasons missing in collection it will create
   * it (but case is rare - all networks will be have records, and those records should be added whenever new network created
   */
  def add(networkId: NetworkId, rejectionIds: Seq[String], user: UserACL)
    (implicit traceId: TraceId): Future[Seq[String]] = {
    val capabilities: Set[String] = getAvailableAccessKeysForCapabilities(EditCapabilities, user)

    for {
      _ <- toNotFound(networksRepo.entitledFind(networkId, capabilities))
      rejections <- generalRejectionsRepo.list(rejectionIds)
      _ <- if (rejections.isEmpty)
        Future.failed(HttpError.badRequest("Cannot find any rejection reason with ids provided in request"))
      else Future.unit
      missingRejections = rejectionIds.toSet.diff(rejections.map(_.id).toSet)
      _ <- networkRejectionsRepo.add(networkId, rejections)
    } yield missingRejections.toSeq
  }

  def addCustom(networkId: NetworkId, reason: String, group: String, user: UserACL)
    (implicit traceId: TraceId): Future[String] = {
    val capabilities: Set[String] = getAvailableAccessKeysForCapabilities(EditCapabilities, user)

    val now = Instant.now()
    for {
      _ <- toNotFound(networksRepo.entitledFind(networkId, capabilities))
      _ <- if (reason.trim.isEmpty || group.trim.isEmpty) Future.failed(HttpError.badRequest("Nor reason or group cannot be empty"))
      else Future.unit
      rejection = RejectionReason(
        id = UUID.randomUUID().toString,
        reason = reason,
        group = group,
        userCreated = user.userId,
        timeCreated = now,
        userLastUpdated = user.userId,
        timeLastUpdated = now,
        version = 1,
        traceId = traceId.toString
      )
      _ <- networkRejectionsRepo.add(networkId, Seq(rejection))
    } yield rejection.id
  }

  def delete(networkId: NetworkId, reasonIds: Seq[String], user: UserACL)
    (implicit traceId: TraceId): Future[Boolean] = {
    val capabilities: Set[String] = getAvailableAccessKeysForCapabilities(EditCapabilities, user)

    for {
      _ <- toNotFound(networksRepo.entitledFind(networkId, capabilities))
      r <- networkRejectionsRepo.delete(networkId, reasonIds)
    } yield r
  }

  private def toNotFound(networkOptF: Future[Option[Network]]): Future[Unit] = {
    for {
      networkOpt <- networkOptF
      r <- if (networkOpt.isEmpty) Future.failed(HttpError.notFound("Cannot find network. Please check if id is correct and you have an access"))
      else Future.unit
    } yield r
  }
}
