package com.simonmarkets.networks.service

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.domain.Attestation
import com.simonmarkets.networks.repository.NetworkAttestationsRepository
import simon.Id.NetworkId

import java.time.Instant
import java.util.UUID
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class NetworkAttestationService(networkAttestationsRepo: NetworkAttestationsRepository) {

  def list(networkId: NetworkId, user: UserACL)(implicit traceId: TraceId): Future[Seq[Attestation]] = {

    for {
      _ <- if (user.networkId == networkId || user.userPurviewIds.contains(networkId) || user.capabilities.contains(Capabilities.Admin)) Future.unit
      else HttpError.forbidden("You don't have permissions for this action.").future
      attestations <- networkAttestationsRepo.list(networkId)
      filteredAttestations = if (user.capabilities.contains(Capabilities.Admin)) attestations else
      attestations.filter(attestation => attestation.customRoles.forall(roles => roles.intersect(user.customRoles).nonEmpty))
    } yield filteredAttestations
  }

  def create(networkId: NetworkId, attestationText: String, customRoles: Option[Set[String]], user: UserACL)
    (implicit traceId: TraceId): Future[Attestation] = {

    val now = Instant.now()

    val attestation: Attestation = Attestation(
      id = UUID.randomUUID().toString,
      attestationText = attestationText,
      customRoles = customRoles,
      version = 1,
      timeCreated = now,
      userCreated = user.userId,
      timeLastUpdated = now,
      userLastUpdated = user.userId
    )

    for {
      _ <- if (user.capabilities.contains(Capabilities.Admin)) Future.unit
      else HttpError.forbidden("You don't have permissions for this action.").future
      attestation <- networkAttestationsRepo.create(networkId, attestation)
    } yield attestation
  }

  def delete(networkId: NetworkId, attestationId: String, user: UserACL)
    (implicit traceId: TraceId): Future[Boolean] = {

    for {
      _ <- if (user.capabilities.contains(Capabilities.Admin)) Future.unit
      else HttpError.forbidden("You don't have permissions for this action.").future
      isDeleted <- networkAttestationsRepo.delete(networkId, attestationId)
    } yield isDeleted
  }

  def update(networkId: NetworkId, id: String, attestationText: String, customRoles: Option[Set[String]], user: UserACL)
    (implicit traceId: TraceId): Future[Long] = {
    for {
      _ <- if (user.capabilities.contains(Capabilities.Admin)) Future.unit
      else HttpError.forbidden("You don't have permissions for this action.").future
      attestationOpt <- networkAttestationsRepo.update(networkId, id, attestationText, customRoles, user.userId)
    } yield attestationOpt
  }
}
