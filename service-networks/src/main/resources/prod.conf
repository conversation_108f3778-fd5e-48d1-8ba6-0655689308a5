include classpath("service-networks-common.conf")

run-mode {
  type = lambda-mode
}

acl-client-config {
  http-client {
    auth {
      type = OAuth
      client-id = "0oa2h4re4ym0Fh3s82p7"
      client-secret-file = ""
      site = "sm:applicationconfig-issuer-uri"
      authorization-path = ""
      token-path = "/v1/token"
      scope = "read_product_data"
      token-type {
        type = Cookie
        name = SimSSO
      }
      client-secret = "sm:applicationconfig-oktaclientsecret"
      proxy {
        port = 3128
        address = "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
      }
    }
    proxy {
      port = 3128
      address = "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
    }
  }
  base-url = "https://origin-dc1.api.simonmarkets.com/simon/api"
  cache-config {
    enabled = true
    config {
      service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
      signin-region = "us-east-1"
    }
  }
}

no-auth-client-config {
  auth {
    type = NoAuth
  }
  proxy {
    port = 3128
    address = "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
  }
}

authentication {
  type = "jwt"
  jwt {
    source {
      type = "cookieOrHeader"
      name = "SimSSO:X-Simon-AccessToken"
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clock-offset-in-seconds = 0
      proxy-host = "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
      proxy-port = 3128
    }
  }
  user-info {
    source {
      type = "header"
      name = "x-userinfo"
    }
  }
}

okta-config {
  client-config {
    okta-org-url = "https://auth.simonmarkets.com"
    okta-auth-token = "sm:applicationconfig-okta-api-token"
    cache-ttl = 30.seconds
    proxy {
      port = 3128
      host = "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
    }
  }

  service-config {
    create {
      activate = true,
      setPassword = true,
      expirePassword = false
    }

    profile {
      network-name = "network"
      skip-secondary-emails = ["@simonmarkets.com"]
      secondary-email = "<EMAIL>"
    }
  }
  enabled = true
}

cloudfront-url-config {
  global-domain-name = "cdn.simon.io"
  domestic-domain-name = "cdn.simonmarkets.com"
  cloudfront-key-pair-id = "sm:cloudFrontKeyPairId"
  cloudfront-private-key = "sm:binary:cloudFrontPrivateKeyFile"
  expiration-millis = "50400000"
}

icn-kms-config {
    icn-environment = "production"
    endpoint = "https://origin-dc1.api.simonmarkets.com/kms/api/v1"
}