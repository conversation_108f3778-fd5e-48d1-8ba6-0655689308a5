openapi: 3.0.0
info:
  title: Network Service V2
  version: 1.0.0
x-basepath: /simon/api
x-kong-service-defaults:
  read_timeout: 300000
tags:
  - name: service-networks
    description: Network Service
x-kong-plugin-aws-lambda:
  config:
    function_name: service-networks-${STAGE}-api
    aws_region: us-east-1
    invocation_type: RequestResponse
    awsgateway_compatible: true
    forward_request_body: true
    forward_request_headers: true
    forward_request_method: true
    forward_request_uri: true
    is_proxy_integration: true
    skip_large_bodies: false
    proxy_url: ${HTTP_PROXY}
    proxy_scheme: https
    log_type: None

paths:
  /v2/networks/ids:
    x-scopes:
      - admin
      - simon-system-user
      - fa-manager
      - fa
      - issuer
      - wholesaler
    get:
      summary: Get network IDs
      responses:
        200:
          description: Available network IDs
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}

  /v2/networks/get-sparse-networks:
    x-kong-route-defaults:
      regex_priority: 10
    x-scopes:
      - admin
      - fa-manager
      - fa
      - issuer
      - wholesaler
    get:
      summary: Get networks
      tags: organizations
      responses:
        200:
          description: Available networks
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ${com.simonmarkets.networks.common.VisibleNetworkView}

  /v2/networks/uptime:
    get:
      x-disabled-plugins:
        - openId
        - request-validator
      x-scopes:
        - admin
        - simon-system-user
      tags:
        - users
      summary: Retrieve service uptime
      responses:
        200:
          description: Success
  /v2/networks/info:
    get:
      x-scopes:
        - admin
        - simon-system-user
      tags:
        - users
      summary: Retrieve service info
      responses:
        200:
          description: Success
  /v2/networks/healthcheck:
    get:
      x-disabled-plugins:
        - openId
        - request-validator
      x-scopes:
        - admin
        - simon-system-user
      tags:
        - users
      summary: Retrieve service healthcheck
      responses:
        200:
          description: Success
  /v2/networks:
    x-kong-route-defaults:
      regex_priority: -15
    x-scopes:
      - admin
      - fa-manager
      - internal
    get:
      # disabled because of a bug with array query params
      x-disabled-plugins: [ request-validator ]
      tags:
        - networks
      summary: Retrieve all networks
      parameters:
        - name: limit
          in: query
          required: false
          schema:
            $ref: ${com.simonmarkets.networks.api.OpenApiDefinitions.Limit}
        - name: next
          in: query
          required: false
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
        - name: subject
          in: query
          required: false
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
        - name: ids
          in: query
          required: false
          schema:
            maxItems: 100
            type: array
            items:
              $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
        - name: locationsView
          in: query
          required: false
          schema:
            type: boolean
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.OpenApiDefinitions.NetworkPageDefinition}
    post:
      tags:
        - networks
      summary: Create new Network
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.networks.common.api.UpsertNetworkRequest}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.common.NetworkView}

  /v2/networks/names:
    x-kong-route-defaults:
      regex_priority: -8
    x-scopes:
      - admin
      - fa-manager
    get:
      # disabled because of a bug with array query params
      x-disabled-plugins: [ request-validator ]
      tags:
        - networks
      summary: Retrieve network names for the given ids
      parameters:
        - name: id
          in: query
          required: true
          schema:
            maxItems: 100
            type: array
            items:
              $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.OpenApiDefinitions.NetworkIdToNameMap}

  /v2/networks/{id}:
    x-kong-route-defaults:
      regex_priority: -10
    x-scopes:
      - admin
      - simon-system-user
      - fa-manager
      - internal
    get:
      # disabled because of a bug with array query params (the same is done in the monolith)
      x-disabled-plugins: [ request-validator ]
      tags:
        - networks
      summary: Retrieve specified Network
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
        - name: external
          in: query
          required: false
          schema:
            description: Is the ${id} an external id
            type: boolean
        - name: subject
          in: query
          required: false
          description: The subject network of the external id. If not provided, is inferred from network of caller
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.StringWithWhiteSpace128Length}
      responses:
        200:
          description: Successful Operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.common.NetworkView}
    put:
      tags:
        - networks
      summary: Update Network
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.networks.common.api.UpsertNetworkRequest}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.common.NetworkView}

  /v2/networks/{id}/locations:
    x-kong-route-defaults:
      regex_priority: -5
    x-scopes:
      - admin
      - simon-system-user
      - fa-manager
    put:
      tags:
        - networks
      summary: Update Network Locations
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.networks.common.api.UpdateLocationsRequest}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.common.NetworkView}

  /v2/networks/{id}/snapshots:
    x-kong-route-defaults:
      regex_priority: -5
    x-scopes: [ admin ]
    get:
      tags:
        - networks
      summary: Retrieve specified Network Change Logs
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
      responses:
        200:
          description: Successful Operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ${com.simonmarkets.networks.common.ChangelogItem}

  /v2/networks/{networkId}/snapshots/{snapId}:
    x-kong-route-defaults:
      regex_priority: -4
    x-scopes: [ admin ]
    get:
      tags:
        - networks
      summary: Retrieve specified Network Snapshot
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
        - name: snapId
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: Successful Operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.common.NetworkView}

  /v2/networks/{id}/product-nomenclature:
    x-kong-route-defaults:
      regex_priority: -5
    get:
      x-kong-plugin-aws-lambda:
        config:
          function_name: service-networks-${STAGE}-product-nomenclature-api
          aws_region: us-east-1
          invocation_type: RequestResponse
          awsgateway_compatible: true
          forward_request_body: true
          forward_request_headers: true
          forward_request_method: true
          forward_request_uri: true
          is_proxy_integration: true
          skip_large_bodies: false
          proxy_url: ${HTTP_PROXY}
          proxy_scheme: https
          log_type: None
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      tags:
        - networks
      summary: List product names
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
      responses:
        200:
          description: Successful Operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.ProductNomenclatureList}
    post:
      x-kong-plugin-aws-lambda:
        config:
          function_name: service-networks-${STAGE}-product-nomenclature-api
          aws_region: us-east-1
          invocation_type: RequestResponse
          awsgateway_compatible: true
          forward_request_body: true
          forward_request_headers: true
          forward_request_method: true
          forward_request_uri: true
          is_proxy_integration: true
          skip_large_bodies: false
          proxy_url: ${HTTP_PROXY}
          proxy_scheme: https
          log_type: None
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      tags:
        - networks
      summary: Add a new product name
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.networks.api.request.AddProductNomenclatureRequest}
      responses:
        200:
          description: Successful Operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.ProductNomenclatureView}

  /v2/networks/{id}/product-nomenclature/{productNomenclatureId}:
    x-kong-route-defaults:
      regex_priority: -4
    get:
      x-kong-plugin-aws-lambda:
        config:
          function_name: service-networks-${STAGE}-product-nomenclature-api
          aws_region: us-east-1
          invocation_type: RequestResponse
          awsgateway_compatible: true
          forward_request_body: true
          forward_request_headers: true
          forward_request_method: true
          forward_request_uri: true
          is_proxy_integration: true
          skip_large_bodies: false
          proxy_url: ${HTTP_PROXY}
          proxy_scheme: https
          log_type: None
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      tags:
        - networks
      summary: List product names
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
        - name: productNomenclatureId
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: Successful Operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.ProductNomenclatureView}
    put:
      x-kong-plugin-aws-lambda:
        config:
          function_name: service-networks-${STAGE}-product-nomenclature-api
          aws_region: us-east-1
          invocation_type: RequestResponse
          awsgateway_compatible: true
          forward_request_body: true
          forward_request_headers: true
          forward_request_method: true
          forward_request_uri: true
          is_proxy_integration: true
          skip_large_bodies: false
          proxy_url: ${HTTP_PROXY}
          proxy_scheme: https
          log_type: None
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      tags:
        - networks
      summary: Update a product name
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
        - name: productNomenclatureId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.networks.api.request.UpdateProductNomenclatureRequest}
      responses:
        200:
          description: Successful Operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.ProductNomenclatureView}
    delete:
      x-kong-plugin-aws-lambda:
        config:
          function_name: service-networks-${STAGE}-product-nomenclature-api
          aws_region: us-east-1
          invocation_type: RequestResponse
          awsgateway_compatible: true
          forward_request_body: true
          forward_request_headers: true
          forward_request_method: true
          forward_request_uri: true
          is_proxy_integration: true
          skip_large_bodies: false
          proxy_url: ${HTTP_PROXY}
          proxy_scheme: https
          log_type: None
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      tags:
        - networks
      summary: List product names
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
        - name: productNomenclatureId
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: Successful Operation

  /v2/networks/encryptionClientKey:
    get:
      summary: Get multiple encryption client keys
      x-scopes:
        - admin
        - simon-system-user
      tags:
        - networks
      parameters:
        - name: id
          in: query
          required: true
          schema:
            maxItems: 100
            type: array
            items:
              $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.common.api.EncryptionClientKeyResponse}
    patch:
      summary: Create multiple encryption client keys
      x-scopes:
        - admin
        - simon-system-user
      tags:
        - networks
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.networks.common.api.EnsureEncryptionRequest}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ${com.simonmarkets.networks.common.NetworkView}

  /v1/external-id-type:
    x-kong-route-defaults:
      regex_priority: 10
    x-scopes: [ admin ]
    get:
      tags:
        - networks
        - users
      summary: Retrieve all external id types
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.common.ExternalIdTypeBatch}
    put:
      tags:
        - networks
        - users
      summary: Upsert and external id type
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.networks.common.ExternalIdType}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.common.ExternalIdType}

  /v1/external-id-type/{name}:
    x-kong-route-defaults:
      regex_priority: 10
    x-scopes: [ admin ]
    get:
      tags:
        - networks
        - users
      summary: Retrieve an external id type
      parameters:
        - name: name
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.StringWithWhiteSpace128Length}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.common.ExternalIdType}
    delete:
      tags:
        - networks
        - users
      summary: Delete an external id type
      parameters:
        - name: name
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.StringWithWhiteSpace128Length}
      responses:
        200:
          description: Successful delete
  /v1/network-entities:
    get:
      x-kong-plugin-aws-lambda:
        config:
          function_name: service-networks-${STAGE}-entities-api
          aws_region: us-east-1
          invocation_type: RequestResponse
          awsgateway_compatible: true
          forward_request_body: true
          forward_request_headers: true
          forward_request_method: true
          forward_request_uri: true
          is_proxy_integration: true
          skip_large_bodies: false
          proxy_url: ${HTTP_PROXY}
          proxy_scheme: https
          log_type: None
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
        - fa
        - issuer
        - wholesaler
      tags:
        - networks
      summary: Get network entities for networkId
      parameters:
        - name: networkIds
          in: query
          required: false
          schema:
            maxItems: 100
            type: array
            items:
              $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
        - name: keys
          in: query
          required: false
          schema:
            maxItems: 100
            type: array
            items:
              type: string
        - name: symbols
          in: query
          required: false
          schema:
            maxItems: 100
            type: array
            items:
              type: string
        - name: isFlattenedView
          in: query
          required: false
          schema:
            description: Is the response to be in flattened view
            type: boolean
      responses:
        200:
          description: Successful Operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.NetworkEntitiesView}
  /v1/network-entities/{networkId}:
    get:
      x-kong-route-defaults:
        regex_priority: 10
      x-kong-plugin-aws-lambda:
        config:
          function_name: service-networks-${STAGE}-entities-api
          aws_region: us-east-1
          invocation_type: RequestResponse
          awsgateway_compatible: true
          forward_request_body: true
          forward_request_headers: true
          forward_request_method: true
          forward_request_uri: true
          is_proxy_integration: true
          skip_large_bodies: false
          proxy_url: ${HTTP_PROXY}
          proxy_scheme: https
          log_type: None
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
        - fa
        - issuer
        - wholesaler
      tags:
        - networks
      summary: Get network entities for networkId
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
      responses:
        200:
          description: Successful Operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.NetworkEntitiesView}
    post:
      x-kong-plugin-aws-lambda:
        config:
          function_name: service-networks-${STAGE}-entities-api
          aws_region: us-east-1
          invocation_type: RequestResponse
          awsgateway_compatible: true
          forward_request_body: true
          forward_request_headers: true
          forward_request_method: true
          forward_request_uri: true
          is_proxy_integration: true
          skip_large_bodies: false
          proxy_url: ${HTTP_PROXY}
          proxy_scheme: https
          log_type: None
      x-scopes:
        - admin
      tags:
        - networks
      summary: Add a new entity information
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.networks.api.request.AddNetworkEntitiesRequest}
      responses:
        200:
          description: Successful Operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.NetworkEntitiesView}
    put:
      x-kong-plugin-aws-lambda:
        config:
          function_name: service-networks-${STAGE}-entities-api
          aws_region: us-east-1
          invocation_type: RequestResponse
          awsgateway_compatible: true
          forward_request_body: true
          forward_request_headers: true
          forward_request_method: true
          forward_request_uri: true
          is_proxy_integration: true
          skip_large_bodies: false
          proxy_url: ${HTTP_PROXY}
          proxy_scheme: https
          log_type: None
      x-scopes:
        - admin
      tags:
        - networks
      summary: Update an entity information
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.networks.api.request.UpdateNetworkEntitiesRequest}
      responses:
        200:
          description: Successful Operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.NetworkEntitiesView}
    delete:
      x-kong-plugin-aws-lambda:
        config:
          function_name: service-networks-${STAGE}-entities-api
          aws_region: us-east-1
          invocation_type: RequestResponse
          awsgateway_compatible: true
          forward_request_body: true
          forward_request_headers: true
          forward_request_method: true
          forward_request_uri: true
          is_proxy_integration: true
          skip_large_bodies: false
          proxy_url: ${HTTP_PROXY}
          proxy_scheme: https
          log_type: None
      x-scopes:
        - admin
      tags:
        - networks
      summary: Delete network entity info
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
      responses:
        200:
          description: Successful Operation
  /v1/network-entities/key/{key}:
    get:
      x-kong-plugin-aws-lambda:
        config:
          function_name: service-networks-${STAGE}-entities-api
          aws_region: us-east-1
          invocation_type: RequestResponse
          awsgateway_compatible: true
          forward_request_body: true
          forward_request_headers: true
          forward_request_method: true
          forward_request_uri: true
          is_proxy_integration: true
          skip_large_bodies: false
          proxy_url: ${HTTP_PROXY}
          proxy_scheme: https
          log_type: None
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
        - fa
        - issuer
        - wholesaler
      tags:
        - networks
      summary: Get network entities for networkId
      parameters:
        - name: key
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: Successful Operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.FlattenedNetworkEntitiesView}
  /v1/network-entities/proprietary-indices:
    get:
      x-kong-route-defaults:
        regex_priority: 20
      x-kong-plugin-aws-lambda:
        config:
          function_name: service-networks-${STAGE}-entities-api
          aws_region: us-east-1
          invocation_type: RequestResponse
          awsgateway_compatible: true
          forward_request_body: true
          forward_request_headers: true
          forward_request_method: true
          forward_request_uri: true
          is_proxy_integration: true
          skip_large_bodies: false
          proxy_url: ${HTTP_PROXY}
          proxy_scheme: https
          log_type: None
      x-scopes:
        - admin
        - simon-system-user
      tags:
        - networks
      summary: Get the prop indices for all network entities
      responses:
        200:
          description: Successful Operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.ProprietaryIndicesList}

  /v1/network-visuals:
    put:
      x-scopes:
        - admin
        - internal
      tags:
        - networks
      summary: Update Network Visuals
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.networks.common.domain.NetworkVisuals}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.common.domain.NetworkVisuals}

  /v1/network-visuals/{networkId}:
    get:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
        - internal
      tags:
        - networks
      summary: Get Network Visuals
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.common.domain.NetworkVisuals}
    delete:
      x-scopes:
        - admin
        - internal
      tags:
        - networks
      summary: Delete Network Visuals
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.common.domain.NetworkVisuals}
  /v1/networks/{networkId}/rejection-reasons:
    get:
      x-scopes:
        - admin
        - fa-manager
        - fa
        - issuer
        - wholesaler
      description: Returns network rejection reasons
      responses:
        200:
          description: List of rejection reasons
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.RejectionReasonsList}
    post:
      x-scopes:
        - admin
        - fa-manager
      description: Adds rejection reason to list of network rejection reasons
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.networks.api.request.RejectionReasonIdList}
      responses:
        200:
          description: One or more rejection reason added to the network
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.AddRejectionReasonsResponse}
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.ApiError}
  /v1/networks/{networkId}/rejection-reasons/custom:
    post:
      x-scopes:
        - admin
        - fa-manager
      description: Adds custom rejection reason to list of network rejection reasons
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.networks.api.request.CustomRejection}
      responses:
        200:
          description: One or more rejection reason added to the network
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.AddCustomRejectionResponse}
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.ApiError}
  /v1/networks/{networkId}/rejection-reasons/{reasonId}:
    delete:
      x-scopes:
        - admin
        - fa-manager
      description: Removes rejection reason from list of network rejection reasons
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
        - name: reasonId
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.EntityID}
      responses:
        200:
          description: Rejection reason removed from the network
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.DeleteRejectionReasonResponse}
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.ApiError}
  /v1/networks/{networkId}/attestations:
    get:
      x-scopes:
        - admin
        - fa-manager
        - fa
        - issuer
        - wholesaler
      description: Returns network attestations
      responses:
        200:
          description: List of attestations
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.AttestationView}
    post:
      x-scopes:
        - admin
        - fa-manager
      description: Adds attestation to list of network attestations
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.networks.api.request.CreateAttestationRequest}
      responses:
        200:
          description: New attestation added to the network
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.AttestationView}
        400:
          description: Invalid request body
  /v1/networks/{networkId}/attestations/{attestationId}:
    post:
      x-scopes:
        - admin
        - fa-manager
      description: Updates existing attestation in list of network attestations
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.networks.api.request.UpdateAttestationRequest}
      responses:
        200:
          description: Updated attestation
        400:
          description: Invalid request body
    delete:
      x-scopes:
        - admin
        - fa-manager
      description: Removes attestation from list of network attestations
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
        - name: attestationId
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.EntityID}
      responses:
        200:
          description: Attestation removed from the network
        404:
          description: Attestation not found
  /v1/networks/{networkId}/disclaimers:
    get:
      x-scopes:
        - admin
        - fa-manager
        - fa
        - issuer
        - wholesaler
      description: Returns network disclaimer by type
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
        - name: type
          in: query
          required: true
          schema:
            $ref: ${com.simonmarkets.networks.domain.DisclaimerType}
      responses:
        200:
          description: Disclaimer of specified type
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.api.response.DisclaimerView}
        404:
          description: Disclaimer of specified type and network not found
    post:
      x-scopes:
        - admin
        - fa-manager
      description: Adds disclaimer of specified type to the network
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
        - name: type
          in: query
          required: true
          schema:
            $ref: ${com.simonmarkets.networks.domain.DisclaimerType}
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.networks.api.request.UpdateDisclaimerRequest}
      responses:
        200:
          description: New disclaimer added to the network
        400:
          description: Invalid request body
    delete:
      x-scopes:
        - admin
        - fa-manager
      description: Delete disclaimer of specified type to the network
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
        - name: type
          in: query
          required: true
          schema:
            $ref: ${com.simonmarkets.networks.domain.DisclaimerType}
      responses:
        200:
          description: Disclaimer deleted
        404:
          description: Disclaimer of this type not found for provided networkId
