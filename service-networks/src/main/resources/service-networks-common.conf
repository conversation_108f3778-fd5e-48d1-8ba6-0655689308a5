info {
  name = "Network Service"
  description = "A service responsible for creating / editing networks and setting up network configurations"
  repository = "networks"
  module = "service-networks"
  version = "${project.version}"
  revision = "${git.commit.id.abbrev}"
  build-timestamp = "${build.timestamp.millis}"
  product-line = "CrossProduct"
  used-by = ["CrossProduct", "Storefront", "Spectrum", "SMA", "Annuity", "ETF", "StructuredInvestment"]
  owner = "CrossProduct"
  support-distro = ["<EMAIL>"]
  metrics-url = "",
  documentation-url = "https://simonmarkets.atlassian.net/wiki/spaces/ENG/pages/445349961/REST+Easy+Framework"
}


config.resolvers {
  sm {
    region = "us-east-1"
  }
  file {
    root = "app-config/dev"
  }
}

mongo-db {
  networks {
    collection = "networks_new"
    database = "pipg"
  }
  network-visuals {
    collection = "network.visuals"
    database = "pipg"
  }
  networks-snapshots {
    collection = "networks_new.snapshots"
    database = "pipg"
  }
  product-nomenclature {
    collection = "productNomenclature"
    database = "pipg"
  }
  external-id-types {
    collection = "users.externalIdTypes",
    database = "pipg"
  }
  network-entities {
    collection = "networkEntities",
    database = "pipg"
  }
  network-rejection-reasons {
    collection = "rejectionReasons",
    database = "pipg"
  }
  general-rejection-reasons {
      collection = "generalRejectionReasons",
      database = "pipg"
  }
  network-attestations {
    collection = "orderAttestations",
    database = "pipg"
  }
  network-disclaimers {
    collection = "distributorDisclaimers",
      database = "pipg"
  }
  client {
    app-name = "service-networks"
    connection = "sm:config:applicationconfig-mongo-auth-pipg"
  }
}

system-routes {
  service-up {
    path = "simon/api/v2/networks/uptime"
  }
  service-info {
    path = "simon/api/v2/networks/info"
  }
  health-check {
    path = "simon/api/v2/networks/healthcheck"
  }
}

service-config {
  external-targets = ["MixPanel"]
  oms-alias = "twd"
  paging {
    default-limit = 1000
  }

  activation {
    skip-emails = ["@simonmarkets.com"]
  }
  transaction-retry-limit = 3
  default-custom-roles-config = [
    {
      "role": "EqPIPGFA", "capabilities": ["viewUIMyProductsDashboard", "viewUILifecycleEventsDashboard", "viewUINotificationsNewsfeedDashboard", "viewUISearchBar", "viewUISPPortal",
      "viewUILearningCenterV2", "editUserViewViaOwner", "viewUserViewViaOwner", "viewInvestmentDocument", "viewUserViaGuid", "viewUserViaNetwork", "viewOrderViaOwner", "cancelOrderViaOwner",
      "viewActiveTracksViaTrackId", "viewAllTracksViaTrackId", "viewTrackActivityViaOwner", "editTrackActivityViaOwner", "ViewContentViaContentId", "viewContentActivityViaOwner", "editContentActivityViaOwner",
      "viewHoldingViaFaNumber", "viewAccountViaFaNumber", "viewHouseholdViaFaNumber", "viewUILearningCenter", "viewUILearningCenterV2StructuredInvestments", "viewUIUseBrokerDealer",
      "viewUIAnalyticsAsOfDate", "viewNonRegSSPPastOfferingsViaViewPayoffEntitlements", "viewApprovedSPOfferingViaNetwork", "tradeSPOfferingViaNetwork", "viewSPOfferingViaUserGuid",
      "tradeSPOfferingViaUserGuid", "viewSPOfferingViaFaNumber", "tradeSPOfferingViaFaNumber", "viewSPOfferingViaLocation", "tradeSPOfferingViaLocation", "viewUILifecyclePortalSP", "viewUIHeatmap", "viewNewSILifecycleDashboard", "uploadUserViaGuid", "viewNetworkViaNetwork",
      "viewUISPDetailsViaDetailsPayoffEntitlements", "viewUIOrderCreateNewOrderAccount", "viewOrderBulkUploadViaOwner", "viewOrderBulkUploadViaUserCreated", "viewForYouCardsViaUser","viewForYouCardsViaNetwork","viewForYouCardsViaPurview", "fa"
    ]
    },
    {
      "role": "EqPIPGFAManager", "capabilities": ["viewUIMyProductsDashboard", "viewUILifecycleEventsDashboard", "viewUINotificationsNewsfeedDashboard", "viewUISearchBar",
      "viewUISPPortal", "viewUILearningCenterV2", "editUserViewViaOwner", "viewUserViewViaOwner", "viewInvestmentDocument", "viewUserViaLocation", "viewUserViaNetwork",
      "viewOrderViaLocation", "viewOrderViaNetwork", "approveOrderViaApprovers", "editOrderViaLocation", "editOrderViaNetwork", "cancelOrderViaLocation", "cancelOrderViaNetwork", "viewActiveTracksViaTrackId", "viewAllTracksViaTrackId",
      "viewTrackActivityViaOwner", "editTrackActivityViaOwner", "viewTrackActivityViaNetwork", "ViewContentViaContentId", "viewContentActivityViaOwner", "editContentActivityViaOwner",
      "viewContentActivityViaNetwork", "viewHoldingViaLocation", "viewAccountViaLocation", "viewHouseholdViaLocation", "viewUISPManagementDashboard", "viewUIBroadcastInNetworkOnly",
      "viewUIExportCurrentOfferingNetworkIOIs", "viewUIUseBrokerDealer", "viewUILearningCenter", "viewUILearningCenterDetails", "viewUILearningCenterV2StructuredInvestments", "viewUIAnalyticsAsOfDate",
      "viewNonRegSSPPastOfferingsViaViewPayoffEntitlements", "viewSPOfferingViaNetworkIssuerPurview", "tradeSPOfferingViaNetworkIssuerPurview", "approveSPOfferingViaNetworkIssuerPurview",
      "viewUnapprovedSPOfferingViaNetwork", "viewApprovedSPOfferingViaNetwork", "tradeSPOfferingViaNetwork", "approveSPOfferingViaNetwork", "viewSPOfferingViaUserGuid", "tradeSPOfferingViaUserGuid",
      "viewSPOfferingViaFaNumber", "tradeSPOfferingViaFaNumber", "viewSPOfferingViaLocation", "tradeSPOfferingViaLocation", "viewUIPendingOfferings", "viewUILifecyclePortalSP", "viewUIHeatmap", "viewNewSILifecycleDashboard", "uploadUserViaGuid", "viewNetworkViaNetwork",
      "viewUISPDetailsViaDetailsPayoffEntitlements", "viewUIOrderCreateNewOrderAccount", "viewOrderBulkUploadViaOwner", "viewOrderBulkUploadViaUserCreated", "viewOrderBulkUploadViaNetwork", "viewForYouCardsViaUser","viewForYouCardsViaNetwork","viewForYouCardsViaPurview","fa-manager"
    ]
    },
    {
      "role": "AnnuitiesFA", "capabilities": ["editUserViewViaOwner", "viewUserViewViaOwner", "viewTrackActivityViaOwner", "editTrackActivityViaOwner", "ViewContentViaContentId",
      "viewContentActivityViaOwner", "editContentActivityViaOwner", "viewActiveTracksViaTrackId", "viewAllTracksViaTrackId", "viewUILearningCenter", "viewUISPPortal", "viewUISVAPortal",
      "viewUILearningCenterV2", "viewUIFIAPortal", "viewUIClientHoldingsGroupByHousehold", "viewUILearningCenterV2StructuredInvestments", "viewUILearningCenterV2Annuities", "viewUIAnalyticsAsOfDate",
      "viewNonRegSNonRegisteredAnnuityPastOffering", "viewApprovedNonRegisteredAnnuityOfferingViaNetwork", "tradeNonRegisteredAnnuityOfferingViaNetwork", "viewNonRegisteredAnnuityOfferingViaUserGuid",
      "tradeNonRegisteredAnnuityOfferingViaUserGuid", "viewNonRegisteredAnnuityOfferingViaFaNumber", "tradeNonRegisteredAnnuityOfferingViaFaNumber", "viewNonRegisteredAnnuityOfferingViaLocation",
      "tradeNonRegisteredAnnuityOfferingViaLocation", "viewNonRegSRegisteredAnnuityPastOffering", "viewApprovedRegisteredAnnuityOfferingViaNetwork", "tradeRegisteredAnnuityOfferingViaNetwork",
      "viewRegisteredAnnuityOfferingViaUserGuid", "tradeRegisteredAnnuityOfferingViaUserGuid", "viewRegisteredAnnuityOfferingViaFaNumber", "tradeRegisteredAnnuityOfferingViaFaNumber",
      "viewRegisteredAnnuityOfferingViaLocation", "tradeRegisteredAnnuityOfferingViaLocation", "viewUserViaNetwork", "viewUISPDetailsViaDetailsPayoffEntitlements", "viewUIOrderCreateNewOrderAccount", "viewForYouCardsViaUser","viewForYouCardsViaNetwork","viewForYouCardsViaPurview","fa"]
    },
    {
      "role": "HomeOfficeAnnuitiesManager", "capabilities": ["editUserViewViaOwner", "viewUserViewViaOwner", "viewActiveTracksViaTrackId", "viewAllTracksViaTrackId", "viewTrackActivityViaOwner", "editTrackActivityViaOwner",
      "ViewContentViaContentId", "viewContentActivityViaOwner", "editContentActivityViaOwner", "viewUILearningCenter", "viewUISPPortal", "viewUILearningCenterMyNetwork", "viewUISVAPortal",
      "viewUILearningCenterV2", "viewUIFIAPortal", "viewUIHoldings", "viewUILearningCenterV2MyNetworkProductTraining", "viewUIClientHoldingsGroupByHousehold",
      "viewUILearningCenterV2StructuredInvestments", "viewUILearningCenterDetails", "viewUISPManagementDashboard", "viewUILearningCenterV2Annuities", "viewUIAnalyticsAsOfDate",
      "viewNonRegSNonRegisteredAnnuityPastOffering", "viewNonRegisteredAnnuityOfferingViaNetworkIssuerPurview", "tradeNonRegisteredAnnuityOfferingViaNetworkIssuerPurview",
      "approveNonRegisteredAnnuityOfferingViaNetworkIssuerPurview", "viewUnapprovedNonRegisteredAnnuityOfferingViaNetwork", "viewApprovedNonRegisteredAnnuityOfferingViaNetwork",
      "tradeNonRegisteredAnnuityOfferingViaNetwork", "approveNonRegisteredAnnuityOfferingViaNetwork", "viewNonRegisteredAnnuityOfferingViaUserGuid", "tradeNonRegisteredAnnuityOfferingViaUserGuid",
      "viewNonRegisteredAnnuityOfferingViaFaNumber", "tradeNonRegisteredAnnuityOfferingViaFaNumber", "viewNonRegisteredAnnuityOfferingViaLocation", "tradeNonRegisteredAnnuityOfferingViaLocation",
      "viewNonRegSRegisteredAnnuityPastOffering", "viewRegisteredAnnuityOfferingViaNetworkIssuerPurview", "tradeRegisteredAnnuityOfferingViaNetworkIssuerPurview", "approveRegisteredAnnuityOfferingViaNetworkIssuerPurview",
      "viewUnapprovedRegisteredAnnuityOfferingViaNetwork", "viewApprovedRegisteredAnnuityOfferingViaNetwork", "tradeRegisteredAnnuityOfferingViaNetwork", "approveRegisteredAnnuityOfferingViaNetwork",
      "viewRegisteredAnnuityOfferingViaUserGuid", "tradeRegisteredAnnuityOfferingViaUserGuid", "viewRegisteredAnnuityOfferingViaFaNumber", "tradeRegisteredAnnuityOfferingViaFaNumber", "viewRegisteredAnnuityOfferingViaLocation",
      "tradeRegisteredAnnuityOfferingViaLocation", "viewUIPendingOfferings", "viewUserViaNetwork", "viewUISPDetailsViaDetailsPayoffEntitlements", "viewForYouCardsViaUser","viewForYouCardsViaNetwork","viewForYouCardsViaPurview","fa-manager"]
    },
    {
      "role": "AltsFA", "capabilities": ["viewUIAltsSubscriptionWorkflow", "viewUIPlusSubscribeAlts", "viewAltProductViaViewPayoffEntitlement", "viewApprovedAltOfferingViaNetworkAndViewPayoffEntitlement", "viewUIAltsPortal",
      "viewUISubscribeInvestmentEntityOptions", "viewUILearningCenterV2AlternativeInvestments", "viewForYouCardsViaUser","viewForYouCardsViaNetwork","viewForYouCardsViaPurview","fa"]
    },
    {
      "role": "AltsFAManager", "capabilities": ["viewUIAltsSubscriptionWorkflow", "viewAltOfferingViaNetworkManager", "approveAltOfferingViaNetworkManager", "viewAltOfferingViaPurviewNetworkManager", "approveAltOfferingViaPurviewNetworkManager", "viewUILearningCenterV2AlternativesMyNetworkEducation", "viewUIPlusSubscribeAlts", "viewAltProductViaViewPayoffEntitlement", "viewApprovedAltOfferingViaNetworkAndViewPayoffEntitlement", "viewUIAltsPortal",
      "viewUISubscribeInvestmentEntityOptions", "viewUILearningCenterV2AlternativeInvestments", "viewUIAltsPendingOfferings", "viewUIAltsStagedOfferings", "viewUIAltsClosedOfferings", "viewApprovedAltOfferingViaUser", "viewPendingAltOfferingViaUser", "viewClosedAltOfferingViaUser",
      "viewApprovedAltOfferingViaLocation", "viewPendingAltOfferingViaLocation", "viewClosedAltOfferingViaLocation", "viewApprovedAltOfferingViaFaNumber", "viewPendingAltOfferingViaFaNumber", "viewClosedAltOfferingViaFaNumber", "viewForYouCardsViaUser","viewForYouCardsViaNetwork","viewForYouCardsViaPurview","fa-manager"
    ]
    },
    {
      "role": "Issuer", "capabilities": ["viewUIMyProductsDashboard", "viewUILifecycleEventsDashboard", "viewUINotificationsNewsfeedDashboard", "viewUISearchBar", "viewUISPPortal", "viewUILearningCenterV2",
      "editUserViewViaOwner", "viewUserViewViaOwner", "viewInvestmentDocument", "editInvestmentDocument", "viewActiveTracksViaTrackId", "viewAllTracksViaTrackId", "viewTrackActivityViaOwner", "editTrackActivityViaOwner", "ViewContentViaContentId",
      "viewContentActivityViaOwner", "editContentActivityViaOwner", "viewUserViaGuid", "viewUserViaNetwork", "viewOrderViaPurview", "viewUISPManagementDashboard", "viewUIMultipleNetworks", "viewUIBroadcastOutsideNetwork",
      "viewUIShareOutsideNetwork", "viewUIUseBrokerDealer", "viewUILearningCenter", "viewUILearningCenterV2StructuredInvestments", "viewUIAnalyticsAsOfDate", "viewSPOfferingViaNetworkTypeAndIssuerSymbol",
      "editSPOfferingViaNetworkTypeAndIssuerSymbol", "tradeSPOfferingViaNetworkTypeAndIssuerSymbol", "viewSPOfferingViaNetworkIssuerPurview", "tradeSPOfferingViaNetworkIssuerPurview", "viewUIPendingOfferings",
      "viewUILifecyclePortalSP", "viewUIHeatmap", "viewNewSILifecycleDashboard", "uploadUserViaGuid", "viewNetworkViaNetwork", "viewUISPDetailsViaDetailsPayoffEntitlements", "viewForYouCardsViaUser","viewForYouCardsViaNetwork","viewForYouCardsViaPurview","issuer"]
    },
    {
      "role": "Wholesaler", "capabilities": ["viewUIMyProductsDashboard", "viewUILifecycleEventsDashboard", "viewUINotificationsNewsfeedDashboard", "viewUISearchBar", "viewUISPPortal", "viewUILearningCenterV2",
      "editUserViewViaOwner", "viewUserViewViaOwner", "viewInvestmentDocument", "viewActiveTracksViaTrackId", "viewAllTracksViaTrackId", "viewTrackActivityViaOwner", "editTrackActivityViaOwner", "ViewContentViaContentId",
      "viewContentActivityViaOwner", "editContentActivityViaOwner", "viewUserViaGuid", "viewUserViaNetwork", "viewOrderViaPurview", "viewUISPManagementDashboard", "viewUIMultipleNetworks",
      "viewUIBroadcastOutsideNetwork", "viewUIShareOutsideNetwork", "viewUIUseBrokerDealer", "viewUILearningCenter", "viewUILearningCenterV2StructuredInvestments", "viewUIWholesalerPortal",
      "viewUIAnalyticsAsOfDate", "viewNonRegSSPPastOfferingsViaViewPayoffEntitlements", "tradeUnapprovedSPOfferingViaNetwork", "editSPOfferingViaNetwork", "viewSPOfferingViaNetworkIssuerPurview",
      "tradeSPOfferingViaNetworkIssuerPurview", "viewUIPendingOfferings", "viewUILifecyclePortalSP", "viewUIHeatmap", "viewNewSILifecycleDashboard", "uploadUserViaGuid", "viewNetworkViaNetwork",
      "viewUISPDetailsViaDetailsPayoffEntitlements", "viewForYouCardsViaUser","viewForYouCardsViaNetwork","viewForYouCardsViaPurview","wholesaler"
    ]
    },
    {
      "role": "DeveloperHubUser", "capabilities": ["viewDeveloperHubUserPreferencesViaGuid", "editDeveloperHubUserPreferencesViaGuid", "viewDeveloperHubUserSettingsViaGuid", "viewDeveloperHubServiceViaGuid", "viewDeveloperHubRouteViaGuid"]
    },
    {
      "role": "DefaultApiUserRole", "capabilities": []
    }
  ]
  encryption-client-prefix = "applicationkey-kms-",
  admin-network-type-config = "sm:config:applicationconfig-admin-network-type-config",
  default-domiciles = ["US"]
}
