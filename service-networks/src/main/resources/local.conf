include classpath("service-networks-common.conf")

run-mode {
  type = "server-mode"
  http-server-config {
    port = 1984
    interface = "0.0.0.0"
    ssl {
      enabled = false
      keystore {
        type = ""
        file = ""
        password = ""
      }
    }
  }
}

acl-client-config {
  http-client {
    auth {
      type = OAuth
      client-id = "0oaevl54gplnFvyx70h7"
      client-secret-file = "<YOUR_PATH_TO_SECRET_FILE>/config/0oaevl54gplnFvyx70h7.secret"
      site = "sm:applicationconfig-issuer-uri"
      authorization-path = ""
      token-path = "/v1/token"
      scope = "read_product_data"
      token-type {
        type = Cookie
        name = SimSSO
      }
      client-secret = "sm:Okta-secret"
    }
  }
  base-url = "https://origin-a.dev.simonmarkets.com/simon/api"
  cache-config {
    enabled = true
    config {
      service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
      signin-region = "us-east-1"
    }
  }
}

no-auth-client-config {
  auth {
    type = NoAuth
  }
}

authentication {
  type = "jwt"
  jwt {
    source {
      type = "bearer"
      name = ""
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      keys-url = "sm:applicationconfig-okta-validator"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clock-offset-in-seconds = 0
    }
  }
  user-info {
    source {
      type = "header"
      name = "x-userinfo"
    }
  }
}

mongo-db {
  client {
    app-name = "service-networks"
    connection {
      url = "mongodb://localhost:27017/pipg?retryWrites=true&w=majority&readPreference=secondaryPreferred"
      authentication {
        type = none
      }
    }
  }
}

okta-config {
  client-config {
    okta-org-url = "https://simonpoc.okta.com"
    okta-auth-token = "fake"
    cache-ttl = 30.seconds
  }

  service-config {
    create {
      activate = true,
      setPassword = true,
      expirePassword = false
    }

    profile {
      network-name = "alphaNetwork"
      skip-secondary-emails = ["@simonmarkets.com"]
      secondary-email = "<EMAIL>"
    }
  }
  enabled = false
}

cloudfront-url-config {
  global-domain-name = "alpha.cdn.simon.io"
  domestic-domain-name = "d3imx7n7j1sdie.cloudfront.net"
  cloudfront-key-pair-id = "sm:cloudFrontKeyPairId"
  cloudfront-private-key = "sm:binary:cloudFrontPrivateKeyFile"
  expiration-millis = "50400000"
}

icn-kms-config {
    icn-environment = "development"
    endpoint = "https://origin-a.dev.simonmarkets.com/kms/api/v1"
}