package com.simonmarkets.networks.service

import akka.http.scaladsl.model.StatusCodes
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.repository.NetworkAttestationsRepository
import org.mockito.Mockito.{never, reset, verify, when}
import org.mockito.{ArgumentMatchers => MM}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, Matchers}
import simon.Id.NetworkId
import com.simonmarkets.networks.domain.Attestation
import java.time.Instant
import scala.concurrent.Future

class NetworkAttestationServiceSpec extends AsyncWordSpec with MockitoSugar with Matchers with BeforeAndAfterEach {

  private implicit val traceId: TraceId = TraceId("network-attestation-spec")

  private val networkAttestationRepo = mock[NetworkAttestationsRepository]
  private val networkAttestationService = new NetworkAttestationService(networkAttestationRepo)

  private val networkId1 = NetworkId("network-id-1")
  private val nonUserNetworkId = NetworkId("non-user-network")

  val nonAdmin = TestUserACL("non-admin-user", networkId1, capabilities = Set(Capabilities.DefaultCapability))
  val admin = TestUserACL("admin-user", networkId1, capabilities = Set(Capabilities.Admin))
  val userWithoutNet = TestUserACL("non-admin-user", nonUserNetworkId, capabilities = Set(Capabilities.DefaultCapability))
  private val attestation1 = Attestation(
    id = "attestation-id-1",
    attestationText = "Some attestation text",
    userCreated = admin.userId,
    timeCreated = Instant.now(),
    userLastUpdated = admin.userId,
    timeLastUpdated = Instant.now(),
    version = 1
  )

  private val attestation2 = Attestation(
    id = "attestation-id-2",
    attestationText = "Some attestation text",
    userCreated = admin.userId,
    timeCreated = Instant.now(),
    userLastUpdated = admin.userId,
    timeLastUpdated = Instant.now(),
    version = 1
  )

  private val attestation3 = Attestation(
    id = "attestation-id-3",
    attestationText = "Some attestation text",
    customRoles = Some(Set("Issuer")),
    userCreated = admin.userId,
    timeCreated = Instant.now(),
    userLastUpdated = admin.userId,
    timeLastUpdated = Instant.now(),
    version = 1
  )


  override def beforeEach(): Unit = {
    reset(networkAttestationRepo)
  }

  "NetworkAttestationService" can {
    "list" should {
      "return all attestations for admin with required network" in {
        when(networkAttestationRepo.list(networkId1)).thenReturn(Future.successful(Seq(attestation1, attestation2, attestation3)))

        networkAttestationService.list(networkId1, admin).map { list =>
          list should be(Seq(attestation1, attestation2, attestation3))
        }
      }

      "return list of attestations for user with required network" in {
        when(networkAttestationRepo.list(networkId1)).thenReturn(Future.successful(Seq(attestation1, attestation2, attestation3)))

        networkAttestationService.list(networkId1, nonAdmin).map { list =>
          list should be(Seq(attestation1, attestation2))
        }
      }

      "return list of attestations for user with required network and specific role" in {
        when(networkAttestationRepo.list(networkId1)).thenReturn(Future.successful(Seq(attestation1, attestation2, attestation3)))
        val adminIssuer = nonAdmin.copy(customRoles = Set("Issuer"))

        networkAttestationService.list(networkId1, adminIssuer).map { list =>
          list should be(Seq(attestation1, attestation2, attestation3))
        }
      }

      "throws exception for user without required network" in {
        val r = recoverToExceptionIf[HttpError] {
          networkAttestationService.list(networkId1, userWithoutNet)
        }

        r.map { httpError =>
          verify(networkAttestationRepo, never()).list(MM.any[NetworkId])(MM.any[TraceId])
          httpError.status should be(StatusCodes.Forbidden)
        }
      }

      "return empty list if network not found" in {
       when(networkAttestationRepo.list(networkId1)).thenReturn(Future.successful(Seq.empty))

        networkAttestationService.list(networkId1, admin).map { list =>
          list should be(Seq.empty)
        }
      }
    }

    "create" should {
      "creates a new attestation for user with admin capabilities" in {
        val attestationText = "Some attestation text"
        when(networkAttestationRepo.create(MM.any[NetworkId], MM.any[Attestation])(MM.any[TraceId])).thenReturn(Future.successful(attestation1))

        networkAttestationService.create(networkId1, attestationText, None, admin) map { attestation =>
          verify(networkAttestationRepo).create(MM.any[NetworkId], MM.any[Attestation])(MM.any[TraceId])
          attestation shouldBe attestation1
        }
      }

      "throws exception for user without admin capabiities" in {
        val attestationText = "Some attestation text"
        val r = recoverToExceptionIf[HttpError] {
          networkAttestationService.create(networkId1, attestationText, None, nonAdmin)
        }

        r.map { httpError =>
          verify(networkAttestationRepo, never()).create(MM.any[NetworkId], MM.any[Attestation])(MM.any[TraceId])
          httpError.status should be(StatusCodes.Forbidden)
        }
      }
    }

    "update" should {
      "updates existing attestation in network attestation list" in {
        val attestationText = "Some attestation text"
        when(networkAttestationRepo.update(MM.any[NetworkId], MM.any[String], MM.any[String], MM.any[Option[Set[String]]],
          MM.any[String])(MM.any[TraceId])).thenReturn(Future.successful(1L))

        networkAttestationService.update(networkId1, attestation1.id, attestationText, None, admin) map { result =>
          verify(networkAttestationRepo).update(MM.any[NetworkId], MM.any[String], MM.any[String], MM.any[Option[Set[String]]],
            MM.any[String])(MM.any[TraceId])
          result shouldBe 1
        }
      }

      "throws exception for user without admin capabiities" in {
        val attestationText = "Some attestation text"
        val r = recoverToExceptionIf[HttpError] {
          networkAttestationService.update(networkId1, "att-id", attestationText, None, nonAdmin)
        }

        r.map { httpError =>
          verify(networkAttestationRepo, never()).update(MM.any[NetworkId], MM.any[String], MM.any[String], MM.any[Option[Set[String]]]
            , MM.any[String])(MM.any[TraceId])
          httpError.status should be(StatusCodes.Forbidden)
        }
      }
    }

    "delete" should {
      "deletes existing attestation in network attestation list" in {
        when(networkAttestationRepo.delete(MM.any[NetworkId], MM.any[String])(MM.any[TraceId])).thenReturn(Future.successful(true))

        networkAttestationService.delete(networkId1, attestation1.id, admin) map { isDeleted =>
          verify(networkAttestationRepo).delete(MM.any[NetworkId], MM.any[String])(MM.any[TraceId])
          isDeleted shouldBe true
        }
      }

      "throws exception for user without admin capabiities" in {
        val r = recoverToExceptionIf[HttpError] {
          networkAttestationService.delete(networkId1, "att-id", nonAdmin)
        }

        r.map { httpError =>
          verify(networkAttestationRepo, never()).delete(MM.any[NetworkId], MM.any[String])(MM.any[TraceId])
          httpError.status should be(StatusCodes.Forbidden)
        }
      }
    }
  }

}
