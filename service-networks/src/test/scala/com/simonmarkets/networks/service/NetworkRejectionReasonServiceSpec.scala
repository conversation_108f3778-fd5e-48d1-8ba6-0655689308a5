package com.simonmarkets.networks.service

import akka.http.scaladsl.model.StatusCodes
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.capabilities.NetworksCapabilities._
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.repository.{GeneralRejectionReasonRepository, NetworkRejectionReasonRepository, NetworkRepository}
import org.mockito.Mockito._
import org.mockito.{ArgumentMatchers => MM}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, Matchers}
import simon.Id.NetworkId
import com.simonmarkets.networks.domain.Network
import java.time.Instant
import scala.concurrent.Future
import com.simonmarkets.networks.rejections.domain.RejectionReason
class NetworkRejectionReasonServiceSpec extends AsyncWordSpec with MockitoSugar with Matchers with BeforeAndAfterEach {

  private implicit val traceId: TraceId = TraceId("network-rejection-reason-spec")

  private val generalReasonRepo = mock[GeneralRejectionReasonRepository]
  private val networkReasonRepo = mock[NetworkRejectionReasonRepository]
  private val networkRepo = mock[NetworkRepository]
  private val networkRejectionReasonService = new NetworkRejectionReasonService(networkReasonRepo, generalReasonRepo,
    networkRepo)

  private val network1 = Network(
    id = "Network 1",
    name = "Network 1",
    entitlements = Set("admin", "viewNetworkViaNetwork:Network 1", "viewNetworkViaPurview:Network 1", "editNetworkViaNetwork:Network 1")
  )

  private val networkId1 = NetworkId(network1.id)

  private val user1 = TestUserACL(userId = "test-user-1", networkId = NetworkId(network1.id))
  private val rejectionReason1 = RejectionReason(
    id = "reason-id-1",
    reason = "Some reason",
    group = "Some group",
    userCreated = user1.userId,
    timeCreated = Instant.now(),
    userLastUpdated = user1.userId,
    timeLastUpdated = Instant.now(),
    version = 1,
    traceId = traceId.toString
  )

  private val rejectionReason2 = RejectionReason(
    id = "reason-id-1",
    reason = "Some reason",
    group = "Some group",
    userCreated = user1.userId,
    timeCreated = Instant.now(),
    userLastUpdated = user1.userId,
    timeLastUpdated = Instant.now(),
    version = 1,
    traceId = traceId.toString
  )

  override def beforeEach(): Unit = {
    reset(generalReasonRepo)
    reset(networkReasonRepo)
    reset(networkRepo)
  }

  "NetworkRejectionReasonService" can {
    "list" should {
      "return list of rejection reasons for network" in {
        when(networkRepo.entitledFind(networkId1, getAvailableAccessKeysForCapabilities(ViewCapabilities, user1)))
          .thenReturn(Future.successful(Some(network1)))
        when(networkReasonRepo.list(networkId1)).thenReturn(Future.successful(Seq(rejectionReason1, rejectionReason2)))

        networkRejectionReasonService.list(networkId1, user1).map { list =>
          list should be(Seq(rejectionReason1, rejectionReason2))
        }
      }

      "return empty list if network not found" in {
        when(networkRepo.entitledFind(networkId1, getAvailableAccessKeysForCapabilities(ViewCapabilities, user1)))
          .thenReturn(Future.successful(None))

        recoverToExceptionIf[HttpError] {
          networkRejectionReasonService.list(networkId1, user1)
        } map { httpError: HttpError =>
          verify(networkReasonRepo, never()).list(MM.any[NetworkId])(MM.any[TraceId])
          httpError.status should be(StatusCodes.NotFound)
        }
      }
    }

    "add" should {
      "return bad request if none of provided rejection ids are valid" in {
        val rejectionIds = Seq("non-existing-1", "non-existing-2")
        when(generalReasonRepo.list(rejectionIds)).thenReturn(Future.successful(Seq()))
        when(networkRepo.entitledFind(networkId1, getAvailableAccessKeysForCapabilities(EditCapabilities, user1)))
          .thenReturn(Future.successful(Some(network1)))

        recoverToExceptionIf[HttpError] {
          networkRejectionReasonService.add(networkId1, rejectionIds, user1)
        } map { httpError: HttpError =>
          verify(networkReasonRepo, never()).add(MM.any[NetworkId], MM.any[Seq[RejectionReason]])(MM.any[TraceId])
          httpError.status should be(StatusCodes.BadRequest)
        }
      }

      "return not found when user is not entitled to edit network" in {
        val rejectionIds = Seq("non-existing-1", "non-existing-2")
        when(generalReasonRepo.list(rejectionIds)).thenReturn(Future.successful(Seq()))
        when(networkRepo.entitledFind(networkId1, getAvailableAccessKeysForCapabilities(EditCapabilities, user1)))
          .thenReturn(Future.successful(None))

        recoverToExceptionIf[HttpError] {
          networkRejectionReasonService.add(networkId1, rejectionIds, user1)
        } map { httpError: HttpError =>
          verify(networkReasonRepo, never()).add(MM.any[NetworkId], MM.any[Seq[RejectionReason]])(MM.any[TraceId])
          httpError.status should be(StatusCodes.NotFound)
        }
      }

      "return invalid rejection ids" in {
        val rejectionIds = Seq(rejectionReason1.id, "non-existing-1")
        when(generalReasonRepo.list(rejectionIds)).thenReturn(Future.successful(Seq(rejectionReason1)))
        when(networkReasonRepo.add(networkId1, Seq(rejectionReason1))).thenReturn(Future.unit)
        when(networkRepo.entitledFind(networkId1, getAvailableAccessKeysForCapabilities(EditCapabilities, user1)))
          .thenReturn(Future.successful(Some(network1)))

        networkRejectionReasonService.add(networkId1, rejectionIds, user1).map { invalidIds =>
          invalidIds should equal(Seq("non-existing-1"))
        }
      }

      "return empty list when all rejections added" in {
        val rejectionIds = Seq(rejectionReason1.id, rejectionReason2.id)
        when(generalReasonRepo.list(rejectionIds)).thenReturn(Future.successful(Seq(rejectionReason1, rejectionReason2)))
        when(networkReasonRepo.add(networkId1, Seq(rejectionReason1, rejectionReason2))).thenReturn(Future.unit)
        when(networkRepo.entitledFind(networkId1, getAvailableAccessKeysForCapabilities(EditCapabilities, user1)))
          .thenReturn(Future.successful(Some(network1)))

        networkRejectionReasonService.add(networkId1, rejectionIds, user1).map { invalidIds =>
          invalidIds should be(empty)
        }
      }
    }

    "addCustom" should {
      "check that group and reason are non-empty" in {
        when(networkRepo.entitledFind(networkId1, getAvailableAccessKeysForCapabilities(EditCapabilities, user1)))
          .thenReturn(Future.successful(Some(network1)))

        recoverToExceptionIf[HttpError] {
          networkRejectionReasonService.addCustom(networkId1, "", "", user1)
        } map { httpError =>
          httpError.status should be(StatusCodes.BadRequest)
        }
      }

      "check that user has access to network" in {
        when(networkRepo.entitledFind(networkId1, getAvailableAccessKeysForCapabilities(EditCapabilities, user1)))
          .thenReturn(Future.successful(None))

        recoverToExceptionIf[HttpError] {
          networkRejectionReasonService.addCustom(networkId1, "reason 1 ", "group 1", user1)
        } map { httpError =>
          httpError.status should be(StatusCodes.NotFound)
        }
      }

      "create reason, insert into db and return id" in {
        when(networkReasonRepo.add(MM.eq(networkId1), MM.any[Seq[RejectionReason]])(MM.any[TraceId])).thenReturn(Future.unit)
        when(networkRepo.entitledFind(networkId1, getAvailableAccessKeysForCapabilities(EditCapabilities, user1)))
          .thenReturn(Future.successful(Some(network1)))

        networkRejectionReasonService.addCustom(networkId1, "Some reason", "Some group", user1) map { id =>
          id should not be empty
        }
      }
    }

    "delete" should {
      "check user entitlements" in {
        val rejectionIds = Seq(rejectionReason1.id, rejectionReason2.id)
        when(networkRepo.entitledFind(networkId1, getAvailableAccessKeysForCapabilities(EditCapabilities, user1)))
          .thenReturn(Future.successful(None))

        recoverToExceptionIf[HttpError] {
          networkRejectionReasonService.delete(networkId1, rejectionIds, user1)
        } map { httpError =>
          httpError.status should be(StatusCodes.NotFound)
        }
      }

      "delete rejection by id" in {
        val rejectionIds = Seq(rejectionReason1.id, rejectionReason2.id)
        when(networkReasonRepo.delete(networkId1, rejectionIds)).thenReturn(Future.successful(true))
        when(networkRepo.entitledFind(networkId1, getAvailableAccessKeysForCapabilities(EditCapabilities, user1)))
          .thenReturn(Future.successful(Some(network1)))

        networkRejectionReasonService.delete(networkId1, rejectionIds, user1) map { result =>
          result should be(true)
        }
      }
    }
  }

}
