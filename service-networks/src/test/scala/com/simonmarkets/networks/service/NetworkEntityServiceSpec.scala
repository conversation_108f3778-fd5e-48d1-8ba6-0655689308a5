package com.simonmarkets.networks.service

import akka.http.scaladsl.model.StatusCodes
import com.goldmansachs.marquee.pipg.{ContactInfo, UserACL, UserRole}
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.api.request.{AddNetworkEntitiesRequest, UpdateNetworkEntitiesRequest}
import com.simonmarkets.networks.api.response.{FlattenedNetworkEntitiesView, NetworkEntitiesView, ProprietaryIndicesView}
import com.simonmarkets.networks.domain.{Entity, NetworkEntities, ProprietaryIndex, ProprietaryIndexIdentifiers}
import com.simonmarkets.networks.repository.mongo.MongoNetworkEntitiesRepository
import com.simonmarkets.syntax.futureOpsConversion
import org.mockito.Mockito.{reset, when}
import org.mockito.{ArgumentMatchers => MM}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}
import simon.Id.NetworkId

import java.time.Instant

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}

class NetworkEntityServiceSpec extends WordSpec with MockitoSugar with Matchers with BeforeAndAfterEach {

  private implicit val traceId: TraceId = TraceId("network-attestation-spec")
  private implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  private val networksEntityRepo = mock[MongoNetworkEntitiesRepository]
  private val networkEntityService = new NetworkEntitiesService(networksEntityRepo)

  private val networkId1 = NetworkId("network-id-1")

  val nonAdmin: UserACL = TestUserACL("non-admin-user", networkId1, capabilities = Set(Capabilities.DefaultCapability))
  val admin: UserACL = TestUserACL("admin-user", NetworkId("SIMON Admin"), capabilities = Set(Capabilities.Admin), roles = Set(UserRole.EqPIPGGSAdmin))
  val now: Instant = Instant.now()

  private val preExisting1 = NetworkEntities(
    networkId = "Goldman Sachs",
    entities = Some(Set(Entity(
      key = "GSFC",
      symbol = "GS",
      contractTypeWrapper = Some("Note"),
      fullName = Some("(GS) GS FINANCE CORP."),
      shortName = Some("Goldman Sachs"),
      contactInfo = Some(ContactInfo(email = Some("<EMAIL>"), phone = Some("(*************")))
    ), Entity(
      key = "GSBank",
      symbol = "GS",
      contractTypeWrapper = Some("CD"),
      fullName = Some("(GS) GOLDMAN SACHS BANK U S A"),
      shortName = Some("Goldman Sachs"),
      is3a2 = true,
      contactInfo = Some(ContactInfo(email = Some("<EMAIL>"), phone = Some("(*************")))
    ))),
    userCreated = admin.userId,
    timeCreated = now,
    userLastUpdated = admin.userId,
    timeLastUpdated = now,
    version = 1,
    proprietaryIndices = Some(List(ProprietaryIndex("shortName1", ProprietaryIndexIdentifiers(mqId = "mqId1", bbId = "bbId1"))))
  )

  private val preExisting2 = NetworkEntities(
    networkId = "Fidelity",
    entities = Some(Set(Entity(
      key = "FIDELITY",
      symbol = "FIDELITY"
    ))),
    userCreated = admin.userId,
    timeCreated = now,
    userLastUpdated = admin.userId,
    timeLastUpdated = now,
    version = 1
  )

  override def beforeEach(): Unit = {
    reset(networksEntityRepo)
  }

  "NetworkEntitiesServiceSpec" can {
    "list" should {
      "return all network entities" in {
        when(networksEntityRepo.list(None, None, None)).thenReturn(Future.successful(Seq(preExisting1, preExisting2)))
        val list = networkEntityService.list().await
        list shouldBe Seq(preExisting1, preExisting2).map(NetworkEntitiesView(_))
      }

      "return network entities for required network" in {
        val networkIds = Some(Set(preExisting1.networkId))
        when(networksEntityRepo.list(networkIds, None, None)).thenReturn(Future.successful(Seq(preExisting1)))
        val list = networkEntityService.list(networkIds).await
        list shouldBe Seq(preExisting1).map(NetworkEntitiesView(_))
        list.head.proprietaryIndices shouldBe Some(List(ProprietaryIndex("shortName1", ProprietaryIndexIdentifiers(mqId = "mqId1", bbId = "bbId1"))))
      }

      "return network entities for required keys" in {
        val keys = Some(preExisting1.entities.get.map(_.key))
        when(networksEntityRepo.list(None, keys, None)).thenReturn(Future.successful(Seq(preExisting1)))
        val list = networkEntityService.list(keys = keys).await
        list shouldBe Seq(preExisting1).map(NetworkEntitiesView(_))
      }

      "return network entities for required symbols" in {
        val symbols = Some(preExisting1.entities.get.map(_.symbol))
        when(networksEntityRepo.list(None, None, symbols)).thenReturn(Future.successful(Seq(preExisting1)))
        val list = networkEntityService.list(symbols = symbols).await
        list shouldBe Seq(preExisting1).map(NetworkEntitiesView(_))
      }
    }

    "listFlattenedView" should {
      "return all network entities" in {
        when(networksEntityRepo.list(None, None, None)).thenReturn(Future.successful(Seq(preExisting1, preExisting2)))
        val list = networkEntityService.listFlattenedView().await
        list shouldBe Seq(preExisting1, preExisting2).flatMap(FlattenedNetworkEntitiesView(_, None, None))
      }

      "return network entities for required network" in {
        val networkIds = Some(Set(preExisting1.networkId))
        when(networksEntityRepo.list(networkIds, None, None)).thenReturn(Future.successful(Seq(preExisting1)))
        val list = networkEntityService.listFlattenedView(networkIds).await
        list shouldBe Seq(preExisting1).flatMap(FlattenedNetworkEntitiesView(_, None, None))
        list.head.proprietaryIndices shouldBe Some(List(ProprietaryIndex("shortName1", ProprietaryIndexIdentifiers(mqId = "mqId1", bbId = "bbId1"))))
      }

      "return network entities for required keys" in {
        val keys = Some(preExisting1.entities.get.map(_.key))
        when(networksEntityRepo.list(None, keys, None)).thenReturn(Future.successful(Seq(preExisting1)))
        val list = networkEntityService.listFlattenedView(keys = keys).await
        list.size shouldBe 2
        list shouldBe Seq(preExisting1).flatMap(FlattenedNetworkEntitiesView(_, keys, None))
      }

      "return network entities for required key" in {
        val keys = Some(Set("GSFC"))
        when(networksEntityRepo.list(None, keys, None)).thenReturn(Future.successful(Seq(preExisting1)))
        val list = networkEntityService.listFlattenedView(keys = keys).await
        list.size shouldBe 1
        list shouldBe Seq(preExisting1).flatMap(FlattenedNetworkEntitiesView(_, keys, None))
      }

      "return network entities for required symbols" in {
        val symbols = Some(preExisting1.entities.get.map(_.symbol))
        when(networksEntityRepo.list(None, None, symbols)).thenReturn(Future.successful(Seq(preExisting1)))
        val list = networkEntityService.listFlattenedView(symbols = symbols).await
        list.size shouldBe 2
        list shouldBe Seq(preExisting1).flatMap(FlattenedNetworkEntitiesView(_, None, symbols))
      }
    }

    "add" should {
      "create network entity successfully" in {
        when(networksEntityRepo.add(MM.any[NetworkEntities])).thenReturn(Future.successful(preExisting1))
        val add = networkEntityService.add(AddNetworkEntitiesRequest(proprietaryIndices = Some(List(ProprietaryIndex("shortName1", ProprietaryIndexIdentifiers(mqId = "mqId1", bbId = "bbId1")))),entities = preExisting1.entities.get), preExisting1.networkId)(admin, traceId).await
        add shouldBe NetworkEntitiesView(preExisting1)
        add.proprietaryIndices shouldBe Some(List(ProprietaryIndex("shortName1", ProprietaryIndexIdentifiers(mqId = "mqId1", bbId = "bbId1"))))
      }

      "fail to create network entity when not an admin" in {
        val add = intercept[HttpError] {
          networkEntityService.add(AddNetworkEntitiesRequest(entities = preExisting1.entities.get), preExisting1.networkId)(nonAdmin, traceId).await
        }
        add.status shouldBe StatusCodes.Forbidden
      }
    }

    "getByKey" should {
      "get network by key successfully" in {
        when(networksEntityRepo.getByKey("GSFC")).thenReturn(Future.successful(Some(preExisting1)))
        val issuerKey = networkEntityService.getByKey("GSFC").await
        issuerKey shouldBe FlattenedNetworkEntitiesView(preExisting1, Some(Set("GSFC")), None).headOption
      }

      "get network by key successfully with no entires" in {
        when(networksEntityRepo.getByKey("someIssuerKey")).thenReturn(Future.successful(None))
        val issuerKey = networkEntityService.getByKey("someIssuerKey").await
        issuerKey shouldBe None
      }
    }

    "getByNetworkId" should {
      "get network by network id successfully" in {
        when(networksEntityRepo.getByNetworkId("Goldman Sachs")).thenReturn(Future.successful(Some(preExisting1)))
        val networkEntitiesViewByNetworkId = networkEntityService.getByNetworkId("Goldman Sachs").await
        networkEntitiesViewByNetworkId shouldBe Some(NetworkEntitiesView(preExisting1))
        networkEntitiesViewByNetworkId.flatMap(_.proprietaryIndices) shouldBe Some(List(ProprietaryIndex("shortName1", ProprietaryIndexIdentifiers(mqId = "mqId1", bbId = "bbId1"))))
      }
    }

    "getAllProprietaryIndices" should {
      "get proprietary indices for all network entities when user is Simon admin" in {
        val preExisting1ExcludeEntities = preExisting1.copy(entities = None)
        val preExisting2ExcludeEntities = preExisting2.copy(entities = None)
        val preExisting3ExcludeEntities = preExisting2.copy(networkId = "HSBC", proprietaryIndices = Some(List(ProprietaryIndex("shortName2", ProprietaryIndexIdentifiers(mqId = "mqId2", bbId = "bbId2")))))
        when(networksEntityRepo.getAllProprietaryIndices).thenReturn(Future.successful(Seq(preExisting1ExcludeEntities, preExisting2ExcludeEntities, preExisting3ExcludeEntities)))
        val proprietaryIndicesList = networkEntityService.getAllProprietaryIndices(admin, traceId).await
        proprietaryIndicesList shouldBe Seq(ProprietaryIndicesView("Goldman Sachs", List(ProprietaryIndex("shortName1", ProprietaryIndexIdentifiers(mqId = "mqId1", bbId = "bbId1")))), ProprietaryIndicesView("Fidelity", List.empty), ProprietaryIndicesView("HSBC", List(ProprietaryIndex("shortName2", ProprietaryIndexIdentifiers(mqId = "mqId2", bbId = "bbId2")))))
      }

      "fail to return proprietary indices when user is not simon admin" in {
        val nonSimonAdmin = TestUserACL("non-simon-admin", NetworkId("dummy"), capabilities = Set(Capabilities.Admin))
        val result = intercept[HttpError] {
          networkEntityService.getAllProprietaryIndices(nonSimonAdmin, traceId).await
        }
        result.status shouldBe StatusCodes.Forbidden
        result shouldBe HttpError.forbidden("User has no capability to call the endpoint")
      }
    }

    "update" should {
      "update network entity successfully" in {
        when(networksEntityRepo.update(MM.any[NetworkEntities])).thenReturn(Future.successful(Some(preExisting1)))
        when(networksEntityRepo.getByNetworkId(preExisting1.networkId)).thenReturn(Future.successful(Some(preExisting1)))
        val update = networkEntityService.update(UpdateNetworkEntitiesRequest(entities = preExisting1.entities.get), preExisting1.networkId)(admin, traceId).await
        update shouldBe Some(NetworkEntitiesView(preExisting1))
      }

      "fail to update network entity successfully when existing record not found" in {
        when(networksEntityRepo.getByNetworkId(preExisting1.networkId)).thenReturn(Future.successful(None))
        val update = intercept[HttpError] {
          networkEntityService.update(UpdateNetworkEntitiesRequest(entities = preExisting1.entities.get), preExisting1.networkId)(admin, traceId).await
        }
        update.status shouldBe StatusCodes.NotFound
      }

      "fail to update network entity when not an admin" in {
        val update = intercept[HttpError] {
          networkEntityService.update(UpdateNetworkEntitiesRequest(entities = preExisting1.entities.get), preExisting1.networkId)(nonAdmin, traceId).await
        }
        update.status shouldBe StatusCodes.Forbidden
      }
    }

    "delete" should {
      "delete network entity successfully" in {
        when(networksEntityRepo.delete(preExisting1.networkId)).thenReturn(Future.successful(true))
        val add = networkEntityService.delete(preExisting1.networkId)(admin, traceId).await
        add shouldBe true
      }

      "fail to delete network entity when not an admin" in {
        val add = intercept[HttpError] {
          networkEntityService.delete(preExisting1.networkId)(nonAdmin, traceId).await
        }
        add.status shouldBe StatusCodes.Forbidden
      }
    }
  }

}
