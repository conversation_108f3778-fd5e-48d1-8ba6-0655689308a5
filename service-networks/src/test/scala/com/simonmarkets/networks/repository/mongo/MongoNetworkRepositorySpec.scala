package com.simonmarkets.networks.repository.mongo

import com.goldmansachs.marquee.pipg.UserACL
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.capabilities.NetworksCapabilities.{ViewCapabilities, getAvailableAccessKeysForCapabilities}
import com.simonmarkets.networks.domain.Network
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros._
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, Matchers, OptionValues}
import simon.Id.NetworkId

import scala.concurrent.Await
import scala.concurrent.duration._

class MongoNetworkRepositorySpec extends AsyncWordSpec with EmbeddedMongoLike with Matchers with BeforeAndAfterEach with OptionValues {

  private val codecRegistry = fromRegistries(fromProviders(classOf[Network]), DEFAULT_CODEC_REGISTRY)
  private lazy val collection = db.getCollection[Network]("networks").withCodecRegistry(codecRegistry)
  private lazy val repo = new MongoNetworkRepository(collection)
  private val duration = 5.seconds

  override def beforeEach(): Unit = {
    Await.result(collection.insertMany(Seq(
      Network(id = "net-1", name = "Network 1", entitlements = Set("admin", "viewNetworkViaNetwork:net-1", "viewNetworkViaPurview:net-1",
        "editNetworkViaNetwork:net-1")),
      Network("net-2", "Network 2", Set("admin", "viewNetworkViaNetwork:net-2", "viewNetworkViaPurview:net-2",
        "editNetworkViaNetwork:net-2")),
    )).toFuture, duration)
  }

  "MongoNetworkRepository" can {
    "entitledFind" should {
      "return network by entitlements" in {
        val user: UserACL = TestUserACL(userId = "id", networkId = NetworkId("net-1"), capabilities = Set("viewNetworkViaNetwork"))

        val capabilities = getAvailableAccessKeysForCapabilities(ViewCapabilities, user)

        repo.entitledFind(NetworkId("net-1"), capabilities) map { net1 =>
          net1 should not be (empty)
          net1.value.id should be("net-1")
        }
      }

      "return nothing if network does not exist" in {
        val user = TestUserACL(userId = "id", networkId = NetworkId("net-1"))

        val capabilities = getAvailableAccessKeysForCapabilities(ViewCapabilities, user)

        repo.entitledFind(NetworkId("net-22"), capabilities) map { net1 =>
          net1 should be (empty)
        }
      }

      "return nothing if entitlements not allow it" in {
        val user = TestUserACL(userId = "id", networkId = NetworkId("net-1"))

        val capabilities = getAvailableAccessKeysForCapabilities(ViewCapabilities, user)

        repo.entitledFind(NetworkId("net-2"), capabilities) map { net =>
          net should be (empty)
        }
      }
    }
  }


}
