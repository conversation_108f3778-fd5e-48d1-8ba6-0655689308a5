package com.simonmarkets.networks.repository.mongo

import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.rejections.domain.{NetworkRejectionReasons, RejectionReason}
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.model.Filters
import org.scalatest.OptionValues._
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, Matchers}
import simon.Id.NetworkId

import java.time.Instant
import java.time.temporal.ChronoUnit
import scala.concurrent.Await
import scala.concurrent.duration._

class MongoNetworkRejectionReasonRepositorySpec extends AsyncWordSpec with EmbeddedMongoLike with BeforeAndAfterEach with Matchers {
  implicit val traceId: TraceId = TraceId("mongo-rejection-reason-repo-spec")

  private val codecRegistry = fromRegistries(
    fromProviders(classOf[NetworkRejectionReasons], classOf[RejectionReason]),
    DEFAULT_CODEC_REGISTRY)

  private lazy val collection = db.getCollection[NetworkRejectionReasons]("networkRejections")
    .withCodecRegistry(codecRegistry)

  private lazy val repository = new MongoNetworkRejectionReasonRepository(collection)
  private val user1 = TestUserACL(userId = "test-user-1", networkId = NetworkId("net-1"))

  private val duration = 5.seconds
  private val now = Instant.now().truncatedTo(ChronoUnit.MILLIS)

  private val rejectionReason = RejectionReason(
    id = "reason-id-1",
    reason = "Some reason",
    group = "Some group",
    userCreated = user1.userId,
    timeCreated = now,
    userLastUpdated = user1.userId,
    timeLastUpdated = now,
    version = 1,
    traceId = traceId.toString()
  )

  private val networkRejection1 = NetworkRejectionReasons(
    network = "network-1",
    reasons = Seq(
      rejectionReason,
      rejectionReason.copy(
        id = "reason-id-2",
        reason = "Some reason 2",
        group = "Some group 2",
      )
    ),
  )

  private val networkRejection2 = NetworkRejectionReasons(
    network = "network-2",
    reasons = Seq(
      rejectionReason,
      rejectionReason.copy(
        id = "reason-id-2",
        reason = "Some reason 2",
        group = "Some group 2",
      ),
      rejectionReason.copy(
        id = "reason-id-custom",
        reason = "Some reason 3",
        group = "Some group 3",
      )
    ),
  )

  override def beforeEach(): Unit = {
    Await.result(collection.drop().toFuture, duration)
    Await.result(collection.insertMany(Seq(networkRejection1, networkRejection2)).toFuture, duration)
  }

  "MongoGeneralRejectionReasonRepository" can {
    "list" should {
      "list rejection reasons for network" in {
        repository.list(NetworkId(networkRejection1.network)).map { list =>
          list should contain only (networkRejection1.reasons: _*)
        }
      }

      "return empty list if network does not exists" in {
        repository.list(NetworkId("Non-existing network")).map { list =>
          list should be(empty)
        }
      }
    }

    "add" should {
      "add rejection reason to exiting network rejection reasons list" in {
        val newRejection = rejectionReason.copy(id = "rejection-id-3",
          reason = "One more reason to reject")
        for {
          _ <- repository.add(NetworkId(networkRejection1.network), Seq(newRejection))
          networkRejection <- collection.find(networkCondition(networkRejection1.network)).headOption()
        } yield {
          val expectedReasons = networkRejection1.reasons :+ newRejection
          networkRejection.value.reasons should contain only (expectedReasons: _*)
        }
      }

      "create a new network rejection if it does not exists yet" in {
        val networkId = NetworkId("new network")
        val newRejection = rejectionReason.copy(id = "rejection-id-3",
          reason = "One more reason to reject")
        for {
          _ <- repository.add(networkId, Seq(rejectionReason, newRejection))
          networkRejection <- collection.find(networkCondition(NetworkId.unwrap(networkId))).headOption()
        } yield {
          networkRejection.value.network should be(NetworkId unwrap networkId)
          networkRejection.value.reasons should contain only(rejectionReason, newRejection)
        }
      }

      "not add the same reason twice" in {
        for {
          _ <- repository.add(NetworkId(networkRejection1.network), Seq(rejectionReason))
          networkRejection <- collection.find(networkCondition(networkRejection1.network)).headOption()
        } yield {
          networkRejection.value.reasons should contain only (networkRejection1.reasons: _*)
        }
      }
    }

    "delete" should {
      "delete one or more rejection reasons from network" in {
        val newNetworkId = NetworkId("one-more-network")
        val rejectionReason2 = rejectionReason.copy(
          id = "reason-id-2",
          reason = "New rejection"
        )

        for {
          _ <- repository.add(newNetworkId, Seq(rejectionReason, rejectionReason2))
          r <- repository.delete(NetworkId(networkRejection1.network), Seq("reason-id-2"))
          changedNetworkRejection <- collection.find(networkCondition(networkRejection1.network)).headOption()
          nonChangedNetworkRejection <- collection.find(networkCondition(NetworkId.unwrap(newNetworkId))).headOption()
        } yield {
          r should be(true)
          changedNetworkRejection.value.reasons should contain only rejectionReason

          // check that other network is not affected
          nonChangedNetworkRejection.value.reasons should contain only(rejectionReason, rejectionReason2)

        }
      }

      "delete all rejection reasons from network" in {
        for {
          r <- repository.delete(NetworkId(networkRejection1.network), Seq("reason-id-1", "reason-id-2"))
          networkRejection <- collection.find(networkCondition(networkRejection1.network)).headOption()
        } yield {
          r should be(true)
          networkRejection.value.reasons should be(empty)
        }
      }

      "do nothing if there is no rejections with such ids" in {
        for {
          r <- repository.delete(NetworkId(networkRejection1.network), Seq("reason-id-11", "reason-id-22"))
          networkRejection <- collection.find(networkCondition(networkRejection1.network)).headOption()
        } yield {
          r should be(false)
          networkRejection.value should equal(networkRejection1)
        }
      }

      "do nothing if cannot find rejections by network id" in {
        for {
          r <- repository.delete(NetworkId("non-existing-rejection"), Seq("reason-id-1", "reason-id-2"))
          networkRejection <- collection.find(networkCondition(networkRejection1.network)).headOption()
        } yield {
          r should be(false)
          networkRejection.value should equal(networkRejection1)
        }
      }
    }
  }

  private def networkCondition(networkId: String) = Filters.equal("network", networkId)

}
