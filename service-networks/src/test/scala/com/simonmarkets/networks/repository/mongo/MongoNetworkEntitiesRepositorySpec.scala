package com.simonmarkets.networks.repository.mongo

import com.goldmansachs.marquee.pipg.ContactInfo
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.networks.domain.{Entity, NetworkEntities, ProprietaryIndex, ProprietaryIndexIdentifiers}
import com.simonmarkets.syntax._
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.model.Filters._
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, OptionValues}
import simon.Id.NetworkId

import java.time.Instant
import java.time.temporal.ChronoUnit

class MongoNetworkEntitiesRepositorySpec extends AsyncWordSpec with EmbeddedMongoLike with OptionValues with BeforeAndAfterEach {

  private lazy val rawCollection = db.getCollection("networkEntities")
  private lazy val repository = new MongoNetworkEntitiesRepository(rawCollection.withDocumentClass[NetworkEntities])
  private lazy val collection = rawCollection.withDocumentClass[NetworkEntities].withCodecRegistry(repository.codecRegistry)

  private val user1 = TestUserACL(userId = "test-user-1", networkId = NetworkId("net-1"))

  private val now = Instant.now().truncatedTo(ChronoUnit.MILLIS)
  private val preExisting1 = NetworkEntities(
    networkId = "Goldman Sachs",
    entities = Some(Set(Entity(
      key = "GSFC",
      symbol = "GS",
      contractTypeWrapper = Some("Note"),
      fullName = Some("(GS) GS FINANCE CORP."),
      shortName = Some("Goldman Sachs"),
      contactInfo = Some(ContactInfo(email = Some("<EMAIL>"), phone = Some("(*************")))
    ), Entity(
      key = "GSBank",
      symbol = "GS",
      contractTypeWrapper = Some("CD"),
      fullName = Some("(GS) GOLDMAN SACHS BANK U S A"),
      shortName = Some("Goldman Sachs"),
      is3a2 = true,
      contactInfo = Some(ContactInfo(email = Some("<EMAIL>"), phone = Some("(*************")))
    ))),
    userCreated = user1.userId,
    timeCreated = now,
    userLastUpdated = user1.userId,
    timeLastUpdated = now,
    version = 1
  )

  private val preExisting2 = NetworkEntities(
    networkId = "Fidelity",
    entities = Some(Set(Entity(
      key = "FIDELITY",
      symbol = "FIDELITY"
    ))),
    userCreated = user1.userId,
    timeCreated = now,
    userLastUpdated = user1.userId,
    timeLastUpdated = now,
    version = 1
  )

  override def beforeEach(): Unit = {
    super.beforeEach()

    collection.drop.toFuture().await
    collection.insertMany(Seq(preExisting1, preExisting2)).toFuture.await
  }

  "MongoNetworkEntitiesRepository" can {
    "create" should {
      "create a new network entity" in {
        for {
          newNetworkEntities <- repository.add(NetworkEntities(
            networkId = "Schwab",
            entities = Some(Set(Entity(
              key = "SCHWAB",
              symbol = "SCHWAB"
            ))),
            userCreated = user1.userId,
            timeCreated = now,
            userLastUpdated = user1.userId,
            timeLastUpdated = now,
            version = 1,
            proprietaryIndices = Some(List(ProprietaryIndex("shortName1", ProprietaryIndexIdentifiers(mqId = "mqId1", bbId = "bbId1"))))
          ))
          dbResult <- collection.find(equal("networkId", newNetworkEntities.networkId)).toFuture
        } yield {
          val dbInstance = dbResult.head
          assert(dbInstance.version == 1)
          assert(dbInstance.userCreated == user1.userId)
          assert(dbInstance.userLastUpdated == user1.userId)
          assert(dbInstance.timeCreated == dbInstance.timeLastUpdated)
          assert(dbInstance.entities.contains(Set(Entity(
            key = "SCHWAB",
            symbol = "SCHWAB"
          ))))
          assert(Instant.now.compareTo(dbInstance.timeCreated) >= 0)
        }
      }
    }

    "update" should {
      "update entire network entity" in {
        val updatedGS = preExisting1.copy(entities = Some(Set(preExisting1.entities.get.head)), userLastUpdated = "test-user-2", timeLastUpdated = Instant.now(), version = 2)
        for {
          resultOpt <- repository.update(updatedGS)
          dbResultOpt <- collection.find(equal("networkId", preExisting1.networkId)).toFuture()
        } yield {
          val result = resultOpt.value
          val dbResult = dbResultOpt.headOption.value

          assert(result.networkId == dbResult.networkId)
          assert(result.timeLastUpdated.compareTo(preExisting1.timeLastUpdated) > 0)
          assert(result.timeCreated == preExisting1.timeCreated)
          assert(result.userLastUpdated == "test-user-2")
          assert(result.userCreated == user1.userId)
        }
      }

      "not update if network id is incorrect" in {
        val updatedGS = preExisting1.copy(networkId = "Some network")
        for {
          resultOpt <- repository.update(updatedGS)
          dbResultOpt <- collection.find(equal("networkId", preExisting1.networkId)).toFuture()
        } yield {
          assert(resultOpt.isEmpty)

          val dbResult = dbResultOpt.headOption.value
          assert(dbResult == preExisting1)
        }
      }

      "not update if id is incorrect" in {
        val updatedGS = preExisting1.copy(networkId = "Some id")
        for {
          resultOpt <- repository.update(updatedGS)
          dbResultOpt <- collection.find(equal("networkId", preExisting1.networkId)).toFuture()
        } yield {
          assert(resultOpt.isEmpty)

          val dbResult = dbResultOpt.headOption.value
          assert(dbResult == preExisting1)
        }
      }

      "not update if version is incorrect" in {
        val updatedGS = preExisting1.copy(version = 3)
        for {
          resultOpt <- repository.update(updatedGS)
          dbResultOpt <- collection.find(equal("networkId", preExisting1.networkId)).toFuture()
        } yield {
          assert(resultOpt.isEmpty)

          val dbResult = dbResultOpt.headOption.value
          assert(dbResult == preExisting1)
        }
      }
    }

    "list" should {
      "return list of network entity for specific network" in {
        for {
          allResults <- repository.list()
          gsResult <- repository.list(networkIds = Some(Set(preExisting1.networkId)))
          gsKeysResult <- repository.list(keys = Some(preExisting1.entities.get.map(_.key)))
          gsSymbolsResult <- repository.list(symbols = Some(preExisting1.entities.get.map(_.symbol)))
        } yield {
          assert(allResults == Seq(preExisting1, preExisting2))
          assert(gsResult == Seq(preExisting1))
          assert(gsKeysResult == Seq(preExisting1))
          assert(gsSymbolsResult == Seq(preExisting1))
        }
      }
    }

    "delete" should {
      "remove network entity for a specific network" in {
        for {
          result <- repository.delete(preExisting1.networkId)
          all <- collection.find().toFuture
        } yield {
          assert(result)
          assert(all == Seq(preExisting2))
        }
      }

      "not remove network entity if id is incorrect" in {
        for {
          result <- repository.delete("some-id")
          all <- collection.find().toFuture
        } yield {
          assert(!result)
          assert(all == Seq(preExisting1, preExisting2))
        }
      }
    }

    "getByKey" should {
      "return network entity by key" in {
        for {
          result <- repository.getByKey("GSFC")
        } yield {
          assert(result.value == preExisting1)
        }
      }

      "return None if key is incorrect" in {
        for {
          result <- repository.getByKey("some-issuer-key")
        } yield {
          assert(result.isEmpty)
        }
      }
    }

    "getByNetworkId" should {
      "return network entity by networkId" in {
        for {
          result <- repository.getByNetworkId(preExisting1.networkId)
        } yield {
          assert(result.value == preExisting1)
        }
      }

      "return None if networkId is incorrect" in {
        for {
          result <- repository.getByNetworkId("some-id")
        } yield {
          assert(result.isEmpty)
        }
      }
    }

    "getAllProprietaryIndices" should {
      "return all the proprietary indices along with the network ids and exclude the entities" in {
        for {
          result <- repository.getAllProprietaryIndices
        } yield {
          assert(result == Seq(preExisting1.copy(entities = None), preExisting2.copy(entities = None)))
        }
      }
    }
  }
}
