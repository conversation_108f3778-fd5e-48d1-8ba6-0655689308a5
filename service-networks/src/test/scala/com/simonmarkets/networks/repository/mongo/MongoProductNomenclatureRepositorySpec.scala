package com.simonmarkets.networks.repository.mongo

import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.networks.domain.ProductNomenclature
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros._
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, OptionValues}
import org.mongodb.scala.model.Filters._
import simon.Id.NetworkId

import scala.concurrent.Await
import java.time.Instant
import java.time.temporal.ChronoUnit

import scala.concurrent.duration._

class MongoProductNomenclatureRepositorySpec extends AsyncWordSpec with EmbeddedMongoLike with OptionValues with BeforeAndAfterEach {

  private val duration = 5.seconds
  private lazy val rawCollection = db.getCollection("productNomenclature")
  private lazy val repository = new MongoProductNomenclatureRepository(rawCollection.withDocumentClass[ProductNomenclature])
  private lazy val codecRegistry = fromRegistries(fromProviders(classOf[ProductNomenclature]), DEFAULT_CODEC_REGISTRY)
  private lazy val collection = rawCollection.withDocumentClass[ProductNomenclature].withCodecRegistry(codecRegistry)

  private val user1 = TestUserACL(userId = "test-user-1", networkId = NetworkId("net-1"))
  private val user2 = TestUserACL(userId = "test-user-2", networkId = NetworkId("net-2"))

  private val now = Instant.now().truncatedTo(ChronoUnit.MILLIS)
  private val preExisting1 = ProductNomenclature(
    id = "pre-existing-1",
    name = "Callable Product",
    networkId = NetworkId.unwrap(user1.networkId),
    userCreated = user1.userId,
    timeCreated = now,
    userLastUpdated = user1.userId,
    timeLastUpdated = now,
    version = 1
  )

  private val preExisting2 = ProductNomenclature(
    id = "pre-existing-2",
    name = "Callable Product 2",
    networkId = NetworkId.unwrap(user1.networkId),
    userCreated = user1.userId,
    timeCreated = now,
    userLastUpdated = user1.userId,
    timeLastUpdated = now,
    version = 1
  )

  private val preExisting3 = ProductNomenclature(
    id = "pre-existing-3",
    name = "Callable Product 2",
    networkId = NetworkId.unwrap(user2.networkId),
    userCreated = user2.userId,
    timeCreated = now,
    userLastUpdated = user2.userId,
    timeLastUpdated = now,
    version = 1
  )

  override def beforeEach(): Unit = {
    super.beforeEach()

    Await.result(collection.drop.toFuture(), duration)
    Await.result(collection.insertMany(Seq(preExisting1, preExisting2, preExisting3)).toFuture, duration)
  }

  "MongoProductNomenclatureRepository" can {
    "create" should {
      "create a new product nomenclature" in {
        for {
          newProductNomenclature <- repository.add(user2.networkId, "New Product Name", user2.userId)
          dbResult <- collection.find(equal("_id", newProductNomenclature.id)).toFuture
        } yield {
          val dbInstance = dbResult.head
          assert(dbInstance.name == "New Product Name")
          assert(dbInstance.version == 1)
          assert(dbInstance.userCreated == user2.userId)
          assert(dbInstance.userLastUpdated == user2.userId)
          assert(dbInstance.timeCreated == dbInstance.timeLastUpdated)
          assert(Instant.now.compareTo(dbInstance.timeCreated) >= 0)
        }
      }
    }

    "update" should {
      "update product name along with audit fields" in {
        for {
          resultOpt <- repository.update(user1.networkId, preExisting1.id, "Updated Name", user2.userId)
          dbResultOpt <- collection.find(equal("_id", preExisting1.id)).toFuture()
        } yield {
          val result = resultOpt.value
          val dbResult = dbResultOpt.headOption.value

          assert(result.id == dbResult.id)
          assert(result.name == "Updated Name")
          assert(result.name == dbResult.name)
          assert(result.timeLastUpdated.compareTo(preExisting1.timeLastUpdated) > 0)
          assert(result.timeCreated == preExisting1.timeCreated)
          assert(result.userLastUpdated == user2.userId)
          assert(result.userCreated == user1.userId)
        }
      }

      "not update if network id is incorrect" in {
        for {
          resultOpt <- repository.update(NetworkId("Some network"), preExisting1.id, "Updated Name", user2.userId)
          dbResultOpt <- collection.find(equal("_id", preExisting1.id)).toFuture()
        } yield {
          assert(resultOpt.isEmpty)

          val dbResult = dbResultOpt.headOption.value
          assert(dbResult == preExisting1)
        }
      }

      "not update if id is incorrect" in {
        for {
          resultOpt <- repository.update(user1.networkId, "Some other nomenclature id", "Updated Name", user2.userId)
          dbResultOpt <- collection.find(equal("_id", preExisting1.id)).toFuture()
        } yield {
          assert(resultOpt.isEmpty)

          val dbResult = dbResultOpt.headOption.value
          assert(dbResult == preExisting1)
        }
      }
    }

    "list" should {
      "return list of product nomenclature for specific network" in {
        for {
          user1Results <- repository.list(user1.networkId)
          user2Results <- repository.list(user2.networkId)
        } yield {
          assert(user1Results == Seq(preExisting1, preExisting2))
          assert(user2Results == Seq(preExisting3))
        }
      }
    }

    "delete" should {
      "remove product nomenclature for a specific network" in {
        for {
          result <- repository.delete(user1.networkId, preExisting1.id)
          all <- collection.find().toFuture
        } yield {
          assert(result)
          assert(all == Seq(preExisting2, preExisting3))
        }
      }

      "not remove product nomenclature if network is incorrect" in {
        for {
          result <- repository.delete(user2.networkId, preExisting1.id)
          all <- collection.find().toFuture
        } yield {
          assert(!result)
          assert(all == Seq(preExisting1, preExisting2, preExisting3))
        }
      }

      "not remove product nomenclature if id is incorrect" in {
        for {
          result <- repository.delete(user2.networkId, "non-existing product nomenclature")
          all <- collection.find().toFuture
        } yield {
          assert(!result)
          assert(all == Seq(preExisting1, preExisting2, preExisting3))
        }
      }
    }

    "get" should {
      "return product nomenclature by id and network" in {
        for {
          result <- repository.get(user1.networkId, preExisting2.id)
        } yield {
          assert(result.value == preExisting2)
        }
      }

      "return None if network id is incorrect" in {
        for {
          result <- repository.get(user2.networkId, preExisting2.id)
        } yield {
          assert(result.isEmpty)
        }
      }

      "return None if id is incorrect" in {
        for {
          result <- repository.get(user1.networkId, "non-existing-id")
        } yield {
          assert(result.isEmpty)
        }
      }
    }

    "exists" should {
      "should check if product nomenclature name exists for particular networks" in {
        for {
          exists <- repository.exists(NetworkId(preExisting1.networkId), preExisting1.name)
          existsInNetwork2 <- repository.exists(NetworkId(preExisting3.networkId), preExisting3.name)
          existsInOtherNetwork <- repository.exists(NetworkId(preExisting3.networkId), preExisting1.name)
          existsInOtherNetworkWithName <- repository.exists(NetworkId(preExisting3.networkId), preExisting1.name)
          existsWithOtherName <- repository.exists(NetworkId(preExisting1.networkId), "non-existing")
        } yield {
          assert(exists)
          assert(existsInNetwork2)
          assert(!existsInOtherNetwork)
          assert(!existsInOtherNetworkWithName)
          assert(!existsWithOtherName)
        }
      }
    }
  }
}
