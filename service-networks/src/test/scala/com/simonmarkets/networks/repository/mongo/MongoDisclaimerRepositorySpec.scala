package com.simonmarkets.networks.repository.mongo

import com.simonmarkets.logging.TraceId
import com.simonmarkets.mongodb.codec.EnumEntryCodecProvider
import com.simonmarkets.networks.domain.DisclaimerType.LifecycleEventsEmail
import com.simonmarkets.networks.domain.{Disclaimer, DisclaimerType}
import com.simonmarkets.networks.repository.DisclaimerRepository
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros._
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, Matchers, OptionValues}
import simon.Id.NetworkId

import scala.concurrent.Await
import scala.concurrent.duration.DurationInt

class MongoDisclaimerRepositorySpec extends AsyncWordSpec with EmbeddedMongoLike with BeforeAndAfterEach with Matchers
  with OptionValues {

  private implicit val traceId: TraceId = TraceId("mongo-disclaimer-repository-spec")

  val codecRegistry: CodecRegistry = fromRegistries(
    fromProviders(
      classOf[Disclaimer],
      EnumEntryCodecProvider[DisclaimerType]),
    DEFAULT_CODEC_REGISTRY)

  private lazy val collection = db.getCollection[Disclaimer]("disclaimer").withCodecRegistry(codecRegistry)
  private lazy val repository = new DisclaimerRepository.Mongo(collection)

  override def beforeEach(): Unit = {
    super.beforeEach()
    Await.result(collection.drop().toFuture(), 60.seconds)
  }

  private val networkId = NetworkId("test-network-1")
  private val networkId2 = NetworkId("test-network-2")
  private val userId = "user-id-1"

  private val networkIdText = "Some text"
  private val networkId2Text = "Another text"


  "MongoDisclaimersRepository" can {
    "add disclaimers" should {
      "create new pdf disclaimers if not exist" in {
        for {
          result <- repository.add(networkId, DisclaimerType.Pdf, networkIdText, userId)
          all <- collection.find().toFuture()
        } yield {
          result should not be empty
          all shouldBe result.toSeq

          val dbItem = all.head
          dbItem.text shouldBe "Some text"
          dbItem.networkId shouldBe NetworkId.unwrap(networkId)
          dbItem.disclaimerType shouldBe DisclaimerType.Pdf
          dbItem.userCreated shouldBe userId
        }
      }

      "create new video disclaimers if not exist" in {
        for {
          result <- repository.add(networkId, DisclaimerType.VideoSharingEmail, networkIdText, userId)
          all <- collection.find().toFuture()
        } yield {
          result should not be empty
          all shouldBe result.toSeq

          val dbItem = all.head
          dbItem.text shouldBe "Some text"
          dbItem.networkId shouldBe NetworkId.unwrap(networkId)
          dbItem.disclaimerType shouldBe DisclaimerType.VideoSharingEmail
          dbItem.userCreated shouldBe userId
        }
      }

      "create new email disclaimers if not exist" in {
        for {
          result <- repository.add(networkId, DisclaimerType.LifecycleEventsEmail, networkIdText, userId)
          all <- collection.find().toFuture()
        } yield {
          result should not be empty
          all shouldBe result.toSeq

          val dbItem = all.head
          dbItem.text shouldBe "Some text"
          dbItem.networkId shouldBe NetworkId.unwrap(networkId)
          dbItem.disclaimerType shouldBe DisclaimerType.LifecycleEventsEmail
          dbItem.userCreated shouldBe userId
        }
      }

      "create new portfolio pdf disclaimers if not exist" in {
        for {
          result <- repository.add(networkId, DisclaimerType.PortfolioPDF, networkIdText, userId)
          all <- collection.find().toFuture()
        } yield {
          result should not be empty
          all shouldBe result.toSeq

          val dbItem = all.head
          dbItem.text shouldBe "Some text"
          dbItem.networkId shouldBe NetworkId.unwrap(networkId)
          dbItem.disclaimerType shouldBe DisclaimerType.PortfolioPDF
          dbItem.userCreated shouldBe userId
        }
      }

      "update existing one if it already exists" in {
        val textFirstVersion = "Some text"
        val textSecondVersion = "Updated text"
        for {
          _ <- repository.add(networkId, DisclaimerType.Pdf, textFirstVersion, userId)
          updatedResult <- repository.add(networkId, DisclaimerType.Pdf, textSecondVersion, userId)
          all <- collection.find().toFuture()
        } yield {
          updatedResult should not be empty
          all shouldBe updatedResult.toSeq

          val dbItem = all.head
          dbItem.text shouldBe textSecondVersion
          dbItem.networkId shouldBe NetworkId.unwrap(networkId)
          dbItem.disclaimerType shouldBe DisclaimerType.Pdf
          dbItem.userCreated shouldBe userId
        }
      }
  }


    "delete" should {
      "delete existing disclaimer by networkId and type" in {
        for {
          _ <- repository.add(networkId, DisclaimerType.Pdf, networkIdText, userId)
          second <- repository.add(networkId2, DisclaimerType.Pdf, networkId2Text, userId)
          deleteResult <- repository.delete(networkId, DisclaimerType.Pdf)
          all <- collection.find().toFuture()
        } yield {
          deleteResult should not be empty
          all shouldBe second.toSeq
          all.head.text shouldBe networkId2Text
        }
      }

      "delete existing disclaimer by networkId and type, but keep another type of disclaimer" in {
        for {
          first <- repository.add(networkId, DisclaimerType.Pdf, networkIdText, userId)
          second <- repository.add(networkId, DisclaimerType.VideoSharingEmail, networkIdText, userId)
          third <- repository.add(networkId, DisclaimerType.LifecycleEventsEmail, networkIdText, userId)
          fourth <- repository.add(networkId, DisclaimerType.PortfolioPDF, networkIdText, userId)
          deleteResult <- repository.delete(networkId, DisclaimerType.VideoSharingEmail)
          all <- collection.find().toFuture()
        } yield {
          deleteResult should not be empty
          deleteResult shouldBe second
          all shouldBe Seq(first.get, third.get,fourth.get)
          all.head.text shouldBe networkIdText
        }
      }

      "do nothing if disclaimer for this network does not exist" in {
        for {
          first <- repository.add(networkId, DisclaimerType.Pdf, networkIdText, userId)
          second <- repository.add(networkId2, DisclaimerType.Pdf, networkId2Text, userId)
          deleteResult <- repository.delete(NetworkId("non-existing"), DisclaimerType.Pdf)
          all <- collection.find().toFuture()
        } yield {
          deleteResult shouldBe empty
          all should contain only (Seq(first, second).flatten: _*)
        }
      }
    }

    "get" should {
      "retrieve disclaimer by type and network id" in {
        for {
          _ <- repository.add(networkId, DisclaimerType.Pdf, networkIdText, userId)
          second <- repository.add(networkId2, DisclaimerType.Pdf, networkId2Text, userId)
          third <- repository.add(networkId, DisclaimerType.VideoSharingEmail, networkIdText, userId)
          fourth <- repository.add(networkId, DisclaimerType.LifecycleEventsEmail, networkIdText, userId)
          fifth <- repository.add(networkId, DisclaimerType.PortfolioPDF, networkIdText, userId)
          _ <- repository.add(networkId2, DisclaimerType.VideoSharingEmail, networkId2Text, userId)
          getResultPdf <- repository.get(networkId2, DisclaimerType.Pdf)
          getResultVideoSharing <- repository.get(networkId, DisclaimerType.VideoSharingEmail)
          getLifecycleEmail <- repository.get(networkId, LifecycleEventsEmail)
          getPortfolioPDF <- repository.get(networkId, DisclaimerType.PortfolioPDF)
        } yield {
          getResultPdf shouldBe second
          getResultPdf.value.text shouldBe networkId2Text
          getResultVideoSharing shouldBe third
          getResultVideoSharing.value.text shouldBe networkIdText
          getLifecycleEmail shouldBe fourth
          getLifecycleEmail.value.text shouldBe networkIdText
          getPortfolioPDF shouldBe fifth
          getPortfolioPDF.value.text shouldBe networkIdText
        }
      }

      "return nothing if disclaimer not found" in {
        for {
          _ <- repository.add(networkId, DisclaimerType.Pdf, networkIdText, userId)
          _ <- repository.add(networkId2, DisclaimerType.VideoSharingEmail, networkId2Text, userId)
          getResultFirst <- repository.get(NetworkId("non-existing-network-id"), DisclaimerType.Pdf)
          getResultSecond <- repository.get(networkId, DisclaimerType.VideoSharingEmail)
        } yield {
          getResultFirst shouldBe None
          getResultSecond shouldBe None
        }
      }
    }
  }
}
