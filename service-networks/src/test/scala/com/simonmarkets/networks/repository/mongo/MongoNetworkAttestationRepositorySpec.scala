package com.simonmarkets.networks.repository.mongo

import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.domain.{Attestation, NetworkAttestations}
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.model.Filters
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, Matchers}
import simon.Id.NetworkId

import java.time.Instant
import java.time.temporal.ChronoUnit
import scala.concurrent.Await
import scala.concurrent.duration._


class MongoNetworkAttestationRepositorySpec extends AsyncWordSpec with EmbeddedMongoLike with BeforeAndAfterEach with Matchers {
  implicit val traceId: TraceId = TraceId("mongo-network-attestations-repo-spec")

  private val codecRegistry = fromRegistries(fromProviders(classOf[NetworkAttestations], classOf[Attestation]), DEFAULT_CODEC_REGISTRY)

  private lazy val collection = db.getCollection[NetworkAttestations]("networkAttestations")
    .withCodecRegistry(codecRegistry)

  private lazy val repository = new MongoNetworkAttestationRepository(collection)
  private val user1 = TestUserACL(userId = "test-user-1", networkId = NetworkId("net-1"))

  private val duration = 5.seconds
  private val now = Instant.now().truncatedTo(ChronoUnit.MILLIS)

  private val attestation = Attestation(
    id = "attestation-id-1",
    attestationText = "Some text",
    userCreated = user1.userId,
    timeCreated = now,
    userLastUpdated = user1.userId,
    timeLastUpdated = now,
    version = 1
  )

  private val networkAttestation1 = NetworkAttestations(
    network = "network-1",
    attestations = Seq(
      attestation,
      attestation.copy(
        id = "attestation-id-2",
        attestationText = "Some text 2"
      )
    ),
  )

  private val networkAttestation2 = NetworkAttestations(
    network = "network-2",
    attestations = Seq(
      attestation,
      attestation.copy(
        id = "attestation-id-2",
        attestationText = "Some text 2"
      ),
      attestation.copy(
        id = "reason-id-custom",
        attestationText = "Some text 3"
      )
    ),
  )

  override def beforeEach(): Unit = {
    Await.result(collection.drop().toFuture, duration)
    Await.result(collection.insertMany(Seq(networkAttestation1, networkAttestation2)).toFuture, duration)
  }

  "MongoNetworkAttestationRepository" can {
    "list" should {
      "list attestations for network" in {
        repository.list(NetworkId(networkAttestation1.network)).map { list =>
          list should contain only (networkAttestation1.attestations: _*)
        }
      }

      "return empty list if network does not exists" in {
        repository.list(NetworkId("Non-existing network")).map { list =>
          list should be(empty)
        }
      }
    }

    "create" should {
      "create attestation in existing network attestation list" in {
        val newAttestation = attestation.copy(id = "attestation-id-3",
          attestationText = "One more attestation")
        for {
          _ <- repository.create(NetworkId(networkAttestation1.network), newAttestation)
          networkAttestation <- collection.find(networkCondition(networkAttestation1.network)).headOption()
        } yield {
          val expectedAttestations = networkAttestation1.attestations :+ newAttestation
          networkAttestation.get.attestations should contain only (expectedAttestations: _*)
        }
      }

      "not create the same attestation twice" in {
        for {
          _ <- repository.create(NetworkId(networkAttestation1.network), attestation)
          networkAttestation <- collection.find(networkCondition(networkAttestation1.network)).headOption()
        } yield {
          networkAttestation.get.attestations should contain only (networkAttestation1.attestations: _*)
        }
      }
    }

    "update" should {
      "update existing attestation in existing network attestation list" in {
        val updatedAttestationText = "Updated attestation text"
        for {
          _ <- repository.update(NetworkId(networkAttestation1.network), attestation.id, updatedAttestationText, Some(Set("Trader")), user1.userId)
          networkAttestation <- collection.find(networkCondition(networkAttestation1.network)).headOption()
        } yield {
          val updatedAttestation = networkAttestation.get.attestations.filter(_.id == attestation.id).head
          updatedAttestation.attestationText shouldEqual updatedAttestationText
          updatedAttestation.version shouldEqual 2
          updatedAttestation.customRoles shouldEqual Some(Set("Trader"))
          networkAttestation.get.attestations should contain(updatedAttestation)
        }
      }

      "update existing attestation in existing network attestation list and unset roles" in {
        val updatedAttestationText = "Updated attestation text"
        val newAttestation = attestation.copy(id = "attestation-id-3",
          attestationText = "One more attestation", customRoles = Some(Set("Issuer")))
        for {
          _ <- repository.create(NetworkId(networkAttestation1.network), newAttestation)
          _ <- repository.update(NetworkId(networkAttestation1.network), newAttestation.id, updatedAttestationText, None, user1.userId)
          networkAttestation <- collection.find(networkCondition(networkAttestation1.network)).headOption()
        } yield {
          val updatedAttestation = networkAttestation.get.attestations.filter(_.id == newAttestation.id).head
          updatedAttestation.attestationText shouldEqual updatedAttestationText
          updatedAttestation.version shouldEqual 2
          updatedAttestation.customRoles shouldEqual None
          networkAttestation.get.attestations should contain(updatedAttestation)
        }
      }
    }

    "delete" should {
      "delete attestation from network" in {
        val newNetworkId = NetworkId("one-more-network")
        val attestation2 = attestation.copy(
          id = "attestation-id-2",
          attestationText = "New attestation"
        )

        for {
          _ <- repository.create(newNetworkId, attestation2)
          _ <- repository.create(newNetworkId, attestation)
          isDeleted <- repository.delete(NetworkId(networkAttestation1.network), attestation2.id)
          networkAttestation <- collection.find(networkCondition(networkAttestation1.network)).headOption()
        } yield {
          isDeleted should be(true)
          networkAttestation.get.attestations should contain only attestation
        }
      }

      "delete all attestations from network" in {
        for {
          isDeleted <- repository.delete(NetworkId(networkAttestation1.network), "attestation-id-1")
          isDeleted2 <- repository.delete(NetworkId(networkAttestation1.network), "attestation-id-2")
          networkAttestation <- collection.find(networkCondition(networkAttestation1.network)).headOption()
        } yield {
          isDeleted should be(true)
          isDeleted2 should be(true)
          networkAttestation.get.attestations should be(empty)
        }
      }

      "do nothing if there is no attestation with such id" in {
        for {
          isDeleted <- repository.delete(NetworkId(networkAttestation1.network), "non-existing-id")
          networkAttestation <- collection.find(networkCondition(networkAttestation1.network)).headOption()
        } yield {
          isDeleted should be(false)
          networkAttestation.get should equal(networkAttestation1)
        }
      }

      "do nothing if cannot find attestation by network id" in {
        for {
          isDeleted <- repository.delete(NetworkId("non-existing-network"), "attestation-id-1")
          networkAttestation <- collection.find(networkCondition(networkAttestation1.network)).headOption()
        } yield {
          isDeleted should be(false)
          networkAttestation.get should equal(networkAttestation1)
        }
      }
    }
  }

  private def networkCondition(networkId: String) = Filters.equal("network", networkId)

}
