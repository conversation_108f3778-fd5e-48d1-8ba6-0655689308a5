package com.simonmarkets.networks.repository.mongo

import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.rejections.domain.RejectionReason
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros._
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, Matchers}
import simon.Id.NetworkId

import java.time.Instant
import java.time.temporal.ChronoUnit
import scala.concurrent.Await
import scala.concurrent.duration._

class MongoGeneralRejectionReasonRepositorySpec extends AsyncWordSpec with EmbeddedMongoLike with BeforeAndAfterEach with Matchers {
  implicit val traceId: TraceId = TraceId("mongo-rejection-reason-repo-spec")

  private val codecRegistry = fromRegistries(
    fromProviders(classOf[RejectionReason]),
    DEFAULT_CODEC_REGISTRY)

  private lazy val collection = db.getCollection[RejectionReason]("generalNetworkRejections")
    .withCodecRegistry(codecRegistry)

  private lazy val repository = new MongoGeneralRejectionReasonRepository(collection)
  private val user1 = TestUserACL(userId = "test-user-1", networkId = NetworkId("net-1"))

  private val duration = 5.seconds

  private val now = Instant.now().truncatedTo(ChronoUnit.MILLIS)

  private val reason1 = RejectionReason(
    id = "reason-id-1",
    reason = "Some reason",
    group = "Some group",
    userCreated = user1.userId,
    timeCreated = now,
    userLastUpdated = user1.userId,
    timeLastUpdated = now,
    traceId = traceId.toString,
    version = 1
  )

  private val reason2 = reason1.copy(id = "reason-id-2", reason = "New reason")
  private val reason3 = reason1.copy(id = "reason-id-3", reason = "Some new reason")

  override def beforeEach(): Unit = {
    Await.result(collection.drop().toFuture, duration)
    Await.result(collection.insertMany(Seq(reason1, reason2, reason3)).toFuture, duration)
  }

  "MongoGeneralRejectionReasonRepository" can {
    "list" should {
      "return empty list if empty list provided" in {
        repository.list(Seq()).map { list =>
          list should be(empty)
        }
      }

      "return list of rejections by ids" in {
        repository.list(Seq(reason2.id, reason3.id)).map { list =>
          list should contain only(reason2, reason3)
        }
      }

      "ignore non-existing ids" in {
        repository.list(Seq("non-existing", reason1.id)).map { list =>
          list should contain only reason1
        }
      }
    }
  }


}
