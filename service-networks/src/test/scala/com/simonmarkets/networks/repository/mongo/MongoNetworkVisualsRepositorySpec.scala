package com.simonmarkets.networks.repository.mongo

import com.simonmarkets.networks.common.domain.{NetworkVisuals, Visuals}
import com.simonmarkets.networks.common.repository.mongo.MongoNetworkVisualsRepository
import com.simonmarkets.syntax._
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.model.Filters._
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, OptionValues}
import simon.Id.NetworkId

class MongoNetworkVisualsRepositorySpec extends AsyncWordSpec with EmbeddedMongoLike with OptionValues with BeforeAndAfterEach {

  private lazy val rawCollection = db.getCollection("network.visuals")
  private lazy val repository = new MongoNetworkVisualsRepository(rawCollection.withDocumentClass[NetworkVisuals])
  private lazy val collection = rawCollection.withDocumentClass[NetworkVisuals].withCodecRegistry(MongoNetworkVisualsRepository.registry)

  private val preExisting1 = NetworkVisuals(
    networkId = "SIMON Admin",
    visuals = Visuals(
      accentColor = Some("accent1"),
      secondaryColor = Some("secondary1"),
      defaultLogo = Some("https://cdn.simon.io/ui-assets/Public/Image/default1"),
      inverseLogo = Some("https://cdn.simon.io/ui-assets/Public/Image/inverse1"),
      defaultLogoPDF = Some("https://cdn.simon.io/ui-assets/Public/Image/defaultPDF1"),
      fillColorPDF = Some("fill1"),
      colorPDF = Some("color1"),
      backgroundPDF = Some("background1"),
      showPoweredByMarketplacePDF = Some(true),
      frontLogoMarketplacePDF = Some("https://cdn.simon.io/ui-assets/Public/Image/frontMarketplace1"),
      showPoweredByPerformancePDF = Some(true),
      supplementLogoPerformancePDF = Some("https://cdn.simon.io/ui-assets/Public/Image/supplementPerformance1"),
      headerLogoPerformancePDF = Some("https://cdn.simon.io/ui-assets/Public/Image/headerPerformance1"),
      showPoweredBySpectrumPDF = Some(true),
      supplementLogoSpectrumPDF = Some("https://cdn.simon.io/ui-assets/Public/Image/supplementSpectrum1"),
      headerLogoSpectrumPDF = Some("https://cdn.simon.io/ui-assets/Public/Image/headerSpectrum1")
    )
  )

  private val preExisting2 = NetworkVisuals(
    networkId = "JPM",
    visuals = Visuals(
      accentColor = Some("accent2"),
      secondaryColor = Some("secondary2"),
      defaultLogo = Some("https://cdn.simon.io/ui-assets/Public/Image/default2"),
      inverseLogo = Some("https://cdn.simon.io/ui-assets/Public/Image/inverse2"),
      defaultLogoPDF = Some("https://cdn.simon.io/ui-assets/Public/Image/defaultPDF2"),
      fillColorPDF = Some("fill2"),
      colorPDF = Some("color2"),
      backgroundPDF = Some("background2"),
      showPoweredByMarketplacePDF = Some(true),
      frontLogoMarketplacePDF = Some("https://cdn.simon.io/ui-assets/Public/Image/frontMarketplace2"),
      showPoweredByPerformancePDF = Some(true),
      supplementLogoPerformancePDF = Some("https://cdn.simon.io/ui-assets/Public/Image/supplementPerformance2"),
      headerLogoPerformancePDF = Some("https://cdn.simon.io/ui-assets/Public/Image/headerPerformance2"),
      hideAdvisorNamePerformancePDF = Some(true),
      hideNotionalDetailsPerformancePDF = Some(true),
      showPoweredBySpectrumPDF = Some(true),
      supplementLogoSpectrumPDF = Some("https://cdn.simon.io/ui-assets/Public/Image/supplementSpectrum2"),
      headerLogoSpectrumPDF = Some("https://cdn.simon.io/ui-assets/Public/Image/headerSpectrum2")
    )
  )

  private val newVisuals = NetworkVisuals(
    networkId = "BNP",
    visuals = Visuals(
      accentColor = Some("accent4"),
      secondaryColor = Some("secondary4"),
      defaultLogo = Some("https://cdn.simon.io/ui-assets/Public/Image/default4"),
      inverseLogo = Some("https://cdn.simon.io/ui-assets/Public/Image/inverse4")
    )
  )

  override def beforeEach(): Unit = {
    super.beforeEach()

    collection.drop.toFuture().await
    collection.insertMany(Seq(preExisting1, preExisting2)).toFuture.await
  }

  "MongoNetworkVisualsRepository" can {
    "upsert" should {
      "insert a non-existent visuals object" in {
        for {
          newVisualsObject <- repository.upsert(newVisuals)
          dbResultOpt <- collection.find(equal("networkId", newVisualsObject.networkId)).toFuture
        } yield {
          val dbResult = dbResultOpt.headOption.value
          assert(dbResult == newVisuals)
        }
      }

      "update an existing visuals object" in {
        for {
          updateVisualsObject <- repository.upsert(preExisting1.copy(visuals = preExisting1.visuals.copy(accentColor = Some("new_accent1"))))
          dbResultOpt <- collection.find(equal("networkId", updateVisualsObject.networkId)).toFuture
        } yield {
          val dbResult = dbResultOpt.headOption.value
          assert(dbResult == preExisting1.copy(visuals = preExisting1.visuals.copy(accentColor = Some("new_accent1"))))
        }
      }
    }

    "get" should {
      "return a visuals object by networkId" in {
        for {
          result <- repository.get(NetworkId(preExisting1.networkId))
        } yield {
          assert(result.value == preExisting1)
        }
      }

      "return None if networkId is nonexistent" in {
        for {
          result <- repository.get(NetworkId("Some Bad Id"))
        } yield {
          assert(result.isEmpty)
        }
      }
    }

    "delete" should {
      "delete and return a visuals object by networkId" in {
        for {
          result <- repository.delete(NetworkId(preExisting1.networkId))
          all <- collection.find().toFuture
        } yield {
          assert(result)
          assert(all == Seq(preExisting2))
        }
      }

      "do not remove object if networkId is incorrect" in {
        for {
          result <- repository.delete(NetworkId("Some Bad Id"))
          all <- collection.find().toFuture
        } yield {
          assert(!result)
          assert(all == Seq(preExisting1, preExisting2))

        }
      }
    }
  }

}
