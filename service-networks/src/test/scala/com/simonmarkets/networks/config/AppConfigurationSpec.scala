package com.simonmarkets.networks.config

import com.simonmarkets.networks.common.api.UpsertNetworkRequestValidator
import com.simonmarkets.resteasy.framework.{LambdaMode, ServerMode}
import com.simonmarkets.utils.config.Resolvers.SecretsConfigOps
import com.simonmarkets.utils.config.resolvers.ConfigResolver
import com.typesafe.config.ConfigFactory
import org.scalatest.{Matchers, WordSpecLike}

import java.io.File

class AppConfigurationSpec extends WordSpecLike with Matchers {

  val localConf = "/local.conf"
  val envConfs = List( "/alpha.conf", "/qa.conf", "/prod.conf")

  "AppConfiguration" should {

    "instantiate environment configs in lambda mode" in {
      envConfs.map( fileName => {
        val file = new File(getClass.getResource(fileName).getPath)
        val rawConfig = ConfigFactory.parseFile(file).resolveSecrets(List(new TestResolver))
        val config = AppConfiguration(rawConfig)
        config.runMode shouldBe a[LambdaMode.type]
        UpsertNetworkRequestValidator.validateCustomRolesConfig(config.serviceConfig.defaultCustomRolesConfig) shouldBe Right(())
        config.systemRoutes.get.serviceUp.get.path shouldBe "simon/api/v2/networks/uptime"
        config.systemRoutes.get.healthCheck.get.path shouldBe "simon/api/v2/networks/healthcheck"
        config.systemRoutes.get.serviceInfo.get.path shouldBe "simon/api/v2/networks/info"

        config.info.get.name shouldBe "Network Service"

      })
    }

    "instantiate local config in server mode" in {
      val file = new File(getClass.getResource(localConf).getPath)
      val rawConfig = ConfigFactory.parseFile(file).resolveSecrets(List(new TestResolver))
      val config = AppConfiguration(rawConfig)
      config.runMode shouldBe a[ServerMode]
      UpsertNetworkRequestValidator.validateCustomRolesConfig(config.serviceConfig.defaultCustomRolesConfig) shouldBe Right(())
      config.systemRoutes.get.serviceUp.get.path shouldBe "simon/api/v2/networks/uptime"
      config.systemRoutes.get.healthCheck.get.path shouldBe "simon/api/v2/networks/healthcheck"
      config.systemRoutes.get.serviceInfo.get.path shouldBe "simon/api/v2/networks/info"

      config.info.get.name shouldBe "Network Service"
    }
  }

  class TestResolver extends ConfigResolver {
    override def prefix: String = "sm"

    override def resolve(path: String): String =
      path match {
        case "applicationconfig-mongo-auth-pipg" =>
          """
            |   {
            |      url = "mongoUrl"
            |      authentication {
            |        type = "password"
            |        user = "alpha-pipg-user"
            |        password = "password"
            |        database = "admin"
            |      }
            |    }""".stripMargin
        case "applicationconfig-admin-network-type-config" =>
          """
            |   {
            |      whitelist = ["TestNetwork"]
            |    }""".stripMargin
        case other => other
      }

    override def resolveBinary(path: String): Array[Byte] = Array.empty[Byte]
  }
}
