package com.simonmarkets.networks.api

import akka.actor.{ActorRef, ActorSystem}
import akka.http.scaladsl.model.{ContentTypes, HttpEntity, StatusCodes}
import akka.http.scaladsl.server._
import akka.http.scaladsl.testkit._
import akka.testkit.javadsl.TestKit
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.goldmansachs.marquee.pipg.{IdHubOrganization, NetworkLocation, UserACL}
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.logging.akkahttp.AkkaLoggingContextActor
import com.simonmarkets.networks.api.codec.NetworkServiceCodecs
import com.simonmarkets.networks.common.api.{Page, UpdateLocationsRequest, UpsertNetworkRequest}
import com.simonmarkets.networks.common.{ChangelogItem, EventInfo, Network, NetworkView}
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.common.service.networkservice.NetworkService
import com.simonmarkets.networks.service.ProductNomenclatureService
import com.simonmarkets.resteasy.authn.User
import com.simonmarkets.resteasy.{DirectivesWithCirce, HttpErrorHandler}
import com.simonmarkets.syntax.anyOpsConversion
import com.simonmarkets.useracl.UserAclAuthorizedDirective
import com.simonmarkets.users.common.LoginMode
import io.circe.Json
import io.circe.parser._
import org.mockito.ArgumentMatchers.{any, eq => meq}
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterAll, Matchers, WordSpecLike}
import simon.Id.NetworkId

import java.time.LocalDateTime

import scala.annotation.nowarn
import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}

@nowarn("cat=deprecation")
class NetworkServiceRoutesSpec extends TestKit(ActorSystem("NetworkServiceRoutesSpec"))
  with WordSpecLike with BeforeAndAfterAll with MockitoSugar with Matchers with ScalatestRouteTest
  with DirectivesWithCirce
  with NetworkServiceCodecs {

  implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  implicit val traceId: TraceId = TraceId("NetworkServiceRoutes")
  implicit val duration: FiniteDuration = 5.seconds
  implicit val errorHandler: ExceptionHandler = HttpErrorHandler.errorHandler

  val adminRequesterAcl: UserACL = TestUserACL("user", NetworkId("test"), capabilities = Set("admin"))
  val adminUser: User = User("test-id", "test-token", None, List("admin"))

  val networkService = mock[NetworkService]
  val productNomenclatureService: ProductNomenclatureService = mock[ProductNomenclatureService]
  val loggingContextActor: ActorRef = system.actorOf(AkkaLoggingContextActor.props())
  val requester: User = mock[User]
  val directives: UserAclAuthorizedDirective = mock[UserAclAuthorizedDirective]

  when(directives.authorizedUser(Set.empty[String])) thenReturn tprovide((traceId, adminUser, adminRequesterAcl))

  val routes: Route =
    Route.seal(NetworkServiceRoutes(networkService, directives).routes)

  override def afterAll(): Unit = TestKit.shutdownActorSystem(system)

  "NetworkServiceRoutes" should {
    "get network by network id" in {
      val net_id = NetworkId("testing_id")
      val network = Network(id = net_id, name = net_id.toString, idHubOrganization = IdHubOrganization(123, "Testing Org"), networkCode = "nc",
        loginMode = LoginMode.SSOAndUsernamePassword, eventInfo = EventInfo.Default)
      val netView = NetworkView.fromNetwork(network)
      when(networkService.getNetworkViewById(meq(adminRequesterAcl), meq(net_id))(any[TraceId]))
        .thenReturn(Future.successful(netView))

      Get("/simon/api/v2/networks/" + net_id.toString) ~>
        routes ~>
        check {
          status shouldEqual StatusCodes.OK
          contentType shouldEqual ContentTypes.`application/json`
          entityAs[String] shouldEqual netView.asJson.deepDropNullValues.asJsonStr
        }
    }

    "get network by network id but masked is false" in {
      val net_id = NetworkId("testing_id")
      val network = Network(id = net_id, name = net_id.toString, idHubOrganization = IdHubOrganization(123, "Testing Org"), networkCode = "nc",
        loginMode = LoginMode.SSOAndUsernamePassword, eventInfo = EventInfo.Default)
      val netView = NetworkView.fromNetwork(network)
      when(networkService.getNetworkViewById(meq(adminRequesterAcl), meq(net_id))(any(classOf[TraceId])))
        .thenReturn(Future.successful(netView))

      Get(s"/simon/api/v2/networks/${net_id.toString}?masked=false") ~>
        routes ~>
        check {
          status shouldEqual StatusCodes.OK
          contentType shouldEqual ContentTypes.`application/json`
          entityAs[String] shouldEqual netView.asJson.deepDropNullValues.asJsonStr
        }
    }

    "get network by external id with subject" in {
      val externalId = "myMaskedId"
      val subject = "mySubject"
      val net_id = NetworkId("testing_id")
      val network = Network(id = net_id, name = net_id.toString, idHubOrganization = IdHubOrganization(123, "Testing Org"), networkCode = "nc",
        loginMode = LoginMode.SSOAndUsernamePassword, eventInfo = EventInfo.Default)
      val netView = NetworkView.fromNetwork(network)
      when(networkService.getNetworkViewByExternalId(meq(adminRequesterAcl), meq(ExternalId(subject, externalId)))(any(classOf[TraceId])))
        .thenReturn(Future.successful(netView))

      Get(s"/simon/api/v2/networks/$externalId?external=true&subject=${subject}") ~>
        routes ~>
        check {
          status shouldEqual StatusCodes.OK
          contentType shouldEqual ContentTypes.`application/json`
          entityAs[String] shouldEqual netView.asJson.deepDropNullValues.asJsonStr
        }
    }

    "get network by external id without supplied subject" in {
      val externalId = "myMaskedId"
      val net_id = NetworkId("testing_id")
      val network = Network(id = net_id, name = net_id.toString, idHubOrganization = IdHubOrganization(123, "Testing Org"), networkCode = "nc",
        loginMode = LoginMode.SSOAndUsernamePassword, eventInfo = EventInfo.Default)
      val netView = NetworkView.fromNetwork(network)
      when(networkService.getNetworkViewByExternalId(meq(adminRequesterAcl), meq(ExternalId(adminRequesterAcl.networkId.toString, externalId)))(any[TraceId]))
        .thenReturn(Future.successful(netView))

      Get(s"/simon/api/v2/networks/$externalId?external=true") ~>
        routes ~>
        check {
          status shouldEqual StatusCodes.OK
          contentType shouldEqual ContentTypes.`application/json`
          entityAs[String] shouldEqual netView.asJson.deepDropNullValues.asJsonStr
        }
    }

    "get network by id not found" in {

      when(networkService.getNetworkViewById(meq(adminRequesterAcl), meq(NetworkId("NOT_FOUND")))(any[TraceId]))
        .thenReturn(HttpError.notFound("").future)

      Get("/simon/api/v2/networks/NOT_FOUND") ~>
        routes ~>
        check {
          status shouldEqual StatusCodes.NotFound
        }
    }

    "get networks" should {
      "get all networks when no parameters provided" in {
        val net_id = NetworkId("testing_id")
        val network_list = List(Network(id = net_id, name = net_id.toString, idHubOrganization = IdHubOrganization(123, "Testing Org"), networkCode = "nc",
          loginMode = LoginMode.SSOAndUsernamePassword, eventInfo = EventInfo.Default))
        val page: Page[NetworkView] = Page(total = 10, count = 10, next = None, result = network_list.map(NetworkView.fromNetwork))
        when(networkService.getNetworkViewPage(meq(adminRequesterAcl), meq(None), meq(None))(any[TraceId])) thenReturn Future.successful(page)

        Get("/simon/api/v2/networks") ~>
          routes ~>
          check {
            status shouldEqual StatusCodes.OK
            contentType shouldEqual ContentTypes.`application/json`
            entityAs[String] shouldEqual page.asJson.deepDropNullValues.asJsonStr
          }
      }
      "get networks using provided limit and next" in {
        val net_id = NetworkId("testing_id")
        val network_list = List(Network(id = net_id, name = net_id.toString, idHubOrganization = IdHubOrganization(123, "Testing Org"), networkCode = "nc",
          loginMode = LoginMode.SSOAndUsernamePassword, eventInfo = EventInfo.Default))
        val page: Page[NetworkView] = Page(total = 10, count = 10, next = None, result = network_list.map(NetworkView.fromNetwork))
        when(networkService.getNetworkViewPage(meq(adminRequesterAcl), meq(Some(10)), meq(Some("some_id")))(any[TraceId])) thenReturn Future.successful(page)

        Get("/simon/api/v2/networks?limit=10&next=some_id") ~>
          routes ~>
          check {
            status shouldEqual StatusCodes.OK
            contentType shouldEqual ContentTypes.`application/json`
            entityAs[String] shouldEqual page.asJson.deepDropNullValues.asJsonStr
          }
      }
      "get networks using provided ids and ignoring other parameters" in {
        val net_id = NetworkId("testing_id")
        val network_list = List(Network(id = net_id, name = net_id.toString, idHubOrganization = IdHubOrganization(123, "Testing Org"), networkCode = "nc",
          loginMode = LoginMode.SSOAndUsernamePassword, eventInfo = EventInfo.Default))
        val page: Page[NetworkView] = Page(total = 10, count = 10, next = None, result = network_list.map(NetworkView.fromNetwork))
        when(networkService.getNetworkViewPageByIds(meq(adminRequesterAcl), meq(Set("myId", "myId2")))(any[TraceId])) thenReturn Future.successful(page)

        Get("/simon/api/v2/networks?limit=5&ids=myId,myId2") ~>
          routes ~>
          check {
            status shouldEqual StatusCodes.OK
            contentType shouldEqual ContentTypes.`application/json`
            entityAs[String] shouldEqual page.asJson.deepDropNullValues.asJsonStr
          }
      }
    }

    "get snapshots by network id" in {
      val networkId =  NetworkId("testing_id")
      val changeLogs = List(
        ChangelogItem(id = "snapId", userId = "someUser", modificationDate = LocalDateTime.of(1993, 7, 24, 1, 1), comment = Some("comment"))
      )
      when(networkService.getChangelogs(meq(networkId))(any[TraceId])) thenReturn Future.successful(changeLogs)

      Get(s"/simon/api/v2/networks/${networkId.toString}/snapshots") ~>
        routes ~>
        check {
          status shouldEqual StatusCodes.OK
          contentType shouldEqual ContentTypes.`application/json`
          entityAs[String] shouldEqual changeLogs.asJson.deepDropNullValues.asJsonStr
        }
    }

    "get snapshot by id" in {
      val snapId = "snapId"
      val networkId = NetworkId("testing_id")
      val network = Network(id = networkId, name = networkId.toString, idHubOrganization=IdHubOrganization(123, "Testing Org"), networkCode = "nc",
        loginMode = LoginMode.SSOAndUsernamePassword, eventInfo = EventInfo.Default)
      val netView = NetworkView.fromNetwork(network)

      when(networkService.getSnapshot(meq(snapId))(any[TraceId])) thenReturn Future.successful(netView.some)

      Get(s"/simon/api/v2/networks/${networkId.toString}/snapshots/$snapId") ~>
        routes ~>
        check {
          status shouldEqual StatusCodes.OK
          contentType shouldEqual ContentTypes.`application/json`
          entityAs[String] shouldEqual netView.asJson.deepDropNullValues.asJsonStr
        }
    }

    "post a new network" in {
      val net_id = NetworkId("testing_id")
      val net = Network(id= net_id, name=net_id.toString, idHubOrganization=IdHubOrganization(123, "Testing Org"), networkCode = "nc",
        loginMode = LoginMode.SSOAndUsernamePassword, eventInfo = EventInfo.Default)
      val netview = NetworkView.fromNetwork(net)
      val request = UpsertNetworkRequest(Some("test_id"), "test_name", IdHubOrganization(0,""))

      when(networkService.handleInsertRequest(meq(adminRequesterAcl), meq(request))(any[TraceId])) thenReturn Future.successful(netview)

      Post("/simon/api/v2/networks", HttpEntity(ContentTypes.`application/json`, request.asJson.noSpaces)) ~>
        routes ~>
        check {
          status shouldEqual StatusCodes.OK
          contentType shouldEqual ContentTypes.`application/json`
          entityAs[String] shouldEqual netview.asJson.deepDropNullValues.asJsonStr
        }
    }

    "post a new network with simplest json" in {
      val net_id = NetworkId("testing_id")
      val net = Network(id= net_id, name=net_id.toString, idHubOrganization=IdHubOrganization(123, "Testing Org"), networkCode = "nc",
        loginMode = LoginMode.SSOAndUsernamePassword, eventInfo = EventInfo.Default)
      val netview = NetworkView.fromNetwork(net)

      val requestStr = """{ "id": "test_id","networkName": "test_name","idHubOrganization": {"id": 0,"name": ""}}"""
      val requestJson = parse(requestStr).getOrElse(Json.Null)

      when(networkService.handleInsertRequest(meq(adminRequesterAcl), any[UpsertNetworkRequest])(any[TraceId])) thenReturn Future.successful(netview)
      Post("/simon/api/v2/networks", HttpEntity(ContentTypes.`application/json`, requestJson.noSpaces)) ~>
        routes ~>
        check {
          status shouldEqual StatusCodes.OK
          contentType shouldEqual ContentTypes.`application/json`
          entityAs[String] shouldEqual netview.asJson.deepDropNullValues.asJsonStr
        }
    }

    "post empty request" in {
      Post("/simon/api/v2/networks", HttpEntity(ContentTypes.`application/json`, "")) ~> routes ~>
        check {
          status shouldEqual StatusCodes.BadRequest
        }
    }

    "put network update" in {
      val net_id = NetworkId("testing_id")
      val net = Network(id= net_id, name=net_id.toString, idHubOrganization=IdHubOrganization(123, "Testing Org"), networkCode = "nc",
        loginMode = LoginMode.SSOAndUsernamePassword, eventInfo = EventInfo.Default)
      val netview = NetworkView.fromNetwork(net)
      val request = UpsertNetworkRequest(Some("test_id"), "test_name", IdHubOrganization(0,""))

      when(networkService.handleUpdateRequest(meq(adminRequesterAcl), meq(net_id), meq(request))(any[TraceId])) thenReturn Future.successful(netview)

      Put("/simon/api/v2/networks/" + net_id.toString, HttpEntity(ContentTypes.`application/json`, request.asJson.noSpaces)) ~>
        routes ~>
        check {
          status shouldEqual StatusCodes.OK
          contentType shouldEqual ContentTypes.`application/json`
          entityAs[String] shouldEqual netview.asJson.deepDropNullValues.asJsonStr
        }
    }

    "put empty update request" in {
      Put("/simon/api/v2/networks" + "/someNetId", HttpEntity(ContentTypes.`application/json`, "")) ~> routes ~>
        check {
          status shouldEqual StatusCodes.BadRequest
        }
    }

    "put network locations" in {
      val net_id = NetworkId("put_testing")
      val network = Network(id = net_id, name = net_id.toString, idHubOrganization = IdHubOrganization(123, "Testing Org"), networkCode = "nc",
        loginMode = LoginMode.SSOAndUsernamePassword, eventInfo = EventInfo.Default)
      val netView = NetworkView.fromNetwork(network)
      val request = UpdateLocationsRequest(Set(NetworkLocation("loc1", Some("parent"), Set("child"))))

      when(networkService.updateNetworkLocations(
        meq(adminRequesterAcl), meq(net_id),
        meq(request))(any[TraceId])) thenReturn Future.successful(netView.some)

      Put("/simon/api/v2/networks/" + net_id.toString + "/locations",
        HttpEntity(ContentTypes.`application/json`, request.asJson.noSpaces)
      ) ~>
        routes ~>
        check {
          status shouldEqual StatusCodes.OK
          contentType shouldEqual ContentTypes.`application/json`
          entityAs[String] shouldEqual netView.asJson.deepDropNullValues.asJsonStr
        }
    }

    "leave GET requests to other paths unhandled" in {
      Get("/undefined") ~>
        routes ~>
        check {
          responseAs[String] shouldBe """The requested resource could not be found."""
        }
    }
  }
}
