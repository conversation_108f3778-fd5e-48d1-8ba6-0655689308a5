package com.simonmarkets.networks.api

import akka.actor.ActorSystem
import akka.http.scaladsl.model.{ContentTypes, StatusCodes}
import akka.http.scaladsl.server._
import akka.http.scaladsl.testkit._
import akka.testkit.javadsl.TestKit
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.goldmansachs.marquee.pipg.{ContactInfo, UserACL}
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.api.request.{AddNetworkEntitiesRequest, UpdateNetworkEntitiesRequest}
import com.simonmarkets.networks.api.response.{NetworkEntitiesView, ProprietaryIndicesView}
import com.simonmarkets.networks.domain.{Entity, ProprietaryIndex, ProprietaryIndexIdentifiers}
import com.simonmarkets.networks.service.NetworkEntitiesService
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.useracl.UserAclAuthorizedDirective
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterAll, Matchers, WordSpecLike}
import simon.Id.NetworkId

import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}

class NetworkEntitiesRoutesSpec extends TestKit(ActorSystem("NetworkEntitiesRoutesSpec"))
  with WordSpecLike with BeforeAndAfterAll with MockitoSugar with Matchers with ScalatestRouteTest
  with DirectivesWithCirce
  with JsonCodecs {

  implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  implicit val traceId: TraceId = TraceId("NetworkEntitiesRoutesSpec")
  implicit val duration: FiniteDuration = 5.seconds
  implicit val adminRequesterAcl: UserACL = TestUserACL("user", NetworkId("test"), capabilities = Set("admin"))

  val networkEntitiesService: NetworkEntitiesService = mock[NetworkEntitiesService]
  val userACLDirective: UserAclAuthorizedDirective = mock[UserAclAuthorizedDirective]

  private val entityView = NetworkEntitiesView(
    networkId = "Goldman Sachs",
    entities = Set(Entity(
      key = "GSFC",
      symbol = "GS",
      contractTypeWrapper = Some("Note"),
      fullName = Some("(GS) GS FINANCE CORP."),
      shortName = Some("Goldman Sachs"),
      contactInfo = Some(ContactInfo(email = Some("<EMAIL>"), phone = Some("(*************")))
    ), Entity(
      key = "GSBank",
      symbol = "GS",
      contractTypeWrapper = Some("CD"),
      fullName = Some("(GS) GOLDMAN SACHS BANK U S A"),
      shortName = Some("Goldman Sachs"),
      is3a2 = true,
      contactInfo = Some(ContactInfo(email = Some("<EMAIL>"), phone = Some("(*************")))
    )),
    version = 1,
    proprietaryIndices = Some(List(ProprietaryIndex("shortName1", ProprietaryIndexIdentifiers(mqId = "mqId1", bbId =  "bbId1"))))
  )

  when(userACLDirective.authorized(Set.empty[String])) thenReturn tprovide((traceId, adminRequesterAcl))

  val routes: Route = Route.seal(
    NetworkEntitiesRoutes(networkEntitiesService, userACLDirective).routes
  )

  override def afterAll(): Unit = TestKit.shutdownActorSystem(system)

  "NetworkEntitiesRoutesSpec" should {

    "get list of network entity" in {
      val result = Seq(entityView)
      when(networkEntitiesService.list(networkIds = Some(Set("networkId", "networkId2")), keys = Some(Set("A", "B")), None)) thenReturn Future.successful(result)

      Get("/simon/api/v1/network-entities?networkIds=networkId,networkId2&keys=A,B") ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
        entityAs[String] shouldEqual result.asJson.deepDropNullValues.asJsonStr
      }
    }

    "get single network entity" in {
      when(networkEntitiesService.getByNetworkId("testNetwork")) thenReturn Future.successful(Some(entityView))

      Get("/simon/api/v1/network-entities/testNetwork") ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
        entityAs[String] shouldEqual Some(entityView).asJson.deepDropNullValues.asJsonStr
      }
    }

    "return not found when non-existing id requested " in {
      when(networkEntitiesService.getByNetworkId("testNetwork")) thenReturn Future.successful(None)

      Get("/simon/api/v1/network-entities/testNetwork") ~>
        routes ~> check {
        status shouldEqual StatusCodes.NotFound
      }
    }

    "add a new network entity" in {
      val req = AddNetworkEntitiesRequest(Some(List(ProprietaryIndex("shortName1", ProprietaryIndexIdentifiers(mqId = "mqId1", bbId =  "bbId1")))), entityView.entities)
      when(networkEntitiesService.add(req, "goldmanSachs")) thenReturn Future.successful(entityView)

      Post(s"/simon/api/v1/network-entities/goldmanSachs", req) ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
        entityAs[String] shouldEqual entityView.asJson.deepDropNullValues.asJsonStr
      }
    }

    "update network entity" in {
      val req = UpdateNetworkEntitiesRequest(entities = entityView.entities)

      when(networkEntitiesService.update(req, "goldmanSachs")) thenReturn Future.successful(Some(entityView))

      Put(s"/simon/api/v1/network-entities/goldmanSachs", req) ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
        entityAs[String] shouldEqual Some(entityView).asJson.deepDropNullValues.asJsonStr
      }
    }

    "delete network entity" in {
      when(networkEntitiesService.delete("goldmanSachs")) thenReturn Future.successful(true)

      Delete(s"/simon/api/v1/network-entities/goldmanSachs") ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
      }
    }

    "leave GET requests to other paths unhandled" in {
      Get("/undefined") ~>
        routes ~>
        check {
          responseAs[String] shouldBe """The requested resource could not be found."""
        }
    }

    "get list of all proprietary indices" in {
      val proprietaryIndicesList = Seq(ProprietaryIndicesView("Goldman Sachs", List(ProprietaryIndex("shortName1", ProprietaryIndexIdentifiers(mqId = "mqId1", bbId =  "bbId1")))))
      when(networkEntitiesService.getAllProprietaryIndices) thenReturn Future.successful(proprietaryIndicesList)
      Get(s"/simon/api/v1/network-entities/proprietary-indices") ~>
        routes ~> check {
        status shouldBe StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
        entityAs[String] shouldEqual proprietaryIndicesList.asJson.deepDropNullValues.asJsonStr
      }
    }
  }
}
