//package com.simonmarkets.order.attestations.api
//
//import akka.http.scaladsl.model.{ContentTypes, HttpEntity, StatusCodes}
//import akka.http.scaladsl.server.Directives.tprovide
//import akka.http.scaladsl.server.Route
//import akka.http.scaladsl.testkit.ScalatestRouteTest
//import com.goldmansachs.marquee.pipg.UserACL
//import com.goldmansachs.marquee.pipg.service.user.TestUserACL
//import com.simonmarkets.capabilities.Capabilities
//import com.simonmarkets.logging.{TraceId, TraceLogging}
//import com.simonmarkets.networks.api.request.UpdateDisclaimerRequest
//import com.simonmarkets.networks.api.response.AttestationView
//import com.simonmarkets.order.attestations.domain.{Attestation, Disclaimer, DisclaimerType}
//import com.simonmarkets.order.attestations.service.{DisclaimerService, NetworkAttestationService}
//import com.simonmarkets.useracl.UserAclLoggedAuthorizedDirective
//import com.simonmarkets.utils.akkahttp.json.{DecoderIntegration, EncoderIntegration}
//import org.mockito.Mockito.{reset, when}
//import org.mockito.{ArgumentMatchers => MM}
//import org.scalatest.mockito.MockitoSugar
//import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}
//import simon.Id.NetworkId
//
//import java.time.Instant
//import scala.concurrent.Future
//
//class NetworkAttestationsRoutesSpec extends WordSpec with Matchers with ScalatestRouteTest with MockitoSugar
//  with BeforeAndAfterEach with DecoderIntegration with EncoderIntegration with TraceLogging {
//
//  private val userACLDirective = mock[UserAclLoggedAuthorizedDirective]
//  private val networkAttestationService = mock[NetworkAttestationService]
//  private val disclaimerService = mock[DisclaimerService]
//  private val routesClass = NetworkAttestationsRoutes(networkAttestationService, disclaimerService,
//    userACLDirective)
//
//  private val routes = Route.seal(routesClass.routes)
//  private val admin: UserACL = TestUserACL("admin-user", NetworkId("net-1"), capabilities = Set(Capabilities.Admin))
//
//  when(userACLDirective.authorized(Set.empty[String])).thenReturn(tprovide((TraceId.randomize, admin)))
//
//  override def beforeEach(): Unit = {
//    super.beforeEach()
//
////    reset(userACLDirective)
//    reset(disclaimerService)
//
//  }
//
//  "NetworkAttestationRoutes" can {
//    "handle attestations requests" should {
//      "handle GET attestations" in {
//        val networkId = NetworkId("TestNetwork")
//        val response = Seq(Attestation("test-id", "attestation-text", 0, "123", Instant.now, "123", Instant.now))
//        when(networkAttestationService.list(MM.eq(networkId), MM.eq(admin))(MM.any[TraceId])).thenReturn(
//          Future.successful(response)
//        )
//
//        Get(s"/simon/api/v1/networks/${NetworkId.unwrap(networkId)}/attestations") ~> routes ~> check {
//          status shouldBe StatusCodes.OK
//          responseAs[Seq[AttestationView]] shouldBe response.map(AttestationView(_))
//        }
//      }
//
//      "handle DELETE attestation by id" in {
//        val networkId = NetworkId("TestNetwork")
//        val attestationId = "123"
//        when(networkAttestationService.delete(MM.eq(networkId), MM.eq(attestationId), MM.eq(admin))(MM.any[TraceId])).thenReturn(
//          Future.successful(true)
//        )
//
//        Delete(s"/simon/api/v1/networks/${NetworkId.unwrap(networkId)}/attestations/$attestationId") ~> routes ~> check {
//          status shouldBe StatusCodes.OK
//        }
//      }
//    }
//
//    "handle disclaimer requests" should {
//      "handle GET disclaimer" in {
//        val networkId = NetworkId("TestNetwork")
//
//        when(disclaimerService.get(MM.eq(networkId), MM.eq(DisclaimerType.Pdf))(MM.eq(admin), MM.any[TraceId])).thenReturn(
//          Future.successful(Some(Disclaimer(
//            networkId = "Net",
//            disclaimerType = DisclaimerType.Pdf,
//            text = "Some text",
//            userCreated = "user-id",
//            timeCreated = Instant.now())))
//        )
//
//        Get(s"/simon/api/v1/networks/${NetworkId.unwrap(networkId)}/disclaimers?type=pdf") ~> routes ~> check {
//          status shouldBe StatusCodes.OK
//        }
//      }
//
//      "handle GET disclaimer when nothing found" in {
//        val networkId = NetworkId("TestNetwork")
//
//        when(disclaimerService.get(MM.eq(networkId), MM.eq(DisclaimerType.Pdf))(MM.eq(admin), MM.any[TraceId])).thenReturn(
//          Future.successful(None))
//
//        Get(s"/simon/api/v1/networks/${NetworkId.unwrap(networkId)}/disclaimers?type=pdf") ~> routes ~> check {
//          status shouldBe StatusCodes.NotFound
//        }
//      }
//
//      "handle DELETE disclaimer" in {
//        val networkId = NetworkId("TestNetwork")
//
//        when(disclaimerService.delete(MM.eq(networkId), MM.eq(DisclaimerType.Pdf))(MM.eq(admin), MM.any[TraceId])).thenReturn(
//          Future.successful(Some(Disclaimer(
//            networkId = "Net",
//            disclaimerType = DisclaimerType.Pdf,
//            text = "Some text",
//            userCreated = "user-id",
//            timeCreated = Instant.now())))
//        )
//
//        Delete(s"/simon/api/v1/networks/${NetworkId.unwrap(networkId)}/disclaimers?type=pdf") ~> routes ~> check {
//          status shouldBe StatusCodes.OK
//        }
//      }
//
//      "handle DELETE disclaimer when nothing found" in {
//        val networkId = NetworkId("TestNetwork")
//
//        when(disclaimerService.delete(MM.eq(networkId), MM.eq(DisclaimerType.Pdf))(MM.eq(admin), MM.any[TraceId])).thenReturn(
//          Future.successful(None))
//
//        Delete(s"/simon/api/v1/networks/${NetworkId.unwrap(networkId)}/disclaimers?type=pdf") ~> routes ~> check {
//          status shouldBe StatusCodes.NotFound
//        }
//      }
//
//      "handle POST disclaimer" in {
//        val networkId = NetworkId("TestNetwork")
//        val disclaimerText = "My disclaimer"
//
//        when(disclaimerService.update(MM.eq(networkId), MM.eq(DisclaimerType.Pdf), MM.eq(disclaimerText))(MM.eq(admin), MM.any[TraceId])).thenReturn(
//          Future.successful(Some(Disclaimer(
//            networkId = "Net",
//            disclaimerType = DisclaimerType.Pdf,
//            text = disclaimerText,
//            userCreated = "user-id",
//            timeCreated = Instant.now())))
//        )
//
//        val request = UpdateDisclaimerRequest(disclaimer = "My disclaimer")
//
//        Post(s"/simon/api/v1/networks/${NetworkId.unwrap(networkId)}/disclaimers?type=pdf",
//          HttpEntity(ContentTypes.`application/json`, request.asJson.noSpaces)) ~> routes ~> check {
//          status shouldBe StatusCodes.OK
//        }
//
//      }
//    }
//  }
//}
