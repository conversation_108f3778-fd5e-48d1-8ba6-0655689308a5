package com.simonmarkets.networks.api

import akka.actor.ActorSystem
import akka.http.scaladsl.model.{ContentTypes, StatusCodes}
import akka.http.scaladsl.server._
import akka.http.scaladsl.testkit._
import akka.testkit.javadsl.TestKit
import com.goldmansachs.marquee.pipg.UserACL
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.domain.{NetworkVisuals, Visuals}
import com.simonmarkets.networks.common.service.NetworkVisualsService
import com.simonmarkets.resteasy.{DirectivesWithCirce, HttpErrorHandler}
import com.simonmarkets.useracl.UserAclAuthorizedDirective
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterAll, Matchers, WordSpecLike}
import simon.Id.NetworkId

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}

class NetworkVisualsRoutesSpec extends TestKit(ActorSystem("NetworkVisualsRoutesSpec"))
  with WordSpecLike with BeforeAndAfterAll with MockitoSugar with Matchers with ScalatestRouteTest
  with DirectivesWithCirce
  with JsonCodecs {

  implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  implicit val traceId: TraceId = TraceId("NetworkVisualsRoutesSpec")
  implicit val adminRequesterAcl: UserACL = TestUserACL("user", NetworkId("test"), capabilities = Set("admin"))
  implicit val errorHandler: ExceptionHandler = HttpErrorHandler.errorHandler

  val networkVisualsService: NetworkVisualsService = mock[NetworkVisualsService]
  val userACLDirective: UserAclAuthorizedDirective = mock[UserAclAuthorizedDirective]

  private val visualsView = NetworkVisuals(
    networkId = "Raymond James",
    visuals = Visuals(
      defaultLogoPDF = Some("https://cdn.simon.io/ui-assets/Public/Image/defaultPDF3"),
      fillColorPDF = Some("fill3"),
      colorPDF = Some("color3"),
      backgroundPDF = Some("background3"),
      showPoweredByPerformancePDF = Some(true),
      hideAdvisorNamePerformancePDF = Some(true),
      supplementLogoPerformancePDF = Some("https://cdn.simon.io/ui-assets/Public/Image/defaultPDF3"),
      headerLogoPerformancePDF = Some("https://cdn.simon.io/ui-assets/Public/Image/defaultPDF3"),
    )
  )

  when(userACLDirective.authorized(any[Set[String]])) thenReturn tprovide((traceId, adminRequesterAcl))

  val routes: Route = Route.seal(
    NetworkVisualsRoutes(networkVisualsService, userACLDirective).routes
  )

  override def afterAll(): Unit = TestKit.shutdownActorSystem(system)

  "NetworkVisualsRoutesSpec" should {

    "get single network visuals object" in {
      when(networkVisualsService.get(NetworkId("Raymond James"))) thenReturn Future.successful(visualsView)

      Get("/simon/api/v1/network-visuals/Raymond%20James") ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
        entityAs[String] shouldEqual Some(visualsView).asJson.deepDropNullValues.asJsonStr
      }
    }

    "return not found when non-existing id requested " in {

      when(networkVisualsService.get(NetworkId("someBadId"))) thenReturn Future.failed(HttpError.notFound(""))

      Get("/simon/api/v1/network-visuals/someBadId") ~>
        routes ~> check {
        status shouldEqual StatusCodes.NotFound
      }
    }

    "add a new visuals object" in {
      when(networkVisualsService.upsert(visualsView)) thenReturn Future.successful(visualsView)

      Put(s"/simon/api/v1/network-visuals", visualsView) ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
        entityAs[String] shouldEqual visualsView.asJson.deepDropNullValues.asJsonStr
      }
    }

    "update a visuals object" in {
      val req = visualsView.copy(visuals = visualsView.visuals.copy(colorPDF = Some("test_color3")))

      when(networkVisualsService.upsert(req)) thenReturn Future.successful(req)

      Put(s"/simon/api/v1/network-visuals", req) ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
        entityAs[String] shouldEqual Some(req).asJson.deepDropNullValues.asJsonStr
      }
    }

    "delete a visuals object" in {
      when(networkVisualsService.delete(NetworkId("Raymond James"))) thenReturn Future.successful(true)

      Delete(s"/simon/api/v1/network-visuals/Raymond%20James") ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
      }
    }

    "leave GET requests to other paths unhandled" in {
      Get("/undefined") ~>
        routes ~>
        check {
          responseAs[String] shouldBe """The requested resource could not be found."""
        }
    }
  }
}
