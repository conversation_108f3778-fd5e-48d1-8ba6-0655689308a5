package com.simonmarkets.networks.api

import akka.actor.ActorSystem
import akka.http.scaladsl.model.{ContentTypes, StatusCodes}
import akka.http.scaladsl.server._
import akka.http.scaladsl.testkit._
import akka.testkit.javadsl.TestKit
import com.goldmansachs.marquee.pipg.UserACL
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.api.codec.ProductNomenclatureServiceCodecs
import com.simonmarkets.networks.api.request.{AddProductNomenclatureRequest, UpdateProductNomenclatureRequest}
import com.simonmarkets.networks.api.response.ProductNomenclatureView
import com.simonmarkets.networks.domain.ProductNomenclature
import com.simonmarkets.networks.service.ProductNomenclatureService
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.useracl.UserAclAuthorizedDirective
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterAll, Matchers, WordSpecLike}
import simon.Id.NetworkId

import java.time.Instant
import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}

class ProductNomenclatureServiceRoutesSpec extends TestKit(ActorSystem("ProductNomenclatureServiceRoutesSpec"))
  with WordSpecLike with BeforeAndAfterAll with MockitoSugar with Matchers with ScalatestRouteTest
  with DirectivesWithCirce
  with ProductNomenclatureServiceCodecs {

  implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  implicit val traceId: TraceId = TraceId("ProductNomenclatureServiceRoutesSpec")
  implicit val duration: FiniteDuration = 5.seconds
  val adminRequesterAcl: UserACL = TestUserACL("user", NetworkId("test"), capabilities = Set("admin"))

  val productNomenclatureService: ProductNomenclatureService = mock[ProductNomenclatureService]
  val userACLDirective: UserAclAuthorizedDirective = mock[UserAclAuthorizedDirective]

  when(userACLDirective.authorized(Set.empty[String])) thenReturn tprovide((traceId, adminRequesterAcl))

  val routes: Route = Route.seal(
      ProductNomenclatureServiceRoutes(productNomenclatureService, userACLDirective).routes
  )

  override def afterAll(): Unit = TestKit.shutdownActorSystem(system)

  "ProductNomenclatureServiceRoutes" should {

    "get list of product nomenclature" in {
      val result = Seq(ProductNomenclature("id-1", "Product Name 1", "test network", adminRequesterAcl.userId, Instant.now,
        adminRequesterAcl.userId, Instant.now, 1))
      when(productNomenclatureService.list(NetworkId("test network"))(adminRequesterAcl)) thenReturn Future.successful(result)

      Get("/simon/api/v2/networks/test%20network/product-nomenclature") ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
        entityAs[String] shouldEqual result.map(ProductNomenclatureView(_)).asJson.deepDropNullValues.asJsonStr
      }
    }

    "get single product nomenclature" in {
      val result = ProductNomenclature("c3609a1e-b0c9-48f6-8ddd-cf8abfd8fe8c", "Product Name 1", "test network", adminRequesterAcl.userId, Instant.now,
        adminRequesterAcl.userId, Instant.now, 1)
      when(productNomenclatureService.get(NetworkId("test network"), result.id)(adminRequesterAcl)) thenReturn Future.successful(Some(result))

      Get(s"/simon/api/v2/networks/test%20network/product-nomenclature/${result.id}") ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
        entityAs[String] shouldEqual ProductNomenclatureView(result).asJson.deepDropNullValues.asJsonStr
      }
    }

    "return not found when non-existing id requested " in {
      val nonExistingId = "non-existing-id"
      when(productNomenclatureService.get(NetworkId("test network"), nonExistingId)(adminRequesterAcl)) thenReturn Future.successful(None)

      Get(s"/simon/api/v2/networks/test%20network/product-nomenclature/$nonExistingId") ~>
        routes ~> check {
        status shouldEqual StatusCodes.NotFound
      }
    }

    "add a new product nomenclature" in {
      val result = ProductNomenclature("c3609a1e-b0c9-48f6-8ddd-cf8abfd8fe8c", "Product Name 1", "test network", adminRequesterAcl.userId, Instant.now,
        adminRequesterAcl.userId, Instant.now, 1)
      when(productNomenclatureService.add(NetworkId("test network"), result.name)(adminRequesterAcl)) thenReturn Future.successful(result)

      Post(s"/simon/api/v2/networks/test%20network/product-nomenclature", AddProductNomenclatureRequest("Product Name 1")) ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
        entityAs[String] shouldEqual ProductNomenclatureView(result).asJson.deepDropNullValues.asJsonStr
      }
    }

    "update product nomenclature" in {
      val result = ProductNomenclature("c3609a1e-b0c9-48f6-8ddd-cf8abfd8fe8c", "Product Name 1", "test network", adminRequesterAcl.userId, Instant.now,
        adminRequesterAcl.userId, Instant.now, 1)
      when(productNomenclatureService.update(NetworkId("test network"), result.id, result.name)(adminRequesterAcl)) thenReturn Future.successful(Some(result))

      Put(s"/simon/api/v2/networks/test%20network/product-nomenclature/${result.id}", UpdateProductNomenclatureRequest("Product Name 1")) ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
        entityAs[String] shouldEqual ProductNomenclatureView(result).asJson.deepDropNullValues.asJsonStr
      }
    }

    "delete product nomenclature" in {
      val id = "c3609a1e-b0c9-48f6-8ddd-cf8abfd8fe8d"

      when(productNomenclatureService.delete(NetworkId("test network"), id)(adminRequesterAcl)) thenReturn Future.successful(true)

      Delete(s"/simon/api/v2/networks/test%20network/product-nomenclature/$id", UpdateProductNomenclatureRequest("Product Name 1")) ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
      }
    }

    "leave GET requests to other paths unhandled" in {
      Get("/undefined") ~>
        routes ~>
        check {
          responseAs[String] shouldBe """The requested resource could not be found."""
        }
    }
  }
}
