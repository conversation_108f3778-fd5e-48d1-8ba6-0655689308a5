service: service-networks

provider:
  stage: ${self:custom.file.provider.environment}
  name: aws
  runtime: java11
  region: us-east-1
  profile: ${self:custom.file.provider.profile}
  versionFunctions: false
  deploymentBucket:
    name: simon-dev-local-serverless

custom:
  localstack:
    stages:
      - dev-local
    host: http://localstack-svc.${env:OKTETO_NAMESPACE}
    edgePort: 4566
    debug: true
  file: ${file(../config/okteto.yml)}
  newRelic:
    javaNewRelicHandler: handleStreamsRequest

plugins:
  - serverless-localstack
  - serverless-deployment-bucket


package:
  artifact: target/service-networks-uber.jar

functions:
  api:
    handler: com.simonmarkets.networks.NetworkServiceApp$EntryPoint$
    # memorySize: 2048
    timeout: 300
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=okteto.conf
  product-nomenclature-api:
    handler: com.simonmarkets.networks.ProductNomenclatureServiceApp$EntryPoint$
    # memorySize: 1024
    timeout: 300
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=okteto.conf
  entities-api:
    handler: com.simonmarkets.networks.NetworkEntitiesApp$EntryPoint$
    # memorySize: 1024
    timeout: 300
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=okteto.conf
