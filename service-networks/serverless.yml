service: service-networks

provider:
  stage: ${opt:stage, 'sandbox'}
  name: aws
  runtime: java11
  region: us-east-1
  profile: ${self:custom.file.provider.profile}
  iam:
    role: ${self:custom.file.provider.iam.role}
  versionFunctions: false
  tracing:
    lambda: true
  vpc:
    securityGroupIds: ${self:custom.file.provider.securityGroupIds}
    subnetIds: ${self:custom.file.provider.subnetIds}
  deploymentBucket:
    name: simon-${self:provider.profile}-serverless
  logRetentionInDays: ${self:custom.file.logRetentionInDays}

custom:
  file: ${file(../config/${opt:stage}.yml)}
  logSubscription: ${self:custom.file.logSubscription}
  newRelic:
    javaNewRelicHandler: handleStreamsRequest

plugins:
  - serverless-plugin-log-subscription

package:
  artifact: target/service-networks-uber.jar

functions:
  api:
    handler: com.simonmarkets.networks.NetworkServiceApp$EntryPoint$
    memorySize: 2048
    timeout: 60
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=${opt:stage}.conf ${self:custom.file.proxySystemProperty, ''}
  product-nomenclature-api:
    handler: com.simonmarkets.networks.ProductNomenclatureServiceApp$EntryPoint$
    memorySize: 1024
    timeout: 60
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=${opt:stage}.conf ${self:custom.file.proxySystemProperty, ''}
  entities-api:
    handler: com.simonmarkets.networks.NetworkEntitiesApp$EntryPoint$
    memorySize: 1024
    timeout: 60
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=${opt:stage}.conf ${self:custom.file.proxySystemProperty, ''}