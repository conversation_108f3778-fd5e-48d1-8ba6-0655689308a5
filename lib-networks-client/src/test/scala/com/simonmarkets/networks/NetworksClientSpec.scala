package com.simonmarkets.networks

import akka.http.scaladsl.model.Uri.Query
import akka.http.scaladsl.model.{HttpHeader, <PERSON><PERSON>}
import akka.http.scaladsl.testkit.ScalatestRouteTest
import com.simonmarkets.http.retry.RetryStrategy
import com.simonmarkets.http.{FutureHttpClient, HttpDecoder}
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.api.Page
import org.mockito.ArgumentMatchers
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{reset, when}
import org.scalatest.Matchers._
import org.scalatest.concurrent.ScalaFutures.convertScalaFuture
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach}
import simon.Id.NetworkId

import scala.concurrent.Future

class NetworksClientSpec extends AsyncWordSpec with MockitoSugar with ScalatestRouteTest with BeforeAndAfterEach {

  private val httpClient: FutureHttpClient = mock[FutureHttpClient]
  private val networksClient: NetworksClient = new HttpNetworksClient(httpClient, "https://dev.simonmarkets.com/simon/api")

  override protected def afterEach(): Unit = {
    reset(httpClient)
    super.afterEach()
  }

  private implicit val traceId: TraceId = TraceId("NetworksClientSpec")
  private val networkDTO1 = NetworkDTO(NetworkId("networkId 1"), "networkDTO1", Set.empty, None, Set.empty, Seq.empty, Seq.empty)
  private val networkDTO2 = NetworkDTO(NetworkId("networkId 2"), "networkDTO2", Set.empty, None, Set.empty, Seq.empty, Seq.empty)
  private val networkDTO3 = NetworkDTO(NetworkId("networkId 3"), "networkDTO3", Set.empty, None, Set.empty, Seq.empty, Seq.empty)

  "NetworksClient" when {
    "getByExternalId" should {
      "not throw StackOverflow error" in {
        when(httpClient.get(any[Uri], any[List[HttpHeader]])(any[HttpDecoder[NetworkDTO]], any[TraceId], any[RetryStrategy], any[FutureHttpClient.RequestTag]))
          .thenReturn(Future.successful(networkDTO1))

        networksClient.getByExternalId("externalId", "subject").map(_ => succeed)
      }
    }

    "streamAll" should {
      "create a url with the correct parameters" in {
        val page = Page(total = 2, count = 2, next = None, result = List(networkDTO1, networkDTO2))

        val baseQuery = Query(
          Map(
            "limit" -> "30",
            "ids" -> s"${networkDTO1.id},${networkDTO2.id}"
          )
        )

        val uri = Uri(s"https://dev.simonmarkets.com/simon/api/v2/networks").withQuery(baseQuery)

        when(httpClient.get[Page[NetworkDTO]](ArgumentMatchers.eq(uri), any[List[HttpHeader]])(any[HttpDecoder[Page[NetworkDTO]]], any[TraceId], any[RetryStrategy], any[FutureHttpClient.RequestTag]))
          .thenReturn(Future.successful(page))
        val result = networksClient.streamAll(ids = Some(Seq(networkDTO1.id, networkDTO2.id)))
          .runFold(Seq.empty[NetworkDTO])(_ ++ _).futureValue

        result.toList shouldBe List(networkDTO1, networkDTO2)
      }
      "return all results" in {
        val page1 = Page(total = 3, count = 1, next = Some("nextPageToken"), result = List(networkDTO1))
        val page2 = Page(total = 3, count = 1, next = Some("nextPageToken"), result = List(networkDTO2))
        val page3 = Page(total = 3, count = 1, next = None, result = List(networkDTO3))

        when(httpClient.get[Page[NetworkDTO]](any[Uri], any[List[HttpHeader]])(any[HttpDecoder[Page[NetworkDTO]]], any[TraceId], any[RetryStrategy], any[FutureHttpClient.RequestTag]))
          .thenReturn(Future.successful(page1), Future.successful(page2), Future.successful(page3))
        val result = networksClient.streamAll()
          .runFold(Seq.empty[NetworkDTO])(_ ++ _).futureValue

        result.toList shouldBe List(networkDTO1, networkDTO2, networkDTO3)
      }
      "return all unpaginated results" in {
        val page = Page(total = 2, count = 2, next = None, result = List(networkDTO1, networkDTO2))

        when(httpClient.get[Page[NetworkDTO]](any[Uri], any[List[HttpHeader]])(any[HttpDecoder[Page[NetworkDTO]]], any[TraceId], any[RetryStrategy], any[FutureHttpClient.RequestTag]))
          .thenReturn(Future.successful(page))
        val result = networksClient.streamAll()
          .runFold(Seq.empty[NetworkDTO])(_ ++ _).futureValue

        result.toList shouldBe List(networkDTO1, networkDTO2)
      }
      "return unpaginated results given ids" in {
        val page = Page(total = 2, count = 2, next = None, result = List(networkDTO1, networkDTO2))

        when(httpClient.get[Page[NetworkDTO]](any[Uri], any[List[HttpHeader]])(any[HttpDecoder[Page[NetworkDTO]]], any[TraceId], any[RetryStrategy], any[FutureHttpClient.RequestTag]))
          .thenReturn(Future.successful(page))
        val result = networksClient.streamAll(ids = Some(Seq(networkDTO1.id, networkDTO2.id)))
          .runFold(Seq.empty[NetworkDTO])(_ ++ _).futureValue

        result.toList shouldBe List(networkDTO1, networkDTO2)
      }
      "return all networks given ids on multiple pages" in {
        val page1 = Page(total = 2, count = 1, next = Some("nextPageToken"), result = List(networkDTO1))
        val page2 = Page(total = 2, count = 1, next = None, result = List(networkDTO2))

        when(httpClient.get[Page[NetworkDTO]](any[Uri], any[List[HttpHeader]])(any[HttpDecoder[Page[NetworkDTO]]], any[TraceId], any[RetryStrategy], any[FutureHttpClient.RequestTag]))
          .thenReturn(Future.successful(page1), Future.successful(page2))

        val result = networksClient.streamAll(ids = Some(Seq(networkDTO1.id))).runFold(Seq.empty[NetworkDTO])(_ ++ _).futureValue

        result.toList shouldBe List(networkDTO1, networkDTO2)
      }
      "return empty list if no matching networks are found" in {
        val page = Page(total = 2, count = 1, next = None, result = List.empty[NetworkDTO])

        when(httpClient.get[Page[NetworkDTO]](any[Uri], any[List[HttpHeader]])(any[HttpDecoder[Page[NetworkDTO]]], any[TraceId], any[RetryStrategy], any[FutureHttpClient.RequestTag]))
          .thenReturn(Future.successful(page))

        val result = networksClient.streamAll(ids = Some(Seq(networkDTO1.id))).runFold(Seq.empty[NetworkDTO])(_ ++ _).futureValue

        result.toList shouldBe List.empty
      }
    }
  }
}
