/**
 * For Okta + Twilio telephony integration
 * this is the code that sets up the Twilio Function
 * The function exposes an endpoint that <PERSON><PERSON> can hit to trigger an SMS / Voice MFA message authentication
 * Note: this code works as-is, modification should be unnecessary
 */

exports.handler = async function(context, event, callback) {
    try {
        // we are grabbing the secret using the VERIFY_SID as the key
        // note the key needs to be lower-cased due to how <PERSON><PERSON> sends it
        let key = context.VERIFY_SID.toString().toLowerCase();
        let requestSecret = event.request.headers[key];

        if (context.auth_secret !== requestSecret) {
            throw new Error("Authentication failed, secret does not match");
        }

        let client = context.getTwilioClient();

        // https://developer.okta.com/docs/reference/telephony-hook/#data-messageprofile
        let to = event.data.messageProfile.phoneNumber;
        let customCode = event.data.messageProfile.otpCode;
        let channel =
            event.data.messageProfile.deliveryChannel.toLowerCase() === "sms" ?
                "sms" :
                "call";

        let verification = await client.verify.v2
            .services(context.VERIFY_SID)
            .verifications.create({
                to,
                channel,
                customCode
            });

        console.log(verification);
        console.log(verification.sendCodeAttempts);

        let response = {
            commands: [{
                type: "com.okta.telephony.action",
                value: [{
                    status: "SUCCESSFUL",
                    provider: "Twilio Verify",
                    transactionId: verification.sid,
                    transactionMetadata: verification.sendCodeAttempts.at(-1).attempt_sid,
                }],
            }],
        };

        return callback(null, response);
    } catch (error) {
        console.error("Error: " + error);
        let errorResponse = {
            error: {
                errorSummary: error.message,
                errorCauses: [{
                    errorSummary: error.status || error.message,
                    reason: error.moreInfo || error.message,
                }],
            },
        };
        return callback(null, errorResponse);
    }
};
