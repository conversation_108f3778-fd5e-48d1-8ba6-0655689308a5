package com.simonmarkets.okta.client.api.factors

import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.okta.domain.{FactorType, FactorProfileProperty}
import io.circe.Encoder
import io.circe.generic.semiauto.deriveEncoder


case class EnrollFactorRequest(
    factorType: FactorType,
    profile: Map[FactorProfileProperty, String],
    provider: String,
)

object EnrollFactorRequest extends JsonCodecs {
  implicit val encoder: Encoder[EnrollFactorRequest] = deriveEncoder[EnrollFactorRequest]
  def apply(factorType: FactorType, profile: Map[FactorProfileProperty, String]): EnrollFactorRequest = {
    new EnrollFactorRequest(
      factorType,
      profile,
      provider = "OKTA"
    )
  }
}

