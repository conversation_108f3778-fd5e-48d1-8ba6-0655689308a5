package com.simonmarkets.okta.client.api

import akka.actor.ActorSystem
import akka.http.scaladsl.Http
import akka.http.scaladsl.model.headers.{Authorization, GenericHttpCredentials}
import akka.stream.Materializer
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.http.FutureHttpClient.Tag
import com.simonmarkets.http.authentication.NoAuthCredentials
import com.simonmarkets.http.{FutureHttpClient, HttpClientConfig, ProxySettings}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.okta.client.api.application.{CreateOAuthSecretRequest, CreateOAuthSecretResponse, GetOAuthSecretResponse, OAuthSecretStatus}
import com.simonmarkets.okta.client.api.factors.{EnrollFactorRequest, EnrollFactorResponse}
import com.simonmarkets.okta.config.OktaClientConfig

import scala.concurrent.{ExecutionContext, Future}


/**
 * Used to interact with Okta APIs not present via sdk
 */
trait OktaClient {
  /**
   * https://support.okta.com/help/s/article/okta-factors-api?language=en_US
   *
   * @param oktaUserId          user's okta id
   * @param enrollFactorRequest data to enroll the specified type of factor
   * @param traceId             trace
   * @return enroll factor response
   */
  def autoEnrollFactor(oktaUserId: String, enrollFactorRequest: EnrollFactorRequest)
    (implicit traceId: TraceId): Future[EnrollFactorResponse]

  def listApplicationSecrets(oktaApplicationId: String)
    (implicit traceId: TraceId): Future[List[GetOAuthSecretResponse]]

  def createApplicationSecret(oktaApplicationId: String)(implicit traceId: TraceId): Future[CreateOAuthSecretResponse]

  def activateApplicationSecret(oktaApplicationId: String, applicationSecretId: String)
    (implicit traceId: TraceId): Future[GetOAuthSecretResponse]

  def deactivateApplicationSecret(oktaApplicationId: String, applicationSecretId: String)
    (implicit traceId: TraceId): Future[GetOAuthSecretResponse]
}

case class HttpOktaClient(
    client: FutureHttpClient,
    basePath: String,
    apiToken: String,
)(implicit ex: ExecutionContext, mat: Materializer) extends OktaClient with JsonCodecs with TraceLogging {

  private implicit val requestTag: Tag = Tag("okta-client")
  private val authHeader = Authorization(GenericHttpCredentials("SSWS", apiToken))

  override def autoEnrollFactor(oktaUserId: String, enrollFactorRequest: EnrollFactorRequest)
    (implicit traceId: TraceId): Future[EnrollFactorResponse] = {
    client.post[EnrollFactorRequest, EnrollFactorResponse](
      s"$basePath/api/v1/users/$oktaUserId/factors?activate=true",
      enrollFactorRequest,
      List(authHeader)
    )
  }

  override def listApplicationSecrets(oktaApplicationId: String)
    (implicit traceId: TraceId): Future[List[GetOAuthSecretResponse]] = {
    client.get[List[GetOAuthSecretResponse]](
      s"$basePath/api/v1/apps/$oktaApplicationId/credentials/secrets",
      List(authHeader)
    )
  }

  override def createApplicationSecret(oktaApplicationId: String)
    (implicit traceId: TraceId): Future[CreateOAuthSecretResponse] = {
    client.post[CreateOAuthSecretRequest, CreateOAuthSecretResponse](
      s"$basePath/api/v1/apps/$oktaApplicationId/credentials/secrets",
      CreateOAuthSecretRequest(OAuthSecretStatus.ACTIVE),
      List(authHeader)
    )
  }

  override def activateApplicationSecret(oktaApplicationId: String,
      applicationSecretId: String)(implicit
      traceId: TraceId): Future[GetOAuthSecretResponse] =
    client.post[Unit, GetOAuthSecretResponse](
      s"$basePath/api/v1/apps/$oktaApplicationId/credentials/secrets/$applicationSecretId/lifecycle/activate",
      None,
      List(authHeader)
    )

  override def deactivateApplicationSecret(oktaApplicationId: String,
      applicationSecretId: String)(implicit
      traceId: TraceId): Future[GetOAuthSecretResponse] =
    client.post[Unit, GetOAuthSecretResponse](
      s"$basePath/api/v1/apps/$oktaApplicationId/credentials/secrets/$applicationSecretId/lifecycle/deactivate",
      None,
      List(authHeader)
    )
}

object HttpOktaClient {

  def apply(config: OktaClientConfig)
    (implicit ac: ActorSystem, ec: ExecutionContext): HttpOktaClient = {
    val proxyOpt = config.proxy.map { p =>
      ProxySettings(
        address = p.host,
        port = p.port,
        credentials = None
      )
    }
    new HttpOktaClient(
      client = new FutureHttpClient(Http(), HttpClientConfig(proxy = proxyOpt, auth = NoAuthCredentials)),
      basePath = config.oktaOrgUrl,
      apiToken = config.oktaAuthToken
    )
  }

}
