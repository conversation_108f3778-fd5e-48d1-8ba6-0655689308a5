package com.simonmarkets.okta.client.api.application

import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.util.{EnumEntry, SafeEnums}
import io.circe.Decoder
import io.circe.generic.semiauto.deriveDecoder
import io.simon.openapi.annotation.Field.EnumValues

import java.time.Instant

sealed trait BaseResponse {
  def id: String
  def created: Instant
  def lastUpdated: Instant
  def status: OAuthSecretStatus
}

@EnumValues("Active", "Inactive")
sealed trait OAuthSecretStatus extends EnumEntry

object OAuthSecretStatus extends SafeEnums[OAuthSecretStatus] {
  case object ACTIVE extends OAuthSecretStatus
  case object INACTIVE extends OAuthSecretStatus
  case object EnumNotFound extends OAuthSecretStatus

  override def Values: Seq[OAuthSecretStatus] = Seq(
    ACTIVE,
    INACTIVE,
    EnumNotFound
  )
}

case class GetOAuthSecretResponse(
    id: String,
    created: Instant,
    lastUpdated: Instant,
    status: OAuthSecretStatus
) extends BaseResponse

case class CreateOAuthSecretRequest(
    status: OAuthSecretStatus
)

case class CreateOAuthSecretResponse(
    id: String,
    created: Instant,
    lastUpdated: Instant,
    status: OAuthSecretStatus,
    client_secret: String
) extends BaseResponse

object OAuthSecretDecoders extends JsonCodecs {
  implicit val decodeListSecretsResponse: Decoder[GetOAuthSecretResponse] = deriveDecoder[GetOAuthSecretResponse]
  implicit val decodeCreateSecretResponse: Decoder[CreateOAuthSecretResponse] = deriveDecoder[CreateOAuthSecretResponse]
}
