package com.simonmarkets.okta.client.sdk

import com.okta.commons.http.config.Proxy
import com.okta.sdk.authc.credentials.TokenClientCredentials
import com.okta.sdk.cache.Caches
import com.okta.sdk.client.Clients
import com.okta.sdk.resource.client.ApiClient
import com.simonmarkets.okta.config.OktaClientConfig

import scala.concurrent.duration.Duration

object OktaClientFactory {

  def getOktaClient(
    oktaOrgUrl: String,
    oktaAuthToken: String,
    cacheTtl: Duration,
    proxy: Option[Proxy] = None
  ): ApiClient = {
    // fixme (abhi) - disabling caching temporarily https://github.com/okta/okta-sdk-java/issues/1600
    /*
     val cacheManager =
      Caches
        .newCacheManager
        .withCache(
          forResource(classOf[User]) // User-specific cache settings
            .withTimeToLive(cacheTtl.toSeconds, TimeUnit.SECONDS)
        )
        .build
     */
    val _ = cacheTtl

    val clientBuilder =
      Clients
        .builder()
        .setCacheManager(Caches.newDisabledCacheManager())
        .setOrgUrl(oktaOrgUrl)
        .setClientCredentials(new TokenClientCredentials(oktaAuthToken))

    proxy.foreach(clientBuilder.setProxy)
    clientBuilder.build()
  }

  def getOktaClient(clientConfig: OktaClientConfig): ApiClient =
    getOktaClient(
      clientConfig.oktaOrgUrl,
      clientConfig.oktaAuthToken,
      clientConfig.cacheTtl,
      clientConfig.proxy.map(_.toOktaProxy)
    )

}
