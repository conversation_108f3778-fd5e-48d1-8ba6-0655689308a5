package com.simonmarkets.okta.client.api.factors

import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.okta.domain.FactorType
import com.simonmarkets.util.{EnumEntry, SafeEnums}
import io.circe.Decoder
import io.circe.generic.semiauto.deriveDecoder
import io.simon.openapi.annotation.Field.EnumValues

import java.time.Instant

case class EnrollFactorResponse(
    id: String,
    factorType: FactorType,
    provider: String,
    vendorName: String,
    status: EnrollFactorStatus,
    created: Instant,
    lastUpdated: Instant,
    profile: Map[String, String]
)

@EnumValues(
  "ACTIVE",
  "PENDING_ACTIVATION"
)
sealed trait EnrollFactorStatus extends EnumEntry

object EnrollFactorStatus extends SafeEnums[EnrollFactorStatus] {
  case object ACTIVE extends EnrollFactorStatus

  case object PENDING_ACTIVATION extends EnrollFactorStatus

  case object EnumNotFound extends EnrollFactorStatus

  override def Values: Seq[EnrollFactorStatus] = Seq(
    ACTIVE,
    PENDING_ACTIVATION,
    EnumNotFound
  )
}

object EnrollFactorResponse extends JsonCodecs {
  implicit val decoder: Decoder[EnrollFactorResponse] = deriveDecoder[EnrollFactorResponse]
}
