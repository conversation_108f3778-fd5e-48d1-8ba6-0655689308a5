package com.simonmarkets.okta.testimpl

import com.okta.sdk.resource.model.UserProfile

import scala.collection.JavaConverters._

class UserProfileTestImpl(
    var email: String = null,
    var firstName: String = null,
    var lastName: String = null,
    var login: String = null,
    var secondEmail: String = "<EMAIL>",
    var customProperties: Map[String, Object] = Map()
) extends UserProfile {
  super.setAdditionalProperties(customProperties.asJava)

  override def getEmail: String = email

  override def getFirstName: String = firstName

  override def getLogin: String = login

  override def getMobilePhone: String = ???

  override def getSecondEmail: String = secondEmail

  override def getCity: String = ???

  override def getCostCenter: String = ???

  override def getCountryCode: String = ???

  override def getDepartment: String = ???

  override def getDisplayName: String = ???

  override def getDivision: String = ???

  override def getEmployeeNumber: String = ???

  override def getHonorificPrefix: String = ???

  override def getHonorificSuffix: String = ???

  override def getLocale: String = ???

  override def getManager: String = ???

  override def getManagerId: String = ???

  override def getMiddleName: String = ???

  override def getNickName: String = ???

  override def getOrganization: String = ???

  override def getPostalAddress: String = ???

  override def getPreferredLanguage: String = ???

  override def getPrimaryPhone: String = ???

  override def getProfileUrl: String = ???

  override def getState: String = ???

  override def getStreetAddress: String = ???

  override def getTimezone: String = ???

  override def getTitle: String = ???

  override def getUserType: String = ???
}
