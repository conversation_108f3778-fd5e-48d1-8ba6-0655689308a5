package com.simonmarkets.okta.testimpl

import com.okta.sdk.resource.client.ApiException
import com.simonmarkets.okta.NotFoundErrorCode

import scala.collection.JavaConverters._

/**
 * This class is an implementation of Okta's ApiException used strictly for testing.
 */
protected class OktaTestException(
  message: String,
  code: Int,
  responseHeaders: Map[String, List[String]],
  responseBody: String)
extends ApiException(message, code, responseHeaders.map {case (k, v) => k -> v.asJava}.asJava, responseBody)

object OktaTestException {
  def notFoundException: OktaTestException = new OktaTestException(
    message = "",
    code = 123,
    responseHeaders = Map.empty,
    responseBody =
      s"""{
         |"errorCode": "$NotFoundErrorCode",
         |"errorSummary": "errorSummary",
         |"errorId": "errorId"
         |}""".stripMargin
  )

  def notFoundExceptionBungledResponseBody: OktaTestException = new OktaTestException(
    "",
    123,
    Map.empty,
    "haha-funny∂å¬˚ß∂∆¬"
  )

  def notFoundExceptionUnexpectedResponseBodyToken: OktaTestException = new OktaTestException(
    message = "",
    code = 123,
    responseHeaders = Map.empty,
    responseBody =
      s"""{
         |"errorCode": "$NotFoundErrorCode",
         |"errorSummary": "errorSummary",
         |"errorId": "errorId",
         |"monkey": "orangutan"
         |}""".stripMargin
  )
}
