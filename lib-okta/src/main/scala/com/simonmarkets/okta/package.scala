package com.simonmarkets

import com.okta.sdk.resource.client.ApiException
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.okta.domain.OktaApiExceptionResponseBody
import io.circe.{Decoder, jawn}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Failure

/**
 * Package object that holds multiple utility methods.
 */
package object okta extends TraceLogging {

  // Okta Error Codes
  val NotFoundErrorCode = "E0000007"

  val RateLimitErrorCodes: Set[String] = Set("E0000047", "E0000151")

  // error code parsing
  def parseApiExceptionResponseBody(json: String)(implicit decoder: Decoder[OktaApiExceptionResponseBody])
  : Option[OktaApiExceptionResponseBody] = {
    jawn.decode[OktaApiExceptionResponseBody](json).toOption
  }

  def containsErrorCodes(t: ApiException, args: String*): Boolean = {
    parseApiExceptionResponseBody(t.getResponseBody)
      .flatMap(_.errorCode)
      .exists(args.contains(_))
  }

  def notFound(t: ApiException): Boolean = {
    containsErrorCodes(t, NotFoundErrorCode)
  }

  def rateLimited(t: ApiException): Boolean = {
    containsErrorCodes(t, RateLimitErrorCodes.toSeq:_*)
  }

  def handleNotFound[T]: PartialFunction[Throwable, Option[T]] = {
    case e: ApiException if notFound(e) => None
  }

  // futures with error logging
  def futureWithErrorLogging[T](body: => T, errorMsg: String, reason: String)
                               (implicit traceId: TraceId, ec: ExecutionContext): Future[T] = {
    Future {
      body
    }.andThen { case Failure(ex) => log.error(ex, s"${errorMsg} reason='${reason}'") }
  }

  def futureWithErrorLogging[T](body: => T, errorMsg: String, reasonCallback: Function[Throwable, String])
                               (implicit traceId: TraceId, ec: ExecutionContext): Future[T] = {
    Future {
      body
    }.andThen { case Failure(ex) => log.error(ex, s"${errorMsg} reason='${reasonCallback.apply(ex)}'") }
  }
}
