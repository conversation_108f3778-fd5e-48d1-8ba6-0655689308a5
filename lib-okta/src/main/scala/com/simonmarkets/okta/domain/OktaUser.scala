package com.simonmarkets.okta.domain

import com.simonmarkets.okta.BcryptHash

case class OktaUser(
    id: String,
    oktaId: Option[String],
    firstName: String,
    lastName: String,
    email: String,
    isActive: Boolean,
    network: String,
    customRoles: Set[String],
    faNumbers: Set[String],
    locations: Set[String],
    omsId: Option[String],
    distributorId: Option[String],
    tradewebEligible: Boolean,
    regSEligible: Boolean,
    roles: Set[String],
    licenses: Set[String],
    endpointCapabilities: Set[String],
    loginId: String,
    accountInContext: Option[String] = None,
    contextAccountId: Option[String] = None,
    contextProposalId: Option[String] = None,
    contextTaskId: Option[String] = None,
    contextServiceRequestId: Option[String] = None,
    contextLastUpdatedAt: Option[String] = None,
    externalIds: Map[String, String] = Map.empty,
    whiteLabelPartnerId: Option[String] = None,
    firmId: Option[String] = None,
    iCapitalUserId: Option[String] = None,
    secondaryEmail: Option[String] = None,
    password: Option[BcryptHash] = None
)
