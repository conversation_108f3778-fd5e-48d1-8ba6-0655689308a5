package com.simonmarkets.okta.domain

import io.circe.Decoder
import io.circe.generic.semiauto.deriveDecoder

/**
 * This case class is used to parse an Okta ApiException json string into a Scala class
 * Contains a decoder object to enable json -> object parsing.
 */
final case class OktaApiExceptionResponseBody(
  errorCode: Option[String],
  errorSummary: Option[String],
  errorId: Option[String]
)

object OktaApiExceptionResponseBody {
  implicit val decoder: Decoder[OktaApiExceptionResponseBody] = deriveDecoder[OktaApiExceptionResponseBody]
}
