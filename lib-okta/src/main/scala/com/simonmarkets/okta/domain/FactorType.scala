package com.simonmarkets.okta.domain

import com.simonmarkets.util.{EnumEntry, SafeEnums}
import io.circe.Decoder
import io.circe.generic.semiauto.deriveDecoder
import io.simon.openapi.annotation.Field.EnumValues

@EnumValues(
  "call",
  "email",
  "push",
  "question",
  "sms",
  "token:hardware",
  "token:hotp",
  "token:software:totp",
  "token",
  "u2f",
  "web",
  "webauthn",
  "EnumNotFound"
)
sealed trait FactorType extends EnumEntry

object FactorType extends SafeEnums[FactorType] {
  case object call extends FactorType

  case object email extends FactorType

  case object push extends FactorType

  case object question extends FactorType

  case object sms extends FactorType

  case object `token:hardware` extends FactorType

  case object `token:hotpcall` extends FactorType

  case object `token:software:totp` extends FactorType

  case object token extends FactorType

  case object u2f extends FactorType

  case object web extends FactorType

  case object webauthn extends FactorType

  case object EnumNotFound extends FactorType

  override def Values: Seq[FactorType] = Seq(
    call,
    email,
    push,
    question,
    sms,
    `token:hardware`,
    `token:hotpcall`,
    `token:software:totp`,
    token,
    u2f,
    web,
    webauthn,
    EnumNotFound
  )

  implicit val decoder: Decoder[FactorType] = deriveDecoder[FactorType]
}

@EnumValues(
  "phoneNumber",
  "email",
  "EnumNotFound"
)
sealed trait FactorProfileProperty extends EnumEntry

object FactorProfileProperty extends SafeEnums[FactorProfileProperty] {
  case object phoneNumber extends FactorProfileProperty
  case object email extends FactorProfileProperty
  case object EnumNotFound extends FactorProfileProperty

  override def Values: Seq[FactorProfileProperty] = Seq(
    phoneNumber,
    email,
    EnumNotFound,
  )
}
