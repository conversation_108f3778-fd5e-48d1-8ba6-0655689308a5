package com.simonmarkets.okta.domain

import com.okta.sdk.resource.model._

import scala.collection.JavaConverters._


case class UserProfileProperty(name: String, title: String, description: String) {

  /**
   * {
   * "definitions": {
   * "custom": {
   * "id": "#custom",
   * "type": "object",
   * "properties": {
   * "$this.name": {
   * "title": "title",
   * "description": "description",
   * "type": "string",
   * "required": false,
   * "permissions": [
   * {
   * "principal": "SELF",
   * "action": "READ_ONLY"
   * }
   * ],
   * // john added this, it make okta the profile master
   * "master": {
   * "type": "OKTA"
   * }
   * }
   * },
   * "required": []
   * }
   * }
   * }
   */
  def toUserSchema: UserSchema = {
    new UserSchema()
      .definitions(
        new UserSchemaDefinitions()
          .custom(
            new UserSchemaPublic()
              .id("#custom")
              .`type`("object")
              .properties(
                Map(
                  name ->
                    new UserSchemaAttribute()
                      .title(title)
                      .description(description)
                      .`type`(UserSchemaAttributeType.STRING)
                      .required(false)
                      .permissions(
                        List(
                          new UserSchemaAttributePermission()
                            .principal("SELF")
                            .action("READ_ONLY")).asJava)
                      .master(
                        new UserSchemaAttributeMaster()
                          .`type`(UserSchemaAttributeMasterType.OKTA))
                ).asJava)))
  }
}
