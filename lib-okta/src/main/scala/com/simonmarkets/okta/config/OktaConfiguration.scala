package com.simonmarkets.okta.config

import com.okta.commons.http.config.Proxy
import scala.concurrent.duration.Duration

case class OktaConfiguration(
    clientConfig: OktaClientConfig,
    serviceConfig: OktaServiceConfig,
    enabled: Boolean
)

case class OktaServiceCreateConfig(
    activate: Boolean = true,
    setPassword: Boolean = true,
    expirePassword: Boolean = false
)

case class OktaProfileConfig(
    networkName: String = "network",
    skipSecondaryEmails: Set[String] = Set(),
    secondaryEmail: String = ""
)

case class OktaServiceConfig(create: OktaServiceCreateConfig = OktaServiceCreateConfig(),
    profile: OktaProfileConfig = OktaProfileConfig())

case class OktaClientConfig(
    oktaOrgUrl: String,
    oktaAuthToken: String,
    cacheTtl: Duration,
    proxy: Option[ProxySettings] = None
)

case class ProxySettings(host: String, port: Int) {
  def toOktaProxy: Proxy = new Proxy(host, port)
}