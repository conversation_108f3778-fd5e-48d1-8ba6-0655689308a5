package com.simonmarkets.okta.service

import akka.actor.ActorSystem
import akka.pattern.after
import com.okta.sdk.resource.model.{Group, GroupRule, UpdateUserRequest, UserNextLogin, UserProfile, UserStatus, User => OktaSdkUser}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.okta.client.api.application.{CreateOAuthSecretResponse, GetOAuthSecretResponse}
import com.simonmarkets.okta.client.api.factors.EnrollFactorRequest
import com.simonmarkets.okta.config.OktaServiceConfig
import com.simonmarkets.okta.domain._
import com.simonmarkets.okta.service.OktaService.SimonIdConst

import java.lang
import java.security.SecureRandom
import java.util.Base64

import scala.collection.JavaConverters._
import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}

/**
 * Service layer on top of okta clients [[OktaRepository]] to support business logic
 */
trait OktaService extends TraceLogging {

  def addBaseProfileAttribute(property: UserProfileProperty)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit]

  def getUserByOktaId(id: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Option[OktaSdkUser]]

  def getUserBySimonId(id: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Option[OktaSdkUser]]

  def upsertUser(user: OktaUser, includeDeactivated: Boolean = false)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[OktaSdkUser]

  def activateUser(user: OktaSdkUser, sendEmail: Boolean = true)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit]

  def deactivateUser(user: OktaSdkUser)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit]

  /**
   * Either
   *   1. directly reset password - if user exists and has set their recovery question
   *   1. reset, deactivate, activate - if user has not set their recovery. All users are set with a password to begin
   *   with to support SSO non-jit users. May be modified in future
   * @param user to reset
   */
  def resetPassword(user: OktaSdkUser)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit]

  def createGroup(name: String, description: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Group]

  def upsertGroup(oldName: String, newName: String, description: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Group]

  def createGroupRule(rule: OktaGroupRule, activate: Boolean = true)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[GroupRule]

  def upsertGroupRule(rule: OktaGroupRule, activate: Boolean = true)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[GroupRule]

  def getNetworkFieldName: String

  def getSimonId(user: OktaSdkUser): Option[String]

  def createApplication(appName: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[UserInfo]

  def listApplicationSecrets(appId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[List[GetOAuthSecretResponse]]

  def createApplicationSecret(appId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[CreateOAuthSecretResponse]

  def activateApplicationSecret(appId: String, secretId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[GetOAuthSecretResponse]

  def deactivateApplicationSecret(appId: String, secretId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[GetOAuthSecretResponse]

  def addUserToGroup(groupId: String, simonId: String, oktaIdOpt: Option[String])
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit]

  def removeUserFromGroup(groupId: String, simonId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit]

  def deleteFactors(factorTypes: Set[String], oktaId: String, deleteAll: Boolean)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit]

  def enrollFactors(userId: String, user: OktaSdkUser, factors: Map[String, Factor])
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Map[String, Factor]]
}

class OktaServiceImpl(
    config: OktaServiceConfig,
    repository: OktaRepository
)(implicit ac: ActorSystem) extends OktaService {

  override def addBaseProfileAttribute(property: UserProfileProperty)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = {
    log.info("Create new profile property on base profile", property)
    repository.addProfileAttribute(property).map(_ => log.info("External Id Type sync to okta completed"))
  }

  override def getUserByOktaId(id: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Option[OktaSdkUser]] =
    repository.getUserByOktaId(oktaId = id)

  override def getUserBySimonId(id: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Option[OktaSdkUser]] =
    repository.getUserBySimonId(id)

  /**
   * Creates the user if not found in Okta by email or simonId
   * If the user is found, updates its properties in Okta
   *
   * @param user     internal representation of okta user
   * @param activate if set to true, the user will have 'Active' status and a welcome email will be sent to him.
   * @return
   */
  override def upsertUser(
      user: OktaUser,
      includeDeactivated: Boolean = false,
  )(implicit traceId: TraceId, ec: ExecutionContext): Future[OktaSdkUser] = {
    log.info(s"Upsert user", user.id)
    repository.getUser(user).flatMap {
      case Some(initialUser) if user.password.isDefined && initialUser.getStatus != UserStatus.STAGED =>
        //user passwords can only be set in "staged" state, so recreate the user if needed
        deleteUser(initialUser, user).flatMap { _ =>
          createUser(
            user = user.copy(
              oktaId = None
            ),
            activate = user.isActive
          )
        }
      case Some(initialUser) => updateUser(initialUser, user, includeDeactivated)
      case None => createUser(user, activate = config.create.activate)
    }
  }

  /**
   * Approximates synchronous deletion. Okta delete is normally async, so this will await until deletion is confirmed
   */
  private def deleteUser(oktaUser: OktaSdkUser, user: OktaUser)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = {

    def exists(tries: Int): Future[Boolean] = {
      for {
        stillExists <- repository.getUser(user).map(_.isDefined)
        result <-
          if (tries > 0 && stillExists) after(1.second)(exists(tries - 1))
          else Future.successful(stillExists)
      } yield result
    }

    for {
      _ <- repository.deactivateUser(oktaUser)
      _ <- repository.deleteUser(oktaUser)
      stillExists <- exists(2)
    } yield {
      if (stillExists) {
        log.error("okta user deletion for password reset failed", "simonId" -> user.id, "oktaId" -> user.oktaId)
        Future.failed(OktaError.Internal("user deletion failed"))
      } else ()
    }
  }

  /**
   * Sync the 'localUser' state to the 'remoteUser'
   *
   * If a password is provided, the user is assumed to be in a staged state, otherwise this will fail at runtime
   *
   * @param remoteUser         okta user
   * @param localUser          representation of internal user state
   * @param includeDeactivated whether to modify users in deactivated state
   * @return updated remote user
   */
  private def updateUser(
      remoteUser: OktaSdkUser,
      localUser: OktaUser,
      includeDeactivated: Boolean,
  )(implicit traceId: TraceId, ec: ExecutionContext): Future[OktaSdkUser] = {

    log.info(s"User found in okta, updating it", "simonId" -> localUser.id, "oktaId" -> remoteUser.getId,
      "userStatus" -> remoteUser.getStatus)

    val updateUserRequest = new UpdateUserRequest()
      .profile(new UserProfile()
        .login(localUser.loginId)
        .firstName(localUser.firstName)
        .lastName(localUser.lastName)
        .email(localUser.email)
      )
      .credentials(remoteUser.getCredentials)

    updateUserRequest.getProfile.setAdditionalProperties(createUserProperties(localUser))

    localUser.password.foreach(hash => updateUserRequest.getCredentials.setPassword(repository.passwordBuilder(hash)))

    if (shouldSetSecondaryEmail(localUser)) {
      updateUserRequest.getProfile.setSecondEmail(config.profile.secondaryEmail)
    }

    if (includeDeactivated && remoteUser.getStatus == UserStatus.DEPROVISIONED) {
      val updateAttempt = for {
        _ <- repository.activateUser(remoteUser, sendEmail = false)
        _ <- repository.updateUser(remoteUser.getId, updateUserRequest)
        _ <- repository.deactivateUser(remoteUser)
      } yield remoteUser

      updateAttempt.recoverWith {
        // User should be deactivated if the activation was successful but the update failed
        case ex =>
          repository.deactivateUser(remoteUser)
          throw ex
      }
    } else repository.updateUser(remoteUser.getId, updateUserRequest)
  }

  /**
   * Creates a user in Okta and sets the custom properties for it. Does not validate existence of user
   *
   * @param user representation to build the user
   */
  private def createUser(user: OktaUser, activate: Boolean)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[OktaSdkUser] = {

    log.info(s"Creating user in okta for simonId='${user.id}'")
    val builder = repository.userBuilder
      .setLogin(user.loginId)
      .setFirstName(user.firstName)
      .setLastName(user.lastName)
      .setEmail(user.email)
      .setActive(activate)

    createUserProperties(user)
      .forEach((k, v) => builder.setCustomProfileProperty(k, v))

    (user.password, config.create.setPassword) match {
      case (Some(hash), _) => builder.setBcryptPasswordHash(hash.hashedPassword, hash.salt, hash.workFactor)
      case (None, true) =>
        // Add a prefix to guarantee conformance to password requirements
        val tmpPassword = "Aa1" + {
          val bytes = new Array[Byte](20)
          new SecureRandom().nextBytes(bytes)
          Base64.getUrlEncoder.withoutPadding.encodeToString(bytes)
        }
        builder.setPassword(tmpPassword.toCharArray)
        if (config.create.expirePassword) {
          builder.setNextLogin(UserNextLogin.CHANGEPASSWORD)
        }
      case (None, false) =>
        if (shouldSetSecondaryEmail(user)) {
          builder.setSecondEmail(config.profile.secondaryEmail)
        }
    }

    repository.createUser(builder)
  }

  override def activateUser(user: OktaSdkUser, sendEmail: Boolean = true)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = {
    log.info(s"Activating user in Okta", "oktaStatus" -> user.getStatus, "oktaId" -> user.getId, sendEmail)
    repository.activateUser(user, sendEmail = sendEmail)
  }

  override def deactivateUser(user: OktaSdkUser)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = {
    log.info(s"Deactivating user in Okta", "oktaStatus" -> user.getStatus, "oktaId" -> user.getId)
    repository.deactivateUser(user)
  }

  override def resetPassword(user: OktaSdkUser)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = {
    val isFirstActivation = user.getCredentials.getRecoveryQuestion == null
    if (isFirstActivation) {
      log.info(s"Resetting password via deactivate and activate flow", "oktaId" -> user.getId)
      for {
        _ <- repository.resetPassword(user, sendEmail = false)
        _ <- repository.deactivateUser(user)
        _ <- repository.activateUser(user, sendEmail = true)
      } yield ()
    } else repository.resetPassword(user, sendEmail = true)
  }

  def createGroup(name: String, description: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Group] = {
    repository.createGroup(name, description)
  }

  def upsertGroup(oldName: String, newName: String, description: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Group] =
    for {
      searchResult <- repository.searchGroups(oldName)
      group <- searchResult.find(_.getProfile.getName.trim.equalsIgnoreCase(oldName.trim)) match {
        case Some(group) =>
          if (group.getProfile.getDescription != description) {
            group.getProfile.setName(newName)
            group.getProfile.setDescription(description)
            repository.updateGroup(group)
          } else Future.successful(group)
        case None => repository.createGroup(newName, description)
      }
    } yield group

  override def createGroupRule(rule: OktaGroupRule, activate: Boolean = true)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[GroupRule] =
    repository.createGroupRule(rule, activate)

  override def upsertGroupRule(rule: OktaGroupRule, activate: Boolean = true)
    (implicit tid: TraceId, ec: ExecutionContext): Future[GroupRule] = {
    for {
      searchResult <- repository.searchGroupRules(rule.name)
      result <- searchResult.find(r => r.getName == rule.name) match {
        case Some(groupRule) => repository.updateGroupRule(groupRule, rule, activate)
        case None => repository.createGroupRule(rule, activate)
      }
    } yield result
  }

  private def createUserProperties(user: OktaUser): java.util.Map[java.lang.String, Object] = {
    val userAttributes = Map(
      config.profile.networkName -> user.network,
      "customRoles" -> user.customRoles.asJava,
      "locations" -> user.locations.asJava,
      "faNumbers" -> user.faNumbers.asJava,
      "omsId" -> user.omsId.getOrElse(""),
      "clientId" -> user.distributorId.getOrElse(""),
      "clientIdMatchAgainst" -> user.distributorId.map(clientId => s"${user.network.replace(' ', '-')}:${clientId}").getOrElse(""),
      "simonId" -> user.id,
      "regSEligible" -> lang.Boolean.valueOf(user.regSEligible),
      "userRoles" -> user.roles.asJava,
      "licenses" -> user.licenses.asJava,
      "endpointCapabilities" -> user.endpointCapabilities.asJava,
      "tradewebEligible" -> lang.Boolean.valueOf(user.tradewebEligible),
      "accountInContext" -> user.accountInContext.getOrElse(""),
      "contextAccountId" -> user.contextAccountId.getOrElse(""),
      "contextProposalId" -> user.contextProposalId.getOrElse(""),
      "contextTaskId" -> user.contextTaskId.getOrElse(""),
      "contextServiceRequestId" -> user.contextServiceRequestId.getOrElse(""),
      "contextLastUpdatedAt" -> user.contextLastUpdatedAt.getOrElse(""),
      "whiteLabelPartnerId" -> user.whiteLabelPartnerId.getOrElse(""),
      "firmId" -> user.firmId.getOrElse(""),
      "iCapitalUserId" -> user.iCapitalUserId.getOrElse(""),
      "secondaryEmail" -> user.secondaryEmail.getOrElse("")
    )

    (userAttributes ++ user.externalIds).asJava
  }

  def getNetworkFieldName: String = {
    config.profile.networkName
  }

  override def getSimonId(user: OktaSdkUser): Option[String] = {
    user.getProfile.getAdditionalProperties.asScala.get(SimonIdConst).flatMap(id => stringToOpt(id.asInstanceOf[String]))
  }

  private def stringToOpt: PartialFunction[String, Option[String]] = {
    case "" => None
    case nonEmpty => Some(nonEmpty)
  }

  override def createApplication(appName: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[UserInfo] = {
    log.info(s"Creating Application in okta with App Integration Name='$appName'")
    for {
      oktaApp <- repository.createApplication(appName)
      userId = oktaApp.getId
      oauthClient = oktaApp.getCredentials.getOauthClient
      clientSecret = Option(oauthClient.getClientSecret)
      userInfo = UserInfo(userId, clientSecret)
    } yield userInfo
  }

  override def listApplicationSecrets(appId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[List[GetOAuthSecretResponse]] = {
    log.info(s"Retrieving Application secrets in Okta with AppId=$appId")

    repository.listApplicationSecrets(appId)
  }

  override def createApplicationSecret(appId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[CreateOAuthSecretResponse] = {
    log.info(s"Creating secret in Okta for application AppId=$appId")

    repository.createApplicationSecret(appId)
  }

  override def activateApplicationSecret(appId: String, secretId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[GetOAuthSecretResponse] = {
    log.info(s"Activating secret secretId=$secretId in Okta for application AppId=$appId")

    repository.activateApplicationSecret(appId, secretId)
  }

  override def deactivateApplicationSecret(appId: String, secretId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[GetOAuthSecretResponse] = {
    log.info(s"Deactivating secret secretId=$secretId in Okta for application AppId=$appId")

    repository.deactivateApplicationSecret(appId, secretId)
  }

  override def addUserToGroup(groupId: String, simonId: String, oktaIdOpt: Option[String])
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = {
    for {
      oktaUser <-
        oktaIdOpt match {
          case Some(oktaId) =>
            log.info(s"Getting user by oktaId: $oktaId.")
            getUserByOktaId(oktaId)
          case None =>
            log.info(s"Getting user by simonId: $simonId.")
            getUserBySimonId(simonId)
        }
    } yield {
      log.info(s"Got user before adding user to MFA: $oktaUser.")
      oktaUser.foreach(user => repository.addGroupToUser(user, groupId))
    }
  }

  override def removeUserFromGroup(groupId: String, simonId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = {
    for {
      group <- repository.getGroup(groupId)
      oktaUser <- getUserBySimonId(simonId)
    } yield {
      oktaUser.foreach(user => repository.removeUserFromGroup(user.getId, group))
    }
  }

  override def deleteFactors(factorTypes: Set[String], oktaId: String, deleteAll: Boolean)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = {
    repository.listUserFactors(oktaId)
      .map { list =>
        if (deleteAll) list else list.filter(factor => factorTypes.contains(factor.getFactorType.toString))
      }
      .flatMap { list =>
        Future.traverse(list)(factor => repository.deleteUserFactor(userId = oktaId, factorId = factor.getId))
      }
      .map(_ => ())
  }

  override def enrollFactors(userId: String, oktaSdkUser: OktaSdkUser, factors: Map[String, Factor])
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Map[String, Factor]] = {
    val oktaId = oktaSdkUser.getId
    Future.sequence {
      factors.map { case (factorType, factor) =>
        (FactorType(factorType), factor.entry, factor.isFactorEnrolled) match {
          case (FactorType.sms, Some(factorVal), false) =>
            repository
              .enrollFactor(
                userId,
                oktaId,
                EnrollFactorRequest(
                  factorType = FactorType.sms,
                  profile = Map(FactorProfileProperty.phoneNumber -> factorVal)
                )
              )
              .map(enrolled => factorType -> factor.copy(isFactorEnrolled = enrolled))
          case (_, Some(_), true) =>
            log.info(s"factorType: $factorType has already been enrolled, userId=$userId, oktaId=$oktaId")
            Future(factorType -> factor)
          case (_, _, _) =>
            log.error(s"factorType: $factorType is unsupported and will not be enrolled. userId=$userId, oktaId=$oktaId")
            Future(factorType -> factor)
        }
      }
    }.map(_.toMap)
  }

  private def shouldSetSecondaryEmail(user: OktaUser) = {
    !config.profile.skipSecondaryEmails.exists(user.email.contains)
  }

}

object OktaService {

  val SimonIdConst = "simonId"

  def apply(
      config: OktaServiceConfig,
      repository: OktaRepository
  )(implicit ac: ActorSystem): OktaService = new OktaServiceImpl(config, repository)

}

/** Use this for a local scenario where you don't want okta to be called
 * For most cases, this means returning a `NotImplementedError` inside a Future
 */
object OktaServiceStub extends OktaService {
  val fail = Future.failed(new NotImplementedError)

  override def addBaseProfileAttribute(property: UserProfileProperty)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = Future.unit

  override def upsertUser(user: OktaUser, includeDeactivated: Boolean)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[OktaSdkUser] = fail

  override def activateUser(user: OktaSdkUser, sendEmail: Boolean)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = fail

  override def deactivateUser(user: OktaSdkUser)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = fail

  override def createGroup(name: String, description: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Group] = fail

  override def upsertGroup(oldName: String, newName: String, description: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Group] = fail

  override def createGroupRule(rule: OktaGroupRule, activate: Boolean)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[GroupRule] = fail

  override def upsertGroupRule(rule: OktaGroupRule, activate: Boolean)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[GroupRule] = fail

  override def getNetworkFieldName: String = ""

  override def getSimonId(user: OktaSdkUser): Option[String] = None

  override def createApplication(appName: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[UserInfo] = fail

  override def listApplicationSecrets(appId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[List[GetOAuthSecretResponse]] = fail

  override def createApplicationSecret(appId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[CreateOAuthSecretResponse] = fail

  override def activateApplicationSecret(appId: String, secretId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[GetOAuthSecretResponse] = fail

  override def deactivateApplicationSecret(appId: String, secretId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[GetOAuthSecretResponse] = fail

  override def removeUserFromGroup(groupId: String, simonId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = fail

  override def deleteFactors(factorTypes: Set[String], oktaId: String, deleteAll: Boolean)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = fail

  override def enrollFactors(userId: String, oktaSdkUser: OktaSdkUser, factors: Map[String, Factor])
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Map[String, Factor]] = fail

  override def addUserToGroup(groupId: String, simonId: String, oktaIdOpt: Option[String])
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = fail

  override def getUserByOktaId(id: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Option[OktaSdkUser]] = fail

  override def getUserBySimonId(id: String)(implicit traceId: TraceId,
      ec: ExecutionContext): Future[Option[OktaSdkUser]] = fail

  override def resetPassword(user: OktaSdkUser)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = fail
}
