package com.simonmarkets.okta.service

import com.okta.sdk.resource.api._
import com.okta.sdk.resource.client.ApiClient
import com.okta.sdk.resource.group.GroupBuilder
import com.okta.sdk.resource.model._
import com.okta.sdk.resource.user._
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.okta
import com.simonmarkets.okta.client.api.HttpOktaClient
import com.simonmarkets.okta.client.api.application.{CreateOAuthSecretResponse, GetOAuthSecretResponse}
import com.simonmarkets.okta.client.api.factors.{EnrollFactorRequest, EnrollFactorStatus}
import com.simonmarkets.okta.client.sdk.OktaClientFactory
import com.simonmarkets.okta.config.OktaClientConfig
import com.simonmarkets.okta.domain.{OktaUser, UserProfileProperty}
import com.simonmarkets.okta.{BcryptHash, futureWithErrorLogging}

import scala.collection.JavaConverters._
import scala.concurrent.{ExecutionContext, Future}

/**
 * Contains the minimum logic to update okta stateful elements. Should only be accessed thru OktaService layer
 */
trait OktaRepository {

  ////////// user operations \\\\\\\\\\

  /**
   * Retrieve user with fall thru logic over okta id, internal user id, loginId
   */
  def getUser(user: OktaUser)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Option[User]]

  def getUserByOktaId(oktaId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Option[User]]

  def getUserBySimonId(id: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Option[User]]

  def createUser(builder: UserBuilder)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[User]

  def updateUser(user: User)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[User]

  def updateUser(userId: String, updateUserRequest: UpdateUserRequest)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[User]

  def activateUser(user: User, sendEmail: Boolean)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit]

  def deactivateUser(user: User)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit]

  def deleteUser(user: User)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit]

  def resetPassword(oktaUser: User, sendEmail: Boolean)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit]

  def passwordBuilder(password: BcryptHash): PasswordCredential

  def userBuilder: UserBuilder

  def enrollFactor(userId: String, oktaId: String, enrollFactorRequest: EnrollFactorRequest)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Boolean]
  ////////// user operations \\\\\\\\\\

  ////////// user factor operations \\\\\\\\\\
  def listUserFactors(userId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[List[UserFactor]]

  def deleteUserFactor(userId: String, factorId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit]
  ////////// user factor operations \\\\\\\\\\

  ////////// application operations \\\\\\\\\\
  def createApplication(appName: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[OpenIdConnectApplication]

  def listApplicationSecrets(appId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[List[GetOAuthSecretResponse]]

  def createApplicationSecret(appId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[CreateOAuthSecretResponse]

  def activateApplicationSecret(appId: String, secretId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[GetOAuthSecretResponse]

  def deactivateApplicationSecret(appId: String, secretId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[GetOAuthSecretResponse]
  ////////// application operations \\\\\\\\\\

  ////////// user schema operations \\\\\\\\\\
  def addProfileAttribute(property: UserProfileProperty)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[UserSchema]
  ////////// user schema operations \\\\\\\\\\

  ////////// group operations \\\\\\\\\\
  def getGroup(id: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Group]

  def createGroup(name: String, description: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Group]

  def updateGroup(group: Group)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Group]

  def addGroupToUser(user: User, groupId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit]

  def removeUserFromGroup(oktaId: String, group: Group)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit]

  def searchGroups(name: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[List[Group]]
  ////////// group operations \\\\\\\\\\

  ////////// group rule operations \\\\\\\\\\
  def searchGroupRules(name: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[List[GroupRule]]

  def createGroupRule(rule: OktaGroupRule, activate: Boolean)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[GroupRule]

  def updateGroupRule(existingRule: GroupRule, ruleUpdate: OktaGroupRule, activate: Boolean)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[GroupRule]
  ////////// group rule operations \\\\\\\\\\

}

/**
 * Wrapper around SDK and HTTP client (where SDK not available) to support all OktaRepository semantics
 *
 * @param client       Okta SDK client
 * @param directClient Raw HTTP client for unsupported operations
 */
private[okta] case class OktaClientWrap(
    userApi: UserApi,
    userFactorApi: UserFactorApi,
    applicationApi: ApplicationApi,
    groupApi: GroupApi,
    schemaApi: SchemaApi,
    directClient: HttpOktaClient
) extends OktaRepository with TraceLogging {

  override def getUser(user: OktaUser)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Option[User]] = {
    user.oktaId match {
      case Some(oktaId) => getUserByOktaId(id = oktaId)
      case None => getUserBySimonId(user.id).flatMap {
        case None => getUserByLoginId(user.loginId)
        case some => Future.successful(some)
      }
    }
  }

  override def getUserByOktaId(id: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Option[User]] = {
    futureWithErrorLogging(
      Option(userApi.getUser(id)),
      s"Error while getting user by oktaId='$id'",
      "GET User by oktaId failed"
    ).recover(okta.handleNotFound)
  }

  override def getUserBySimonId(id: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Option[User]] = {
    futureWithErrorLogging(
      {
        log.info(s"Querying user by simonId='${id}'")

        val userList = userApi.listUsers(null, null, null, null, "profile.simonId eq \"" + id + "\"", null, null)

        if (userList.asScala.isEmpty) {
          log.info(s"User not found in Okta by simonId='${id}'")
          None
        } else if (userList.asScala.size > 1) {
          val errorMsg = s"Multiple users have the same simonId in Okta:" +
            s" ${userList.asScala.map(u => s"oktaId='${u.getId}'")}, simonId='${id}'" +
            s" reason='Get User by SimonID failed'"
          log.error(errorMsg)
          throw new Exception(errorMsg)
        } else {
          Some(userList.get(0))
        }
      }, s"Error while getting User by simonId='${id}'",
      "Get User by SimonID failed")
  }

  override def createUser(builder: UserBuilder)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[User] = {
    futureWithErrorLogging(
      builder.buildAndCreate(userApi),
      s"Couldn't create user in okta",
      "Create User failed"
    )
  }

  override def updateUser(user: User)
                         (implicit traceId: TraceId, ec: ExecutionContext): Future[User] = {
    val updateUserRequest = new UpdateUserRequest()
      .credentials(user.getCredentials)
      .profile(user.getProfile)
      .realmId(user.getRealmId)

    updateUser(user.getId, updateUserRequest)
  }

  override def updateUser(userId: String, updateUserRequest: UpdateUserRequest)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[User] = {

    futureWithErrorLogging(
      userApi.updateUser(userId, updateUserRequest, null),
      s"Couldn't update User with oktaId='$userId'",
      "User Update failed"
    )
  }

  override def activateUser(user: User, sendEmail: Boolean)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = {
    log.info("Activating User in Okta", "oktaId" -> user.getId)
    futureWithErrorLogging(
      userApi.activateUser(user.getId, sendEmail),
      s"Couldn't activate User with oktaId='${user.getId}'",
      "User Activate failed"
    )
  }

  override def deactivateUser(user: User)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = {
    log.info("Deactivating User in Okta", "oktaId" -> user.getId)
    if (user.getStatus != UserStatus.DEPROVISIONED) {
      futureWithErrorLogging(
        userApi.deactivateUser(user.getId, null),
        s"User Deactivation failed oktaId=${user.getId}, oktaStatus=${user.getStatus}", "Deactivate User failed")
    } else {
      log.warn("User already in deactivated state")
      Future.unit
    }
  }

  override def deleteUser(user: User)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = {
    log.info("Deleting user in Okta", "oktaId" -> user.getId)
    futureWithErrorLogging(
      userApi.deleteUser(user.getId, false),
      s"User deletion failed oktaId=${user.getId}, oktaStatus=${user.getStatus}", "Delete user failed")
  }

  override def resetPassword(oktaUser: User, sendEmail: Boolean)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = {
    log.info("Reset Okta Password", "oktaId" -> oktaUser.getId, sendEmail)
    futureWithErrorLogging(
      userApi.generateResetPasswordToken(oktaUser.getId, sendEmail, false),
      s"Exception while resetting password oktaId='${oktaUser.getId}",
      "Password reset failed"
    )
  }

  override def passwordBuilder(password: BcryptHash): PasswordCredential = {
    new PasswordCredential()
      .hash(
        new PasswordCredentialHash()
          .algorithm(PasswordCredentialHashAlgorithm.BCRYPT)
          .salt(password.salt)
          .workFactor(password.workFactor)
          .value(password.hashedPassword))
  }

  override def userBuilder: UserBuilder = UserBuilder.instance()

  override def enrollFactor(
      userId: String,
      oktaId: String,
      enrollFactorRequest: EnrollFactorRequest
  )
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Boolean] = {
    for {
      res <- directClient.autoEnrollFactor(oktaId, enrollFactorRequest)
      enrolled = if (res.status == EnrollFactorStatus.ACTIVE) {
        log.info(s"successfully enrolled user's ${enrollFactorRequest.factorType} factor", userId, oktaId)
        true
      } else {
        log.warn(s"unable to auto-enroll ${enrollFactorRequest.factorType}",
          userId,
          oktaId,
          "enrollmentStatus" -> res.status,
          "createdAt" -> res.created,
          "lastUpdatedAt" -> res.lastUpdated
        )
        false
      }
    } yield enrolled
  }

  override def listUserFactors(userId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[List[UserFactor]] = {
    futureWithErrorLogging(
      userFactorApi.listFactors(userId).asScala.toList,
      s"Exception while listing user factors for user='$userId'",
      "List User Factors failed"
    )
  }

  override def deleteUserFactor(userId: String, factorId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = {
    futureWithErrorLogging(
      userFactorApi.unenrollFactor(userId, factorId, false),
      s"Exception while deleting user factor for user='$userId' with factorId='$factorId'",
      "Deleting User Factors failed"
    )
  }

  override def createApplication(appName: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[OpenIdConnectApplication] = {
    log.info(s"Creating Application in okta with App Integration Name='$appName'")

    val applicationInstance = new OpenIdConnectApplication()
      .settings(
        new OpenIdConnectApplicationSettings()
          .oauthClient(
            new OpenIdConnectApplicationSettingsClient()
              .grantTypes(List(OAuthGrantType.CLIENT_CREDENTIALS).asJava)
              .applicationType(OpenIdConnectApplicationType.SERVICE)
              .dpopBoundAccessTokens(false)
          )
      )
      .credentials(
        new OAuthApplicationCredentials()
          .oauthClient(
            new ApplicationCredentialsOAuthClient()
              .tokenEndpointAuthMethod(OAuthEndpointAuthenticationMethod.CLIENT_SECRET_BASIC)
          )
      )
      .label(appName)
      .signOnMode(ApplicationSignOnMode.OPENID_CONNECT)

    futureWithErrorLogging(applicationApi
      .createApplication(applicationInstance, true, null)
      .asInstanceOf[OpenIdConnectApplication],
      s"Okta failed to create App: ${appName}",
      _.getMessage
    )
  }

  override def listApplicationSecrets(appId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[List[GetOAuthSecretResponse]] =
    directClient.listApplicationSecrets(appId)

  override def createApplicationSecret(appId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[CreateOAuthSecretResponse] =
    directClient.createApplicationSecret(appId)

  override def activateApplicationSecret(appId: String, secretId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[GetOAuthSecretResponse] =
    directClient.activateApplicationSecret(appId, secretId)

  override def deactivateApplicationSecret(appId: String, secretId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[GetOAuthSecretResponse] =
    directClient.deactivateApplicationSecret(appId, secretId)

  override def addProfileAttribute(property: UserProfileProperty)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[UserSchema] = {
    Future(schemaApi.updateUserProfile("default", property.toUserSchema))
      .recoverWith {
      case exception: Throwable =>
        log.error("External Id Type sync to Okta failed", exception.getMessage)
        Future.failed(exception)
    }
  }

  override def createGroup(name: String, description: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Group] = {
    futureWithErrorLogging(
      GroupBuilder.instance().setName(name).setDescription(description).buildAndCreate(groupApi),
      s"Error while creating Group with name='$name'",
      "Create Group failed"
    )
  }

  override def updateGroup(group: Group)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Group] = {
    futureWithErrorLogging(groupApi.replaceGroup(group.getId, group),
      s"Couldn't update groupId=${group.getId}", "Update Group failed")
  }

  override def addGroupToUser(user: User, groupId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] =
    futureWithErrorLogging(
      groupApi.assignUserToGroup(groupId, user.getId),
      s"Couldn't add user=${user.getId} to group groupId=$groupId", "User group add failed"
    )

  override def removeUserFromGroup(oktaId: String, group: Group)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] =
    futureWithErrorLogging(
      groupApi.unassignUserFromGroup(group.getId, oktaId),
      s"Couldn't remove user from group groupId=${group.getId} oktaId=$oktaId", "User group remove failed"
    )

  override def getGroup(id: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Group] = {
    futureWithErrorLogging(
      groupApi.getGroup(id),
      s"Error while getting Group with id='$id'",
      "Get Group failed"
    )
  }

  override def searchGroups(name: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[List[Group]] = {
    futureWithErrorLogging(
      groupApi.listGroups(name, null, null, null, null, null, null, null, null),
      s"Error while getting Group withe groupName='$name'", "Get Group failed")
  }.map(_.asScala.toList)

  def searchGroupRules(name: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[List[GroupRule]] = {
    futureWithErrorLogging(
      groupApi.listGroupRules(null, null, name, null),
      s"Exception while calling okta.listRules",
      "Get GroupRule failed"
    ).map(_.asScala.toList)
  }

  def createGroupRule(rule: OktaGroupRule, activate: Boolean)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[GroupRule] = {

    val groupRule = buildGroupRule(rule)

    futureWithErrorLogging(
      {
        val createdGroupRule = groupApi.createGroupRule(groupRule)
        if (activate) {
          groupApi.activateGroupRule(createdGroupRule.getId)
        }
        createdGroupRule
      },
      s"Couldn't create or activate Group Rule: ${groupRule.getName}",
      "Create GroupRule failed"
    )
  }

  def updateGroupRule(existingRule: GroupRule, ruleUpdate: OktaGroupRule, activate: Boolean)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[GroupRule] = {
    val updatedRule = buildGroupRule(ruleUpdate)
    existingRule.setActions(updatedRule.getActions)
    existingRule.setConditions(updatedRule.getConditions)
    futureWithErrorLogging(
      {
        if (existingRule.getStatus == GroupRuleStatus.ACTIVE) {
          groupApi.deactivateGroupRule(existingRule.getId)
        }
        val result = groupApi.replaceGroupRule(existingRule.getId, existingRule)
        if (activate) {
          groupApi.activateGroupRule(result.getId)
        }
        result
      },
      s"Couldn't update groupRule: ${existingRule.getName}",
      "Update GroupRule failed"
    )
  }

  // helpers
  private def getUserByLoginId(loginId: String)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Option[User]] = {
    futureWithErrorLogging(
      Option(userApi.getUser(loginId)),
      s"Error while getting user by loginId='$loginId'",
      "GET User by loginId failed"
    ).recover(okta.handleNotFound)
  }

  private def buildGroupRule(rule: OktaGroupRule): GroupRule = {
    val groupRule = new GroupRule()
      .name(rule.name)
      .conditions(
        new GroupRuleConditions()
          .expression(
            new GroupRuleExpression()
              .`type`("urn:okta:expression:1.0")
              .value(rule.expression)))
      .actions(
        new GroupRuleAction()
          .assignUserToGroups(
            new GroupRuleGroupAssignment()
              .groupIds(rule.groupIds.asJava)
          ))
      .`type`("group_rule")

    if (rule.excludedUserIds.nonEmpty) {
      groupRule.getConditions
        .setPeople(
          new GroupRulePeopleCondition()
            .users(
              new GroupRuleUserCondition()
                .exclude(rule.excludedUserIds.asJava))
        )
    }
    groupRule
  }

}

object OktaRepository {

  /**
   * @param config     SDK client config, also shared by direct client
   * @param httpClient HTTP client config. NOTE - this must be unauthenticated
   * @return
   */
  def apply(config: OktaClientConfig, httpClient: HttpOktaClient): OktaRepository = {
    val client = OktaClientFactory.getOktaClient(config)
    apply(client, httpClient)
  }

  /**
   * @param client     Okta SDK client
   * @param httpClient Raw HTTP client for unsupported operations
   * @return
   */
  def apply(client: ApiClient, httpClient: HttpOktaClient): OktaRepository = {
    OktaClientWrap(
      userApi = new UserApi(client),
      userFactorApi = new UserFactorApi(client),
      applicationApi = new ApplicationApi(client),
      groupApi = new GroupApi(client),
      schemaApi = new SchemaApi(client),
      httpClient
    )
  }

}
