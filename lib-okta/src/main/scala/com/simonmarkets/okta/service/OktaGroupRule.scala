package com.simonmarkets.okta.service

case class OktaGroupRule(name: String, expression: String, groupIds: List[String],
    excludedUserIds: List[String] = List())

object OktaGroupRules {

  def networkGroupRule(networkId: String, groupId: String, networkFieldName: String): OktaGroupRule = {
    OktaGroupRule(s"$networkId $networkFieldName", s"user." + networkFieldName + "==\"" + networkId + "\"", List(groupId))
  }
}