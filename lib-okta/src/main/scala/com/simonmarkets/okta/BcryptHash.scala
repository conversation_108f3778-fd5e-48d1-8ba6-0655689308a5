package com.simonmarkets.okta

import scala.util.Try

case class BcryptHash(
    hashedPassword: String,
    salt: String,
    workFactor: Integer
)

object BcryptHash {

  /**
   * Split brcyptHash string into its component parts
   *
   * @param password example: $2a$10$d64jcnf67sq4wlp0c/fr1.hs5d7t9op[/dt,cra6r.cfs10g63lc5
   * @return
   *
   * costFactor - 10
   *
   * salt - d64jcnf67sq4wlp0c/fr1. (first 22 characters)
   *
   * hash - hs5d7t9op[/dt,cra6r.cfs10g63lc5 (last 31 characters)
   */
  def fromString(password: String): Option[BcryptHash] = {
    password.split("\\$") match {
      case Array(_, _, costFactorRaw, saltAndHash) =>
        Try(costFactorRaw.toInt).toOption.map { costFactor =>
          BcryptHash(
            hashedPassword = saltAndHash.substring(22),
            salt = saltAndHash.substring(0, 22),
            workFactor = costFactor
          )
        }
      case _ => None
    }
  }
}