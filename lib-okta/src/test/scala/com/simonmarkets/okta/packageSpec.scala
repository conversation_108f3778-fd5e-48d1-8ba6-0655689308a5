package com.simonmarkets.okta

import com.simonmarkets.okta.domain.OktaApiExceptionResponseBody
import com.simonmarkets.okta.testimpl.OktaTestException
import org.scalatest.WordSpec

class packageSpec extends WordSpec {

  "parseApiExceptionResponseBody" when {
    "given valid JSON string input" should {
      "parse to a populated OktaApiExceptionResponseBody object" in {
        val res = parseApiExceptionResponseBody(OktaTestException.notFoundException.getResponseBody)

        val correct = OktaApiExceptionResponseBody(
          errorCode = Option(NotFoundErrorCode),
          errorSummary = Option("errorSummary"),
          errorId = Option("errorId"))

        assert(res.contains(correct))
      }
    }

    "given invalid JSON string input" should {
      "parse to a empty OktaApiExceptionResponseBody object" in {
        val res = parseApiExceptionResponseBody(OktaTestException.notFoundExceptionBungledResponseBody.getResponseBody)

        assert(res.isEmpty)
      }
    }

    "given unexpected JSON string input" should {
      "parse to a populated OktaApiExceptionResponseBody object that simply ignores unexpected tokens" in {
        val res = parseApiExceptionResponseBody(OktaTestException.notFoundExceptionUnexpectedResponseBodyToken.getResponseBody)

        val correct = OktaApiExceptionResponseBody(
          errorCode = Option(NotFoundErrorCode),
          errorSummary = Option("errorSummary"),
          errorId = Option("errorId"))

        assert(res.contains(correct))
      }
    }
  }

}
