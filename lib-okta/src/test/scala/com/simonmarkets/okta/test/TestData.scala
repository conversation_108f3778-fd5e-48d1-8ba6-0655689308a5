package com.simonmarkets.okta.test

import akka.actor.ActorSystem
import akka.http.scaladsl.Http
import akka.stream.Materializer
import com.okta.sdk.resource.model.{Group, UserCredentials, UserStatus}
import com.simonmarkets.http.authentication.NoAuthCredentials
import com.simonmarkets.http.{FutureHttpClient, HttpClientConfig}
import com.simonmarkets.okta.config.{OktaProfileConfig, OktaServiceConfig}
import com.simonmarkets.okta.service.OktaGroupRule
import com.simonmarkets.okta.testimpl._

import java.{lang, util}

import scala.collection.JavaConverters._
import scala.concurrent.ExecutionContext.Implicits.global

object TestData {

  val simonId = "id"
  val networkId = "networkId"
  val firstName = "firstName"
  val lastName = "lastName"
  val email = "email"
  val loginId = "loginId"
  val clientId = None
  val omsId = Some("omsId")
  val roles: Set[String] = Set("EqPIPGDeveloper")
  val locations = Set("loc1", "loc2")
  val tradewebEligible = true
  val regSEligible = false
  val isActive = true
  val faNumbers = Set("123", "456")
  val customRoles = Set("ADMIN", "customRole2")

  val adminCustomRoles = Set("EQPipgAdmin")
  val licenses: Set[String] = Set("license")
  val idpId = Some("123")
  val status = UserStatus.ACTIVE

  implicit val ac: ActorSystem = ActorSystem("test")

  implicit val mat: Materializer = Materializer.matFromSystem(ac)
  val httpClient = new FutureHttpClient(Http(),
    HttpClientConfig(auth = NoAuthCredentials,
      proxy = None))

  def oktaTestConfig(profile: OktaProfileConfig = OktaProfileConfig()): OktaServiceConfig = {
    OktaServiceConfig(profile = profile)
  }

  def oktaSDKUser(
      userId: String = simonId,
      email: String = email,
      firstName: String = firstName,
      lastName: String = lastName,
      login: String = idpId.get,
      secondEmail: String = email,
      userstatus: UserStatus = status,
      userprop: Map[String, Object] = customUserProperties(),
      credentials: UserCredentials = null
  )
  = UserTestImpl(
    userId,
    new UserProfileTestImpl(
      email,
      firstName,
      lastName,
      login,
      secondEmail,
      userprop
    ),
    userstatus,
    credentials
  )

  val defaultOktaUser: UserTestImpl = oktaSDKUser()

  val oktaUser2: UserTestImpl = oktaSDKUser(
    "userId2",
    "<EMAIL>",
    "fn2",
    "ln2",
    "<EMAIL>",
    userprop = customUserProperties(simonId = "simonId2")
  )

  def oktaGroup(id: String = "GroupId", name: String = "GroupName",
      desc: String = "GroupDesc"): Group = GroupTestImpl(id, new GroupProfileTestImpl(name, desc))

  def oktaGroupRule(name: String = "GroupRule Name", expression: String = "some expression",
      groupIds: List[String] = List(oktaGroup().getId)) = OktaGroupRule(name, expression, groupIds)

  def groupRule(name: String = "GroupRule Name", id: String = "12354") = GroupRuleTestImpl(id, name)

  def customUserProperties(
      network: String = "TestNetwork",
      customRoles: Set[String] = Set(),
      locations: Set[String] = Set(),
      faNumbers: Set[String] = Set(),
      omsId: Option[String] = None,
      clientId: Option[String] = None,
      simonId: String = "simonId",
      regSEligible: Boolean = false,
      userRoles: Set[String] = Set(),
      licenses: Set[String] = Set(),
      endpointCapabilities: Set[String] = Set(),
      tradewebEligible: Boolean = false,
      accountInContext: Option[String] = None,
      contextAccountId: Option[String] = None,
      contextProposalId: Option[String] = None,
      contextTaskId: Option[String] = None,
      contextServiceRequestId: Option[String] = None,
      contextLastUpdatedAt: Option[String] = None,
      ssoNetwork: String = "",
      ssoCustomRoles: Set[String] = Set(),
      ssoLocations: Set[String] = Set(),
      ssoFaNumbers: Set[String] = Set(),
      ssoOmsId: Option[String] = None,
      ssoClientId: Option[String] = None,
      ssoRegSEligible: Boolean = false,
      ssoUserRoles: Set[String] = Set(),
      ssoNpn: Option[String] = None,
      ssoEndpointCapabilities: Set[String] = Set(),
      ssoTradewebEligible: Boolean = false,
      ssoAccountInContext: Option[String] = None,
      ssoContextAccountId: Option[String] = None,
      ssoContextProposalId: Option[String] = None,
      ssoContextTaskId: Option[String] = None,
      ssoContextServiceRequestId: Option[String] = None,
      ssoFaNumbersCommaSeparated: Option[String] = None,
      ssoLocationsCommaSeparated: Option[String] = None,
      ssoCustomRolesCommaSeparated: Option[String] = None,
      ssoSubjectNetwork: Option[String] = None,
      ssoExternalNetworkId: Option[String] = None,
      whiteLabelPartnerId: Option[String] = None,
      firmId: Option[String] = None,
      iCapitalUserId: Option[String] = None,
      secondaryEmail: Option[String] = None,
  ) = {
    val noJitProps = Map[String, Object](
      "network" -> network,
      "customRoles" -> new util.ArrayList[String](customRoles.asJava),
      "locations" -> new util.ArrayList[String](locations.asJava),
      "faNumbers" -> new util.ArrayList[String](faNumbers.asJava),
      "omsId" -> omsId.getOrElse(""),
      "clientId" -> clientId.getOrElse(""),
      "simonId" -> simonId,
      "regSEligible" -> lang.Boolean.valueOf(regSEligible),
      "userRoles" -> new util.ArrayList[String](if (userRoles.isEmpty) Set("EqPIPGFA").asJava else userRoles.asJava),
      "licenses" -> new util.ArrayList[String](licenses.asJava),
      "endpointCapabilities" -> new util.ArrayList[String](endpointCapabilities.asJava),
      "tradewebEligible" -> lang.Boolean.valueOf(tradewebEligible),
      "clientIdMatchAgainst" -> clientId.map(cId => network.replace(" ", "-") + ":" + cId).getOrElse(""),
      "accountInContext" -> accountInContext.getOrElse(""),
      "contextAccountId" -> contextAccountId.getOrElse(""),
      "contextProposalId" -> contextProposalId.getOrElse(""),
      "contextTaskId" -> contextTaskId.getOrElse(""),
      "contextServiceRequestId" -> contextServiceRequestId.getOrElse(""),
      "contextLastUpdatedAt" -> contextLastUpdatedAt.getOrElse(""),
      "whiteLabelPartnerId" -> whiteLabelPartnerId.getOrElse(""),
      "firmId" -> firmId.getOrElse(""),
      "iCapitalUserId" -> iCapitalUserId.getOrElse(""),
      "secondaryEmail" -> secondaryEmail.getOrElse(""),
    )
    if (ssoNetwork.nonEmpty) noJitProps ++ Map("ssoNetwork" -> ssoNetwork,
      "ssoCustomRoles" -> new util.ArrayList[String](ssoCustomRoles.asJava),
      "ssoLocations" -> new util.ArrayList[String](ssoLocations.asJava),
      "ssoFaNumbers" -> new util.ArrayList[String](ssoFaNumbers.asJava),
      "ssoOmsId" -> ssoOmsId.getOrElse(""),
      "ssoClientId" -> ssoClientId.getOrElse(""),
      "ssoRegSEligible" -> lang.Boolean.valueOf(ssoRegSEligible),
      "ssoUserRoles" -> new util.ArrayList[String](ssoUserRoles.asJava),
      "ssoNpn" -> ssoNpn.getOrElse(""),
      "ssoEndpointCapabilities" -> new util.ArrayList[String](ssoEndpointCapabilities.asJava),
      "ssoTradewebEligible" -> lang.Boolean.valueOf(ssoTradewebEligible),
      "ssoAccountInContext" -> ssoAccountInContext.getOrElse(""),
      "ssoContextAccountId" -> ssoContextAccountId.getOrElse(""),
      "ssoContextProposalId" -> ssoContextProposalId.getOrElse(""),
      "ssoContextTaskId" -> ssoContextTaskId.getOrElse(""),
      "ssoContextServiceRequestId" -> ssoContextServiceRequestId.getOrElse(""),
      "ssoFaNumbersCommaSeparated" -> ssoFaNumbersCommaSeparated.getOrElse(""),
      "ssoLocationsCommaSeparated" -> ssoLocationsCommaSeparated.getOrElse(""),
      "ssoCustomRolesCommaSeparated" -> ssoCustomRolesCommaSeparated.getOrElse(""),
      "ssoSubjectNetwork" -> ssoSubjectNetwork.getOrElse(""),
      "ssoExternalNetworkId" -> ssoExternalNetworkId.getOrElse(""),
    )
    else noJitProps
  }

}
