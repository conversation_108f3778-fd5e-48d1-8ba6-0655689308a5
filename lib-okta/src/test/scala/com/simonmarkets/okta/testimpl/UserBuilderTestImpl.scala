package com.simonmarkets.okta.testimpl

import com.okta.sdk.resource.api.UserApi
import com.okta.sdk.resource.model.{AuthenticationProvider, User, UserNextLogin, UserType}
import com.okta.sdk.resource.user.UserBuilder

import java.{lang, util}

object UserBuilderTestImpl {

  trait UserBuilderBase extends UserBuilder {

    override def setPassword(password: Array[Char]): UserBuilder = this

    override def usePasswordHookForImport(): UserBuilder = ???

    override def usePasswordHookForImport(`type`: String): UserBuilder = ???

    override def setSecurityQuestion(question: String): UserBuilder = ???

    override def setSecurityQuestionAnswer(answer: String): UserBuilder = ???

    override def setEmail(email: String): UserBuilder = this

    override def setFirstName(firstName: String): UserBuilder = this

    override def setLastName(lastName: String): UserBuilder = this

    override def setLogin(login: String): UserBuilder = this

    override def setMobilePhone(mobilePhone: String): UserBuilder = ???

    override def setSecondEmail(secondEmail: String): UserBuilder = ???

    override def setActive(active: lang.Boolean): UserBuilder = {
      this
    }

    override def setType(userTypeId: String): UserBuilder = ???

    override def addGroup(groupId: String): UserBuilder = ???

    override def setBcryptPasswordHash(value: String, salt: String, workFactor: Int): UserBuilder = ???

    override def setSha256PasswordHash(value: String, salt: String, saltOrder: String): UserBuilder = ???

    override def setSha512PasswordHash(value: String, salt: String, saltOrder: String): UserBuilder = ???

    override def setSha1PasswordHash(value: String, salt: String, saltOrder: String): UserBuilder = ???

    override def setMiddleName(middleName: String): UserBuilder = ???

    override def setHonorificPrefix(honorificPrefix: String): UserBuilder = ???

    override def setHonorificSuffix(honorificSuffix: String): UserBuilder = ???

    override def setTitle(title: String): UserBuilder = ???

    override def setDisplayName(displayName: String): UserBuilder = ???

    override def setNickName(nickName: String): UserBuilder = ???

    override def setProfileUrl(profileUrl: String): UserBuilder = ???

    override def setPrimaryPhone(primaryPhone: String): UserBuilder = ???

    override def setStreetAddress(streetAddress: String): UserBuilder = ???

    override def setCity(city: String): UserBuilder = ???

    override def setState(state: String): UserBuilder = ???

    override def setZipCode(zipCode: String): UserBuilder = ???

    override def setCountryCode(countryCode: String): UserBuilder = ???

    override def setPostalAddress(postalAddress: String): UserBuilder = ???

    override def setPreferredLanguage(preferredLanguage: String): UserBuilder = ???

    override def setLocale(locale: String): UserBuilder = ???

    override def setTimezone(timezone: String): UserBuilder = ???

    override def setEmployeeNumber(employeeNumber: String): UserBuilder = ???

    override def setCostCenter(costCenter: String): UserBuilder = ???

    override def setOrganization(organization: String): UserBuilder = ???

    override def setDivision(division: String): UserBuilder = ???

    override def setDepartment(department: String): UserBuilder = ???

    override def setManagerId(managerId: String): UserBuilder = ???

    override def setManager(manager: String): UserBuilder = ???

    override def setProvider(provider: AuthenticationProvider): UserBuilder = ???

    override def setType(userType: UserType): UserBuilder = ???

    override def setGroups(groupIds: util.List[String]): UserBuilder = ???

    override def setNextLogin(nextLogin: UserNextLogin): UserBuilder = ???

    override def setCustomProfileProperty(key: String, value: Any): UserBuilder = {
      this
    }

    override def buildAndCreate(client: UserApi): User = {
      new User()
    }
  }
}
