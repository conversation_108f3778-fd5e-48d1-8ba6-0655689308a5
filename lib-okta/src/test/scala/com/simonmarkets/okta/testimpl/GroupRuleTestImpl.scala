package com.simonmarkets.okta.testimpl

import com.okta.sdk.resource.model.{GroupRule, GroupRuleAction, GroupRuleConditions, GroupRuleStatus}
import org.mockito.Mockito

class GroupRuleTestImpl(ruleId: String,var name: String)
  extends GroupRule {

  override def getActions: GroupRuleAction = null

  override def getConditions: GroupRuleConditions = null

  override def getId: String = ruleId

  override def getName: String = name

  override def getStatus: GroupRuleStatus = null

  override def getType: String = ???
}

object GroupRuleTestImpl {
  def apply(ruleId: String, name: String): GroupRuleTestImpl =
    Mockito.spy(new GroupRuleTestImpl(ruleId, name))
}