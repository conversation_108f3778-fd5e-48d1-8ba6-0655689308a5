package com.simonmarkets.okta.testimpl

import com.okta.sdk.resource.model._
import com.simonmarkets.okta.domain.OktaUser
import org.mockito.Mockito

import java.util

class UserTestImpl(
    var userId: String = "",
    var userProfile: UserProfile = null,
    var userStatus: UserStatus = UserStatus.STAGED,
    var userCredentials: UserCredentials = null
) extends User {

  val hasPassword: Boolean = userStatus == UserStatus.ACTIVE

  override def getEmbedded: util.Map[String, Object] = ???

  override def getCredentials: UserCredentials = userCredentials

  override def getId: String = userId

  override def getProfile: UserProfile = userProfile

  override def getStatus: UserStatus = userStatus

  override def getTransitioningToStatus: UserStatus = ???

  override def getType: UserType = ???
}

object UserTestImpl {
  def apply(userId: String = "", userProfile: UserProfile = null,
      userStatus: UserStatus = UserStatus.STAGED, credentials: UserCredentials = null): UserTestImpl =
    Mockito.spy(new UserTestImpl(userId, userProfile, userStatus, credentials))

  def apply(oktaUser: OktaUser): UserTestImpl = new UserTestImpl(
    oktaUser.oktaId.getOrElse("invalid"),
    new UserProfileTestImpl(
      oktaUser.email,
      oktaUser.firstName,
      oktaUser.lastName,
      oktaUser.loginId,
      null,
      Map.empty
    )
  )
}
