package com.simonmarkets.okta.testimpl

import com.okta.sdk.resource.model.{Group, GroupProfile, GroupType}
import org.mockito.Mockito

import java.util

class GroupTestImpl(
  val groupId: String,
  var groupProfile: GroupProfile
) extends Group {

  override def getEmbedded: util.Map[String, Object] = ???

  override def getId: String = groupId

  override def getObjectClass: util.List[String] = ???

  override def getProfile: GroupProfile = groupProfile

  override def getType: GroupType = ???
}

object GroupTestImpl {
  def apply(groupId: String,
      groupProfile: GroupProfile): GroupTestImpl = Mockito.spy(new GroupTestImpl(groupId, groupProfile))
}
