package com.simonmarkets.okta

import org.scalatest.MustMatchers.convertToAnyMustWrapper
import org.scalatest.WordSpec

class BcryptHashSpec extends WordSpec {

  "BcryptHash" when {
    "Correct input" should {
      "succeed as Some" in {
        BcryptHash.fromString("$2a$10$d64jcnf67sq4wlp0c/fr1.hs5d7t9op[/dt,cra6r.cfs10g63lc5") mustBe
          Some(BcryptHash(
            hashedPassword = "hs5d7t9op[/dt,cra6r.cfs10g63lc5",
            salt = "d64jcnf67sq4wlp0c/fr1.",
            workFactor = 10
          ))
      }
    }
    "Incorrect input" should {
      "be None if empty" in {
        BcryptHash.fromString("") mustBe None
      }
      "be None if malformed" in {
        BcryptHash.fromString("$2a10$d64jcnf67sq4wlp0c/fr1.hs5d7t9op[/dt,cra6r.cfs10g63lc5") mustBe None
      }
    }
  }
}
