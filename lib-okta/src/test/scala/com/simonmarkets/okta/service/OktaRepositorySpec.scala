package com.simonmarkets.okta.service

import com.okta.sdk.resource.api._
import com.okta.sdk.resource.model.{UpdateUserRequest, User}
import com.simonmarkets.logging.TraceId
import com.simonmarkets.okta.client.api.HttpOktaClient
import com.simonmarkets.okta.client.api.factors.{EnrollFactorRequest, EnrollFactorResponse, EnrollFactorStatus}
import com.simonmarkets.okta.domain.{FactorProfileProperty, FactorType, OktaUser}
import com.simonmarkets.okta.test.TestData._
import com.simonmarkets.okta.testimpl.{OktaTestException, UserTestImpl}
import com.simonmarkets.syntax.{anyOpsConversion, futureOpsConversion}
import org.mockito.ArgumentMatchers
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito._
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

class OktaRepositorySpec extends WordSpec with MockitoSugar with BeforeAndAfterEach with Matchers {

  private implicit val traceId: TraceId = TraceId.randomize
  private implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  private val directClient = mock[HttpOktaClient]
  private val sdkUser: User = mock[User]
  private val userApi = mock[UserApi]
  private val userFactorApi = mock[UserFactorApi]
  private val applicationApi = mock[ApplicationApi]
  private val groupApi = mock[GroupApi]
  private val schemaApi = mock[SchemaApi]

  private val service = OktaClientWrap(
    userApi,
    userFactorApi,
    applicationApi,
    groupApi,
    schemaApi,
    directClient = directClient
  )

  private val oktaUser: OktaUser = OktaUser(
    id = simonId,
    network = networkId,
    firstName = firstName,
    lastName = lastName,
    email = email,
    distributorId = clientId,
    omsId = omsId,
    roles = roles,
    locations = locations,
    tradewebEligible = tradewebEligible,
    regSEligible = regSEligible,
    isActive = isActive,
    faNumbers = faNumbers,
    customRoles = customRoles,
    licenses = licenses,
    oktaId = idpId,
    endpointCapabilities = Set.empty,
    loginId = loginId
  )

  override def beforeEach(): Unit = reset(
    userApi,
    userFactorApi,
    applicationApi,
    groupApi,
    schemaApi,
    directClient,
    sdkUser
  )


  "getBySimonId " should {
    "return the Some(user) if the user exists" in {
      when(userApi.listUsers(null, null, null, null, "profile.simonId eq \"" + oktaUser.id + "\"", null, null))
        .thenReturn(java.util.List.of[User](sdkUser))

      service.getUserBySimonId(oktaUser.id).await shouldBe Some(sdkUser)
    }

    "return None if the user not found" in {
      when(userApi.listUsers(null, null, null, null, "profile.simonId eq \"" + oktaUser.id + "\"", null, null))
        .thenReturn(java.util.List.of[User]())

      service.getUserBySimonId(oktaUser.id).await shouldBe None
    }

    "throw an Exception if more that one user found with simonId" in {
      when(userApi.listUsers(null, null, null, null, "profile.simonId eq \"" + oktaUser.id + "\"", null, null))
        .thenReturn(java.util.List.of[User](sdkUser, sdkUser))

      assertThrows[Exception](service.getUserBySimonId(oktaUser.id).await)
    }
  }


  "getUser" should {
    "query the user by oktaId if its defined" in {
      service.getUser(oktaUser).await
      verify(userApi).getUser(oktaUser.oktaId.get)
      verifyNoMoreInteractions(userApi)
    }

    "query the user by simonId if oktaId is not defined" in {
      val userNoOktaId = oktaUser.copy(oktaId = None)

      when(userApi.listUsers(null, null, null, null, "profile.simonId eq \"" + userNoOktaId.id + "\"", null, null))
        .thenReturn(java.util.List.of[User](UserTestImpl(userNoOktaId)))

      service.getUser(userNoOktaId).await
    }

    "query the user by loginId if oktaId is not defined and simonId is not found" in {
      val userNoOktaId = oktaUser.copy(oktaId = None)

      when(userApi.listUsers(null, null, null, null, "profile.simonId eq \"" + userNoOktaId.id + "\"", null, null))
        .thenReturn(java.util.List.of[User]())

      service.getUser(userNoOktaId).await

      verify(userApi).getUser(oktaUser.loginId)
    }

    "return None if the user not found by oktaId, simonId or loginId" in {
      val userNoOktaId = oktaUser.copy(oktaId = None)

      when(userApi.listUsers(null, null, null, null, "profile.simonId eq \"" + userNoOktaId.id + "\"", null, null))
        .thenReturn(java.util.List.of[User]())
      when(userApi.getUser(oktaUser.loginId))
        .thenThrow(OktaTestException.notFoundException)

      service.getUser(userNoOktaId).await shouldBe None
    }
  }

  "getUserByOktaId" should {
    "return the user if found by oktaId" in {
      when(userApi.getUser(oktaUser.oktaId.get))
        .thenReturn(sdkUser)

      service.getUserByOktaId(oktaUser.oktaId.get).await shouldBe Some(sdkUser)
    }

    "return the None if the user is not found by oktaId" in {
      when(userApi.getUser(oktaUser.oktaId.get))
        .thenThrow(OktaTestException.notFoundException)

      service.getUserByOktaId(oktaUser.oktaId.get).await shouldBe None
    }
  }

  "updateUser" should {
    "call update" in {
      val user = oktaSDKUser()
      service.updateUser(user).await
      verify(userApi, times(1)).updateUser(any[String](), any[UpdateUserRequest](), any[Boolean]())
    }
  }

  "enrollFactor" should {
    val decryptedPhoneNumber = "13124441223"
    val userId = "user1"
    val req = EnrollFactorRequest(FactorType.sms, Map(FactorProfileProperty.phoneNumber -> decryptedPhoneNumber))
    "register sms factor" in {
      val res = mock[EnrollFactorResponse]
      when(directClient.autoEnrollFactor(
        ArgumentMatchers.eq(userId),
        ArgumentMatchers.eq(req))(any[TraceId]))
        .thenReturn(res.successFuture)
      when(res.status).thenReturn(EnrollFactorStatus.ACTIVE)
      val updatedFactors = service.enrollFactor("", userId, req).await

      updatedFactors shouldBe true
    }

    "not enroll factor if not active" in {
      val res = mock[EnrollFactorResponse]
      when(directClient.autoEnrollFactor(
        ArgumentMatchers.eq(userId),
        ArgumentMatchers.eq(req))(any[TraceId]))
        .thenReturn(res.successFuture)
      when(res.status).thenReturn(EnrollFactorStatus.PENDING_ACTIVATION)
      val updatedFactors = service.enrollFactor("", userId, req).await

      updatedFactors shouldBe false
    }

  }
}
