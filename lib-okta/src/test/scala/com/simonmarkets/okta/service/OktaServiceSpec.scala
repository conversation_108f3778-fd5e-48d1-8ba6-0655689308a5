package com.simonmarkets.okta.service

import com.okta.sdk.resource.model.{Group, GroupRule, PasswordCredential, RecoveryQuestionCredential, UpdateUserRequest, User, UserCredentials, UserStatus}
import com.okta.sdk.resource.user.UserBuilder
import com.simonmarkets.logging.TraceId
import com.simonmarkets.okta.BcryptHash
import com.simonmarkets.okta.client.api.OktaClient
import com.simonmarkets.okta.client.api.factors.EnrollFactorRequest
import com.simonmarkets.okta.config.OktaServiceConfig
import com.simonmarkets.okta.domain.{Factor, FactorProfileProperty, FactorType, OktaUser}
import com.simonmarkets.okta.test.TestData
import com.simonmarkets.okta.test.TestData._
import com.simonmarkets.okta.testimpl.UserBuilderTestImpl.UserBuilderBase
import com.simonmarkets.okta.testimpl.UserProfileTestImpl
import com.simonmarkets.syntax.{anyOpsConversion, futureOpsConversion}
import org.mockito.{ArgumentCaptor, ArgumentMatchers}
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito._
import org.scalatest.Matchers._
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterEach, WordSpec}

import scala.concurrent.ExecutionContext.global
import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}


class OktaServiceSpec extends WordSpec with MockitoSugar with BeforeAndAfterEach {

  implicit val traceId: TraceId = TraceId("OktaServiceSpec")
  implicit val ec: ExecutionContextExecutor = global

  val repository: OktaRepository = mock[OktaRepository]
  val config: OktaServiceConfig = OktaServiceConfig()

  val service: OktaService = OktaService(config, repository)

  private val password = BcryptHash(
    hashedPassword = "hash",
    salt = "salt",
    workFactor = 10
  )

  private val oktaUser: OktaUser = OktaUser(
    id = simonId,
    network = networkId,
    firstName = firstName,
    lastName = lastName,
    email = email,
    distributorId = clientId,
    omsId = omsId,
    roles = roles,
    locations = locations,
    tradewebEligible = tradewebEligible,
    regSEligible = regSEligible,
    isActive = isActive,
    faNumbers = faNumbers,
    customRoles = customRoles,
    licenses = licenses,
    oktaId = idpId,
    endpointCapabilities = Set.empty,
    loginId = loginId,
  )

  private val oktaUserPassword = oktaUser.copy(password = Some(password))

  val sdkUser: User = mock[User]

  val oktaHttpClient: Option[OktaClient] = Some(mock[OktaClient])

  override def beforeEach(): Unit = reset(repository, sdkUser)

  "resetPassword" should {
    val mockCredentials = mock[UserCredentials]

    "reset, deactivate, and activate if user have not set recovery" in {
      when(sdkUser.getCredentials).thenReturn(mockCredentials)
      when(mockCredentials.getRecoveryQuestion).thenReturn(null)
      when(repository.deactivateUser(sdkUser)).thenReturn(Future.unit)
      when(repository.activateUser(sdkUser, sendEmail = true)).thenReturn(Future.unit)
      when(repository.resetPassword(sdkUser, sendEmail = false)).thenReturn(Future.unit)

      service.resetPassword(sdkUser).await shouldBe (())
    }

    "trigger reset-password email" in {
      when(sdkUser.getCredentials).thenReturn(mockCredentials)
      when(mockCredentials.getRecoveryQuestion).thenReturn(mock[RecoveryQuestionCredential])
      when(repository.resetPassword(sdkUser, sendEmail = true)).thenReturn(Future.unit)

      service.resetPassword(sdkUser).await shouldBe (())
    }
  }

  "deactivateUser" should {
    "deactivate the user" in {
      when(repository.deactivateUser(sdkUser)).thenReturn(Future.unit)

      service.deactivateUser(sdkUser).await
    }
  }

  "upsertUser" should {
    "insert the user and activate it if not exists in okta" in {
      val builder = new UserBuilderBase {}

      when(repository.getUser(oktaUser)).thenReturn(None.successFuture)
      when(repository.userBuilder).thenReturn(builder)
      when(repository.createUser(builder)).thenReturn(sdkUser.successFuture)

      service.upsertUser(oktaUser).await shouldBe sdkUser
    }

    "insert the user without activating it if not exists in okta and config specifies not to" in {
      val notActivateService = OktaService(config.copy(create = config.create.copy(activate = false)), repository)
      val builder = new UserBuilderBase {}

      when(repository.getUser(oktaUser)).thenReturn(None.successFuture)
      when(repository.userBuilder).thenReturn(builder)
      when(repository.createUser(builder)).thenReturn(sdkUser.successFuture)

      notActivateService.upsertUser(oktaUser).await shouldBe sdkUser
    }

    "update user if already exists" in {
      val user = oktaSDKUser()

      when(repository.getUser(oktaUser)).thenReturn(user.some.successFuture)

      val successfulUser: User = mock[User]
      when(repository
        .updateUser(any[String], any[UpdateUserRequest])(any[TraceId], any[ExecutionContext]))
        .thenReturn(successfulUser.successFuture)

      service.upsertUser(oktaUser).await
      verify(repository, never).createUser(any[UserBuilder])(any[TraceId], any[ExecutionContext])
      val captor: ArgumentCaptor[UpdateUserRequest] = ArgumentCaptor.forClass(classOf[UpdateUserRequest])
      verify(repository, times(1)).updateUser(any[String], captor.capture())(any[TraceId], any[ExecutionContext])

      val updatedProfile = captor.getValue.getProfile
      updatedProfile.getFirstName shouldBe oktaUser.firstName
      updatedProfile.getLastName shouldBe oktaUser.lastName
      updatedProfile.getEmail shouldBe oktaUser.email
      updatedProfile.getLogin shouldBe oktaUser.loginId
      updatedProfile.getFirstName shouldBe user.getProfile.getFirstName
    }

    "update user custom properties if already exists" in {
      val updatedCustomProperties = customUserProperties(
        network = oktaUser.network,
        customRoles = oktaUser.customRoles,
        locations = oktaUser.locations,
        faNumbers = oktaUser.faNumbers,
        omsId = oktaUser.omsId,
        clientId = oktaUser.distributorId,
        simonId = oktaUser.id,
        regSEligible = oktaUser.regSEligible,
        userRoles = oktaUser.roles,
        licenses = oktaUser.licenses,
        endpointCapabilities = oktaUser.endpointCapabilities,
        tradewebEligible = oktaUser.tradewebEligible,
        accountInContext = oktaUser.accountInContext,
        contextAccountId = oktaUser.contextAccountId,
        contextProposalId = oktaUser.contextProposalId,
        contextTaskId = oktaUser.contextTaskId,
        contextServiceRequestId = oktaUser.contextServiceRequestId,
        contextLastUpdatedAt = oktaUser.contextLastUpdatedAt,
        whiteLabelPartnerId = oktaUser.whiteLabelPartnerId,
        firmId = oktaUser.firmId,
        secondaryEmail = oktaUser.secondaryEmail,
        iCapitalUserId = oktaUser.iCapitalUserId,
      )
      val user = oktaSDKUser(userprop = updatedCustomProperties)

      when(repository.getUser(oktaUser)).thenReturn(user.some.successFuture)

      val successfulUser: User = mock[User]
      when(repository
        .updateUser(any[String], any[UpdateUserRequest])(any[TraceId], any[ExecutionContext]))
        .thenReturn(successfulUser.successFuture)

      service.upsertUser(oktaUser).await
      verify(repository, never).createUser(any[UserBuilder])(any[TraceId], any[ExecutionContext])
      customPropertiesAsScala(user.getProfile.asInstanceOf[UserProfileTestImpl].customProperties) shouldBe
        customPropertiesAsScala(user.getProfile.asInstanceOf[UserProfileTestImpl].customProperties)

      customPropertiesAsScala(user.getProfile.asInstanceOf[UserProfileTestImpl].customProperties) shouldBe
        customPropertiesAsScala(updatedCustomProperties)

    }

    "set the user password if user is in staged state" in {
      val mockCredentials = mock[UserCredentials]
      val passwordMock = mock[PasswordCredential]
      val user = oktaSDKUser(userstatus = UserStatus.STAGED)

      when(repository.getUser(oktaUserPassword)).thenReturn(user.some.successFuture)
      when(user.getCredentials).thenReturn(mockCredentials)
      when(repository.passwordBuilder(password)).thenReturn(passwordMock)
      val successfulUser: User = mock[User]
      when(repository
        .updateUser(any[String], any[UpdateUserRequest])(any[TraceId], any[ExecutionContext]))
        .thenReturn(successfulUser.successFuture)

      service.upsertUser(oktaUserPassword).await
      verify(mockCredentials, times(1)).setPassword(passwordMock)

    }

    "delete and then set the user password if user is not in staged state" in {

      when(repository.getUser(oktaUserPassword))
        .thenReturn(sdkUser.some.successFuture)
        .thenReturn(sdkUser.some.successFuture) //extra to make sure retry works
        .thenReturn(None.successFuture)
      when(repository.deactivateUser(sdkUser)).thenReturn(Future.unit)
      when(repository.deleteUser(sdkUser)).thenReturn(Future.unit)

      val builder = new UserBuilderBase {
        override def setBcryptPasswordHash(value: String, salt: String, workFactor: Int): UserBuilder = this
      }

      when(repository.userBuilder).thenReturn(builder)
      when(repository.createUser(builder)).thenReturn(sdkUser.successFuture)

      service.upsertUser(oktaUserPassword).await(Duration.Inf) shouldBe sdkUser
    }
  }

  "upsertGroup" should {
    "update the group if it exists" in {
      val group = oktaGroup()
      val newName = "newName"
      val newDesc = "newDesc"
      val newGroup = oktaGroup(id = group.getId, newName, newDesc)

      when(repository.searchGroups(group.getProfile.getName)).thenReturn(List(group).successFuture)
      when(repository.updateGroup(any[Group])(any[TraceId], any[ExecutionContext])).thenReturn(newGroup.successFuture)

      service.upsertGroup(group.getProfile.getName, newName, newDesc).await shouldBe newGroup
      verify(repository, times(1)).updateGroup(any[Group])(any[TraceId], any[ExecutionContext])
    }

    "create the group if it doesn't exist" in {
      val group = oktaGroup()

      when(repository.searchGroups(group.getProfile.getName)).thenReturn(Nil.successFuture)
      when(repository.createGroup(group.getProfile.getName, group.getProfile.getDescription))
        .thenReturn(group.successFuture)

      service
        .upsertGroup(group.getProfile.getName, group.getProfile.getName, group.getProfile.getDescription)
        .await shouldBe group
    }
  }

  "createGroupRule" should {
    "create and activate the GroupRule" in {
      val rule = TestData.oktaGroupRule()

      when(repository.createGroupRule(rule, activate = true)).thenReturn(mock[GroupRule].successFuture)

      service.createGroupRule(rule, activate = true)
    }
  }

  "upsertGroupRule" should {
    "Create the groupRule if it doesn't exists" in {
      val rule = TestData.oktaGroupRule()

      when(repository.searchGroupRules(rule.name)).thenReturn(Nil.successFuture)
      when(repository.createGroupRule(rule, true)).thenReturn(mock[GroupRule].successFuture)

      service.upsertGroupRule(rule, true).await
      verify(repository, times(1))
        .createGroupRule(any[OktaGroupRule], any[Boolean])(any[TraceId], any[ExecutionContext])
    }

    "Update the rule if it exists" in {
      val oldRule = TestData.groupRule()
      val rule = TestData.oktaGroupRule()

      when(repository.searchGroupRules(rule.name)).thenReturn(List(oldRule).successFuture)
      when(repository.updateGroupRule(oldRule, rule, true)).thenReturn(mock[GroupRule].successFuture)

      service.upsertGroupRule(rule, true).await
      verify(repository, never)
        .createGroupRule(any[OktaGroupRule], any[Boolean])(any[TraceId], any[ExecutionContext])
    }
  }

  "enrollFactors" should {

    val decryptedPhoneNumber = "13124441223"
    val userId = "user1"

    val factors = Map("sms" -> Factor(Some(decryptedPhoneNumber)))
    val req = EnrollFactorRequest(FactorType("sms"), Map(FactorProfileProperty("phoneNumber") -> decryptedPhoneNumber))
    "register sms factor" in {
      when(sdkUser.getId).thenReturn(userId)
      when(repository.enrollFactor(userId, userId, req))
        .thenReturn(Future.successful(true))

      service.enrollFactors(userId, sdkUser, factors).await
    }

    "not enroll an already registered factor" in {
      val factors = Map("sms" -> Factor(Some(decryptedPhoneNumber), isFactorEnrolled = true))
      when(sdkUser.getId).thenReturn(userId)
      val req = EnrollFactorRequest(FactorType("sms"), Map(FactorProfileProperty("phoneNumber") -> decryptedPhoneNumber))
      service.enrollFactors(userId, sdkUser, factors).await shouldBe factors
      verify(repository, never)
        .enrollFactor(any[String], any[String], ArgumentMatchers.eq(req))(any[TraceId], any[ExecutionContext])
    }

    "not enroll unsupported factor" in {
      val factors = Map(
        "sms" -> Factor(Some(decryptedPhoneNumber)),
        "call" -> Factor(Some("unsupported"), isFactorEnrolled = false)
      )
      when(sdkUser.getId).thenReturn("someId")
      when(repository.enrollFactor(any[String], any[String], ArgumentMatchers.eq(req))(any[TraceId], any[ExecutionContext])).thenReturn(Future.successful(true))
      val updatedFactors = service.enrollFactors(userId, sdkUser, factors).await
      val result = factors.updated("sms", Factor(Some(decryptedPhoneNumber), isFactorEnrolled = true))

      updatedFactors shouldBe result
    }
  }

  private def customPropertiesAsScala(prop: Map[String, Object]): Map[String, Any] = {
    prop.map {
      case (key, value) => value match {
        case _ => (key, value)
      }
    }
  }

}
