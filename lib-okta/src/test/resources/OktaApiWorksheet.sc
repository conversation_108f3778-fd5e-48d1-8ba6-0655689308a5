import com.okta.sdk.authc.credentials.TokenClientCredentials
import com.okta.sdk.client.Clients
import com.okta.sdk.resource.api.UserApi
import com.okta.sdk.resource.client.ApiException

/**
 * This worksheet is intended to help developers quickly test Okta api behaviors.
 */

// fill in these fields
val orgUrl = ???
val apiAccessToken = ???

// client created to build apis on
val apiClient = Clients.builder()
  .setOrgUrl(orgUrl)
  .setClientCredentials(new TokenClientCredentials(apiAccessToken))
  .build()

// api created to interact with Okta
val userApi = new UserApi(apiClient)

// testing code
try {
  val user = userApi.getUser("myUser")
} catch {
  case e : ApiException => println(e.getCode)
}
