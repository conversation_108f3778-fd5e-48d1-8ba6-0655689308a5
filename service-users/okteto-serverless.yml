service: service-users

provider:
  stage: ${self:custom.file.provider.environment}
  name: aws
  runtime: java11
  region: us-east-1
  profile: ${self:custom.file.provider.profile}
  versionFunctions: false
  deploymentBucket:
    name: simon-dev-local-serverless

custom:
  localstack:
    stages:
      - dev-local
    host: http://localstack-svc.${env:OKTETO_NAMESPACE}
    edgePort: 4566
    debug: true
  file: ${file(../config/okteto.yml)}
  warmup:
    default:
      enabled: true
      concurrency: 1
      prewarm: true
      timeout: 300
      events:
        - schedule: rate(5 minutes)
      vpc: false
  icnFeatures: ${self:custom.file.icnFeatures}
  newRelic:
    javaNewRelicHandler: handleStreamsRequest


package:
  artifact: target/service-users-uber.jar

plugins:
  - serverless-localstack
  - serverless-deployment-bucket
  - serverless-plugin-warmup


functions:
  monolith-cache-expire:
    handler: com.simonmarkets.users.MonolithCacheExpiryApp::handleRequest
    warmup:
      default:
        enabled: false
    # memorySize: 1024
    timeout: 900
    reservedConcurrency: 1
    environment:
      JAVA_TOOL_OPTIONS:
        -Dconfig.file=okteto.conf
        -DSM_CREDENTIAL_PROVIDER=default
    events:
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusUsers}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"
            detail:
              ns:
                db:
                  - pipg
                coll:
                  - users
          inputTransformer:
            inputPathsMap:
              id: "$.id"
              time: "$.time"
              detail: "$.detail"
            inputTemplate: '{"id": <id>, "time": <time>, "detail":<detail>}'
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusNetworks}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"
            detail:
              ns:
                db:
                  - ${self:custom.file.dbNetworks}
                coll:
                  - ${self:custom.file.collectionNetworks}
          inputTransformer:
            inputPathsMap:
              id: "$.id"
              time: "$.time"
              detail: "$.detail"
            inputTemplate: '{"id": <id>, "time": <time>, "detail":<detail>}'
  acl-sync:
    handler: com.simonmarkets.users.AclSyncEventHandler::handleRequest
    warmup:
      default:
        enabled: false
    # memorySize: 4096
    timeout: 900
    reservedConcurrency: 1
    events:
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusUsers}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"
            detail:
              ns:
                db:
                  - ${self:custom.file.dbUsers}
                coll:
                  - ${self:custom.file.collectionUsers}
          inputTransformer:
            inputPathsMap:
              id: "$.id"
              time: "$.time"
              detail: "$.detail"
            inputTemplate: '{"id": <id>, "time": <time>, "detail":<detail>}'
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusNetworks}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"
            detail:
              ns:
                db:
                  - ${self:custom.file.dbNetworks}
                coll:
                  - ${self:custom.file.collectionNetworks}
          inputTransformer:
            inputPathsMap:
              id: "$.id"
              time: "$.time"
              detail: "$.detail"
            inputTemplate: '{"id": <id>, "time": <time>, "detail":<detail>}'
    environment:
      JAVA_TOOL_OPTIONS:
        -Dconfig.file=okteto.conf
        -DSM_CREDENTIAL_PROVIDER=default

  okta-sync:
    handler: com.simonmarkets.users.OktaSyncEventHandler::handleRequest
    warmup:
      default:
        enabled: false
    # memorySize: 1024
    timeout: 900
    reservedConcurrency: 1
    events:
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusUsers}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"
            detail:
              ns:
                db:
                  - ${self:custom.file.dbUsers}
                coll:
                  - ${self:custom.file.collectionUsers}
          inputTransformer:
            inputPathsMap:
              id: "$.id"
              time: "$.time"
              detail: "$.detail"
            inputTemplate: '{"id": <id>, "time": <time>, "detail":<detail>}'
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusNetworks}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"
            detail:
              ns:
                db:
                  - ${self:custom.file.dbNetworks}
                coll:
                  - ${self:custom.file.collectionNetworks}
          inputTransformer:
            inputPathsMap:
              id: "$.id"
              time: "$.time"
              detail: "$.detail"
            inputTemplate: '{"id": <id>, "time": <time>, "detail":<detail>}'
    environment:
      JAVA_TOOL_OPTIONS:
        -Dconfig.file=okteto.conf
        -DSM_CREDENTIAL_PROVIDER=default

  okta-sync-simple:
    handler: com.simonmarkets.users.OktaSyncEventHandler::handleRequest
    warmup:
      default:
        enabled: false
    # memorySize: 1024
    timeout: 900
    reservedConcurrency: 1
    environment:
      JAVA_TOOL_OPTIONS:
        -Dconfig.file=okteto.conf
        -DSM_CREDENTIAL_PROVIDER=default

  okta-sync-network-mfa:
    handler: com.simonmarkets.users.OktaSyncEventHandler::handleRequest
    warmup:
      default:
        enabled: false
    # memorySize: 1024
    timeout: 900
    reservedConcurrency: 1
    environment:
      JAVA_TOOL_OPTIONS:
        -Dconfig.file=okteto.conf
        -DSM_CREDENTIAL_PROVIDER=default

  master-user:
    handler: com.simonmarkets.users.MasterUserEventHandler::handleRequest
    warmup:
      default:
        enabled: false
    # memorySize: 1024
    timeout: 300
    reservedConcurrency: 0
    events:
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusUsers}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"
            detail:
              ns:
                db:
                  - ${self:custom.file.dbUsers}
                coll:
                  - ${self:custom.file.collectionUsers}
          inputTransformer:
            inputPathsMap:
              id: "$.id"
              time: "$.time"
              detail: "$.detail"
            inputTemplate: '{"id": <id>, "time": <time>, "detail":<detail>}'
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusNetworks}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"
            detail:
              ns:
                db:
                  - ${self:custom.file.dbUsers}
                coll:
                  - ${self:custom.file.collectionNetworks}
          inputTransformer:
            inputPathsMap:
              id: "$.id"
              time: "$.time"
              detail: "$.detail"
            inputTemplate: '{"id": <id>, "time": <time>, "detail":<detail>}'
    environment:
      JAVA_TOOL_OPTIONS:
        -Dconfig.file=okteto.conf
        -DSM_CREDENTIAL_PROVIDER=default

  process-users-upload:
    handler: com.simonmarkets.users.ProcessUsersBatchApp::handleRequest
    warmup:
      default:
        enabled: false
    # memorySize: 2048
    timeout: 300
    environment:
      JAVA_TOOL_OPTIONS:
        -Dconfig.file=okteto.conf
        -DSM_CREDENTIAL_PROVIDER=default


resources:
  Resources:
    ManualUserEventBus:
      Type: AWS::Events::EventBus
      Properties:
        Name: ManualUserEvents

    # ManualUserEventBusPolicy:
    #   Type: AWS::Events::EventBusPolicy
    #   Properties:
    #     EventBusName:
    #       Fn::GetAtt:
    #         - ManualUserEventBus
    #         - Name
    #     StatementId: "ManualUserEventBusPolicy"
    #     Statement:
    #       Effect: "Allow"
    #       Principal:
    #         AWS: ${self:custom.file.provider.iam.role}
    #       Action: "events:PutEvents"
    #       Resource:
    #         Fn::GetAtt:
    #           - ManualUserEventBus
    #           - Arn

    ManualUserEventRule:
      Type: AWS::Events::Rule
      Properties:
        Name: "Manual-user-okta-sync"
        EventBusName:
          Fn::GetAtt:
          - ManualUserEventBus
          - Name

        EventPattern:
          source: [ "ManualOktaSync" ]
        State: ENABLED
        Targets:
          - Arn:
              Fn::GetAtt:
              - OktaDashsyncDashsimpleLambdaFunction
              - Arn
            Id: "okta-sync-simple"

    ManualUserOktaSyncPolicy:
      Type: AWS::Lambda::Permission
      Properties:
        Action: lambda:InvokeFunction
        FunctionName:
          Fn::GetAtt:
          - OktaDashsyncDashsimpleLambdaFunction
          - Arn
        Principal: events.amazonaws.com

    NetworkMfaEventRule:
      Type: AWS::Events::Rule
      Properties:
        Name: "Network-mfa-okta-sync"
        EventBusName:
          Fn::GetAtt:
            - ManualUserEventBus
            - Name

        EventPattern:
          source: [ "NetworkMfaOktaSync" ]
        State: ENABLED
        Targets:
          - Arn:
              Fn::GetAtt:
                - OktaDashsyncDashnetworkDashmfaLambdaFunction
                - Arn
            Id: "okta-sync-network-mfa"

    NetworkMfaOktaSyncPolicy:
      Type: AWS::Lambda::Permission
      Properties:
        Action: lambda:InvokeFunction
        FunctionName:
          Fn::GetAtt:
            - OktaDashsyncDashnetworkDashmfaLambdaFunction
            - Arn
        Principal: events.amazonaws.com
