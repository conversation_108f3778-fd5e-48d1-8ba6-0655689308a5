##
# AWS eks cluster settings
##

# AWS eks IRSA settings
serviceAccount:
  created: false
  name: "service-users"
  iamARN: ""

# SIMON service settings
service:
  name: service-users
  scheme: https
  port: 443
  cpu: "2"
  memory: "4G"
  healthCheck:
    # Default: /simon/api/v1/healthcheck
    path: /simon/api/v2/users/healthcheck
    initialDelay: 30
    interval: 5
    timeout: 15
    failureThreshold: 3
