include classpath("service-users-common.conf")

config.resolvers {
  sm {
    region = "us-east-1"
    credentialsProvider = ""
  }
  file {
    root = ""
  }
}

run-mode {
  type = "server-mode"
  http-server-config {
    port = 1984
    interface = "0.0.0.0"
    ssl {
      enabled = false
      keystore {
        type = "JKS"
        file = ""
        password = ""
      }
    }
  }
}

simon-base-path = "https://www.dev.simonmarkets.com/simon/api"
system-user-id = "0oaevl54gplnFvyx70h7"
user-sync-system-user-id = "0oa1lzn1n8kk3EXkV0h8"

authentication {
  type = "jwt"
  jwt {
    source {
      type = "bearer"
      name = ""
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      keys-url = "sm:applicationconfig-okta-validator"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clock-offset-in-seconds = 0
    }
  }
  user-info {
    source {
      type = "header"
      name = "x-userinfo"
    }
  }
}

http-client-config {
  auth: {
    type = OAuth
    client-id = "0oaevl54gplnFvyx70h7"
    client-secret-file = ""
    site = "sm:applicationconfig-issuer-uri"
    authorization-path = ""
    token-path = "/v1/token"
    scope = "read_product_data"
    token-type = {
      type = BearerToken
    }
    client-secret = "sm:Okta-secret"
  }
}

acl-cache-config {
  enabled = false
  config {
    service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
    signin-region = "us-east-1"
  }
}

acl-repository-config {
  service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
  signin-region = "us-east-1"
}

okta-config {
  client-config {
    okta-org-url = "https://auth.dev.simonmarkets.com"
    okta-auth-token = "sm:applicationconfig-okta-api-token"
    cache-ttl = 30.seconds
  }
  service-config {
    create {
      activate = true,
      setPassword = true,
      expirePassword = false
    }
    profile {
      network-name = "alphaNetwork"
      skip-secondary-emails = ["@simonmarkets.com"]
      secondary-email = "<EMAIL>"
    }
  }
  enabled = true
}

okta-sync-config {
  service-entitlements = ["admin"]
  cache-ttl = 15 minutes
  multi-factor-config {
    sms-group-id = "00g1kisw957yzLOEJ0h8"
    totp-group-id = "00g1kit12koGDognL0h8"
    email-group-id = "00g1kiszwnitLMIsG0h8"
    security-question-group-id = "00g1kiszgccQX8oY00h8"
    google-auth-group-id = "00g1kiszvdukp3hNr0h8"
    voice-call-group-id = "00g1kit0r35z23Kxt0h8"
    okta-verify-group-id = "00g1kisv2rhSljbkN0h8"
    sms-voice-call-group-id = "00g1lp0bcnr6JRf8Y0h8"
    enabled = true
  }
}

enhance-access-token-config {
  app-expiry-config {
    "0oa1ggsd0jae9ZSRT0h8" = 60.minutes
    "0oa1lmw4iq2gmYcIk0h8" = 60.minutes
  }
  system-admin-id = "0oaevl54gplnFvyx70h7"
  pilot-role-key = "pilot"
  inline-hook-auth {
    header-name = "X-Simon-Local-Token"
    header-value = "test"
    timeout = 15.seconds
  }
  undefined-sub-claim: "undefined"
  network-token-lifetimes: []
}

mongo-db {
  client {
    app-name = "service-users"
    connection = {
      url = "mongodb://localhost:27017/pipg"
      authentication = {
        type = none
      }
    }
  }
}

encryption {
  endpoint = "https://origin-a.dev.simonmarkets.com/kms/api/v1"
  environment = "staging"
  simon-base-url = "https://origin-a.dev.simonmarkets.com"
  hashing-key-json = "sm:applicationkey-hashing"
}

mapping-client-config {

  base-path = "http://127.0.0.1:3000/api"
  api-key = ""
  http-client-config {
    auth {
      type = NoAuth
    }
  }
}


kafka-config {
  producer = {
    topic = "simon_users"
  }
  consumer = {
    group-id = "icn_to_simon_usersync"
    client-id = ""
    topics = ["icn_users_sync"]
    connection = {
      type = plain
      username = ""
      password = ""
      servers = ["pkc-2396y.us-east-1.aws.confluent.cloud:9092"]
    }
    trace = {
      header = "guid"
      annotation = "traceId"
    }
  }
  connection-url = "pkc-2396y.us-east-1.aws.confluent.cloud:9092"
  is-user-sync-enabled = false
}

addepar-config {
    base-url = "https://icapital.clientdev.addepar.com"
    client-id = "sm:user-networks-service-addepar-oauth-client-id"
    client-secret = "sm:user-networks-service-addepar-oauth-client-secret"
}
