akka.http.server.request-timeout = 60.seconds

info {
  name = "User Service"
  description = "A service responsible for creating / editing users"
  repository = "networks"
  module = "service-users"
  version = "${project.version}"
  revision = "${git.commit.id.abbrev}"
  build-timestamp = "${build.timestamp.millis}"
  product-line = "CrossProduct"
  used-by = ["CrossProduct", "Storefront", "Spectrum", "SMA", "Annuity", "ETF", "StructuredInvestment"]
  owner = "CrossProduct"
  support-distro = ["<EMAIL>"]
  metrics-url = "",
  documentation-url = "https://simonmarkets.atlassian.net/wiki/spaces/ENG/pages/445349961/REST+Easy+Framework"
}

config.resolvers {
  sm {
    region = "us-east-1"
    credentialsProvider = ${SM_CREDENTIAL_PROVIDER}
  }
  file {
    root = ""
  }
}

run-mode {
  type = "server-mode"
  http-server-config {
    port = 443
    interface = "0.0.0.0"
    ssl {
      enabled = true
      keystore {
        type = "JKS"
        file = "/var/cv/creds/simon-vertx-ssl.jks"
        password = "sm:applicationconfig-simon-vertx-ssl-jks-pass"
      }
    }
  }
}

system-routes {
  service-up {
    path = "simon/api/v2/users/uptime"
  }
  service-info {
    path = "simon/api/v2/users/info"
  }
  health-check {
    path = "simon/api/v2/users/healthcheck"
  }
}

acl-sync-config {
  service-entitlements = ["admin"]
}

mongo-db {
  client {
    app-name = "service-users"
    connection = "sm:config:applicationconfig-mongo-auth-pipg"
  }
  users {
    collection = "users"
    database = "pipg"
  }
  users-snapshots {
    collection = "users.snapshots"
    database = "pipg"
  }
  users-impersonation {
    collection = "users.impersonation"
    database = "pipg"
  }
  users-impersonation-approvers {
    collection = "users.impersonation.approvers"
    database = "pipg"
  }
  networks {
    collection = "networks_new"
    database = "pipg"
  }
  networks-snapshots {
    collection = "networks_new.snapshots"
    database = "pipg"
  }
  master-user {
    collection = "master_users"
    database = "pipg"
  }
  tasks {
    collection = "users.tasks",
    database = "pipg"
  }
  inputs {
    collection = "users.tasks.inputs",
    database = "pipg"
  }
  outputs {
    collection = "users.tasks.outputs",
    database = "pipg"
  }
  external-id-types {
    collection = "users.externalIdTypes",
    database = "pipg"
  }
  passport {
    collection = "users.passport",
    database = "pipg"
  }
  transient-state {
    collection = "users.transientState",
    database = "pipg"
  }
  tokens {
    collection = "users.tokens"
    database = "pipg"
  }
}

service-config {
  external-targets = ["MixPanel", "InsuranceTechnologies"]
  paging {
    default-limit = 500
  }

  parallelism = 4
  multi-networks-excluded = ["SIMON Admin"]
  internal-domains = ["simonmarkets.com", "simon.io", "icapitalnetwork.com"]
}

okta-sync-event-bus {
  region: "us-east-1",
  source: "ManualOktaSync",
  target-bus: "ManualUserEvents",
}

network-mfa-sync-event-bus {
  region: "us-east-1",
  source: "NetworkMfaOktaSync",
  target-bus: "ManualUserEvents"
}

impersonation-exclusions = ["iCapital Admin", "SIMON Admin"]

domain-config = [
  {
    domain = "simonmarkets.com"
    landing-page = "SIMON"
    login-modes = ["SSO", "UsernamePassword", "Embedded", "SSOAndUsernamePassword", "UnifiedPassword"]
    default-login-mode = "UsernamePassword"
  },
  {
    domain = "icapitalnetwork.com"
    landing-page = "ICN"
    login-modes = ["ICNUsernamePassword", "SSOAndICNUsernamePassword", "UnifiedPassword"]
    default-login-mode = "ICNUsernamePassword"
  }
]

kafka-config = "sm:config:applicationconfig-usersync-kafka-config"

mapping-client-config = "sm:config:applicationconfig-usersync-mapping-client-config"

passport {
  validity-window = 10.minutes
  code-length = 6
  from = "<EMAIL>"
}

system-user-network-limitation-ignore-list = ["iCapital Admin", "SIMON Admin", "Test Network 1", "Test Network 2", "Test Network 3"]
