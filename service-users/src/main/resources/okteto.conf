include required(classpath("alpha.conf"))
# copied during build time from /okteto-files/okteto-common.conf
include required(classpath("okteto-common.conf"))

dynamo-db-config {
  service-endpoint = ${oktetoLocalstackURL}
  signin-region = "us-east-1"
}

acl-cache-config.config.service-endpoint = ${oktetoLocalstackURL}

acl-repository-config.service-endpoint = ${oktetoLocalstackURL}

okta-config.client-config.proxy = null

mongo-db.client.connection = ${oktetoMongoAuthSecret}
