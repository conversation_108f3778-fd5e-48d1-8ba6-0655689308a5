include classpath("service-users-common.conf")

simon-base-path = "https://origin-a.dev.simonmarkets.com/simon/api"
system-user-id = "0oaevl54gplnFvyx70h7"

user-sync-system-user-id = "0oa1lzn1n8kk3EXkV0h8"

dynamo-db-config {
  service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
  signin-region = "us-east-1"
}

authentication {
  type = "jwt"
  jwt {
    source {
      type = "cookieOrHeader"
      name = "SimSSO:X-Simon-AccessToken"
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      keys-url = "sm:applicationconfig-okta-validator"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clock-offset-in-seconds = 0
      proxy-host = "internal-alpha-squid-proxy-elb-1264059358.us-east-1.elb.amazonaws.com"
      proxy-port = 3128
    }
  }
  user-info {
    source {
      type = "header"
      name = "x-userinfo"
    }
  }
}

http-client-config {
  auth: {
    type = OAuth
    client-id = "0oaevl54gplnFvyx70h7"
    client-secret-file = ""
    site = "sm:applicationconfig-issuer-uri"
    authorization-path = ""
    token-path = "/v1/token"
    scope = "read_product_data"
    token-type = {
      type = BearerToken
    }
    client-secret = "sm:Okta-secret"
  }
  proxy {
    port: 3128
    address: "internal-alpha-squid-proxy-elb-1264059358.us-east-1.elb.amazonaws.com"
  }
}

acl-cache-config {
  enabled = true
  config {
    service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
    signin-region = "us-east-1"
  }
}

acl-repository-config {
  service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
  signin-region = "us-east-1"
}

okta-config {
  client-config {
    okta-org-url = "https://auth.dev.simonmarkets.com"
    okta-auth-token = "sm:applicationconfig-okta-api-token"
    cache-ttl = 30.seconds
    proxy {
      port: 3128
      host: "internal-alpha-squid-proxy-elb-1264059358.us-east-1.elb.amazonaws.com"
    }
  }

  service-config {
    create {
      activate = true,
      setPassword = true,
      expirePassword = false
    }

    profile {
      network-name = "network"
      skip-secondary-emails = ["@simonmarkets.com"]
      secondary-email = "<EMAIL>"
    }
  }

  enabled = true
}

okta-sync-config {
  service-entitlements = ["admin"]
  cache-ttl = 15 minutes
  multi-factor-config {
    sms-group-id = "00g1kisw957yzLOEJ0h8"
    totp-group-id = "00g1kit12koGDognL0h8"
    email-group-id = "00g1kiszwnitLMIsG0h8"
    security-question-group-id = "00g1kiszgccQX8oY00h8"
    google-auth-group-id = "00g1kiszvdukp3hNr0h8"
    voice-call-group-id = "00g1kit0r35z23Kxt0h8"
    okta-verify-group-id = "00g1kisv2rhSljbkN0h8"
    sms-voice-call-group-id = "00g1lp0bcnr6JRf8Y0h8"
    enabled = true
  }
}

mongo-trigger-config {
  database-collections = [
    {
      database = "pipg"
      collection = "users"
    }
  ]
}

enhance-access-token-config {

  app-expiry-config {
    "0oa1ggsd0jae9ZSRT0h8" = 60.minutes
    "0oa1lmw4iq2gmYcIk0h8" = 60.minutes
    "0oa1n4xz7wwbdIJwA0h8" = 14.hours
    "0oa1era5g3hppXRCs0h8" = 14.hours
  }

  system-admin-id = "0oaevl54gplnFvyx70h7"
  pilot-role-key = "pilot"

  inline-hook-auth {
    header-name = "X-Simon-Alpha-Token"
    header-value = "sm:gitlab_internal/kong-alpha-enhance-access-token-inline-hook-secret"
    timeout = 15.seconds
  }
  undefined-sub-claim: "undefined"
  network-token-lifetimes: [{
    network-id = "fd675b3a-6422-4360-9778-44e7f3297883"
    expiration-time = 1800.seconds
  }]
}

encryption {
  endpoint = "https://origin-a.dev.simonmarkets.com/kms/api/v1"
  environment = "staging"
  simon-base-url = "https://origin-a.dev.simonmarkets.com"
  hashing-key-json = "sm:applicationkey-hashing"
}

passport.cc = "<EMAIL>"

addepar-config {
    base-url = "https://icapital.clientdev.addepar.com"
    client-id = "sm:user-networks-service-addepar-oauth-client-id"
    client-secret = "sm:user-networks-service-addepar-oauth-client-secret"
}
