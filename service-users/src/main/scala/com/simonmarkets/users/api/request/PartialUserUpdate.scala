package com.simonmarkets.users.api.request

import com.goldmansachs.marquee.pipg.License
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.users.api.OpenApiDefinitions.{FaNumberOrEmpty, LocationOrEmpty}
import com.simonmarkets.users.common._
import io.simon.openapi.annotation.ClassReference
import io.simon.openapi.annotation.Field._
import io.simon.openapi.definitions.CommonDefinitions
import io.simon.openapi.definitions.CommonDefinitions.NetworkName
import simon.Id.NetworkId

/**
 * Extension of this trait allows for patches to users objects. The design/implementation is imperfect but intentional.
 * The use of optional fields to support various id types instead of using the native [[UniqueUserId]] sealed trait
 * allows for the service to supply reasonable defaults. Also allows for backwards compatibility. Request object that
 * directly hydrates to the typed user id would be nicer, but was unachievable within the constraints
 */
sealed trait PartialUserUpdate {
  def userId: Option[String]

  def userIdType: IdType

  def externalId: Option[ExternalId]

  def networkId: Option[NetworkId]

}

case class LocationsUpdateRequest(
    @Ref(CommonDefinitions.ArbitraryMessage)
    userId: Option[String],

    @Ref(ClassReference(classOf[ExternalId]))
    externalId: Option[ExternalId],

    @Required
    @Ref(ClassReference(classOf[IdType]))
    userIdType: IdType,

    @Description("Array of locations")
    @TypeArgRef(LocationOrEmpty)
    @MinItems(0)
    @MaxItems(10000)
    locations: Set[String],

    @Ref(NetworkName)
    networkId: Option[NetworkId] = None
) extends PartialUserUpdate {
  @Ignore
  val locationsSet: Set[String] = locations.map(_.trim).filterNot(_.isEmpty)
}

case class FANumbersUpdateRequest(
    @Ref(CommonDefinitions.ArbitraryMessage)
    userId: Option[String],

    @Ref(ClassReference(classOf[ExternalId]))
    externalId: Option[ExternalId],

    @Required
    @Ref(ClassReference(classOf[IdType]))
    userIdType: IdType,

    @Description("Array of FA numbers")
    @TypeArgRef(FaNumberOrEmpty)
    @MinItems(0)
    @MaxItems(10000)
    faNumbers: Set[String],

    @Ref(NetworkName)
    networkId: Option[NetworkId] = None
) extends PartialUserUpdate {

  @Ignore
  val faNumbersSet: Set[String] = faNumbers.map(_.trim).filterNot(_ == "")
}

case class UserEmailUpdateRequest(
    @Required
    @Ref(CommonDefinitions.EmailAddress)
    email: String
)

case class UserAccountUpdateRequest(
    @Required
    @Ref(CommonDefinitions.ArbitraryMessage)
    account: String
)

case class AccountUpdateRequest(
    @Ref(CommonDefinitions.ArbitraryMessage)
    userId: Option[String],

    @Ref(ClassReference(classOf[ExternalId]))
    externalId: Option[ExternalId],

    @Required
    @Ref(ClassReference(classOf[IdType]))
    userIdType: IdType,

    @Required
    @Ref(CommonDefinitions.ArbitraryMessage)
    account: String,

    @Ref(NetworkName)
    networkId: Option[NetworkId] = None

) extends PartialUserUpdate

case class EmailUpdateRequest(
    @Ref(CommonDefinitions.ArbitraryMessage)
    userId: Option[String],

    @Ref(ClassReference(classOf[ExternalId]))
    externalId: Option[ExternalId],

    @Required
    @Ref(ClassReference(classOf[IdType]))
    userIdType: IdType,

    @Description("Updated email address")
    email: String,

    @Ref(NetworkName)
    networkId: Option[NetworkId] = None

) extends PartialUserUpdate

case class LicensesUpdateRequest(
    @Ref(CommonDefinitions.ArbitraryMessage)
    userId: Option[String],

    @Ref(ClassReference(classOf[ExternalId]))
    externalId: Option[ExternalId],

    @Required
    @Ref(ClassReference(classOf[IdType]))
    userIdType: IdType,

    @AnyOf(CommonDefinitions.StringInt, CommonDefinitions.EmptyString)
    npn: Option[String] = None,

    @AnyOf(CommonDefinitions.StringInt, CommonDefinitions.EmptyString)
    crd: Option[String] = None,

    @Ref(NetworkName)
    networkId: Option[NetworkId] = None
) extends PartialUserUpdate {

  @Ignore
  val licenseSet: Set[License] = Set(
    clean(npn).map(License.NPN),
    clean(crd).map(License.CRD)
  ).flatten

  private def clean(s: Option[String]) = s.map(_.trim).filterNot(_ == "")
}

case class ExternalIdsUpdateRequest(
    @Ref(CommonDefinitions.ArbitraryMessage)
    userId: Option[String],

    @Ref(ClassReference(classOf[ExternalId]))
    externalId: Option[ExternalId],

    @Required
    @Ref(ClassReference(classOf[IdType]))
    userIdType: IdType,

    @Ref(NetworkName)
    networkId: Option[NetworkId] = None,

    externalIds: Seq[ExternalId]
) extends PartialUserUpdate

case class CusipsUpdateRequest(
    @Ref(CommonDefinitions.ArbitraryMessage)
    userId: Option[String],

    @Ref(ClassReference(classOf[ExternalId]))
    externalId: Option[ExternalId],

    @Required
    @Ref(ClassReference(classOf[IdType]))
    userIdType: IdType,

    @Description("Array of cusips")
    @TypeArgRef(CommonDefinitions.EntityID)
    @MinItems(0)
    @MaxItems(100)
    cusips: Set[String],

    @Ref(NetworkName)
    networkId: Option[NetworkId] = None
) extends PartialUserUpdate {

  @Ignore
  val cusipsSet: Set[String] = cusips.map(_.trim).filterNot(_.isEmpty)
}
