package com.simonmarkets.users.api.routes


import akka.http.scaladsl.model.{ContentTypes, HttpEntity, HttpResponse, StatusCodes}
import akka.http.scaladsl.server.{Directive, Directive0, Route}
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.oauth.domain.request.OAuthTokenRequest
import com.simonmarkets.okta.domain.SimonId
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.resteasy.framework.zio.route.{ErrorResponse, ZIOMarshallingSupport}
import com.simonmarkets.resteasy.requests.AuthorizedDirectives
import com.simonmarkets.users.api.codec.AccessTokenCodecs
import com.simonmarkets.users.api.request.{AuthorizationCodeInlineHookRequest, ClientCredentialInlineHookRequest, SamlBearerHookRequest}
import com.simonmarkets.users.api.response.InlineHookResponse
import com.simonmarkets.users.config.InlineHookConfig
import com.simonmarkets.users.domain._
import com.simonmarkets.users.service.{AccessTokenService, AddeparOAuthTokenService}

case class AccessTokenRoutes(
    authAccessTokenService: AccessTokenService,
    authDirectives: AuthorizedDirectives[UserACL],
    inlineHookConfig: InlineHookConfig,
    addeparTokenService: AddeparOAuthTokenService
) extends DirectivesWithCirce
  with AccessTokenCodecs
  with TraceLogging
  with ZIOMarshallingSupport {

  import authDirectives._

  val baseUrl: Directive0 = pathPrefix("simon" / "api" / "v1" / "access-tokens")

  lazy val routes: Route = inlineHookRoutes ~ adminRoutes ~ oauthTokenRoutes

  lazy val inlineHookRoutes: Route = (baseUrl & unauthenticated & post & inlineHookHeaderValidated) { _ =>

    concat(
      `POST client-credentials/enhance` { (trace: TraceId, req: ClientCredentialInlineHookRequest) =>
        timeout(trace) {
          complete(authAccessTokenService.clientCredentialEnrichment(req)(trace).map(InlineHookResponse.apply))
        }
      },

      `POST authorization-code/enhance` { (trace: TraceId, req: AuthorizationCodeInlineHookRequest) =>
        timeout(trace) {
          complete(authAccessTokenService.authCodeEnrichment(req)(trace).map(InlineHookResponse.apply))
        }
      },

      `POST saml-bearer/enhance` { (trace: TraceId, req: SamlBearerHookRequest) =>
        timeout(trace) {
          complete(authAccessTokenService.samlBearerEnrichment(req)(trace).map(InlineHookResponse.apply))
        }
      }
    )
  }

  lazy val adminRoutes: Route =
    authorized(Capabilities.Admin) { (trace, _) =>
      `GET :userId/dry-run` { userId =>
        complete(
          authAccessTokenService.getScopesAndTokenExpiration(SimonId(userId))(trace).map(InlineHookResponse.apply)
        )
      }
    }

  lazy val oauthTokenRoutes: Route =
    authorized() { (trace, acl) =>
      implicit val traceId: TraceId = trace
      implicit val userACL: UserACL = acl
      concat(
        `POST token` { (provider, req) =>
          provider match {
            case TokenProvider.Addepar =>
              complete(addeparTokenService.exchangeAndStoreToken(req))
            case _ =>
              complete(StatusCodes.BadRequest -> "Unsupported provider")
          }
        },

        `GET token` {
          case TokenProvider.Addepar =>
            complete(addeparTokenService.getStoredToken)
          case _ =>
            complete(StatusCodes.BadRequest -> "Unsupported provider")
        }
      )
    }


  private val `POST client-credentials/enhance`: Directive[(TraceId, ClientCredentialInlineHookRequest)] =
    path("client-credentials" / "enhance") & clientCredentialEntityAndTrace

  private val `POST authorization-code/enhance`: Directive[(TraceId, AuthorizationCodeInlineHookRequest)] =
    path("authorization-code" / "enhance") &
      entity(as[AuthorizationCodeInlineHookRequest]).map(req => (TraceId(req.data.context.request.id), req))

  private val `POST saml-bearer/enhance`: Directive[(TraceId, SamlBearerHookRequest)] =
    path("saml-bearer" / "enhance") &
      entity(as[SamlBearerHookRequest]).map(req => (TraceId(req.data.context.request.id), req))

  private val `GET :userId/dry-run`: Directive[Tuple1[String]] = baseUrl & get & path(Segment / "dry-run") & pathEnd

  private val `POST token`: Directive[(TokenProvider, OAuthTokenRequest)] =
    baseUrl & path("token") & post &
      parameter("provider".as[TokenProvider]) &
      entity(as[OAuthTokenRequest])

  private val `GET token`: Directive[Tuple1[TokenProvider]] =
    baseUrl & path("token") & get &
      parameter("provider".as[TokenProvider])

  val timeout: TraceId => Directive[Unit] = { implicit trace: TraceId =>
    withRequestTimeout(inlineHookConfig.timeout) & withRequestTimeoutResponse { _ =>
      log.error("Access token inline hook timed out")
      HttpResponse(
        StatusCodes.OK,
        entity = HttpEntity(ContentTypes.`application/json`, InlineHookResponse(Nil).asJsonStr)
      )
    }
  }

  implicit val domainErrorResponse: ErrorResponse[AccessTokenServiceError] = {
    case e: NotAuthorized => HttpError.unauthorized(e.message).httpResponse
    case e: UserNotFound => HttpError.notFound(e.message).httpResponse
    case e: GeneralNotFound => HttpError.notFound(e.message).httpResponse
    case e: OktaDecodeError => HttpError.internalServerError(e.message).httpResponse
    case e: UnableToEnrichFromIdService => HttpError.internalServerError(e.message).httpResponse
    case e: AccessTokenServiceError => HttpError.internalServerError(e.message).httpResponse
  }

  private def clientCredentialEntityAndTrace: Directive[(TraceId, ClientCredentialInlineHookRequest)] = {
    entity(as[ClientCredentialInlineHookRequest]).map {
      req: ClientCredentialInlineHookRequest =>
        val trace = TraceId(req.data.context.request.id)
        (trace, req)
    }
  }

  private def inlineHookHeaderValidated: Directive0 = {
    optionalHeaderValueByName(inlineHookConfig.headerName).flatMap {
      case Some(value) if value == inlineHookConfig.headerValue => pass
      case _ => complete(HttpError.forbidden("Unable to validate the auth token").httpResponse)
    }
  }

}
