package com.simonmarkets.users.api

import com.simonmarkets.users.api.request._
import com.simonmarkets.users.api.response.PartialUserUpdateError
import com.simonmarkets.users.common.api.request.UpsertUserRequest
import com.simonmarkets.users.common.api.response.UserView
import io.simon.openapi.annotation.Field._
import io.simon.openapi.annotation.{ClassReference, OpenApiType, Reference}

object OpenApiDefinitions {

  @Type(OpenApiType.String)
  @Pattern("^[-\\w\\s&\\\\\\\\\\/'~!*():^{}|+,'.]{1,128}")
  case object LocationOrEmpty extends Reference

  @Type(OpenApiType.String)
  @Pattern("^[\\w-]{0,128}$")
  case object FaNumberOrEmpty extends Reference

  @Ref(ClassReference(classOf[Page[UserView]]))
  case object UserViewPageDefinition

  @Minimum(1)
  @Maximum(20000)
  @Type(OpenApiType.Int)
  @Description("Number of users which should be returned on one page")
  case object Limit

  @Ref(ClassReference(classOf[Seq[LocationsUpdateRequest]]))
  case object UserLocationsBatch

  @Ref(ClassReference(classOf[Seq[FANumbersUpdateRequest]]))
  case object UserFANumbersBatch

  @Ref(ClassReference(classOf[UserEmailUpdateRequest]))
  case object EmailUpdate

  @Ref(ClassReference(classOf[UserAccountUpdateRequest]))
  case object AccountUpdate

  @Ref(ClassReference(classOf[Seq[PartialUserUpdateError]]))
  case object PartialUpdateBatchResponse

  @Ref(ClassReference(classOf[Seq[LicensesUpdateRequest]]))
  case object LicenseUpdateRequestBatch

  @Ref(ClassReference(classOf[Seq[UpsertUserRequest]]))
  case object BatchUserInsertRequest

  @Ref(ClassReference(classOf[MasterUserRequest]))
  case object MasterUserQuery

}
