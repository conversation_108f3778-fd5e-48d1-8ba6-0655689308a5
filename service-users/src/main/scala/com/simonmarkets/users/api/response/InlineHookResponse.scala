package com.simonmarkets.users.api.response

import com.simonmarkets.users.domain.{Human<PERSON>ser, OktaBaseUser, SystemUser}
import com.simonmarkets.util.{EnumEntry, ProductEnums}

import scala.concurrent.duration.Duration

case class InlineHookResponse(commands: List[PatchAccessToken])

case class PatchAccessToken(`type`: String, value: List[ValueItem])

object PatchAccessToken {
  def apply(value: List[ValueItem]) = new PatchAccessToken("com.okta.access.patch", value)
}

sealed trait ValueItem {
  val op: Operation
  val path: Path
  val value: ValueItemType
}

sealed trait ValueItemType

case class LongValueItemType(value: Long) extends ValueItemType

case class StringValueItemType(value: String) extends ValueItemType

case class StringSetValueItemType(value: Set[String]) extends ValueItemType

case class ActionValueItemType(value: Action) extends ValueItemType

sealed trait Operation extends EnumEntry

object Operation extends ProductEnums[Operation] {

  case object add extends Operation

  case object remove extends Operation

  case object replace extends Operation

  override def Values: Seq[Operation] = add :: remove :: replace :: Nil
}

sealed trait Path extends EnumEntry

object Path extends ProductEnums[Path] {

  case object `/claims/scopes` extends Path

  case object `/token/lifetime/expiration` extends Path

  case object `/claims/trace` extends Path

  case object `/claims/result` extends Path

  case object `/claims/sub` extends Path

  case object `/claims/impersonator` extends Path

  case object `/claims/icnId` extends Path

  case object `/claims/impersonatorIcnId` extends Path

  case object `/claims/wlp` extends Path

  override def Values: Seq[Path] = List(
    `/claims/scopes`,
    `/claims/sub`,
    `/token/lifetime/expiration`,
    `/claims/trace`,
    `/claims/result`,
    `/claims/impersonator`,
    `/claims/icnId`,
    `/claims/impersonatorIcnId`,
    `/claims/wlp`
  )
}

case class ReplaceScopes(value: StringSetValueItemType) extends ValueItem {
  val op = Operation.replace
  val path = Path.`/claims/scopes`
}

object ReplaceScopes {
  def apply(value: Set[String]) = new ReplaceScopes(StringSetValueItemType(value))
}

case class AddSubject(value: StringValueItemType) extends ValueItem {
  val path = Path.`/claims/sub`
  val op = Operation.add
}

object AddSubject {
  def apply(value: String) = new AddSubject(StringValueItemType(value))
}

case class UpdateTokenExpiration(value: LongValueItemType) extends ValueItem {
  val op = Operation.replace
  val path = Path.`/token/lifetime/expiration`
}

object UpdateTokenExpiration {
  def apply(value: Duration) = new UpdateTokenExpiration(LongValueItemType(value.toSeconds))
}

case class AddTrace(value: StringValueItemType) extends ValueItem {
  val op = Operation.add
  val path = Path.`/claims/trace`
}

object AddTrace {
  def apply(value: String) = new AddTrace(StringValueItemType(value))
}

case class AddImpersonator(value: StringValueItemType) extends ValueItem {
  val op = Operation.add
  val path = Path.`/claims/impersonator`
}

object AddImpersonator {
  def apply(value: String) = new AddImpersonator(StringValueItemType(value))
}

case class AddIcnId(value: StringValueItemType) extends ValueItem {
  val op = Operation.add
  val path = Path.`/claims/icnId`
}

object AddIcnId {
  def apply(value: String): AddIcnId = AddIcnId(StringValueItemType(value))
}

case class AddImpersonatorIcnId(value: StringValueItemType) extends ValueItem {
  val op: Operation = Operation.add
  val path: Path = Path.`/claims/impersonatorIcnId`
}

object AddImpersonatorIcnId {
  def apply(value: String): AddImpersonatorIcnId = AddImpersonatorIcnId(StringValueItemType(value))
}

case class Addwlp(value: StringValueItemType) extends ValueItem {
  val op = Operation.add
  val path = Path.`/claims/wlp`
}

object Addwlp {
  def apply(value: String): Addwlp = Addwlp(StringValueItemType(value))
}

case class AddAction(value: ActionValueItemType) extends ValueItem {
  val op = Operation.add
  val path = Path.`/claims/result`
}

object AddAction {
  def apply(value: Action) = new AddAction(ActionValueItemType(value))
}

sealed trait Action extends EnumEntry

object Action extends ProductEnums[Action] {
  case object created extends Action

  case object updated extends Action

  case object `no-change` extends Action

  override def Values: Seq[Action] = created :: updated :: `no-change` :: Nil
}

sealed abstract class InlineHookFailure(message: String) extends Exception(message)

case class InvalidHeader(message: String) extends InlineHookFailure(message)

case class UserNotFound(message: String) extends InlineHookFailure(message)

case class UnexpectedFailure(message: String) extends InlineHookFailure(message)


object InlineHookResponse {

  def apply(user: OktaBaseUser): InlineHookResponse = {
    val standard: List[ValueItem] = List(
      ReplaceScopes(user.scopes),
      AddTrace(user.trace.toString)
    ) ++ List(
      user.icnId.map(AddIcnId.apply),
      user.wlp.map(Addwlp.apply),
      user.expiration.map(UpdateTokenExpiration.apply)
    ).flatten

    val allPatches = user match {
      case h: HumanUser =>
        standard ++
          h.impersonatorUserId.map(AddImpersonator.apply) ++
          h.impersonatorIcnId.map(AddImpersonatorIcnId.apply) :+
          AddSubject(h.userId)
      case _: SystemUser => standard
    }

    InlineHookResponse(PatchAccessToken(allPatches) :: Nil)
  }

}
