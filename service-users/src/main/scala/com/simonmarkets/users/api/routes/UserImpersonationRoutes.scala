package com.simonmarkets.users.api.routes

import akka.http.scaladsl.server.Route
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.resteasy.requests.AuthorizedDirectives
import com.simonmarkets.users.api.request.{ApproveUserImpersonationRequest, ForceCompleteImpersonationRequest, InsertUserImpersonationRequest, UpsertUserImpersonationApproversRequest}
import com.simonmarkets.users.service.{UserImpersonationApproversService, UserImpersonationService}

case class UserImpersonationRoutes(
    userImpersonationService: UserImpersonationService,
    userImpersonationApproversService: UserImpersonationApproversService,
    authDirectives: AuthorizedDirectives[UserACL]
) extends TraceLogging with DirectivesWithCirce with JsonCodecs {

  import authDirectives._

  def routes: Route =
    pathPrefix("simon" / "api" / "v2" / "users" / "impersonate") {

      authorizedUser() { (tid, user, acl) =>
        implicit val traceId: TraceId = tid
        implicit val userACL: UserACL = acl
        concat(
          (get & path(Segment)) { id =>
            rejectEmptyResponse {
              complete(userImpersonationService.getById(id))
            }
          },
          (pathEnd & post) {
            entity(as[InsertUserImpersonationRequest]) { request =>
              complete(userImpersonationService.insert(request, user.token))
            }
          },
          path("complete") {
            post {
              complete(userImpersonationService.complete(user.impersonatorUserId))
            }
          },
          path("status") {
            get {
              parameters("impersonatedUserId") { impersonatedUserId =>
                complete(userImpersonationService.getImpersonationStatus(impersonatedUserId))
              }
            }
          },
          path("approve") {
            post {
              entity(as[ApproveUserImpersonationRequest]) { request =>
                complete(userImpersonationService.approve(request))
              }
            }
          }
        )
      } ~
      authorized(Capabilities.ReadOnlyAdmin, Capabilities.Admin) { (tid, acl) =>
        implicit val traceId: TraceId = tid
        implicit val userACL: UserACL = acl
        concat(
          path("force-complete") {
            post {
              entity(as[ForceCompleteImpersonationRequest]) { request =>
                complete(userImpersonationService.forceComplete(request))
              }
            }
          }
        )
      } ~
      authorized(Capabilities.Admin) { (tid, acl) =>
        implicit val traceId: TraceId = tid
        implicit val userACL: UserACL = acl
        concat(
          pathPrefix("approvers") {
            concat(
              (get & path(Segment)) { networkId =>
                complete(userImpersonationApproversService.getByNetworkId(networkId))
              },
              (put & pathEnd) {
                entity(as[UpsertUserImpersonationApproversRequest]) { request =>
                  complete(userImpersonationApproversService.upsertUserImpersonationApprovers(request))
                }
              }
            )
          }
        )
      }
    }
}
