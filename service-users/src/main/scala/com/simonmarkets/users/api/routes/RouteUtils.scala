package com.simonmarkets.users.api.routes

import akka.http.scaladsl.server.Directives.complete
import akka.http.scaladsl.server.ExceptionHandler
import com.simonmarkets.http.HttpError
import com.simonmarkets.mongodb.error.Conflict
import com.simonmarkets.resteasy.HttpErrorHandler

object RouteUtils {

  val customExceptionHandler: ExceptionHandler = HttpErrorHandler.errorHandler.withFallback(
    ExceptionHandler {
      case ex: Conflict => complete(HttpError.conflict(ex.message).httpResponse)
    }
  )

}
