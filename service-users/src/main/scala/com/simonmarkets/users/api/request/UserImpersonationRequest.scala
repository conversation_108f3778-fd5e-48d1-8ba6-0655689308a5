package com.simonmarkets.users.api.request

import com.simonmarkets.users.domain.ImpersonationStatus

case class ApproveUserImpersonationRequest(status: ImpersonationStatus, id: String)

case class InsertUserImpersonationRequest(impersonatedUserId: String, reason: String, ticketNumber: Option[String])

case class ForceCompleteImpersonationRequest(impersonatorUserId: String)

case class UpsertUserImpersonationApproversRequest(networkId: String, userIds: Set[String])
