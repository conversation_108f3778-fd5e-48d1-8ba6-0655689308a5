package com.simonmarkets.users.api.request

import com.simonmarkets.util.{EnumEntry, SafeEnums}

import java.time.Instant


trait InlineHookRequest {
  val source: String
  val eventId: String
  val eventTime: Instant
  val eventType: String
  val data: Data
}

trait Data {
  val context: Context
  val access: Access
}

trait Context {
  val request: Request
}

case class ClientCredentialInlineHookRequest(
    source: String,
    eventId: String,
    eventTime: Instant,
    eventType: String,
    data: ClientCredentialData
) extends InlineHookRequest

case class ClientCredentialData(context: ClientCredentialContext, access: Access) extends Data

case class ClientCredentialContext(request: Request) extends Context

case class AuthorizationCodeInlineHookRequest(
    source: String,
    eventId: String,
    eventTime: Instant,
    eventType: String,
    data: AuthorizationCodeData
) extends InlineHookRequest

case class SamlBearerHookRequest(
    source: String,
    eventId: String,
    eventTime: Instant,
    eventType: String,
    data: SamlCodeData
) extends InlineHookRequest

case class SamlCodeData(context: SamlCodeContext, access: Access) extends Data

case class SamlCodeContext(request: Request, user: UserInfo, protocol: Protocol) extends Context

case class AuthorizationCodeData(context: AuthorizationCodeContext, access: Access) extends Data

case class AuthorizationCodeContext(
    request: Request,
    session: Session,
    user: UserInfo,
    protocol: Protocol
) extends Context

case class Session(
    id: String,
    userId: String,
    login: String,
    createdAt: Instant,
    expiresAt: Instant,
    status: String,
    lastPasswordVerification: Option[Instant],
    amr: List[String],
    idp: Idp,
    mfaActive: Boolean
)

case class Protocol(request: ProtocolRequest, client: ProtocolClient)

case class ProtocolRequest(redirect_uri: Option[String] = None)

case class ProtocolClient(id: String, name: String, `type`: String)

case class Idp(id: String, `type`: IdpType)

sealed trait IdpType extends EnumEntry

object IdpType extends SafeEnums[IdpType] {

  case object OKTA extends IdpType

  case object ACTIVE_DIRECTORY extends IdpType

  case object LDAP extends IdpType

  case object FEDERATION extends IdpType

  case object SOCIAL extends IdpType

  case object EnumNotFound extends IdpType

  override def Values: Seq[IdpType] = OKTA :: ACTIVE_DIRECTORY :: LDAP :: FEDERATION :: SOCIAL :: Nil
}

case class UserInfo(id: String, passwordChanged: Option[Instant], profile: Profile)

case class Profile(login: String, firstName: String, lastName: String, locale: String, timeZone: String)

case class Request(id: String, method: String, url: Url, ipAddress: String)

case class Url(value: String)

case class Access(claims: AccessClaims, token: Token)

case class AccessClaims(sub: String)

case class Token(lifetime: Lifetime)

case class Lifetime(expiration: Long)
