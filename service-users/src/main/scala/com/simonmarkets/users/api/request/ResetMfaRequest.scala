package com.simonmarkets.users.api.request

import com.simonmarkets.okta.domain.FactorType
import io.simon.openapi.annotation.Field.Description
import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues

case class ResetMfaRequest(
    @Description("Sync provided mfa factor types")
    factorTypes: Set[FactorType] = Set.empty,
    @Description("An optional parameter that allows removal of the the phone factor (SMS/Voice) as both a recovery method and a factor")
    removeRecoveryEnrollment: Option[<PERSON>olean],
    @Description("Delete all mfa factors")
    deleteAll: Option[<PERSON>olean]
)

@EnumValues("Password", "Mfa")
sealed trait ResetOperation extends EnumEntry

object ResetOperation extends ProductEnums[ResetOperation] {
    case object Password extends ResetOperation

    case object Mfa extends ResetOperation

    override def Values: Seq[ResetOperation] = Seq(Password, Mfa)
}
