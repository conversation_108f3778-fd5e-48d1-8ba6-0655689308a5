package com.simonmarkets.users.api.response

import scala.language.implicitConversions

sealed trait PrimitiveWrapper

case class StringWrapper(v: String) extends PrimitiveWrapper

case class LongWrapper(v: Long) extends PrimitiveWrapper

object PrimitiveWrapper {
  implicit def toStringWrapper(v: String): PrimitiveWrapper = StringWrapper(v)

  implicit def toLongWrapper(v: Long): PrimitiveWrapper = LongWrapper(v)
}

