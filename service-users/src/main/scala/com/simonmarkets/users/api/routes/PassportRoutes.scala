package com.simonmarkets.users.api.routes

import akka.http.scaladsl.server.AuthenticationFailedRejection.CredentialsRejected
import akka.http.scaladsl.server.{AuthenticationFailedRejection, Directive, Directive0, Route}
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.resteasy.authn.akkahttp.DecodedToken.DecodedUser
import com.simonmarkets.resteasy.authn.akkahttp.{DecodedToken, providers}
import com.simonmarkets.resteasy.requests.AuthorizedDirectives
import com.simonmarkets.users.api.request.{PassportConfirmation, PassportVerificationRequest}
import com.simonmarkets.users.service.PassportService

case class PassportRoutes(
    service: PassportService,
    authDirectives: AuthorizedDirectives[UserACL],
) extends DirectivesWithCirce with JsonCodecs with TraceLogging {

  import authDirectives._

  def routes: Route = pathPrefix("simon" / "api" / "v2" / "users" / "passport") {
    authorized() { (_traceId, _userACL) =>

      implicit val (traceId, userAcl) = (_traceId, _userACL)

      (rejectImpersonators & extractJti) { (authToken, jti) =>

        concat(

          (path("initialize") & post & entity(as[PassportVerificationRequest])) { request =>
            onSuccess(service.initializeVerification(userAcl, request, authToken, jti)) {
              case Some(cookie) => setCookie(cookie) {
                complete(())
              }
              case None => complete(())
            }
          },

          (path("confirm") & post & entity(as[PassportConfirmation])) { request =>
            complete(service.confirm(userAcl, request, jti))
          },

          (pathEnd & delete) {
            complete(service.removePassport(userAcl))
          }

        )
      }
    }
  }

  private def extractJti: Directive[(String, String)] =
    attribute(DecodedToken.tokenAttribute).collect(
      { case user: DecodedUser if user.user.jti.isDefined => (user.user.token, user.user.jti.get) },
      AuthenticationFailedRejection(CredentialsRejected, providers.challenge)
    )

  private def rejectImpersonators[T]: Directive0 = {
    attribute(DecodedToken.tokenAttribute).require(
      {
        case user: DecodedUser => user.user.impersonatorUserId.isEmpty
        case _ => false
      },
      {
        AuthenticationFailedRejection(CredentialsRejected, providers.challenge)
      }
    )
  }


}
