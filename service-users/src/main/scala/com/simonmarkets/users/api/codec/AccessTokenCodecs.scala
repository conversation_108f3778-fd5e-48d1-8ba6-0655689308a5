package com.simonmarkets.users.api.codec

import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.oauth.domain.request.OAuthTokenRequest
import com.simonmarkets.users.api.request.{AuthorizationCodeInlineHookRequest, ClientCredentialInlineHookRequest, SamlBearerHookRequest}
import com.simonmarkets.users.api.response._
import io.circe._
import io.circe.generic.semiauto.deriveCodec

trait AccessTokenCodecs extends JsonCodecs {

  implicit lazy val clientCredentialInlineHookRequestCodec: Codec[ClientCredentialInlineHookRequest] = deriveCodec

  implicit lazy val authorizationCodeInlineHookRequestCodec: Codec[AuthorizationCodeInlineHookRequest] = deriveCodec

  implicit lazy val samlBearerInlineHookRequestCodec: Codec[SamlBearerHookRequest] = deriveCodec

  implicit lazy val inlineHookResponseCodec: Codec[InlineHookResponse] = deriveCodec

  implicit lazy val oauthTokenRequestCodec: Codec[OAuthTokenRequest] = deriveCodec

  implicit val encodeEvent: Encoder[ValueItemType] = Encoder.instance {
    case str: StringValueItemType => str.value.asJson
    case act: ActionValueItemType => act.value.asJson
    case long: LongValueItemType => long.value.asJson
    case set: StringSetValueItemType => set.value.asJson
  }

  implicit val genericValueItemEncoder: Encoder[ValueItem] = (valueItem: ValueItem) => Json.obj(
    ("op", valueItem.op.asJson),
    ("path", valueItem.path.asJson),
    ("value", valueItem.value.asJson)
  )

}
