package com.simonmarkets.users.api.routes

import akka.http.scaladsl.server._
import akka.stream.Materializer
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.mongodb.trigger.codec.MongoTriggerJsonCodecs
import com.simonmarkets.mongodb.trigger.event.MongoEvent
import com.simonmarkets.networks.common.clients.usersync.UserSyncMappingClient
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.resteasy.framework.zio.route.ZIOMarshallingSupport
import com.simonmarkets.resteasy.requests.AuthorizedDirectives
import com.simonmarkets.resteasy.utils.FutureOps._
import com.simonmarkets.users.api.request._
import com.simonmarkets.users.common.IdType.UserId
import com.simonmarkets.users.common.api.request.{UpsertUserRequest, BumpUsersVersionRequest, OktaSyncRequest}
import com.simonmarkets.users.common.api.response.UserView
import com.simonmarkets.users.common.trigger.UserPayload
import com.simonmarkets.users.config.{InlineHookConfig, KafkaConfig}
import com.simonmarkets.users.service.{UserEventPublisher, UserServiceImpl}
import zio.{TaskLayer, ZIO}

import scala.concurrent.ExecutionContext

case class UserServiceRoutes(
    service: UserServiceImpl,
    authDirectives: AuthorizedDirectives[UserACL],
    inlineHookAuthConfig: InlineHookConfig,
    kafkaConfig: KafkaConfig,
    zEnv: TaskLayer[UserEventPublisher],
    mappingClient: UserSyncMappingClient,
)(implicit ec: ExecutionContext, mat: Materializer)
  extends TraceLogging
    with DirectivesWithCirce
    with MongoTriggerJsonCodecs
    with ZIOMarshallingSupport {

  import authDirectives._

  lazy val routes: Route = pathPrefix("simon" / "api" / "v2" / "users") {
    concat(

      authorized() { (_traceId, _userACL) =>
        implicit val (traceId, userAcl) = (_traceId, _userACL)

        concat(

          (post & path("updated")) {
            entity(as[MongoEvent[UserPayload]]) { mongoEvent =>
              complete {
                if (kafkaConfig.isUserSyncEnabled) {
                  ZIO.serviceWithZIO[UserEventPublisher](_.publishUserEventMessage(mongoEvent))
                    .provideLayer(zEnv)
                } else {
                  HttpError.internalServerError("User Sync is disabled.").httpResponse
                }
              }
            }
          },

          `POST /locations` {
            entity(as[Seq[LocationsUpdateRequest]]) { request =>
              complete(service.partiallyUpdateUser(userAcl, request))
            }
          },
          `POST /fa-numbers` {
            entity(as[Seq[FANumbersUpdateRequest]]) { request =>
              complete(service.partiallyUpdateUser(userAcl, request))
            }
          },
          `POST /licenses` {
            entity(as[Seq[LicensesUpdateRequest]]) { requests =>
              complete(service.partiallyUpdateUser(userAcl, requests))
            }
          },
          `PUT /email` {
            entity(as[UserEmailUpdateRequest]) { request =>
              val req = Seq(
                EmailUpdateRequest(Some(userAcl.userId), None, UserId, request.email, None)
              )
              complete(service.partiallyUpdateUser(userAcl, req))
            }
          },
          `PUT /account` {
            entity(as[UserAccountUpdateRequest]) { request =>
              val req = Seq(
                AccountUpdateRequest(Some(userAcl.userId), None, UserId, request.account, None)
              )
              complete(service.partiallyUpdateUser(userAcl, req))
            }
          },
          `POST /external-ids` {
            entity(as[Seq[ExternalIdsUpdateRequest]]) { request =>
              complete(service.partiallyUpdateUser(userAcl, request))
            }
          },
          `POST /cusips` {
            entity(as[Seq[CusipsUpdateRequest]]) { requests =>
              complete(service.partiallyUpdateUser(userAcl, requests))
            }
          },
          `GET /:id` { userId =>
            parameters("isICapitalUserId".?) {
              isICapitalUserIdOpt =>
                val isICapitalUserId = isICapitalUserIdOpt.exists(_.toBoolean)
                complete(service.getUserViewById(userAcl, userId, isICapitalUserId).flattenOption())
            }
          },
          `POST /` {
            entity(as[UpsertUserRequest]) { request =>
              complete(service.handleInsertRequest(userAcl, request).map(userAndSecret => UserView(userAndSecret._1, userAndSecret._2)))
            }
          },
          `POST /bulk-upsert` {
            entity(as[Seq[UpsertUserRequest]]) { requests =>
              complete(service.handleBulkUpsertRequest(userAcl, requests))
            }
          },
          `PUT /:id` { userId =>
            entity(as[UpsertUserRequest]) { request =>
              complete(service.handleUpdateRequest(userAcl, request.copy(id = Some(userId))).map(UserView(_)))
            }
          },
          `POST /:id/deactivate` { userId =>
            complete(service.deactivateUser(userAcl, userId))
          },
          `GET /` {
            parameters("limit".as[Int].?, "next".?) {
              (limit: Option[Int], next) => {
                parameterMultiMap { parameters =>
                  val filters = parameters.filterKeys(key => key != "limit" && key != "next")
                  complete(service.getUserPage(userAcl, limit, next, filters))
                }
              }
            }
          },
          `GET /:id/secrets` { appId =>
            complete(service.listApplicationSecrets(userAcl, appId))
          },
          `POST /:id/secrets` { appId =>
            complete(service.createApplicationSecret(userAcl, appId))
          },
          `POST /:id/secrets/:secretId/activate` { (userId, secretId) =>
            complete(service.activateApplicationSecret(userAcl, userId, secretId))
          },
          `POST /:id/secrets/:secretId/deactivate` { (userId, secretId) =>
            complete(service.deactivateApplicationSecret(userAcl, userId, secretId))
          }
        )
      },

      authorized(Capabilities.Admin) { (_traceId, _userAcl) =>
        implicit val (traceId, userAcl) = (_traceId, _userAcl)

        concat(
          (post & path("okta-sync") & pathEnd) {
            entity(as[OktaSyncRequest]) { request =>
              complete(service.handleOktaSyncRequest(userAcl, request))
            }
          },

          `POST /:id/activate` { userId =>
            entity(as[ActivateUserRequest]) { request =>
              complete(service.activateUser(userAcl, userId, request.sendEmail.getOrElse(true)))
            }
          },

          `POST /:id/reset-password` { userId =>
            complete(service.resetPassword(userAcl, userId))
          },

          `POST /:id/reset-mfa` { userId =>
            entity(as[ResetMfaRequest]) { request =>
              complete(service.resetMfa(userAcl, userId, request))
            }
          },

          (post & path("bump-versions")) {
            entity(as[BumpUsersVersionRequest]) { request =>
              complete(service.handleBumpUsersRequest(userAcl, request))
            }
          },
          pathPrefix("expire-cache") {
            concat(
              (post & path("mapping-client")) {
                complete(mappingClient.invalidateCache)
              }
            )
          },
          `GET /:id/internal` { userId =>
            complete(service.getUserById(userAcl, userId))
          }
        )
      }
    )
  }

  def `GET /:id`: Directive1[String] = get & path(Segment)

  def `GET /`: Directive0 = get & pathEndOrSingleSlash

  def `GET /:id/internal`: Directive1[String] = get & path(Segment / "internal")

  def `GET /:id/secrets`: Directive1[String] = get & path(Segment / "secrets")

  def `POST /:id/secrets`: Directive1[String] = post & path(Segment / "secrets")

  def `POST /:id/secrets/:secretId/activate`: Directive[(String, String)] = post & path(Segment / "secrets" / Segment / "activate")

  def `POST /:id/secrets/:secretId/deactivate`: Directive[(String, String)] = post & path(Segment / "secrets" / Segment / "deactivate")

  def `POST /`: Directive0 = post & pathEndOrSingleSlash

  def `POST /bulk-upsert`: Directive0 = post & pathPrefix("bulk-upsert") & pathEndOrSingleSlash

  def `PUT /:id`: Directive1[String] = put & path(Segment)

  def `POST /:id/deactivate`: Directive1[String] = post & path(Segment / "deactivate")

  def `POST /:id/activate`: Directive1[String] = post & path(Segment / "activate")

  def `POST /:id/reset-password`: Directive1[String] = post & path(Segment / "reset-password")

  def `POST /:id/reset-mfa`: Directive1[String] = post & path(Segment / "reset-mfa")

  val `POST /locations`: Directive0 = post & pathPrefix("locations") & pathEndOrSingleSlash

  val `POST /fa-numbers`: Directive0 = post & pathPrefix("fa-numbers") & pathEndOrSingleSlash

  val `PUT /email`: Directive0 = put & pathPrefix("email") & pathEndOrSingleSlash

  val `PUT /account`: Directive0 = put & pathPrefix("account") & pathEndOrSingleSlash

  val `POST /licenses`: Directive0 = post & path("licenses")

  val `POST /external-ids`: Directive0 = post & path("external-ids")

  val `POST /cusips`: Directive0 = post & path("cusips")

}
