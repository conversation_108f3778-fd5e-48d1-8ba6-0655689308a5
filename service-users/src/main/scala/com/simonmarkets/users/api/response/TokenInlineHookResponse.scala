package com.simonmarkets.users.api.response

case class TokenInlineHookResponse(commands: List[Command])

case class Command(`type`: String, `value`: List[Value])

case class Value(op: String, path: String, `value`: PrimitiveWrapper)

object OperationType {
  val Add: String = "add"
  val Replace: String = "replace"
}

object CommandType {
  val AccessPatch: String = "com.okta.access.patch"
  val IdentityPatch: String = "com.okta.identity.patch"
}

object Paths {
  val ScopeClaim: String = "/claims/scopes/-"
  val ScpClaim: String = "/claims/scp/-"
  val Expiration: String = "/token/lifetime/expiration"
  val TraceClaim: String = "/claims/trace"
  val ActionClaim: String = "/claims/result"
  val SubClaim: String = "/claims/sub"
}

object ActionClaim {
  val Created: String = "created"
  val Updated: String = "updated"
  val NoChange: String = "no-change"
}