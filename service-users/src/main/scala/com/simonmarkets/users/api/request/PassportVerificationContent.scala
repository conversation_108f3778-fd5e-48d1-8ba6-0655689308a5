package com.simonmarkets.users.api.request

import io.circe.Encoder
import io.circe.generic.semiauto.deriveEncoder

case class PassportVerificationContent(
    users: Seq[PassportCandidate],
    confirmationCode: String
)

object PassportVerificationContent {

  implicit val enc: Encoder[PassportVerificationContent] = deriveEncoder[PassportVerificationContent]
  implicit val encCandidate: Encoder[PassportCandidate] = deriveEncoder[PassportCandidate]

}

case class PassportCandidate(
    email: String,
    whiteLabelPartnerName: String
)
