package com.simonmarkets.users.api.codec

import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.users.api.request._
import com.simonmarkets.users.api.response.PartialUserUpdateError
import com.simonmarkets.users.common.api.request.{UpsertUserRequest, OktaSyncRequest}
import com.simonmarkets.users.common.api.response.{OktaSyncResponse, UserInsertFailure, UserView}
import io.circe.Codec
import io.circe.generic.semiauto.deriveCodec

trait UserServiceCodecs extends JsonCodecs {

  implicit lazy val locationsUpdateRequestCodec: Codec[LocationsUpdateRequest] = deriveCodec

  implicit lazy val partialUserUpdateErrorCodec: Codec[PartialUserUpdateError] = deriveCodec

  implicit lazy val faNumbersUpdateRequestCodec: Codec[FANumbersUpdateRequest] = deriveCodec

  implicit lazy val licensesUpdateRequestCodec: Codec[LicensesUpdateRequest] = deriveCodec

  implicit lazy val userEmailUpdateRequestCodec: Codec[UserEmailUpdateRequest] = deriveCodec

  implicit lazy val userAccountUpdateRequestCodec: Codec[UserAccountUpdateRequest] = deriveCodec

  implicit lazy val externalIdsUpdateRequestCodec: Codec[ExternalIdsUpdateRequest] = deriveCodec

  implicit lazy val cusipsUpdateRequestCodec: Codec[CusipsUpdateRequest] = deriveCodec

  implicit lazy val userViewCodec: Codec[UserView] = deriveCodec

  implicit lazy val upsertUserRequestCodec: Codec[UpsertUserRequest] = deriveCodec

  implicit lazy val userInsertFailureCodec: Codec[UserInsertFailure] = deriveCodec

  implicit lazy val activateUserRequestCodec: Codec[ActivateUserRequest] = deriveCodec

  implicit lazy val oktaSyncRequestCodec: Codec[OktaSyncRequest] = deriveCodec

  implicit lazy val oktaSyncResponseCodec: Codec[OktaSyncResponse] = deriveCodec

  implicit lazy val resetMfaRequestCodec: Codec[ResetMfaRequest] = deriveCodec

}
