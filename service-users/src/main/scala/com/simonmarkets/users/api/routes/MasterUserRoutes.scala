package com.simonmarkets.users.api.routes

import akka.http.scaladsl.model.headers.`X-Forwarded-Host`
import akka.http.scaladsl.server.Route
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.logging.TraceId
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.resteasy.requests.AuthorizedDirectives
import com.simonmarkets.users.api.request.MasterUserRequest
import com.simonmarkets.users.service.MasterUserService

case class MasterUserRoutes(
    masterUserService: MasterUserService,
    authDirectives: AuthorizedDirectives[UserACL],
) extends DirectivesWithCirce with JsonCodecs {

  import authDirectives._

  def routes: Route =
    unauthenticated(implicit tid =>
      (path("simon" / "api" / "v2" / "master-user") & optionalHeaderValueByType(`X-Forwarded-Host`)) { hostHeader =>

        val hostAddress = hostHeader.map(_.host.address)

        concat(

          get(getMasterUserByEmail(hostAddress)),
          post(getMasterUser(hostAddress))

        )
      }
    )

  private def getMasterUserByEmail(hostHeaderValue: Option[String])(implicit traceId: TraceId) =
    parameter("email", "wlpId".as[Int].?)((email, wlpId) =>
      complete(masterUserService.getMasterUser(email.toLowerCase, wlpId, hostHeaderValue))
    )

  private def getMasterUser(hostHeaderValue: Option[String])(implicit traceId: TraceId) = {
    entity(as[MasterUserRequest]) { req =>
      complete(masterUserService.getMasterUser(req.email.toLowerCase, req.wlpId, hostHeaderValue))
    }
  }
}
