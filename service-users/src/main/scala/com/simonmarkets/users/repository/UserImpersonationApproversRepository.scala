package com.simonmarkets.users.repository

import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.mongodb.snapshots.SnapshotableRepository.RecoverFuture
import com.simonmarkets.users.domain.UserImpersonationApprovers
import com.simonmarkets.users.repository.MongoUserImpersonationApproversRepository.userImpersonationApproversCodecRegistry
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.ReplaceOptions

import scala.concurrent.{ExecutionContext, Future}

trait UserImpersonationApproversRepository {

  def getByNetworkId(networkId: String)
    (implicit traceId: TraceId): Future[Option[UserImpersonationApprovers]]

  def upsertUserImpersonationApprovers(userImpersonationApprovers: UserImpersonationApprovers)
    (implicit traceId: TraceId): Future[UserImpersonationApprovers]

}

class MongoUserImpersonationApproversRepository(userImpersonationApproversCollection: MongoCollection[UserImpersonationApprovers])
  (implicit ec: ExecutionContext) extends UserImpersonationApproversRepository with TraceLogging {

  object Fields {
    final val NetworkId = "networkId"
  }

  private final val collection = userImpersonationApproversCollection.withCodecRegistry(userImpersonationApproversCodecRegistry)

  override def getByNetworkId(networkId: String)
    (implicit traceId: TraceId): Future[Option[UserImpersonationApprovers]] = {
    collection
      .find(filter = equal(Fields.NetworkId, networkId))
      .comment(s"traceId: $traceId")
      .limit(1)
      .toFuture
      .map(_.headOption)
  }

  override def upsertUserImpersonationApprovers(userImpersonationApprovers: UserImpersonationApprovers)
    (implicit traceId: TraceId): Future[UserImpersonationApprovers] = {
    collection
      .replaceOne(
        filter = equal(Fields.NetworkId, userImpersonationApprovers.networkId),
        replacement = userImpersonationApprovers,
        options = ReplaceOptions.apply.upsert(true))
      .toFuture
      .map(_ => userImpersonationApprovers)
      .refineDuplicateKey
  }

}

object MongoUserImpersonationApproversRepository {
  val userImpersonationApproversCodecRegistry: CodecRegistry = fromRegistries(fromProviders(classOf[UserImpersonationApprovers]), DEFAULT_CODEC_REGISTRY)
}