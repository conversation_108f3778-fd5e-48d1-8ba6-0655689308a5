package com.simonmarkets.users.repository

import com.mongodb.client.model.ReplaceOptions
import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.users.domain.TransientState
import com.simonmarkets.users.repository.TransientStateRepository.V1.UserId
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.bson.codecs.Macros.createCodecProviderIgnoreNone
import org.mongodb.scala.model.Filters.{and, equal}

import scala.concurrent.{ExecutionContext, Future}

trait TransientStateRepository {

  /**
   * Upsert semantics keyed by userId (unique)
   */
  def upsert(state: TransientState)(implicit ec: ExecutionContext): Future[Unit]

  /**
   * Optional get and delete by userId
   */
  def getAndDelete(userId: String): Future[Option[TransientState]]

}


object TransientStateRepository {

  class V1(
      collection: MongoCollection[TransientState]
  ) extends TransientStateRepository with TraceLogging {

    override def upsert(state: TransientState)(implicit ec: ExecutionContext): Future[Unit] =
      collection
        .replaceOne(
          filter = equal(UserId, state.userId),
          replacement = state,
          options = new ReplaceOptions().upsert(true)
        )
        .toFuture
        .map(_ => ())

    override def getAndDelete(userId: String): Future[Option[TransientState]] =
      collection
        .findOneAndDelete(and(equal(UserId, userId)))
        .toFutureOption

  }

  object V1 {

    val registry: CodecRegistry = fromRegistries(
      fromProviders(
        createCodecProviderIgnoreNone[TransientState],
      ),
      DEFAULT_CODEC_REGISTRY
    )

    val UserId = "userId"
  }

}
