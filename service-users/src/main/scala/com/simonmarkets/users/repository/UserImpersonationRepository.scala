package com.simonmarkets.users.repository

import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.mongodb.codec.EnumEntryCodecProvider
import com.simonmarkets.mongodb.snapshots.SnapshotableRepository.RecoverFuture
import com.simonmarkets.users.domain.{ImpersonationStatus, UserImpersonation}
import com.simonmarkets.users.repository.MongoUserImpersonationRepository.userImpersonationCodecRegistry
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.bson.conversions
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.ReturnDocument.AFTER
import org.mongodb.scala.model.Sorts.descending
import org.mongodb.scala.model.Updates.{combine, set}
import org.mongodb.scala.model.{FindOneAndUpdateOptions, ReturnDocument}

import java.time.Instant

import scala.concurrent.{ExecutionContext, Future}

trait UserImpersonationRepository {

  /**
   * Returns one UserImpersonation by the id */
  def getById(id: String)(entitlements: Set[String])
    (implicit traceId: TraceId): Future[Option[UserImpersonation]]

  /**
   * Returns UserImpersonations by impersonatorUserId with a status equal to Pending, Approved, or Submitted */
  def getNonFinishedUserImpersonations(impersonatorUserId: String)
    (implicit traceId: TraceId): Future[Seq[UserImpersonation]]

  /**
   * Returns one UserImpersonation by the impersonatedUserId */
  def getByImpersonatedUserId(impersonatedUserId: String)
    (implicit traceId: TraceId): Future[Option[UserImpersonation]]

  /**
   * Returns one UserImpersonation by the impersonatorUserId & impersonatedUserId then sort by createdAt */
  def getByImpersonatorUserIdAndImpersonatedUserId(impersonatorUserId: String, impersonatedUserId: String)(entitlements: Set[String])
    (implicit traceId: TraceId): Future[Option[UserImpersonation]]

  /**
   * Inserts one UserImpersonation into the MongoDB collection
   * Returns the inserted UserImpersonation */
  def insert(userImpersonation: UserImpersonation)
    (implicit traceId: TraceId): Future[UserImpersonation]

  /**
   * Updates the UserImpersonation status from Pending to Complete and completedAt to now
   * Returns the modified UserImpersonation */
  def complete(impersonatorUserId: String)
    (implicit traceId: TraceId): Future[Option[UserImpersonation]]

  /**
   * Updates the UserImpersonation status from Pending to Complete and completedAt to now
   * Returns the modified UserImpersonation */
  def forceComplete(impersonatorUserId: String)(entitlements: Set[String])
    (implicit traceId: TraceId): Future[Option[UserImpersonation]]

  /**
   * Updates a UserImpersonation status to Expired
   * Returns true if a UserImpersonation was modified */
  def expire(id: String)
    (implicit traceId: TraceId): Future[Boolean]

  /**
   * Updates the UserImpersonations statuses to Expired for all the ids
   * Returns true if every UserImpersonation with the specified ids was modified */
  def expireAll(ids: Seq[String])
    (implicit traceId: TraceId): Future[Boolean]

  /**
   * Updates the UserImpersonation status to toStatus
   * Returns the updated UserImpersonation */
  def updateStatus(id: String, fromStatus: ImpersonationStatus, toStatus: ImpersonationStatus)(entitlements: Set[String])
    (implicit traceId: TraceId): Future[Option[UserImpersonation]]

  /**
   * Updates the UserImpersonation status, approverUserId, and completedAt
   * Returns the updated UserImpersonation */
  def approveOrRejectStatus(id: String, toStatus: ImpersonationStatus, approverUserId: String)(entitlements: Set[String])
    (implicit traceId: TraceId): Future[Option[UserImpersonation]]
}

class MongoUserImpersonationRepository(userImpersonationCollection: MongoCollection[UserImpersonation])
  (implicit ec: ExecutionContext) extends UserImpersonationRepository with TraceLogging {

  object Fields {
    final val Id = "id"
    final val ImpersonatorUserId = "impersonatorUserId"
    final val ImpersonatedUserId = "impersonatedUserId"
    final val Status = "status"
    final val CompletedAt = "completedAt"
    final val CreatedAt = "createdAt"
    final val Entitlements = "entitlements"
    final val ApproverUserId = "approverUserId"
  }

  private final val collection = userImpersonationCollection.withCodecRegistry(userImpersonationCodecRegistry)

  private def withEntitlements(entitlements: Set[String]): conversions.Bson = in(Fields.Entitlements, entitlements.toSeq: _*)

  override def getById(id: String)(entitlements: Set[String])
    (implicit traceId: TraceId): Future[Option[UserImpersonation]] = {
    collection
      .find(filter = and(
        equal(Fields.Id, id),
        withEntitlements(entitlements)))
      .comment(s"traceId: $traceId")
      .limit(1)
      .toFuture
      .map(_.headOption)
  }

  override def getNonFinishedUserImpersonations(impersonatorUserId: String)
    (implicit traceId: TraceId): Future[Seq[UserImpersonation]] = {
    collection
      .find(filter = and(
        equal(Fields.ImpersonatorUserId, impersonatorUserId),
        in(Fields.Status, ImpersonationStatus.Pending, ImpersonationStatus.Approved, ImpersonationStatus.Submitted)))
      .comment(s"traceId: $traceId")
      .toFuture()
  }

  def getByImpersonatedUserId(impersonatedUserId: String)
    (implicit traceId: TraceId): Future[Option[UserImpersonation]] = {
    collection
      .find(filter = equal(Fields.ImpersonatedUserId, impersonatedUserId))
      .comment(s"traceId: $traceId")
      .limit(1)
      .headOption
  }

  override def getByImpersonatorUserIdAndImpersonatedUserId(impersonatorUserId: String, impersonatedUserId: String)(entitlements: Set[String])
    (implicit tracedId: TraceId): Future[Option[UserImpersonation]] = {
    collection
      .find(filter = and(
        equal(Fields.ImpersonatorUserId, impersonatorUserId),
        equal(Fields.ImpersonatedUserId, impersonatedUserId),
        withEntitlements(entitlements)))
      .sort(descending(Fields.CreatedAt))
      .comment(s"tracedId: $tracedId")
      .limit(1)
      .headOption()
  }

  override def insert(userImpersonation: UserImpersonation)(implicit traceId: TraceId): Future[UserImpersonation] = {
    collection.insertOne(userImpersonation).toFuture.map(_ => userImpersonation).refineDuplicateKey
  }

  override def complete(impersonatorUserId: String)
    (implicit traceId: TraceId): Future[Option[UserImpersonation]] = {
    log.info(s"Trying to complete UserImpersonation for impersonatorUserId: $impersonatorUserId")
    collection
      .findOneAndUpdate(
        filter = and(
          equal(Fields.ImpersonatorUserId, impersonatorUserId),
          equal(Fields.Status, ImpersonationStatus.Pending)),
        update = combine(
          set(Fields.Status, ImpersonationStatus.Complete),
          set(Fields.CompletedAt, Instant.now())),
        options = FindOneAndUpdateOptions.apply().returnDocument(ReturnDocument.AFTER))
      .toFuture
      .map(Option(_))
      .refineDuplicateKey
  }

  override def forceComplete(impersonatorUserId: String)(entitlements: Set[String])
    (implicit traceId: TraceId): Future[Option[UserImpersonation]] = {
    log.info(s"Trying to forceComplete for impersonatorUserId: $impersonatorUserId")
    collection
      .findOneAndUpdate(
        filter = and(
          equal(Fields.ImpersonatorUserId, impersonatorUserId),
          equal(Fields.Status, ImpersonationStatus.Pending),
          withEntitlements(entitlements)),
        update = combine(
          set(Fields.Status, ImpersonationStatus.Complete),
          set(Fields.CompletedAt, Instant.now())),
        options = FindOneAndUpdateOptions.apply().returnDocument(ReturnDocument.AFTER))
      .toFuture
      .map(Option(_))
      .refineDuplicateKey
  }

  override def expire(id: String)(implicit traceId: TraceId): Future[Boolean] = {
    log.info(s"Trying to expire UserImpersonation with id: $id")
    collection
      .updateOne(
        filter = equal(Fields.Id, id),
        update = combine(
          set(Fields.Status, ImpersonationStatus.Expired),
          set(Fields.CompletedAt, Instant.now())))
      .toFuture()
      .map(_.getModifiedCount == 1)
      .refineDuplicateKey
  }

  override def expireAll(ids: Seq[String])(implicit traceId: TraceId): Future[Boolean] = {
    log.info(s"Trying to expire all the UserImpersonation with ids: $ids")
    collection
      .updateMany(
        filter = in(Fields.Id, ids: _*),
        update = combine(
          set(Fields.Status, ImpersonationStatus.Expired),
          set(Fields.CompletedAt, Instant.now())))
      .toFuture()
      .map(_.getModifiedCount == ids.size)
      .refineDuplicateKey
  }

  override def updateStatus(id: String, fromStatus: ImpersonationStatus, toStatus: ImpersonationStatus)(entitlements: Set[String])
    (implicit traceId: TraceId): Future[Option[UserImpersonation]] = {
    log.info(s"Trying to update the status of the UserImpersonation with id: $id from $fromStatus to $toStatus")
    collection
      .findOneAndUpdate(
        filter = and(
          equal(Fields.Id, id),
          equal(Fields.Status, fromStatus),
          withEntitlements(entitlements)),
        update = set(Fields.Status, toStatus),
        options = FindOneAndUpdateOptions.apply().returnDocument(AFTER))
      .toFuture()
      .map(Option(_))
      .refineDuplicateKey
  }

  override def approveOrRejectStatus(id: String, toStatus: ImpersonationStatus, approverUserId: String)(entitlements: Set[String])
    (implicit traceId: TraceId): Future[Option[UserImpersonation]] = {
    log.info(s"Trying to approve or reject the UserImpersonation with id: $id to $toStatus")
    collection
      .findOneAndUpdate(
        filter = and(
          equal(Fields.Id, id),
          withEntitlements(entitlements)),
        update = combine(
          set(Fields.Status, toStatus),
          set(Fields.CompletedAt, Instant.now()),
          set(Fields.ApproverUserId, approverUserId)),
        options = FindOneAndUpdateOptions.apply().returnDocument(AFTER))
      .toFuture()
      .map(Option(_))
      .refineDuplicateKey
  }

}

object MongoUserImpersonationRepository {
  val userImpersonationCodecRegistry: CodecRegistry = fromRegistries(fromProviders(classOf[UserImpersonation], EnumEntryCodecProvider[ImpersonationStatus]), DEFAULT_CODEC_REGISTRY)
}
