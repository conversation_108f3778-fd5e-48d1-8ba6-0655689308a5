package com.simonmarkets.users.repository

import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.networks.common.domain.PassportUserCandidate
import com.simonmarkets.users.domain.PassportVerification
import com.simonmarkets.users.repository.PassportRepository.V1.{Code, CompletionTime, Pending}
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.bson.codecs.Macros
import org.mongodb.scala.model.{Filters, Updates}

import java.time.Instant

import scala.concurrent.{ExecutionContext, Future}

trait PassportRepository {

  /**
   * create new verfication
   */
  def insert(verification: PassportVerification): Future[Unit]

  /**
   * find verification by `code`. `code` is uniquely indexed
   */
  def findByCode(code: Array[Byte]): Future[Option[PassportVerification]]

  /**
   * transition verification to completed state
   */
  def confirm(code: Array[Byte]): Future[Unit]

}

object PassportRepository {

  case class V1(
      collection: MongoCollection[PassportVerification]
  )(implicit ec: ExecutionContext) extends PassportRepository with TraceLogging {

    override def insert(verification: PassportVerification): Future[Unit] =
      collection.insertOne(verification).toFuture.map(_ => ())

    override def findByCode(code: Array[Byte]): Future[Option[PassportVerification]] =
      collection.find(Filters.equal(Code, code)).headOption

    override def confirm(code: Array[Byte]): Future[Unit] = {
      collection
        .updateOne(
          filter = Filters.equal(Code, code),
          update = Seq(
            Updates.set(Pending, false),
            Updates.set(CompletionTime, Instant.now)
          )
        )
        .toFuture
        .map(_ => ())
    }

  }

  object V1 {

    val registry: CodecRegistry = fromRegistries(
      fromProviders(
        Macros.createCodecProviderIgnoreNone[PassportUserCandidate],
        Macros.createCodecProviderIgnoreNone[PassportVerification]
      ),
      DEFAULT_CODEC_REGISTRY
    )

    val Code = "code"
    val Pending = "pending"
    val CompletionTime = "completionTime"

  }

}
