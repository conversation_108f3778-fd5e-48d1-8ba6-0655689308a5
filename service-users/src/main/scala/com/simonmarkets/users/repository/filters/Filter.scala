package com.simonmarkets.users.repository.filters

import com.goldmansachs.marquee.pipg.LicenseName.NPN
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.users.common.{LoginMode, UserType}
import com.simonmarkets.users.repository.encoders.UserFormat._
import org.mongodb.scala.bson.conversions.Bson
import org.mongodb.scala.model.Filters._

sealed trait MongoFilter {
  def expression: Bson
}

final case class UserIdFilter(value: String) extends MongoFilter {
  val expression: Bson = equal(Fields.Id, value)
}

final case class NetworkFilter(value: String) extends MongoFilter {
  val expression: Bson = equal(Fields.NetworkId, value)
}

final case class EmailFilter(value: String) extends MongoFilter {
  val expression: Bson = equal(Fields.Email, value.toLowerCase)
}

final case class OmsIdFilter(value: String) extends MongoFilter {
  val expression: Bson = equal(Fields.OmsId, value)
}

final case class DistributorIdFilter(value: String, networkId: Option[String]) extends MongoFilter {
  val expression: Bson =
    networkId match {
      case Some(networkId) => and(equal(Fields.DistributorId, value), NetworkFilter(networkId).expression)
      case None            => equal(Fields.DistributorId, value)
    }
}

final case class NpnFilter(value: String) extends MongoFilter {
  val expression: Bson = elemMatch("licenses", and(equal("name", NPN.toString), equal("number", value)))
}

final case class IdpIdFilter(value: String) extends MongoFilter {
  val expression: Bson = equal(Fields.IdpId, value)
}

final case class MaskedIdFilter(id: String, target: Option[String]) extends MongoFilter {
  val expression: Bson = target match {
    case None         => elemMatch("maskedIds", equal("id", id))
    case Some(target) => elemMatch("maskedIds", and(equal("id", id), equal("target", target)))
  }
}

final case class OrFilter(filters: MongoFilter*) extends MongoFilter {
  val expression: Bson = or(filters.map(_.expression): _*)
}

final case class AndFilter(filters: MongoFilter*) extends MongoFilter {
  val expression: Bson = and(filters.map(_.expression): _*)
}

case object IsActiveFilter extends MongoFilter {
  val expression: Bson = equal(Fields.IsActive, true)
}

final case class ExternalIdFilter(externalId: ExternalId) extends MongoFilter {
  val expression: Bson = elemMatch(
    Fields.ExternalIds,
    and(
      equal("subject", externalId.subject),
      equal("id", externalId.id)
    )
  )
}

final case class FaNumberFilter(value: String, networkId: Option[String]) extends MongoFilter {
  val expression: Bson = and(
    Seq(Some(equal(Fields.FaNumbers, value)), networkId.map(NetworkFilter(_).expression)).flatten: _*
  )
}

final case class CustomRoleFilter(value: String, networkId: Option[String]) extends MongoFilter {
  val expression: Bson = and(
    Seq(Some(equal(Fields.CustomRoles, value)), networkId.map(NetworkFilter(_).expression)).flatten: _*
  )
}

final case class ICapitalUserIdFilter(value: String) extends MongoFilter {
  val expression: Bson = equal(Fields.ICapitalUserId, value)
}

final case class SystemUsersFilter(networkId: String) extends MongoFilter {
  val expression: Bson = and(
    NetworkFilter(networkId).expression,
    equal(Fields.LoginMode, LoginMode.ClientCredentials.toString),
    equal(Fields.UserType, UserType.System.toString)
  )
}

final case class FirmIdFilter(firmId: String) extends MongoFilter {
  val expression: Bson = equal(Fields.FirmId, firmId)
}

final case class WhiteLabelPartnerIdFilter(wlpId: String) extends MongoFilter {
  val expression: Bson = equal(Fields.WhiteLabelPartnerId, wlpId)
}

final case class LocationsFilter(locations: List[String]) extends MongoFilter {
  val expression: Bson = in(Fields.Locations, locations: _*)
}

final case class UserIdsFilter(userIds: List[String]) extends MongoFilter {
  val expression: Bson = in(Fields.Id, userIds: _*)
}

object Filters {

  /**
   * Creates a set of [[MongoFilter]] from a parameter map. For fields that support only a single filter, the last value
   * in the list is used.
   */
  def make(filters: Map[String, List[String]]): Set[MongoFilter] = {
    val externalIdFilter: Option[MongoFilter] = for {
      subject <- filters.get("external-subject").flatMap(_.lastOption)
      id <- filters.get("external-id").flatMap(_.lastOption)
      externalId = ExternalId(subject, id)
    } yield ExternalIdFilter(externalId)

    val networkFilterValue = filters.get("network").flatMap(_.lastOption)

    filters
      .flatMap {
        case ("distributorId", vs) => filter(vs)(DistributorIdFilter(_, networkFilterValue))
        case ("email", vs)         => filter(vs)(EmailFilter.apply)
        case ("faNumber", vs)      => filter(vs)(FaNumberFilter(_, networkFilterValue))
        case ("firmId", vs)        => filter(vs)(FirmIdFilter.apply)
        case ("idpId", vs)         => filter(vs)(IdpIdFilter.apply)
        case ("location", vs)      => if (vs.isEmpty) List.empty else List(LocationsFilter(vs))
        case ("maskedId", vs)      => filter(vs)(MaskedIdFilter(_, filters.get("target").flatMap(_.lastOption)))
        case ("network", vs)       => filter(vs)(NetworkFilter.apply)
        case ("npn", vs)           => filter(vs)(NpnFilter.apply)
        case ("omsId", vs)         => filter(vs)(OmsIdFilter.apply)
        case ("wlpId", vs)         => filter(vs)(WhiteLabelPartnerIdFilter.apply)
        case ("id", vs)        => if (vs.isEmpty) List.empty else List(UserIdsFilter(vs))
        case _                     => List.empty
      }
      .toSet ++ externalIdFilter.toSet
  }

  private def filter[A](values: List[String])(make: String => A): List[A] =
    values.lastOption.map(make).toList

}
