package com.simonmarkets.users.repository

import com.simonmarkets.circe.{CirceDecoders, CirceEncoders}
import io.circe.Decoder
import io.circe.generic.extras.{AutoDerivation, Configuration}


import java.time.{Instant, LocalDateTime, ZoneId}

trait UserMongoJsonCodecs extends CirceEncoders with CirceDecoders with AutoDerivation {

  implicit val circeConfig: Configuration = Configuration.default.withDefaults

  def mongoLocalDateTimeDecoder[A<: LocalDateTime]: Decoder[LocalDateTime] =
    Decoder.decodeJsonObject.map(x => LocalDateTime.ofInstant(Instant.ofEpochMilli(x.toMap("$date").toString().toLong), ZoneId.systemDefault()))


  override implicit def localDateTimeDecoder[A <: LocalDateTime]: Decoder[LocalDateTime] =
    mongoLocalDateTimeDecoder or super.localDateTimeDecoder
}
