package com.simonmarkets.users.repository.encoders

import com.goldmansachs.marquee.pipg.User.DistributorInfo
import com.goldmansachs.marquee.pipg.{AcordState, Custodian, CustodianFaNumber, License, LicenseName, LicenseType, UserRole}
import com.simonmarkets.mongodb.bson.MacrosFormat
import com.simonmarkets.mongodb.codec.{EnumEntryCodecProvider, IdTypeCodecProvider, SafeEnumCodecProvider, TraceIdCodec}
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.okta.domain.{Factor, FactorType}
import com.simonmarkets.shared.MaskedId
import com.simonmarkets.users.common.{Context, EventInfo, LandingPage, LoginMode, User, UserDomainEvent, UserType}
import org.bson.codecs.Codec
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.{CodecRegistries, CodecRegistry}
import org.mongodb.scala.MongoClient
import org.mongodb.scala.bson.codecs.Macros
import simon.Id.NetworkId


object UserFormat extends MacrosFormat[User] {

  val codecRegistry: CodecRegistry = fromRegistries(
    MongoClient.DEFAULT_CODEC_REGISTRY,
    fromProviders(
      IdTypeCodecProvider(NetworkId),
      Macros.createCodecProviderIgnoreNone[User],
      Macros.createCodecProviderIgnoreNone[MaskedId],
      Macros.createCodecProviderIgnoreNone[CustodianFaNumber],
      Macros.createCodecProviderIgnoreNone[License],
      Macros.createCodecProviderIgnoreNone[DistributorInfo],
      Macros.createCodecProviderIgnoreNone[Context],
      Macros.createCodecProviderIgnoreNone[EventInfo],
      Macros.createCodecProviderIgnoreNone[ExternalId],
      Macros.createCodecProviderIgnoreNone[Factor],
      EnumEntryCodecProvider[UserRole],
      EnumEntryCodecProvider[LicenseName],
      EnumEntryCodecProvider[LicenseType],
      EnumEntryCodecProvider[AcordState],
      SafeEnumCodecProvider[FactorType],
      SafeEnumCodecProvider[LoginMode],
      SafeEnumCodecProvider[UserType],
      SafeEnumCodecProvider[UserDomainEvent],
      SafeEnumCodecProvider[LandingPage],
      SafeEnumCodecProvider[Custodian]
    ),
    CodecRegistries.fromCodecs(
      TraceIdCodec,
    )
  )

  override protected def entityCodecProvider: Codec[User] = Macros.createCodecIgnoreNone[User](codecRegistry)

  object Fields {
    val ObjectId = "_id"
    val Id = "id"
    val NetworkId = "networkId"
    val WhiteLabelPartnerId = "whiteLabelPartnerId"
    val Email = "email"
    val CustomRoles = "customRoles"
    val Entitlements = "entitlements"
    val IsActive = "isActive"
    val FaNumbers = "faNumbers"
    val MaskedIds = "maskedIds"
    val Version = "version"
    val LastVisitedAt = "lastVisitedAt"
    val DistributorId = "distributorId"
    val OmsId = "omsId"
    val IdpId = "idpId"
    val DistributorInfo = "distributorInfo"
    val Context = "context"
    val IdpLoginId = "idpLoginId"
    val ExternalIds = "externalIds"
    val EmailLowerCase = "emailLowerCase"
    val SecondaryEmail = "secondaryEmail"
    val ICapitalUserId = "iCapitalUserId"
    val Passport = "passport"
    val Mfas = "mfas"
    val LoginMode = "loginMode"
    val UserType = "userType"
    val FirmId = "firmId"
    val Locations = "locations"
  }

}
