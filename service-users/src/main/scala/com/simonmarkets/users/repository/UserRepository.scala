package com.simonmarkets.users.repository

import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.mongodb.bson.ScalaBsonDocumentsOps.Ops
import com.simonmarkets.mongodb.bson.{Format, MacrosFormat, SnapshotFormat}
import com.simonmarkets.mongodb.codec.IdTypeCodecProvider
import com.simonmarkets.mongodb.error.Conflict
import com.simonmarkets.mongodb.snapshots.SnapshotableRepository.RecoverFuture
import com.simonmarkets.mongodb.snapshots.{EntitySnapshot, ExtractId, IncrementVersion, SnapshotableRepository}
import com.simonmarkets.users.api.Page
import com.simonmarkets.users.common.UniqueUserId.{Distributor, Email, External, Guid, Idp, NPN, Oms}
import com.simonmarkets.users.common._
import com.simonmarkets.users.domain.{UserSnapshotEntityView, UserSnapshotView}
import com.simonmarkets.users.repository.encoders.UserFormat
import com.simonmarkets.users.repository.encoders.UserFormat.Fields
import com.simonmarkets.users.repository.filters._
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.bson.types.ObjectId
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros
import org.mongodb.scala.bson.collection.immutable.Document
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.Projections.include
import org.mongodb.scala.model.Sorts._
import org.mongodb.scala.model.Updates._
import org.mongodb.scala.model.{FindOneAndReplaceOptions, ReturnDocument}
import org.mongodb.scala.{MongoClient, MongoCollection, Observable}
import simon.Id.NetworkId

import java.time.{LocalDateTime, OffsetDateTime, ZoneId}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

trait UserRepository {

  def getById(userId: String)(entitlements: Set[String])(implicit traceId: TraceId): Future[Option[User]]

  def getUserByUniqueId(id: UniqueUserId)
    (entitlements: Set[String])(implicit traceId: TraceId): Future[Option[User]]

  def getUser(filters: Set[MongoFilter])(entitlements: Set[String])(implicit traceId: TraceId): Future[Option[User]]

  def getUserIds(filters: Set[MongoFilter])(entitlements: Set[String])(implicit traceId: TraceId): Future[Set[String]]

  def getUsersPage(limit: Int, from: Option[String], filters: Set[MongoFilter] = Set.empty)
    (entitlements: Set[String])
    (implicit traceId: TraceId): Future[Page[User]]

  def getUsers(filters: Set[MongoFilter] = Set.empty, limitOpt: Option[Int] = None)(entitlements: Set[String])
    (implicit traceId: TraceId): Future[List[User]]

  def getUsersStream(filters: Set[MongoFilter] = Set.empty, limitOpt: Option[Int] = None)(entitlements: Set[String])
    (implicit traceId: TraceId): Observable[User]

  def getUsersByIds(ids: Set[String])(entitlements: Set[String])(implicit traceId: TraceId): Future[List[User]]

  def getUsersCount(filters: Set[MongoFilter])(implicit traceId: TraceId): Future[Long]

  def exists(userId: String)(implicit traceId: TraceId): Future[Boolean]

  def existsByEmailAndNetwork(email: String, networkId: NetworkId)(implicit traceId: TraceId): Future[Boolean]

  def insertUser(userId: String, user: User)(implicit traceId: TraceId): Future[User]

  def updateUser(userId: String, user: User)(implicit traceId: TraceId): Future[User]

  def refreshLastUpdatedTime(userId: String)(implicit traceId: TraceId): Future[Int]

  def bumpVersions(userIds: Set[String])(entitlements: Set[String])(implicit traceId: TraceId): Future[Int]

  def getPriorSnapshotByEntityId(userId: String)(implicit traceId: TraceId): Future[Option[UserSnapshotView]]

}

class MongoUserRepository(
    val client: MongoClient,
    val entityCollection: MongoCollection[Document],
    val entitySnapshotCollection: MongoCollection[Document],
    useSnapshots: Boolean = true)
  (implicit val ec: ExecutionContext)
  extends UserRepository
    with SnapshotableRepository[User]
    with UserMongoJsonCodecs
    with TraceLogging {

  implicit val userFormat: MacrosFormat[User] = UserFormat
  implicit val userSnapshotFormat: Format[EntitySnapshot[User]] = new SnapshotFormat[User](userFormat)

  override def getById(userId: String)(entitlements: Set[String])(implicit traceId: TraceId): Future[Option[User]] = {
    entityCollection
      .find(and(equal(Fields.Id, userId), withEntitlements(entitlements)))
      .comment(s"traceId: $traceId")
      .limit(1)
      .headOption
      .map(_.map(documentToUser))
  }

  override def getUser(filters: Set[MongoFilter])(entitlements: Set[String])
    (implicit traceId: TraceId): Future[Option[User]] = {
    getUsers(filters, Some(1))(entitlements).map(_.headOption)
  }

  override def getUsersPage(limit: Int, from: Option[String], filters: Set[MongoFilter] = Set.empty)
    (entitlements: Set[String])
    (implicit traceId: TraceId): Future[Page[User]] = {

    val filterExpressions = filters.map(_.expression)

    val result = from match {
      case Some(id) =>
        Try {
          val objectId = new ObjectId(id)
          val expressions = filterExpressions + gt(Fields.ObjectId, objectId) + withEntitlements(entitlements)
          entityCollection.find(and(expressions.toSeq: _*))
            .comment(s"traceId: $traceId")
            .sort(ascending(Fields.ObjectId)).limit(limit).toFuture()
        }.getOrElse(Future(List()))

      case None =>
        val expressions = filterExpressions + withEntitlements(entitlements)
        entityCollection.find(and(expressions.toSeq: _*))
          .comment(s"traceId: $traceId")
          .sort(ascending(Fields.ObjectId)).limit(limit).toFuture()
    }

    entityCollection.countDocuments(and((filterExpressions + withEntitlements(entitlements)).toSeq: _*)).toFuture().flatMap { totalCount =>
      result.map { documents =>
        log.info(s"Got ${documents.size} users with params", from, limit)
        if (documents.isEmpty) {
          Page(totalCount, 0, None, List())
        } else {
          val lastDocumentId = documents.last.get(Fields.ObjectId).get.asObjectId().getValue.toString
          val users = documents.map(documentToUser).toList
          val next = if (users.size < limit) None else Some(lastDocumentId)
          Page(totalCount, users.size, next, users)
        }
      }
    }
  }

  override def getUsers(filters: Set[MongoFilter] = Set.empty, limitOpt: Option[Int] = None)(entitlements: Set[String])
    (implicit traceId: TraceId): Future[List[User]] = {
    getUsersStream(filters, limitOpt)(entitlements).toFuture().map(_.toList)
  }

  override def getUsersStream(filters: Set[MongoFilter], limitOpt: Option[Int] = None)(entitlements: Set[String])
    (implicit traceId: TraceId): Observable[User] = {
    val expression =
      if (filters.nonEmpty) {
        and((filters.map(_.expression) + withEntitlements(entitlements)).toSeq: _*)
      } else
        withEntitlements(entitlements)

    limitOpt match {
      case Some(limit) => entityCollection.find(expression)
        .comment(s"traceId: $traceId")
        .limit(limit).map(documentToUser)
      case None => entityCollection.find(expression)
        .comment(s"traceId: $traceId")
        .map(documentToUser)
    }
  }

  def getUserIds(filters: Set[MongoFilter])(entitlements: Set[String])
    (implicit traceId: TraceId): Future[Set[String]] = {
    val expression =
      if (filters.nonEmpty)
        and((filters.map(_.expression) + withEntitlements(entitlements)).toSeq: _*)
      else
        withEntitlements(entitlements)

    entityCollection
      .find(expression)
      .comment(s"traceId: $traceId")
      .projection(include(Fields.Id))
      .map(_.getString(Fields.Id))
      .toFuture()
      .map(_.toSet)
  }

  override def getUsersCount(filters: Set[MongoFilter])(implicit traceId: TraceId): Future[Long] = {
    entityCollection
      .countDocuments(and(filters.map(_.expression).toSeq: _*))
      .toFuture()
  }

  override def exists(userId: String)(implicit traceId: TraceId): Future[Boolean] = {
    entityCollection.find(equal(Fields.Id, userId))
      .comment(s"traceId: $traceId")
      .limit(1).headOption().map(_.isDefined)
  }

  override def existsByEmailAndNetwork(email: String, networkId: NetworkId)
    (implicit traceId: TraceId): Future[Boolean] = {
    val filters = and(
      EmailFilter(email).expression,
      equal(Fields.NetworkId, networkId.toString)
    )
    entityCollection.find(filters)
      .comment(s"traceId: $traceId")
      .limit(1).headOption().map(_.isDefined)
  }

  override def getUserByUniqueId(id: UniqueUserId)(entitlements: Set[String])
    (implicit traceId: TraceId): Future[Option[User]] = {
    val filters: Set[MongoFilter] = id match {
      case Oms(id) => Set(OmsIdFilter(id))
      case Distributor(id, networkId) => Set(DistributorIdFilter(id, Some(NetworkId.unwrap(networkId))))
      case Guid(id) => Set(UserIdFilter(id))
      case NPN(id, networkId) => Set(NpnFilter(id), NetworkFilter(NetworkId.unwrap(networkId)))
      case Email(id, networkId) => Set(EmailFilter(id), NetworkFilter(NetworkId.unwrap(networkId)))
      case Idp(id) => Set(IdpIdFilter(id))
      case External(id) => Set(ExternalIdFilter(id))
    }
    getUser(filters)(entitlements)
  }

  override def insertUser(userId: String, user: User)(implicit traceId: TraceId): Future[User] = {
    if (useSnapshots) {
      insert(userId, user, None).refineDuplicateKey
    } else {
      entityCollection.insertOne(userToDocument(user)).toFuture().map(_ => user)
    }
  }

  override def updateUser(userId: String, user: User)(implicit traceId: TraceId): Future[User] = {
    if (useSnapshots) {
      import MongoUserRepository.{ExtId, IncVer}
      update(userId, user, None).refineDuplicateKey
    } else {
      log.warn("Snapshots are disabled. This should only happen when running unit tests")
      val userToUpdate = user.incrementVersion
      entityCollection.findOneAndReplace(and(equal(Fields.Id, user.id), equal(Fields.Version, user.version)), userToDocument(userToUpdate),
        new FindOneAndReplaceOptions().returnDocument(ReturnDocument.AFTER)).toFuture().flatMap((updatedUser: Document) => {

        if (updatedUser == null) {
          Future.failed(Conflict("User not found", None))
        } else {
          Future.successful(documentToUser(updatedUser))
        }
      })
    }

  }

  override def getUsersByIds(ids: Set[String])(entitlements: Set[String])
    (implicit traceId: TraceId): Future[List[User]] = {
    if (ids.nonEmpty) {
      entityCollection.find(and(withEntitlements(entitlements), or(ids.map(id => equal(Fields.Id, id)).toSeq: _*)))
        .comment(s"traceId: $traceId")
        .toFuture()
        .map(_.map(documentToUser).toList)
    } else {
      Future.successful(List())
    }
  }

  def refreshLastUpdatedTime(userId: String)(implicit traceId: TraceId): Future[Int] = {
    val zone = ZoneId.systemDefault()
    val now = LocalDateTime.now(zone)
    entityCollection.updateOne(equal(Fields.Id, userId),
      set(Fields.LastVisitedAt, java.util.Date.from(now.toInstant(OffsetDateTime.now(zone).getOffset)))).toFuture.map(result => result.getModifiedCount.toInt)
  }

  def bumpVersions(userIds: Set[String])(entitlements: Set[String])(implicit traceId: TraceId): Future[Int] = {
    val usersFut = getUsersByIds(userIds)(entitlements)
    usersFut
      .flatMap { users =>
        val updatedUsersFut = users.map(user => updateUser(user.id, user))
        Future.sequence(updatedUsersFut)
      }
      .map(users => users.size)
  }

  private def documentToUser(doc: Document) = {
    UserFormat.read(doc)
  }

  private def userToDocument(user: User): Document = {
    UserFormat.write(user)
  }

  private def withEntitlements(entitlements: Set[String]) = in(Fields.Entitlements, entitlements.toSeq: _*)

  override def getPriorSnapshotByEntityId(userId: String)
    (implicit traceId: TraceId): Future[Option[UserSnapshotView]] = {
    entitySnapshotCollection.find[UserSnapshotView](equal("entity.id", userId))
      .comment(s"traceId: $traceId")
      .sort(descending(SnapshotFormat.Fields.ModificationDate))
      .skip(1)
      .limit(1)
      .headOption()
  }

}

object MongoUserRepository {

  val registry: CodecRegistry = fromRegistries(fromProviders(
    IdTypeCodecProvider(NetworkId),
    Macros.createCodecProvider[UserSnapshotEntityView],
    Macros.createCodecProvider[UserSnapshotView]),
    DEFAULT_CODEC_REGISTRY)

  implicit val IncVer: IncrementVersion[User] = (x: User) => x.copy(version = x.version + 1)

  implicit val ExtId: ExtractId[User] = new ExtractId[User] {
    type Id = String

    def id(x: User): String = x.id
  }
}
