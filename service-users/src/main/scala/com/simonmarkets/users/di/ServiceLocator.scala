package com.simonmarkets.users.di

import akka.actor.ActorSystem
import akka.http.scaladsl.Http
import akka.stream.Materializer
import com.github.benmanes.caffeine.cache.{AsyncCache, Caffeine}
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.api.users.HttpACLClient
import com.simonmarkets.caching.config.CacheConfig.CaffeineCacheConfig.CacheOptionalArgs
import com.simonmarkets.eventbridge.client.{EventBridgeClient, EventBridgeImpl}
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.http.authentication.NoAuthCredentials
import com.simonmarkets.kafka.ZConsumer
import com.simonmarkets.kafka.codec.Deserializers
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.clients.emailsender.EmailSenderHttpClient
import com.simonmarkets.networks.common.clients.icn.HttpIcnClient
import com.simonmarkets.networks.common.clients.usersync.{HttpUserSyncMappingClient, UserNetworkMappingRequest, UserNetworkMappingResponse}
import com.simonmarkets.networks.common.service.ExternalIdTypeService
import com.simonmarkets.networks.common.service.networkservice.BasicNetworkService
import com.simonmarkets.oauth.client.AddeparOAuthClient
import com.simonmarkets.okta.client.api.HttpOktaClient
import com.simonmarkets.okta.client.sdk.OktaClientFactory
import com.simonmarkets.okta.service.{OktaRepository, OktaService}
import com.simonmarkets.resteasy.authn.User
import com.simonmarkets.resteasy.framework.RestEasyCore
import com.simonmarkets.resteasy.requests.AuthorizedDirectives
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.useracl.UserAclAuthorizedDirective
import com.simonmarkets.users.ExternalUsersClient
import com.simonmarkets.users.config.AppConfiguration
import com.simonmarkets.users.domain.{IcnUserEvent, SsoMapper}
import com.simonmarkets.users.service.UserImpersonationApproversService.UserImpersonationApproversServiceImpl
import com.simonmarkets.users.service.UserImpersonationService.UserImpersonationServiceImpl
import com.simonmarkets.users.service._
import io.simon.encryption.v3.service.IcnEncryptionService
import org.mongodb.scala.MongoDatabase
import zio._
import zio.kafka.producer.{Producer, ProducerSettings}

import java.security.SecureRandom

import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext}

class ServiceLocator(config: AppConfiguration)(implicit system: ActorSystem, mat: Materializer,
    executionContext: ExecutionContext, traceId: TraceId) extends TraceLogging {

  private val futureHttpClient: FutureHttpClient = new FutureHttpClient(Http(), config.httpClientConfig)
  private val noAuthFutureHttpClient: FutureHttpClient = new FutureHttpClient(Http(), config.httpClientConfig.copy(auth = NoAuthCredentials))
  val httpACLClient: HttpACLClient = HttpACLClient(futureHttpClient, config.simonBasePath, Some(config.aclCacheConfig))
  private val systemUser = httpACLClient.getUserACL(config.systemUserId).await(Duration.Inf)
  private val userACLDirective: UserAclAuthorizedDirective = UserAclAuthorizedDirective(httpACLClient)
  val authDirective: AuthorizedDirectives[UserACL] = RestEasyCore
    .allowSystemCalls(userACLDirective, User(systemUser.userId, "", None, Nil), systemUser)

  log.info("Creating mongo client")
  lazy val repositories = new MongoRepositories(config.mongoDB)
  lazy val database: MongoDatabase = repositories.database

  private val externalUsersClient = new ExternalUsersClient(config.simonBasePath, futureHttpClient)
  private val emailSenderClient = new EmailSenderHttpClient(futureHttpClient, config.simonBasePath)
  private val encryptionClient = new IcnEncryptionService(futureHttpClient, config.encryption)
  private val icnClient = new HttpIcnClient(
    noAuthClient = noAuthFutureHttpClient,
    basePath = config.mappingClientConfig.basePath,
    hostOpt = config.mappingClientConfig.host
  )

  log.info("init okta client and oktaService")
  private val oktaService = if (config.oktaConfig.enabled) {
    OktaService(
      config = config.oktaConfig.serviceConfig,
      repository = OktaRepository(
        client = OktaClientFactory.getOktaClient(config.oktaConfig.clientConfig),
        httpClient = HttpOktaClient(config.oktaConfig.clientConfig)
      )
    )
  } else {
    throw new RuntimeException("Okta sync in no-op mode")
  }

  log.info("getting system user")
  val systemAdminAcl: UserACL = Await.result(httpACLClient.getUserACL(config.enhanceAccessTokenConfig.systemAdminId), 5.seconds.asScala)

  log.info("init services")
  val networkService = new BasicNetworkService(repositories.networkRepository)
  val oktaSyncBus: EventBridgeClient = EventBridgeImpl(config.oktaSyncEventBus)
  val externalIdTypeService = new ExternalIdTypeService.V1(repositories.externalIdTypeRepository)
  val userImpersonationService = new UserImpersonationServiceImpl(
    userImpersonationRepository = repositories.userImpersonationRepository,
    userRepository = repositories.userRepository,
    networkRepository = repositories.networkRepository,
    userImpersonationApproversRepository = repositories.userImpersonationApproversRepository,
    emailSenderClient = emailSenderClient,
    icnClient = icnClient,
    excludedNetworks = config.impersonationExclusions
  )
  private val httpMappingClient: FutureHttpClient = new FutureHttpClient(Http(), config.mappingClientConfig.httpClientConfig)

  private val mapppingClientCache: Option[AsyncCache[UserNetworkMappingRequest, Option[UserNetworkMappingResponse]]] =
    config.mappingClientConfig.cacheSettings.map { cacheSettings =>
      Caffeine
        .newBuilder()
        .withMaxSize(cacheSettings.maxEntries)
        .withTtl(cacheSettings.ttl)
        .buildAsync[UserNetworkMappingRequest, Option[UserNetworkMappingResponse]]
    }

  val userNetworkMappingClient = new HttpUserSyncMappingClient(
    httpMappingClient,
    config.mappingClientConfig.basePath,
    config.mappingClientConfig.apiKey,
    config.mappingClientConfig.host,
    mapppingClientCache
  )

  val userImpersonationApproversService = new UserImpersonationApproversServiceImpl(repositories.userImpersonationApproversRepository)
  val userService = new UserServiceImpl(
    systemAcl = systemAdminAcl,
    userRepository = repositories.userRepository,
    networkService = networkService,
    oktaService = oktaService,
    config = config.serviceConfig,
    oktaSyncBus = oktaSyncBus,
    externalIdTypeRepo = externalIdTypeService,
    impersonationService = userImpersonationService,
    mappings = userNetworkMappingClient,
    systemUserNetworkLimitationIgnoreList = config.systemUserNetworkLimitationIgnoreList
  )
  private val random = new SecureRandom()
  val passportService: PassportService = PassportService.V1(
    userRepository = repositories.userRepository,
    passportRepository = repositories.passportRepository,
    emailSenderClient = emailSenderClient,
    icnClient = icnClient,
    encryptionClient = encryptionClient,
    validityWindow = config.passport.validityWindow,
    codeLength = config.passport.codeLength,
    random = Iterator.continually(random.nextInt(10)),
    cc = config.passport.cc,
    from = config.passport.from
  )


  val masterUserService = new MasterUserService(repositories.userRepository, repositories.networkRepository, config.domainConfig)
  val accessTokenService = new AccessTokenService.Impl(
    serviceUser = systemAdminAcl,
    cachedAclClient = httpACLClient,
    networkTokenExpiration = config.enhanceAccessTokenConfig.networkTokenLifetimes.map { tokenLifetime =>
      tokenLifetime.networkId -> tokenLifetime.expirationTime
    }.toMap,
    appExpiryConfig = config.enhanceAccessTokenConfig.appExpiryConfig,
    oktaService = oktaService,
    userService = userService,
    networkService = networkService,
    userImpersonationService = userImpersonationService,
    externalUsersClient = externalUsersClient,
    ssoMapper = SsoMapper.Impl,
  )

  val addeparAuthClient = new AddeparOAuthClient(futureHttpClient, config.addeparConfig.baseUrl, config.addeparConfig.clientId, config.addeparConfig.clientSecret)
  val addeparOAuthTokenService = new AddeparOAuthTokenService(addeparAuthClient, repositories.tokenRepository)

  private val kafkaProperties: Map[String, AnyRef] =
    if (config.kafkaConfig.auth.nonEmpty) {
      val credentials = config.kafkaConfig.auth.get
      Map(
        "security.protocol" -> credentials.securityProtocol,
        "sasl.jaas.config" -> s"org.apache.kafka.common.security.plain.PlainLoginModule required username='${credentials.apiKey}' password='${credentials.secret}';",
        "sasl.mechanism" -> "PLAIN"
      )
    } else Map()

  val producerSettings: ProducerSettings = ProducerSettings(List(config.kafkaConfig.connectionUrl))
    .withProperties(kafkaProperties)

  val producerLayer: ZLayer[Any, Throwable, Producer] =
    ZLayer.scoped {
      Producer.make(producerSettings)
    }

  val userEventPublisherEnv: TaskLayer[UserEventPublisher] = ZLayer.make[UserEventPublisherImpl](
    producerLayer,
    ZLayer.succeed(executionContext),
    ZLayer.succeed(repositories.userRepository),
    ZLayer.succeed(config.kafkaConfig.producer),
    UserEventPublisherImpl.layer,
  )



  private val userSyncSystemUser: UserACL = Await.result(httpACLClient.getUserACL(config.userSyncSystemUserId), 5.seconds.asScala)

  private def consumerEff = {
    log.info("[User Sync] starting consumer")
    ZIO.serviceWithZIO[UserEventConsumer] { userEventConsumer =>
        ZConsumer.run[UserEventConsumer, String, IcnUserEvent](
          deserializers = Deserializers.json[Any, IcnUserEvent],
          consume = userEventConsumer.consume
        )
      }
      .retry(Schedule.exponential(zio.Duration.fromSeconds(1)) && Schedule.recurs(5))
      .catchAllCause(cause => ZIO.succeed(log.error(s"${cause.squashTrace.getMessage}")))
      .provide(
        ZConsumer.configured,
        UserEventConsumer.live,
        ZLayer.succeed(userNetworkMappingClient),
        ZLayer.succeed(userService),
        ZLayer.succeed(networkService),
        ZLayer.succeed(userSyncSystemUser),
        ZLayer.succeed(config.kafkaConfig.consumer),
        ZLayer.succeed(repositories.transientStateRepository),
        Scope.default
      )
  }

  if (config.kafkaConfig.isUserSyncEnabled) {
    Unsafe.unsafe { implicit unsafe =>
      Runtime
        .default
        .unsafe
        .run(consumerEff.forkDaemon)
    }
  }
}
