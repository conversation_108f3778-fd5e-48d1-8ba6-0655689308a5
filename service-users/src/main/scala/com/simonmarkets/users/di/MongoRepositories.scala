package com.simonmarkets.users.di

import com.simonmarkets.mongodb.codec.IdTypeCodecProvider
import com.simonmarkets.networks.common.ExternalIdType
import com.simonmarkets.networks.common.repository.{ExternalIdTypeRepository, MongoNetworkRepository}
import com.simonmarkets.users.config.{DbConfig, MongoConfiguration}
import com.simonmarkets.users.domain.{OAuthToken, PassportVerification, TransientState, UserImpersonation, UserImpersonationApprovers}
import com.simonmarkets.users.repository.{MongoUserImpersonationApproversRepository, MongoUserImpersonationRepository, MongoUserRepository, OAuthTokenRepository, OAuthTokenRepositoryImpl, PassportRepository, TransientStateRepository}
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros
import org.mongodb.scala.{Document, MongoClient, MongoCollection, MongoDatabase}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext
import scala.reflect.ClassTag

class MongoRepositories(config: MongoConfiguration)(implicit ec: ExecutionContext) {
  private lazy val mongoClient: MongoClient = com.simonmarkets.mongodb.Client.create(config.client)

  private lazy val userCollection: MongoCollection[Document] = collection[Document](config.users)

  private lazy val usersImpersonationCollection: MongoCollection[UserImpersonation] = collection[UserImpersonation](config.usersImpersonation)

  private lazy val usersImpersonationApproversCollection: MongoCollection[UserImpersonationApprovers] = collection[UserImpersonationApprovers](config.usersImpersonationApprovers)

  private lazy val usersSnapshotsCollection: MongoCollection[Document] =
    mongoClient
      .getDatabase(config.usersSnapshots.database)
      .withCodecRegistry(MongoUserRepository.registry)
      .getCollection[Document](config.usersSnapshots.collection)

  private lazy val networkCollection: MongoCollection[Document] = collection[Document](config.networks)

  private lazy val networksSnapshotsCollection: MongoCollection[Document] = collection[Document](config.networksSnapshots)

  private lazy val externalIdTypeCollection: MongoCollection[ExternalIdType] = {
    val registry = fromRegistries(fromProviders(
      IdTypeCodecProvider(NetworkId),
      Macros.createCodecProvider[ExternalIdType]),
      DEFAULT_CODEC_REGISTRY)
    collection[ExternalIdType](config.externalIdTypes, registry)
  }

  private lazy val transientStateCollection: MongoCollection[TransientState] =
    collection[TransientState](config.transientState, TransientStateRepository.V1.registry)

  private lazy val oauthTokensCollection: MongoCollection[OAuthToken] = collection[OAuthToken](config.tokens, OAuthTokenRepository.registry)

  private def collection[T: ClassTag](config: DbConfig,
      registry: CodecRegistry = DEFAULT_CODEC_REGISTRY): MongoCollection[T] =
    mongoClient
      .getDatabase(config.database)
      .withCodecRegistry(registry)
      .getCollection[T](config.collection)

  lazy val database: MongoDatabase = mongoClient.getDatabase(config.users.database)
  lazy val userRepository = new MongoUserRepository(
    mongoClient, entityCollection = userCollection, entitySnapshotCollection = usersSnapshotsCollection
  )

  lazy val userImpersonationRepository = new MongoUserImpersonationRepository(usersImpersonationCollection)

  lazy val userImpersonationApproversRepository = new MongoUserImpersonationApproversRepository(usersImpersonationApproversCollection)

  lazy val networkRepository = new MongoNetworkRepository(networkCollection, networksSnapshotsCollection, mongoClient)

  lazy val externalIdTypeRepository = new ExternalIdTypeRepository.V1(externalIdTypeCollection)

  lazy val passportRepository: PassportRepository =
    PassportRepository.V1(collection[PassportVerification](config.passport, PassportRepository.V1.registry))

  lazy val transientStateRepository = new TransientStateRepository.V1(transientStateCollection)

  lazy val tokenRepository = new OAuthTokenRepositoryImpl(oauthTokensCollection)

}
