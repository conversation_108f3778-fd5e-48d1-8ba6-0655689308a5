package com.simonmarkets.users

import akka.actor.ActorSystem
import akka.stream.Materializer
import com.amazonaws.services.lambda.runtime.{Context, RequestStreamHandler}
import com.goldmansachs.marquee.pipg.{License, UserACL}
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.mongodb.bson.AsyncFormat
import com.simonmarkets.taskprocessing.TasksManager.{TaskInput, TaskOutput}
import com.simonmarkets.taskprocessing.TasksRepository
import com.simonmarkets.taskprocessing.mongoscala.driver.TasksRepositoryMongoScalaDriver
import com.simonmarkets.taskprocessing.mongoscala.driver.format.ProcessingTaskFormat
import com.simonmarkets.users.api.request.UsersBatchTaskInput
import com.simonmarkets.users.api.response.UsersBatchTaskOutput
import com.simonmarkets.users.common.api.request.UpsertUserRequest
import com.simonmarkets.users.common.batch._
import com.simonmarkets.users.common.repository.encoder.{UserUploadTaskInputFormat, UserUploadTaskOutputFormat}
import com.simonmarkets.users.config.AppConfiguration
import com.simonmarkets.users.di.ServiceLocator
import com.simonmarkets.utils.config.Resolvers.SecretsConfigOps
import com.typesafe.config.ConfigFactory
import org.mongodb.scala.Document
import pureconfig.generic.auto._
import pureconfig.loadConfigOrThrow
import simon.Id.{NetworkId, ProcessingTaskId}

import java.io.{InputStream, OutputStream}

import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.language.postfixOps

class ProcessUsersBatchApp extends RequestStreamHandler with JsonCodecs with TraceLogging {

  import com.simonmarkets.users.config.AppConfiguration._

  private final implicit lazy val traceId: TraceId = TraceId("process-users-batch-bootstrap")

  log.info("Starting actor system")
  private final implicit val system: ActorSystem = ActorSystem("ProcessUsersBatch")
  log.info("Creating materializer")
  private implicit val mat: Materializer = Materializer(system)
  log.info("Setting up execution context")
  private implicit val executionContext: ExecutionContext = system.dispatcher

  private val rawConfig = ConfigFactory.load().resolveSecrets()
  private val config = loadConfigOrThrow[AppConfiguration](rawConfig)

  private final val Timeout = 120 seconds

  private val serviceLocator = new ServiceLocator(config)

  private final val tasksCollection = serviceLocator.database.getCollection[Document](config.mongoDB.tasks.collection)
  private final val taskInputsCollection = serviceLocator.database.getCollection[Document](config.mongoDB.inputs.collection)
  private final val taskOutputsCollection = serviceLocator.database.getCollection[Document](config.mongoDB.outputs.collection)
  private final val userService = serviceLocator.userService

  private final val tasksRepository: TasksRepository = new TasksRepositoryMongoScalaDriver(
    tasksCollection,
    taskFormat = ProcessingTaskFormat,
    inputs = taskInputsCollection,
    inputFormat = UserUploadTaskInputFormat,
    outputs = taskOutputsCollection,
    outputFormat = UserUploadTaskOutputFormat.asInstanceOf[AsyncFormat[TaskOutput]]
  )

  private def toUpsertRequest(input: UserUploadTaskInput, userId: Option[String]): UpsertUserRequest = {
    val npn = input.npn.filter(_.trim.nonEmpty).map(npn => License.NPN(npn))
    val crd = input.crd.filter(_.trim.nonEmpty).map(crd => License.CRD(crd))

    val email = if (input.operation == BatchOperation.Edit) {
      input.newEmail.filter(_.trim.nonEmpty).getOrElse(input.email)
    } else {
      input.email
    }
    UpsertUserRequest(
      id = userId,
      networkId = simon.Id.NetworkId(input.network),
      email = email,
      firstName = input.firstName,
      lastName = input.lastName,
      externalIds = input.externalIds,
      tradewebEligible = Some(input.tradewebEligible),
      regSEligible = Some(input.regSEligible),
      roles = input.roles,
      locations = Some(input.locations),
      faNumbers = Some(input.faNumbers),
      customRoles = Some(input.customRoles),
      licenses = Some(npn.toSet ++ crd.toSet),
      loginMode = input.loginMode,
      firmId = input.firmId,
      whiteLabelPartnerId = input.whiteLabelPartnerId,
      iCapitalUserId = input.iCapitalUserId,
      secondaryEmail = input.secondaryEmail,
    )
  }

  private def buildTraceId(taskInput: UsersBatchTaskInput): TraceId =
    TraceId(s"${ProcessingTaskId unwrap taskInput.id}-${taskInput.batch.from}-${taskInput.batch.to}")

  override def handleRequest(inputStream: InputStream, outputStream: OutputStream, context: Context): Unit = {
    implicit val traceId: TraceId = TraceId(context.getAwsRequestId)

    val bufferedSource = scala.io.Source.fromInputStream(inputStream)
    val inputString = bufferedSource.mkString
    bufferedSource.close()

    decode[UsersBatchTaskInput](inputString) match {
      case Right(taskInput: UsersBatchTaskInput) =>
        implicit val traceId: TraceId = buildTraceId(taskInput)

        val taskOutputF: Future[UsersBatchTaskOutput] = for {
          user <- serviceLocator.httpACLClient.getUserACL(taskInput.initiator)
          taskInputs <- tasksRepository.findTaskInputs(taskInput.id, taskInput.batch.from, taskInput.batch.to)
          processedInputs: Seq[Future[TaskOutput]] = taskInputs.toSeq.map(processInput(_, user))
          outputs <- Future.sequence(processedInputs)
          _ <- if (outputs.nonEmpty) tasksRepository.saveTaskOutput(outputs.toList) else {
            log.warn(s"Empty task outputs for task", taskInput.id)
            Future.successful(Unit)
          }
        } yield UsersBatchTaskOutput(id = taskInput.id, initiator = taskInput.initiator, networkId = taskInput.networkId, batch = taskInput.batch)

        val resultString = Await.result(taskOutputF.map(_.asJsonStr), Timeout)

        outputStream.write(resultString.getBytes())
        outputStream.flush()
        outputStream.close()

      case Left(e) =>
        log.error(e, s"Cannot parse task event", inputString)
        throw HttpError.badRequest(s"Cannot parse task event: $inputString")
    }
  }

  private def processInput(taskInput: TaskInput, acl: UserACL): Future[TaskOutput] = {
    taskInput match {
      case input: UserUploadTaskInput =>

        val succeed = UserUploadSucceed(
          taskId = input.taskId,
          row = input.row,
          warning = "",
          email = input.email,
          operation = input.operation
        )

        lazy val userIdFut = userService.idFromEmail(input.email, NetworkId(input.network), acl)

        val response = input.operation match {
          case BatchOperation.Add =>
            userService.handleInsertRequest(acl, toUpsertRequest(input, None))
          case BatchOperation.Edit =>
            userIdFut.flatMap(userId => userService.handleUpdateRequest(acl, toUpsertRequest(input, Some(userId))))
          case BatchOperation.Deactivate =>
            userIdFut.flatMap(userService.deactivateUser(acl, _))
          case BatchOperation.`Reset-Password` =>
            userIdFut.flatMap(userService.resetPassword(acl, _))
        }

        response.map(_ => succeed).recoverWith {
          case e: Exception =>
            log.error(e, "Exception on user batch modification")
            Future.successful(UserUploadFailed(
              taskId = input.taskId,
              row = input.row,
              error = e.toString,
              email = input.email,
              operation = input.operation
            ))
        }
    }
  }
}
