package com.simonmarkets.users.domain

import com.simonmarkets.okta.domain.UserId

sealed trait AccessTokenServiceError extends Exception {
  def message: String
}

case class NotAuthorized(message: String) extends AccessTokenServiceError

case class UserNotFound(userId: UserId) extends AccessTokenServiceError {
  override def message: String = s"User not found $userId"
}

case class GeneralNotFound(message: String) extends AccessTokenServiceError

case class Internal(message: String) extends AccessTokenServiceError

case class OktaDecodeError(message: String) extends AccessTokenServiceError

object OktaDecodeError {

  def apply(field: String, message: String): OktaDecodeError =
    OktaDecodeError(field + " decoding error: " + message)

  def apply(field: String, t: Throwable): OktaDecodeError =
    OktaDecodeError(field, t.toString)

}

case class UnableToEnrichFromIdService(message: String) extends AccessTokenServiceError