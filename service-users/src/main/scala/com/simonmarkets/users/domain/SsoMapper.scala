package com.simonmarkets.users.domain

import com.okta.sdk.resource.model.UserProfile
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.users.common.api.request.UpsertUserRequest
import com.simonmarkets.users.domain.SsoField._
import simon.Id.NetworkId

trait SsoMapper {
  def applyAll(request: UpsertUserRequest, oktaUser: UserProfile): Either[OktaDecodeError, UpsertUserRequest]

  def getSsoClientId(oktaUser: UserProfile): Either[OktaDecodeError, Option[String]]

  def getSsoNetworkId(oktaUser: UserProfile): Either[OktaDecodeError, Option[NetworkId]]

  def getSsoExternalNetworkId(oktaUser: UserProfile): Either[OktaDecodeError, Option[ExternalId]]

  def getNetworkId(oktaUser: UserProfile): Either[OktaDecodeError, Option[NetworkId]]

  def setContext(oktaUser: UserProfile, request: UpsertUserRequest): Either[OktaDecodeError, UpsertUserRequest]
}

object SsoMapper {

  object Impl extends SsoMapper {

    override def applyAll(request: UpsertUserRequest, oktaUser: UserProfile): Either[OktaDecodeError, UpsertUserRequest] = {
      val lenses = SsoField.values
      lenses.foldLeft[Either[OktaDecodeError, UpsertUserRequest]](Right(request)) { (request, lens) =>
        request.flatMap(r => lens.set(r, oktaUser))
      }
    }

    override def getSsoClientId(oktaUser: UserProfile): Either[OktaDecodeError, Option[String]] = SsoClientId.get(oktaUser)

    override def getSsoNetworkId(oktaUser: UserProfile): Either[OktaDecodeError, Option[NetworkId]] =
      SsoNetwork.get(oktaUser)

    override def getSsoExternalNetworkId(oktaUser: UserProfile): Either[OktaDecodeError, Option[ExternalId]] =
      SsoExternalNetworkId.get(oktaUser)

    def getNetworkId(oktaUser: UserProfile): Either[OktaDecodeError, Option[NetworkId]] =
      Network.get(oktaUser)

    override def setContext(oktaUser: UserProfile, request: UpsertUserRequest): Either[OktaDecodeError, UpsertUserRequest] =
      SsoContext.set(request, oktaUser)

  }
}
