package com.simonmarkets.users.domain

import com.simonmarkets.networks.common.Network

sealed trait SsoUserType

sealed trait NoJit extends SsoUserType

case object ContextOnly extends NoJit

sealed trait Jit extends SsoUserType {
  def network: Network
}

case class JitSso(network: Network) extends Jit

case class JitIdentitySso(network: Network) extends Jit

case object NoJitIdentitySso extends NoJit
