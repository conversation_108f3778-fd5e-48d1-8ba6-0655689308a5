package com.simonmarkets.users.domain

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues

import java.time.Instant

case class UserImpersonation(
    id: String,
    impersonatorUserId: String,
    impersonatedUserId: String,
    impersonatedNetworkId: String,
    reason: String,
    ticketNumber: Option[String],
    status: ImpersonationStatus,
    createdAt: Instant,
    completedAt: Option[Instant],
    traceId: String,
    approverUserId: Option[String],
    approversUserIds: Option[Set[String]],
    entitlements: Set[String]
)

@EnumValues("Pending", "Complete", "Expired", "NotSubmitted", "Submitted", "Approved", "Rejected")
sealed trait ImpersonationStatus extends EnumEntry

object ImpersonationStatus extends ProductEnums[ImpersonationStatus] {
  case object Pending extends ImpersonationStatus

  case object Complete extends ImpersonationStatus

  case object Expired extends ImpersonationStatus

  case object NotSubmitted extends ImpersonationStatus

  case object Submitted extends ImpersonationStatus

  case object Approved extends ImpersonationStatus

  case object Rejected extends ImpersonationStatus

  override def Values: Seq[ImpersonationStatus] = Seq(Pending, Complete, Expired, NotSubmitted, Submitted, Approved, Rejected)
}

case class ImpersonationStatusResponse(status: String)
