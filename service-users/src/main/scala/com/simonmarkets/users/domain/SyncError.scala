package com.simonmarkets.users.domain

import akka.http.scaladsl.model.StatusCodes
import com.simonmarkets.http.HttpError
import zio._

sealed trait SyncError extends Throwable {
  def message: String
  override def getMessage: String = message
}

object SyncError {

  final case class Internal(msg: String) extends SyncError {
    def message: String = "unexpected error: " + msg
  }

  final case class NotFound(msg: String) extends SyncError {
    def message: String = "not found: " + msg
  }

  final case class Skipped(id: String, msg: String) extends SyncError {
    def message: String = s"skipping syncing user icnUserId=$id cause:" + msg
  }

  final case class Unauthorized(msg: String) extends SyncError {
    def message: String = "unauthorized: " + msg
  }

  def fromThrowable(message: String): Throwable => UIO[SyncError] = {
    case err @ HttpError(_, StatusCodes.Unauthorized) =>
      ZIO.succeed(Unauthorized(message + ": " + err.payload)).tap(e => ZIO.logError(e.getMessage))
    case err @ HttpError(_, StatusCodes.NotFound) =>
      ZIO.succeed(NotFound(message + ": " + err.payload)).tap(e => ZIO.logError(e.getMessage))
    case err => ZIO.succeed(Internal(err.getMessage)).tap(e => ZIO.logError(e.getMessage))
  }

}
