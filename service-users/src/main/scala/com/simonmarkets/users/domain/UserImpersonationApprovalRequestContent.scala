package com.simonmarkets.users.domain

import io.circe.Encoder
import io.circe.generic.semiauto.deriveEncoder
import simon.Id.NetworkId

case class UserImpersonationApprovalRequestContent(
  impersonationUUID: String,
  impersonatorUserId: String,
  impersonatorUserName: String,
  impersonatedUserId: String,
  impersonatedUserName: String,
  networkId: NetworkId,
  networkName: String,
  reason: String
)

object UserImpersonationApprovalRequestContent {
  implicit val enc: Encoder[UserImpersonationApprovalRequestContent] = deriveEncoder[UserImpersonationApprovalRequestContent]
  implicit val encNetworkId: Encoder[NetworkId] = Encoder.encodeString.contramap[NetworkId](_.toString())
}
