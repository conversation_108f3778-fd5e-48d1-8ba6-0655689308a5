package com.simonmarkets.users.domain

import io.circe.Encoder
import io.circe.generic.semiauto.deriveEncoder
import simon.Id.NetworkId

case class UserImpersonationStatusUpdateContent(
  impersonatedUserId: String,
  impersonatedUserName: String,
  approverUserName: String,
  networkId: NetworkId,
  networkName: String,
  reason: String,
  status: ImpersonationStatus
)

object UserImpersonationStatusUpdateContent {
  implicit val enc: Encoder[UserImpersonationStatusUpdateContent] = deriveEncoder[UserImpersonationStatusUpdateContent]
  implicit val encNetworkId: Encoder[NetworkId] = Encoder.encodeString.contramap[NetworkId](_.toString())
  implicit val encStatus: Encoder[ImpersonationStatus] = Encoder.encodeString.contramap[ImpersonationStatus](_.toString())
}
