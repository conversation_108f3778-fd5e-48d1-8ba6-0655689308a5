package com.simonmarkets.users.domain

import com.goldmansachs.marquee.pipg.{License, LicenseName, UserRole}
import com.okta.sdk.resource.model.UserProfile
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.users.common.User.DistributorKey
import com.simonmarkets.users.common.api.request.UpsertUserRequest
import com.simonmarkets.users.common.{Context, User}
import com.simonmarkets.users.util.OktaProfileExtractors
import com.simonmarkets.users.util.OktaProfileExtractors.FieldExtractor
import org.apache.commons.lang3.StringUtils
import simon.Id.NetworkId

import java.time.Instant
import java.time.temporal.ChronoUnit

import scala.util.Try

/**
 * Goal of this is to provide a consistent API to convert b/w okta user object (java) and SIMON user fields There are a
 * few levers you can pull via overrides, but otherwise everything should come for free based on the `FieldType`
 *
 * Overrides
 *
 *   - FieldType - This is the field that you want to extract to, typically just match to what appears on the
 *     `UpsertUserRequest`. For legacy reasons, this is not always the case
 *   - field - This defaults to using the name of your case object and should match the field name in okta
 *   - extractor - This will get implicitly pulled in from the companion object where available based on your
 *     `FieldType`. Provide an implicit in the object to override. See
 *     [[com.simonmarkets.users.util.OktaProfileExtractors]].
 */
sealed trait SsoField[FieldType] extends Product { self =>

  def field: String = StringUtils.uncapitalize(self.productPrefix)

  implicit def extractor: FieldExtractor[FieldType]

  final def get(profile: UserProfile): Either[OktaDecodeError, FieldType] = extractor(field)(profile)

  def set(request: UpsertUserRequest, profile: UserProfile): Either[OktaDecodeError, UpsertUserRequest]

}

object SsoField {

  import OktaProfileExtractors._

  def values: Set[SsoField[_]] = Set(
    SsoAccountInContext,
    SsoAltsGroups,
    SsoAltsRoles,
    SsoClientId,
    SsoContext,
    SsoCustomRoles,
    SsoCustomRolesCommaSeparated,
    SsoEmail,
    SsoExternalIds,
    SsoFaNumbers,
    SsoFaNumbersCommaSeparated,
    SsoFirmId,
    SsoFirstName,
    SsoLastName,
    SsoLocations,
    SsoLocationsCommaSeparated,
    SsoNpn,
    SsoOmsId,
    SsoPurviewLicenses,
    SsoPurviewNsccCodes,
    SsoRegSEligible,
    SsoTradewebEligible,
    SsoUserRoles,
    SsoWlpId
  )

  case object SsoAccountInContext extends DefaultSsoField[Option[String]] {

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map(account => request.copy(accountInContext = account))

  }

  case object SsoAltsGroups extends DefaultSsoField[Option[Set[Int]]] {

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map { icnGroups =>
        if (icnGroups.isEmpty)
          request
        else
          request.copy(icnGroups = icnGroups.map(_.map(_.toString)))
      }

  }

  case object SsoAltsRoles extends DefaultSsoField[Option[Set[Int]]] {

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map { icnRoles =>
        if (icnRoles.isEmpty)
          request
        else
          request.copy(icnRoles = icnRoles.map(_.map(_.toString)))
      }

  }

  case object SsoClientId extends DefaultSsoField[Option[String]] {

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map {
        case Some(value) => request.copy(externalIds = request.externalIds + (DistributorKey -> value))
        case None        => request
      }

  }

  case object SsoContext extends SsoField[Option[Context]] {

    private val SsoContextAccountId = "ssoContextAccountId"
    private val SsoContextProposalId = "ssoContextProposalId"
    private val SsoContextTaskId = "ssoContextTaskId"
    private val SsoContextServiceRequestId = "ssoContextServiceRequestId"
    private val SsoContextBucketId = "ssoContextBucketId"
    private val SsoContextExternalRoles = "ssoContextExternalRoles"

    override implicit def extractor: FieldExtractor[Option[Context]] =
      _ =>
        profile =>
          for {
            accountId <- stringOptDecoder(SsoContextAccountId)(profile)
            proposalId <- stringOptDecoder(SsoContextProposalId)(profile)
            taskId <- stringOptDecoder(SsoContextTaskId)(profile)
            serviceRequestId <- stringOptDecoder(SsoContextServiceRequestId)(profile)
            bucketId <- stringOptDecoder(SsoContextBucketId)(profile)
            externalRoles <- stringSetOptDecoder(SsoContextExternalRoles)(profile)
          } yield
            if (Seq(accountId, proposalId, taskId, serviceRequestId, bucketId, externalRoles).exists(_.isDefined))
              Some(
                Context(
                  accountId = accountId,
                  proposalId = proposalId,
                  taskId = taskId,
                  serviceRequestId = serviceRequestId,
                  bucketId = bucketId,
                  externalRoles = externalRoles,
                  lastUpdatedAt = Some(Instant.now.truncatedTo(ChronoUnit.SECONDS))
                )
              )
            else
              None

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map {
        case ctx @ Some(_) => request.copy(context = ctx)
        case None          => request.copy(context = None) // reset on login
      }

  }

  case object SsoCustomRoles extends DefaultSsoField[Option[Set[String]]] {

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map { customRoles =>
        if (customRoles.isEmpty)
          request
        else
          request.copy(customRoles = customRoles)
      }

  }

  case object SsoCustomRolesCommaSeparated extends CommaSeparatedSsoField {

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map {
        case Some(customRoles) =>
          if (customRoles.isEmpty)
            request
          else
            request.copy(customRoles = Some(customRoles))
        case None => request
      }

  }

  case object SsoEmail extends DefaultSsoField[Option[String]] {

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map {
        case Some(email) => request.copy(email = email)
        case None        => request
      }

  }

  case object SsoExternalIds extends SsoField[Set[ExternalId]] {

    private val SsoFieldPrefix = "ssoExternalId_"

    implicit def extractor: FieldExtractor[Set[ExternalId]] =
      _ =>
        profile =>
          Right {
            profile
              .properties
              .keySet
              .filter(_.startsWith(SsoFieldPrefix))
              .flatMap { key =>
                profile
                  .stringNonEmptyOpt(key)
                  .toOption
                  .flatMap(_.map(id => ExternalId(subject = key.substring(SsoFieldPrefix.length), id = id)))
              }
          }

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map { externalIds =>
        if (externalIds.isEmpty)
          request
        else
          request.copy(externalIds = update(request.externalIds, externalIds))
      }

    private def update(existing: Map[String, String], add: Set[ExternalId]) = {
      val existingMap = ExternalId.fromStringMap(existing)
      val combined = ExternalId.upsert(existingMap, add.toSeq)
      ExternalId.toStringMap(combined)
    }

  }

  case object SsoExternalNetworkId extends SsoField[Option[ExternalId]] {

    private val SsoSubjectNetwork = "ssoSubjectNetwork"
    private val SsoExternalNetworkId = "ssoExternalNetworkId"

    override implicit def extractor: FieldExtractor[Option[ExternalId]] =
      _ =>
        profile =>
          for {
            subject <- stringOptDecoder(SsoSubjectNetwork)(profile)
            id <- stringOptDecoder(SsoExternalNetworkId)(profile)
          } yield subject.flatMap(s => id.map(i => ExternalId(subject = s, id = i)))

    // intentionally a no-op
    def set(request: UpsertUserRequest, profile: UserProfile) = Right(request)

  }

  case object SsoFaNumbers extends DefaultSsoField[Set[String]] {

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map { faNumbers =>
        val filtered = faNumbers.filterNot(_.isBlank)
        if (filtered.isEmpty)
          request
        else
          request.copy(faNumbers = Some(filtered))
      }

  }

  case object SsoFaNumbersCommaSeparated extends CommaSeparatedSsoField {

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map {
        case Some(faNumbers) =>
          if (faNumbers.isEmpty)
            request
          else
            request.copy(faNumbers = Some(faNumbers))
        case None => request
      }

  }

  case object SsoFirmId extends DefaultSsoField[Option[Int]] {

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map {
        case firmId @ Some(_) => request.copy(firmId = firmId.map(_.toString))
        case None             => request
      }

  }

  case object SsoFirstName extends DefaultSsoField[Option[String]] {

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map {
        case Some(name) => request.copy(firstName = name)
        case None       => request
      }

  }

  case object SsoLastName extends DefaultSsoField[Option[String]] {

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map {
        case Some(name) => request.copy(lastName = name)
        case None       => request
      }

  }

  case object SsoLocations extends DefaultSsoField[Set[String]] {

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map { locations =>
        if (locations.isEmpty)
          request
        else
          request.copy(locations = Some(locations))
      }

  }

  case object SsoLocationsCommaSeparated extends CommaSeparatedSsoField {

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map {
        case Some(locations) =>
          if (locations.isEmpty)
            request
          else
            request.copy(locations = Some(locations))
        case None => request
      }

  }

  case object SsoNetwork extends DefaultSsoField[Option[NetworkId]] {

    // intentionally a no-op
    def set(request: UpsertUserRequest, profile: UserProfile) = Right(request)

  }

  case object Network extends DefaultSsoField[Option[NetworkId]] {

    // intentionally a no-op
    def set(request: UpsertUserRequest, profile: UserProfile) = Right(request)

  }

  case object SsoNpn extends SsoField[Option[License]] {

    implicit def extractor: FieldExtractor[Option[License]] =
      field => stringOptDecoder(field).andThen(_.map(_.map(License.NPN)))

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map {
        case Some(npn) =>
          request.copy(licenses = Some(request.licenses.fold(Set(npn))(_.filterNot(_.name == LicenseName.NPN) + npn)))
        case None => request
      }

  }

  case object SsoOmsId extends DefaultSsoField[Option[String]] {

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map {
        case Some(omsId) => request.copy(externalIds = request.externalIds + (User.OmsKey -> omsId))
        case None        => request
      }

  }

  case object SsoPurviewLicenses extends SsoField[Option[Set[License]]] {

    implicit def extractor: FieldExtractor[Option[Set[License]]] =
      field =>
        profile =>
          profile
            .stringSetOpt(field)
            .flatMap {
              case Some(licenses) =>
                licenses
                  .filter(_.nonEmpty)
                  .map { licenseStr =>
                    licenseStr.split(":") match {
                      case Array(name, num) =>
                        Try(LicenseName(name)).toEither match {
                          case Left(invalid) => Left(OktaDecodeError(field, "invalid license type: " + invalid))
                          case Right(name)   => Right(License(name, num, name.licenseType, None))
                        }
                      case _ =>
                        Left(OktaDecodeError(field, "invalid license str: " + licenseStr))
                    }
                  }
                  .foldLeft[Either[OktaDecodeError, Set[License]]](Right(Set.empty)) { case (acc, license) =>
                    for {
                      ls <- acc.right
                      l <- license.right
                    } yield ls + l
                  }
                  .map(Some.apply)
              case None => Right(None)
            }

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map {
        case licenses @ Some(_) => request.copy(purviewLicenses = licenses)
        case None               => request
      }

  }

  case object SsoPurviewNsccCodes extends CommaSeparatedSsoField {

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map {
        case Some(codes) =>
          if (codes.isEmpty)
            request
          else
            request.copy(purviewNsccCodes = Some(codes))
        case None => request
      }

  }

  case object SsoRegSEligible extends DefaultSsoField[Option[Boolean]] {

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map {
        case value @ Some(_) => request.copy(regSEligible = value)
        case None            => request
      }

  }

  case object SsoTradewebEligible extends DefaultSsoField[Option[Boolean]] {

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map {
        case value @ Some(_) => request.copy(tradewebEligible = value)
        case None            => request
      }

  }

  case object SsoUserRoles extends DefaultSsoField[Option[Set[String]]] {

    private val defaultRoles: Set[UserRole] = Set(UserRole.EqPIPGFA)

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map { roles =>
        if (roles.isEmpty && request.roles.isEmpty)
          request.copy(roles = defaultRoles)
        else if (roles.isEmpty)
          request
        else
          request.copy(roles = roles.map(_.map(UserRole.apply)).getOrElse(defaultRoles))
      }

  }

  case object SsoWlpId extends DefaultSsoField[Option[Int]] {

    def set(request: UpsertUserRequest, profile: UserProfile) =
      get(profile).map {
        case wlpId @ Some(_) => request.copy(whiteLabelPartnerId = wlpId.map(_.toString))
        case None            => request
      }

  }

  sealed abstract class DefaultSsoField[FieldType](implicit val extractor: FieldExtractor[FieldType])
      extends SsoField[FieldType]

  sealed abstract class CommaSeparatedSsoField extends SsoField[Option[Set[String]]] {

    implicit def extractor: FieldExtractor[Option[Set[String]]] =
      field => profile => profile.stringSetCommaSeparatedOpt(field)

  }

}
