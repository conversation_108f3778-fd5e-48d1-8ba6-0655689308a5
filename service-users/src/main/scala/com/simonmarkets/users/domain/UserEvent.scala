package com.simonmarkets.users.domain

import com.goldmansachs.marquee.pipg.License
import com.goldmansachs.marquee.pipg.User.DistributorInfo
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.shared.MaskedId
import com.simonmarkets.users.common.{Context, EventInfo, LoginMode, UserType}
import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}
import io.circe.{Decoder, Encoder}
import io.simon.openapi.annotation.Field.Type
import io.simon.openapi.annotation.OpenApiType
import zio._
import zio.json.{DeriveJsonDecoder, DeriveJsonEncoder, JsonDecoder, JsonEncoder}
import zio.kafka.serde.Serde

import java.time.LocalDateTime


case class UserEvent(
    id: String,
    @Type(OpenApiType.String)
    networkId: String,
    createdAt: LocalDateTime,
    createdBy: String,
    updatedAt: LocalDateTime,
    updatedBy: String,
    lastVisitedAt: Option[LocalDateTime],
    email: String,
    firstName: String,
    lastName: String,
    tradewebEligible: Boolean = false,
    regSEligible: Boolean = false,
    isActive: Boolean = true,
    locations: Set[String] = Set.empty,
    faNumbers: Set[String] = Set.empty,
    customRoles: Set[String] = Set.empty,
    maskedIds: Set[MaskedId] = Set.empty,
    licenses: Set[License] = Set.empty,
    idpId: Option[String] = None,
    distributorInfo: Option[DistributorInfo] = None,
    context: Option[Context] = None,
    cusips: Set[String] = Set.empty,
    idpLoginId: String,
    version: Int = 0,
    loginMode: LoginMode,
    userType: UserType,
    eventInfo: EventInfo,
    externalIds: Seq[ExternalId],
    purviewLicenses: Set[License] = Set.empty,
    purviewNsccCodes: Set[String] = Set.empty,
    iCapitalUserId: Option[String] = None,
    entitlements: Set[String] = Set.empty,
    icnRoles: Set[String] = Set.empty,
    icnGroups: Set[String] = Set.empty,
    passport: Map[String, Int] = Map.empty
)


object UserEvent extends JsonCodecs {

  import SerdeHelper.jsonSerde

  implicit val encoder: Encoder[UserEvent] = deriveEncoder
  implicit val decoder: Decoder[UserEvent] = deriveDecoder

  val userEventSerde: Serde[Any, UserEvent] = jsonSerde[UserEvent]
}

case class KafkaUserMessageResponse(
    userId: String,
    topic: String,
    partition: Int,
    eventInfo: EventInfo
)

case class IcnUserEvent(
    contact_card_id: Int,
    first_name: String,
    last_name: String,
    email: String,
    title: Option[String],
    job_title: Option[String],
    white_label_partner_id: Int,
    branch_code: Option[String],
    user_id: Int,
    phone: Option[String],
    disabled: Option[Boolean],
    country_code: Option[String],
    firm_id: Option[Int],
    user_guid: Option[String],
    simon_user_id: Option[String],
    user_groups: Option[Set[String]],
    user_external_ids: Option[Seq[IcnExternalId]],
    user_roles: Option[Set[String]],
    encrypted_password: Option[String],
) {

  def simonExternalIds: Seq[ExternalId] = user_external_ids.fold(Seq.empty[ExternalId])(_.map(_.toSimonExternalId))

  def simonExternalIdMap: Map[String, String] =
    user_external_ids.fold(Map.empty[String, String])(_.map(e => e.external_type -> e.external_id).toMap)

}

object IcnUserEvent extends JsonCodecs {
  implicit val encoder: Encoder[IcnUserEvent] = deriveEncoder
  implicit val decoder: Decoder[IcnUserEvent] = deriveDecoder
  implicit val icnUserEventDecoder: JsonDecoder[IcnUserEvent] = DeriveJsonDecoder.gen[IcnUserEvent]
  implicit val icnUserEventEncoder: JsonEncoder[IcnUserEvent] = DeriveJsonEncoder.gen[IcnUserEvent]
}

case class IcnExternalId(
    external_id: String,
    external_type: String,
) {
  def toSimonExternalId: ExternalId = {
    ExternalId(external_type, external_id)
  }
}

object IcnExternalId {
  implicit val icnExternalIdEncoder: JsonEncoder[IcnExternalId] = DeriveJsonEncoder.gen[IcnExternalId]
  implicit val icnExternalIdDecoder: JsonDecoder[IcnExternalId] = DeriveJsonDecoder.gen[IcnExternalId]
}



object SerdeHelper extends JsonCodecs {
  def jsonSerde[A](implicit encoder: Encoder[A],
      decoder: Decoder[A]): Serde[Any, A] = Serde.string.inmapM { msgString =>
    ZIO.fromEither(decode(msgString)(decoder)).mapError(new RuntimeException(_))
  } { msgObj => ZIO.succeed(msgObj.asJsonStr) }

}
