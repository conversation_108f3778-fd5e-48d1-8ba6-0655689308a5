package com.simonmarkets.users.domain

import com.simonmarkets.logging.TraceId

import scala.concurrent.duration.FiniteDuration

sealed trait OktaBaseUser {

  def scopes: Set[String]

  def icnId: Option[String]

  def wlp: Option[String]

  def expiration: Option[FiniteDuration]

  def trace: TraceId
}

case class SystemUser(
    icnId: Option[String],
    wlp: Option[String],
    scopes: Set[String],
    expiration: Option[FiniteDuration],
    trace: TraceId
) extends OktaBaseUser

case class HumanUser(
    userId: String,
    icnId: Option[String],
    wlp: Option[String],
    impersonatorUserId: Option[String],
    impersonatorIcnId: Option[String],
    scopes: Set[String],
    expiration: Option[FiniteDuration],
    trace: TraceId
) extends OktaBaseUser
