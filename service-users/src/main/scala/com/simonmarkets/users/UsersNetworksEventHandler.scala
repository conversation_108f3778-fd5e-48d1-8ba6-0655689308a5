package com.simonmarkets.users

import cats.Functor.ops.toAllFunctorOps
import com.amazonaws.services.lambda.runtime.{Context, RequestStreamHandler}
import com.simonmarkets.logging.aws.lambda.LoggingHelperFuncs
import com.simonmarkets.logging.{InstanceTraceId, InstanceTraceLogging, TraceId}
import com.simonmarkets.mongodb.trigger.codec.MongoTriggerJsonCodecs
import com.simonmarkets.mongodb.trigger.event.{Event, MfaOperation, MongoEvent, MongoEventWithoutDocument, NetworkMfaSyncEvent, UserSyncEvent}
import com.simonmarkets.networks.common.trigger.NetworkPayload
import com.simonmarkets.users.UsersNetworksEventHandler._
import com.simonmarkets.users.common.trigger.UserPayload
import com.simonmarkets.users.config.MongoConfiguration
import io.circe.Decoder

import java.io.{InputStream, OutputStream}
import java.util.concurrent.atomic.AtomicInteger

import scala.concurrent.duration.{FiniteDuration, _}
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success}

trait UsersNetworksEventHandler[T] extends RequestStreamHandler with InstanceTraceLogging with MongoTriggerJsonCodecs {

  protected def handleUser(userPayload: MongoEvent[UserPayload])(implicit traceId: TraceId): Future[T]

  protected def handleNetwork(networkPayload: MongoEvent[NetworkPayload])(implicit traceId: TraceId): Future[T]

  protected def handleSimpleUser(userId: String)(implicit traceId: TraceId): Future[T]

  protected def handleNetworkMfa(userId: String, groupId: String, operation: MfaOperation)(implicit traceId: TraceId): Future[T]

  protected val dbConfig: MongoConfiguration

  protected implicit def executionContext: ExecutionContext

  protected def timeout: FiniteDuration = 15.minutes

  protected def logResponseBody: Boolean

  protected def currentSimonTime(): String = LoggingHelperFuncs.currentSimonTime

  protected def currentTimeMillis(): Long = System.currentTimeMillis()

  private val nextSerialState = new AtomicInteger(1)

  protected def nextSerial(): Int = nextSerialState.getAndIncrement()

  protected def generateInstanceTraceId(input: InputStream, context: Context): InstanceTraceId = {
    val _ = (input, context)
    InstanceTraceId.Empty
  }

  private def decodeEvent(raw: String): Future[Event] = {
    val eventDecoder: Decoder[Event] =
      List[Decoder[Event]](
        Decoder[MongoEventWithoutDocument].widen,
        Decoder[NetworkMfaSyncEvent].widen,
        Decoder[UserSyncEvent].widen
      ).reduceLeft(_ or _)

    decode(raw)(eventDecoder) match {
      case Left(error) => Future.failed(new Exception(s"Unable to decode event: $raw \n$error"))
      case Right(value) => Future.successful(value)
    }
  }

  /* TODO: because currently we use single lambda for networks_new and users collection with different payloads
  we cannot use proper abstraction for logging similar to AwsLambda3Logging. Once we will split networks_new
  trigger handler into separate lambda we will be able to use something similar. Until that we will use ad-hoc approach.
  */
  override def handleRequest(inputStream: InputStream, outputStream: OutputStream, context: Context): Unit = {
    implicit val traceId: TraceId = TraceId(context.getAwsRequestId)
    implicit val instanceTraceId: InstanceTraceId = generateInstanceTraceId(inputStream, context)

    val amazonRequestId = Option(context.getAwsRequestId).getOrElse("")
    val serial = nextSerial()
    val startTime = currentTimeMillis()

    if (LoggingHelperFuncs.isWarmUpReq(context)) {
      log.info("Got warmup request")
    } else {
      val bufferedSource = scala.io.Source.fromInputStream(inputStream)
      val rawEvent = bufferedSource.mkString
      bufferedSource.close()

      log.info("rawEvent", rawEvent)

      val result = decodeEvent(rawEvent).flatMap {
        case event: MongoEventWithoutDocument =>
          val db = event.detail.ns.db
          val collection = event.detail.ns.coll
          (db, collection) match {
            case (dbConfig.users.database, dbConfig.users.collection) =>
              Future.fromTry(parseEvent[MongoEvent[UserPayload]](rawEvent)).flatMap(event => {
                logStartLambda(event, amazonRequestId, serial, userPayloadLoggable)
                handleUser(event)
              })

            case (dbConfig.networks.database, dbConfig.networks.collection) =>
              Future.fromTry(parseEvent[MongoEvent[NetworkPayload]](rawEvent)).flatMap(event => {
                logStartLambda(event, amazonRequestId, serial, networkPayloadLoggable)
                handleNetwork(event)
              })

            case _ =>
              Future.failed(new Exception(s"Invalid db or collection, db=$db, collection=$collection"))
          }
        case event: UserSyncEvent =>
          log.info("Got UserSyncEvent")
          logStartLambda(event, amazonRequestId, serial, simplePayloadLoggable)
          handleSimpleUser(event.detail.userId)
        case event: NetworkMfaSyncEvent =>
          log.info("Got NetworkMfaSyncEvent")
          logStartLambda(event, amazonRequestId, serial, networkMfaPayloadLoggable)
          handleNetworkMfa(event.detail.userId, event.detail.groupId, event.detail.operation)
      }

      val loggedResult = result.transform { resTry =>
        resTry match {
          case Failure(exception) =>
            logUnhandledExceptionResponse(exception, amazonRequestId, serial, startTime, endTime = currentTimeMillis())
          case Success(response) =>
            logEndLambda(response, amazonRequestId, serial, startTime, endTime = currentTimeMillis(), logResponseBody)
        }
        resTry
      }

      Await.result(loggedResult, timeout)
    }
  }

  trait EventLoggable[A] extends InstanceTraceLogging {
    def logEvent(e: A)(implicit traceId: TraceId, instanceTraceId: InstanceTraceId): Unit
  }

  val userPayloadLoggable: EventLoggable[MongoEvent[UserPayload]] = new EventLoggable[MongoEvent[UserPayload]] {
    override def logEvent(e: MongoEvent[UserPayload])
      (implicit traceId: TraceId, instanceTraceId: InstanceTraceId): Unit = {
      e.detail.fullDocument.foreach(user => {
        log.info(eventInfoMessage, e.detail.ns, user.id, user.version, user.updatedBy, user.updatedAt)
      })
    }
  }

  val networkPayloadLoggable: EventLoggable[MongoEvent[NetworkPayload]] = new EventLoggable[MongoEvent[NetworkPayload]] {
    override def logEvent(e: MongoEvent[NetworkPayload])
      (implicit traceId: TraceId, instanceTraceId: InstanceTraceId): Unit = {
      e.detail.fullDocument.foreach(network => {
        log.info(eventInfoMessage, e.detail.ns, network.id, network.version, network.name)
      })
    }
  }

  val simplePayloadLoggable: EventLoggable[UserSyncEvent] = new EventLoggable[UserSyncEvent] {
    override def logEvent(e: UserSyncEvent)(implicit traceId: TraceId, instanceTraceId: InstanceTraceId): Unit = {
      log.info(eventInfoMessage, e.detail.userId)
    }
  }

  val networkMfaPayloadLoggable: EventLoggable[NetworkMfaSyncEvent] = new EventLoggable[NetworkMfaSyncEvent] {
    override def logEvent(e: NetworkMfaSyncEvent)(implicit traceId: TraceId, instanceTraceId: InstanceTraceId): Unit = {
      log.info(eventInfoMessage, e.detail.userId, e.detail.groupId, e.detail.operation)
    }
  }

  private def logStartLambda[A](
      request: A,
      amazonRequestId: String,
      serial: Int,
      loggable: EventLoggable[A])(implicit traceId: TraceId, instanceTraceId: InstanceTraceId): Unit = {
    val simonTimestamp = currentSimonTime()
    log.info(
      processingStartMessage,
      simonTimestamp,
      serial,
      amazonRequestId)

    loggable.logEvent(request)
  }

  private def logEndLambda(
      response: T,
      amazonRequestId: String,
      serial: Int,
      startTime: Long,
      endTime: Long,
      logResponseBody: Boolean
  )(implicit traceId: TraceId, instanceTraceId: InstanceTraceId): Unit = {
    val timeTakenMillis = endTime - startTime
    val simonTimestamp = currentSimonTime()
    val responseBody = if (logResponseBody) response.toString else ""

    log.info(
      processingEndMessage,
      simonTimestamp,
      serial,
      amazonRequestId,
      timeTakenMillis,
      responseBody)
  }

  private def logUnhandledExceptionResponse(
      error: Throwable,
      amazonRequestId: String,
      serial: Int,
      startTime: Long,
      endTime: Long
  )(implicit traceId: TraceId, instanceTraceId: InstanceTraceId): Unit = {
    val timeTakenMillis = endTime - startTime
    val simonTimestamp = currentSimonTime()

    log.alert(
      error,
      unhandledExceptionMessage,
      simonTimestamp,
      serial,
      amazonRequestId,
      timeTakenMillis)
  }
}

object UsersNetworksEventHandler {
  val processingStartMessage = "Processing Mongo trigger Event"
  val processingEndMessage = "Processed Mongo trigger Event"
  val unhandledExceptionMessage = "Unhandled exception during Mongo trigger Event processing"
  val eventInfoMessage = "Event info"
}