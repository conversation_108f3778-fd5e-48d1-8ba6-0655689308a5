package com.simonmarkets.users

import akka.actor.{ActorRef, ActorSystem}
import com.simonmarkets.api.users.cache.dynamodb.DynamoDbUserAclRepository
import com.simonmarkets.logging.{InstanceTraceId, TraceId}
import com.simonmarkets.logging.akkahttp.AkkaLoggingContextActor
import com.simonmarkets.mongodb.trigger.event.{MfaOperation, MongoEvent}
import com.simonmarkets.networks.common.trigger.NetworkPayload
import com.simonmarkets.users.common.trigger.UserPayload
import com.simonmarkets.users.config.{AclSyncApplicationConfig, MongoConfiguration}
import com.simonmarkets.users.di.MongoRepositories
import com.simonmarkets.users.service.AclSyncService
import com.simonmarkets.utils.config.Resolvers.SecretsConfigOps
import com.typesafe.config.{Config, ConfigFactory}
import pureconfig._
import pureconfig.generic.auto._

import scala.concurrent.{ExecutionContext, Future}

class AclSyncEventHandler extends UsersNetworksEventHandler[String] {
  private implicit val traceId: TraceId = TraceId("acl-sync-handler-init")
  private implicit val instanceTraceId: InstanceTraceId = InstanceTraceId.Empty

  log.info("Starting actor system")
  implicit lazy private val system: ActorSystem = ActorSystem("acl-sync")

  val loggingContextActor: ActorRef = system.actorOf(AkkaLoggingContextActor.props())

  log.info("Loading config file")
  lazy val rawConfig: Config = ConfigFactory.load().resolveSecrets()

  lazy val config: AclSyncApplicationConfig = loadConfigOrThrow[AclSyncApplicationConfig](rawConfig)

  val aclRepo = new DynamoDbUserAclRepository(config.dynamoDbConfig)

  lazy val repositories = new MongoRepositories(config.mongoDB)
  val service = new AclSyncService(aclRepo, repositories.userRepository, repositories.networkRepository, config.aclSyncConfig)

  override protected lazy val dbConfig: MongoConfiguration = config.mongoDB
  override protected implicit lazy val executionContext: ExecutionContext = system.dispatcher
  override protected lazy val logResponseBody: Boolean = false

  override protected def handleUser(userPayload: MongoEvent[UserPayload])(implicit traceId: TraceId): Future[String] = {
    service.syncUser(userPayload)
  }

  override protected def handleNetwork(networkPayload: MongoEvent[NetworkPayload])
    (implicit traceId: TraceId): Future[String] = {
    service.syncNetwork(networkPayload)
  }

  override protected def handleSimpleUser(userId: String)
    (implicit traceId: TraceId): Future[String] = service.syncUser(userId)

  override protected def handleNetworkMfa(userId: String, groupId: String, operation: MfaOperation)
    (implicit traceId: TraceId): Future[String] = {
    log.info("Got handleNetworkMfa event in AclSyncEventHandler. Doing nothing")
    Future.successful("")
  }
}
