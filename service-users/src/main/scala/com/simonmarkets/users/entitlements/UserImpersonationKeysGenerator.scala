package com.simonmarkets.users.entitlements

import com.simonmarkets.capabilities.Capabilities.{Admin, ReadOnlyAdmin}
import com.simonmarkets.capabilities.UserImpersonationCapabilities.{ApproveUserImpersonationViaApproverUserId, ViewUserImpersonationViaImpersonatorUserId, ViewUserImpersonationViaNetworkType}
import com.simonmarkets.entitlements.{AcceptedAccessKeysGenerator, AcceptedKeyBuilder}
import com.simonmarkets.users.domain.UserImpersonation

object UserImpersonationKeysGenerator extends AcceptedAccessKeysGenerator[UserImpersonation] {
  override def capabilityToAcceptedKeyBuilders: Map[String, AcceptedKeyBuilder[UserImpersonation]] = Map(
    Admin -> AcceptedKeyBuilder(buildAdminKeys),
    ReadOnlyAdmin -> AcceptedKeyBuilder(buildAdminKeys),
    ViewUserImpersonationViaNetworkType -> AcceptedKeyBuilder(buildAdminNetworkTypeKey),
    ViewUserImpersonationViaImpersonatorUserId -> AcceptedKeyBuilder(buildImpersonatorUserImpersonationKeys),
    ApproveUserImpersonationViaApproverUserId -> AcceptedKeyBuilder(buildApproverUserImpersonationKeys),
  )

  private def buildImpersonatorUserImpersonationKeys(capability: String, userImpersonation: UserImpersonation): Set[String] = {
    Set(s"$capability:${userImpersonation.impersonatorUserId}")
  }

  private def buildApproverUserImpersonationKeys(capability: String, userImpersonation: UserImpersonation): Set[String] = {
    // The impersonator can not approve their own UserImpersonation
    userImpersonation.approversUserIds.getOrElse(Set.empty)
      .filterNot(_ == userImpersonation.impersonatorUserId)
      .map(approverUserId => s"$capability:$approverUserId")
  }

}
