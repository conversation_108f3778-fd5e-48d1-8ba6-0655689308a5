package com.simonmarkets.users

import akka.actor.ActorSystem
import akka.http.scaladsl.Http
import com.simonmarkets.eventbridge.client.{EventBridgeClient, EventBridgeImpl}
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.logging.TraceId
import com.simonmarkets.mongodb.trigger.event.{MfaOperation, MongoEvent}
import com.simonmarkets.networks.common.ExternalIdType
import com.simonmarkets.networks.common.trigger.NetworkPayload
import com.simonmarkets.okta.client.api.HttpOktaClient
import com.simonmarkets.okta.client.sdk.OktaClientFactory
import com.simonmarkets.okta.service.{OktaRepository, OktaService}
import com.simonmarkets.users.common.trigger.UserPayload
import com.simonmarkets.users.config.{MongoConfiguration, OktaSyncApplicationConfig}
import com.simonmarkets.users.di.MongoRepositories
import com.simonmarkets.users.service.OktaSyncService
import com.simonmarkets.users.service.OktaSyncService.UserUpdated
import com.simonmarkets.utils.config.Resolvers.SecretsConfigOps
import com.typesafe.config.{Config, ConfigFactory}
import io.simon.encryption.v3.service.IcnEncryptionService
import pureconfig.loadConfigOrThrow
import scalacache.CacheConfig
import scalacache.caffeine._
import pureconfig.loadConfigOrThrow
import pureconfig.generic.auto._

import scala.concurrent.{ExecutionContext, Future}

class OktaSyncEventHandler extends UsersNetworksEventHandler[Option[UserUpdated]] {
  implicit val traceId: TraceId = TraceId("okta-sync-event-handler-init")

  implicit lazy private val system: ActorSystem = ActorSystem("OktaSyncActorSystem")

  lazy val rawConfig: Config = ConfigFactory.load().resolveSecrets()
  lazy val config: OktaSyncApplicationConfig = loadConfigOrThrow[OktaSyncApplicationConfig](rawConfig)
  lazy val repositories = new MongoRepositories(config.mongoDB)

  val futureHttpClient: FutureHttpClient = new FutureHttpClient(Http(), config.httpClientConfig)
  val encryptionClient = new IcnEncryptionService(futureHttpClient, config.encryption)
  lazy val oktaService: OktaService = OktaService(
    config = config.oktaConfig.serviceConfig,
    repository = OktaRepository(
      client = OktaClientFactory.getOktaClient(config.oktaConfig.clientConfig),
      httpClient = HttpOktaClient(config.oktaConfig.clientConfig)
    )
  )

  implicit val customisedCaffeineCache: CaffeineCache[Set[ExternalIdType]] =
    CaffeineCache(CacheConfig.defaultCacheConfig)

  val oktaSyncBus: EventBridgeClient = EventBridgeImpl(config.networkMfaSyncEventBus)

  lazy val oktaSyncService = new OktaSyncService.Impl(
    config.oktaSyncConfig,
    oktaService,
    repositories.userRepository,
    repositories.networkRepository,
    repositories.externalIdTypeRepository,
    oktaSyncBus,
    encryptionClient,
    repositories.transientStateRepository
  )

  override protected val dbConfig: MongoConfiguration = config.mongoDB
  override protected implicit lazy val executionContext: ExecutionContext = system.dispatcher

  override protected def logResponseBody: Boolean = false

  override protected def handleUser(userPayload: MongoEvent[UserPayload])
    (implicit traceId: TraceId): Future[Option[UserUpdated]] = oktaSyncService.syncToOkta(userPayload)

  override protected def handleNetwork(networkPayload: MongoEvent[NetworkPayload])
    (implicit traceId: TraceId): Future[Option[UserUpdated]] = oktaSyncService.handleNetworkEvent(networkPayload)

  override protected def handleSimpleUser(userId: String)
    (implicit traceId: TraceId): Future[Option[UserUpdated]] = oktaSyncService.syncUserToOkta(userId)

  override protected def handleNetworkMfa(userId: String, groupId: String, operation: MfaOperation)
    (implicit traceId: TraceId): Future[Option[UserUpdated]] =
    oktaSyncService.syncUserToOktaForNetworkMfa(userId, groupId, operation)

}
