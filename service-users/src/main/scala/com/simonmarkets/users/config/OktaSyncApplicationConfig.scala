package com.simonmarkets.users.config

import com.simonmarkets.eventbridge.config.EventBridgeConfig
import com.simonmarkets.http.HttpClientConfig
import com.simonmarkets.okta.config.OktaConfiguration
import io.simon.encryption.v3.service.IcnEncryptionService.IcnEncryptionConfig

import scala.concurrent.duration.Duration

case class OktaSyncApplicationConfig(
    oktaConfig: OktaConfiguration,
    mongoDB: MongoConfiguration,
    oktaSyncConfig: OktaSyncConfig,
    networkMfaSyncEventBus: EventBridgeConfig,
    httpClientConfig: HttpClientConfig,
    encryption: IcnEncryptionConfig
)

case class OktaSyncConfig(
    serviceEntitlements: Set[String],
    cacheTtl: Option[Duration],
    multiFactorConfig: OktaMultiFactorConfig
)

case class OktaMultiFactorConfig(
    smsGroupId: String,
    totpGroupId: String,
    emailGroupId: String,
    securityQuestionGroupId: String,
    googleAuthGroupId: String,
    voiceCallGroupId: String,
    oktaVerifyGroupId: String,
    smsVoiceCallGroupId: String,
    enabled: Boolean
)