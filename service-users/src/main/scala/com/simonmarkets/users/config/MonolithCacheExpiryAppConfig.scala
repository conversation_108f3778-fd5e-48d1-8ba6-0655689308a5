package com.simonmarkets.users.config

import com.simonmarkets.http.HttpClientConfig
import com.typesafe.config.Config
import pureconfig.generic.auto._

case class MonolithCacheExpiryAppConfig(
    httpClientConfig: HttpClientConfig,
    simonBasePath: String,
    mongoDB: MongoConfiguration
)

object MonolithCacheExpiryAppConfig {
  def apply(config: Config): MonolithCacheExpiryAppConfig = {
    pureconfig.loadConfigOrThrow[MonolithCacheExpiryAppConfig](config)
  }
}