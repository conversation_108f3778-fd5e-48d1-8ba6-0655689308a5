package com.simonmarkets.users.util

import com.okta.sdk.resource.model.UserProfile
import com.simonmarkets.users.domain.OktaDecodeError
import simon.Id.NetworkId

import java.{lang => jl, util => ju}

import scala.collection.JavaConverters._
import scala.util.{Failure, Success, Try}

trait OktaProfileExtractors {

  import OktaProfileExtractors._

  implicit final val intDecoder: FieldExtractor[Int] =
    field => profile => profile.int(field)

  implicit final val intOptDecoder: FieldExtractor[Option[Int]] =
    field => profile => profile.intOpt(field)

  implicit val intSetOptDecoder: FieldExtractor[Option[Set[Int]]] =
    field => profile => profile.intSetOpt(field)

  implicit val stringOptDecoder: FieldExtractor[Option[String]] =
    field => profile => profile.stringOpt(field)

  implicit val networkIdOptDecoder: FieldExtractor[Option[NetworkId]] =
    field => profile => profile.stringOpt(field).map(_.map(NetworkId.apply))

  implicit val stringSetDecoder: FieldExtractor[Set[String]] =
    field => profile => profile.stringSetOpt(field).map(_.getOrElse(Set.empty))

  // remove any empty strings -- return None if set is empty after any removals
  implicit val stringSetOptDecoder: FieldExtractor[Option[Set[String]]] =
    field =>
      profile =>
        profile
          .stringSetOpt(field)
          .map {
            case None => None
            case Some(set) =>
              val ret = set.filterNot(_.isBlank)
              if (ret.isEmpty)
                None
              else
                Some(ret)
          }

  implicit val booleanDecoder: FieldExtractor[Boolean] =
    field => profile => profile.bool(field)

  implicit val booleanOptDecoder: FieldExtractor[Option[Boolean]] =
    field => profile => profile.boolOpt(field)

}

object OktaProfileExtractors extends OktaProfileExtractors {

  final type FieldExtractor[A] = String => UserProfile => Either[OktaDecodeError, A]

  /**
   * Converts a string like "[a, b, c]" into Set("a", "b", "c")
   */
  def parseStringList(s: String): Either[OktaDecodeError, Set[String]] =
    "\\[(.*?)]".r.findFirstMatchIn(s) match {
      case Some(m) => Right(m.group(1).split(",\\s*").toSet)
      case None    => Left(OktaDecodeError("could not parse as set"))
    }

  def tryToEither[T](key: String)(t: Try[T]): Either[OktaDecodeError, T] = t match {
    case Success(value) => Right(value)
    case Failure(err)   => Left(OktaDecodeError(key, err))
  }

  final implicit class UserProfileOps(private val profile: UserProfile) extends AnyVal {

    /**
     * Reads the value mapped to the specified key as a boolean. Returns an error if the value is blank, null, or cannot
     * be parsed as a boolean.
     */
    def bool(key: String): Either[OktaDecodeError, Boolean] =
      boolOpt(key).flatMap(_.toRight(OktaDecodeError(key, "expected boolean")))

    /**
     * Reads the value mapped to the specified key as a boolean. If the value is not an [[java.lang.Integer]] and is
     * blank, returns Right(None). If the value cannot be parsed as a boolean or is null, returns an error.
     */
    def boolOpt(key: String): Either[OktaDecodeError, Option[Boolean]] =
      profile.getAdditionalProperties.get(key) match {
        case o if o != null =>
          o match {
            case b: jl.Boolean => Right(Some(b.booleanValue))
            case s: String =>
              if (s.isBlank)
                Right(None)
              else
                tryToEither(key)(Try(s.toBoolean)).map(Some.apply)
            case o =>
              o.toString match {
                case s if s.isBlank => Right(None)
                case s              => tryToEither(key)(Try(s.toBoolean)).map(Some.apply)
              }
          }
        case _ => Right(None)
      }

    /**
     * Reads the value mapped to the specified key as an int. Returns an error if the value is blank, null, or cannot be
     * parsed as an int.
     */
    def int(key: String): Either[OktaDecodeError, Int] =
      intOpt(key).flatMap(_.toRight(OktaDecodeError(key, "expected int")))

    /**
     * Reads the value mapped to the specified key as an int. If the value is not an [[java.lang.Integer]] and is blank,
     * returns Right(None). If the value cannot be parsed as an int, returns an error.
     */
    def intOpt(key: String): Either[OktaDecodeError, Option[Int]] =
      profile.getAdditionalProperties.get(key) match {
        case o if o != null =>
          o match {
            case i: jl.Integer => Right(Some(i.intValue))
            case s: String =>
              if (s.isBlank)
                Right(None)
              else
                tryToEither(key)(Try(s.toInt)).map(Some.apply)
            case o =>
              o.toString match {
                case s if s.isBlank => Right(None)
                case s              => tryToEither(key)(Try(s.toInt)).map(Some.apply)
              }
          }
        case _ => Right(None)
      }

    /**
     * Returns a set of ints associated with `key`, if found. Returns an error
     * if any of the values in the list cannot be converted to an int or no associated value found.
     */
    def intSet(key: String): Either[OktaDecodeError, Set[Int]] =
      intSetOpt(key).flatMap(_.toRight(OktaDecodeError(key, "expected int set")))

    /**
     * Returns an optional set of ints associated with `key`, if found. Otherwise, returns Right(None). Returns an error
     * if any of the values in the list cannot be converted to an int.
     */
    def intSetOpt(key: String): Either[OktaDecodeError, Option[Set[Int]]] =
      stringSetOpt(key).flatMap {
        case Some(set) =>
          set
            .map(s => tryToEither(key)(Try(s.toInt)))
            .foldLeft[Either[OktaDecodeError, Set[Int]]](Right(Set.empty)) { (acc, s) =>
              for {
                is <- acc.right
                i <- s.right
              } yield is + i
            }
            .map(Some.apply)
        case None => Right(None)
      }

    def properties: Map[String, AnyRef] = profile.getAdditionalProperties.asScala.toMap

    /**
     * Returns the string associated with `key`, if found. Otherwise, returns an error
     */
    def string(key: String): Either[OktaDecodeError, String] =
      stringOpt(key).flatMap(_.toRight(OktaDecodeError(key, "expected string")))

    /**
     * Returns the non-blank string associated with `key`, if found. Otherwise, returns an error
     */
    def stringNonEmpty(key: String): Either[OktaDecodeError, String] =
      stringNonEmptyOpt(key).flatMap(_.toRight(OktaDecodeError(key, "expected non-blank string")))

    /**
     * Returns the non-blank string associated with `key`, if found. Otherwise, returns Right(None)
     */
    def stringNonEmptyOpt(key: String): Either[OktaDecodeError, Option[String]] =
      stringOpt(key).map(_.filter(!_.isBlank))

    /**
     * Returns the string associated with `key`, if found. Otherwise returns Right(None)
     */
    def stringOpt(key: String): Either[OktaDecodeError, Option[String]] =
      profile.getAdditionalProperties.get(key) match {
        case s if s != null => Right(Some(s.toString))
        case _              => Right(None)
      }

    /**
     * Returns an optional set of strings associated with `key`. Otherwise, returns an error
     */
    def stringSet(key: String): Either[OktaDecodeError, Set[String]] =
      stringSetOpt(key).flatMap(_.toRight(OktaDecodeError(key, "expected string set")))

    /**
     * Splits a string by commas into a set. Returns an error if the string mapped to `key` is blank or null
     */
    def stringSetCommaSeparated(key: String): Either[OktaDecodeError, Set[String]] =
      stringSetCommaSeparatedOpt(key).flatMap(_.toRight(OktaDecodeError(key, "expected comma separated string")))

    /**
     * Splits a string by commas, if present
     */
    def stringSetCommaSeparatedOpt(key: String): Either[OktaDecodeError, Option[Set[String]]] =
      stringOpt(key).map(_.map(_.split(",").filter(_.nonEmpty).toSet))

    /**
     * Returns an optional set associated with `key`. Turns empty sets into an error
     */
    def stringSetNonEmpty(key: String): Either[OktaDecodeError, Set[String]] =
      stringSetOpt(key).flatMap {
        case Some(set) if set.nonEmpty => Right(set)
        case _                         => Left(OktaDecodeError(key, "expected non-empty set"))
      }

    /**
     * Returns an optional set associated with `key`. Turns empty sets into a Right(None)
     */
    def stringSetNonEmptyOpt(key: String): Either[OktaDecodeError, Option[Set[String]]] =
      stringSetOpt(key).map(_.filter(_.nonEmpty))

    /**
     * Returns an optional set of strings associated with `key`, if found. Otherwise, returns Right(None)
     */
    def stringSetOpt(key: String): Either[OktaDecodeError, Option[Set[String]]] =
      profile.getAdditionalProperties.get(key) match {
        case o if o != null =>
          o match {
            case list: ju.Collection[_] => Right(Some(list.asScala.map(_.toString).toSet))
            case s: String =>
              if (s.isBlank)
                Right(None)
              else
                parseStringList(s).map(Some.apply)

            case o =>
              o.toString match {
                case s if s.isBlank => Right(None)
                case s              => parseStringList(s).map(Some.apply)
              }
          }
        case _ => Right(None)
      }

  }

}
