package com.simonmarkets.users

import akka.actor.ActorSystem
import akka.http.scaladsl.Http
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.logging.{InstanceTraceId, TraceId}
import com.simonmarkets.mongodb.trigger.event.{MfaOperation, MongoEvent}
import com.simonmarkets.mongodb.trigger.event.OperationType._
import com.simonmarkets.networks.HttpNetworksClient
import com.simonmarkets.networks.common.trigger.NetworkPayload
import com.simonmarkets.users.common.trigger.UserPayload
import com.simonmarkets.users.config.{MongoConfiguration, MonolithCacheExpiryAppConfig}
import com.simonmarkets.utils.config.Resolvers.SecretsConfigOps
import com.typesafe.config.ConfigFactory

import scala.concurrent.{ExecutionContext, Future}

class MonolithCacheExpiryApp extends UsersNetworksEventHandler[Map[String, String]] {

  private implicit val traceId: TraceId = TraceId("monolith-cache-expiry-init")
  private implicit val instanceTraceId: InstanceTraceId = InstanceTraceId.Empty

  log.info("Starting actor system")
  implicit lazy private val system: ActorSystem = ActorSystem("monolith-cache-expiry")
  log.info("Setting up execution context")

  log.info("Loading config file")
  private val rawConfig = ConfigFactory.load().resolveSecrets()

  private val config = MonolithCacheExpiryAppConfig(rawConfig)

  private val futureHttpClient = new FutureHttpClient(Http(), config.httpClientConfig)
  private val userClient = new HttpUsersClient(futureHttpClient, config.simonBasePath)
  private val networkClient = new HttpNetworksClient(futureHttpClient, config.simonBasePath)

  override protected val dbConfig: MongoConfiguration = config.mongoDB

  override protected implicit lazy val executionContext: ExecutionContext = system.dispatcher

  override protected def logResponseBody: Boolean = false

  override protected def handleUser(userPayload: MongoEvent[UserPayload])
    (implicit traceId: TraceId): Future[Map[String, String]] = {
    userPayload.detail.operationType match {
      case INSERT | UPDATE | REPLACE =>
        val user = userPayload.detail.fullDocument.get
        handleSimpleUser(user.id)
      case _ => Future.failed(new Exception(s"Got invalid operationType: ${userPayload.detail.operationType}"))
    }
  }

  override protected def handleNetwork(networkPayload: MongoEvent[NetworkPayload])
    (implicit traceId: TraceId): Future[Map[String, String]] = {
    networkPayload.detail.operationType match {
      case INSERT | UPDATE | REPLACE =>
        val network = networkPayload.detail.fullDocument.get
        networkClient.expire(network.id)
      case _ => Future.failed(new Exception(s"Got invalid operationType: ${networkPayload.detail.operationType}"))
    }
  }

  override protected def handleSimpleUser(userId: String)
    (implicit traceId: TraceId): Future[Map[String, String]] = userClient.expire(userId)

  override protected def handleNetworkMfa(userId: String, groupId: String, operationType: MfaOperation)
    (implicit traceId: TraceId): Future[Map[String, String]] = {
    log.info("Got handleNetworkMfa event in MonolithCacheExpiryApp. Doing nothing")
    Future.successful(Map.empty)
  }
}
