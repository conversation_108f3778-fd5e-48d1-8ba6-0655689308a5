package com.simonmarkets.users.service

import akka.stream.Materializer
import akka.stream.scaladsl.{Sink, Source}
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.api.users.cache.UserAclRepository
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.mongodb.trigger.event.MongoEvent
import com.simonmarkets.mongodb.trigger.event.OperationType._
import com.simonmarkets.networks.common.repository.NetworkRepository
import com.simonmarkets.networks.common.trigger.NetworkPayload
import com.simonmarkets.users.common.trigger.UserPayload
import com.simonmarkets.users.config.AclSyncConfig
import com.simonmarkets.users.repository.UserRepository
import com.simonmarkets.users.repository.filters.NetworkFilter

import scala.concurrent.{ExecutionContext, Future}

class AclSyncService(
    aclRepo: UserAclRepository,
    userRepo: UserRepository,
    networkRepo: NetworkRepository,
    config: AclSyncConfig
)(implicit ec: ExecutionContext, mat: Materializer) extends TraceLogging {

  def syncUser(userEvent: MongoEvent[UserPayload])(implicit traceId: TraceId): Future[String] = {

    userEvent.detail.operationType match {
      case INSERT | UPDATE | REPLACE =>
        val userPayload: UserPayload = userEvent.detail.fullDocument.get
        syncUser(userPayload.id)

      case _ => Future.failed(new Exception(s"Got unhandled operationType:${userEvent.detail.operationType}"))
    }
  }

  def syncUser(userId: String)(implicit traceId: TraceId): Future[String] = {

    log.debug(s"Syncing user to useracl repo", "userId" -> userId)

    userRepo.getById(userId)(config.serviceEntitlements).flatMap {
      case Some(user) =>
        networkRepo.getById(user.networkId)(config.serviceEntitlements).flatMap {
          case Some(network) => aclRepo.put(UserACL(user.asLegacyUser, network.asLegacyNetwork)).map(_ => userId)
          case None => Future.failed(new Exception(s"Couldn't find network with networkId=${user.networkId}"))
        }

      case None =>
        Future.failed(new Exception(s"Couldn't find user with userId=${userId}"))
    }
  }

  def syncNetwork(networkEvent: MongoEvent[NetworkPayload])(implicit traceId: TraceId): Future[String] = {
    networkEvent.detail.operationType match {
      case INSERT | UPDATE | REPLACE =>
        val networkPayload = networkEvent.detail.fullDocument.get
        networkRepo.getById(networkPayload.id)(config.serviceEntitlements).flatMap {
          case Some(network) =>
            log.debug(s"Syncing users to useracl repo", "networkName" -> network.name)

            Source
              .fromPublisher {
                userRepo.getUsersStream(Set(NetworkFilter(networkPayload.id.toString)))(config.serviceEntitlements)
              }
              .grouped(25)
              .mapAsync(1) { users =>
                aclRepo.putAll(users.map(user => UserACL(user.asLegacyUser, network.asLegacyNetwork)).toList)
              }
              .runWith(Sink.ignore)
              .map(_ => networkPayload.id.toString)

          case None => Future.failed(new Exception(s"Couldn't find network with networkId=${networkPayload.id}"))
        }
      case _ => Future.failed(new Exception(s"Got unhandled operationType:${networkEvent.detail.operationType}"))
    }
  }
}
