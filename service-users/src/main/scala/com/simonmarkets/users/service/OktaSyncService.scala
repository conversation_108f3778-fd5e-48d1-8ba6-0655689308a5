package com.simonmarkets.users.service

import akka.actor.ActorSystem
import com.amazonaws.services.eventbridge.model.PutEventsResult
import com.goldmansachs.marquee.pipg.{UserACL, UserRole}
import com.okta.sdk.resource.client.ApiException
import com.okta.sdk.resource.model.{User => OktaSdkUser}
import com.simonmarkets.capabilities.MultiFactorAuthenticationCapabilities
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.eventbridge.client.EventBridgeClient
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.mongodb.trigger.event.{MfaOperation, MongoEvent, NetworkMfaSyncEventDetail}
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.common.repository.{ExternalIdTypeRepository, NetworkRepository}
import com.simonmarkets.networks.common.trigger.NetworkPayload
import com.simonmarkets.networks.common.{ExternalIdType, Network}
import com.simonmarkets.okta.domain.{Factor, OktaUser}
import com.simonmarkets.okta.service.OktaService
import com.simonmarkets.okta.{BcryptHash, notFound, rateLimited}
import com.simonmarkets.resteasy.utils.FutureOps._
import com.simonmarkets.users.common.api.response.OktaSyncResponse
import com.simonmarkets.users.common.okta.OktaUserConversions
import com.simonmarkets.users.common.trigger.UserPayload
import com.simonmarkets.users.common.{EventInfo, LoginMode, User, UserDomainEvent}
import com.simonmarkets.users.config.OktaSyncConfig
import com.simonmarkets.users.domain.UserSnapshotEntityView
import com.simonmarkets.users.repository.filters.NetworkFilter
import com.simonmarkets.users.repository.{TransientStateRepository, UserRepository}
import com.simonmarkets.users.service.OktaSyncService.UserUpdated
import io.simon.encryption.v2.model.EncryptedData
import io.simon.encryption.v3.conversion.DefaultConverters
import io.simon.encryption.v3.model.ClientId
import io.simon.encryption.v3.service.EncryptionService
import scalacache._
import scalacache.caffeine._
import scalacache.modes.scalaFuture.mode
import simon.Id.NetworkId

import java.nio.charset.StandardCharsets
import java.util.Base64
import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

trait OktaSyncService {
  /**
   * Sync user to Okta on user change.
   *
   * @return UserUpdated if user was updated None if error was caught
   */
  def syncToOkta(event: MongoEvent[UserPayload])(implicit traceId: TraceId): Future[Option[UserUpdated]]

  /**
   * Sync user to Okta on network change.
   *
   * @return UserUpdated if user was updated None if error was caught
   */
  def handleNetworkEvent(event: MongoEvent[NetworkPayload])(implicit traceId: TraceId): Future[Option[UserUpdated]]

  def syncUserToOkta(userId: String)(implicit traceId: TraceId): Future[Option[UserUpdated]]

  def putNetworkMfaSyncEvents(networkId: NetworkId)(implicit traceId: TraceId): Future[OktaSyncResponse]

  def syncUserToOktaForNetworkMfa(simonId: String, groupId: String, operation: MfaOperation)
    (implicit traceId: TraceId): Future[Option[UserUpdated]]

}

object OktaSyncService {

  val OktaSyncServiceUser = "oktaSyncServiceUser"
  val RetryWaitTime = 30.seconds

  class Impl(
      oktaSyncConfig: OktaSyncConfig,
      oktaService: OktaService,
      usersRepository: UserRepository,
      networksRepository: NetworkRepository,
      externalIdTypeRepository: ExternalIdTypeRepository,
      oktaSyncBus: EventBridgeClient,
      encryptionClient: EncryptionService,
      transientStateRepository: TransientStateRepository
  )(implicit ec: ExecutionContext, ac: ActorSystem, externalIdTypeCache: CaffeineCache[Set[ExternalIdType]])
    extends OktaSyncService with TraceLogging with JsonCodecs with DefaultConverters {

    val capabilitiesToGroupMap = Map(
      MultiFactorAuthenticationCapabilities.EnrollMFASMS.name -> oktaSyncConfig.multiFactorConfig.smsGroupId,
      MultiFactorAuthenticationCapabilities.EnrollMFAGoogleAuth.name -> oktaSyncConfig.multiFactorConfig.googleAuthGroupId,
      MultiFactorAuthenticationCapabilities.EnrollMFAVoiceCall.name -> oktaSyncConfig.multiFactorConfig.voiceCallGroupId,
      MultiFactorAuthenticationCapabilities.EnrollMFAEmail.name -> oktaSyncConfig.multiFactorConfig.emailGroupId,
      MultiFactorAuthenticationCapabilities.EnrollMFAOktaVerify.name -> oktaSyncConfig.multiFactorConfig.oktaVerifyGroupId,
      MultiFactorAuthenticationCapabilities.EnrollMFASecurityQuestion.name -> oktaSyncConfig.multiFactorConfig.securityQuestionGroupId,
      MultiFactorAuthenticationCapabilities.EnrollMFATOTP.name -> oktaSyncConfig.multiFactorConfig.totpGroupId,
      MultiFactorAuthenticationCapabilities.EnrollMFASMSAndVoiceCall.name -> oktaSyncConfig.multiFactorConfig.smsVoiceCallGroupId
    )

    override def syncToOkta(event: MongoEvent[UserPayload])(implicit traceId: TraceId): Future[Option[UserUpdated]] = {
      val userIdOpt = event.detail.fullDocument.map(_.id)
      log.info("Attempting user sync to okta", userIdOpt)
      for {
        userId <- Future.successful(userIdOpt).flattenOption("fullDocument is empty, cannot retrieve user id from event")
        userUpdated <- syncUserToOkta(userId)
      } yield userUpdated
    }

    override def handleNetworkEvent(event: MongoEvent[NetworkPayload])
      (implicit traceId: TraceId): Future[Option[UserUpdated]] = {
      if (oktaSyncConfig.multiFactorConfig.enabled) {
        val networkIdOpt = event.detail.fullDocument.map(_.id)
        log.info("Attempting user mfa sync to okta on network event", networkIdOpt)
        for {
          networkId <- Future.successful(networkIdOpt).flattenOption("fullDocument is empty, cannot retrieve network id from event")
          _ <- putNetworkMfaSyncEvents(networkId)
        } yield Some(UserUpdated("", "", Some("")))
      } else {
        Future.successful(Some(UserUpdated("", "", Some(""))))
      }
    }

    /** Update users in Okta MFA groups on user event
     *
     * Get prior user from user snapshots by user_id,
     *
     * Get current user from users by user_id
     *
     * Construct userACL and oldUserACL
     *
     * Compare them to decide which Okta MFA group the user should be added to or removed from
     */

    override def syncUserToOkta(userId: String)(implicit traceId: TraceId): Future[Option[UserUpdated]] = {
      val syncFut = for {
        user <- usersRepository
          .getById(userId)(oktaSyncConfig.serviceEntitlements)
          .flattenOption(s"User $userId not found")
        network <- networksRepository
          .getById(user.networkId)(oktaSyncConfig.serviceEntitlements)
          .flattenOption(s"Network ${user.networkId} not found")
        priorUserSnapshot <-
          if (oktaSyncConfig.multiFactorConfig.enabled)
            usersRepository.getPriorSnapshotByEntityId(userId).map(_.map(_.entity))
          else Future.successful(None)
        externalIdTypes <- cachedExternalIdTypes(network.id)
        icnPasswordOpt <- getPasswordOpt(
          userId = user.id,
          loginMode = user.loginMode,
          eventType = user.eventInfo.eventType
        )
        oktaUser = OktaUserConversions.toOktaUser(
          user,
          network.customRolesConfig,
          ExternalId.toStringMap(defaultedExternalIds(user.externalIds, externalIdTypes)),
          password = icnPasswordOpt
        )
        upsertedOktaUser <- oktaService.upsertUser(oktaUser, includeDeactivated = true)
        isOktaStateTransitioned <- setUserActiveState(upsertedOktaUser, user.eventInfo.eventType)
        decryptedMfas <- decryptMfas(user).recover {
          case ex =>
            log.error(ex, "could not decrypt mfas")
            Map.empty[String, Factor]
        }
        enrolledMfas <- oktaService.enrollFactors(user.id, upsertedOktaUser, decryptedMfas)
        userWithUpdatedMfas = updateEnrolledMfas(user, enrolledMfas)
        finalUser <- internalUserUpdate(
          internalUser = userWithUpdatedMfas,
          idpId = upsertedOktaUser.getId,
          isOktaStateTransitioned = isOktaStateTransitioned,
          isPasswordSynced = icnPasswordOpt.isDefined,
          isMfaUpdated = user != userWithUpdatedMfas
        )
        userPriorNetwork <- if (oktaSyncConfig.multiFactorConfig.enabled) {
          priorUserSnapshot match {
            case Some(priorUser) => if (priorUser.networkId != finalUser.networkId) {
              log.info("User changed network", "priorNetworkId" -> priorUser.networkId, "currentNetworkId" -> user.networkId)
              networksRepository.getById(priorUser.networkId)(oktaSyncConfig.serviceEntitlements)
            } else Future.successful(Some(network))
            case _ => Future.successful(None)
          }
        } else Future.successful(None)
        _ <- if (oktaSyncConfig.multiFactorConfig.enabled) updateUserInMFAGroup(network, userWithUpdatedMfas, upsertedOktaUser.getId, priorUserSnapshot, userPriorNetwork) else Future.unit
      } yield {
        log.info("Okta sync complete")

        UserUpdated(
          oktaId = upsertedOktaUser.getId,
          oktaLoginId = upsertedOktaUser.getProfile.getLogin,
          simonId = oktaService.getSimonId(upsertedOktaUser)
        )
      }

      def fallback = syncUserToOkta(userId)

      runWithRetry(syncFut, userId)(fallback)
    }

    private def internalUserUpdate(
        internalUser: User,
        idpId: String,
        isOktaStateTransitioned: Boolean,
        isPasswordSynced: Boolean,
        isMfaUpdated: Boolean
    )(implicit traceId: TraceId): Future[User] = {

      val mfaUpdate: User => User = { u =>
        if (isMfaUpdated)
          u.copy(eventInfo = EventInfo(UserDomainEvent.UserUpdated, OktaSyncServiceUser, traceId))
        else u
      }

      val stateUpdate: User => User = { u =>
        if (isOktaStateTransitioned)
          u.copy(eventInfo = EventInfo(UserDomainEvent.UserStateTransitioned, OktaSyncServiceUser, traceId))
        else u
      }

      val idpUpdate: User => User = { u =>
        if (u.idpId.isEmpty || !u.idpId.contains(idpId)) {
          log.info("Adding idpId to user", u.id, idpId)
          u.copy(
            idpId = Some(idpId),
            eventInfo = EventInfo(UserDomainEvent.UserIdpIdUpdated, OktaSyncServiceUser, traceId)
          )
        }
        else u
      }

      val loginModeUpdate: User => User = { u =>
        if (isPasswordSynced) {
          log.info("Updating loginMode to UnifiedPassword", "userId" -> u.id)
          u.copy(
            loginMode = LoginMode.UnifiedPassword,
            eventInfo = EventInfo(UserDomainEvent.UserLoginModeUpdated, OktaSyncServiceUser, traceId)
          )
        }
        else u
      }

      //order matters as each update may step on a prior
      val potentialUpdates = Seq(mfaUpdate, stateUpdate, idpUpdate, loginModeUpdate)
      val updatedUser = Function.chain(potentialUpdates)(internalUser)

      if (updatedUser != internalUser) usersRepository.updateUser(OktaSyncServiceUser, updatedUser)
      else Future.successful(internalUser)
    }

    override def syncUserToOktaForNetworkMfa(simonId: String, groupId: String, operation: MfaOperation)
      (implicit traceId: TraceId): Future[Option[UserUpdated]] = {
      log.info("Sync user to okta for network mfa", simonId, groupId, operation)

      val syncFut = for {
        _ <- operation match {
          case MfaOperation.add => oktaService.addUserToGroup(groupId, simonId, None)
          case MfaOperation.remove => oktaService.removeUserFromGroup(groupId, simonId)
        }
      } yield UserUpdated("", "", Some(simonId))

      def fallback = syncUserToOktaForNetworkMfa(simonId, groupId, operation)

      runWithRetry(syncFut, simonId)(fallback)
    }

    private def runWithRetry[T](attempt: Future[T], userId: String)(fallback: => Future[Option[T]])
      (implicit traceId: TraceId): Future[Option[T]] = {
      attempt.transformWith {
        case Failure(exception) =>
          exception match {
            case e: ApiException if rateLimited(e) => {
              log.error(e, s"Okta rate limit exception received, retrying after $RetryWaitTime")
              akka.pattern.after(RetryWaitTime)(fallback)
            }

            case e: ApiException if notFound(e) => {
              log.error(e, s"Okta user not found", "simonId" -> userId)
              Future.successful(None)
            }

            case other =>
              log.error(other, s"Unhandled error")
              Future.failed(other)
          }
        case Success(value) => Future.successful(Some(value))
      }
    }

    private def getGroupsChanged(acl: UserACL, oldAcl: UserACL)(implicit trceId: TraceId) = {
      val mfaCapabilities = MultiFactorAuthenticationCapabilities.availableAccessKeyGen.getAvailableAccessKeys(acl)
      val oldMfaCapabilities = MultiFactorAuthenticationCapabilities.availableAccessKeyGen.getAvailableAccessKeys(oldAcl)
      val addedCapabilities = mfaCapabilities -- oldMfaCapabilities
      val removedCapabilities = oldMfaCapabilities -- mfaCapabilities
      val groupsToAdd = addedCapabilities.flatMap(capabilitiesToGroupMap.get)
      val groupsToRemove = removedCapabilities.flatMap(capabilitiesToGroupMap.get)

      if (groupsToAdd.nonEmpty || groupsToRemove.nonEmpty) {
        log.info("MFA changes", acl.userId, acl.networkId, oldAcl.networkId, addedCapabilities, removedCapabilities, groupsToAdd, groupsToRemove)
      }
      (groupsToAdd, groupsToRemove)
    }

    private def generateNetworkMfaSyncEventDetail(network: Network, user: User, oldNetwork: Option[Network],
        oktaUser: OktaUser)
      (implicit traceId: TraceId): Set[NetworkMfaSyncEventDetail] = {
      val acl = UserACL.apply(user.asLegacyUser, network.asLegacyNetwork)
      val oldAcl = UserACL.apply(user.asLegacyUser, oldNetwork.getOrElse(network.copy(customRolesConfig = Set.empty)).asLegacyNetwork)
      val (groupsToAdd, groupsToRemove) = getGroupsChanged(acl, oldAcl)
      val addToEvents = groupsToAdd
        .map(groupId => NetworkMfaSyncEventDetail(oktaUser.id, groupId, MfaOperation.add))
      val removeFromEvents = groupsToRemove
        .map(groupId => NetworkMfaSyncEventDetail(oktaUser.id, groupId, MfaOperation.remove))
      if (addToEvents.nonEmpty || removeFromEvents.nonEmpty) {
        log.info("generateNetworkMfaSyncEventDetail", groupsToAdd, groupsToRemove, addToEvents, removeFromEvents)
      }
      addToEvents ++ removeFromEvents
    }

    private def updateUserInMFAGroup(network: Network, user: User, userOktaId: String,
        oldUserSnap: Option[UserSnapshotEntityView], oldNetwork: Option[Network])
      (implicit traceId: TraceId): Future[Unit] = {
      val acl = UserACL.apply(user.asLegacyUser, network.asLegacyNetwork)
      val oldUser = oldUserSnap match {
        case Some(value) => user.copy(customRoles = value.customRoles, roles = value.roles.map(UserRole.apply))
        case None => user.copy(customRoles = Set.empty, roles = Set.empty)
      }
      val oldAcl = UserACL.apply(
        oldUser.asLegacyUser,
        oldNetwork.getOrElse(network.copy(customRolesConfig = Set.empty)).asLegacyNetwork)
      val (groupsToAdd, groupsToRemove) = getGroupsChanged(acl, oldAcl)
      if (groupsToAdd.nonEmpty || groupsToRemove.nonEmpty) {
        log.info("updateUserInMFAGroup", groupsToAdd, groupsToRemove)
      }
      val addFuts = groupsToAdd
        .map(groupId => oktaService.addUserToGroup(groupId, user.id, Some(userOktaId)))
      val removeFuts = groupsToRemove
        .map(groupId => oktaService.removeUserFromGroup(groupId, user.id))
      Future.sequence(addFuts ++ removeFuts).map(_ => ())
    }

    /** Update users in Okta MFA groups on network event
     *
     * Get prior network from network snapshots by network_id,
     *
     * Get current network from networks by network_id
     *
     * Get all users for the network
     *
     * For each user, construct userACL and oldUserACL
     *
     * Compare them to decide which Okta MFA group the user should be added to or removed from
     *
     * Put events to eventbridge to be picked up by okta-sync-network-mfa lambda
     */

    override def putNetworkMfaSyncEvents(networkId: NetworkId)(implicit traceId: TraceId): Future[OktaSyncResponse] = {
      val syncEvents = for {
        network <- networksRepository
          .getById(networkId)(oktaSyncConfig.serviceEntitlements)
          .flattenOption(s"Network $networkId not found")
        _ = log.info("Getting network snapshot", networkId)
        networkPriorSnapshot <- networksRepository
          .getPriorSnapshotByEntityId(networkId)
        allUsers <- usersRepository.getUsers(Set(NetworkFilter(NetworkId.unwrap(networkId))))(oktaSyncConfig.serviceEntitlements)
      } yield allUsers
        .map(u => generateNetworkMfaSyncEventDetail(network, u, networkPriorSnapshot, OktaUserConversions.toOktaUser(u, Set.empty)))
        .flatten

      syncEvents.flatMap(events => if (!events.isEmpty)
          oktaSyncBus.putEvents(events, "NetworkMfaOktaSync")
        else
          Future(Seq(new PutEventsResult().withFailedEntryCount(0)))
        ).map(results => {
          val failedCount = results.map(_.getFailedEntryCount).reduce(_ + _)
          if (failedCount > 0) log.error("Put NetworkMfaOktaSync events failed", "results" -> results.mkString("\n"))
          failedCount
        })
        .map(i => OktaSyncResponse(i))
        .andThen { case Failure(ex) =>
          log.error("Put network MFA sync events failed", ex)
          ex
        }
    }

    private[service] def defaultedExternalIds(externalIds: Seq[ExternalId],
        externalIdTypes: Set[ExternalIdType]): Seq[ExternalId] = {
      val translatedExternalIds = externalIds.flatMap { externalId =>
        val idpFieldName = externalIdTypes.find(_.name == externalId.subject).map(_.idpFieldName)
        idpFieldName.map(ExternalId(_, externalId.id))
      }
      val toClear = externalIdTypes.map(_.idpFieldName) -- externalIds.map(_.subject).toSet
      translatedExternalIds ++ toClear.map(ExternalId(_, "")).toSeq
    }

    private def cachedExternalIdTypes(networkId: NetworkId)(implicit traceId: TraceId): Future[Set[ExternalIdType]] =
      cachingF(NetworkId.unwrap(networkId))(oktaSyncConfig.cacheTtl) {
        externalIdTypeRepository.getByNetwork(networkId).map(_.toSet)
      }

    private def decryptMfas(
        user: User
    )(implicit traceId: TraceId): Future[Map[String, Factor]] = {
      user.eventInfo.eventType match {
        case UserDomainEvent.UserSynced =>
          val decodedEncryptedData = encryptedMfaDataMap(user)
          for {
            decryptedMap <-
              if (decodedEncryptedData.nonEmpty) encryptionClient.bulkDecrypt[String, String](decodedEncryptedData)
              else Future.successful(Map.empty)
            decryptedFactors = decryptedMap
              .map { case (factor, decryptedVal) => factor -> user.mfas(factor).copy(entry = Some(decryptedVal)) }
            allFactors = decryptedFactors.foldLeft(user.mfas) { case (combinedFactors, (factorType, updatedFactor)) =>
              combinedFactors + (factorType -> updatedFactor)
            }
          } yield allFactors
        case _ => Future(user.mfas)
      }
    }

    private def getPasswordOpt(userId: String, loginMode: LoginMode, eventType: UserDomainEvent) =
      if (eventType == UserDomainEvent.UserSynced && !Set(LoginMode.UnifiedPassword, LoginMode.SSO).contains(loginMode))
        transientStateRepository
          .getAndDelete(userId)
          .map(_.flatMap(state => BcryptHash.fromString(state.password)))
      else
        Future.successful(None)

    private def encryptedMfaDataMap(user: User)(implicit traceId: TraceId): Map[String, EncryptedData] = {
      user.mfas.filterNot(_._2.isFactorEnrolled).flatMap {
        case (factorType, factor) =>
          factor.entry match {
            case Some(entry) =>
              val b64decodedDataTry = Try(Base64.getDecoder.decode(entry))
              b64decodedDataTry match {
                case Failure(exception) =>
                  log.error(exception, s"Error decoding factor $factorType userId=${user.id}")
                  None
                case Success(b64decodedData) =>
                  val encryptedData = encryptionClient.wrap(new String(b64decodedData, StandardCharsets.UTF_8), ClientId.Unknown)
                  Some(factorType -> encryptedData)
              }
            case _ => None
          }
      }
    }

    private def updateEnrolledMfas(user: User, updatedMfas: Map[String, Factor]): User = {
      val enrolledMfas = user.mfas.map { case (factorType, factor) =>
        val isEnrolled = updatedMfas(factorType).isFactorEnrolled
        factorType -> factor.copy(isFactorEnrolled = isEnrolled)
      }
      user.copy(mfas = enrolledMfas)
    }

    /**
     * Use event info to correspondingly set Okta activation state
     */
    private def setUserActiveState(user: OktaSdkUser, eventType: UserDomainEvent)
      (implicit traceId: TraceId): Future[Boolean] = {
      eventType match {
        case UserDomainEvent.UserActivatedSendEmail =>
          oktaService
            .activateUser(user, sendEmail = true)
            .map(_ => true)
        case UserDomainEvent.UserActivatedDontSendEmail =>
          oktaService
            .activateUser(user, sendEmail = false)
            .map(_ => true)
        case UserDomainEvent.UserDeactivated =>
          oktaService
            .deactivateUser(user)
            .map(_ => true)
        case UserDomainEvent.UserPasswordReset =>
          oktaService
            .resetPassword(user)
            .map(_ => true)
        case _ => Future.successful(false)
      }
    }

  }

  case class UserUpdated(oktaId: String, oktaLoginId: String, simonId: Option[String])

}
