package com.simonmarkets.users.service

import akka.http.scaladsl.model.StatusCodes
import com.goldmansachs.marquee.pipg.LicenseName.{CRD, NPN}
import com.goldmansachs.marquee.pipg.LicenseType.{Annuities, Securities}
import com.goldmansachs.marquee.pipg.{GroupIdType, License, UserACL, UserRole}
import com.okta.sdk.resource.model.{UserProfile, User => OktaSdkUser}
import com.simonmarkets.api.users.HttpACLClient
import com.simonmarkets.capabilities.EndpointScopes
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.networks.common.service.networkservice.BasicNetworkService
import com.simonmarkets.okta.domain.{OktaId, SimonId}
import com.simonmarkets.okta.service.OktaService
import com.simonmarkets.syntax.anyOpsConversion
import com.simonmarkets.users.ExternalUsersClient
import com.simonmarkets.users.api.request.{AuthorizationCodeInlineHookRequest, ClientCredentialInlineHookRequest, SamlBearerHookRequest}
import com.simonmarkets.users.common.IdType
import com.simonmarkets.users.common.User.{DistributorKey, OmsKey}
import com.simonmarkets.users.common.api.request.UpsertUserRequest
import com.simonmarkets.users.common.responses.ServiceEntitlements
import com.simonmarkets.users.domain._
import io.scalaland.chimney.dsl.TransformerOps
import simon.Id.NetworkId
import zio.{IO, UIO, ZIO}

import scala.concurrent.duration.FiniteDuration


/**
 * Generates data to add/modify claims on authentication token.
 * Depending on flow, may create/update a user.
 *
 * Currently supported grant types:
 *  - client credentials
 *  - authorization code
 *  - saml see [[https://developer.okta.com/docs/guides/implement-grant-type/saml2assert/main docs]]
 *
 * Reference - [[https://developer.okta.com/docs/reference/token-hook/ okta]]
 */
trait AccessTokenService {

  /**
   * Enrichment data for client credentials
   */
  def clientCredentialEnrichment(req: ClientCredentialInlineHookRequest)
    (implicit traceId: TraceId): IO[AccessTokenServiceError, SystemUser] =
    getScopesAndTokenExpiration(SimonId(req.data.access.claims.sub))

  /**
   * Enrichment data for authorization code
   */
  def authCodeEnrichment(req: AuthorizationCodeInlineHookRequest)
    (implicit traceId: TraceId): IO[AccessTokenServiceError, HumanUser]

  /**
   * Enrichment data for saml2bearer
   */
  def samlBearerEnrichment(req: SamlBearerHookRequest)
    (implicit traceId: TraceId): IO[AccessTokenServiceError, HumanUser]

  /**
   * Admin/test method for client credentials
   */
  def getScopesAndTokenExpiration(id: SimonId)
    (implicit traceId: TraceId): IO[AccessTokenServiceError, SystemUser]

}

object AccessTokenService {

  class Impl(
      serviceUser: UserACL,
      cachedAclClient: HttpACLClient,
      networkTokenExpiration: Map[NetworkId, FiniteDuration],
      appExpiryConfig: Map[String, FiniteDuration],
      oktaService: OktaService,
      userService: UserServiceImpl,
      networkService: BasicNetworkService,
      userImpersonationService: UserImpersonationService,
      externalUsersClient: ExternalUsersClient,
      ssoMapper: SsoMapper,
  ) extends AccessTokenService with TraceLogging {

    implicit val serviceUserImplicit: UserACL = serviceUser

    override def getScopesAndTokenExpiration(id: SimonId)
      (implicit traceId: TraceId): IO[AccessTokenServiceError, SystemUser] = {
      for {
        _ <- ZIO.succeed(log.info("[getScopesAndTokenExpiration] Getting scopes and expiration", id))
        acl <- ZIO
          .fromFuture(_ => cachedAclClient.getUserACL(id.id))
          .flatMapError(mapErrorType(s"User ACL not found for id: $id"))
      } yield
        SystemUser(
          icnId = acl.iCapitalUserId,
          wlp = acl.whiteLabelPartnerId,
          scopes = EndpointScopes.getAvailableAccessKeys(acl) + EndpointScopes.FaScope,
          expiration = networkTokenExpiration.get(acl.networkId),
          trace = traceId
        )
    }

    override def authCodeEnrichment(req: AuthorizationCodeInlineHookRequest)
      (implicit traceId: TraceId): IO[AccessTokenServiceError, HumanUser] = {
      onboardOrUpdateAndGetUserOktaData(OktaId(req.data.context.user.id), req.data.context.protocol.request.redirect_uri)
    }

    override def samlBearerEnrichment(req: SamlBearerHookRequest)
      (implicit traceId: TraceId): IO[AccessTokenServiceError, HumanUser] = {
      val configOverride = appExpiryConfig.get(req.data.context.protocol.client.id)
      onboardOrUpdateAndGetUserOktaData(OktaId(req.data.context.user.id), None, configOverride)
    }

    private def onboardOrUpdateAndGetUserOktaData(userId: OktaId, redirectUri: Option[String],
        expiryOverride: Option[FiniteDuration] = None)
      (implicit traceId: TraceId): IO[AccessTokenServiceError, HumanUser] =
      for {
        _ <- ZIO.succeed(log.info("onboardOrUpdateAndGetUserOktaData start", userId))

        //get okta user
        requestingOktaUser <-
          ZIO
            .fromFuture(implicit ec => oktaService.getUserByOktaId(userId.id))
            .flatMapError(mapErrorType("Okta service failed"))
            .someOrFail(UserNotFound(userId))

        //conditionally swap if impersonating
        impersonationOpt <- oktaService
          .getSimonId(requestingOktaUser)
          .map { simonId =>
            ZIO.fromFuture(_ => userImpersonationService.getByImpersonatorUserIdThenExpire(simonId))
          }
          .getOrElse(ZIO.succeed(None))
          .flatMapError(mapErrorType("Impersonation service failed"))
        finalOktaUser <- impersonationOpt match {
          case Some(impersonation) =>
            ZIO
              .succeed(log.info(
                s"impersonator=${impersonation.impersonatorUserId} " +
                  s"impersonated=${impersonation.impersonatedUserId} " +
                  s"traceId=$traceId")) *>
              ZIO
                .fromFuture(implicit ec => oktaService.getUserBySimonId(impersonation.impersonatedUserId))
                .someOrFail(UserNotFound(SimonId(impersonation.impersonatedUserId)))
                .flatMapError(mapErrorType("Okta service failed"))
          case None => ZIO.succeed(requestingOktaUser)
        }

        //get acl (via insert or update)
        acl <- authorize(finalOktaUser)

        //convert to okta claims response
        initResponse = toUserOktaData(acl, expiryOverride)

        //conditionally set impersonator if impersonating
        responseWithImpersonation =
          if (impersonationOpt.isDefined)
            initResponse.copy(impersonatorUserId = Some(impersonationOpt.get.impersonatorUserId))
          else
            initResponse

        //conditionally modify if passporting and not impersonating
        response <-
          if (impersonationOpt.isDefined)
            ZIO.succeed(responseWithImpersonation)
          else
            resolvePassporting(redirectUri, acl.passport, responseWithImpersonation)
      } yield response

    /**
     * Extract and set domain-relevant icn id if user is passporting between whitelabels
     *
     * For example - extract `abc` from `https://abc.icapitalnetwork.com/some/path` and lookup in passport map
     *
     * @param redirectUri - target domain of user
     * @param passport    - allowable passporting domains and ids
     * @return conditionally modified claims response
     */
    private def resolvePassporting(
        redirectUri: Option[String],
        passport: Option[Map[String, Int]],
        user: HumanUser
    )(implicit traceId: TraceId): UIO[HumanUser] = {
      val icnId = for {
        rawUri <- redirectUri
        uri <- PassportService.wlpFromUrl(rawUri)
        pass <- passport
        icnId <- pass.get(uri)
      } yield icnId

      if (icnId.isDefined) {
        ZIO
          .succeed(log.info("User Passport", "simonId" -> user.userId, "passportId" -> icnId, "uri" -> redirectUri.get))
          .as(user.copy(icnId = icnId.map(_.toString)))
      } else ZIO.succeed(user)
    }

    private def authorize(oktaUser: OktaSdkUser)
      (implicit traceId: TraceId): IO[AccessTokenServiceError, UserACL] =
      for {
        _ <- ZIO.succeed(log.info("authorize start", oktaUser.getId))
        userType <- resolveNetworkAndType(oktaUser.getProfile)
        response <- userType match {
          case t: NoJit => ZIO.succeed(log.info("User sso type", "ssoType" -> t)) *>
            updateNoJitUser(oktaUser, OktaId(oktaUser.getId), t)
          case u: Jit =>
            ZIO.succeed(log.info("User sso type and network",
              "ssoType" -> "Jit",
              "networkId" -> u.network.id,
              "networkName" -> u.network.name)) *>
              getJit(oktaUser, u, OktaId(oktaUser.getId))
        }
      } yield response

    private def updateNoJitUser(oktaUser: OktaSdkUser, oktaId: OktaId, userType: NoJit)
      (implicit traceId: TraceId): IO[AccessTokenServiceError, UserACL] = {
      for {
        simonId <- ZIO.succeed(oktaService.getSimonId(oktaUser)).someOrFail(UserNotFound(oktaId))
        aclUpsertTup <- transformAcl(SimonId(simonId))
        (acl, existingUser) = aclUpsertTup
        ssoEnrichedRequest <- ZIO.fromEither(ssoMapper.setContext(oktaUser.getProfile, existingUser))
        fullyEnrichedRequest <- userType match {
          case ContextOnly => ZIO.succeed(ssoEnrichedRequest)
          case NoJitIdentitySso => enrichUpsertRequest(ssoEnrichedRequest, acl)
        }
        change = existingUser != fullyEnrichedRequest
        _ <- ZIO.succeed(log.info("conditionally update user context", "toUpdate" -> change))
        response <-
          if (change) ZIO
            .fromFuture(_ => userService.handleUpdateRequest(serviceUser, fullyEnrichedRequest, None))
            .flatMapError(mapErrorType("User update failed"))
            .map(_ => acl)
          else ZIO.succeed(acl)
      } yield response
    }

    private def getJit(oktaUser: OktaSdkUser, userType: Jit, oktaId: OktaId)
      (implicit traceId: TraceId): IO[AccessTokenServiceError, UserACL] = {
      oktaService.getSimonId(oktaUser) match {
        case None => insertUser(oktaUser.getProfile, userType, oktaId)
        case Some(simonId) => updateUser(oktaUser.getProfile, SimonId(simonId), userType)
      }
    }

    private def insertUser(oktaUser: UserProfile, userType: Jit, oktaId: OktaId)
      (implicit traceId: TraceId): IO[AccessTokenServiceError, UserACL] = {

      val minRequest = UpsertUserRequest(
        networkId = userType.network.id,
        email = oktaUser.getEmail,
        firstName = oktaUser.getFirstName,
        lastName = oktaUser.getLastName,
        externalIds = Map.empty,
        tradewebEligible = None,
        regSEligible = None,
        roles = Set.empty,
        idpId = oktaId.id.some,
        id = oktaId.id.some
      )

      for {
        _ <- ZIO.succeed(log.info("insert user"))
        request <- ZIO.fromEither(ssoMapper.applyAll(minRequest, oktaUser))
        enriched <- userType match {
          case _: JitSso => ZIO.succeed(request)
          case u: JitIdentitySso => enrichUpsertRequest(request, u, oktaUser)
        }
        userAndSecret <- ZIO
          .fromFuture(_ => userService.handleInsertRequest(serviceUser, enriched))
          .flatMapError(mapErrorType("User insert failed"))
      } yield UserACL(userAndSecret._1.asLegacyUser, userType.network.asLegacyNetwork)
    }

    private def updateUser(oktaUser: UserProfile, id: SimonId, userType: Jit)
      (implicit traceId: TraceId): IO[AccessTokenServiceError, UserACL] = {
      for {
        aclUpsertTup <- transformAcl(id)
        (acl, existingUser) = aclUpsertTup
        ssoEnrichedRequest <- ZIO.fromEither(ssoMapper.applyAll(existingUser, oktaUser))
        withPilotRoles = retainPilotRoles(ssoEnrichedRequest, existingUser.customRoles)
        fullyEnrichedRequest <- userType match {
          case _: JitSso => ZIO.succeed(withPilotRoles)
          case u: JitIdentitySso => enrichUpsertRequest(withPilotRoles, u, oktaUser)
        }
        change = existingUser != fullyEnrichedRequest
        _ <- ZIO.succeed(log.info("conditionally update user", "toUpdate" -> change))
        response <-
          if (change) ZIO
            .fromFuture(_ => userService.handleUpdateRequest(serviceUser, fullyEnrichedRequest, None))
            .flatMapError(mapErrorType("User update failed"))
            .map(u => UserACL(u.asLegacyUser, userType.network.asLegacyNetwork))
          else ZIO.succeed(acl)
      } yield response
    }

    private def enrichUpsertRequest(request: UpsertUserRequest, acl: UserACL)
      (implicit traceId: TraceId): IO[AccessTokenServiceError, UpsertUserRequest] =
      for {
        _ <- ZIO.succeed(log.info("enrich user with identity service data"))
        distributorId <- ZIO
          .getOrFailWith(UnableToEnrichFromIdService(s"Identity SSO user does not have distributor id userId=${acl.userId}")) {
            acl.distributorId
          }
          .tapError(e => ZIO.succeed(log.error(e.message)))
        enriched <- enrichFromIdService(request, distributorId, acl.networkId)
      } yield enriched

    private def enrichUpsertRequest(request: UpsertUserRequest, userType: JitIdentitySso, oktaUser: UserProfile)
      (implicit traceId: TraceId): IO[AccessTokenServiceError, UpsertUserRequest] =
      for {
        _ <- ZIO.succeed(log.info("enrich Jit SSO user with identity service data"))
        distributorId <- ZIO
          .fromEither(ssoMapper.getSsoClientId(oktaUser))
          .someOrFail(UnableToEnrichFromIdService("JitIdentitySso user does not have distributor id"))
        enriched <- enrichFromIdService(request, distributorId, userType.network.id)
      } yield enriched

    private def enrichFromIdService(request: UpsertUserRequest, distributorId: String, networkId: NetworkId)
      (implicit traceId: TraceId): IO[AccessTokenServiceError, UpsertUserRequest] = {
      val attempt = for {
        _ <- ZIO.succeed(log.info("distributor id for enrichment", distributorId))
        entitlements <- ZIO
          .fromFuture(_ => externalUsersClient.getEntitlements(distributorId, IdType.Distributor, networkId))
          .flatMapError(mapErrorType("Failed to enrich user with identity service data"))
      } yield {
        request.copy(
          locations = entitlements.locations |> wrap,
          faNumbers = entitlements.faNumbers |> wrap,
          customRoles = calculateCustomRoles(entitlements, request.roles, request.customRoles.getOrElse(Set.empty)) |> wrap,
          licenses = calculateLicenses(entitlements, request.licenses.getOrElse(Set.empty)) |> wrap,
          cusips = entitlements.cusips |> wrap
        )
      }

      //catch all failures, effectively allowing this step to be skipped
      attempt
        .tapError(e => ZIO.succeed(log.error("Failed to enrich identity service user", e)))
        .orElseSucceed(request)
    }

    private def toUserOktaData(acl: UserACL, expiryOverride: Option[FiniteDuration])
      (implicit traceId: TraceId): HumanUser = {
      HumanUser(
        userId = acl.userId,
        icnId = acl.iCapitalUserId,
        wlp = acl.whiteLabelPartnerId,
        impersonatorUserId = None,
        impersonatorIcnId = None,
        scopes = EndpointScopes.getAvailableAccessKeys(acl) + EndpointScopes.FaScope,
        expiration = expiryOverride.orElse(networkTokenExpiration.get(acl.networkId)),
        trace = traceId
      )
    }

    private def resolveNetworkAndType(user: UserProfile)
      (implicit traceId: TraceId): IO[AccessTokenServiceError, SsoUserType] = {
      for {
        ssoNetworkId <- ZIO.fromEither(ssoMapper.getSsoNetworkId(user))
        ssoExternalNetworkId <- ZIO.fromEither(ssoMapper.getSsoExternalNetworkId(user))
        networkId <- ZIO.fromEither(ssoMapper.getNetworkId(user))
        _ <- ZIO.succeed(log.info("network id", ssoNetworkId, ssoExternalNetworkId))
        userType <-
          if (ssoNetworkId.isDefined)
            ZIO.fromFuture(_ => networkService.getNetworkById(serviceUser, ssoNetworkId.get))
              .flatMapError(mapErrorType(s"Network not found by id $ssoNetworkId"))
              .flatMap(resolveUserType)
          else if (ssoExternalNetworkId.isDefined)
            ZIO.fromFuture(_ => networkService.getNetworkByExternalId(serviceUser, ssoExternalNetworkId.get))
              .flatMapError(mapErrorType(s"Network not found by external id $ssoExternalNetworkId"))
              .flatMap(resolveUserType)
          else if (networkId.isDefined) resolveUserType(networkId.get)
          else ZIO.succeed(ContextOnly)
      } yield userType
    }

    private def resolveUserType(networkId: NetworkId)
      (implicit traceId: TraceId): IO[AccessTokenServiceError, SsoUserType] = {
      for {
        identityNetworkIds <- ZIO
          .fromFuture(_ => externalUsersClient.getCachedIdentityNetworks)
          .flatMapError(mapErrorType("Failed to retrieve identity networks set"))
        isIdentity = identityNetworkIds.contains(networkId)
        userType <-
          if (isIdentity) ZIO.succeed(NoJitIdentitySso)
          else ZIO.succeed(ContextOnly)
      } yield userType
    }

    private def resolveUserType(network: Network)(implicit traceId: TraceId): IO[AccessTokenServiceError, Jit] = {
      for {
        identityNetworkIds <- ZIO
          .fromFuture(_ => externalUsersClient.getCachedIdentityNetworks)
          .flatMapError(mapErrorType("Failed to retrieve identity networks set"))
        isIdentity = identityNetworkIds.contains(network.id)
      } yield if (isIdentity) JitIdentitySso(network) else JitSso(network)
    }

    private def transformAcl(id: SimonId)
      (implicit traceId: TraceId): IO[AccessTokenServiceError, (UserACL, UpsertUserRequest)] =
      ZIO
        .fromFuture(_ => cachedAclClient.getUserACL(id.id))
        .flatMapError(mapErrorType(s"User ACL not found for id: $id"))
        .map { acl =>
          val upsertUserRequest = acl
            .into[UpsertUserRequest]
            .withFieldComputed(_.externalIds, acl => {
              val legacyIds = List(
                acl.distributorId.map(DistributorKey -> _),
                acl.omsId.map(OmsKey -> _)
              ).flatten.toMap
              ExternalId.toStringMap(acl.externalIds.getOrElse(Seq.empty)) ++ legacyIds
            })
            .withFieldComputed(_.groups, _.groups.getOrElse(Map.empty).flatMap { case (k, v) => GroupIdType.unapply(k).map(_ -> v) })
            .withFieldComputed(_.tradewebEligible, _.tradewebEligible.some)
            .withFieldComputed(_.regSEligible, _.regSEligible.some)
            .withFieldComputed(_.faNumbers, _.faNumbers |> wrap)
            .withFieldComputed(_.custodianFaNumbers, _.custodianFaNumbers |> wrap)
            .withFieldComputed(_.locations, _.locations |> wrap)
            .withFieldComputed(_.customRoles, _.customRoles |> wrap)
            .withFieldComputed(_.licenses, _.licenses |> wrap)
            .withFieldComputed(_.cusips, _.cusips |> wrap)
            .withFieldComputed(_.purviewLicenses, _.purviewLicenses)
            .withFieldComputed(_.id, _.userId.some) //user id constant, should not matter but to be extra safe
            .withFieldConst(_.isActive, Some(true)) //jit can activate
            .withFieldConst(_.loginMode, None) //no-op
            .withFieldConst(_.userType, None) //no-op
            .transform
          (acl, upsertUserRequest)
        }

    private def mapErrorType(message: String)
      (implicit traceId: TraceId): PartialFunction[Throwable, UIO[AccessTokenServiceError]] = {
      case HttpError(resp, StatusCodes.NotFound) =>
        ZIO.succeed(log.error(s"$message: $resp")).map(_ => GeneralNotFound(message))
      case HttpError(resp, StatusCodes.Unauthorized) =>
        ZIO.succeed(log.error(s"$message: $resp")).map(_ => NotAuthorized(message))
      case other =>
        ZIO.succeed(log.error(s"$message: ${other.getMessage}")).map(_ => Internal(s"$message: ${other.getMessage}"))
    }

    private def calculateCustomRoles(serviceEntitlements: ServiceEntitlements, roles: Set[UserRole],
        customRoles: Set[String]): Set[String] = {
      //Copy over all the roles except for Super Admin into custom roles
      //A lot of code in old services depends on user.isAdmin and for that UserRole.EqPIPGGSAdmin is needed but we
      //do not want to give these users EqPipgGSAdmin custom role.
      customRoles ++ roles.filterNot(_ == UserRole.EqPIPGGSAdmin).map {
        _.name
      } ++ serviceEntitlements.customRoles
    }

    private def calculateLicenses(serviceEntitlements: ServiceEntitlements, licenses: Set[License]): Set[License] = {
      // Check if a CRD is present in the service entitlements and update its value in Licenses
      val licensesWithCRD = serviceEntitlements
        .crdNum
        .map { crd => licenses.filterNot(_.name == CRD) ++ Set(License(CRD, crd, Securities, None)) }
        .getOrElse(licenses)
      // Then update the NPN if it is present
      serviceEntitlements
        .npnNum
        .map { npn => licensesWithCRD.filterNot(_.name == NPN) ++ Set(License(NPN, npn, Annuities, None)) }
        .getOrElse(licensesWithCRD)
    }

    private def retainPilotRoles(upsertRequest: UpsertUserRequest,
        existingRoles: Option[Set[String]]): UpsertUserRequest = {
      val pilotRoles: Set[String] = existingRoles.getOrElse(Set.empty).filter(_.toLowerCase.contains("pilot"))
      val ssoRoles: Set[String] = upsertRequest.customRoles.getOrElse(Set.empty)
      upsertRequest.copy(customRoles = wrap(pilotRoles ++ ssoRoles))
    }

    private def wrap[T](in: Set[T]): Option[Set[T]] = {
      if (in.nonEmpty) Some(in)
      else None
    }
  }
}
