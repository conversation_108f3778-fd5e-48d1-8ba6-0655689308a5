package com.simonmarkets.users.service

import akka.NotUsed
import akka.http.scaladsl.model.StatusCodes
import akka.stream.Materializer
import akka.stream.scaladsl.{Sink, Source}
import com.goldmansachs.marquee.pipg.Custodian.EnumNotFound
import com.goldmansachs.marquee.pipg.{UserACL, UserRole, User => LegacyUser}
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.SimonUICapabilities.ViewUISwitchUsersNetworkCapabilities
import com.simonmarkets.capabilities.UsersCapabilities._
import com.simonmarkets.eventbridge.client.EventBridgeClient
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.mongodb.error.Conflict
import com.simonmarkets.mongodb.trigger.codec.Codecs.singleUserEncoder
import com.simonmarkets.mongodb.trigger.event.SingleUser
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.networks.common.clients.usersync.{SourceDestinationPrimaryIdKind, SourceDestinationSecondaryIdKind, SourceDestinationSystem, UserNetworkMappingRequest, UserNetworkMappingResponse, UserSyncMappingClient}
import com.simonmarkets.networks.common.service.ExternalIdTypeService
import com.simonmarkets.networks.common.service.networkservice.BasicNetworkService
import com.simonmarkets.okta.client.api.application.{CreateOAuthSecretResponse, GetOAuthSecretResponse}
import com.simonmarkets.okta.domain.{FactorType, UserInfo}
import com.simonmarkets.okta.service.OktaService
import com.simonmarkets.resteasy.utils.FutureOps._
import com.simonmarkets.syntax._
import com.simonmarkets.users.api.Page
import com.simonmarkets.users.api.request._
import com.simonmarkets.users.api.response.PartialUserUpdateError
import com.simonmarkets.users.common.UniqueUserId.{Distributor, Email, Guid, Oms}
import com.simonmarkets.users.common.User.{DistributorKey, OmsKey}
import com.simonmarkets.users.common.Utils._
import com.simonmarkets.users.common._
import com.simonmarkets.users.common.api.request.{BumpUsersVersionRequest, GetUniqueUserIdRequest, OktaSyncRequest, RequestWithOptNetworkIds, RequestWithOptUserIds, UpsertUserRequest}
import com.simonmarkets.users.common.api.response._
import com.simonmarkets.users.config.UserServiceConfig
import com.simonmarkets.users.domain.ImpersonationStatus
import com.simonmarkets.users.repository.UserRepository
import com.simonmarkets.users.repository.filters._
import simon.Id.NetworkId

import java.time.LocalDateTime
import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

trait UserService {

  def getUserViewById(requesterAcl: UserACL, userId: String, isICapitalUserId: Boolean = false)
    (implicit traceId: TraceId): Future[Option[UserView]]

  def getUserById(requesterAcl: UserACL, userId: String)(implicit traceId: TraceId): Future[Option[User]]

  def getUserPage(requesterAcl: UserACL, limit: Option[Int], from: Option[String], filters: Map[String, List[String]])
    (implicit traceId: TraceId): Future[Page[UserView]]

  def getUserPage(requesterAcl: UserACL, limit: Option[Int], from: Option[String], filters: Set[MongoFilter])
    (implicit traceId: TraceId): Future[Page[User]]

  def getUserByUniqueUserId(requesterAcl: UserACL, request: GetUniqueUserIdRequest, upsertField: UpsertField)
    (implicit traceId: TraceId): Future[(Option[User], UniqueUserId)]

  def deactivateUser(requesterAcl: UserACL, userId: String)(implicit traceId: TraceId): Future[UserView]

  def activateUser(requesterAcl: UserACL, userId: String, sendEmail: Boolean)
    (implicit traceId: TraceId): Future[UserView]

  def resetPassword(requesterAcl: UserACL, userId: String)(implicit traceId: TraceId): Future[UserView]

  def resetMfa(requesterAcl: UserACL, userId: String, request: ResetMfaRequest)
    (implicit traceId: TraceId): Future[UserView]

  def handleInsertRequest(requesterAcl: UserACL, request: UpsertUserRequest, eventType: Option[UserDomainEvent] = None)
    (implicit traceId: TraceId): Future[(User, Option[String])]

  def handleBulkUpsertRequest(requesterAcl: UserACL, requests: Seq[UpsertUserRequest])
    (implicit traceId: TraceId, mat: Materializer): Future[BatchUserUpsertResponse]

  def idFromEmail(email: String, networkId: NetworkId, requesterAcl: UserACL)
    (implicit traceId: TraceId): Future[String]

  def handleUpdateRequest(requesterAcl: UserACL, request: UpsertUserRequest,
      eventType: Option[UserDomainEvent] = None)
    (implicit traceId: TraceId): Future[User]

  def handleOktaSyncRequest(requesterAcl: UserACL, request: OktaSyncRequest)
    (implicit traceId: TraceId): Future[OktaSyncResponse]

  def handleBumpUsersRequest(requesterAcl: UserACL, request: BumpUsersVersionRequest)
    (implicit traceId: TraceId): Future[BumpUsersVersionResponse]

  def partiallyUpdateUser(requester: UserACL, updateRequests: Seq[PartialUserUpdate])
    (implicit traceId: TraceId, mat: Materializer): Future[Seq[PartialUserUpdateError]]

  def listApplicationSecrets(requesterAcl: UserACL, appId: String)
    (implicit traceId: TraceId): Future[List[GetOAuthSecretResponse]]

  def createApplicationSecret(requesterAcl: UserACL, appId: String)
    (implicit traceId: TraceId): Future[CreateOAuthSecretResponse]

  def activateApplicationSecret(requesterAcl: UserACL, appId: String, secretId: String)
    (implicit traceId: TraceId): Future[GetOAuthSecretResponse]

  def deactivateApplicationSecret(requesterAcl: UserACL, appId: String, secretId: String)
    (implicit traceId: TraceId): Future[GetOAuthSecretResponse]
}

class UserServiceImpl(
    systemAcl: UserACL,
    userRepository: UserRepository,
    networkService: BasicNetworkService,
    oktaService: OktaService,
    config: UserServiceConfig,
    oktaSyncBus: EventBridgeClient,
    externalIdTypeRepo: ExternalIdTypeService,
    impersonationService: UserImpersonationService,
    mappings: UserSyncMappingClient,
    systemUserNetworkLimitationIgnoreList: Set[NetworkId]
)(implicit ec: ExecutionContext) extends UserService with TraceLogging with UserAcceptedAccessKeysGenerator {

  override def getUserViewById(requesterAcl: UserACL, userId: String, isICapitalUserId: Boolean = false)
    (implicit traceId: TraceId): Future[Option[UserView]] = {
    if (isICapitalUserId)
      userRepository.getUser(Set(ICapitalUserIdFilter(userId)))(generateEntitlements(requesterAcl, ViewCapabilities)).map(_.map(UserView(_)))
    else getUserById(requesterAcl, userId).map(_.map(UserView(_)))
  }

  def getUserById(requesterAcl: UserACL, userId: String)(implicit traceId: TraceId): Future[Option[User]] = {
    log.info(s"Getting user from repository", "requester" -> requesterAcl.userId, userId)
    userRepository.getById(userId)(generateEntitlements(requesterAcl, ViewCapabilities))
  }

  override def getUserPage(
    requesterAcl: UserACL,
    limit: Option[Int],
    from: Option[String],
    filters: Map[String, List[String]]
  )(implicit
    traceId: TraceId
  ): Future[Page[UserView]] =
    getUserPage(requesterAcl, limit, from, Filters.make(filters)).map(page => page.map(UserView(_)))

  override def getUserPage(requesterAcl: UserACL, limit: Option[Int], from: Option[String],
      filters: Set[MongoFilter])
    (implicit traceId: TraceId): Future[Page[User]] = {
    log.info("Getting users from repository", "requester" -> requesterAcl.userId, limit, from, filters)

    val entitlements = generateEntitlements(requesterAcl, ViewCapabilities)

    userRepository
      .getUsersPage(limit.getOrElse(config.paging.defaultLimit), from, filters)(entitlements)
  }

  override def deactivateUser(requesterAcl: UserACL, userId: String)(implicit traceId: TraceId): Future[UserView] = {
    log.info("Deactivating user", "requester" -> requesterAcl.userId, userId)
    val deactivationEvent = EventInfo(UserDomainEvent.UserDeactivated, requesterAcl)
    for {
      user <- userRepository.getById(userId)(availableAccessKeysGen.getAvailableAccessKeysForCapabilities(ViewCapabilities, requesterAcl)).flattenOption()
      availableAccessKeys = availableAccessKeysGen.getAvailableAccessKeysForCapabilities(UploadCapabilities, requesterAcl)
      acceptedAccessKeys = getAcceptedAccessKeys(user)
      _ <- if (availableAccessKeys.intersect(acceptedAccessKeys).nonEmpty) Future.unit
      else Future.failed(HttpError.unauthorized(s"User does not have right access keys to deactivate user: $userId"))

      _ <- if (config.internalDomains.exists(emailSuffix => user.email.contains(emailSuffix))) {
        Future.failed(HttpError.badRequest(s"Deactivation of ${config.internalDomains.mkString(",")} accounts are not allowed" +
          s" via the Onboarding tool. Please contact an Okta administrator."))
      } else Future.unit
      deactivatedUser = user.copy(
        omsId = None,
        distributorId = None,
        isActive = false,
        eventInfo = deactivationEvent
      ).refreshUpdatedFields(requesterAcl.userId)
      inactive <- userRepository.updateUser(requesterAcl.userId, deactivatedUser)
      _ <- if (user.isManager) {
        log.info("removing fas");
        networkService.removeUserFromApprovers(requesterAcl, user)
      } else Future.unit
    } yield UserView(inactive)
  }

  override def activateUser(requesterAcl: UserACL, userId: String, sendEmail: Boolean)
    (implicit traceId: TraceId): Future[UserView] = {
    log.info("Activating user", "requester" -> requesterAcl.userId, userId)
    val activationEvent = EventInfo(
      eventType = if (sendEmail) UserDomainEvent.UserActivatedSendEmail else UserDomainEvent.UserActivatedDontSendEmail,
      duplicated = true,
      triggeredBy = requesterAcl.userId,
      correlationId = traceId
    )
    for {
      user <- getUserById(requesterAcl, userId).flattenOption()
      _ <- activationCheck(user.email)
      activatedUser <-
        if (!user.isActive) userRepository.updateUser(user.id, user.copy(isActive = true, eventInfo = activationEvent))
        else Future.successful(user)
    } yield UserView(activatedUser)
  }

  private def activationCheck(userEmail: String)(implicit traceId: TraceId): Future[Unit] =
    when(config.internalDomains.exists(emailSuffix => userEmail.contains(emailSuffix))).failWith {
      HttpError.badRequest(s"Activation of ${config.internalDomains.mkString(",")} accounts are not allowed" +
        s" via the Onboarding tool. Please contact an Okta administrator.")
    }

  private def checkAccessKeysToResetUser(requesterAcl: UserACL, userId: String, resetOperation: ResetOperation)
    (implicit traceId: TraceId): Future[User] = {
    log.info(s"Resetting $resetOperation for user", "requester" -> requesterAcl.userId, userId)
    for {
      user <- getUserById(requesterAcl, userId).flattenOption()
      availableAccessKeys = availableAccessKeysGen.getAvailableAccessKeysForCapabilities(UploadCapabilities, requesterAcl)
      acceptedAccessKeys = getAcceptedAccessKeys(user)
      _ <- if (availableAccessKeys.intersect(acceptedAccessKeys).nonEmpty) Future.unit
      else Future.failed(HttpError.unauthorized(s"User does not have right access keys to reset $resetOperation for user: $userId"))
    } yield user
  }

  override def resetPassword(requesterAcl: UserACL, userId: String)(implicit traceId: TraceId): Future[UserView] = {
    for {
      user <- checkAccessKeysToResetUser(requesterAcl, userId, ResetOperation.Password)

      _ <- if (user.loginMode == LoginMode.SSO)
        Future.failed(HttpError.badRequest(s"Can't reset password for SSO user: $userId"))
      else Future.unit

      _ <- when(user.isPreviewUser).failWith(HttpError.badRequest(s"Can't reset password for Preview User: $userId"))

      resetPasswordEvent = EventInfo(
        eventType = UserDomainEvent.UserPasswordReset,
        duplicated = true,
        triggeredBy = requesterAcl.userId,
        correlationId = traceId
      )

      updatedUser = user.copy(
        emailSentAt = Some(LocalDateTime.now),
        emailSentBy = Some(requesterAcl.userId),
        eventInfo = resetPasswordEvent
      ).refreshUpdatedFields(requesterAcl.userId)
      _ <- userRepository.updateUser(requesterAcl.userId, updatedUser)
    } yield UserView(updatedUser)
  }

  override def resetMfa(requesterAcl: UserACL, userId: String, request: ResetMfaRequest)
    (implicit traceId: TraceId): Future[UserView] = {
    for {
      user <- checkAccessKeysToResetUser(requesterAcl, userId, ResetOperation.Mfa)
      _ <- user.idpId match {
        case Some(oktaId) => oktaService.deleteFactors(request.factorTypes.map(_.toString), oktaId, request.deleteAll.getOrElse(false))
        case None => Future.failed(HttpError.badRequest(s"user with userId $userId doesn't have oktaId. Can't reset mfa factors"))
      }
      userWithResetMfas = resetMfasOnUser(user, request)
      updatedUser <- userRepository.updateUser(requesterAcl.userId, userWithResetMfas)
    } yield UserView(updatedUser)
  }

  private def resetMfasOnUser(user: User, request: ResetMfaRequest): User = {
    if (request.deleteAll.getOrElse(false))
      user.copy(mfas = Map())
    else {
      val updatedMfas = user.mfas
        .filterKeys(factor =>
          !request.factorTypes.contains(FactorType(factor))
        )
      user.copy(mfas = updatedMfas)
    }
  }

  private def validateUserUpsert(requesterAcl: UserACL,
      user: User): Either[HttpError, Unit] = (user.loginMode, user.userType) match {
    case (LoginMode.ClientCredentials, UserType.System) =>
      val systemUsersCapabilities = generateEntitlements(requesterAcl, ManageSystemUsersCapabilities)
      Either.cond(user.entitlements.exists(systemUsersCapabilities), (), HttpError.unauthorized("User does not have correct access keys to upload system user in this network"))

    case _ =>
      val uploadEntitlements = generateEntitlements(requesterAcl, UploadCapabilities)
      Either.cond(user.entitlements.exists(uploadEntitlements), (), HttpError.unauthorized("User does not have right access keys to upload another user in this network")
      )
  }

  private def insertUser(requesterAcl: UserACL, user: User)
    (implicit traceId: TraceId): Future[User] = {
    log.info("Inserting user", "requester" -> requesterAcl.userId, "id" -> user.id)
    validateUserUpsert(requesterAcl, user) match {
      case Left(error) => error.failedFuture
      case Right(_) =>
        userRepository.insertUser(requesterAcl.userId, user)
    }
  }

  private def updateUser(requesterAcl: UserACL, user: User)
    (implicit traceId: TraceId): Future[User] = {
    log.info("Updating user", "requester" -> requesterAcl.userId, "id" -> user.id)
    val uploadCapabilities = generateEntitlements(requesterAcl, UploadCapabilities)
    if (!user.entitlements.exists(uploadCapabilities)) {
      HttpError.unauthorized(s"User does not have right access keys to upload another user (${user.id}) in this network").failedFuture
    } else {
      userRepository.updateUser(requesterAcl.userId, user)
    }
  }

  private def validateUpsertCustodianFaNumbers(request: UpsertUserRequest): Future[Boolean] = {
    val existsCustodianEqualToEnumNotFound: Boolean = request.custodianFaNumbers.exists(_.map(_.custodian).contains(EnumNotFound))
    if (!existsCustodianEqualToEnumNotFound) {
      Future.successful(true)
    } else {
      Future.failed(HttpError.badRequest("Invalid Enum/s detected in \"custodianFaNumbers\""))
    }
  }

  private def validateUpsertIntoWlpFirm(
      requesterAcl: UserACL,
      request: UpsertUserRequest,
  )(implicit traceId: TraceId): Future[Boolean] = {

    def wrap[T](in: Set[T]): Option[Set[T]] =
      if (in.nonEmpty) Some(in)
      else None

    def validateNetworkMapsToRequestWLPFirm(): Future[Boolean] = {
      for {
        mapping <- getMapping(requesterAcl.networkId, requesterAcl.locations.headOption)
        wlpId = mapping.map(_.destination_primary.external_id)
        firmId = mapping.flatMap(_.destination_secondary.map(_.external_id))
        canUpsert <- if (mapping.isEmpty || (request.whiteLabelPartnerId != wlpId || request.firmId != firmId)) {
          HttpError.unauthorized(s"User does not have the correct network and location to upload another user (${request.id.getOrElse("")}) to this white label partner and firm or user has not been unified").failedFuture
        } else {
          Future.successful(true)
        }
      } yield canUpsert
    }

    val isUnifiedFieldPresent = Seq(
      request.whiteLabelPartnerId,
      request.firmId,
      request.icnRoles.flatMap(wrap),
      request.icnGroups.flatMap(wrap),
      request.mfas.headOption,
    ).exists(_.nonEmpty)

    val isAdmin = generateEntitlements(requesterAcl, Set(Admin)).nonEmpty
    if (isAdmin || !isUnifiedFieldPresent)
      Future.successful(true)
    else
      validateNetworkMapsToRequestWLPFirm()

  }

  private def getMapping(networkId: NetworkId, firstLocation: Option[String])
    (implicit traceId: TraceId): Future[Option[UserNetworkMappingResponse]] = {
    val req =
      UserNetworkMappingRequest(
        sourceSystem = SourceDestinationSystem.SIMON,
        destinationSystem = SourceDestinationSystem.ICN,
        sourcePrimaryId = networkId.toString,
        sourcePrimaryIdKind = SourceDestinationPrimaryIdKind.`SIMON_NETWORK`,
        sourceSecondaryId = firstLocation,
        sourceSecondaryIdKind = Some(SourceDestinationSecondaryIdKind.`SIMON_LOCATION`)
      )

    mappings.get(req)
  }

  private def createSystemUser(requesterAcl: UserACL, request: UpsertUserRequest, network: Network)
    (implicit traceId: TraceId): Future[UserInfo] = for {
    _ <- Future.unit
    systemUsersCapabilities = generateEntitlements(requesterAcl, ManageSystemUsersCapabilities)
    // Manually build the network key for the request to ensure the user making the request is doing so in their own network
    requestNetworkKeys = Set(Admin, s"$EditClientCredentialsViaNetwork:${request.networkId.toString}")
    _ <- whenNot(requestNetworkKeys.exists(systemUsersCapabilities)).failWith(HttpError.unauthorized("User does not have correct access keys to create System User in this network"))
    _ <- if (!systemUserNetworkLimitationIgnoreList.contains(requesterAcl.networkId)) {
      for {
        existingSystemUsers <- userRepository.getUsersCount(Set(SystemUsersFilter(network.id.toString)))
        networkMaxSystemUsers = network.maxSystemUsers.getOrElse(10)
        _ <- when(existingSystemUsers >= networkMaxSystemUsers).failWith(HttpError.forbidden(s"Number of system users exceeds network limit of $networkMaxSystemUsers"))
      } yield ()
    } else Future.successful(())

    clientCredentials <- oktaService.createApplication(s"${request.firstName} ${request.lastName}")
  } yield UserInfo(
    clientCredentials.userId, clientCredentials.clientSecret
  )

  private def insertUserIntoNetwork(
      requesterAcl: UserACL,
      request: UpsertUserRequest,
      network: Network,
      eventType: Option[UserDomainEvent],
  )(implicit traceId: TraceId): Future[(User, Option[String])] = {
    val loginMode = request.loginMode.getOrElse(network.loginMode)
    val userType = request.userType.getOrElse(UserType.Human)
    val isNewSystemUser = loginMode == LoginMode.ClientCredentials && userType == UserType.System && request.id.isEmpty
    for {
      clientCredentials <- if (isNewSystemUser) {
        createSystemUser(requesterAcl, request, network)
      } else {
        UserInfo(
          userId = request.id.getOrElse(UUID.randomUUID().toString),
          None
        ).successFuture
      }

      _ <- validateInsert(requesterAcl, request)
      maskedIds = config.externalTargets.map(target => generateMaskedIdForTarget(target))
      (distributorId, omsId, externalIds) <- legacyExternalIds(request.externalIds, request.networkId)
      now = LocalDateTime.now()
      customRoles = calculateCustomRoles(request.roles, request.customRoles.getOrElse(
        if (isNewSystemUser) {
          Set("DefaultApiUserRole")
        } else {
          Set.empty
        }
      ))

      eventInfo = EventInfo(
        eventType.getOrElse(UserDomainEvent.UserCreated),
        requesterAcl
      )
      baseUser = User(
        id = clientCredentials.userId,
        networkId = network.id,
        email = request.email.toLowerCase,
        firstName = request.firstName,
        lastName = request.lastName,
        distributorId = distributorId,
        omsId = omsId,
        tradewebEligible = request.tradewebEligible.getOrElse(false),
        regSEligible = request.regSEligible.getOrElse(false),
        roles = request.roles,
        createdAt = now,
        createdBy = requesterAcl.userId,
        updatedAt = now,
        updatedBy = requesterAcl.userId,
        emailSentAt = None,
        emailSentBy = None,
        lastVisitedAt = None,
        locations = request.locations.getOrElse(Set.empty),
        faNumbers = request.faNumbers.getOrElse(Set.empty),
        custodianFaNumbers = request.custodianFaNumbers.getOrElse(Set.empty),
        customRoles = customRoles,
        licenses = request.licenses.getOrElse(Set.empty),
        cusips = request.cusips.getOrElse(Set.empty),
        maskedIds = maskedIds,
        isActive = request.isActive.getOrElse(true),
        idpLoginId = User.buildIdpLoginId(network.networkCode, request.email, !config.multiNetworksExcluded.contains(network.id)),
        eventInfo = eventInfo,
        loginMode = loginMode,
        userType = userType,
        externalIds = externalIds,
        accountInContext = request.accountInContext,
        context = request.context,
        idpId = request.idpId,
        purviewLicenses = request.purviewLicenses.getOrElse(Set.empty),
        purviewNsccCodes = request.purviewNsccCodes.getOrElse(Set.empty),
        firmId = request.firmId.filterNot(_.isBlank),
        whiteLabelPartnerId = request.whiteLabelPartnerId.filterNot(_.isBlank),
        secondaryEmail = request.secondaryEmail.filterNot(_.isBlank),
        iCapitalUserId = request.iCapitalUserId.filterNot(_.isBlank),
        landingPage = request.landingPage,
        userSyncedAt =
          if (eventType.contains(UserDomainEvent.UserSynced)) Some(now)
          else None,
        groups = request.groups.map { case (k, v) => k.productPrefix -> v },
        icnGroups = request.icnGroups.getOrElse(Set.empty[String]),
        icnRoles = request.icnRoles.getOrElse(Set.empty[String]),
        mfas = request.mfas.map { case (k, v) => k.productPrefix -> v },
      )
      user = baseUser
        .resetEntitlements(getAcceptedAccessKeys)
        .updateEntitlements(u => LegacyUser.Entitlements(u.asLegacyUser))
      response <- insertUser(requesterAcl, user)
    } yield (response, clientCredentials.clientSecret)
  }

  override def handleInsertRequest(
      requesterAcl: UserACL,
      request: UpsertUserRequest,
      eventType: Option[UserDomainEvent] = None
  )(implicit traceId: TraceId): Future[(User, Option[String])] = {
    for {
      _ <- validateUpsertIntoWlpFirm(requesterAcl, request)
      _ <- validateUpsertCustodianFaNumbers(request)
      userAndSecret <- request.upsertField match {
        case Some(upsertField) =>
          log.info("Upserting a user")
          handleUpsertRequest(requesterAcl, request, upsertField, eventType: Option[UserDomainEvent])
        case None =>
          log.info("Inserting a new user")
          for {
            network <- networkService.getNetworkById(requesterAcl, request.networkId)
            userAndSecret <- insertUserIntoNetwork(requesterAcl, request, network, eventType)
          } yield userAndSecret
      }
    } yield userAndSecret
  }


  /**
   * Insert batch of users
   * <p>**Note that some error types are caught and returned while others fail the call
   *
   * @param requesterAcl caller
   * @param requests     creation requests
   * @param traceId      trace
   * @return created users and a list of caught failures
   */
  override def handleBulkUpsertRequest(requesterAcl: UserACL, requests: Seq[UpsertUserRequest])
    (implicit traceId: TraceId, mat: Materializer): Future[BatchUserUpsertResponse] = {
    val attempts: Future[Seq[Either[UserInsertFailure, User]]] = Source(requests.toList)
      .mapAsync(config.parallelism) { request =>
        handleInsertRequest(requesterAcl, request).map(userAndSecret => userAndSecret._1).transformWith {
          case Failure(Conflict(message, _)) =>
            Left(UserInsertFailure(request, StatusCodes.Conflict.intValue.toString, message)).successFuture
          case Failure(e@HttpError(_, StatusCodes.Conflict)) =>
            Left(UserInsertFailure(request, StatusCodes.Conflict.intValue.toString, e.getMessage)).successFuture
          case other => Future.fromTry(other).map(Right.apply)
        }
      }
      .runFold(Seq.empty[Either[UserInsertFailure, User]])((acc, u) => acc :+ u)

    attempts.map { eithers =>
      val (lefts, rights) = eithers.partition(_.isLeft)
      val success = rights.map(r => BatchUser.fromUser(r.right.get))
      BatchUserUpsertResponse(success, lefts.map(_.left.get))
    }
  }


  override def idFromEmail(email: String, networkId: NetworkId, requesterAcl: UserACL)
    (implicit traceId: TraceId): Future[String] = {
    log.info("Retrieve user id", email, networkId)
    val filters: Set[MongoFilter] = Set(EmailFilter(email), NetworkFilter(NetworkId.unwrap(networkId)))
    val entitlements = generateEntitlements(requesterAcl, EditCapabilities)
    userRepository
      .getUser(filters)(entitlements)
      .flattenOption("User not found")
      .map(_.id)
  }


  override def handleUpdateRequest(
      requesterAcl: UserACL,
      request: UpsertUserRequest,
      eventType: Option[UserDomainEvent] = None
  )(implicit traceId: TraceId): Future[User] = {
    log.trace(s"Updating user into network with id ${request.networkId}")
    for {
      _ <- validateUpsertCustodianFaNumbers(request)
      _ <- validateUpsertIntoWlpFirm(requesterAcl, request)
      userId <- request.id.map(_.successFuture).getOrElse(Future.failed(HttpError.badRequest("user id is missing")))
      user <- getUserById(requesterAcl, userId).flattenOption("User Not Found")
      _ <- validateUpdate(requesterAcl, request, user)
      network <- networkService.getNetworkById(requesterAcl, request.networkId)
      updatedUser <- updateUserInNetwork(requesterAcl, user, request, network, eventType = eventType)
    } yield updatedUser
  }

  private def handleUpsertRequest(
      requesterAcl: UserACL,
      request: UpsertUserRequest,
      upsertField: UpsertField,
      eventType: Option[UserDomainEvent],
  )(implicit traceId: TraceId): Future[(User, Option[String])] = {
    for {
      (userOption, uniqueUserId) <- getUserByUniqueUserId(requesterAcl, GetUniqueUserIdRequest(request), upsertField)
      result <- userOption match {
        case Some(user) =>
          //1. If User exists, update user
          log.info("[handleUpsertRequest] updating a user", uniqueUserId)
          for {
            _ <- validateUpdate(requesterAcl, request, user)
            network <- networkService.getNetworkById(requesterAcl, request.networkId)
            updatedUser <- updateUserInNetwork(requesterAcl, user, request, network, eventType)
              .map(updatedUser => (updatedUser, None))
          } yield updatedUser
        case None =>
          //2. If User does not exist, create user
          log.info("[handleUpsertRequest] inserting a new user", uniqueUserId)
          for {
            network <- networkService.getNetworkById(requesterAcl, request.networkId)
            userAndSecret <- insertUserIntoNetwork(requesterAcl, request, network, eventType)
          } yield userAndSecret
      }
    } yield result
  }

  override def handleOktaSyncRequest(requesterAcl: UserACL, request: OktaSyncRequest)
    (implicit traceId: TraceId): Future[OktaSyncResponse] = {
    val entitlements = generateEntitlements(requesterAcl, EditCapabilities)
    val userIds =
      if (request.syncAll.getOrElse(false)) {
        log.info("Sync all users to okta")
        userRepository.getUserIds(Set.empty)(entitlements)
      }
      else {
        val usersToSync = getUserIdsFromRequest(request, entitlements)
        usersToSync.map(users => log.info("Sync to okta", users))
        usersToSync
      }
    userIds
      .flatMap(ids => oktaSyncBus.putEvents(ids.toList.map(SingleUser), "ManualOktaSync"))
      .map(results => {
        val failedCount = results.map(_.getFailedEntryCount).reduce(_ + _)
        if (failedCount > 0) log.error("Put events failed", "results" -> results.mkString("\n"))
        failedCount
      })
      .map(i => OktaSyncResponse(i))
      .andThen { case Failure(ex) =>
        log.error("Manual okta sync failed", ex)
        ex
      }
  }

  private def getUserIdsFromRequest[T <: RequestWithOptUserIds with RequestWithOptNetworkIds](request: T,
      entitlements: Set[String])(implicit traceId: TraceId): Future[Set[String]] = {
    val usersFromNetworksField = request.networkIds.map { networkIds =>
      val filter = OrFilter(networkIds.map(id => NetworkFilter(id.toString)).toList: _*)
      userRepository.getUserIds(Set(filter))(entitlements)
    }.getOrElse(Future.successful(Set.empty))
    val usersFromUsersField = request.userIds.getOrElse(Set.empty)
    val usersToSync = usersFromNetworksField.map(_ ++ usersFromUsersField)
    usersToSync
  }

  override def handleBumpUsersRequest(requesterAcl: UserACL, request: BumpUsersVersionRequest)
    (implicit traceId: TraceId): Future[BumpUsersVersionResponse] = {
    val entitlements = generateEntitlements(requesterAcl, EditCapabilities)
    val userIdsFut = getUserIdsFromRequest(request, entitlements)
    userIdsFut
      .flatMap(userIds => userRepository.bumpVersions(userIds)(entitlements).map(BumpUsersVersionResponse))
  }

  def getUserByUniqueUserId(
      requesterAcl: UserACL,
      request: GetUniqueUserIdRequest,
      upsertField: UpsertField
  )(implicit traceId: TraceId): Future[(Option[User], UniqueUserId)] = {
    val entitlements = generateEntitlements(requesterAcl, ViewCapabilities)

    val matchedUpsertField = upsertField match {
      case UpsertField.Distributor =>
        legacyExternalIds(request.externalIds, request.networkId).flatMap {
          case (distributorId, _, _) =>
            distributorId match {
              case Some(distId) => Future.successful(Distributor(distId, request.networkId))
              case None => Future.failed(HttpError.badRequest("distributor is needed for Distributor upsert request"))
            }
        }
      case UpsertField.Oms =>
        legacyExternalIds(request.externalIds, request.networkId).flatMap {
          case (_, omsId, _) =>
            omsId match {
              case Some(omsIdValue) => Future.successful(Oms(omsIdValue))
              case None => Future.failed(HttpError.badRequest("oms is needed for Oms upsert request"))
            }
        }
      case UpsertField.Email =>
        Future.successful(Email(request.email, request.networkId))
      case UpsertField.UserId =>
        request.id match {
          case Some(userIdValue) => Future.successful(Guid(userIdValue))
          case None => Future.failed(HttpError.badRequest("email is needed for UserId upsert request"))
        }
      case UpsertField.ExternalId(subject) =>
        request.externalIds.find(_._1 == subject) match {
          case Some(id) =>
            Future.successful(UniqueUserId.External(ExternalId(id._1, id._2)))
          case None =>
            Future.failed(
              HttpError.badRequest(s"externalId subject=$subject is needed for ExternalId upsert request")
            )
        }
    }

    for {
      field <- matchedUpsertField
      user <- userRepository.getUserByUniqueId(field)(entitlements)
    } yield (user, field)
  }

  private def updateUserInNetwork(
      requesterAcl: UserACL,
      user: User,
      request: UpsertUserRequest,
      network: Network,
      eventType: Option[UserDomainEvent]
  )(implicit traceId: TraceId): Future[User] = {

    log.info("Handling updateUserRequest")

    for {
      _ <- validateEmailUpdate(user.email, request.email, request.networkId)
      (distributorId, omsId, externalIds) <- legacyExternalIds(request.externalIds, request.networkId)
      //v1 users supported implicit activation via PUT, so it is ported here
      shouldActivate = request.isActive.contains(true) && !user.isActive
      _ <- if (shouldActivate) activationCheck(user.email) else Future.unit
      baseUpdatedUser = user.copy(
        networkId = network.id,
        email = request.email.toLowerCase,
        firstName = request.firstName,
        lastName = request.lastName,
        distributorId = distributorId,
        omsId = omsId,
        tradewebEligible = request.tradewebEligible.getOrElse(user.tradewebEligible),
        regSEligible = request.regSEligible.getOrElse(user.regSEligible),
        isActive = request.isActive.getOrElse(user.isActive),
        roles = request.roles,
        locations = request.locations.getOrElse(user.locations),
        faNumbers = request.faNumbers.getOrElse(user.faNumbers),
        custodianFaNumbers = request.custodianFaNumbers.getOrElse(user.custodianFaNumbers),
        customRoles = calculateCustomRoles(request.roles, request.customRoles.getOrElse(user.customRoles)),
        licenses = request.licenses.getOrElse(user.licenses),
        cusips = request.cusips.getOrElse(user.cusips),
        idpLoginId = User.buildIdpLoginId(network.networkCode, request.email, !config.multiNetworksExcluded.contains(user.networkId)),
        loginMode = request.loginMode.getOrElse(user.loginMode),
        userType = request.userType.getOrElse(user.userType),
        externalIds = externalIds,
        accountInContext = request.accountInContext,
        context = request.context,
        purviewLicenses = request.purviewLicenses.getOrElse(user.purviewLicenses),
        purviewNsccCodes = request.purviewNsccCodes.getOrElse(user.purviewNsccCodes),
        eventInfo =
          if (shouldActivate) EventInfo(UserDomainEvent.UserActivatedDontSendEmail, requesterAcl)
          else EventInfo(eventType.getOrElse(UserDomainEvent.UserUpdated), requesterAcl),
        firmId = request.firmId.filterNot(_.isBlank),
        whiteLabelPartnerId = request.whiteLabelPartnerId.filterNot(_.isBlank),
        secondaryEmail = request.secondaryEmail.filterNot(_.isBlank),
        iCapitalUserId = request.iCapitalUserId.filterNot(_.isBlank),
        landingPage = request.landingPage,
        userSyncedAt =
          if (eventType.contains(UserDomainEvent.UserSynced) && user.userSyncedAt.isEmpty) Some(LocalDateTime.now)
          else user.userSyncedAt,
        groups = request.groups.map { case (k, v) => k.productPrefix -> v },
        icnGroups = request.icnGroups.getOrElse(user.icnGroups),
        icnRoles = request.icnRoles.getOrElse(user.icnRoles),
        mfas = request.mfas.map { case (k, v) => k.productPrefix -> v },
      )
      updatedUser = baseUpdatedUser
        .resetEntitlements(getAcceptedAccessKeys)
        .updateEntitlements(u => LegacyUser.Entitlements(u.asLegacyUser))

      finalUser <-
        if (updatedUser != user) updateUser(requesterAcl, updatedUser.refreshUpdatedFields(requesterAcl.userId))
        else {
          log.info("No data changed, update skipped", "userId" -> user.id)
          Future.successful(user)
        }
    } yield finalUser
  }

  private case class UserWithNetwork(user: User, network: Network)

  override def partiallyUpdateUser(requester: UserACL, updateRequests: Seq[PartialUserUpdate])
    (implicit traceId: TraceId, mat: Materializer): Future[Seq[PartialUserUpdateError]] = {
    val source: Source[PartialUserUpdateError, NotUsed] = Source.fromIterator(() => updateRequests.iterator)
      .mapAsync[Either[HttpError, User]](config.parallelism)(getValidateAndUpdateUser(requester)(_))
      .mapAsync[Either[HttpError, UserWithNetwork]](config.parallelism)({
        case Left(err) => Future.successful(Left(err))
        case Right(updatedUser: User) =>
          log.info(s"Network lookup for  ${updatedUser.id}")
          networkService.getNetworkById(requester, updatedUser.networkId).map {
            case network: Network => Right(UserWithNetwork(updatedUser, network))
            case _ => Left(HttpError.notFound(s"Cannot find network for user: ${updatedUser.user}"))
          }
      })
      .mapAsync[Either[HttpError, User]](config.parallelism)({
        case Left(err) => Future.successful(Left(err))
        case Right(userWithNetwork: UserWithNetwork) =>
          val updatedUser = userWithNetwork.user
          log.info(s"Updating user", updatedUser.id)
          updateUser(requester, user = updatedUser).transformWith {
            case Failure(HttpError(response, status)) =>
              Future.successful(Left(HttpError(response, status)))
            case Success(user) =>
              Future.successful(Right(user))
            case Failure(exception) =>
              Future.failed(exception)
          }
      })
      .collectType[Left[HttpError, User]]
      .map((error: Left[HttpError, User]) => PartialUserUpdateError(error.value.payload))

    source.runWith(Sink.seq[PartialUserUpdateError])
  }

  private def getValidateAndUpdateUser(requester: UserACL)(update: PartialUserUpdate)(implicit traceId: TraceId) = {

    val entitlements = generateEntitlements(requester, UploadCapabilities)
    val uniqueIdFut: Future[UniqueUserId] = UniqueUserId.unsafeApply(
      update.userIdType,
      update.userId,
      update.externalId,
      update.networkId.orElse(requester.networkId.some)
    ) match {
      case Left(value) => HttpError.badRequest(value.message).future
      case Right(value) => Future.successful(value)
    }

    for {
      uniqueId <- uniqueIdFut
      _ = log.info(s"looking for user with id $uniqueId")
      userOpt <- userRepository.getUserByUniqueId(uniqueId)(entitlements)
      _ <- (userOpt, update) match {
        case (Some(user), e: EmailUpdateRequest) => validateEmailUpdate(user.email, e.email, user.networkId)
        case _ => Future.unit
      }
    } yield userOpt match {
      case Some(user) => Right(
        applyPartialUserUpdate(requester, user, update)
          .resetEntitlements(getAcceptedAccessKeys)
          .updateEntitlements(u => LegacyUser.Entitlements(u.asLegacyUser))
          .refreshUpdatedFields(requester.userId)
      )
      case None => Left(HttpError.notFound(s"User $uniqueId not found"))
    }
  }

  private def applyPartialUserUpdate(requester: UserACL, user: User, update: PartialUserUpdate)
    (implicit traceId: TraceId): User = {
    val infoHelper = (t: UserDomainEvent) => EventInfo(eventType = t, triggeredBy = requester.userId, correlationId = traceId)
    update match {
      case u: LicensesUpdateRequest => user.copy(licenses = u.licenseSet,
        eventInfo = infoHelper(UserDomainEvent.UserLicensesUpdated))
      case u: FANumbersUpdateRequest => user.copy(faNumbers = u.faNumbersSet,
        eventInfo = infoHelper(UserDomainEvent.UserFaNumbersUpdated))
      case e: EmailUpdateRequest => user.copy(email = e.email,
        eventInfo = infoHelper(UserDomainEvent.UserEmailUpdated))
      case a: AccountUpdateRequest => user.copy(accountInContext = Some(a.account),
        eventInfo = infoHelper(UserDomainEvent.UserAccountInContextUpdated))
      case l: LocationsUpdateRequest => user.copy(locations = l.locationsSet,
        eventInfo = infoHelper(UserDomainEvent.UserLocationsUpdated))
      case u: ExternalIdsUpdateRequest => user.copy(externalIds = u.externalIds,
        eventInfo = infoHelper(UserDomainEvent.UserExternalIdsUpdated))
      case c: CusipsUpdateRequest => user.copy(cusips = c.cusipsSet,
        eventInfo = infoHelper(UserDomainEvent.UserCusipsUpdated))
    }
  }

  private def validateEmailUpdate(
      currentUserEmail: String, requestEmail: String, requestNetwork: NetworkId
  )(implicit traceId: TraceId): Future[Unit] =
    if (currentUserEmail.equalsIgnoreCase(requestEmail)) Future.unit
    else
      userRepository.existsByEmailAndNetwork(requestEmail, requestNetwork).flatMap(exists =>
        when(exists).failWith(HttpError.conflict("User already exists with the same email and network"))
      )

  private[service] def validateUpdate(requesterAcl: UserACL, request: UpsertUserRequest, user: User)
    (implicit traceId: TraceId, ec: ExecutionContext): Future[Unit] = {
    val isNetworksMatch = request.networkId == user.networkId
    val isSuperAdmin = requesterAcl.isAdmin
    val isRequesterNetworkSwitchingAllowed = requesterAcl.capabilities.intersect(ViewUISwitchUsersNetworkCapabilities.toCapabilityNames).nonEmpty
    val isInternalEmail = config.internalDomains.exists(domain => user.email.matches(s".*@$domain"))
    val isSpecialistAdmin = isRequesterNetworkSwitchingAllowed && isInternalEmail
    val allowed = isNetworksMatch || isSuperAdmin || isSpecialistAdmin
    val canChangePreviewUserRoles = isSuperAdmin || isSpecialistAdmin
    val rolesChanged = request.roles != user.roles || !request.customRoles.contains(user.customRoles)
    val isNewPreviewUser = request.userType.contains(UserType.Preview) && !user.isPreviewUser
    val hasPreviewUserTypeChanged = !request.userType.contains(UserType.Preview) && user.isPreviewUser
    val hasEmailChanged = request.email != user.email
    val isImpersonatedFut =
      if (!isNetworksMatch)
        impersonationService.getByImpersonatedUserId(user.id)(traceId, systemAcl).map { impersonationSession =>
          impersonationSession.exists(_.status == ImpersonationStatus.Pending)
        }
      else Future.successful(false)
    for {
      isImpersonated <- isImpersonatedFut
      _ <- when(isImpersonated).failWith(HttpError.badRequest("Cannot change user network while being impersonated"))
      _ <- whenNot(allowed).failWith(HttpError.forbidden("User does not have proper permissions to switch user network"))
      _ <- if (isNewPreviewUser) validateInsertPreviewUser(request, isSuperAdmin) else Future.unit
      _ <- if (!isNewPreviewUser && user.isPreviewUser) validateUpdatePreviewUser(isNetworksMatch, rolesChanged, canChangePreviewUserRoles, hasEmailChanged, hasPreviewUserTypeChanged) else Future.unit
    } yield ()
  }

  private def validateUpdatePreviewUser(
      isNetworkMatch: Boolean,
      rolesChanged: Boolean,
      canChangePreviewUserRoles: Boolean,
      hasEmailChanged: Boolean,
      hasPreviewUserTypeChanged: Boolean
  ): Future[Unit] = {
    if (hasEmailChanged) {
      Future.failed(HttpError.forbidden("It is forbidden to change Preview User email"))
    } else if (!isNetworkMatch) {
      Future.failed(HttpError.forbidden("It is forbidden to change Preview User network"))
    } else if (rolesChanged && !canChangePreviewUserRoles) {
      Future.failed(HttpError.unauthorized("Only admin users can change Preview User roles"))
    } else if (hasPreviewUserTypeChanged) {
      Future.failed(HttpError.forbidden("It is forbidden to change Preview User UserType"))
    }
    else {
      Future.unit
    }
  }

  /**
   * Allow `distributor` or `oms` id keys to be passed in and picked up as such.
   * Validate that external ids in request are distributor, oms, or allowed for network
   *
   * @return tuple of (distributorId, omsId, externalIds)
   */
  private[service] def legacyExternalIds(externalIds: Map[String, String], networkId: NetworkId)
    (implicit traceId: TraceId): Future[(Option[String], Option[String], Seq[ExternalId])] = {

    val distributorId = externalIds.get(DistributorKey)
    val omsId = externalIds.get(OmsKey)
    val externalIdsOut = ExternalId
      .fromStringMap(externalIds.filterKeys(k => !Set(DistributorKey, OmsKey).contains(k)))
    val externalIdKeys = externalIdsOut.map(_.subject).toSet

    val validatedExternalIds = {
      if (externalIdKeys.isEmpty) Future(Seq.empty)
      else for {
        externalIdsForNetwork <- externalIdTypeRepo.getByNetwork(networkId)
        externalIdNamesForNetwork = externalIdsForNetwork.map(_.name).toSet
        //validate that ids are in allowable set based on network
        _ <- if (externalIdKeys.subsetOf(externalIdNamesForNetwork)) Future.unit
        else {
          val disallowedExternalIds = externalIdKeys -- externalIdNamesForNetwork
          log.error("Attempted to upsert user with disallowed id types", disallowedExternalIds, networkId)
          HttpError.badRequest(s"External id types not allowed: $disallowedExternalIds").failedFuture
        }
      } yield externalIdsOut
    }

    validatedExternalIds.map((distributorId, omsId, _))
  }

  /**
   * validates the request based on domain logic
   */
  private def validateInsert(requesterAcl: UserACL, request: UpsertUserRequest)
    (implicit traceId: TraceId): Future[Unit] = {
    for {
      emailNetworkConflict <- userRepository.existsByEmailAndNetwork(request.email, request.networkId)
      _ <- when(emailNetworkConflict).failWith(HttpError.conflict("User already exists with the same email and network"))
      idConflict <- request.id.map(userRepository.exists).getOrElse(Future.successful(false))
      _ <- when(idConflict).failWith(HttpError.conflict("UserId already exists"))
      _ <- if (request.userType.contains(UserType.Preview)) validateInsertPreviewUser(request, requesterAcl.isAdmin) else Future.unit
    } yield ()
  }

  //TODO: might need to change isAdmin to something else when SpecialistAdmin and SuperAdmin gets added
  private def validateInsertPreviewUser(request: UpsertUserRequest, isAdmin: Boolean): Future[Unit] = {
    val isEmailValid = isValidPreviewUserEmail(request.email)
    if (!isAdmin) {
      Future.failed(HttpError.unauthorized("User does not have right access keys to create Preview User"))
    } else if (!isEmailValid) {
      Future.failed(HttpError.badRequest("Preview user email was not formatted correctly, needs to be preview[Number]@icapitalnetwork.com"))
    } else {
      Future.unit
    }
  }

  private def isValidPreviewUserEmail(email: String): Boolean = {
    val pattern = """^preview\d*@icapitalnetwork\.com$""".r
    pattern.findFirstMatchIn(email).isDefined
  }

  private def calculateCustomRoles(roles: Set[UserRole], customRoles: Set[String]): Set[String] = {
    //Copy over all the roles except for Super Admin into custom roles
    //A lot of code in old services depends on user.isAdmin and for that UserRole.EqPIPGGSAdmin is needed but we
    //do not want to give these users EqPipgGSAdmin custom role.
    customRoles ++ roles.filterNot(_ == UserRole.EqPIPGGSAdmin).map(_.name)
  }

  private def generateEntitlements(userACL: UserACL, capabilities: Set[String]) = {
    availableAccessKeysGen.getAvailableAccessKeysForCapabilities(capabilities, userACL)
  }

  private def handleApplicationSecretRequest[Resp](
      requesterAcl: UserACL,
      capabilities: Set[String],
      applicationId: String,
      action: () => Future[Resp],
  )(implicit traceId: TraceId): Future[Resp] = for {
    capabilities <- generateEntitlements(requesterAcl, capabilities).successFuture
    foundUsers <- userRepository.getUsersByIds(Set(applicationId))(capabilities)
    _ <- when(foundUsers.isEmpty)
      .failWith(HttpError.notFound(s"$applicationId does not exist or user does not have access"))
    result <- action()
  } yield result

  override def listApplicationSecrets(requesterAcl: UserACL, appId: String)
    (implicit traceId: TraceId): Future[List[GetOAuthSecretResponse]] =
    handleApplicationSecretRequest(
      requesterAcl,
      ViewSystemUsersCapabilities,
      appId,
      () => oktaService.listApplicationSecrets(appId)
    )

  override def createApplicationSecret(requesterAcl: UserACL, appId: String)
    (implicit traceId: TraceId): Future[CreateOAuthSecretResponse] =
    handleApplicationSecretRequest(
      requesterAcl,
      ManageSystemUsersCapabilities,
      appId,
      () => oktaService.createApplicationSecret(appId)
    )

  override def activateApplicationSecret(requesterAcl: UserACL, appId: String, secretId: String)
    (implicit traceId: TraceId): Future[GetOAuthSecretResponse] =
    handleApplicationSecretRequest(
      requesterAcl,
      ManageSystemUsersCapabilities,
      appId,
      () => oktaService.activateApplicationSecret(appId, secretId)
    )

  override def deactivateApplicationSecret(requesterAcl: UserACL, appId: String, secretId: String)
    (implicit traceId: TraceId): Future[GetOAuthSecretResponse] =
    handleApplicationSecretRequest(
      requesterAcl,
      ManageSystemUsersCapabilities,
      appId,
      () => oktaService.deactivateApplicationSecret(appId, secretId)
    )
}
