package com.simonmarkets.users.service

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.kafka.ZConsumer
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.common.clients.usersync._
import com.simonmarkets.networks.common.service.networkservice.BasicNetworkService
import com.simonmarkets.okta.domain.{Factor, FactorType}
import com.simonmarkets.users.common.api.request.{UpsertUserRequest, GetUniqueUserIdRequest}
import com.simonmarkets.users.common.{LoginMode, UpsertField, User, UserDomainEvent}
import com.simonmarkets.users.domain.{IcnUserEvent, SyncError, TransientState}
import com.simonmarkets.users.repository.TransientStateRepository
import org.apache.kafka.common.header.Header
import org.apache.kafka.common.header.internals.RecordHeader
import simon.Id.NetworkId
import zio._

import java.time.Instant

trait UserEventConsumer {

  def sync(record: ZConsumer.Record[String, IcnUserEvent])(implicit traceId: TraceId): IO[SyncError, User]

  final def consume: UserEventConsumer.ConsumerFn = record =>
    ZIO.logAnnotate("offset", record.offset.toString) {
      FiberRef
        .currentLogAnnotations
        .getWith { annotations =>
          implicit val traceId: TraceId = TraceId(annotations("traceId"))
          sync(record).foldZIO(
            {
              case skip: SyncError.Skipped => ZIO.log("Processed ICN user event, skipped: " + skip.getMessage)
              case e => ZIO.logError("Processed ICN user event, error: " + e.getMessage)
            },
            synced =>
              ZIO.log(
                "Processed ICN user event, success: " +
                  s"icnUserId=${record.value.user_id} " +
                  s"userId=${synced.id} " +
                  s"whiteLabelPartnerId=${record.value.white_label_partner_id} " +
                  s"firmId=${record.value.firm_id} " +
                  record.value.firm_id.fold("")(f => s"firmId=$f ") +
                  s"networkId=${synced.networkId}"
              )
          )
        }
    }

}

object UserEventConsumer {

  val consume: ZIO[UserEventConsumer, Nothing, ConsumerFn] = ZIO.serviceWith[UserEventConsumer](_.consume)

  lazy val live = ZLayer.fromFunction(apply _)

  def apply(
      mappings: UserSyncMappingClient,
      networks: BasicNetworkService,
      users: UserService,
      transientState: TransientStateRepository,
      systemUser: UserACL
  ): UserEventConsumer = V2(mappings, networks, users, transientState, systemUser)

  private type ConsumerFn = ZConsumer.Record[String, IcnUserEvent] => UIO[Unit]

  private[users] val ICN_SOURCE_HEADER = new RecordHeader("source", "icn".getBytes)
  private[users] val VERSION_HEADER = new RecordHeader("version", "1.0".getBytes)
  private[users] val CREATED_TYPE_HEADER = new RecordHeader("type", "user_created".getBytes)
  private[users] val UPDATED_TYPE_HEADER = new RecordHeader("type", "user_updated".getBytes)

  private final case class V2(
      mappings: UserSyncMappingClient,
      networks: BasicNetworkService,
      users: UserService,
      transientState: TransientStateRepository,
      systemUser: UserACL
  ) extends UserEventConsumer {

    def sync(record: ZConsumer.Record[String, IcnUserEvent])(implicit traceId: TraceId): IO[SyncError, User] =
      for {
        _ <- verify(record.headers.toSet)
        mapping <- getMapping(record.value.white_label_partner_id, record.value.firm_id).mapError {
          case SyncError.NotFound(msg) => SyncError.Skipped(record.value.user_id.toString, msg)
          case e => e
        }
        network <-
          ZIO
            .fromFuture(_ => networks.unentitledGetNetworkById(NetworkId(mapping.destination_primary.external_id)))
            .flatMapError(
              SyncError.fromThrowable(s"getting network failed id=${mapping.destination_primary.external_id}")
            )
        user <- upsert(record.value, mapping, network.id)
      } yield user

    /**
     * Expected headers:
     *   - `source = "icn"`
     *   - `version = "1.0"`
     *   - either `type = "user_created"` OR `type = "user_updated"`
     */
    private def verify(headers: Set[Header]): IO[SyncError, Unit] =
      ZIO
        .fail(
          SyncError.NotFound(
            "missing required headers, found=" +
              headers
                .map(h => "(key=\"" + h.key + "\", value=\"" + new String(h.value) + "\")")
                .mkString("[", ", ", "]") // `[(key="h1", value="v1"), (key="h2", value="v2")]`
          )
        )
        .unless(
          headers.size >= 3 &&
            headers.contains(ICN_SOURCE_HEADER) &&
            headers.contains(VERSION_HEADER) &&
            (headers.contains(UPDATED_TYPE_HEADER) || headers.contains(CREATED_TYPE_HEADER))
        )
        .unit

    private def getMapping(
        whiteLabelPartnerId: Int,
        firmId: Option[Int]
    )(implicit
        traceId: TraceId
    ): IO[SyncError, UserNetworkMappingResponse] =
      ZIO
        .fromFuture(_ =>
          mappings.get(
            UserNetworkMappingRequest(
              sourceSystem = SourceDestinationSystem.ICN,
              destinationSystem = SourceDestinationSystem.SIMON,
              sourcePrimaryId = whiteLabelPartnerId.toString,
              sourcePrimaryIdKind = SourceDestinationPrimaryIdKind.`ICN_WHITE_LABEL`,
              sourceSecondaryId = firmId.map(_.toString)
            )
          )
        )
        .flatMapError(
          SyncError.fromThrowable(s"getting mapping for whiteLabelPartnerId=$whiteLabelPartnerId firmId=$firmId failed")
        )
        .someOrFail(SyncError.NotFound(s"mapping whiteLabelPartnerId=$whiteLabelPartnerId firmId=$firmId"))

    private def upsert(
        event: IcnUserEvent,
        mapping: UserNetworkMappingResponse,
        networkId: NetworkId
    )(implicit
        traceId: TraceId
    ): IO[SyncError, User] =
      for {
        upsertField <-
          if (event.simon_user_id.isDefined)
            ZIO.succeed(UpsertField.UserId)
          else
            mapping.destination_property_matcher.primary match {
              case MatchByPropertyName.email => ZIO.succeed(UpsertField.Email)
              case MatchByPropertyName.distributor_id => ZIO.succeed(UpsertField.Distributor)
              case MatchByPropertyName.external_id =>
                ZIO
                  .fromOption(mapping.destination_property_matcher.secondary)
                  .mapBoth(
                    _ => SyncError.NotFound("expected subject in secondary property matcher to match by external id"),
                    UpsertField.ExternalId.apply
                  )
            }
        // if matching on external id, icn user must have an external id with the same subject required by the mapping
        externalId <-
          upsertField match {
            case UpsertField.ExternalId(subject) =>
              ZIO
                .succeed(event.simonExternalIds.find(_.subject == subject))
                .tapSome { case None =>
                  ZIO.fail(SyncError.NotFound(s"expected external id with subject=$subject in user, but got None"))
                }
            case _ => ZIO.none
          }
        user <-
          ZIO
            .fromFuture { _ =>
              users.getUserByUniqueUserId(
                systemUser,
                GetUniqueUserIdRequest(
                  id = event.simon_user_id,
                  email = event.email,
                  externalIds = ExternalId.toStringMap(externalId.toSeq),
                  networkId = networkId
                ),
                upsertField
              )
            }
            .flatMapError(SyncError.fromThrowable(s"getting user id=${event.user_id}"))
            .map(_._1)
            .tapSome { // ensure that we found a matching simon user when simon id defined on the icn user
              case None if event.simon_user_id.isDefined =>
                ZIO.fail(
                  SyncError.NotFound(s"incorrect SIMON id=${event.simon_user_id} on ICN user id=${event.user_id}")
                )
            }
        base = user match {
          case Some(u) =>
            val locations =
              mapping
                .destination_secondary
                .foldLeft(u.locations) { case (locations, dest) =>
                  /*
                   * FIXME
                   *  The intention here is:
                   *    if dest.external_id (mapping location) not exists in user's locations
                   *      add to beginning of user's locations
                   *    else if mapping location exists
                   *      move to beginning of user's locations
                   *  but `Set` doesn't maintain insertion order, so there is no guarantee that the mapping location
                   *  will be at the head of the set; i.e. this may not work if a user has more than one location.
                   */
                  if (!locations.contains(dest.external_id))
                    Set(dest.external_id) ++ locations
                  else
                    locations.partition(_ == dest.external_id) match {
                      case (matched, missed) => matched ++ missed
                    }
                }
            UpsertUserRequest.fromUser(u).copy(locations = Some(locations))
          case None =>
            UpsertUserRequest(
              id = event.simon_user_id,
              networkId = networkId,
              email = event.email,
              firstName = event.first_name,
              lastName = event.last_name,
              roles = Set.empty,
              loginMode = Some(LoginMode.ICNUsernamePassword),
              locations = mapping.destination_secondary.map(s => Set(s.external_id)),
              tradewebEligible = None,
              regSEligible = None,
              externalIds = Map.empty
            )
        }
        patch =
          mapping
            .fields
            .foldLeft(base) { case (req, field) =>
              field match {
                case SyncField.email => req.copy(email = event.email)
                case SyncField.external_ids => req.copy(externalIds = req.externalIds ++ event.simonExternalIdMap)
                case SyncField.first_name => req.copy(firstName = event.first_name)
                case SyncField.icn_groups => req.copy(icnGroups = event.user_groups.map(_.map(_.toString)))
                case SyncField.icn_roles => req.copy(icnRoles = event.user_roles.map(_.map(_.toString)))
                case SyncField.last_name => req.copy(lastName = event.last_name)
                case SyncField.phone =>
                  event
                    .phone
                    .fold(req) { p =>
                      // if user not already registered for MFA through SMS, add new MFA method with incoming phone
                      if (!req.mfas.get(FactorType.sms).exists(_.isFactorEnrolled))
                        req.copy(mfas = Map(FactorType.sms -> Factor(Some(p))))
                      else
                        req
                    }
                case SyncField.password => req //handled as side effect instead
                case SyncField.EnumNotFound => req
              }
            }
        request = {
          val icnGroups = event.user_groups.fold(Set.empty[String])(_.map(_.toString))
          val customRoles = // current roles -- mapped roles ++ incoming roles
            user.map(_.customRoles).getOrElse(Set.empty) --
              mapping.entitlements.map(_.destination_entitlement) ++
              mapping
                .entitlements
                .collect {
                  case entitlement if icnGroups.contains(entitlement.source_entitlement) =>
                    entitlement.destination_entitlement
                }
          patch.copy(
            customRoles = if (customRoles.nonEmpty) Some(customRoles) else None,
            firmId = event.firm_id.map(_.toString),
            whiteLabelPartnerId = Some(event.white_label_partner_id.toString),
            iCapitalUserId = Some(event.user_id.toString),
            upsertField = Some(upsertField)
          )
        }
        u <-
          ZIO
            .fromFuture(_ => users.handleInsertRequest(systemUser, request, Some(UserDomainEvent.UserSynced)))
            .flatMapError(SyncError.fromThrowable(s"upserting user icnUserId=${event.user_id} failed"))
            .map(_._1)
        //this is a race condition that we are living with for now
        _ <- ZIO
          .when(mapping.fields.contains(SyncField.password)) {
            upsertIcnPassword(u, event.encrypted_password)
          }
      } yield u


    private def upsertIcnPassword(user: User, password: Option[String])
      (implicit traceId: TraceId): IO[SyncError, Unit] = {
      val ignoredLoginModes = Set(LoginMode.UnifiedPassword, LoginMode.SSO)
      if (password.isDefined && password.get.nonEmpty && !ignoredLoginModes.contains(user.loginMode)) {
        val state = TransientState(user.id, password.get, Instant.now())
        ZIO
          .fromFuture(implicit ec => transientState.upsert(state))
          .zipLeft(ZIO.logInfo(s"[User Sync] Password stored traceId=$traceId"))
          .flatMapError(SyncError.fromThrowable("[User Sync] could not insert ICN Hashed Password"))
      }
      else ZIO.unit
    }
  }

}

