package com.simonmarkets.users.service

import com.goldmansachs.marquee.pipg.{EmbeddingInfo, SSOPrefix}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.Network
import com.simonmarkets.users.common.{LandingPage, LoginMode, User}

case class UserRef(
    login: String,
    networkName: Option[String],
    ssoPrefix: Option[SSOPrefix],
    embeddingInfo: Option[EmbeddingInfo],
    loginMode: LoginMode,
    landingPage: LandingPage = LandingPage.SIMON
)

object UserRef extends TraceLogging {

  val UnknownUserNetworkCodes = Array("CE4w","Jr9I","cxpN","lhUp","juPL","LQHK","BKB5","Mi7m","y3AN","b0o3","h8RS","YOwJ","4ng9","11G9","fg1z","yZ8z","YsLp","djxB","oqjp","SGb9","baAI")

  private def determineLoginMode(user: User, network: Network)(implicit traceId: TraceId): LoginMode = {
    user.loginMode match {
      case LoginMode.UsernamePassword => LoginMode.UsernamePassword
      case LoginMode.SSOAndUsernamePassword =>
        if(network.ssoPrefix.isDefined) {
          LoginMode.SSOAndUsernamePassword
        } else {
          log.alert("SSO prefix not set even though the login mode supports it ", user.email, user.loginMode)
          LoginMode.UsernamePassword
        }
      case LoginMode.SSO =>
        if(network.ssoPrefix.isDefined) {
          LoginMode.SSO
        } else {
          log.alert("SSO prefix not set even though the login mode supports it ", user.email, user.loginMode)
          LoginMode.UsernamePassword
        }
      case LoginMode.Embedded =>
        if(network.embeddingInfo.isDefined) {
          LoginMode.Embedded
        } else {
          log.alert("Embedding info not set even though the login mode supports it ", user.email, user.loginMode)
          LoginMode.UsernamePassword
        }
      case LoginMode.ClientCredentials =>
        log.alert("A login attempt is made using a user that has login mode of client credentials", user.email)
        LoginMode.UsernamePassword
      case LoginMode.ICNUsernamePassword => LoginMode.ICNUsernamePassword
      case LoginMode.SSOAndICNUsernamePassword =>
        if(network.ssoPrefix.isDefined) {
          LoginMode.SSOAndICNUsernamePassword
        } else {
          log.alert("SSO prefix not set even though the login mode supports it ", user.email, user.loginMode)
          LoginMode.ICNUsernamePassword
        }
      case LoginMode.UnifiedPassword => LoginMode.UnifiedPassword
    }
  }



  def fromUserAndNetwork(user: User, network: Network, setNetworkName: Boolean, landingPage: Option[LandingPage])(implicit traceId: TraceId): UserRef = {
    val actualLoginMode = determineLoginMode(user, network)
    UserRef(
      login = user.idpLoginId,
      networkName = if (setNetworkName) Some(network.name) else None,
      ssoPrefix = if (actualLoginMode == LoginMode.UsernamePassword) None else network.ssoPrefix,
      embeddingInfo = if (actualLoginMode == LoginMode.UsernamePassword) None else network.embeddingInfo,
      loginMode = actualLoginMode,
      landingPage = landingPage.getOrElse(LandingPage.EnumNotFound)
    )
  }


  def default(email: String, loginMode: Option[LoginMode], landingPage: Option[LandingPage]): UserRef = UserRef(
      login = getLogin(email),
      networkName = None,
      ssoPrefix = None,
      embeddingInfo = None,
      loginMode = loginMode.getOrElse(LoginMode.UsernamePassword),
      landingPage = landingPage.getOrElse(LandingPage.SIMON)
    )

  /**
   * The login is always in the following format <networkcode>-<email> so for a user that does
   * not exist we are doing the same.
   */
  private def getLogin(email:String): String = {
    val hashCode = Math.abs(email.hashCode)
    val index = hashCode % UnknownUserNetworkCodes.length
    val code = UnknownUserNetworkCodes(index)
    s"$code-$email"
  }
}
