package com.simonmarkets.users.service

import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.Network
import com.simonmarkets.networks.common.repository.NetworkRepository
import com.simonmarkets.users.api.response.MasterUser
import com.simonmarkets.users.common.User
import com.simonmarkets.users.config.DomainConfig
import com.simonmarkets.users.repository.UserRepository
import com.simonmarkets.users.repository.filters.EmailFilter
import simon.Id.NetworkId

import scala.concurrent.{ExecutionContext, Future}

class MasterUserService(
    userRepo: UserRepository,
    networkRepo: NetworkRepository,
    domainMappings: List[DomainConfig]
)(implicit ec: ExecutionContext) extends TraceLogging {

  private val admin: Set[String] = Set(Capabilities.Admin)

  def getMasterUser(email: String, wlpId: Option[Int], hostOpt: Option[String] = None)
    (implicit traceId: TraceId): Future[MasterUser] = {
    log.info("Getting master user",
      "email" -> email,
      "wlpId" -> wlpId.getOrElse("N/A"),
      "host" -> hostOpt.getOrElse("N/A")
    )

    for {
      usersWithSameEmail <- userRepo.getUsers(Set(EmailFilter(email)))(admin)
      filteredUsersWithSameEmail = usersWithSameEmail.filter(user => !user.isPreviewUser)
      _ = log.info(
        s"Retrieved users for given email",
        "count" -> filteredUsersWithSameEmail.size,
        "ids" -> filteredUsersWithSameEmail.map(_.id)
      )
      networks <- networkRepo.getByIds(filteredUsersWithSameEmail.map(_.networkId).toSet)(admin)
      _ = log.info(s"Retrieved networks for found users",
        "count" -> networks.size,
        "ids" -> filteredUsersWithSameEmail.map(_.id)
      )
      masterUser = createMasterUser(email, filteredUsersWithSameEmail, networks, hostOpt, wlpId)
    } yield masterUser
  }

  private def createMasterUser(
      email: String,
      usersWithSameEmail: List[User],
      networks: List[Network],
      hostOpt: Option[String],
      wlpId: Option[Int]
  )(implicit traceId: TraceId): MasterUser = {
    val domainConfig = hostOpt.flatMap(host => domainMappings.find(_.isDomainMatch(host)))
    val filterUsers: Seq[User] =
      usersWithSameEmail
        .filter(_.isActive)
        .filter(u => passportable(u, hostOpt) || wlpId.isEmpty || wlpId.map(_.toString) == u.whiteLabelPartnerId)
        .filter(u => domainConfig.isEmpty || domainConfig.exists(_.loginModes.contains(u.loginMode)))
    val returnNetworkName = filterUsers.size > 1 //Do not return network name if only one profile exists. So we avoid network name discovery
    if (filterUsers.nonEmpty) {
      val networkById: Map[NetworkId, List[Network]] = networks.groupBy(_.id)
      val userRefs: Seq[UserRef] = filterUsers.map { user =>
        val networkOpt = networkById(user.networkId).headOption
        val landingPageOpt = networkOpt.flatMap { network =>
          user.landingPage
            .orElse(network.landingPage)
            .orElse(domainConfig.map(_.landingPage))
        }
        UserRef.fromUserAndNetwork(user, networkById(user.networkId).head, setNetworkName = returnNetworkName, landingPage = landingPageOpt)
      }
      MasterUser(email, userRefs.toList)
    } else {
      log.error("An attempt to login with an email that does not exist", email)
      MasterUser(email, List(UserRef.default(email, domainConfig.map(_.defaultLoginMode), domainConfig.map(_.landingPage))))
    }
  }

  private def passportable(
      user: User,
      host: Option[String],
  ): Boolean = {
    val passporting = for {
      h <- host
      wlp <- PassportService.wlpFromDomain(h)
    } yield user.passport.keySet(wlp)
    passporting.getOrElse(false)
  }

}
