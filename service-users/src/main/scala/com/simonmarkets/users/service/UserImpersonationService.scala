package com.simonmarkets.users.service
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.CanImpersonateStatus._
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.UserImpersonationCapabilities.{AdminUserImpersonationCapabilities, ApproveUserImpersonationCapabilities, UpdateUserImpersonationCapabilities, ViewUserImpersonationCapabilities}
import com.simonmarkets.capabilities.UsersCapabilities.{ImpersonateCapabilities, ImpersonateWithApprovalCapabilities}
import com.simonmarkets.capabilities.{CanImpersonateStatus, NetworksCapabilities, UserImpersonationCapabilities, UsersCapabilities}
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.NetworkIds.AdminNetworkId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.networks.common.clients.emailsender.{EmailRequest, EmailSenderClient, Recipients, TemplateId}
import com.simonmarkets.networks.common.clients.icn.IcnClient
import com.simonmarkets.networks.common.repository.NetworkRepository
import com.simonmarkets.resteasy.utils.FutureOps.FutureOptionOps
import com.simonmarkets.users.api.request.{ApproveUserImpersonationRequest, ForceCompleteImpersonationRequest, InsertUserImpersonationRequest}
import com.simonmarkets.users.common.User
import com.simonmarkets.users.common.Utils._
import com.simonmarkets.users.domain.{ImpersonationStatus, ImpersonationStatusResponse, UserImpersonation, UserImpersonationApprovalRequestContent, UserImpersonationApprovers, UserImpersonationStatusUpdateContent}
import com.simonmarkets.users.entitlements.UserImpersonationKeysGenerator
import com.simonmarkets.users.repository.{UserImpersonationApproversRepository, UserImpersonationRepository, UserRepository}
import io.circe.syntax.EncoderOps
import simon.Id.NetworkId

import java.time.Instant
import java.util.UUID

import scala.concurrent.duration.{FiniteDuration, HOURS}
import scala.concurrent.{ExecutionContext, Future}

trait UserImpersonationService {
  /**
   * Returns the UserImpersonation by given search parameters, if present */
  def getById(id: String)
    (implicit traceId: TraceId, user: UserACL): Future[Option[UserImpersonation]]

  /**
   * Returns the UserImpersonation by impersonatorUserId and status equal to Pending, if present */
  def getByImpersonatorUserIdThenExpire(impersonatorUserId: String,
      expiration: FiniteDuration = FiniteDuration(12, HOURS))
    (implicit traceId: TraceId, user: UserACL): Future[Option[UserImpersonation]]

  /**
   * Returns the UserImpersonation by impersonatedUserId, if present */
  def getByImpersonatedUserId(impersonatedUserId: String)
    (implicit traceId: TraceId, user: UserACL): Future[Option[UserImpersonation]]

  /**
   * Get the impersonation status of a user */
  def getImpersonationStatus(impersonatedUserId: String)
    (implicit traceId: TraceId, user: UserACL): Future[ImpersonationStatusResponse]

  /**
   * Inserts a UserImpersonation with a status equal to Pending */
  def insert(request: InsertUserImpersonationRequest, authToken: String)
    (implicit traceId: TraceId, user: UserACL): Future[UserImpersonation]

  /**
   * Change a UserImpersonation status from Pending to Complete */
  def complete(impersonatorUserId: Option[String])
    (implicit traceId: TraceId, user: UserACL): Future[UserImpersonation]

  /**
   * Change a UserImpersonation status from Pending to Complete */
  def forceComplete(request: ForceCompleteImpersonationRequest)
    (implicit traceId: TraceId, user: UserACL): Future[UserImpersonation]

  /**
   * Approve a UserImpersonation that is currently submitted */
  def approve(request: ApproveUserImpersonationRequest)
    (implicit traceId: TraceId, user: UserACL): Future[UserImpersonation]
}

object UserImpersonationService {
  class UserImpersonationServiceImpl(
      userImpersonationRepository: UserImpersonationRepository,
      userRepository: UserRepository,
      networkRepository: NetworkRepository,
      userImpersonationApproversRepository: UserImpersonationApproversRepository,
      emailSenderClient: EmailSenderClient,
      icnClient: IcnClient,
      excludedNetworks: Set[NetworkId]
  )(implicit ec: ExecutionContext)
    extends UserImpersonationService with TraceLogging {

    // Extract the common pattern of logging an httpError and then returning the corresponding future
    private def logHttpError[A](httpError: HttpError)(implicit traceId: TraceId): Future[A] = {
      log.error(httpError.payload)
      httpError.future
    }

    private def generateViewUserImpersonationEntitlements(userACL: UserACL): Set[String] = {
      // Admin, ReadOnlyAdmin, impersonator, & the approver can you view the UserImpersonation
      UserImpersonationCapabilities.getAvailableAccessKeysForCapabilities(ViewUserImpersonationCapabilities, userACL)
    }

    private def generateUpdateUserImpersonationEntitlements(userACL: UserACL): Set[String] = {
      // Only the impersonator can you update the UserImpersonation status to Submitted, Pending, or Complete
      UserImpersonationCapabilities.getAvailableAccessKeysForCapabilities(UpdateUserImpersonationCapabilities, userACL)
    }

    private def generateApproveUserImpersonationEntitlements(userACL: UserACL): Set[String] = {
      // Only an approver can change the status to Approved or Rejected
      UserImpersonationCapabilities.getAvailableAccessKeysForCapabilities(ApproveUserImpersonationCapabilities, userACL)
    }

    private def generateForceCompleteUserImpersonationEntitlements(userACL: UserACL): Set[String] = {
      // Only an Admin or ReadOnlyAdmin can force the status to Complete
      UserImpersonationCapabilities.getAvailableAccessKeysForCapabilities(AdminUserImpersonationCapabilities, userACL)
    }

    private def generateViewUserEntitlements(userACL: UserACL): Set[String] = {
      UsersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapabilities(UsersCapabilities.ViewCapabilities, userACL)
    }

    private def generateViewNetworkEntitlements(userACL: UserACL): Set[String] = {
      NetworksCapabilities.getAvailableAccessKeysForCapabilities(NetworksCapabilities.ViewCapabilities, userACL)
    }

    override def getById(id: String)
      (implicit traceId: TraceId, user: UserACL): Future[Option[UserImpersonation]] = {
      log.info(s"UserImpersonationService getById for id: $id")
      userImpersonationRepository.getById(id)(generateViewUserImpersonationEntitlements(user))
    }

    override def getByImpersonatorUserIdThenExpire(impersonatorUserId: String, expiration: FiniteDuration)
      (implicit traceId: TraceId, user: UserACL): Future[Option[UserImpersonation]] = {
      log.info(s"UserImpersonationService is trying to getByImpersonatorUserIdThenExpire for userId: $impersonatorUserId")
      val expiredTime: Instant = Instant.now().minus(expiration.length, expiration.unit.toChronoUnit)
      for {
        userImpersonations <- userImpersonationRepository.getNonFinishedUserImpersonations(impersonatorUserId)
        (toExpireImpersonations, activeImpersonations) = userImpersonations.partition(_.createdAt.isBefore(expiredTime))
        // every UserImpersonation with a status of 'Pending', 'Submitted', or 'Approved' with a
        // createdAt older than the expirationTime change the status to 'Expired'
        _ <- userImpersonationRepository.expireAll(toExpireImpersonations.map(_.id))
        // return the UserImpersonation with createdAt newer than the expiredTime and a status of Pending if it exists
        pendingUserImpersonation = activeImpersonations.find(_.status == ImpersonationStatus.Pending)
      } yield pendingUserImpersonation
    }

    override def getByImpersonatedUserId(impersonatedUserId: String)
      (implicit traceId: TraceId, user: UserACL): Future[Option[UserImpersonation]] = {
      log.info(s"UserImpersonationService getByImpersonatedUserId for impersonatedUserId: $impersonatedUserId")
      userImpersonationRepository.getByImpersonatedUserId(impersonatedUserId)
    }

    override def getImpersonationStatus(impersonatedUserId: String)
      (implicit traceId: TraceId, user: UserACL): Future[ImpersonationStatusResponse] = {
      log.info(s"UserImpersonationService getImpersonationStatus for impersonatorUserId: ${user.userId} & impersonatedUserId: $impersonatedUserId")
      userImpersonationRepository
        .getByImpersonatorUserIdAndImpersonatedUserId(user.userId, impersonatedUserId)(generateViewUserImpersonationEntitlements(user))
        .map {
          case Some(userImpersonation) => ImpersonationStatusResponse(userImpersonation.status.toString)
          case None => ImpersonationStatusResponse(ImpersonationStatus.NotSubmitted.toString)
        }
    }

    private def validateImpersonation(impersonatedUserId: String, authToken: String)
      (implicit traceId: TraceId, user: UserACL): Future[CanImpersonateStatus] = {
      for {
        impersonatedUser: User <- userRepository.getById(impersonatedUserId)(generateViewUserEntitlements(user))
          .flattenOption(s"User with id $impersonatedUserId was not found.")
        // check if user is able to impersonate preview User
        _ <- when(impersonatedUser.isPreviewUser && user.networkId != AdminNetworkId)
          .failWith(HttpError.forbidden("Only users in the iCapital Admin Network can impersonate a Preview User"))
        // check if the user is trying to impersonate a user in a restricted network
        _ <- when(excludedNetworks.contains(impersonatedUser.networkId))
          .failWith(HttpError.forbidden(s"Cannot impersonate user in network ${impersonatedUser.networkId}"))
        // check if the impersonatedUser has an Okta id
        _ <- when(impersonatedUser.idpId.isEmpty)
          .failWith(HttpError.forbidden(s"Try impersonating another user. There is an issue with the account for ${impersonatedUser.firstName} ${impersonatedUser.lastName}."))

        isImpersonatorUserUnified = user.iCapitalUserId.isDefined
        isImpersonatorUserSimon = user.iCapitalUserId.isEmpty
        isImpersonatorUserSimonAdmin = user.capabilities.contains(Admin)
        isImpersonatedUserUnified = impersonatedUser.iCapitalUserId.isDefined
        isImpersonatedUserSimon = impersonatedUser.iCapitalUserId.isEmpty

        // check if the user can impersonate the impersonatedUser on icn
        canImpersonateOnIcn <- {
          if(isImpersonatorUserUnified && isImpersonatedUserUnified)
            icnClient.canImpersonate(impersonatedUser.iCapitalUserId.get.toInt, authToken).map(_.canImpersonate)
          else if(isImpersonatorUserSimonAdmin)
            Future.successful(true)
          else
            Future.successful(false)
        }
        // check if the user has the ability to impersonate without approval on simon
        canImpersonateOnSimon = UsersCapabilities
          .availableAccessKeysGen
          .getAvailableAccessKeysForCapabilities(ImpersonateCapabilities, user)
          .nonEmpty
        // check if the user has the ability to impersonate with approval on simon
        canImpersonateWithApprovalOnSimon = UsersCapabilities
          .availableAccessKeysGen
          .getAvailableAccessKeysForCapabilities(ImpersonateWithApprovalCapabilities, user)
          .nonEmpty

        ability = {
          if (canImpersonateOnIcn ||
            (isImpersonatorUserUnified && isImpersonatedUserSimon) ||
            (isImpersonatorUserSimon && isImpersonatedUserSimon)) {
            if (canImpersonateOnSimon)
              CanImpersonate
            else if (canImpersonateWithApprovalOnSimon)
              CanImpersonateWithApproval
            else
              CannotImpersonate
          }
          else
            CannotImpersonate
        }
      } yield ability
    }

    private def insertUserImpersonation(request: InsertUserImpersonationRequest, toStatus: ImpersonationStatus, impersonatedUserNetworkId: String, approversUserIds: Option[Set[String]])
      (implicit traceId: TraceId, user: UserACL): Future[UserImpersonation] = {
      val userImpersonation = UserImpersonation(
        id = UUID.randomUUID.toString,
        impersonatorUserId = user.userId,
        impersonatedUserId = request.impersonatedUserId,
        impersonatedNetworkId = impersonatedUserNetworkId,
        reason = request.reason,
        ticketNumber = request.ticketNumber,
        status = toStatus,
        createdAt = Instant.now(),
        completedAt = None,
        traceId = traceId.toString(),
        approverUserId = None,
        approversUserIds = approversUserIds,
        entitlements = Set.empty
      )
      // change the UserImpersonation entitlements before inserting in MongoDB
      val userImpersonationToInsert = userImpersonation.copy(entitlements = UserImpersonationKeysGenerator.getAcceptedAccessKeys(userImpersonation))
      userImpersonationRepository.insert(userImpersonationToInsert)
    }

    private def sendSubmittedEmail(impersonation: UserImpersonation, impersonatedUser: User)
      (implicit traceId: TraceId, user: UserACL): Future[Unit] = {
      for {
        impersonatorUser: User <- userRepository.getById(impersonation.impersonatorUserId)(generateViewUserEntitlements(user))
          .flattenOption(s"User with id ${impersonation.impersonatorUserId} was not found.")
        impersonatedNetwork: Network <- networkRepository.getById(impersonatedUser.networkId)(generateViewNetworkEntitlements(user))
          .flattenOption(s"Network with id ${impersonatedUser.networkId} was not found.")
        approvers: UserImpersonationApprovers <- userImpersonationApproversRepository.getByNetworkId(impersonatedUser.networkId.toString)
          .flattenOption(s"Approvers for Network with id ${impersonatedUser.networkId} were not found.")
        _ <- {
          if (approvers.userIds.isEmpty) {
            logHttpError(HttpError.forbidden(s"No approvers found for network with id ${impersonatedUser.networkId}."))
          } else {
            emailSenderClient.sendEmail(
              EmailRequest(
                eventId = impersonation.id,
                eventType = TemplateId.`user-impersonation-approval-request`.productPrefix,
                templateId = TemplateId.`user-impersonation-approval-request`,
                recipients = Recipients(
                  userIds = approvers.userIds.filterNot(_ == impersonation.impersonatorUserId),
                  emails = Set.empty
                ),
                content = UserImpersonationApprovalRequestContent(
                  impersonationUUID = impersonation.id,
                  impersonatorUserId = impersonatorUser.id,
                  impersonatorUserName = impersonatorUser.fullName,
                  impersonatedUserId = impersonatedUser.id,
                  impersonatedUserName = impersonatedUser.fullName,
                  networkId = impersonatedUser.networkId,
                  networkName = impersonatedNetwork.name,
                  reason = impersonation.reason
                ).asJson,
                subject = "User Impersonation Approval Request"
              )
            )
          }
        }
      } yield Unit
    }

    private def insertUserImpersonationAndSendSubmittedEmail(request: InsertUserImpersonationRequest, impersonatedUser: User, userImpersonationApproversUserIds: Option[Set[String]])
      (implicit traceId: TraceId, user: UserACL): Future[UserImpersonation] = {
      for {
        inserted <- insertUserImpersonation(request, ImpersonationStatus.Submitted, impersonatedUser.networkId.toString, userImpersonationApproversUserIds)
        _ <- sendSubmittedEmail(inserted, impersonatedUser)
      } yield inserted
    }

    override def insert(request: InsertUserImpersonationRequest, authToken: String)
      (implicit traceId: TraceId, user: UserACL): Future[UserImpersonation] = {
      log.info(s"UserImpersonationService insert - impersonatorUserId:${user.userId} impersonatedUserId:${request.impersonatedUserId} reason:${request.reason}")
      for {
        impersonatedUser <- userRepository.getById(request.impersonatedUserId)(generateViewUserEntitlements(user))
          .flattenOption(s"The impersonated User with id ${request.impersonatedUserId} was not found.")
        impersonatedUserNetworkId = impersonatedUser.networkId.toString
        userImpersonationApproversUserIds: Option[Set[String]] <- userImpersonationApproversRepository.getByNetworkId(impersonatedUserNetworkId).map {
          case Some(userImpersonationApprovers) => Some(userImpersonationApprovers.userIds.filterNot(_ == user.userId))
          case None => None
        }
        ability <- validateImpersonation(request.impersonatedUserId, authToken)
        inserted: UserImpersonation <- ability match {
          case CannotImpersonate =>
            logHttpError(HttpError.forbidden(s"You do not have the capability to impersonate user with id ${request.impersonatedUserId}"))
          case CanImpersonate =>
            insertUserImpersonation(request, ImpersonationStatus.Pending, impersonatedUserNetworkId, None)
          case CanImpersonateWithApproval =>
            for {
              currentUserImpersonation: Option[UserImpersonation] <- userImpersonationRepository
                .getByImpersonatorUserIdAndImpersonatedUserId(user.userId, request.impersonatedUserId)(generateViewUserImpersonationEntitlements(user))
              approvalInserted <- currentUserImpersonation match {
                // For non-existent, create a new request
                case None => insertUserImpersonationAndSendSubmittedEmail(request, impersonatedUser, userImpersonationApproversUserIds)
                case Some(userImpersonation) =>
                  userImpersonation.status match {
                    // If there is already a pending impersonation request, leave that request in place and return it
                    case ImpersonationStatus.Pending => Future.successful(userImpersonation)
                    // If there is a submitted request, re-send the email.
                    case ImpersonationStatus.Submitted =>
                      for {
                        _ <- sendSubmittedEmail(userImpersonation, impersonatedUser)
                      } yield userImpersonation
                    // If there is an approved impersonation request, update that request to pending (active pending login)
                    case ImpersonationStatus.Approved =>
                      userImpersonationRepository
                        .updateStatus(userImpersonation.id, userImpersonation.status, ImpersonationStatus.Pending)(generateUpdateUserImpersonationEntitlements(user))
                        .flattenOption(s"Could not find UserImpersonation with id: ${userImpersonation.id} for update")
                    // For Rejected, Expired, Complete, or non-existent (above), create a new request
                    case ImpersonationStatus.Rejected | ImpersonationStatus.Expired | ImpersonationStatus.Complete =>
                      insertUserImpersonationAndSendSubmittedEmail(request, impersonatedUser, userImpersonationApproversUserIds)
                    case _ => logHttpError(HttpError.internalServerError(s"Unexpected ImpersonationStatus: ${userImpersonation.status}"))
                  }
              }
            } yield approvalInserted
        }
      } yield inserted
    }

    override def complete(impersonatorUserId: Option[String])
      (implicit traceId: TraceId, user: UserACL): Future[UserImpersonation] = {
      impersonatorUserId match {
        case Some(impersonatorId) =>
          userImpersonationRepository
            .complete(impersonatorId)
            .flattenOption(s"No impersonation to complete for user with id $impersonatorId")
        case None =>
          Future.failed(HttpError.badRequest("Missing parameter impersonatorUserId for the request"))
      }
    }

    override def forceComplete(request: ForceCompleteImpersonationRequest)
      (implicit traceId: TraceId, user: UserACL): Future[UserImpersonation] = {
      log.info(s"User with id: ${user.userId} is trying to forceComplete for impersonatorUserId: ${request.impersonatorUserId}")
      userImpersonationRepository
        .forceComplete(request.impersonatorUserId)(generateForceCompleteUserImpersonationEntitlements(user))
        .flattenOption(s"No User Impersonation to force complete for user with id: ${request.impersonatorUserId}")
    }

    private def sendApprovedOrRejectedEmail(userImpersonation: UserImpersonation, status: ImpersonationStatus)
      (implicit traceId: TraceId, user: UserACL): Future[Unit] = {
      for {
        approverUser: User <- userRepository.getById(user.userId)(generateViewUserEntitlements(user))
          .flattenOption(s"User with id ${user.userId} was not found.")
        impersonatedUser: User <- userRepository.getById(userImpersonation.impersonatedUserId)(generateViewUserEntitlements(user))
          .flattenOption(s"User with id ${userImpersonation.impersonatedUserId} was not found.")
        impersonatedNetwork: Network <- networkRepository.getById(impersonatedUser.networkId)(generateViewNetworkEntitlements(user))
          .flattenOption(s"Network with id ${impersonatedUser.networkId} was not found.")
        _ <- {
          val subject: String = status match {
            case ImpersonationStatus.Approved => "User Impersonation Request Approved"
            case ImpersonationStatus.Rejected => "User Impersonation Request Rejected"
            case _ => "" // Silence warning. This function is local, and is only ever called with a status of Approved or Rejected
          }
          emailSenderClient.sendEmail(
            EmailRequest(
              eventId = userImpersonation.id,
              eventType = TemplateId.`user-impersonation-status-update`.productPrefix,
              templateId = TemplateId.`user-impersonation-status-update`,
              recipients = Recipients(
                userIds = Set(userImpersonation.impersonatorUserId),
                emails = Set.empty
              ),
              content = UserImpersonationStatusUpdateContent(
                impersonatedUserId = impersonatedUser.id,
                impersonatedUserName = impersonatedUser.fullName,
                networkId = impersonatedNetwork.id,
                networkName = impersonatedNetwork.name,
                approverUserName = approverUser.fullName,
                reason = userImpersonation.reason,
                status = status
              ).asJson,
              subject = subject
            )
          )
        }
      } yield Unit
    }

    override def approve(request: ApproveUserImpersonationRequest)
      (implicit traceId: TraceId, user: UserACL): Future[UserImpersonation] = {
      for {
        userImpersonation <- userImpersonationRepository.getById(request.id)(generateViewUserImpersonationEntitlements(user))
          .flattenOption(s"Could not find UserImpersonation with id: ${request.id} for update")
        impersonatedUser: User <- userRepository.getById(userImpersonation.impersonatedUserId)(generateViewUserEntitlements(user))
          .flattenOption(s"User with id ${userImpersonation.impersonatedUserId} was not found.")
        _ <- request.status match {
          case ImpersonationStatus.Approved | ImpersonationStatus.Rejected => Future.successful(Unit)
          case _ => logHttpError(HttpError.badRequest(s"""Invalid status: "${request.status}" expected "Approved" or "Rejected""""))
        }
        result <- {
          if (user.userId == userImpersonation.impersonatorUserId) {
            logHttpError(HttpError.forbidden(s"You are not allowed to approve your own impersonation request."))
          } else if (userImpersonation.approversUserIds.getOrElse(Set.empty).contains(user.userId)) {
            userImpersonation.status match {
              case ImpersonationStatus.Submitted =>
                for {
                  result <- userImpersonationRepository.approveOrRejectStatus(request.id, request.status, user.userId)(generateApproveUserImpersonationEntitlements(user))
                    .flattenOption(s"Could not find UserImpersonation with id: ${request.id} for update")
                  _ <- sendApprovedOrRejectedEmail(userImpersonation, request.status)
                } yield result
              // Approved and Rejected are for a first-write-wins policy.
              // Pending is effectively Approved, but the user has already activated the request.
              case ImpersonationStatus.Approved | ImpersonationStatus.Rejected | ImpersonationStatus.Pending => Future.successful(userImpersonation)
              case _ => logHttpError(HttpError.forbidden(s"Cannot approve impersonation request with status: ${userImpersonation.status}"))
            }
          } else {
            logHttpError(HttpError.forbidden(s"You do not have the capability to approve an impersonation request in network: ${impersonatedUser.networkId}"))
          }
        }
      } yield result
    }

  }
}
