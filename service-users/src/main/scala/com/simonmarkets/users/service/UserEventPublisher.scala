package com.simonmarkets.users.service

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.mongodb.trigger.event.MongoEvent
import com.simonmarkets.users.common.trigger.UserPayload
import com.simonmarkets.users.common.User
import com.simonmarkets.users.config.KafkaProducerConfig
import com.simonmarkets.users.domain.{KafkaUserMessageResponse, UserEvent}
import com.simonmarkets.users.repository.UserRepository
import org.apache.kafka.clients.producer.{ProducerRecord, RecordMetadata}
import org.apache.kafka.common.header.Header
import org.apache.kafka.common.header.internals.{RecordHeader, RecordHeaders}
import zio._
import zio.kafka.producer.Producer
import zio.kafka.serde.Serde

import java.time.{Instant, ZoneId}

import scala.concurrent.{ExecutionContext, Future}


trait UserEventPublisher {

  def publishUserEventMessage(mongoEvent: MongoEvent[UserPayload])
    (implicit traceId: TraceId): Task[Option[KafkaUserMessageResponse]]

}

case class UserEventPublisherImpl(
    userRepo: UserRepository,
    producer: Producer,
    executionContext: ExecutionContext,
    kafkaConfig: KafkaProducerConfig,
) extends UserEventPublisher with TraceLogging {

  val topic: String = kafkaConfig.topic

  val producerGroupIdOpt: Option[String] = kafkaConfig.producerGroupId

  val sourceOpt: Option[String] = kafkaConfig.source

  def publishUserEventMessage(mongoEvent: MongoEvent[UserPayload])
    (implicit traceId: TraceId): Task[Option[KafkaUserMessageResponse]] = {
    log.info("[User Sync Publisher] raw mongo event received, attempting to publish", mongoEvent)
    val startTime = convertInstantToMillis(Instant.now)
    convertMongoUserPayloadEventToUserEventRecord(mongoEvent, startTime)
      .flatMap {
        case None =>
          ZIO.succeed {
            log.warn(s"[User Sync Publisher] could not convert mongo event [${mongoEvent.id}] to kafka message")
            None
          }
        case Some(userEventProducerRecord) =>
          val msg = userEventProducerRecord.value
          val eventInfo = userEventProducerRecord.value.eventInfo
          val userLastUpdatedAt = msg.updatedAt
          val userLastUpdatedAtMillis = convertInstantToMillis(userLastUpdatedAt.atZone(ZoneId.systemDefault()).toInstant)
          val msgReceiptLatency = startTime - userLastUpdatedAtMillis
          log.info(s"[User Sync Publisher] publishing user event",
            "eventType" -> eventInfo.eventType,
            "topic" -> topic,
            "mongoEventId" -> mongoEvent.id,
            "userId" -> msg.id,
            "networkId" -> msg.networkId,
            "lastUpdated" -> userLastUpdatedAt,
            "msgReceiptLatency" -> msgReceiptLatency
          )
          val publishedRecordMetadataOrErr: IO[Error, RecordMetadata] = publishUserEventRecordAndReturnMetadata(userEventProducerRecord, mongoEvent.id)
          publishedRecordMetadataOrErr
            .map { recordMetadata =>
              val timePublished = convertInstantToMillis(Instant.now)
              log.info(s"[User Sync Publisher] successfully published user event",
                "eventType" -> eventInfo.eventType,
                "topic" -> topic,
                "mongoEventId" -> mongoEvent.id,
                "userId" -> msg.id,
                "networkId" -> msg.networkId,
                "totalTimeToPublish" -> (timePublished - startTime)
              )
              Some(KafkaUserMessageResponse(msg.id, topic, recordMetadata.partition, eventInfo))
            }
      }
  }

  private def convertMongoUserPayloadEventToUserEventRecord(mongoEvent: MongoEvent[UserPayload],
      timeStarted: Long)(implicit traceId: TraceId): Task[Option[ProducerRecord[String, UserEvent]]] = {
    val userId = mongoEvent.detail.fullDocument.map(_.id)
    val accessKeys = Set(Admin)
    retrieveUserAndTransform(userId, timeStarted, accessKeys)
  }

  private def convertMongoUserToUserEvent(user: User): UserEvent = {
    UserEvent(
      id = user.id,
      networkId = user.networkId.toString,
      createdAt = user.createdAt,
      createdBy = user.createdBy,
      updatedAt = user.updatedAt,
      updatedBy = user.updatedBy,
      lastVisitedAt = user.lastVisitedAt,
      email = user.email,
      firstName = user.firstName,
      lastName = user.lastName,
      tradewebEligible = user.tradewebEligible,
      regSEligible = user.regSEligible,
      isActive = user.isActive,
      locations = user.locations,
      faNumbers = user.faNumbers,
      customRoles = user.customRoles,
      maskedIds = user.maskedIds,
      licenses = user.licenses,
      idpId = user.idpId,
      distributorInfo = user.distributorInfo,
      context = user.context,
      cusips = user.cusips,
      idpLoginId = user.idpLoginId,
      version = user.version,
      loginMode = user.loginMode,
      userType = user.userType,
      eventInfo = user.eventInfo,
      externalIds = user.externalIds,
      purviewLicenses = user.purviewLicenses,
      purviewNsccCodes = user.purviewNsccCodes,
      iCapitalUserId = user.iCapitalUserId,
      entitlements = user.entitlements,
      icnRoles = user.icnRoles,
      icnGroups = user.icnGroups,
      passport = user.passport
    )
  }

  private def retrieveUserAndTransform(userIdOpt: Option[String],
      timeStarted: Long,
      accessKeys: Set[String])(implicit traceId: TraceId): Task[Option[ProducerRecord[String, UserEvent]]] =
    for {
      user <- ZIO.fromFuture { _ =>
          userIdOpt match {
          case Some(userId) => userRepo.getById(userId)(accessKeys)
          case None         => Future.successful(None)
        }
      }
      record = user.map { u =>
        val event = convertMongoUserToUserEvent(u)
        convertUserEventToProducerRecord(event, timeStarted)
      }
    } yield record

  private def convertUserEventToProducerRecord(userEvent: UserEvent,
      timeStarted: Long)(implicit traceId: TraceId): ProducerRecord[String, UserEvent] = {
    val version = "1.0"
    val eventType = userEvent.eventInfo.eventType
    val correlationId = userEvent.eventInfo.correlationId.toString()
    val baseHeadersList: Array[Header] = Array(
      new RecordHeader("version", version.getBytes),
      new RecordHeader("correlationId", correlationId.getBytes),
      new RecordHeader("guid", traceId.toString().getBytes),
      new RecordHeader("eventType", eventType.toString.getBytes()),
    )

    val finalHeadersList: Array[Header] = (producerGroupIdOpt, sourceOpt) match {
      case (Some(producerGroupId), Some(source)) => baseHeadersList ++ Array(
        new RecordHeader("producer_group_id", producerGroupId.getBytes()),
        new RecordHeader("source", source.getBytes())
      )
      case (_, _) => baseHeadersList
    }

    val headers = new RecordHeaders(finalHeadersList)
    new ProducerRecord(topic, null, timeStarted, userEvent.id, userEvent, headers)
  }

  private def publishUserEventRecordAndReturnMetadata(userEventRecord: ProducerRecord[String, UserEvent],
      mongoEventId: String)(implicit traceId: TraceId) =
    producer
      .produce(userEventRecord, Serde.string, UserEvent.userEventSerde)
      .mapError { e =>
        log.error(
          s"[User Sync Publisher] could not publish mongo event",
          "mongoEventId" -> mongoEventId,
          "topic" -> topic,
          "error" -> e.getMessage
        )
        new Error(s"[User Sync Publisher] could not publish mongo event [$mongoEventId] to $topic, error: ${e.getMessage}")
      }

  private def convertInstantToMillis(instant: Instant): Long = instant.toEpochMilli

}

object UserEventPublisherImpl {

  def layer = ZLayer.fromFunction(UserEventPublisherImpl.apply _)

}
