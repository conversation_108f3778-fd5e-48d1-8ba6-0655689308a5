package com.simonmarkets.users.service

import akka.http.scaladsl.model.headers.HttpCookie
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.{Capabilities, UsersCapabilities}
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.api.CandidatesRequest
import com.simonmarkets.networks.common.clients.emailsender.{EmailRequest, EmailSenderClient, Recipients, TemplateId}
import com.simonmarkets.networks.common.clients.icn.HttpIcnClient
import com.simonmarkets.networks.common.domain.PassportUserCandidate
import com.simonmarkets.resteasy.utils.FutureOps.FutureOptionOps
import com.simonmarkets.users.api.request.{PassportCandidate, PassportConfirmation, PassportVerificationContent, PassportVerificationRequest}
import com.simonmarkets.users.common.Utils.{when, whenNot}
import com.simonmarkets.users.domain.PassportVerification
import com.simonmarkets.users.repository.{PassportRepository, UserRepository}
import io.circe.syntax.EncoderOps
import io.simon.encryption.v3.conversion.DefaultConverters
import io.simon.encryption.v3.service.EncryptionService

import java.time.Instant
import java.util.UUID

import scala.concurrent.duration.FiniteDuration
import scala.concurrent.{ExecutionContext, Future}

trait PassportService {

  /**
   * Initialize verification process for passport. Will fire an email to the user with a code to be entered for
   * confirm function
   *
   * @param requesterAcl calling user
   * @param request      passport details
   * @param jti          unique user session id
   * @param authToken    user jwt
   * @return - unit if succeed
   */
  def initializeVerification(
      requesterAcl: UserACL,
      request: PassportVerificationRequest,
      authToken: String,
      jti: String
  )(implicit traceId: TraceId): Future[Option[HttpCookie]]

  /**
   * Confirm passport verification. Requirements
   * - code match
   * - user in same session (jti)
   * - code not expired
   *
   * @param verification - code
   * @param jti          - unique user session id
   * @return - full updated passport
   */
  def confirm(
      requesterAcl: UserACL,
      verification: PassportConfirmation,
      jti: String
  )(implicit traceId: TraceId): Future[Map[String, Int]]

  /**
   * Remove domains from passport
   *
   * @param requesterAcl - calling user
   * @param request      - domains to remove
   * @return - updated passport
   */
  def removePassport(
      requesterAcl: UserACL
  )(implicit traceId: TraceId): Future[Map[String, Int]]

}

object PassportService {

  case class V1(
      userRepository: UserRepository,
      passportRepository: PassportRepository,
      emailSenderClient: EmailSenderClient,
      icnClient: HttpIcnClient,
      encryptionClient: EncryptionService,
      validityWindow: FiniteDuration,
      codeLength: Int,
      random: Iterator[Int],
      cc: Option[String],
      from: String
  )(implicit ec: ExecutionContext) extends PassportService with TraceLogging with DefaultConverters {

    override def initializeVerification(
        requesterAcl: UserACL,
        request: PassportVerificationRequest,
        authToken: String,
        jti: String
    )(implicit traceId: TraceId): Future[Option[HttpCookie]] = {
      log.info(s"Passport - verifying email", "email" -> requesterAcl.email)
      for {
        loggedInId <- Future
          .successful(requesterAcl.iCapitalUserId.map(_.toInt))
          .flattenOption("No valid ICN id on user")
        candidatesResp <- icnClient.candidates(CandidatesRequest(request.userIds + loggedInId), authToken)
        (candidates, cookie) = candidatesResp
        _ <- whenNot(candidates.users.map(_.email).forall(_.equalsIgnoreCase(requesterAcl.email)))
          .failWith(HttpError.badRequest("All passport candidate users must have same email"))
        verificationId = UUID.randomUUID.toString
        code = random.take(codeLength).mkString
        verificationDoc = PassportVerification(
          id = verificationId,
          userId = requesterAcl.userId,
          users = candidates.users,
          code = encryptionClient.hash(code),
          sessionId = jti,
          creationTime = Instant.now,
          completionTime = None,
          pending = true
        )
        _ <- emailSenderClient.sendEmail(
          EmailRequest(
            eventId = verificationId,
            eventType = TemplateId.`user-passport-verification`.productPrefix,
            recipients = Recipients(
              userIds = Set(requesterAcl.userId),
              emails = Set.empty
            ),
            templateId = TemplateId.`user-passport-verification`,
            content = PassportVerificationContent(
              users = candidates.users.map(u => PassportCandidate(u.email, u.wlpName)),
              confirmationCode = code,
            ).asJson,
            subject = "Link Accounts Verification",
            cc = cc,
            from = Some(from)
          )
        )

        _ <- passportRepository.insert(verificationDoc)
      } yield cookie
    }

    override def confirm(
        requester: UserACL,
        verification: PassportConfirmation,
        jti: String
    )(implicit traceId: TraceId): Future[Map[String, Int]] = {
      log.info(s"Passport - verifying code")
      val hashedCode = encryptionClient.hash(verification.code)
      for {
        verificationDoc <- passportRepository.findByCode(hashedCode).flattenOption("Invalid code")

        //validations
        _ <- whenNot(verificationDoc.sessionId == jti).failWith(HttpError.badRequest("Invalid session"))
        _ <- whenNot(verificationDoc.pending).failWith(HttpError.badRequest("Verification already completed"))
        _ <- when(Instant.now.isAfter(verificationDoc.creationTime.plusMillis(validityWindow.toMillis)))
          .failWith(HttpError.badRequest("Verification code expired"))

        //updates
        updatedPassport <- addPassport(
          verificationDoc.userId,
          verificationDoc.users
        )
        _ <- passportRepository.confirm(hashedCode)
      } yield updatedPassport
    }

    override def removePassport(
        requesterAcl: UserACL
    )(implicit traceId: TraceId): Future[Map[String, Int]] = {
      log.info(s"Passport - removing passport")
      val entitlements = UsersCapabilities
        .availableAccessKeysGen
        .getAvailableAccessKeysForCapabilities(UsersCapabilities.DeletePassportCapabilities, requesterAcl)
      for {
        initUser <- userRepository
          .getById(requesterAcl.userId)(entitlements)
          .flattenOption()
        user <- userRepository.updateUser(requesterAcl.userId, initUser.copy(passport = Map.empty))
      } yield user.passport
    }

    private def addPassport(
        userId: String,
        users: Seq[PassportUserCandidate]
    )(implicit traceId: TraceId): Future[Map[String, Int]] = {
      log.info(s"Passport - adding to passport")
      for {
        initUser <- userRepository.getById(userId)(Set(Capabilities.Admin)).flattenOption()
        passport = users
          .flatMap(u => wlpFromDomain(u.domain).map(_ -> u.icnId)).toMap ++ initUser.passport
        user <- userRepository.updateUser(userId, initUser.copy(passport = passport))
      } yield user.passport
    }

  }

  /**
   * For example - extract abc from https://abc.icapitalnetwork.com/some/path
   */
  def wlpFromUrl(uri: String): Option[String] = {
    """(?<=https://)[^.]*(?=.*icapitalnetwork\.com)""".r.findFirstIn(uri)
  }

  /**
   * For example - extract abc from abc.icapitalnetwork.com
   */
  def wlpFromDomain(uri: String): Option[String] = {
    """[^.]*(?=.*icapitalnetwork\.com)""".r.findFirstIn(uri)
  }

}
