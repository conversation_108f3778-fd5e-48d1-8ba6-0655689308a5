package com.simonmarkets.users.service

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.users.api.request.UpsertUserImpersonationApproversRequest
import com.simonmarkets.users.domain.UserImpersonationApprovers
import com.simonmarkets.users.repository.UserImpersonationApproversRepository

import java.util.UUID

import scala.concurrent.Future

trait UserImpersonationApproversService {

  /**
   * Returns the UserImpersonationApprovers given the provided networkId */
  def getByNetworkId(networkId: String)
    (implicit traceId: TraceId, user: UserACL): Future[Option[UserImpersonationApprovers]]

  /**
   * Inserts or update one UserImpersonationApprovers */
  def upsertUserImpersonationApprovers(request: UpsertUserImpersonationApproversRequest)
    (implicit traceId: TraceId, user: UserACL): Future[UserImpersonationApprovers]

}

object UserImpersonationApproversService {
  class UserImpersonationApproversServiceImpl(userImpersonationApproversRepository: UserImpersonationApproversRepository)
    extends UserImpersonationApproversService with TraceLogging {

    override def getByNetworkId(networkId: String)
      (implicit traceId: TraceId, user: UserACL): Future[Option[UserImpersonationApprovers]] = {
      log.info(s"UserImpersonationApproversService is trying to get by networkId: $networkId")
      userImpersonationApproversRepository.getByNetworkId(networkId)
    }

    override def upsertUserImpersonationApprovers(request: UpsertUserImpersonationApproversRequest)
      (implicit traceId: TraceId, user: UserACL): Future[UserImpersonationApprovers] = {
      log.info(s"The user with id: ${user.userId} is trying to upsert the UserImpersonationApprovers: ${request.toString}")
      userImpersonationApproversRepository.upsertUserImpersonationApprovers(
        UserImpersonationApprovers(
          id = UUID.randomUUID.toString,
          networkId = request.networkId,
          userIds = request.userIds
        )
      )
    }

  }
}
