package com.simonmarkets.users.service

import com.simonmarkets.capabilities.Capabilities.{Admin, ReadOnlyAdmin}
import com.simonmarkets.capabilities.UsersCapabilities._
import com.simonmarkets.entitlements.{AcceptedAccessKeysGenerator, AcceptedKeyBuilder}
import com.simonmarkets.users.common.User

trait UserAcceptedAccessKeysGenerator extends AcceptedAccessKeysGenerator[User] {
  def buildPurviewKeys(capability: String, user: User): Set[String] = Set(s"$capability:${user.networkId}")

  def buildNetworkKeys(capability: String, user: User): Set[String] = Set(s"$capability:${user.networkId}")

  def buildLocationKeys(capability: String, user: User): Set[String] = {
    user.locations.map(location => s"$capability:${user.networkId}:$location")
  }

  def buildFANumberKeys(capability: String, user: User): Set[String] = {
    user.faNumbers.map(faNumber => s"$capability:${user.networkId}:$faNumber")
  }

  def buildGuidKeys(capability: String, user: User): Set[String] = Set(s"$capability:${user.id}")

  override def capabilityToAcceptedKeyBuilders: Map[String, AcceptedKeyBuilder[User]] = Map(
    Admin -> AcceptedKeyBuilder(buildAdminKeys),
    ViewUserViaNetworkType -> AcceptedKeyBuilder(buildAdminNetworkTypeKey),
    UploadUserViaNetworkType -> AcceptedKeyBuilder(buildAdminNetworkTypeKey),
    ReadOnlyAdmin -> AcceptedKeyBuilder(buildAdminKeys),
    ViewUserViaPurview -> AcceptedKeyBuilder(buildPurviewKeys),
    ViewUserViaNetwork -> AcceptedKeyBuilder(buildNetworkKeys),
    ViewUserViaLocation -> AcceptedKeyBuilder(buildLocationKeys),
    ViewUserViaLocationHierarchy -> AcceptedKeyBuilder(buildLocationKeys),
    ViewUserViaFANumber -> AcceptedKeyBuilder(buildFANumberKeys),
    ViewUserViaGuid -> AcceptedKeyBuilder(buildGuidKeys),
    ViewUserViaParentNetwork -> AcceptedKeyBuilder(buildNetworkKeys),
    ImpersonateUserViaPurview -> AcceptedKeyBuilder(buildPurviewKeys),
    ImpersonateUserViaNetwork -> AcceptedKeyBuilder(buildNetworkKeys),
    ImpersonateUserViaLocation -> AcceptedKeyBuilder(buildLocationKeys),
    ImpersonateUserViaFANumber -> AcceptedKeyBuilder(buildFANumberKeys),
    ImpersonateUserViaPurviewWithApproval -> AcceptedKeyBuilder(buildPurviewKeys),
    ImpersonateUserViaNetworkWithApproval -> AcceptedKeyBuilder(buildNetworkKeys),
    ImpersonateUserViaLocationWithApproval -> AcceptedKeyBuilder(buildLocationKeys),
    ImpersonateUserViaFANumberWithApproval -> AcceptedKeyBuilder(buildFANumberKeys),
    UploadUserViaPurview -> AcceptedKeyBuilder(buildPurviewKeys),
    UploadUserViaNetwork -> AcceptedKeyBuilder(buildNetworkKeys),
    UploadUserViaGuid -> AcceptedKeyBuilder(buildGuidKeys),
    EditClientCredentialsViaNetwork -> AcceptedKeyBuilder(buildNetworkKeys),
    ViewClientCredentialsViaNetwork -> AcceptedKeyBuilder(buildNetworkKeys),
    DeletePassportViaGuidCapability.name -> AcceptedKeyBuilder(buildGuidKeys),
    ViewPassportViaGuidCapability.name -> AcceptedKeyBuilder(buildGuidKeys)
  )
}
