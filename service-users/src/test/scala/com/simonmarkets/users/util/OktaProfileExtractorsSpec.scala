package com.simonmarkets.users.util

import com.okta.sdk.resource.model.UserProfile
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{EitherValues, Matchers, OptionValues, WordSpec}

import java.{lang => jl, util => ju}

class OktaProfileExtractorsSpec extends WordSpec with Matchers with MockitoSugar with EitherValues with OptionValues {

  import OktaProfileExtractors._

  "OktaProfileExtractors" when {

    "parseStringList" should {

      "read list as string" in {
        parseStringList("[a, b, c]").right.value should contain theSameElementsAs Set("a", "b", "c")
      }

      "return error when not valid" in {
        parseStringList("boo").isLeft shouldBe true
      }

    }

    "UserProfileOps" can {

      "bool" should {

        "return boolean" when {

          "is boxed" in {
            val p = setProps(_.put("key", jl.Boolean.valueOf(true)))
            p.bool("key").right.value shouldBe true
          }

          "is string" in {
            val p = setProps(_.put("key", "true"))
            p.bool("key").right.value shouldBe true
          }

          "is object" in {
            val p = setProps(_.put("key", new Object() { override def toString: String = "false" }))
            p.bool("key").right.value shouldBe false
          }

        }

        "return left" when {

          "is null" in {
            val p = setProps(_ => ())
            p.bool("key").isLeft shouldBe true
          }

          "is string but not boolean" in {
            val p = setProps(_.put("key", "no bool"))
            p.bool("key").isLeft shouldBe true
          }

          "is object but not boolean" in {
            val p = setProps(_.put("key", new Object { override def toString: String = "no bool" }))
            p.bool("key").isLeft shouldBe true
          }

        }

      }

      "boolOpt" should {

        "return boolean" when {

          "is boxed" in {
            val p = setProps(_.put("key", jl.Boolean.valueOf(true)))
            p.boolOpt("key").right.value.value shouldBe true
          }

          "is string" in {
            val p = setProps(_.put("key", "true"))
            p.boolOpt("key").right.value.value shouldBe true
          }

          "is object" in {
            val p = setProps(_.put("key", new Object() { override def toString: String = "false" }))
            p.boolOpt("key").right.value.value shouldBe false
          }

        }

        "return none when is null" in {
          val p = setProps(_ => ())
          p.boolOpt("key").right.value shouldBe empty
        }

        "return left" when {

          "is string but not boolean" in {
            val p = setProps(_.put("key", "no bool"))
            p.boolOpt("key").isLeft shouldBe true
          }

          "is object but not boolean" in {
            val p = setProps(_.put("key", new Object { override def toString: String = "no bool" }))
            p.boolOpt("key").isLeft shouldBe true
          }

        }

      }

      "int" should {

        "return int" when {

          "is boxed" in {
            val p = setProps(_.put("key", jl.Integer.valueOf(1)))
            p.int("key").right.value shouldBe 1
          }

          "is string" in {
            val p = setProps(_.put("key", "1"))
            p.int("key").right.value shouldBe 1
          }

          "is object" in {
            val p = setProps(_.put("key", new Object { override def toString: String = "1" }))
            p.int("key").right.value shouldBe 1
          }

        }

        "return left" when {

          "is null" in {
            val p = setProps(_ => ())
            p.int("key").isLeft shouldBe true
          }

          "is string not int" in {
            val p = setProps(_.put("key", "no int"))
            p.int("key").isLeft shouldBe true
          }

          "is object not int" in {
            val p = setProps(_.put("key", new Object { override def toString: String = "no int" }))
            p.int("key").isLeft shouldBe true
          }

        }

      }

      "intOpt" should {

        "return int" when {

          "is boxed" in {
            val p = setProps(_.put("key", jl.Integer.valueOf(1)))
            p.intOpt("key").right.value.value shouldBe 1
          }

          "is string" in {
            val p = setProps(_.put("key", "1"))
            p.intOpt("key").right.value.value shouldBe 1
          }

          "is object" in {
            val p = setProps(_.put("key", new Object { override def toString: String = "1" }))
            p.intOpt("key").right.value.value shouldBe 1
          }

        }

        "return none when is null" in {
          val p = setProps(_ => ())
          p.intOpt("key").right.value shouldBe empty
        }

        "return left" when {

          "is string not int" in {
            val p = setProps(_.put("key", "no int"))
            p.intOpt("key").isLeft shouldBe true
          }

          "is object not int" in {
            val p = setProps(_.put("key", new Object { override def toString: String = "no int" }))
            p.intOpt("key").isLeft shouldBe true
          }

        }

      }

      "intSet" should {

        "return set" when {

          "is list" in {
            val ll = new ju.ArrayList[jl.Integer]()
            ll.add(1)
            ll.add(2)
            ll.add(3)
            ll.add(4)
            val p = setProps(_.put("key", ll))
            p.intSet("key").right.value should contain theSameElementsAs Set(1, 2, 3, 4)
          }

          "is string" in {
            val p = setProps(_.put("key", "[1, 2, 3, 4]"))
            p.intSet("key").right.value should contain theSameElementsAs Set(1, 2, 3, 4)
          }

          "is object" in {
            val p = setProps(_.put("key", new Object { override def toString: String = "[1, 2, 3, 4]" }))
            p.intSet("key").right.value should contain theSameElementsAs Set(1, 2, 3, 4)
          }

        }

        "return left for invalid ints" when {

          "is list" in {
            val ll = new ju.ArrayList[Object]()
            ll.add(jl.Integer.valueOf(1))
            ll.add(jl.Integer.valueOf(2))
            ll.add("boo")
            ll.add(jl.Integer.valueOf(4))
            val p = setProps(_.put("key", ll))
            p.intSet("key").isLeft shouldBe true
          }

          "is string" in {
            val p = setProps(_.put("key", "[1, 2, boo, 4]"))
            p.intSet("key").isLeft shouldBe true
          }

          "is object" in {
            val p = setProps(_.put("key", new Object { override def toString: String = "[1, 2, boo, 4]" }))
            p.intSet("key").isLeft shouldBe true
          }

        }

        "return empty set when empty list" in {
          val p = setProps(_.put("key", new ju.ArrayList[String]()))
          p.intSet("key").right.value shouldBe Set.empty
        }

        "return left" when {

          "is string" in {
            val p = setProps(_.put("key", ""))
            p.intSet("key").isLeft shouldBe true
          }

          "is object" in {
            val p = setProps(_.put("key", new Object { override def toString: String = "" }))
            p.intSet("key").isLeft shouldBe true
          }

          "is object but not int list" in {
            val p = setProps(_.put("key", new Object { override def toString: String = "boo" }))
            p.intSet("key").isLeft shouldBe true
          }

          "is null" in {
            val p = setProps(_ => ())
            p.intSet("key").isLeft shouldBe true
          }

        }

      }

      "intSetOpt" should {

        "return set" when {

          "is list" in {
            val ll = new ju.ArrayList[jl.Integer]()
            ll.add(1)
            ll.add(2)
            ll.add(3)
            ll.add(4)
            val p = setProps(_.put("key", ll))
            p.intSetOpt("key").right.value.value should contain theSameElementsAs Set(1, 2, 3, 4)
          }

          "is string" in {
            val p = setProps(_.put("key", "[1, 2, 3, 4]"))
            p.intSetOpt("key").right.value.value should contain theSameElementsAs Set(1, 2, 3, 4)
          }

          "is object" in {
            val p = setProps(_.put("key", new Object { override def toString: String = "[1, 2, 3, 4]" }))
            p.intSetOpt("key").right.value.value should contain theSameElementsAs Set(1, 2, 3, 4)
          }

        }

        "return empty set" when {

          "is list" in {
            val p = setProps(_.put("key", new ju.ArrayList[String]()))
            p.intSetOpt("key").right.value.value shouldBe Set.empty
          }

        }

        "return None" when {

          "is string" in {
            val p = setProps(_.put("key", ""))
            p.intSetOpt("key").right.value shouldBe empty
          }

          "is object" in {
            val p = setProps(_.put("key", new Object { override def toString: String = "" }))
            p.intSetOpt("key").right.value shouldBe empty
          }

          "is null" in {
            val p = setProps(_ => ())
            p.intSetOpt("key").right.value shouldBe empty
          }

        }

      }

      "string" should {

        "return string" in {
          val p = setProps(_.put("key", new Object { override def toString: String = "value" }))
          p.string("key").right.value shouldBe "value"
        }

        "return empty string" in {
          val p = setProps(_.put("key", ""))
          p.string("key").right.value shouldBe ""
        }

        "return left when null" in {
          val p = setProps(_ => ())
          p.string("key").isLeft shouldBe true
        }

      }

      "stringNonEmpty" should {

        "return string" in {
          val p = setProps(_.put("key", new Object { override def toString: String = "value" }))
          p.stringNonEmpty("key").right.value shouldBe "value"
        }

        "return left when string empty" in {
          val p = setProps(_.put("key", ""))
          p.stringNonEmpty("key").isLeft shouldBe true
        }

        "return left when null" in {
          val p = setProps(_ => ())
          p.stringNonEmpty("key").isLeft shouldBe true
        }

      }

      "stringSet" should {

        "return set" when {

          "is list" in {
            val ll = new ju.ArrayList[String]()
            ll.add("a")
            ll.add("b")
            ll.add("c")
            ll.add("d")
            val p = setProps(_.put("key", ll))
            p.stringSet("key").right.value should contain theSameElementsAs Set("a", "b", "c", "d")
          }

          "is string" in {
            val p = setProps(_.put("key", "[a, b, c, d]"))
            p.stringSet("key").right.value should contain theSameElementsAs Set("a", "b", "c", "d")
          }

          "is object" in {
            val p = setProps(_.put("key", new Object { override def toString: String = "[a, b, c, d]" }))
            p.stringSet("key").right.value should contain theSameElementsAs Set("a", "b", "c", "d")
          }

        }

        "return empty set" when {

          "is empty list" in {
            val p = setProps(_.put("key", new ju.ArrayList[String]()))
            p.stringSet("key").right.value shouldBe Set.empty
          }

        }

        "return left" when {

          "is empty string" in {
            val p = setProps(_.put("key", ""))
            p.stringSet("key").isLeft shouldBe true
          }

          "is empty object" in {
            val p = setProps(_.put("key", new Object { override def toString: String = "" }))
            p.stringSet("key").isLeft shouldBe true
          }

          "is object but not string list" in {
            val p = setProps(_.put("key", new Object { override def toString: String = "boo" }))
            p.stringSet("key").isLeft shouldBe true
          }

          "is null" in {
            val p = setProps(_ => ())
            p.stringSet("key").isLeft shouldBe true
          }

        }

      }

      "stringSetCommaSeparated" should {

        "return set" in {
          val p = setProps(_.put("key", "a,b,c,d"))
          p.stringSetCommaSeparated("key").right.value should contain theSameElementsAs Set("a", "b", "c", "d")
        }

        "return empty set" in {
          val p = setProps(_.put("key", ""))
          p.stringSetCommaSeparated("key").right.value shouldBe Set.empty
        }

        "return left when null" in {
          val p = setProps(_ => ())
          p.stringSetCommaSeparated("key").isLeft shouldBe true
        }

      }

      "stringSetNonEmpty" should {

        "return set" when {

          "is list" in {
            val ll = new ju.ArrayList[String]()
            ll.add("a")
            ll.add("b")
            ll.add("c")
            ll.add("d")
            val p = setProps(_.put("key", ll))
            p.stringSetNonEmptyOpt("key").right.value.value should contain theSameElementsAs Set("a", "b", "c", "d")
          }

          "is string" in {
            val p = setProps(_.put("key", "[a, b, c, d]"))
            p.stringSetNonEmptyOpt("key").right.value.value should contain theSameElementsAs Set("a", "b", "c", "d")
          }

          "is object" in {
            val p = setProps(_.put("key", new Object { override def toString: String = "[a, b, c, d]" }))
            p.stringSetNonEmptyOpt("key").right.value.value should contain theSameElementsAs Set("a", "b", "c", "d")
          }

        }

        "return None" when {

          "is empty list" in {
            val p = setProps(_.put("key", new ju.ArrayList[String]()))
            p.stringSetNonEmptyOpt("key").right.value shouldBe empty
          }

          "is empty string" in {
            val p = setProps(_.put("key", ""))
            p.stringSetNonEmptyOpt("key").right.value shouldBe empty
          }

          "is empty object" in {
            val p = setProps(_.put("key", new Object { override def toString: String = "" }))
            p.stringSetNonEmptyOpt("key").right.value shouldBe empty
          }

          "is null" in {
            val p = setProps(_ => ())
            p.stringSetNonEmptyOpt("key").right.value shouldBe empty
          }

        }

        "return left when object is not string list" in {
          val p = setProps(_.put("key", new Object { override def toString: String = "boo" }))
          p.stringSetNonEmptyOpt("key").isLeft shouldBe true
        }

      }

      "stringSetOpt" should {

        "return set" when {

          "is list" in {
            val ll = new ju.ArrayList[String]()
            ll.add("a")
            ll.add("b")
            ll.add("c")
            ll.add("d")
            val p = setProps(_.put("key", ll))
            p.stringSetOpt("key").right.value.value should contain theSameElementsAs Set("a", "b", "c", "d")
          }

          "is string" in {
            val p = setProps(_.put("key", "[a, b, c, d]"))
            p.stringSetOpt("key").right.value.value should contain theSameElementsAs Set("a", "b", "c", "d")
          }

          "is object" in {
            val p = setProps(_.put("key", new Object { override def toString: String = "[a, b, c, d]" }))
            p.stringSetOpt("key").right.value.value should contain theSameElementsAs Set("a", "b", "c", "d")
          }

          "is empty list" in {
            val p = setProps(_.put("key", new ju.ArrayList[String]()))
            p.stringSetOpt("key").right.value.value shouldBe empty
          }

        }

        "return left when object is not string list" in {
          val p = setProps(_.put("key", new Object { override def toString: String = "boo" }))
          p.stringSetOpt("key").isLeft shouldBe true
        }

        "return none" when {

          "return None when null" in {
            val p = setProps(_ => ())
            p.stringSetOpt("key").right.value shouldBe empty
          }

          "is empty string" in {
            val p = setProps(_.put("key", ""))
            p.stringSetOpt("key").right.value shouldBe empty
          }

          "is empty object" in {
            val p = setProps(_.put("key", new Object { override def toString: String = "" }))
            p.stringSetOpt("key").right.value shouldBe empty
          }

        }

      }

    }

  }

  private def setProps(f: ju.HashMap[String, Object] => Unit): UserProfile = {
    val p = new UserProfile()
    val props = new ju.HashMap[String, Object]()
    f(props)
    p.setAdditionalProperties(props)
    p
  }

}
