package com.simonmarkets.users.domain

import com.goldmansachs.marquee.pipg.UserRole.{EqPIPGFA, EqPIPGFAManager}
import com.goldmansachs.marquee.pipg.{License, UserRole}
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.okta.testimpl.UserProfileTestImpl
import com.simonmarkets.syntax.anyOpsConversion
import com.simonmarkets.users.common.Context
import com.simonmarkets.users.common.User.{Distributor<PERSON>ey, OmsKey}
import com.simonmarkets.users.common.api.request.UpsertUserRequest
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}
import simon.Id.NetworkId

import java.time.Instant
import java.time.temporal.ChronoUnit
import java.{lang => jl, util => ju}

import scala.collection.JavaConverters._
import scala.reflect.runtime.{universe => sru}

class SsoFieldSpec extends WordSpec with Matchers with MockitoSugar {

  private val ssoNetworkId = "ssoNetworkId"
  private val ssoSubjectNetwork = "ssoSubjectNetwork"
  private val ssoExternalNetworkId = "ssoExternalNetworkId"
  private val networkId = "networkId"
  private val ssoClientId = Some("ssoClientId")
  private val ssoOmsId = Some("ssoOmsId")
  private val ssoUserRoles: Set[String] = Set("EqPIPGSIMONSystemUser")
  private val ssoLocations = Set("ssoLoc1", "ssoLoc2")
  private val ssoPurviewNsccCodes = Set("NSCC1", "NSCC2")
  private val ssoTradewebEligible = true
  private val ssoRegSEligible = true
  private val ssoFaNumbers = Set("sso123", "sso456")
  private val ssoCustomRoles = Set("ssoADMIN", "ssoCustomRole2")
  private val ssoNpn: Option[String] = Some("123")
  private val ssoEndpointCapabilities = Set("EqPIPGSIMONSystemUser")
  private val ssoFaNumbersCommaSeparated = ssoFaNumbers.mkString(",")
  private val ssoLocationsCommaSeparated = ssoLocations.mkString(",")
  private val ssoCustomRolesCommaSeparated = ssoCustomRoles.mkString(",")
  private val ssoAccountInContext = "ssoAccountInContext"
  private val ssoContextAccountId = "ssoContextAccountId"
  private val ssoContextProposalId = "ssoContextProposalId"
  private val ssoContextTaskId = "ssoContextTaskId"
  private val ssoContextServiceRequestId = "ssoContextServiceRequestId"
  private val ssoContextBucketId = "ssoContextBucketId"
  private val ssoExternalRoles = Set("externalRole1", "externalRole2")
  private val ssoExternalUserId1 = "ssoExternalUserId1"
  private val ssoExternalUserId2 = "ssoExternalUserId2"
  private val ssoPurviewLicenseStrings = Set("NPN:1234", "CRD:5678")
  private val firstName = "firstName"
  private val lastName = "lastName"
  private val ssoFirstName = "ssoFirstName"
  private val ssoLastName = "ssoLastName"
  private val ssoEmail = "ssoEmail"
  private val ssoFirmId = "123"
  private val ssoWlpId = "456"
  private val ssoAltsGroups = Set("123", "456")
  private val ssoAltsRoles = Set("789", "111")
  private val ssoPurviewNsccCodesCommaSeparated = ssoPurviewNsccCodes.mkString(",")

  private val customProperties = Map(
    "ssoNetwork" -> ssoNetworkId,
    "ssoCustomRoles" -> new ju.ArrayList[String](ssoCustomRoles.asJava),
    "ssoLocations" -> new ju.ArrayList[String](ssoLocations.asJava),
    "ssoFaNumbers" -> new ju.ArrayList[String](ssoFaNumbers.asJava),
    "ssoOmsId" -> ssoOmsId.getOrElse(""),
    "ssoClientId" -> ssoClientId.getOrElse(""),
    "ssoRegSEligible" -> jl.Boolean.valueOf(ssoRegSEligible),
    "ssoUserRoles" -> new ju.ArrayList[String](ssoUserRoles.asJava),
    "ssoNpn" -> ssoNpn.getOrElse(""),
    "ssoEndpointCapabilities" -> new ju.ArrayList[String](ssoEndpointCapabilities.asJava),
    "ssoTradewebEligible" -> jl.Boolean.valueOf(ssoTradewebEligible),
    "ssoAccountInContext" -> ssoAccountInContext,
    "ssoContextAccountId" -> ssoContextAccountId,
    "ssoContextProposalId" -> ssoContextProposalId,
    "ssoContextTaskId" -> ssoContextTaskId,
    "ssoContextServiceRequestId" -> ssoContextServiceRequestId,
    "ssoContextBucketId" -> ssoContextBucketId,
    "ssoContextExternalRoles" -> new ju.ArrayList[String](ssoExternalRoles.asJava),
    "ssoFaNumbersCommaSeparated" -> ssoFaNumbersCommaSeparated,
    "ssoLocationsCommaSeparated" -> ssoLocationsCommaSeparated,
    "ssoCustomRolesCommaSeparated" -> ssoCustomRolesCommaSeparated,
    "ssoSubjectNetwork" -> ssoSubjectNetwork,
    "ssoExternalNetworkId" -> ssoExternalNetworkId,
    "ssoExternalId_subject1" -> ssoExternalUserId1,
    "ssoExternalId_subject2" -> ssoExternalUserId2,
    "ssoPurviewLicenses" -> new ju.ArrayList[String](ssoPurviewLicenseStrings.asJava),
    "network" -> networkId,
    "ssoFirstName" -> ssoFirstName,
    "ssoLastName" -> ssoLastName,
    "ssoEmail" -> ssoEmail,
    SsoField.SsoFirmId.field -> ssoFirmId,
    SsoField.SsoWlpId.field -> ssoWlpId,
    SsoField.SsoAltsGroups.field -> new ju.ArrayList[String](ssoAltsGroups.asJava),
    SsoField.SsoAltsRoles.field -> new ju.ArrayList[String](ssoAltsRoles.asJava),
    "ssoPurviewNsccCodes" -> ssoPurviewNsccCodesCommaSeparated
  )

  val testProfile = buildTestProfile(customProperties)

  val emptyTestProfile = buildTestProfile(Map.empty)

  val upsertBefore = UpsertUserRequest(
    networkId = NetworkId("bad"),
    email = "bad",
    firstName = firstName,
    lastName = lastName,
    externalIds = Map.empty,
    tradewebEligible = None,
    regSEligible = None,
    roles = Set.empty
  )

  def buildTestProfile(customProperties: Map[String, Object]): UserProfileTestImpl =
    new UserProfileTestImpl(firstName = firstName, lastName = lastName, customProperties = customProperties)

  "Each field should modify the upsert case class as expected" when {

    "SsoUserRoles" when {
      "no roles are provided and some exist should retain existing" in {
        val customProps = customProperties + ("ssoUserRoles" -> new ju.ArrayList[String](Set("").asJava))
        val noRolesProfile = buildTestProfile(customProps)
        val existing = upsertBefore.copy(roles = Set(EqPIPGFAManager))
        SsoField.SsoUserRoles.set(existing, noRolesProfile).map(_.roles) shouldBe Right(Set(EqPIPGFAManager))
      }

      "no roles are provided and none exist should add default" in {
        val customProps = customProperties + ("ssoUserRoles" -> new ju.ArrayList[String](Set("").asJava))
        val noRolesProfile = buildTestProfile(customProps)
        val existing = upsertBefore.copy(roles = Set.empty)
        SsoField.SsoUserRoles.set(existing, noRolesProfile).map(_.roles) shouldBe Right(Set(EqPIPGFA))
      }

      "roles are provided" in {
        SsoField.SsoUserRoles.set(upsertBefore, testProfile).map(_.roles) shouldBe Right(
          ssoUserRoles.map(UserRole.apply)
        )
      }
    }

    "SsoCustomRoles" in {
      SsoField.SsoCustomRoles.set(upsertBefore, testProfile).map(_.customRoles) shouldBe Right(Some(ssoCustomRoles))
    }

    "SsoFaNumbers" when {

      "Has values" in {
        SsoField.SsoFaNumbers.set(upsertBefore, testProfile).map(_.faNumbers) shouldBe Right(Some(ssoFaNumbers))
      }

      "Has empty string" in {
        val customProps = customProperties + ("ssoFaNumbers" -> new ju.ArrayList[String](Set("").asJava))
        val emptyStringProfile = buildTestProfile(customProps)
        SsoField.SsoFaNumbers.set(upsertBefore, emptyStringProfile).map(_.faNumbers) shouldBe Right(None)
      }
    }

    "SsoLocations" when {

      "Has values" in {
        SsoField.SsoLocations.set(upsertBefore, testProfile).map(_.locations) shouldBe Right(Some(ssoLocations))
      }

      "Has empty string" in {
        val customProps = customProperties + ("ssoLocations" -> new ju.ArrayList[String](Set("").asJava))
        val emptyStringProfile = buildTestProfile(customProps)
        SsoField.SsoLocations.set(upsertBefore, emptyStringProfile).map(_.faNumbers) shouldBe Right(None)
      }
    }

    "SsoOmsId" in {
      SsoField.SsoOmsId.set(upsertBefore, testProfile).map(_.externalIds.get(OmsKey)) shouldBe Right(ssoOmsId)
    }

    "SsoClientId" in {
      SsoField.SsoClientId.set(upsertBefore, testProfile).map(_.externalIds.get(DistributorKey)) shouldBe Right(
        ssoClientId
      )
    }

    "SsoTradewebEligible" in {
      SsoField.SsoTradewebEligible.set(upsertBefore, testProfile).map(_.tradewebEligible) shouldBe Right(
        Some(ssoTradewebEligible)
      )
    }

    "SsoRegSEligible" in {
      SsoField.SsoRegSEligible.set(upsertBefore, testProfile).map(_.regSEligible) shouldBe Right(
        Some(ssoRegSEligible)
      )
    }

    "SsoNpn" when {
      "No licenses exist currently" in {
        val npn = License.NPN(ssoNpn.get)
        SsoField.SsoNpn.set(upsertBefore, testProfile).map(_.licenses) shouldBe Right(Some(Set(npn)))
      }

      "Existing licenses should not be wiped" in {
        val npn = License.NPN(ssoNpn.get)
        val crd = License.CRD("crd")
        val upsertWithLicenses = upsertBefore.copy(licenses = Some(Set(crd)))
        SsoField.SsoNpn.set(upsertWithLicenses, testProfile).map(_.licenses) shouldBe Right(Some(Set(npn, crd)))
      }
    }

    "SsoAccountInContext" in {
      SsoField.SsoAccountInContext.set(upsertBefore, testProfile).map(_.accountInContext) shouldBe Right(
        Some(ssoAccountInContext)
      )
    }

    "SsoContext" in {
      val expected = Context(
        accountId = ssoContextAccountId.some,
        proposalId = ssoContextProposalId.some,
        taskId = ssoContextTaskId.some,
        serviceRequestId = ssoContextServiceRequestId.some,
        bucketId = ssoContextBucketId.some,
        externalRoles = Some(ssoExternalRoles),
        lastUpdatedAt = Instant.now.truncatedTo(ChronoUnit.SECONDS).some
      )
      val responseEither = SsoField.SsoContext.set(upsertBefore, testProfile).map(_.context)
      responseEither.map(_.get shouldBe expected)
    }

    "SsoFaNumbersCommaSeparated" in {
      SsoField.SsoFaNumbersCommaSeparated.set(upsertBefore, testProfile).map(_.faNumbers) shouldBe Right(
        Some(ssoFaNumbers)
      )
    }

    "SsoLocationsCommaSeparated" in {
      SsoField.SsoLocationsCommaSeparated.set(upsertBefore, testProfile).map(_.locations) shouldBe Right(
        Some(ssoLocations)
      )
    }

    "SsoCustomRolesCommaSeparated" in {
      SsoField.SsoCustomRolesCommaSeparated.set(upsertBefore, testProfile).map(_.customRoles) shouldBe Right(
        Some(ssoCustomRoles)
      )
    }

    "SsoNetwork" in {
      // validating that the set is a no-op
      SsoField.SsoNetwork.set(upsertBefore, testProfile) shouldBe Right(upsertBefore)
    }

    "SsoExternalNetworkId" in {
      // validating that the set is a no-op
      SsoField.SsoExternalNetworkId.set(upsertBefore, testProfile) shouldBe Right(upsertBefore)
    }

    "SsoExternalIds" when {
      "No user external ids exist currently" in {
        val expected = Map("subject1" -> ssoExternalUserId1, "subject2" -> ssoExternalUserId2)
        SsoField.SsoExternalIds.set(upsertBefore, testProfile).map(_.externalIds) shouldBe Right(expected)
      }

      "Existing non-conflicting external ids should not be wiped" in {
        val existing = Map("subject3" -> "id3")
        val expected = Map("subject1" -> ssoExternalUserId1, "subject2" -> ssoExternalUserId2) ++ existing
        val upsertWithExisting = upsertBefore.copy(externalIds = existing)
        SsoField.SsoExternalIds.set(upsertWithExisting, testProfile).map(_.externalIds) shouldBe Right(expected)
      }

      "Existing conflicting external ids should be upserted" in {
        val existing = Map("subject2" -> "old")
        val expected = Map("subject1" -> ssoExternalUserId1, "subject2" -> ssoExternalUserId2)
        val upsertWithExisting = upsertBefore.copy(externalIds = existing)
        SsoField.SsoExternalIds.set(upsertWithExisting, testProfile).map(_.externalIds) shouldBe Right(expected)
      }
    }

    "SsoPurviewNsccCodes" in {
      SsoField.SsoPurviewNsccCodes.set(upsertBefore, testProfile).map(_.purviewNsccCodes) shouldBe
        Right(Some(ssoPurviewNsccCodes))
    }

    "SsoPurviewLicenses" when {
      "NPN and CRDs provided" in {
        val expected = Set(
          License.NPN("1234"),
          License.CRD("5678")
        )
        SsoField.SsoPurviewLicenses.set(upsertBefore, testProfile).map(_.purviewLicenses) shouldBe Right(
          Some(expected)
        )
      }

      "Wipe when empty" in {
        val testProfile = buildTestProfile(
          Map("ssoPurviewLicenses" -> new ju.ArrayList[String](Set.empty.asJava))
        )
        SsoField.SsoPurviewLicenses.set(upsertBefore, testProfile).map(_.purviewLicenses) shouldBe Right(
          Some(Set.empty)
        )
      }

      "Incorrect license name" in {
        val badTestProfile = buildTestProfile(
          Map("ssoPurviewLicenses" -> new ju.ArrayList[String](Set("NPBAD:12").asJava))
        )
        SsoField.SsoPurviewLicenses.set(upsertBefore, badTestProfile).map(_.purviewLicenses).isLeft shouldBe true
      }

      "Incorrectly formatted values provided" in {
        val badTestProfile = buildTestProfile(
          Map("ssoPurviewLicenses" -> new ju.ArrayList[String](Set("BAD").asJava))
        )
        SsoField.SsoPurviewLicenses.set(upsertBefore, badTestProfile).map(_.purviewLicenses).isLeft shouldBe true
      }
    }

    "SsoFirstName" in {
      SsoField.SsoFirstName.set(upsertBefore, testProfile).map(_.firstName) shouldBe Right(ssoFirstName)
    }

    "SsoLastName" in {
      SsoField.SsoLastName.set(upsertBefore, testProfile).map(_.lastName) shouldBe Right(ssoLastName)
    }

    "SsoEmail" in {
      SsoField.SsoEmail.set(upsertBefore, testProfile).map(_.email) shouldBe Right(ssoEmail)
    }

    "SsoFirmId" when {
      "field is valid integer, then should resolve normally" in {
        SsoField.SsoFirmId.set(upsertBefore, testProfile).map(_.firmId) shouldBe Right(Some(ssoFirmId))
      }

      "field is not present, then should resolve normally and signify empty" in {
        SsoField.SsoFirmId.set(upsertBefore, emptyTestProfile).map(_.firmId) shouldBe Right(None)
      }

      "field is present, but invalid, then should signify error occurred" in {
        val ssoFirmIdIsNotAnInt = buildTestProfile(Map(SsoField.SsoFirmId.field -> "notAnInt"))
        SsoField.SsoFirmId.set(upsertBefore, ssoFirmIdIsNotAnInt).map(_.firmId).isLeft shouldBe true
      }
    }

    "SsoWlpId" when {
      "field is valid integer, then should resolve normally" in {
        SsoField.SsoWlpId.set(upsertBefore, testProfile).map(_.whiteLabelPartnerId) shouldBe Right(Some(ssoWlpId))
      }

      "field is not present, then should resolve normally and signify empty" in {
        SsoField.SsoWlpId.set(upsertBefore, emptyTestProfile).map(_.whiteLabelPartnerId) shouldBe Right(None)
      }

      "field is present, but invalid, then should signify error occurred" in {
        val ssoWlpIdIsNotAnInt = buildTestProfile(Map(SsoField.SsoWlpId.field -> "notAnInt"))
        SsoField.SsoWlpId.set(upsertBefore, ssoWlpIdIsNotAnInt).map(_.whiteLabelPartnerId).isLeft shouldBe true
      }
    }

    "SsoAltsGroups" when {
      "field is a valid set of integer, then should resolve normally" in {
        SsoField.SsoAltsGroups.set(upsertBefore, testProfile).map(_.icnGroups) shouldBe Right(Some(ssoAltsGroups))
      }

      "field is not present, then should resolve normally and signify empty" in {
        SsoField.SsoAltsGroups.set(upsertBefore, emptyTestProfile).map(_.icnGroups) shouldBe Right(None)
      }

      "field is present, but invalid, then should signify error occurred" in {
        val ssoAltsGroupsAreNotInts =
          buildTestProfile(Map(SsoField.SsoAltsGroups.field -> Set("notAnInt1", "notAnInt2")))
        SsoField.SsoAltsGroups.set(upsertBefore, ssoAltsGroupsAreNotInts).map(_.icnGroups).isLeft shouldBe true
      }
    }

    "SsoAltsRoles" when {
      "field is a valid set of integer, then should resolve normally" in {
        SsoField.SsoAltsRoles.set(upsertBefore, testProfile).map(_.icnRoles) shouldBe Right(Some(ssoAltsRoles))
      }

      "field is not present, then should resolve normally and signify empty" in {
        SsoField.SsoAltsRoles.set(upsertBefore, emptyTestProfile).map(_.icnRoles) shouldBe Right(None)
      }

      "field is present, but invalid, then should signify error occurred" in {
        val ssoAltsRolesAreNotInts =
          buildTestProfile(Map(SsoField.SsoAltsRoles.field -> Set("notAnInt1", "notAnInt2")))
        SsoField.SsoAltsRoles.set(upsertBefore, ssoAltsRolesAreNotInts).map(_.icnRoles).isLeft shouldBe true
      }
    }

  }

  "Each field with a get should work" when {

    "SsoClientId" in {
      SsoField.SsoClientId.get(testProfile) shouldBe Right(ssoClientId)
    }

    "SsoNetwork" in {
      SsoField.SsoNetwork.get(testProfile) shouldBe Right(Some(NetworkId(ssoNetworkId)))
    }

    "SsoExternalNetworkId" in {
      val expected = ExternalId(ssoSubjectNetwork, ssoExternalNetworkId)
      SsoField.SsoExternalNetworkId.get(testProfile) shouldBe Right(Some(expected))
    }

    "Network" in {
      SsoField.Network.get(testProfile) shouldBe Right(Some(NetworkId(networkId)))
    }

  }

  "All settable fields should be added to `values`" in {
    val count =
      sru
        .typeOf[SsoField[_]]
        .typeSymbol
        .asClass
        .knownDirectSubclasses
        .foldLeft(Set.empty[sru.Symbol]) { (acc, sym) =>
          acc ++ sym.asClass.knownDirectSubclasses + sym
        }
        .count(!_.isAbstract)

    // sso network and sso external network and network are intentionally excluded
    SsoField.values.size shouldBe count - 3
  }

  "Decoding failures should be reported" in {
    val badUser = new UserProfileTestImpl(customProperties = Map("ssoCustomRoles" -> "notAnArray"))
    SsoField.SsoCustomRoles.set(upsertBefore, badUser).isLeft shouldBe true
  }

}
