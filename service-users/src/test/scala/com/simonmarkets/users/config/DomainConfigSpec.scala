package com.simonmarkets.users.config

import com.simonmarkets.users.common.{LandingPage, LoginMode}
import org.scalatest.{Matchers, WordSpec}

class DomainConfigSpec extends WordSpec with Matchers {

  "Domain config" should {
    "match all sub domains of a configured domain" in {
      val domainConfig = DomainConfig("simonmarkets.com", LandingPage.SIMON, List(LoginMode.SSOAndUsernamePassword), defaultLoginMode = LoginMode.UsernamePassword)

      domainConfig.isDomainMatch("www.simonmarkets.com") shouldBe true
      domainConfig.isDomainMatch("www.qa.api.simonmarkets.com") shouldBe true
      domainConfig.isDomainMatch("www.icapitalnetwork.com") shouldBe false
    }
  }
}
