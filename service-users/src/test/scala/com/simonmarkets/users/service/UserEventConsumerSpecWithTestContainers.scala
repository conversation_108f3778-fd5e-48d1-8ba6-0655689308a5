package com.simonmarkets.users.service

import com.dimafeng.testcontainers.DockerComposeContainer
import com.dimafeng.testcontainers.scalatest.TestContainerForEach
import com.goldmansachs.marquee.pipg.UserACL
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.kafka.codec.{Deserializers, Serializers}
import com.simonmarkets.kafka.config.{ConsumerConfig, KafkaConnectionConfig}
import com.simonmarkets.kafka.{ClientId, GroupId, TopicName, ZConsumer}
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.common.clients.usersync._
import com.simonmarkets.networks.common.service.networkservice.BasicNetworkService
import com.simonmarkets.okta.domain.{Factor, FactorType}
import com.simonmarkets.syntax.{anyOpsConversion, futureOpsConversion}
import com.simonmarkets.users.common.UniqueUserId.{Email, Guid}
import com.simonmarkets.users.common.api.request.{UpsertUserRequest, GetUniqueUserIdRequest}
import com.simonmarkets.users.common.{UpsertField, User, UserDomainEvent}
import com.simonmarkets.users.domain.{IcnUserEvent, SyncError, TransientState}
import com.simonmarkets.users.repository.{MongoUserRepository, TransientStateRepository}
import com.simonmarkets.users.{TestNetwork, TestUser}
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.header.Header
import org.apache.kafka.common.header.internals.{RecordHeader, RecordHeaders}
import org.mockito.ArgumentMatchers.{any, eq => meq}
import org.mockito.Mockito.{reset, times, verify, when}
import org.mongodb.scala.Document
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterEach, DoNotDiscover, Matchers, WordSpec}
import simon.Id.NetworkId
import zio._
import zio.json.{DeriveJsonDecoder, JsonDecoder}
import zio.kafka.admin.AdminClient.NewTopic
import zio.kafka.admin.{AdminClient, AdminClientSettings}
import zio.kafka.producer.{Producer, ProducerSettings}

import java.io.File
import java.time.Instant

import scala.concurrent.ExecutionContext.global
import scala.concurrent.{ExecutionContext, Future}

@DoNotDiscover
class UserEventConsumerSpecWithTestContainers
  extends WordSpec
    with Matchers
    with MockitoSugar
    with EmbeddedMongoLike
    with BeforeAndAfterEach
    with TestContainerForEach {

  val topic = "test"

  implicit val traceId: TraceId = TraceId.randomize
  val runtime = Runtime.default
  implicit val ec = global

  override val containerDef = DockerComposeContainer.Def(
    new File(getClass.getResource("/kafka-compose.yml").getPath)
  )

  val kafkaConnection = s"localhost:9092"

  val UserSyncMappingClient = mock[UserSyncMappingClient]
  val networkService = mock[BasicNetworkService]
  val userService = mock[UserService]
  val testSystemUser = TestUserACL("123", NetworkId("SIMON Admin"))
  val transientStateRepository = mock[TransientStateRepository]

  val adminClientSettings = AdminClientSettings(List(kafkaConnection))
  val adminLayer: ZLayer[Any, Throwable, AdminClient] = ZLayer.scoped {
    AdminClient.make(adminClientSettings)
  }
  val createTopicEff: Task[Unit] = {
    for {
      adminClient <- ZIO.service[AdminClient]
      _ <- adminClient.createTopic(NewTopic(topic, 1, 1))
    } yield ()
  }.provideLayer(adminLayer)

  private val serializers = Serializers.json[IcnUserEvent]

  lazy val collection = db.getCollection[Document]("users")
  lazy val snapshotCollection = db.getCollection[Document]("users_test.snapshots")
  lazy val repo = new MongoUserRepository(client, collection, snapshotCollection, useSnapshots = false)(global)

  val validHeaders: Array[Header] = Array(
    new RecordHeader("version", "1.0".getBytes),
    new RecordHeader("source", "icn".getBytes),
    new RecordHeader("type", "user_updated".getBytes),
    new RecordHeader("guid", "someGuid".getBytes)
  )

  def icnUserEventToProducerRecord(
      icnUserEvent: IcnUserEvent,
      headers: Array[Header]
  ): ProducerRecord[String, IcnUserEvent] = {

    val recordHeaders = new RecordHeaders(headers)

    new ProducerRecord(
      topic,
      null,
      Instant.now.toEpochMilli,
      icnUserEvent.user_id.toString,
      icnUserEvent,
      recordHeaders
    )
  }

  def produceEventEff(record: ProducerRecord[String, IcnUserEvent]) =
    ZIO.serviceWithZIO[Producer](_.produce(record, serializers.key, serializers.value))

  def produceNonEventEff =
    ZIO.serviceWithZIO[Producer](_.produce(topic, "test", s"test-123", serializers.key, serializers.key))

  val icnUserEventProducer: ZLayer[Any, Throwable, Producer] = ZLayer.scoped {
    Producer.make(ProducerSettings(List(kafkaConnection)))
  }

  implicit val icnUserEventDecoder: JsonDecoder[IcnUserEvent] = DeriveJsonDecoder.gen[IcnUserEvent]

  val consumer = for {
    consumer <- ZIO.service[ZConsumer]
    action <- ZIO.serviceWith[UserEventConsumer](_.consume)
    _ <- consumer.run[UserEventConsumer, Any, Any, String, IcnUserEvent](
      deserializers = Deserializers.json[Any, IcnUserEvent],
      consume = action,
      consumeRetryPolicy = Schedule.exponential(10.millis),
      commitRetryPolicy = Schedule.exponential(10.millis),
      handleDecodeError = (k, v) => ZIO.logError(s"couldn't decode key=$k value=$v")
    )
  } yield ()

  val zConfig = ConsumerConfig(
    GroupId("test"),
    ClientId(""),
    Set(TopicName(topic)),
    KafkaConnectionConfig.NoAuth(List(kafkaConnection))
  )

  val zConsumerLayer = ZLayer.succeed(zConfig) >>> ZConsumer.configured
  val mockMappingClient = mock[UserSyncMappingClient]

  val service: TaskLayer[UserEventConsumer] = ZLayer.make[UserEventConsumer](
    ZLayer.succeed(mockMappingClient),
    ZLayer.succeed(userService),
    ZLayer.succeed(testSystemUser),
    ZLayer.succeed(networkService),
    ZLayer.succeed(transientStateRepository),
    UserEventConsumer.live
  )

  def processEventEff(event: IcnUserEvent) = {
    for {
      syncedUser <- ZIO.serviceWithZIO[UserEventConsumer](
        _.sync(
          ZConsumer.Record(
            key = event.user_id.toString,
            value = event,
            headers = Seq(
              UserEventConsumer.ICN_SOURCE_HEADER,
              UserEventConsumer.VERSION_HEADER,
              UserEventConsumer.UPDATED_TYPE_HEADER
            ),
            offset = 0L
          )
        )
      )
    } yield syncedUser
  }.provideLayer(service)

  override def beforeEach(): Unit = {
    collection.drop().toFuture().await
    snapshotCollection.drop().toFuture().await
    reset(networkService)
    reset(mockMappingClient)
    reset(userService)
  }

  "UserEventConsumer" should {
    "sync only specified fields on mapping" should {
      "'default' set of sync fields (first name, last name)" should {
        "create new user" in new UserEventConsumerSpecData {
          val syncEvent: IcnUserEvent = icnUserEvent.copy(
            user_external_ids = Some(icnExternalIds),
            user_groups = Some(Set("1212"))
          )
          val newUser: User = TestUser(
            "test-123",
            iCapitalUserId = syncEvent.user_id.toString.some,
            firmId = syncEvent.firm_id.map(_.toString),
            whiteLabelPartnerId = icnUserEvent.white_label_partner_id.toString.some
          )

          // groups, external ids not getting synced
          val request = baseUpsert.copy(
            locations = Set(defaultMapping.destination_secondary.get.external_id).some,
            firmId = newUser.firmId,
            whiteLabelPartnerId = newUser.whiteLabelPartnerId,
            iCapitalUserId = newUser.iCapitalUserId,
            upsertField = Some(UpsertField.Email)
          )

          val getUniqueUserIdRequest = GetUniqueUserIdRequest(request)

          when(mockMappingClient.get(meq(mappingRequest(syncEvent)))(any[TraceId]))
            .thenReturn(Future.successful(Some(defaultMapping.copy(entitlements = entitlementMap))))
          when(networkService.unentitledGetNetworkById(meq(testNetwork.id))(any[TraceId]))
            .thenReturn(Future(TestNetwork()))
          when(
            userService.getUserByUniqueUserId(meq(testSystemUser), meq(getUniqueUserIdRequest), meq(UpsertField.Email))(
              any[TraceId]
            )
          ).thenReturn(Future.successful((None, Email(icnUserEvent.email, testNetwork.id))))
          when(
            userService.handleInsertRequest(meq(testSystemUser), meq(request), meq(UserDomainEvent.UserSynced.some))(
              any[TraceId]
            )
          ).thenReturn(Future.successful((newUser, None)))

          Unsafe.unsafe { implicit unsafe =>
            val user = runtime.unsafe.run(processEventEff(syncEvent)).getOrThrow()
            user shouldBe newUser
          }
        }

        "update existing unsynced user's default sync fields" in new UserEventConsumerSpecData {
          val syncEvent: IcnUserEvent = icnUserEvent.copy(
            user_external_ids = Some(icnExternalIds),
            user_groups = Some(Set("1212"))
          )

          val updatedUser = existingUnsyncedUser.copy(
            iCapitalUserId = Some(syncEvent.user_id.toString),
            firmId = syncEvent.firm_id.map(_.toString),
            whiteLabelPartnerId = syncEvent.white_label_partner_id.toString.some,
            customRoles = existingUnsyncedUser.customRoles,
            locations = Set(defaultMapping.destination_secondary.get.external_id) ++ existingUnsyncedUser
              .locations
          )
          val updateRequest = UpsertUserRequest
            .fromUser(updatedUser)
            .copy(
              upsertField = Some(UpsertField.Email)
            )

          val getUniqueUserIdRequest = GetUniqueUserIdRequest(updateRequest).copy(id = None)

          when(networkService.unentitledGetNetworkById(meq(testNetwork.id))(any[TraceId]))
            .thenReturn(Future(testNetwork))
          when(mockMappingClient.get(meq(mappingRequest(syncEvent)))(any[TraceId]))
            .thenReturn(Future.successful(Some(defaultMapping.copy(entitlements = entitlementMap))))
          when(
            userService.getUserByUniqueUserId(meq(testSystemUser), meq(getUniqueUserIdRequest), meq(UpsertField.Email))(
              any[TraceId]
            )
          ).thenReturn(Future.successful((Some(existingUnsyncedUser), Guid(existingUnsyncedUser.id))))
          when(
            userService
              .handleInsertRequest(meq(testSystemUser), meq(updateRequest), meq(UserDomainEvent.UserSynced.some))(
                any[TraceId]
              )
          ).thenReturn(Future.successful((updatedUser, None)))

          Unsafe.unsafe { implicit unsafe =>
            val user = runtime.unsafe.run(processEventEff(syncEvent)).getOrThrow()
            user shouldBe updatedUser
          }
        }
      }

      "sync field groups + default sync fields" should {
        "create new user with groups" in new UserEventConsumerSpecData {
          val newUser = TestUser(
            "test-123",
            email = icnUserEvent.email,
            iCapitalUserId = icnUserEvent.user_id.toString.some,
            firmId = icnUserEvent.firm_id.map(_.toString),
            whiteLabelPartnerId = icnUserEvent.white_label_partner_id.toString.some,
            icnGroups = icnUserEvent.user_groups.map(_.map(_.toString)).getOrElse(Set.empty)
          )

          val request = baseUpsert.copy(
            locations = Set(defaultMapping.destination_secondary.get.external_id).some,
            firmId = newUser.firmId,
            whiteLabelPartnerId = newUser.whiteLabelPartnerId,
            iCapitalUserId = newUser.iCapitalUserId,
            upsertField = Some(UpsertField.Email),
            icnGroups = icnUserEvent.user_groups.map(_.map(_.toString)),
            customRoles = Set(architectCustomRole, unifiedEducationCustomRole).some
          )

          val getUniqueUserIdRequest = GetUniqueUserIdRequest(request)

          when(mockMappingClient.get(meq(mappingRequest(icnUserEvent)))(any[TraceId])).thenReturn(
            Future.successful(Some(mappingWithDefaultSyncFieldsAndGroups.copy(entitlements = entitlementMap)))
          )
          when(networkService.unentitledGetNetworkById(meq(testNetwork.id))(any[TraceId]))
            .thenReturn(Future(TestNetwork()))
          when(
            userService.getUserByUniqueUserId(meq(testSystemUser), meq(getUniqueUserIdRequest), meq(UpsertField.Email))(
              any[TraceId]
            )
          ).thenReturn(Future.successful((None, Email(icnUserEvent.email, testNetwork.id))))
          when(
            userService.handleInsertRequest(meq(testSystemUser), meq(request), meq(UserDomainEvent.UserSynced.some))(
              any[TraceId]
            )
          ).thenReturn(Future.successful((newUser, None)))

          Unsafe.unsafe { implicit unsafe =>
            val user: User = runtime.unsafe.run(processEventEff(icnUserEvent)).getOrThrow()
            user shouldBe newUser
          }
        }

        "update existing unsynced user except for custom role, when icn user does not have group(s) and mapping has " +
          "entitlements" in new UserEventConsumerSpecData {
          val syncEvent = icnUserEvent.copy(user_groups = None)
          val updatedUser = existingUnsyncedUser.copy(
            iCapitalUserId = Some(icnUserEvent.user_id.toString),
            firmId = icnUserEvent.firm_id.map(_.toString),
            whiteLabelPartnerId = icnUserEvent.white_label_partner_id.toString.some,
            customRoles = existingUnsyncedUser.customRoles,
            locations =
              Set(defaultMapping.destination_secondary.get.external_id) ++ existingUnsyncedUser
                .locations,
            icnGroups = syncEvent.user_groups.getOrElse(Set.empty)
          )

          val updateRequest = UpsertUserRequest
            .fromUser(updatedUser)
            .copy(
              upsertField = Some(UpsertField.Email)
            )

          val getUniqueUserIdRequest = GetUniqueUserIdRequest(updateRequest).copy(id = None)

          when(networkService.unentitledGetNetworkById(meq(testNetwork.id))(any[TraceId]))
            .thenReturn(Future(testNetwork))
          when(mockMappingClient.get(meq(mappingRequest(syncEvent)))(any[TraceId]))
            .thenReturn(Future.successful(Some(mappingWithDefaultSyncFieldsAndGroups)))
          when(
            userService
              .getUserByUniqueUserId(meq(testSystemUser), meq(getUniqueUserIdRequest), meq(UpsertField.Email))(
                any[TraceId]
              )
          ).thenReturn(Future.successful((Some(existingUnsyncedUser), Guid(existingUnsyncedUser.id))))
          when(
            userService
              .handleInsertRequest(meq(testSystemUser), meq(updateRequest), meq(UserDomainEvent.UserSynced.some))(
                any[TraceId]
              )
          ).thenReturn(Future.successful((updatedUser, None)))

          Unsafe.unsafe { implicit unsafe =>
            val user = runtime.unsafe.run(processEventEff(syncEvent)).getOrThrow()
            user shouldBe updatedUser
          }
        }

        "update a previously synced user's icnGroups" in new UserEventConsumerSpecData {
          val newFirstName = "joe"
          val syncEvent = icnUserEvent.copy(simon_user_id = Some(testUserId), first_name = newFirstName)
          val userGroupsStr = syncEvent.user_groups
          val updatedUser = alreadySyncedUser.copy(
            firstName = newFirstName,
            firmId = syncEvent.firm_id.map(_.toString),
            whiteLabelPartnerId = syncEvent.white_label_partner_id.toString.some,
            customRoles = alreadySyncedUser.customRoles ++ Set(architectCustomRole, unifiedEducationCustomRole),
            icnGroups = alreadySyncedUser.icnGroups ++ userGroupsStr.getOrElse(Set.empty),
            locations = Set(defaultMapping.destination_secondary.get.external_id) ++ existingUnsyncedUser
              .locations
          )

          val updateRequest = UpsertUserRequest
            .fromUser(updatedUser)
            .copy(
              upsertField = Some(UpsertField.UserId),
              icnGroups = userGroupsStr
            )

          val getUniqueUserIdRequest = GetUniqueUserIdRequest(updateRequest).copy(email = syncEvent.email)

          when(networkService.unentitledGetNetworkById(meq(testNetwork.id))(any[TraceId]))
            .thenReturn(Future(testNetwork))
          when(mockMappingClient.get(meq(mappingRequest(syncEvent)))(any[TraceId])).thenReturn(
            Future.successful(Some(mappingWithDefaultSyncFieldsAndGroups.copy(entitlements = entitlementMap)))
          )
          when(
            userService
              .getUserByUniqueUserId(meq(testSystemUser), meq(getUniqueUserIdRequest), meq(UpsertField.UserId))(
                any[TraceId]
              )
          ).thenReturn(Future.successful((Some(alreadySyncedUser), Guid(alreadySyncedUser.id))))
          when(
            userService
              .handleInsertRequest(meq(testSystemUser), meq(updateRequest), meq(UserDomainEvent.UserSynced.some))(
                any[TraceId]
              )
          ).thenReturn(Future.successful((updatedUser, None)))

          Unsafe.unsafe { implicit unsafe =>
            val user = runtime.unsafe.run(processEventEff(syncEvent)).getOrThrow()
            user shouldBe updatedUser
          }
        }

        "not update a previously synced user's groups if mapping does not have sync field groups enabled" in new
            UserEventConsumerSpecData {
          val newFirstName = "joe"
          val syncEvent = icnUserEvent.copy(simon_user_id = Some(testUserId), first_name = newFirstName)
          val syncedUser = alreadySyncedUser.copy(
            firstName = newFirstName,
            firmId = syncEvent.firm_id.map(_.toString),
            whiteLabelPartnerId = syncEvent.white_label_partner_id.toString.some,
            locations =
              Set(defaultMapping.destination_secondary.get.external_id) ++ existingUnsyncedUser.locations,
            icnGroups = Set("1133"),
            customRoles = alreadySyncedUser.customRoles ++ Set(architectCustomRole, unifiedEducationCustomRole)
          )

          val updateRequest = UpsertUserRequest
            .fromUser(syncedUser)
            .copy(
              upsertField = Some(UpsertField.UserId),
              icnGroups = Some(syncedUser.icnGroups)
            )

          val getUniqueUserIdRequest = GetUniqueUserIdRequest(updateRequest).copy(email = syncEvent.email)

          when(networkService.unentitledGetNetworkById(meq(testNetwork.id))(any[TraceId]))
            .thenReturn(Future(testNetwork))
          when(mockMappingClient.get(meq(mappingRequest(syncEvent)))(any[TraceId])).thenReturn(
            Future.successful(Some(defaultMapping.copy(entitlements = entitlementMap)))
          ) // mapping has entitlements but does not sync icn groups
          when(
            userService
              .getUserByUniqueUserId(meq(testSystemUser), meq(getUniqueUserIdRequest), meq(UpsertField.UserId))(
                any[TraceId]
              )
          ).thenReturn(Future.successful((Some(syncedUser), Guid(syncedUser.id))))
          when(
            userService
              .handleInsertRequest(meq(testSystemUser), meq(updateRequest), meq(UserDomainEvent.UserSynced.some))(
                any[TraceId]
              )
          ).thenReturn(Future.successful((syncedUser, None)))

          Unsafe.unsafe { implicit unsafe =>
            val user = runtime.unsafe.run(processEventEff(syncEvent)).getOrThrow()
            user shouldBe syncedUser
          }
        }
      }

      "sync field groups + custom roles + default sync fields" should {
        "create new user with custom roles and groups" in new UserEventConsumerSpecData {
          val newUser = TestUser(
            "test-123",
            email = icnUserEvent.email,
            iCapitalUserId = icnUserEvent.user_id.toString.some,
            firmId = icnUserEvent.firm_id.map(_.toString),
            whiteLabelPartnerId = icnUserEvent.white_label_partner_id.toString.some,
            icnGroups = icnUserEvent.user_groups.getOrElse(Set.empty),
            customRoles = mappedCustomRoles
          )

          val request = baseUpsert.copy(
            locations = Set(defaultMapping.destination_secondary.get.external_id).some,
            firmId = newUser.firmId,
            whiteLabelPartnerId = newUser.whiteLabelPartnerId,
            iCapitalUserId = newUser.iCapitalUserId,
            upsertField = Some(UpsertField.Email),
            icnGroups = icnUserEvent.user_groups,
            customRoles = Some(mappedCustomRoles)
          )

          val getUniqueUserIdRequest = GetUniqueUserIdRequest(request)

          when(mockMappingClient.get(meq(mappingRequest(icnUserEvent)))(any[TraceId])).thenReturn(
            Future.successful(
              Some(
                mappingWithDefaultSyncFieldsAndGroups
                  .copy(entitlements = entitlementMap, fields = mappingWithDefaultSyncFieldsAndGroups.fields)
              )
            )
          )
          when(networkService.unentitledGetNetworkById(meq(testNetwork.id))(any[TraceId]))
            .thenReturn(Future(TestNetwork()))
          when(
            userService.getUserByUniqueUserId(meq(testSystemUser), meq(getUniqueUserIdRequest), meq(UpsertField.Email))(
              any[TraceId]
            )
          ).thenReturn(Future.successful((None, Email(icnUserEvent.email, testNetwork.id))))
          when(
            userService.handleInsertRequest(meq(testSystemUser), meq(request), meq(UserDomainEvent.UserSynced.some))(
              any[TraceId]
            )
          ).thenReturn(Future.successful((newUser, None)))

          Unsafe.unsafe { implicit unsafe =>
            val user: User = runtime.unsafe.run(processEventEff(icnUserEvent)).getOrThrow()
            user shouldBe newUser
          }
        }

        // icn user had simon_user_id field populated from previous sync
        "update a previously synced user's custom roles and icnGroups when mapping has entitlements" in new
            UserEventConsumerSpecData {
          val newFirstName = "joe"
          val syncEvent = icnUserEvent.copy(simon_user_id = Some(testUserId), first_name = newFirstName)
          val userGroupsStr = syncEvent.user_groups
          val updatedUser = alreadySyncedUser.copy(
            firstName = newFirstName,
            firmId = syncEvent.firm_id.map(_.toString),
            whiteLabelPartnerId = syncEvent.white_label_partner_id.toString.some,
            customRoles = alreadySyncedUser.customRoles ++ mappedCustomRoles,
            icnGroups = alreadySyncedUser.icnGroups ++ userGroupsStr.getOrElse(Set.empty),
            locations = Set(defaultMapping.destination_secondary.get.external_id) ++ existingUnsyncedUser
              .locations
          )

          val updateRequest = UpsertUserRequest
            .fromUser(updatedUser)
            .copy(
              upsertField = Some(UpsertField.UserId),
              icnGroups = userGroupsStr
            )

          val getUniqueUserIdRequest = GetUniqueUserIdRequest(updateRequest).copy(email = syncEvent.email)

          when(networkService.unentitledGetNetworkById(meq(testNetwork.id))(any[TraceId]))
            .thenReturn(Future(testNetwork))
          when(mockMappingClient.get(meq(mappingRequest(icnUserEvent)))(any[TraceId])).thenReturn(
            Future.successful(
              Some(
                mappingWithDefaultSyncFieldsAndGroups
                  .copy(entitlements = entitlementMap, fields = mappingWithDefaultSyncFieldsAndGroups.fields)
              )
            )
          )
          when(
            userService
              .getUserByUniqueUserId(meq(testSystemUser), meq(getUniqueUserIdRequest), meq(UpsertField.UserId))(
                any[TraceId]
              )
          ).thenReturn(Future.successful((Some(alreadySyncedUser), Guid(alreadySyncedUser.id))))
          when(
            userService
              .handleInsertRequest(meq(testSystemUser), meq(updateRequest), meq(UserDomainEvent.UserSynced.some))(
                any[TraceId]
              )
          ).thenReturn(Future.successful((updatedUser, None)))

          Unsafe.unsafe { implicit unsafe =>
            val user = runtime.unsafe.run(processEventEff(syncEvent)).getOrThrow()
            user shouldBe updatedUser
          }
        }
      }

      "sync field external ids + default sync fields" should {
        "create new user with external ids" in new UserEventConsumerSpecData {
          val syncEvent = icnUserEvent.copy(user_external_ids = Some(icnExternalIds))
          val newUser = TestUser(
            "test-123",
            email = syncEvent.email,
            firstName = syncEvent.first_name,
            lastName = syncEvent.last_name,
            iCapitalUserId = syncEvent.user_id.toString.some,
            firmId = syncEvent.firm_id.map(_.toString),
            whiteLabelPartnerId = syncEvent.white_label_partner_id.toString.some,
            externalIds = icnExternalIds.map(_.toSimonExternalId)
          )

          val request = baseUpsert.copy(
            id = None,
            upsertField = Some(UpsertField.ExternalId("test_external")),
            externalIds = ExternalId.toStringMap(newUser.externalIds),
            iCapitalUserId = syncEvent.user_id.toString.some,
            firmId = syncEvent.firm_id.map(_.toString),
            whiteLabelPartnerId = syncEvent.white_label_partner_id.toString.some
          )

          val getUniqueUserIdRequest = GetUniqueUserIdRequest(request)
            .copy(externalIds = ExternalId.toStringMap(Seq(mappedIcnExternal.toSimonExternalId)))

          when(mockMappingClient.get(meq(mappingRequest(syncEvent)))(any[TraceId]))
            .thenReturn(Future.successful(Some(mappingWithDefaultSyncFieldsAndExternalIds)))
          when(networkService.unentitledGetNetworkById(meq(testNetwork.id))(any[TraceId]))
            .thenReturn(Future(TestNetwork()))
          when(
            userService.getUserByUniqueUserId(
              meq(testSystemUser),
              meq(getUniqueUserIdRequest),
              meq(UpsertField.ExternalId("test_external"))
            )(
              any[TraceId]
            )
          ).thenReturn(Future.successful((None, Email(icnUserEvent.email, testNetwork.id))))
          when(
            userService.handleInsertRequest(meq(testSystemUser), meq(request), meq(UserDomainEvent.UserSynced.some))(
              any[TraceId]
            )
          ).thenReturn(Future.successful((newUser, None)))

          Unsafe.unsafe { implicit unsafe =>
            val user: User = runtime.unsafe.run(processEventEff(syncEvent)).getOrThrow()
            user shouldBe newUser
          }
        }
      }
      "sync all fields" should {
        "create new user with all sync fields (email, first and last name, external ids, icnGroups, icnRoles, custom " +
          "roles)" in new UserEventConsumerSpecData {
          val syncEvent: IcnUserEvent = icnUserEvent.copy(
            user_external_ids = Some(icnExternalIds),
            user_groups = Some(Set("1111")),
            user_roles = Some(Set("12123"))
          )
          val icnRoles = icnUserEvent.user_roles
          val icnGroups = Some(Set("1111"))
          val externalIds = ExternalId.toStringMap(Seq(mappedIcnExternal.toSimonExternalId))

          val newUser: User = TestUser(
            "test-123",
            iCapitalUserId = icnUserEvent.user_id.toString.some,
            firmId = icnUserEvent.firm_id.map(_.toString),
            whiteLabelPartnerId = icnUserEvent.white_label_partner_id.toString.some,
            icnRoles = icnRoles.getOrElse(Set.empty),
            icnGroups = icnGroups.getOrElse(Set.empty),
            externalIds = ExternalId.fromStringMap(externalIds)
          )

          // groups, external ids being synced
          val request = baseUpsert.copy(
            firmId = newUser.firmId,
            whiteLabelPartnerId = newUser.whiteLabelPartnerId,
            iCapitalUserId = newUser.iCapitalUserId,
            icnRoles = icnRoles,
            icnGroups = icnGroups,
            externalIds = ExternalId.toStringMap(icnExternalIds.map(_.toSimonExternalId)),
            customRoles = Some(Set(architectEntitlement.destination_entitlement)),
            upsertField = Some(UpsertField.ExternalId("test_external"))
          )

          val getUniqueUserIdRequest = GetUniqueUserIdRequest(request)
            .copy(externalIds = ExternalId.toStringMap(Seq(mappedIcnExternal.toSimonExternalId)))

          when(mockMappingClient.get(meq(mappingRequest(syncEvent)))(any[TraceId]))
            .thenReturn(Future.successful(Some(mappingWithAllSyncFields.copy(entitlements = entitlementMap))))
          when(networkService.unentitledGetNetworkById(meq(testNetwork.id))(any[TraceId]))
            .thenReturn(Future(TestNetwork()))
          when(
            userService.getUserByUniqueUserId(
              meq(testSystemUser),
              meq(getUniqueUserIdRequest),
              meq(UpsertField.ExternalId("test_external"))
            )(
              any[TraceId]
            )
          ).thenReturn(Future.successful((None, Email(icnUserEvent.email, testNetwork.id))))
          when(transientStateRepository.upsert(any[TransientState])(any[ExecutionContext])).thenReturn(Future.unit)
          when(
            userService.handleInsertRequest(meq(testSystemUser), meq(request), meq(UserDomainEvent.UserSynced.some))(
              any[TraceId]
            )
          ).thenReturn(Future.successful((newUser, None)))

          Unsafe.unsafe { implicit unsafe =>
            val user = runtime.unsafe.run(processEventEff(syncEvent)).getOrThrow()
            user shouldBe newUser
          }
        }

        "update a previously synced user with all sync fields (email, first and last name, external ids, icnGroups, icnRoles, customRoles)" in new UserEventConsumerSpecData {
          val syncEvent: IcnUserEvent = icnUserEvent.copy(
            user_external_ids = Some(icnExternalIds),
            user_roles = Some(Set("12123")),
            simon_user_id = alreadySyncedUser.id.some,
            phone = Some("encryptedPhone")
          )
          val icnRoles = syncEvent.user_roles
          val icnGroups = syncEvent.user_groups

          val existingExternalIds = ExternalId("someSubject", "id1")
          val alreadySyncedUserWithExternalIds: User = alreadySyncedUser.copy(externalIds = Seq(existingExternalIds))

          val updatedUser = alreadySyncedUserWithExternalIds.copy(
            icnRoles = icnRoles.getOrElse(Set.empty),
            icnGroups = icnGroups.getOrElse(Set.empty),
            externalIds = Seq(existingExternalIds) ++ ExternalId.fromStringMap(syncEvent.simonExternalIdMap),
            customRoles = alreadySyncedUserWithExternalIds.customRoles ++ mappedCustomRoles,
            mfas = Map(FactorType.sms.productPrefix -> Factor(syncEvent.phone))
          )

          // groups, external ids being synced
          val request = UpsertUserRequest
            .fromUser(alreadySyncedUserWithExternalIds)
            .copy(
              email = syncEvent.email,
              icnRoles = icnRoles,
              icnGroups = icnGroups,
              externalIds = ExternalId.toStringMap(Seq(existingExternalIds) ++ ExternalId.fromStringMap(syncEvent.simonExternalIdMap)),
              customRoles = Some(alreadySyncedUserWithExternalIds.customRoles ++ mappedCustomRoles),
              upsertField = UpsertField.UserId.some,
              mfas = Map(FactorType.sms -> Factor(syncEvent.phone))
            )

          val getUniqueUserIdRequest = GetUniqueUserIdRequest(request).copy(
            email = syncEvent.email,
            externalIds = Map.empty
          )

          when(mockMappingClient.get(meq(mappingRequest(syncEvent)))(any[TraceId]))
            .thenReturn(Future.successful(Some(mappingWithAllSyncFields.copy(entitlements = entitlementMap))))
          when(networkService.unentitledGetNetworkById(meq(testNetwork.id))(any[TraceId]))
            .thenReturn(Future(TestNetwork()))
          when(
            userService
              .getUserByUniqueUserId(meq(testSystemUser), meq(getUniqueUserIdRequest), meq(UpsertField.UserId))(
                any[TraceId]
              )
          ).thenReturn(
            Future.successful((alreadySyncedUserWithExternalIds.some, Guid(alreadySyncedUserWithExternalIds.id)))
          )
          when(
            userService.handleInsertRequest(meq(testSystemUser), meq(request), meq(UserDomainEvent.UserSynced.some))(
              any[TraceId]
            )
          ).thenReturn(Future.successful((updatedUser, None)))

          Unsafe.unsafe { implicit unsafe =>
            val user = runtime.unsafe.run(processEventEff(syncEvent)).getOrThrow()
            user shouldBe updatedUser
          }
        }
      }
    }

    "match by property external id" should {
      "throw an error when icn user event has no external id to search with" in new UserEventConsumerSpecData {
        val newFirst = "newFirst"
        val icnUserEventWithExternal = icnUserEvent
          .copy(first_name = newFirst, user_external_ids = Some(icnExternalIds.tail))
        when(networkService.unentitledGetNetworkById(meq(testNetwork2.id))(any[TraceId]))
          .thenReturn(Future(testNetwork2))
        when(mockMappingClient.get(meq(mappingRequest(icnUserEventWithExternal)))(any[TraceId]))
          .thenReturn(Future.successful(Some(mapping2)))

        Unsafe.unsafe { implicit unsafe =>
          a[SyncError] shouldBe thrownBy {
            runtime
              .unsafe
              .run(processEventEff(icnUserEventWithExternal))
              .getOrThrow()
          }
        }
      }

    }

    "throw an error when icn user event has simon user id populated but no user found" in new UserEventConsumerSpecData {
      val nonExistentUserId = "nonExistentUserId"
      val testEvent = icnUserEvent.copy(simon_user_id = Some(nonExistentUserId))
      val getUniqueUserIdRequest = GetUniqueUserIdRequest(
        id = testEvent.simon_user_id,
        email = testEvent.email,
        networkId = testNetwork.id,
        externalIds = Map.empty
      )
      when(networkService.unentitledGetNetworkById(meq(testNetwork.id))(any[TraceId])).thenReturn(Future(testNetwork))
      when(mockMappingClient.get(meq(mappingRequest(testEvent)))(any[TraceId]))
        .thenReturn(Future.successful(Some(defaultMapping)))
      when(
        userService.getUserByUniqueUserId(meq(testSystemUser), meq(getUniqueUserIdRequest), meq(UpsertField.UserId))(
          any[TraceId]
        )
      ).thenReturn(Future.successful((None, Email(icnUserEvent.email, testNetwork.id))))
      when(mockMappingClient.get(meq(mappingRequest(testEvent)))(any[TraceId])).thenReturn(
        Future.successful(Some(defaultMapping.copy(entitlements = entitlementMap)))
      ) // mapping has entitlements but does not sync icn groups

      Unsafe.unsafe { implicit unsafe =>
        a[SyncError.NotFound] shouldBe thrownBy(runtime.unsafe.run(processEventEff(testEvent)).getOrThrow())
      }
    }

    "user not upserted when mapping not found from mapping client" in new UserEventConsumerSpecData {
      val testEvent = icnUserEvent.copy(white_label_partner_id = 200, firm_id = Some(1000))
      when(mockMappingClient.get(meq(mappingRequest(testEvent)))(any[TraceId])).thenReturn(Future.successful(None))

      Unsafe.unsafe { implicit unsafe =>
        a[SyncError.Skipped] shouldBe thrownBy(runtime.unsafe.run(processEventEff(testEvent)).getOrThrow())
      }
    }

    "ignore messages that are not valid icn user events" should {
      "ignore non json message" in {
        val produceAndConsumeEff = {
          for {
            _ <- createTopicEff
            f <- consumer.forkDaemon
            _ <- produceNonEventEff
            _ <- f.join
          } yield ()
        }.provideLayer(icnUserEventProducer ++ service ++ (ZLayer.succeed(Scope.global) >>> zConsumerLayer))
        Unsafe.unsafe { implicit unsafe =>
          runtime.unsafe.run(produceAndConsumeEff.timeout(35.seconds))
          verify(networkService, times(0)).getNetworkById(any[UserACL], any[NetworkId])(any[TraceId])
        }
      }

      "ignore messages with incorrect headers" in new UserEventConsumerSpecData {
        val produceAndConsumeEff = {
          for {
            _ <- createTopicEff
            f <- consumer.forkDaemon
            _ <- produceEventEff(icnUserEventToProducerRecord(icnUserEvent, Array()))
            _ <- produceEventEff(
              icnUserEventToProducerRecord(
                icnUserEvent,
                Array(
                  new RecordHeader("source", "icn".getBytes) // missing type and version
                )
              )
            )
            _ <- produceEventEff(
              icnUserEventToProducerRecord(
                icnUserEvent,
                Array(new RecordHeader("source", "test".getBytes), new RecordHeader("type", "user_updated".getBytes))
              )
            ) // incorrect source and missing version
            _ <- produceEventEff(
              icnUserEventToProducerRecord(
                icnUserEvent,
                Array(
                  new RecordHeader("source", "icn".getBytes),
                  new RecordHeader("type", "user".getBytes)
                )
              )
            ) // incorrect type and missing version
            _ <- f.join
          } yield ()
        }.provideLayer(icnUserEventProducer ++ service ++ (ZLayer.succeed(Scope.global) >>> zConsumerLayer))

        Unsafe.unsafe { implicit unsafe =>
          runtime.unsafe.run(produceAndConsumeEff.timeout(35.seconds))
          verify(networkService, times(0)).getNetworkById(any[UserACL], any[NetworkId])(any[TraceId])
        }
      }
    }
  }
}
