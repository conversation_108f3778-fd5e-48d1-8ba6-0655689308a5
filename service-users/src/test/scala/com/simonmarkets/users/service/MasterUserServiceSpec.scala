package com.simonmarkets.users.service

import com.goldmansachs.marquee.pipg.{EmbeddingInfo, SSOPrefix}
import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.networks.common.repository.NetworkRepository
import com.simonmarkets.syntax._
import com.simonmarkets.users.api.response.MasterUser
import com.simonmarkets.users.common.{LandingPage, LoginMode, User, UserType}
import com.simonmarkets.users.config.DomainConfig
import com.simonmarkets.users.repository.UserRepository
import com.simonmarkets.users.repository.filters.EmailFilter
import com.simonmarkets.users.{TestNetwork, TestUser}
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future
import scala.util.{Failure, Success}

class MasterUserServiceSpec extends WordSpec with Matchers with MockitoSugar {

  "simon/api/v2/master-user" should {
    "return master user when multiple users with given email exist in the system" in new Scope {

      when(userRepo.getUsers(Set(EmailFilter(email1)))(admin))
        .thenReturn(List(user1, user2).successFuture)
      when(networkRepo.getByIds(Set(user1.networkId, user2.networkId))(admin))
        .thenReturn(List(network1, network2).successFuture)


      val actualMasterUser: MasterUser = masterUserService.getMasterUser(email1, None).await

      actualMasterUser shouldBe expectedMasterUserMulti
    }

    "return master user when Preview User is filtered out" in new Scope {

      when(userRepo.getUsers(Set(EmailFilter(previewUserEmail)))(admin))
        .thenReturn(List(user1, user2, previewUser).successFuture)
      when(networkRepo.getByIds(Set(user1.networkId, user2.networkId))(admin))
        .thenReturn(List(network1, network2).successFuture)


      val actualMasterUser: MasterUser = masterUserService.getMasterUser(previewUserEmail, None).await

      actualMasterUser shouldBe expectedMasterUserMultiPreviewUser
    }

    "return master user when single user with given email exist in the system" in new Scope {

      when(userRepo.getUsers(Set(EmailFilter(email2)))(admin))
        .thenReturn(List(user3).successFuture)
      when(networkRepo.getByIds(Set(user3.networkId))(admin))
        .thenReturn(List(network3).successFuture)


      val actualMasterUser: MasterUser = masterUserService.getMasterUser(email2, None).await

      actualMasterUser shouldBe expectedMasterUserSingle

    }

    "return master user when single user with given email and wlpId exist in the system" in new Scope {
      when(userRepo.getUsers(Set(EmailFilter(email2)))(admin))
        .thenReturn(List(user8).successFuture)
      when(networkRepo.getByIds(Set(user8.networkId))(admin))
        .thenReturn(List(network3).successFuture)

      val actualMasterUser: MasterUser = masterUserService.getMasterUser(email2, wlp1.some, Some(icnHost)).await

      actualMasterUser shouldBe expectedMasterUserSingleIcapitalNetwork
    }

    "return master user with user's landing page" in new Scope {
      val user: User = user3.copy(landingPage = Some(LandingPage.SIMON))
      when(userRepo.getUsers(Set(EmailFilter(email2)))(admin))
        .thenReturn(List(user).successFuture)
      when(networkRepo.getByIds(Set(user.networkId))(admin))
        .thenReturn(List(network3).successFuture)

      val actualMasterUser: MasterUser = masterUserService.getMasterUser(email2, None).await

      actualMasterUser shouldBe expectedMasterUserSingle
      actualMasterUser.users.head.landingPage shouldBe user.landingPage.get
    }

    "return master user with network's landing page when user's landing page is empty" in new Scope {
      val network: Network = network3.copy(landingPage = Some(LandingPage.SIMON))
      when(userRepo.getUsers(Set(EmailFilter(email2)))(admin))
        .thenReturn(List(user3.copy(landingPage = None)).successFuture)
      when(networkRepo.getByIds(Set(user3.networkId))(admin))
        .thenReturn(List(network).successFuture)

      val actualMasterUser: MasterUser = masterUserService.getMasterUser(email2, None).await

      actualMasterUser shouldBe expectedMasterUserSingle
      actualMasterUser.users.head.landingPage shouldBe network.landingPage.get
    }

    "return master user after filtering active users when multiple user with given email exist in the system" in new Scope {

      when(userRepo.getUsers(Set(EmailFilter(email3)))(admin))
        .thenReturn(List(user4, user5).successFuture)
      when(networkRepo.getByIds(Set(user4.networkId))(admin))
        .thenReturn(List(network4).successFuture)


      val actualMasterUser: MasterUser = masterUserService.getMasterUser(email3, None).await

      actualMasterUser shouldBe expectedMasterUserActive
    }

    "return random master user when no users with given email exist" in new Scope {

      when(userRepo.getUsers(Set(EmailFilter(emailNotFound)))(admin))
        .thenReturn(List().successFuture)
      when(networkRepo.getByIds(Set())(admin))
        .thenReturn(List().successFuture)

      val actualMasterUser: MasterUser = masterUserService.getMasterUser(emailNotFound, None).await

      actualMasterUser.email shouldBe emailNotFound
      actualMasterUser.users.size shouldBe 1

      val actualUserRef: UserRef = actualMasterUser.users.head

      actualUserRef.loginMode shouldBe LoginMode.UsernamePassword
      actualUserRef.login shouldBe s"CE4w-$emailNotFound"
      actualUserRef.ssoPrefix shouldBe None
      actualUserRef.embeddingInfo shouldBe None
      actualUserRef.networkName shouldBe None
    }

    "returns a failure when there is an issue fetching users for a given email" in new Scope {

      when(userRepo.getUsers(Set(EmailFilter(user1.email)))(admin))
        .thenReturn(Future.failed(new RuntimeException("Unable to get users")))

      masterUserService.getMasterUser(email1, None).awaitTry match {
        case Success(_) => fail("Should not get here")
        case Failure(exception) =>
          exception.getMessage shouldBe "Unable to get users"
      }
    }
  }

  "simon/api/v2/master-user when X-Forwarded-Host is www.simonmarkets.com" should {
    "return master user with login mode that matches the login modes mapped to simonmarkets.com " +
      "when multiple users with given email exist in the system" in new Scope {

      when(userRepo.getUsers(Set(EmailFilter(email1)))(admin))
        .thenReturn(List(user1, user2).successFuture)
      when(networkRepo.getByIds(Set(user1.networkId, user2.networkId))(admin))
        .thenReturn(List(network1, network2).successFuture)


      val actualMasterUser: MasterUser = masterUserService.getMasterUser(email1, None, Some(simonHost)).await

      actualMasterUser shouldBe expectedMasterUserMultiSimonMarketsOnly
    }

    "return master user when single user with given email exist in the system" in new Scope {

      when(userRepo.getUsers(Set(EmailFilter(email2)))(admin))
        .thenReturn(List(user3).successFuture)
      when(networkRepo.getByIds(Set(user3.networkId))(admin))
        .thenReturn(List(network3).successFuture)


      val actualMasterUser: MasterUser = masterUserService.getMasterUser(email2, None, Some(simonHost)).await

      actualMasterUser shouldBe expectedMasterUserSingle

    }

    "return master user after filtering active users when multiple user with given email exist in the system" in new Scope {

      when(userRepo.getUsers(Set(EmailFilter(email3)))(admin))
        .thenReturn(List(user4, user5).successFuture)
      when(networkRepo.getByIds(Set(user4.networkId))(admin))
        .thenReturn(List(network4).successFuture)


      val actualMasterUser: MasterUser = masterUserService.getMasterUser(email3, None, Some(simonHost)).await

      actualMasterUser shouldBe expectedMasterUserActive
    }

    "return random master user when no users with given email exist" in new Scope {

      when(userRepo.getUsers(Set(EmailFilter(emailNotFound)))(admin))
        .thenReturn(List().successFuture)
      when(networkRepo.getByIds(Set())(admin))
        .thenReturn(List().successFuture)

      val actualMasterUser: MasterUser = masterUserService.getMasterUser(emailNotFound, None, Some(simonHost)).await

      actualMasterUser.email shouldBe emailNotFound
      actualMasterUser.users.size shouldBe 1

      val actualUserRef: UserRef = actualMasterUser.users.head

      actualUserRef.loginMode shouldBe LoginMode.UsernamePassword
      actualUserRef.login shouldBe s"CE4w-$emailNotFound"
      actualUserRef.ssoPrefix shouldBe None
      actualUserRef.embeddingInfo shouldBe None
      actualUserRef.networkName shouldBe None
    }

    "returns a failure when there is an issue fetching users for a given email" in new Scope {

      when(userRepo.getUsers(Set(EmailFilter(user1.email)))(admin))
        .thenReturn(Future.failed(new RuntimeException("Unable to get users")))

      masterUserService.getMasterUser(email1, None, Some(simonHost)).awaitTry match {
        case Success(_) => fail("Should not get here")
        case Failure(exception) =>
          exception.getMessage shouldBe "Unable to get users"
      }
    }
  }

  "simon/api/v2/master-user when X-Forwarded-Host is www.icapitalnetwork.com" should {
    "return master user with login mode that matches the login modes mapped to icapitalnetwork.com " +
      "when multiple users with given email exist in the system" in new Scope {

      when(userRepo.getUsers(Set(EmailFilter(email1)))(admin))
        .thenReturn(List(user12, user13).successFuture)
      when(networkRepo.getByIds(Set(user12.networkId, user13.networkId))(admin))
        .thenReturn(List(network1, network2).successFuture)


      val actualMasterUser: MasterUser = masterUserService.getMasterUser(email1, None, Some(icnHost)).await

      actualMasterUser shouldBe expectedMasterUserMultiIcapitalNetworkOnly
    }

    "return master user when single user with given email exist in the system" in new Scope {

      when(userRepo.getUsers(Set(EmailFilter(email2)))(admin))
        .thenReturn(List(user8).successFuture)
      when(networkRepo.getByIds(Set(user8.networkId))(admin))
        .thenReturn(List(network3).successFuture)


      val actualMasterUser: MasterUser = masterUserService.getMasterUser(email2, None, Some(icnHost)).await

      actualMasterUser shouldBe expectedMasterUserSingleIcapitalNetwork

    }

    "return master user with domain config's landing page when user and network's landing page are empty" in new Scope {
      when(userRepo.getUsers(Set(EmailFilter(email2)))(admin))
        .thenReturn(List(user8.copy(landingPage = None)).successFuture)
      when(networkRepo.getByIds(Set(user8.networkId))(admin))
        .thenReturn(List(network3.copy(landingPage = None)).successFuture)

      val actualMasterUser: MasterUser = masterUserService.getMasterUser(email2, None, Some(icnHost)).await

      actualMasterUser shouldBe expectedMasterUserSingleIcapitalNetwork
      actualMasterUser.users.head.landingPage shouldBe icnConfig.landingPage
    }

    "return master user after filtering active users when multiple user with given email exist in the system" in new Scope {

      when(userRepo.getUsers(Set(EmailFilter(email3)))(admin))
        .thenReturn(List(user10, user11).successFuture)
      when(networkRepo.getByIds(Set(user10.networkId))(admin))
        .thenReturn(List(network4).successFuture)


      val actualMasterUser: MasterUser = masterUserService.getMasterUser(email3, None, Some(icnHost)).await

      actualMasterUser shouldBe expectedMasterUserActiveIcapitalNetwork
    }

    "return random master user when no users with given email exist" in new Scope {

      when(userRepo.getUsers(Set(EmailFilter(emailNotFound)))(admin))
        .thenReturn(List().successFuture)
      when(networkRepo.getByIds(Set())(admin))
        .thenReturn(List().successFuture)

      val actualMasterUser: MasterUser = masterUserService.getMasterUser(emailNotFound, None, Some(icnHost)).await

      actualMasterUser.email shouldBe emailNotFound
      actualMasterUser.users.size shouldBe 1

      val actualUserRef: UserRef = actualMasterUser.users.head

      actualUserRef.loginMode shouldBe LoginMode.ICNUsernamePassword
      actualUserRef.login shouldBe s"CE4w-$emailNotFound"
      actualUserRef.ssoPrefix shouldBe None
      actualUserRef.embeddingInfo shouldBe None
      actualUserRef.networkName shouldBe None
    }

    "returns a failure when there is an issue fetching users for a given email" in new Scope {

      when(userRepo.getUsers(Set(EmailFilter(user1.email)))(admin))
        .thenReturn(Future.failed(new RuntimeException("Unable to get users")))

      masterUserService.getMasterUser(email1, None, Some(icnHost)).awaitTry match {
        case Success(_) => fail("Should not get here")
        case Failure(exception) =>
          exception.getMessage shouldBe "Unable to get users"
      }
    }
  }

  trait Scope {
    implicit val admin: Set[String] = Set(Capabilities.Admin)
    implicit val tid: TraceId = TraceId.randomize

    val ssoPrefix: SSOPrefix = SSOPrefix(
      ssoSystemName = Some("SSO System Name"),
      baseUrl = "Base Url",
      redirectionKey = "Redirection Key",
      isRedirectionKeyEncoded = None,
      simonBase = Some("SIMON Base")
    )
    val embeddingInfo: EmbeddingInfo = EmbeddingInfo(
      hostApplicationUrl = "Url",
      hostApplicationName = Some("Name")
    )

    val simonHost = "simonmarkets.com"
    val icnHost = "s.icapitalnetwork.com"
    val icnHostSubDomain = "s"
    val icapitalHost = "icapital.com"
    val whiteLabel1Host = "wlp1.s.icapitalnetwork.com"

    val network1: Network = TestNetwork("network1", name = "network1", networkCode = "nc1", ssoPrefix = Some(ssoPrefix))
    val network2: Network = TestNetwork("network2", name = "network2", networkCode = "nc2", embeddingInfo = Some(embeddingInfo))
    val network3: Network = TestNetwork("network3", name = "network3", networkCode = "nc3")
    val network4: Network = TestNetwork("network4", name = "network4", networkCode = "nc4")
    val network5: Network = TestNetwork("network5", name = "network5", networkCode = "nc5") //Multi network enabled network

    val email1: String = "<EMAIL>"
    val email2: String = "<EMAIL>"
    val email3: String = "<EMAIL>"
    val email4: String = "<EMAIL>"
    val emailNotFound = "<EMAIL>"
    val previewUserEmail: String = "<EMAIL>"

    val wlp1: Int = 1

    //sample users
    val user1: User = TestUser("user1", network1.id, email = email1, loginMode = LoginMode.SSOAndUsernamePassword)
    val user2: User = TestUser("user2", network2.id, email = email1, loginMode = LoginMode.ICNUsernamePassword)
    val user3: User = TestUser("user3", network3.id,email = email2, loginMode = LoginMode.UsernamePassword)
    val user4: User = TestUser("user4", network4.id, email = email3, loginMode = LoginMode.UsernamePassword)
    val user5: User = TestUser("user5", network4.id, email = email3, loginMode = LoginMode.UsernamePassword, isActive = false)
    val user6: User = TestUser("user6", network4.id, email = email4, loginMode = LoginMode.UsernamePassword)
    val user7: User = TestUser("user7", network5.id, email = email4, loginMode = LoginMode.UsernamePassword)
    val user8: User = TestUser("user3", network3.id, whiteLabelPartnerId = wlp1.toString.some, email = email2, loginMode = LoginMode.ICNUsernamePassword)
    val user9: User = TestUser("user3", network3.id, email = email2, loginMode = LoginMode.SSOAndICNUsernamePassword)
    val user10: User = TestUser("user4", network4.id, email = email3, loginMode = LoginMode.ICNUsernamePassword)
    val user11: User = TestUser("user5", network4.id, email = email3, loginMode = LoginMode.ICNUsernamePassword, isActive = false)
    val user12: User = TestUser("user1", network1.id, email = email1, loginMode = LoginMode.SSOAndICNUsernamePassword)
    val user13: User = TestUser("user2", network2.id, email = email1, loginMode = LoginMode.UnifiedPassword)
    val user14: User = user3.copy(passport = Map(icnHostSubDomain -> 1))
    val user15: User = user3.copy(networkId = network4.id, loginMode = LoginMode.ICNUsernamePassword)

    val previewUser: User = TestUser("user2", network1.id, email = previewUserEmail, loginMode = LoginMode.ICNUsernamePassword, userType = UserType.Preview)

    //sample user refs
    val userRef2 = UserRef(login = user2.idpLoginId, networkName = Some("network2"), ssoPrefix = None, embeddingInfo = Some(embeddingInfo), loginMode = LoginMode.ICNUsernamePassword, LandingPage.EnumNotFound)
    val userRef14 = UserRef(login = user14.idpLoginId, networkName = None, ssoPrefix = None, embeddingInfo = None, loginMode = LoginMode.ICNUsernamePassword, LandingPage.ICN)
    val userRef15 = UserRef(login = user15.idpLoginId, networkName = None, ssoPrefix = None, embeddingInfo = None, loginMode = LoginMode.ICNUsernamePassword, LandingPage.ICN)

    val n1Id: String = user1.networkId.toString
    val n2Id: String = user2.networkId.toString
    val expectedMasterUserMulti: MasterUser = MasterUser(
      user1.email,
      List(
        UserRef(login = user1.idpLoginId, networkName = Some("network1"), ssoPrefix = Some(ssoPrefix), embeddingInfo = None, loginMode = LoginMode.SSOAndUsernamePassword, LandingPage.EnumNotFound),
        userRef2
      )
    )

    val expectedMasterUserMultiPreviewUser: MasterUser = MasterUser(
      previewUser.email,
      List(
        UserRef(login = user1.idpLoginId, networkName = Some("network1"), ssoPrefix = Some(ssoPrefix), embeddingInfo = None, loginMode = LoginMode.SSOAndUsernamePassword, LandingPage.EnumNotFound),
        userRef2
      )
    )

    val expectedMasterUserMultiIcapitalOnly: MasterUser = MasterUser(
      user1.email,
      List(
        UserRef(login = user1.idpLoginId, networkName = Some("network1"), ssoPrefix = Some(ssoPrefix), embeddingInfo = None, loginMode = LoginMode.SSOAndUsernamePassword, LandingPage.Unified),
        UserRef(login = user2.idpLoginId, networkName = Some("network2"), ssoPrefix = None, embeddingInfo = Some(embeddingInfo), loginMode = LoginMode.ICNUsernamePassword, LandingPage.Unified),
      )
    )

    val expectedMasterUserMultiSimonMarketsOnly: MasterUser = MasterUser(
      user1.email,
      List(
        UserRef(login = user1.idpLoginId, networkName = None, ssoPrefix = Some(ssoPrefix), embeddingInfo = None, loginMode = LoginMode.SSOAndUsernamePassword, LandingPage.SIMON),
      )
    )

    val expectedMasterUserMultiIcapitalNetworkOnly: MasterUser = MasterUser(
      user12.email,

      List(
        UserRef(login = user12.idpLoginId, networkName = Some("network1"), ssoPrefix = Some(ssoPrefix), embeddingInfo = None, loginMode = LoginMode.SSOAndICNUsernamePassword, LandingPage.ICN),
        UserRef(login = user13.idpLoginId, networkName = Some("network2"), ssoPrefix = None, embeddingInfo = Some(embeddingInfo), loginMode = LoginMode.UnifiedPassword, LandingPage.ICN),
      )

    )

    val expectedMasterUserSingle: MasterUser = MasterUser(
      user3.email,
      List(
        UserRef(login = user3.idpLoginId, networkName = None, ssoPrefix = None, embeddingInfo = None, loginMode = LoginMode.UsernamePassword, LandingPage.EnumNotFound),
      )
    )

    val expectedMasterUserSingleIcapitalOnly: MasterUser = MasterUser(
      user3.email,
      List(
        UserRef(login = user3.idpLoginId, networkName = None, ssoPrefix = None, embeddingInfo = None, loginMode = LoginMode.UsernamePassword, LandingPage.Unified),
      )
    )

    val expectedMasterUserSingleIcapitalNetwork: MasterUser = MasterUser(
      user3.email,
      List(
        UserRef(login = user3.idpLoginId, networkName = None, ssoPrefix = None, embeddingInfo = None, loginMode = LoginMode.ICNUsernamePassword, LandingPage.ICN),
      )
    )

    val expectedMasterUserActive: MasterUser = MasterUser(
      user4.email,
      List(
        UserRef(login = user4.idpLoginId, networkName = None, ssoPrefix = None, embeddingInfo = None, loginMode = LoginMode.UsernamePassword, LandingPage.EnumNotFound),
      )
    )

    val expectedMasterUserActiveIcapitalOnly: MasterUser = MasterUser(
      user4.email,
      List(
        UserRef(login = user4.idpLoginId, networkName = None, ssoPrefix = None, embeddingInfo = None, loginMode = LoginMode.UsernamePassword, LandingPage.Unified),
      )
    )

    val expectedMasterUserActiveIcapitalNetwork: MasterUser = MasterUser(
      user4.email,
      List(
        UserRef(login = user4.idpLoginId, networkName = None, ssoPrefix = None, embeddingInfo = None, loginMode = LoginMode.ICNUsernamePassword, LandingPage.ICN),
      )
    )

    val networkNamesList = List(network1.name, network2.name, network3.name)
    val userRepo: UserRepository = mock[UserRepository]
    val networkRepo: NetworkRepository = mock[NetworkRepository]
    val simonConfig: DomainConfig = DomainConfig("simonmarkets.com", LandingPage.SIMON, List(LoginMode.UsernamePassword,
      LoginMode.SSOAndUsernamePassword, LoginMode.SSO, LoginMode.ClientCredentials, LoginMode.Embedded), LoginMode.UsernamePassword)
    val icnConfig: DomainConfig = DomainConfig("icapitalnetwork.com", LandingPage.ICN, List(LoginMode.ICNUsernamePassword,
      LoginMode.SSOAndICNUsernamePassword, LoginMode.UsernamePassword, LoginMode.SSOAndUsernamePassword, LoginMode.SSO,
      LoginMode.ClientCredentials, LoginMode.Embedded, LoginMode.UnifiedPassword), LoginMode.ICNUsernamePassword
    )

    val domainConfigs = List(simonConfig, icnConfig)

    val masterUserService = new MasterUserService(userRepo, networkRepo, domainConfigs)

    when(networkRepo.getAllNetworkNames()(admin)).thenReturn(Map(network1.id -> network1.name,
      network2.id -> network2.name, network3.id -> network3.name).successFuture)

  }
}
