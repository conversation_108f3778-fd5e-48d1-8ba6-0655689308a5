package com.simonmarkets.users.service

import com.dimafeng.testcontainers.DockerComposeContainer
import com.dimafeng.testcontainers.scalatest.TestContainerForEach
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.logging.TraceId
import com.simonmarkets.mongodb.trigger.event.{MongoEvent, MongoNs, Payload}
import com.simonmarkets.users.common.LoginMode.SSOAndUsernamePassword
import com.simonmarkets.users.common.UserType.Human
import com.simonmarkets.users.common.trigger.UserPayload
import com.simonmarkets.users.common.{EventInfo, User}
import com.simonmarkets.users.config.KafkaProducerConfig
import com.simonmarkets.users.domain.KafkaUserMessageResponse
import com.simonmarkets.users.repository.UserRepository
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{DoNotDiscover, Matchers, WordSpec}
import simon.Id.NetworkId
import zio.{Runtime, _}
import zio.kafka.admin.AdminClient.NewTopic
import zio.kafka.admin.{AdminClient, AdminClientSettings}
import zio.kafka.producer.{Producer, ProducerSettings}

import java.io.File
import java.time.{LocalDateTime, OffsetDateTime}

import scala.concurrent.ExecutionContext.global
import scala.concurrent.Future


@DoNotDiscover //remove once testcontainers are able to run in cicd pipeline
class UserEventPublisherSpecWithTestContainers extends WordSpec with Matchers with MockitoSugar with TestContainerForEach{


  val topic = "test"

  implicit val traceId: TraceId = TraceId.randomize
  val runtime = Runtime.default


  override val containerDef: DockerComposeContainer.Def = DockerComposeContainer.Def(
    new File(getClass.getResource("/kafka-compose.yml").getPath)
  )

  val kafkaConnection = "localhost:9092"
  val adminClientSettings = AdminClientSettings(List(kafkaConnection))
  val adminLayer: ZLayer[Any, Throwable, AdminClient] = ZLayer.scoped {
    AdminClient.make(adminClientSettings)
  }
  val createTopicEff: Task[Unit] = {
    for {
      adminClient <- ZIO.service[AdminClient]
      _ <- adminClient.createTopic(NewTopic("test", 1, 1))
    } yield ()
  }.provideLayer(adminLayer)

  def publisherEff(mongoUserEvent: MongoEvent[UserPayload]): ZIO[UserEventPublisher, Throwable, Option[KafkaUserMessageResponse]] =
    for {
      publisher <- ZIO.service[UserEventPublisher]
      res <- publisher.publishUserEventMessage(mongoUserEvent)
    } yield res

  val userRepo = mock[UserRepository]
  val offsetNow = OffsetDateTime.now()
  val localNow = LocalDateTime.now()

  val user = User(
    id = "id1",
    networkId = NetworkId("SIMON Admin"),
    createdAt = localNow,
    createdBy = "Okta Admin",
    updatedAt = localNow,
    updatedBy = "someUserId2",
    emailSentAt = Some(localNow),
    emailSentBy = Some("Okta Admin"),
    lastVisitedAt = Some(localNow),
    email = "<EMAIL>",
    firstName = "Test",
    lastName = "User",
    distributorId = None,
    omsId = None, roles = Set.empty,
    entitlements = Set("Admin"), idpLoginId = "login", loginMode = SSOAndUsernamePassword,
    userType = Human,
    eventInfo = EventInfo.Default,
    externalIds = Seq.empty,
  )

  val userPayload = UserPayload(
    id = user.id,
    networkId = user.networkId,
    createdAt = user.createdAt,
    createdBy = user.createdBy,
    updatedAt = user.updatedAt,
    updatedBy = user.updatedBy,
    email = user.email,
    firstName = user.firstName,
    lastName = user.lastName,
    roles = Set.empty,
  )

  val producer: ZLayer[Any, Throwable, Producer] = ZLayer.scoped {
    Producer.make(ProducerSettings(List(kafkaConnection)))
  }
  val producer2: ZLayer[Any, Throwable, Producer] = ZLayer.scoped {
    Producer.make(ProducerSettings(List(kafkaConnection)).withProperties(Map("retries" -> "3", "max.block.ms" -> "5000")))
  }
  val producerGroupId = "producerGroupId"
  val source = "simon"
  val producerConfig = KafkaProducerConfig(topic, Some(producerGroupId), Some(source))
  val service: TaskLayer[UserEventPublisher] = ZLayer.make[UserEventPublisherImpl](
    producer,
    ZLayer.succeed(global),
    ZLayer.succeed(userRepo),
    ZLayer.succeed(producerConfig),
    UserEventPublisherImpl.layer,
  )
  val service2: TaskLayer[UserEventPublisher] = ZLayer.make[UserEventPublisherImpl](
    producer2,
    ZLayer.succeed(global),
    ZLayer.succeed(userRepo),
    ZLayer.succeed(producerConfig),
    UserEventPublisherImpl.layer,
  )

  "UserEventPublisher" should {
    "publishUserEventMessage" in {
      withContainers { _ =>
        val mongoEvent: MongoEvent[UserPayload] = MongoEvent(
          id = "mongo_event_1", time = offsetNow, detail = Payload(
            "UPDATE",
            Some(userPayload),
            None,
            MongoNs("pipg", "users")
          )
        )
        when(userRepo.getById("id1")(Set(Admin))).thenReturn(Future.successful(Some(user)))


        Unsafe.unsafe { implicit unsafe =>
          runtime.unsafe.run(createTopicEff)
          val res = runtime.unsafe.run(publisherEff(mongoEvent).provideLayer(service)).getOrThrow()
          res shouldBe Some(KafkaUserMessageResponse(mongoEvent.detail.fullDocument.get.id, topic, 0, user.eventInfo))
        }
      }
    }

    "return none when mongo event cannot be converted to kafka message" in {
      withContainers { _ =>
        val mongoEvent: MongoEvent[UserPayload] = MongoEvent(
          id = "mongo_event_1", time = offsetNow, detail = Payload(
            "UPDATE",
            None,
            None,
            MongoNs("pipg", "users")
          )
        )
        Unsafe.unsafe { implicit unsafe =>
          runtime.unsafe.run(createTopicEff)
          val res = runtime.unsafe.run(publisherEff(mongoEvent).provideLayer(service)).getOrThrow()
          res shouldBe None
        }
      }
    }
  }

  "throw error when mongo event cannot be published to topic" in {
    withContainers { _ =>
      val mongoEvent: MongoEvent[UserPayload] = MongoEvent(
        id = "mongo_event_1", time = offsetNow, detail = Payload(
          "UPDATE",
          Some(userPayload),
          None,
          MongoNs("pipg", "users")
        )
      )
      when(userRepo.getById("id1")(Set(Admin))).thenReturn(Future.successful(Some(user)))
      Unsafe.unsafe { implicit unsafe =>
        //publishing message to nonexistent topic
      val thrown = the[Error] thrownBy runtime.unsafe.run(publisherEff(mongoEvent).provideLayer(service2)).getOrThrow()
        thrown.getMessage.contains(s"[User Sync Publisher] could not publish mongo event [${mongoEvent.id}] to $topic") shouldBe true
      }
    }
  }
}


