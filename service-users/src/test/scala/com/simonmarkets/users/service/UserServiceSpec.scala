package com.simonmarkets.users.service

import akka.actor.ActorSystem
import akka.http.scaladsl.model.StatusCodes
import akka.testkit.TestKit
import com.amazonaws.services.eventbridge.model.PutEventsResult
import com.goldmansachs.marquee.pipg.Custodian.{<PERSON>tera, EnumNotFound}
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.goldmansachs.marquee.pipg.{CustodianFaNumber, ExternalAlias, License, NetworkType, UserACL, UserRole, User => LegacyUser}
import com.okta.sdk.resource.model.{User => OktaSDKUser}
import com.simonmarkets.api.users.HttpACLClient
import com.simonmarkets.capabilities.Capabilities.{Admin, ReadOnlyAdmin}
import com.simonmarkets.capabilities.SimonUICapabilities.ViewUISwitchUsersNetworkCapability
import com.simonmarkets.capabilities.UsersCapabilities
import com.simonmarkets.capabilities.UsersCapabilities._
import com.simonmarkets.eventbridge.client.EventBridgeClient
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.mongodb.error.Conflict
import com.simonmarkets.mongodb.trigger.codec.Codecs.singleUserEncoder
import com.simonmarkets.mongodb.trigger.event.SingleUser
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.NetworkIds.AdminNetworkId
import com.simonmarkets.networks.common.TestUtils.FutureOps
import com.simonmarkets.networks.common.clients.usersync.SourceDestinationPrimaryIdKind.{`ICN_WHITE_LABEL`, `SIMON_NETWORK`}
import com.simonmarkets.networks.common.clients.usersync.SourceDestinationSecondaryIdKind.{`ICN_FIRM`, `SIMON_LOCATION`}
import com.simonmarkets.networks.common.clients.usersync.SourceDestinationSystemName.SIMON
import com.simonmarkets.networks.common.clients.usersync.{MatchByPropertyName, SourceDestinationMatchByProperty, SourceDestinationPrimary, SourceDestinationPrimaryIdKind, SourceDestinationSecondary, SourceDestinationSecondaryIdKind, SourceDestinationSystem, UserNetworkMappingRequest, UserNetworkMappingResponse, UserSyncMappingClient}
import com.simonmarkets.networks.common.service.ExternalIdTypeService
import com.simonmarkets.networks.common.service.networkservice.BasicNetworkService
import com.simonmarkets.networks.common.{ExternalIdType, Network}
import com.simonmarkets.okta.client.api.application.{CreateOAuthSecretResponse, GetOAuthSecretResponse, OAuthSecretStatus}
import com.simonmarkets.okta.domain.{Factor, FactorType, OktaUser, UserInfo}
import com.simonmarkets.okta.service.OktaService
import com.simonmarkets.shared.MaskedId
import com.simonmarkets.syntax._
import com.simonmarkets.users.api.request._
import com.simonmarkets.users.api.response.PartialUserUpdateError
import com.simonmarkets.users.common.IdType.UserId
import com.simonmarkets.users.common.UniqueUserId._
import com.simonmarkets.users.common.User.{DistributorKey, OmsKey}
import com.simonmarkets.users.common._
import com.simonmarkets.users.common.api.request
import com.simonmarkets.users.common.api.request.{BumpUsersVersionRequest, OktaSyncRequest, UpsertUserRequest}
import com.simonmarkets.users.common.api.response._
import com.simonmarkets.users.common.okta.OktaUserConversions
import com.simonmarkets.users.config.{PagingConfig, UserServiceConfig}
import com.simonmarkets.users.domain.{ImpersonationStatus, UserImpersonation}
import com.simonmarkets.users.repository.UserRepository
import com.simonmarkets.users.repository.filters.{ICapitalUserIdFilter, NetworkFilter, OrFilter, SystemUsersFilter}
import com.simonmarkets.users.service.UserServiceSpec._
import com.simonmarkets.users.{TestNetwork, TestUser}
import org.mockito.{ArgumentCaptor, ArgumentMatcher}
import org.mockito.ArgumentMatchers.{any, argThat, eq => meq}
import org.mockito.Mockito._
import org.scalatest.Matchers._
import org.scalatest.RecoverMethods.recoverToExceptionIf
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Assertion, BeforeAndAfterAll, WordSpecLike}
import simon.Id
import simon.Id.NetworkId

import java.io.IOException
import java.time.temporal.ChronoUnit
import java.time.{Instant, LocalDateTime, ZoneOffset}
import scala.concurrent.{Await, ExecutionContext, ExecutionContextExecutor, Future}

class UserServiceSpec extends TestKit(ActorSystem("UserServiceSpec"))
  with WordSpecLike with BeforeAndAfterAll with UserAcceptedAccessKeysGenerator {

  override def afterAll(): Unit = TestKit.shutdownActorSystem(system)

  "UserService" should {
    "getById " should {
      "return a user by id if it exists in the repository" in new Scope {
        when(userRepo.getById(userId)(Set("admin"))) thenReturn Future.successful(Some(userInAdminNetwork))

        userService.getUserViewById(adminRequesterAcl, userId) assertResult Some(UserView(userInAdminNetwork))
      }
      "return None if the user is not exists" in new Scope {
        when(userRepo.getById(userId)(Set("admin"))) thenReturn Future.successful(None)

        userService.getUserViewById(adminRequesterAcl, userId) assertResult None
      }
      "call the users Repo with the correct entitlements" in new Scope {
        val userAcl = TestUserACL(userId, AdminNetworkId, capabilities = UsersCapabilities.ViewCapabilities + "someOther")
        when(userRepo.getById(meq(userId))(any[Set[String]])(any[TraceId])) thenReturn Future.successful(Some(userInAdminNetwork))
        userService.getUserViewById(userAcl, userId)

        verify(userRepo).getById(userId)(Set("admin", "readOnlyAdmin", s"viewUserViaNetwork:${userAcl.networkId}", "viewUserViaGuid:test123", s"viewClientCredentialsViaNetwork:${userAcl.networkId}"))
      }
    }
    "getUserViewById" should {
      "return a UserView if the user exists in the repository" in new Scope {
        when(userRepo.getById(userId)(Set("admin"))) thenReturn Future.successful(Some(userInAdminNetwork))

        val expectedResult: UserView = UserView(userInAdminNetwork)

        userService.getUserViewById(adminRequesterAcl, userId) assertResult Some(expectedResult)
      }

      "return a UserView for user with iCapitalUserId if they exist" in new Scope {
        val iCapitalUserId = "iCapitalUserId"
        val userWithICapId = userInAdminNetwork.copy(iCapitalUserId = Some(iCapitalUserId))
        when(userRepo.getUser(Set(ICapitalUserIdFilter(iCapitalUserId)))(Set("admin"))) thenReturn Future.successful(Some(userWithICapId))

        val expectedResult = UserView(userWithICapId)

        userService.getUserViewById(adminRequesterAcl, iCapitalUserId, true) assertResult Some(expectedResult)
      }
      "return None if the user does not exist" in new Scope {
        when(userRepo.getById(userId)(Set("admin"))) thenReturn Future.successful(None)

        userService.getUserViewById(adminRequesterAcl, userId) assertResult None
      }
    }

    "handle insert" when {
      "request contains one invalid custodian (EnumNotFound) in custodianFaNumbers then throw exception" in new Scope {
        userService.handleInsertRequest(
          adminRequesterAcl,
          insertRequest(upsertUser.copy(custodianFaNumbers = Set(CustodianFaNumber(EnumNotFound, "123abc"))))
        ) assertFailureEx HttpError.badRequest("Invalid Enum/s detected in \"custodianFaNumbers\"")
      }

      "request contains at least one invalid custodian (EnumNotFound) in custodianFaNumbers then throw exception" in new Scope {
        userService.handleInsertRequest(
          adminRequesterAcl,
          insertRequest(upsertUser.copy(custodianFaNumbers = Set(
            CustodianFaNumber(Cetera, "123abc"),
            CustodianFaNumber(EnumNotFound, "123abc")
          )))
        ) assertFailureEx HttpError.badRequest("Invalid Enum/s detected in \"custodianFaNumbers\"")
      }

      "request contains valid custodians + new user (not exists in mongo) then should succeed" in new Scope {
        val userWithOkCustodians = userInAdminNetwork.copy(custodianFaNumbers = Set(CustodianFaNumber(Cetera, "123abc")))

        // happy path logic
        when(networkService.getNetworkById(adminRequesterAcl, userWithOkCustodians.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(userWithOkCustodians.email, userWithOkCustodians.networkId)) thenReturn false.successFuture
        val event = EventInfo(UserDomainEvent.UserCreated, triggeredBy = adminRequesterAcl.userId, traceId)

        when(userRepo.insertUser(any[String], matchWithDynamicFieldsExcluded(userWithOkCustodians.copy(eventInfo = event)))(any(classOf[TraceId]))) thenReturn userWithOkCustodians.successFuture

        userService.handleInsertRequest(adminRequesterAcl, insertRequest(userWithOkCustodians)).map(userAndSecret => userAndSecret._1) assertResult userWithOkCustodians
      }

      "request contains valid custodians + userRepository should get expected custodianFaNumber data as input" in new Scope {
        val userWithOkCustodians = userInAdminNetwork.copy(custodianFaNumbers = Set(CustodianFaNumber(Cetera, "123abc")))

        // happy path logic
        when(networkService.getNetworkById(adminRequesterAcl, userWithOkCustodians.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(userWithOkCustodians.email, userWithOkCustodians.networkId)) thenReturn false.successFuture
        val event = EventInfo(UserDomainEvent.UserCreated, triggeredBy = adminRequesterAcl.userId, traceId)
        when(userRepo.insertUser(any[String], matchWithDynamicFieldsExcluded(userWithOkCustodians.copy(eventInfo = event)))(any(classOf[TraceId]))) thenReturn userWithOkCustodians.successFuture
        userService.handleInsertRequest(adminRequesterAcl, insertRequest(userWithOkCustodians)).map(userAndSecret => userAndSecret._1) assertResult userWithOkCustodians

        // make sure what userRepo receives is the data we want to send
        val captor: ArgumentCaptor[User] = ArgumentCaptor.forClass(classOf[User])
        verify(userRepo, times(1)).insertUser(any[String], captor.capture())(any[TraceId])
        assert(captor.getValue.custodianFaNumbers == userWithOkCustodians.custodianFaNumbers)
      }

      "new user (not exists in mongo)" in new Scope {
        when(networkService.getNetworkById(adminRequesterAcl, userInAdminNetwork.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(userInAdminNetwork.email, userInAdminNetwork.networkId)) thenReturn false.successFuture
        val event = EventInfo(UserDomainEvent.UserCreated, triggeredBy = adminRequesterAcl.userId, traceId)
        when(userRepo.insertUser(any[String], matchWithDynamicFieldsExcluded(userInAdminNetwork.copy(eventInfo = event)))(any(classOf[TraceId]))) thenReturn userInAdminNetwork.successFuture

        userService.handleInsertRequest(adminRequesterAcl, insertRequest(userInAdminNetwork)).map(userAndSecret => userAndSecret._1) assertResult userInAdminNetwork
      }

      "upsert new user (not exists in mongo)" in new Scope {
        when(networkService.getNetworkById(adminRequesterAcl, upsertUser.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.getUserByUniqueId(Distributor(distributorId, upsertUser.networkId))(Set("admin"))) thenReturn Future(None)
        when(userRepo.existsByEmailAndNetwork(upsertUser.email, upsertUser.networkId)) thenReturn false.successFuture
        val event = EventInfo(UserDomainEvent.UserCreated, triggeredBy = adminRequesterAcl.userId, traceId)
        when(userRepo.insertUser(any[String], matchWithDynamicFieldsExcluded(upsertUser.copy(eventInfo = event)))(any(classOf[TraceId]))) thenReturn upsertUser.successFuture
        userService.handleInsertRequest(adminRequesterAcl, upsertRequest(upsertUser)).map(userAndSecret => userAndSecret._1) assertResult upsertUser
      }

      "existing user requested" in new Scope {
        when(networkService.getNetworkById(adminRequesterAcl, upsertUser.networkId)(traceId)) thenReturn adminNetwork.successFuture
        when(userRepo.getUserByUniqueId(Distributor(distributorId, upsertUser.networkId))(Set("admin"))) thenReturn Some(userInAdminNetwork).successFuture
        when(userRepo.updateUser(any[String], matchWithDynamicFieldsExcluded(upsertUser.copy(
          idpId = upsertUser.idpId,
          eventInfo = EventInfo(UserDomainEvent.UserUpdated, adminRequesterAcl.userId, traceId)
        )))(any(classOf[TraceId]))).thenReturn(upsertUser.successFuture)

        userService.handleInsertRequest(adminRequesterAcl, upsertRequest(upsertUser)).map(userAndSecret => userAndSecret._1) assertResult upsertUser
      }

      "new System User (not exists in mongo)" in new Scope {
        when(networkService.getNetworkById(adminRequesterAcl, systemUserAdminNetwork.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(systemUserAdminNetwork.email, systemUserAdminNetwork.networkId)) thenReturn false.successFuture
        val event = EventInfo(UserDomainEvent.UserCreated, triggeredBy = adminRequesterAcl.userId, traceId)
        when(userRepo.insertUser(any[String], matchWithDynamicFieldsExcluded(systemUserAdminNetwork.copy(eventInfo = event)))(any(classOf[TraceId]))) thenReturn systemUserAdminNetwork.successFuture
        when(oktaService.createApplication(any[String])(any[TraceId], any[ExecutionContext])) thenReturn Future(UserInfo("generatedUserId", Some("clientSecret")))
        userService.handleInsertRequest(adminRequesterAcl, insertRequest(systemUserAdminNetwork)).map(userAndSecret => userAndSecret._1) assertResult systemUserAdminNetwork
      }

      "new user with Admin Role (not exists in mongo)" in new Scope {
        when(networkService.getNetworkById(adminRequesterAcl, userWithAdminRole.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(userWithAdminRole.email, userWithAdminRole.networkId)) thenReturn false.successFuture
        val event = EventInfo(UserDomainEvent.UserCreated, triggeredBy = adminRequesterAcl.userId, traceId)
        when(userRepo.insertUser(any[String], matchWithDynamicFieldsExcluded(userWithAdminRole.copy(eventInfo = event)))(any(classOf[TraceId]))) thenReturn userWithAdminRole.successFuture

        userService.handleInsertRequest(adminRequesterAcl, insertRequest(userWithAdminRole)).map(userAndSecret => userAndSecret._1) assertResult userWithAdminRole
      }

      "new user that is NOT multi-network capable" in new Scope {
        private val nonMultiNetworkUser = userInAdminNetwork.copy(networkId = multiNetworkExcludedNetwork.id)
        private val event = EventInfo(UserDomainEvent.UserCreated, triggeredBy = adminRequesterAcl.userId, traceId)
        private val expectedUser = userInAdminNetwork.copy(eventInfo = event, idpLoginId = nonMultiNetworkUser.email, networkId = nonMultiNetworkUser.networkId)

        when(networkService.getNetworkById(adminRequesterAcl, nonMultiNetworkUser.networkId)) thenReturn multiNetworkExcludedNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(nonMultiNetworkUser.email, nonMultiNetworkUser.networkId)) thenReturn false.successFuture
        when(userRepo.insertUser(any[String], any[User])(any(classOf[TraceId]))) thenReturn expectedUser.successFuture

        userService.handleInsertRequest(adminRequesterAcl, insertRequest(nonMultiNetworkUser)).map(userAndSecret => userAndSecret._1) assertResult expectedUser
      }

      "user exists in mongo" in new Scope {
        when(userRepo.existsByEmailAndNetwork(userInAdminNetwork.email, userInAdminNetwork.networkId)) thenReturn Future.successful(true)
        when(networkService.getNetworkById(adminRequesterAcl, userInAdminNetwork.networkId)) thenReturn adminNetwork.successFuture
        userService.handleInsertRequest(adminRequesterAcl, insertRequest(userInAdminNetwork)) assertFailure[HttpError]()
      }

      "mongo is not accessible" in new Scope {
        when(userRepo.existsByEmailAndNetwork(userInAdminNetwork.email, userInAdminNetwork.networkId)) thenReturn Future.failed(new IOException("mongo error"))
        when(networkService.getNetworkById(adminRequesterAcl, userInAdminNetwork.networkId)) thenReturn adminNetwork.successFuture
        userService.handleInsertRequest(adminRequesterAcl, insertRequest(userInAdminNetwork)) assertFailure[IOException]()
      }

      "user id is set which already exists" in new Scope {

        val id = "123"

        when(networkService.getNetworkById(adminRequesterAcl, userInAdminNetwork.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(userInAdminNetwork.email, userInAdminNetwork.networkId)) thenReturn false.successFuture
        when(userRepo.exists(id)) thenReturn true.successFuture

        userService.handleInsertRequest(adminRequesterAcl, insertRequest(userInAdminNetwork).copy(id = id.some)) assertFailure[HttpError]()
      }

      "userId is set" in new Scope {

        val id = "123"

        when(networkService.getNetworkById(adminRequesterAcl, userInAdminNetwork.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(userInAdminNetwork.email, userInAdminNetwork.networkId)) thenReturn false.successFuture
        when(userRepo.exists(id)) thenReturn false.successFuture
        val event = EventInfo(UserDomainEvent.UserCreated, triggeredBy = adminRequesterAcl.userId, traceId)

        when(userRepo.insertUser(any[String], matchWithDynamicFieldsExcluded(userInAdminNetwork.copy(id = id, eventInfo = event)))(any(classOf[TraceId]))) thenReturn userInAdminNetwork.copy(id = id).successFuture

        userService.handleInsertRequest(adminRequesterAcl, insertRequest(userInAdminNetwork).copy(id = id.some)).map(userAndSecret => userAndSecret._1).map(_.id) assertResult id
      }

      "requester does not have entitlement to insert user" in new Scope {
        val requesterAcl2: UserACL = TestUserACL(userId, adminNetwork.id, capabilities = Set(""))
        when(networkService.getNetworkById(requesterAcl2, userInAdminNetwork.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(userInAdminNetwork.email, userInAdminNetwork.networkId)) thenReturn Future.successful(false)

        val userAndSecret: Future[(User, Option[String])] = userService.handleInsertRequest(requesterAcl2, insertRequest(userInAdminNetwork))
        val insertedUser: Future[User] = userAndSecret.map(userAndSecret => userAndSecret._1)
        insertedUser assertFailureEx HttpError.unauthorized("User does not have right access keys to upload another user in this network")
      }

      "requester does not have entitlement to insert System User" in new Scope {
        val requesterAcl2: UserACL = TestUserACL(userId, adminNetwork.id, capabilities = Set(UploadUserViaPurview, UploadUserViaNetwork, UploadUserViaGuid))

        when(networkService.getNetworkById(requesterAcl2, systemUserAdminNetwork.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(systemUserAdminNetwork.email, systemUserAdminNetwork.networkId)) thenReturn Future.successful(false)
        when(userRepo.getUsersCount(Set(SystemUsersFilter(adminNetwork.id.toString)))) thenReturn Future.successful(1L)

        val userAndSecret: Future[(User, Option[String])] = userService.handleInsertRequest(requesterAcl2, insertRequest(systemUserAdminNetwork))
        val insertedUser: Future[User] = userAndSecret.map(userAndSecret => userAndSecret._1)
        insertedUser assertFailureEx HttpError.unauthorized("User does not have correct access keys to create System User in this network")
      }

      "requester in Admin network can create System User in other networks" in new Scope {
        val requesterAcl: UserACL = TestUserACL("adminNetworkUser", adminNetwork.id, capabilities = Set(Admin))
        val testNetwork: Network = TestNetwork()
        val systemUser: User = TestUser(
          id = "OktaClientId",
          email = email,
          createdBy = requesterAcl.userId,
          updatedBy = requesterAcl.userId,
          networkId = testNetwork.id,
          idpLoginId = User.buildIdpLoginId(testNetwork.networkCode, email, multiNetworkEnabled = true),
          loginMode = LoginMode.ClientCredentials,
          userType = UserType.System,
          firstName = "UnitTestNetwork",
          lastName = "API User",
          createdAt = now,
          updatedAt = now
        )

        when(networkService.getNetworkById(requesterAcl, systemUser.networkId)) thenReturn testNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(systemUser.email, systemUser.networkId)) thenReturn false.successFuture
        when(oktaService.createApplication(any[String])(any(classOf[TraceId]), any(classOf[ExecutionContext]))) thenReturn Future(UserInfo("OktaClientId", Some("OktaSecret")))
        val event: EventInfo = EventInfo(UserDomainEvent.UserCreated, triggeredBy = requesterAcl.userId, traceId)
        when(userRepo.insertUser(any[String], matchWithDynamicFieldsExcluded(systemUser.copy(eventInfo = event)))(any(classOf[TraceId]))) thenReturn systemUser.successFuture

        val userAndSecret: Future[(User, Option[String])] = userService.handleInsertRequest(requesterAcl, insertRequest(systemUser))
        userAndSecret.map(_._1) assertResult systemUser
      }

      "requester has EditClientCredentialsViaNetwork capability then user can insert System User" in new Scope {
        val testNetwork: Network = TestNetwork()
        val requesterAcl: UserACL = TestUserACL("testUser", testNetwork.id, capabilities = Set(EditClientCredentialsViaNetwork))
        val systemUser: User = TestUser(
          id = "OktaClientId",
          email = email,
          createdBy = requesterAcl.userId,
          updatedBy = requesterAcl.userId,
          networkId = testNetwork.id,
          idpLoginId = User.buildIdpLoginId(testNetwork.networkCode, email, multiNetworkEnabled = true),
          loginMode = LoginMode.ClientCredentials,
          userType = UserType.System,
          firstName = "UnitTestNetwork",
          lastName = "API User",
          createdAt = now,
          updatedAt = now
        )

        when(networkService.getNetworkById(requesterAcl, systemUser.networkId)) thenReturn testNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(systemUser.email, systemUser.networkId)) thenReturn false.successFuture
        when(userRepo.getUsersCount(Set(SystemUsersFilter(testNetwork.id.toString)))) thenReturn Future.successful(1L)

        val event: EventInfo = EventInfo(UserDomainEvent.UserCreated, triggeredBy = requesterAcl.userId, traceId)

        when(userRepo.insertUser(any[String], matchWithDynamicFieldsExcluded(systemUser.copy(eventInfo = event)))(any(classOf[TraceId]))) thenReturn systemUser.successFuture
        when(oktaService.createApplication(any[String])(any(classOf[TraceId]), any(classOf[ExecutionContext]))) thenReturn Future(UserInfo("OktaClientId", Some("OktaSecret")))
        userService.handleInsertRequest(requesterAcl, insertRequest(systemUser)).map(_._1) assertResult systemUser
      }

      "requester has EditClientCredentialsViaNetwork capability, but network has >= configured system users then user cannot insert System User" in new Scope {
        val testNetwork: Network = TestNetwork(maxSystemUsers = Some(1))
        val requesterAcl: UserACL = TestUserACL("testUser", testNetwork.id, capabilities = Set(EditClientCredentialsViaNetwork))
        val systemUser: User = TestUser(
          id = "OktaClientId",
          email = email,
          createdBy = requesterAcl.userId,
          updatedBy = requesterAcl.userId,
          networkId = testNetwork.id,
          idpLoginId = User.buildIdpLoginId(testNetwork.networkCode, email, multiNetworkEnabled = true),
          loginMode = LoginMode.ClientCredentials,
          userType = UserType.System,
          firstName = "UnitTestNetwork",
          lastName = "API User",
          createdAt = now,
          updatedAt = now
        )

        when(networkService.getNetworkById(requesterAcl, systemUser.networkId)) thenReturn testNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(systemUser.email, systemUser.networkId)) thenReturn false.successFuture
        when(userRepo.getUsersCount(Set(SystemUsersFilter(testNetwork.id.toString)))) thenReturn Future.successful(1L)

        val event: EventInfo = EventInfo(UserDomainEvent.UserCreated, triggeredBy = requesterAcl.userId, traceId)

        when(userRepo.insertUser(any[String], matchWithDynamicFieldsExcluded(systemUser.copy(eventInfo = event)))(any(classOf[TraceId]))) thenReturn systemUser.successFuture
        when(oktaService.createApplication(any[String])(any(classOf[TraceId]), any(classOf[ExecutionContext]))) thenReturn Future(UserInfo("generatedUserId", Some("clientSecret")))
        val insertedUser: Future[User] = userService.handleInsertRequest(requesterAcl, insertRequest(systemUser)).map(_._1)
        insertedUser assertFailureEx HttpError.forbidden("Number of system users exceeds network limit of 1")
      }

      "requester has EditClientCredentialsViaNetwork capability, but cannot create System User in another network" in new Scope {
        val testNetwork: Network = TestNetwork(maxSystemUsers = Some(1))
        val requesterAcl: UserACL = TestUserACL("testUser", testNetwork.id, capabilities = Set(EditClientCredentialsViaNetwork))
        val systemUser: User = TestUser(
          id = "OktaClientId",
          email = email,
          createdBy = requesterAcl.userId,
          updatedBy = requesterAcl.userId,
          networkId = adminNetwork.id,
          idpLoginId = User.buildIdpLoginId(adminNetwork.networkCode, email, multiNetworkEnabled = true),
          loginMode = LoginMode.ClientCredentials,
          userType = UserType.System,
          firstName = "UnitTestNetwork",
          lastName = "API User",
          createdAt = now,
          updatedAt = now
        )

        when(networkService.getNetworkById(requesterAcl, systemUser.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(systemUser.email, systemUser.networkId)) thenReturn false.successFuture
        when(userRepo.getUsersCount(Set(SystemUsersFilter(adminNetwork.id.toString)))) thenReturn 0L.successFuture
        val event: EventInfo = EventInfo(UserDomainEvent.UserCreated, triggeredBy = requesterAcl.userId, traceId)
        when(userRepo.insertUser(any[String], matchWithDynamicFieldsExcluded(systemUser.copy(eventInfo = event)))(any(classOf[TraceId]))) thenReturn systemUser.successFuture
        when(oktaService.createApplication(any[String])(any(classOf[TraceId]), any(classOf[ExecutionContext]))) thenReturn UserInfo("OktaClientId", Some("OktaSecret")).successFuture

        val insertedUser: Future[User] = userService.handleInsertRequest(requesterAcl, insertRequest(systemUser)).map(_._1)
        insertedUser assertFailureEx HttpError.unauthorized("User does not have correct access keys to create System User in this network")
      }

      "requester has only uploadUserViaPurview capability and still can on board a user under purview" in new Scope {

        val purviewNetwork1: Id.NetworkId = NetworkId("purviewNetwork1")
        val purviewNetwork2: Id.NetworkId = NetworkId("purviewNetwork2")

        val requesterAcl2: UserACL = TestUserACL(userId, adminNetwork.id, capabilities = Set(UploadUserViaPurview), userPurviewIds = Set(purviewNetwork1, purviewNetwork2, adminNetwork.id))

        when(networkService.getNetworkById(requesterAcl2, userInAdminNetwork.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(email, userInAdminNetwork.networkId)) thenReturn false.successFuture

        val event = EventInfo(UserDomainEvent.UserCreated, triggeredBy = adminRequesterAcl.userId, traceId)

        when(userRepo.insertUser(any[String], matchWithDynamicFieldsExcluded(userInAdminNetwork.copy(email = email, eventInfo = event)))(any(classOf[TraceId]))) thenReturn
          userInAdminNetwork.copy(email = email).successFuture

        userService.handleInsertRequest(requesterAcl2, insertRequest(userInAdminNetwork.copy(email = email))).map(userAndSecret => userAndSecret._1) assert {
          user: User => user.email == email
        }
      }

      "A user with UploadUserViewNetworkType belonging to network of type Admin will be able to create a new user in any network" in new Scope {

        val requesterAcl2: UserACL = TestUserACL(userId, adminNetwork.id, capabilities = Set(UploadUserViaNetworkType),
          networkTypes = Some(List(NetworkType.Admin))) // This should be replaced with NetworkType.Admin

        when(networkService.getNetworkById(requesterAcl2, userInAdminNetwork.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(email, userInAdminNetwork.networkId)) thenReturn false.successFuture

        val event = EventInfo(UserDomainEvent.UserCreated, triggeredBy = requesterAcl2.userId, traceId)

        when(userRepo.insertUser(any[String], matchWithDynamicFieldsExcluded(userInAdminNetwork.copy(email = email, eventInfo = event)))(any(classOf[TraceId]))) thenReturn
          userInAdminNetwork.copy(email = email).successFuture

        userService.handleInsertRequest(requesterAcl2, insertRequest(userInAdminNetwork.copy(email = email))).map(userAndSecret => userAndSecret._1) assert {
          user: User => user.email == email
        }
      }

      "A user with UploadUserViewNetworkType belonging to network with networkType other than Admin and SMAManager will " +
        "not be able to create a new user in any network" in new Scope {

        val requesterAcl2: UserACL = TestUserACL(userId, adminNetwork.id, capabilities = Set(UploadUserViaNetworkType),
          networkTypes = Some(NetworkType.Values.diff(List(NetworkType.Admin, NetworkType.SMAManager)).toList))

        when(networkService.getNetworkById(requesterAcl2, userInAdminNetwork.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(email, userInAdminNetwork.networkId)) thenReturn false.successFuture

        val event = EventInfo(UserDomainEvent.UserCreated, triggeredBy = requesterAcl2.userId, traceId)

        when(userRepo.insertUser(any[String], matchWithDynamicFieldsExcluded(userInAdminNetwork.copy(email = email, eventInfo = event)))(any(classOf[TraceId]))) thenReturn
          userInAdminNetwork.copy(email = email).successFuture

        val userAndSecret: Future[(User, Option[String])] = userService.handleInsertRequest(requesterAcl2, insertRequest(userInAdminNetwork.copy(email = email)))
        val insertedUser: Future[User] = userAndSecret.map(userAndSecret => userAndSecret._1)
        insertedUser assertFailureEx HttpError.unauthorized("User does not have right access keys to upload another user in this network")

      }

      "A ReadOnlyAdmin requester should not be able to create a new user" in new Scope {
        val requesterAcl2: UserACL = TestUserACL(userId, adminNetwork.id, capabilities = Set(ReadOnlyAdmin))

        when(networkService.getNetworkById(requesterAcl2, userInAdminNetwork.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(userInAdminNetwork.email, userInAdminNetwork.networkId)) thenReturn false.successFuture

        val userAndSecret: Future[(User, Option[String])] = userService.handleInsertRequest(requesterAcl2, insertRequest(userInAdminNetwork))
        val insertedUser: Future[User] = userAndSecret.map(userAndSecret => userAndSecret._1)
        insertedUser assertFailureEx HttpError.unauthorized("User does not have right access keys to upload another user in this network")

      }

      "PreviewUser email is not formatted correctly" in new Scope {
        val previewUser = userInAdminNetwork.copy(userType = UserType.Preview, email = "<EMAIL>")
        when(networkService.getNetworkById(adminRequesterAcl, previewUser.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(previewUser.email, previewUser.networkId)) thenReturn false.successFuture
        when(userRepo.insertUser(any[String], any[User])(any(classOf[TraceId]))) thenReturn previewUser.successFuture

        val insertedUser = userService.handleInsertRequest(adminRequesterAcl, insertRequest(previewUser)).map(userAndSecret => userAndSecret._1)
        insertedUser assertFailureEx HttpError.badRequest("Preview user email was not formatted correctly, needs to be preview[Number]@icapitalnetwork.com")
      }

      "user does not have correct access rights to create PreviewUser" in new Scope {
        val previewUser = userInAdminNetwork.copy(userType = UserType.Preview, email = "<EMAIL>")
        when(networkService.getNetworkById(faRequesterAcl, previewUser.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(previewUser.email, previewUser.networkId)) thenReturn false.successFuture
        when(userRepo.insertUser(any[String], any[User])(any(classOf[TraceId]))) thenReturn previewUser.successFuture

        val insertedUser = userService.handleInsertRequest(faRequesterAcl, insertRequest(previewUser)).map(userAndSecret => userAndSecret._1)
        insertedUser assertFailureEx HttpError.unauthorized("User does not have right access keys to create Preview User")
      }

      "PreviewUser user successfully created" in new Scope {
        val previewUser = userInAdminNetwork.copy(userType = UserType.Preview, email = "<EMAIL>")
        when(networkService.getNetworkById(adminRequesterAcl, previewUser.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(previewUser.email, previewUser.networkId)) thenReturn false.successFuture
        when(userRepo.insertUser(any[String], any[User])(any(classOf[TraceId]))) thenReturn previewUser.successFuture

        userService.handleInsertRequest(adminRequesterAcl, insertRequest(previewUser)).map(userAndSecret => userAndSecret._1) assertResult previewUser
      }

      "PreviewUser user successfully created with number" in new Scope {
        val previewUser = userInAdminNetwork.copy(userType = UserType.Preview, email = "<EMAIL>")
        when(networkService.getNetworkById(adminRequesterAcl, previewUser.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.existsByEmailAndNetwork(previewUser.email, previewUser.networkId)) thenReturn false.successFuture
        when(userRepo.insertUser(any[String], any[User])(any(classOf[TraceId]))) thenReturn previewUser.successFuture

        userService.handleInsertRequest(adminRequesterAcl, insertRequest(previewUser)).map(userAndSecret => userAndSecret._1) assertResult previewUser
      }

      "Cannot reset password of preview user" in new Scope {
        val previewUser = userInAdminNetwork.copy(userType = UserType.Preview, email = "<EMAIL>")
        when(userRepo.getById(any[String])(any[Set[String]])(any(classOf[TraceId]))) thenReturn Some(previewUser).successFuture
        userService.resetPassword(adminRequesterAcl, "previewUserId") assertFailureEx HttpError.badRequest("Can't reset password for Preview User: previewUserId")
      }

      "admin user can create new user with unified fields" in new Scope {
        val acl = adminRequesterAcl
        val icnGroups = Set("1111")
        val icnRoles = Set("role")
        val request = UpsertUserRequest
          .fromUser(userInAdminNetwork)
          .copy(
            icnGroups = icnGroups.some,
            icnRoles = icnRoles.some,
            upsertField = UpsertField.Email.some
          )

        when(networkService.getNetworkById(meq(acl), meq(request.networkId))(any[TraceId]))
          .thenReturn(Future.successful(adminNetwork))
        when(userRepo.existsByEmailAndNetwork(email, userInAdminNetwork.networkId)).thenReturn(false.successFuture)
        when(userRepo.exists(userInAdminNetwork.id)).thenReturn(false.successFuture)
        when(userRepo.getUserByUniqueId(meq(UniqueUserId.Email(userInAdminNetwork.email, userInAdminNetwork.networkId)))(any[Set[String]])(any[TraceId]))
          .thenReturn(Future.successful(None))
        when(userRepo.insertUser(meq(userInAdminNetwork.id), matchWithDynamicFieldsExcluded(userInAdminNetwork.copy(
          icnGroups = icnGroups,
          icnRoles = icnRoles,
          eventInfo = EventInfo(UserDomainEvent.UserCreated, adminRequesterAcl.userId, traceId)
        )))(any(classOf[TraceId])))
          .thenReturn(
            userInAdminNetwork.copy(icnGroups = icnGroups, icnRoles = icnRoles).successFuture)

        val userCreated = userService.handleInsertRequest(acl, request, None).await

        userCreated._1.icnGroups shouldBe icnGroups
        userCreated._1.icnRoles shouldBe icnRoles
        verify(mappings, never).get(any[UserNetworkMappingRequest])(any[TraceId])
      }

      "non-admin user can't create new user with unified fields if user has no wlp and firm" in new Scope {
        val acl = faRequesterAcl
        val icnGroups = Set("1111")
        val icnRoles = Set("role")
        val request = UpsertUserRequest
          .fromUser(userInAdminNetwork)
          .copy(
            icnGroups = icnGroups.some,
            icnRoles = icnRoles.some,
          )

        when(mappings.get(mappingRequest(acl))).thenReturn(Future.successful(Some(mapping(acl, "wlp1", "firm1"))))


        val httpError = recoverToExceptionIf[HttpError] {
          userService.handleInsertRequest(acl, request, None)
        }.await

        httpError.status shouldBe StatusCodes.Unauthorized
      }

      "non-admin user with upload capability can't create new user with unified fields if network is not mapped" in new Scope {
        val acl = faRequesterAcl
        val icnGroups = Set("1111")
        val icnRoles = Set("role")
        val request = UpsertUserRequest
          .fromUser(userInAdminNetwork)
          .copy(
            icnGroups = icnGroups.some,
            icnRoles = icnRoles.some,
          )

        when(mappings.get(mappingRequest(acl))).thenReturn(Future.successful(None))


        val httpError = recoverToExceptionIf[HttpError] {
          userService.handleInsertRequest(acl, request, None)
        }.await

        httpError.status shouldBe StatusCodes.Unauthorized
      }

      "non-admin user with upload capability can create new user with unified fields if in mapped network " in new Scope {
        val acl = faRequesterAcl.copy(
          whiteLabelPartnerId = "wlp1".some,
          firmId = "firm1".some,
          capabilities = faRequesterAcl.capabilities + UploadUserViaNetwork
        )
        val icnGroups = Set("1111")
        val icnRoles = Set("role")
        val request = UpsertUserRequest
          .fromUser(userInAdminNetwork)
          .copy(
            icnGroups = icnGroups.some,
            icnRoles = icnRoles.some,
            whiteLabelPartnerId = "wlp1".some,
            firmId = "firm1".some
          )

        when(mappings.get(mappingRequest(acl))).thenReturn(Future.successful(Some(mapping(acl, "wlp1", "firm1"))))

        when(networkService.getNetworkById(meq(acl), meq(request.networkId))(any[TraceId]))
          .thenReturn(Future.successful(adminNetwork))
        when(userRepo.existsByEmailAndNetwork(email, userInAdminNetwork.networkId)).thenReturn(false.successFuture)
        when(userRepo.exists(userInAdminNetwork.id)).thenReturn(false.successFuture)
        when(userRepo.getUserByUniqueId(meq(UniqueUserId.Email(userInAdminNetwork.email, userInAdminNetwork.networkId)))(any[Set[String]])(any[TraceId]))
          .thenReturn(Future.successful(None))
        when(userRepo.insertUser(meq(acl.userId), matchWithDynamicFieldsExcluded(userInAdminNetwork.copy(
          createdBy = acl.userId,
          updatedBy = acl.userId,
          icnGroups = icnGroups,
          icnRoles = icnRoles,
          whiteLabelPartnerId = "wlp1".some,
          firmId = "firm1".some,
          eventInfo = EventInfo(UserDomainEvent.UserCreated, acl.userId, traceId)
        )))(any(classOf[TraceId])))
          .thenReturn(
            userInAdminNetwork.copy(icnGroups = icnGroups, icnRoles = icnRoles).successFuture)

        val userCreated = userService.handleInsertRequest(acl, request, None).await

        userCreated._1.icnGroups shouldBe icnGroups
        userCreated._1.icnRoles shouldBe icnRoles
      }
    }

    "handle batch insert" when {

      "multiple new users" in new InsertUserFixture {
        override def newUsers: Seq[User] = Seq("id1", "id2").map { i =>
          TestUser(
            id = i,
            email = s"$<EMAIL>",
            createdAt = now,
            updatedAt = now
          )
        }

        private def request = newUsers.map(insertRequest)

        private def expected = BatchUserUpsertResponse(newUsers.map(u => BatchUser.fromUser(u)), Seq.empty)

        assertEquals(userService.handleBulkUpsertRequest(adminRequesterAcl, request), expected)
      }

      "partial batch failure due to conflicts" in new InsertUserFixture {
        override def newUsers: Seq[User] = Seq("id1", "id2").map { i =>
          TestUser(
            id = i,
            email = s"$<EMAIL>",
            createdAt = now,
            updatedAt = now
          )
        }

        override def conflictUsers: Seq[User] = Seq(newUsers.head)

        private def request = newUsers.map(insertRequest)

        private def expected = BatchUserUpsertResponse(
          newUsers.tail.map(u => BatchUser.fromUser(u)),
          Seq(UserInsertFailure(request.head, "409", "conflict")))

        assertEquals(userService.handleBulkUpsertRequest(adminRequesterAcl, request), expected)
      }

      "full batch failure due to fatal error" in new InsertUserFixture {
        override def newUsers: Seq[User] = Seq("id1", "id2").map { i =>
          TestUser(
            id = i,
            email = s"$<EMAIL>",
            createdAt = now,
            updatedAt = now
          )
        }

        override def fatalErrorUsers: Seq[User] = Seq(newUsers.head)

        private def request = newUsers.map(insertRequest)

        userService.handleBulkUpsertRequest(adminRequesterAcl, request) assertFailure[HttpError]()
      }

    }

    "handle Deactivate" when {

      "remove him from network approvers if he is FA manager" in new Scope {

        private val deactivationEvent = EventInfo(UserDomainEvent.UserDeactivated, triggeredBy = adminRequesterAcl.userId, correlationId = traceId)
        private val deactivatedUser = faManagerUser.copy(
          omsId = None,
          distributorId = None,
          isActive = false,
          eventInfo = deactivationEvent
        ).refreshUpdatedFields(adminRequesterAcl.userId)

        when(userRepo.getById(meq(faManagerUser.id))(any[Set[String]])(any[TraceId])) thenReturn faManagerUser.some.successFuture
        when(userRepo.updateUser(any[String], matchWithDynamicFieldsExcluded(deactivatedUser))(any(classOf[TraceId]))).thenReturn(faManagerUser.successFuture)
        when(networkService.removeUserFromApprovers(adminRequesterAcl, faManagerUser)(traceId)) thenReturn adminNetwork.successFuture

        val res = userService.deactivateUser(adminRequesterAcl, faManagerUser.id)
        res assert { _: UserView => verify(networkService).removeUserFromApprovers(adminRequesterAcl, faManagerUser)(traceId) }

      }

      "remove users external ids" in new Scope {
        when(userRepo.getById(meq(faManagerUser.id))(any[Set[String]])(any[TraceId])) thenReturn faManagerUser.some.successFuture
        when(userRepo.updateUser(meq(adminRequesterAcl.userId), any[User])(meq(traceId))) thenReturn faManagerUser.successFuture
        when(networkService.removeUserFromApprovers(adminRequesterAcl, faManagerUser)(traceId)) thenReturn adminNetwork.successFuture

        val res = userService.deactivateUser(adminRequesterAcl, faManagerUser.id)
        val expectedUser = faManagerUser.copy(
          omsId = None,
          distributorId = None,
          isActive = false,
          updatedBy = adminRequesterAcl.userId
        )
        val event = EventInfo(UserDomainEvent.UserDeactivated, adminRequesterAcl.userId, traceId)
        res assert { _: UserView => verify(userRepo).updateUser(meq(adminRequesterAcl.userId), matchWithDynamicFieldsExcluded(expectedUser.copy(eventInfo = event)))(meq(traceId)) }
      }

      "not remove him from network approvers if he is not an FA manager" in new Scope {
        when(userRepo.getById(meq(userInAdminNetwork.id))(any[Set[String]])(any[TraceId])) thenReturn userInAdminNetwork.some.successFuture
        when(userRepo.updateUser(any[String], any[User])(any(classOf[TraceId]))).thenReturn(userInAdminNetwork.successFuture)
        when(networkService.removeUserFromApprovers(adminRequesterAcl, userInAdminNetwork)(traceId)) thenReturn adminNetwork.successFuture

        val res = userService.deactivateUser(adminRequesterAcl, userInAdminNetwork.id)
        res assert { _: UserView => verify(networkService, never()).removeUserFromApprovers(adminRequesterAcl, userInAdminNetwork)(traceId) }
      }

      "user has an email which shouldn't be deactivated" in new Scope {

        val userWithExcludedEmail = userInAdminNetwork.copy(email = "<EMAIL>")

        when(userRepo.getById(meq(userInAdminNetwork.id))(any[Set[String]])(any[TraceId])) thenReturn userWithExcludedEmail.some.successFuture
        when(userRepo.updateUser(any[String], any[User])(any(classOf[TraceId]))).thenReturn(userInAdminNetwork.successFuture)
        when(networkService.removeUserFromApprovers(adminRequesterAcl, userInAdminNetwork)(traceId)) thenReturn adminNetwork.successFuture

        userService.deactivateUser(adminRequesterAcl, userInAdminNetwork.id) assertFailure[HttpError]()
      }
    }

    "handle activate" when {

      "user exists in mongo and okta" in new Scope {
        when(userRepo.getById(userId)(Set("admin"))) thenReturn userInAdminNetwork.some.successFuture

        userService.activateUser(adminRequesterAcl, userInAdminNetwork.id, false) assertResult UserView(userInAdminNetwork)
      }

      "user doesn't exist in mongo" in new Scope {
        when(userRepo.getById(userId)(Set("admin"))) thenReturn None.successFuture

        userService.activateUser(adminRequesterAcl, userInAdminNetwork.id, false) assertFailure[HttpError]()
      }

      "user has an email which shouldn't be activated" in new Scope {
        when(userRepo.getById(userId)(Set("admin"))) thenReturn userInAdminNetwork.copy(email = s"test${config.internalDomains.head}").some.successFuture

        userService.activateUser(adminRequesterAcl, userInAdminNetwork.id, false) assertFailure[HttpError]()
      }
    }
    "handle update" when {

      "if request contains one invalid custodian (EnumNotFound) in custodianFaNumbers then throw exception" in new Scope {
        userService.handleInsertRequest(
          adminRequesterAcl,
          updateRequest(upsertUser.copy(custodianFaNumbers = Set(CustodianFaNumber(EnumNotFound, "123abc"))))
        ) assertFailureEx HttpError.badRequest("Invalid Enum/s detected in \"custodianFaNumbers\"")
      }

      "request contains at least one invalid custodian (EnumNotFound) in custodianFaNumbers then throw exception" in new Scope {
        userService.handleInsertRequest(
          adminRequesterAcl,
          updateRequest(upsertUser.copy(custodianFaNumbers = Set(
            CustodianFaNumber(Cetera, "123abc"),
            CustodianFaNumber(EnumNotFound, "123abc")
          )))
        ) assertFailureEx HttpError.badRequest("Invalid Enum/s detected in \"custodianFaNumbers\"")
      }

      "request contains valid custodianFaNumbers + existing user updated then should succeed" in new Scope {
        val userWithOkCustodians = userInAdminNetwork.copy(custodianFaNumbers = Set(CustodianFaNumber(Cetera, "123abc")))

        // happy path logic
        when(networkService.getNetworkById(adminRequesterAcl, userInAdminNetwork.networkId)(traceId)) thenReturn adminNetwork.successFuture
        when(userRepo.getById(meq(userInAdminNetwork.id))(any[Set[String]])(any[TraceId])) thenReturn userWithOkCustodians.some.successFuture
        when(userRepo.updateUser(any[String], matchWithDynamicFieldsExcluded(userWithOkCustodians.copy(
          idpId = userInAdminNetwork.idpId,
          eventInfo = EventInfo(UserDomainEvent.UserUpdated, adminRequesterAcl.userId, traceId)
        )))(any(classOf[TraceId]))).thenReturn(userWithOkCustodians.successFuture)

        userService.handleUpdateRequest(adminRequesterAcl, updateRequest(userWithOkCustodians)) assertResult userWithOkCustodians
      }

      "request contains valid custodianFaNumbers + userRepository should get expected custodianFaNumber data as input" in new Scope {
        val userWithOkCustodians = userInAdminNetwork.copy(custodianFaNumbers = Set(CustodianFaNumber(Cetera, "123abc")))

        // happy path logic
        when(networkService.getNetworkById(adminRequesterAcl, userInAdminNetwork.networkId)(traceId)) thenReturn adminNetwork.successFuture
        when(userRepo.getById(meq(userInAdminNetwork.id))(any[Set[String]])(any[TraceId])) thenReturn userWithOkCustodians.some.successFuture
        when(userRepo.updateUser(any[String], matchWithDynamicFieldsExcluded(userWithOkCustodians.copy(
          idpId = userInAdminNetwork.idpId,
          eventInfo = EventInfo(UserDomainEvent.UserUpdated, adminRequesterAcl.userId, traceId)
        )))(any(classOf[TraceId]))).thenReturn(userWithOkCustodians.successFuture)
        userService.handleUpdateRequest(adminRequesterAcl, updateRequest(userWithOkCustodians)) assertResult userWithOkCustodians

        // make sure what userRepo receives is the data we want to send
        val captor: ArgumentCaptor[User] = ArgumentCaptor.forClass(classOf[User])
        verify(userRepo, times(1)).updateUser(any[String], captor.capture())(any[TraceId])
        assert(captor.getValue.custodianFaNumbers == userWithOkCustodians.custodianFaNumbers)
      }

      "existing user updated" in new Scope {
        when(networkService.getNetworkById(adminRequesterAcl, userInAdminNetwork.networkId)(traceId)) thenReturn adminNetwork.successFuture
        when(userRepo.getById(meq(userInAdminNetwork.id))(any[Set[String]])(any[TraceId])) thenReturn userInAdminNetwork.some.successFuture
        when(userRepo.updateUser(any[String], matchWithDynamicFieldsExcluded(userInAdminNetwork.copy(
          idpId = userInAdminNetwork.idpId,
          eventInfo = EventInfo(UserDomainEvent.UserUpdated, adminRequesterAcl.userId, traceId)
        )))(any(classOf[TraceId]))).thenReturn(userInAdminNetwork.successFuture)

        userService.handleUpdateRequest(adminRequesterAcl, updateRequest(userInAdminNetwork)) assertResult userInAdminNetwork
      }

      "existing user updated with partial update semantics" in new Scope {
        when(networkService.getNetworkById(adminRequesterAcl, userInAdminNetwork.networkId)(traceId)) thenReturn adminNetwork.successFuture
        when(userRepo.getById(meq(userInAdminNetwork.id))(any[Set[String]])(any[TraceId])) thenReturn userInAdminNetwork.some.successFuture
        when(userRepo.updateUser(any[String], matchWithDynamicFieldsExcluded(userInAdminNetwork.copy(
          idpId = userInAdminNetwork.idpId,
          eventInfo = EventInfo(UserDomainEvent.UserUpdated, adminRequesterAcl.userId, traceId)
        )))(any(classOf[TraceId]))).thenReturn(userInAdminNetwork.successFuture)

        private val partialUpdate = updateRequest(userInAdminNetwork).copy(
          tradewebEligible = None,
          regSEligible = None,
          locations = None,
          faNumbers = None,
          customRoles = None,
          licenses = None,
          cusips = None,
          purviewLicenses = None,
          purviewNsccCodes = None
        )
        userService.handleUpdateRequest(adminRequesterAcl, partialUpdate) assertResult userInAdminNetwork
      }

      "existing user updated via a sync event" in new Scope {
        when(networkService.getNetworkById(adminRequesterAcl, userInAdminNetwork.networkId)(traceId)) thenReturn adminNetwork.successFuture
        when(userRepo.getById(meq(userInAdminNetwork.id))(any[Set[String]])(any[TraceId])) thenReturn userInAdminNetwork.some.successFuture
        when(userRepo.updateUser(any[String], matchWithDynamicFieldsExcluded(userInAdminNetwork.copy(
          idpId = userInAdminNetwork.idpId,
          eventInfo = EventInfo(UserDomainEvent.UserSynced, adminRequesterAcl.userId, traceId),
          userSyncedAt = userInAdminNetwork.updatedAt.some
        )))(any(classOf[TraceId]))).thenReturn(userInAdminNetwork.successFuture)

        userService.handleUpdateRequest(adminRequesterAcl, updateRequest(userInAdminNetwork), UserDomainEvent.UserSynced.some) assertResult userInAdminNetwork
      }

      "user doesn't exist in mongo" in new Scope {
        when(networkService.getNetworkById(adminRequesterAcl, userInAdminNetwork.networkId)(traceId)) thenReturn adminNetwork.successFuture
        when(userRepo.getById(meq(userInAdminNetwork.id))(any[Set[String]])(any[TraceId])) thenReturn None.successFuture

        userService.handleUpdateRequest(adminRequesterAcl, updateRequest(userInAdminNetwork)) assertFailure[HttpError]()
      }


      "user is not active if request doesn't have isActive" in new Scope {
        when(networkService.getNetworkById(adminRequesterAcl, userInAdminNetwork.networkId)(traceId)) thenReturn adminNetwork.successFuture
        when(userRepo.getById(meq(userInAdminNetwork.id))(any[Set[String]])(any[TraceId])) thenReturn userInAdminNetwork.copy(isActive = false).some.successFuture
        when(userRepo.updateUser(any[String], matchWithDynamicFieldsExcluded(userInAdminNetwork.copy(
          idpId = userInAdminNetwork.idpId,
          eventInfo = EventInfo(UserDomainEvent.UserUpdated, adminRequesterAcl.userId, traceId),
          isActive = false
        )))(any(classOf[TraceId]))).thenReturn(userInAdminNetwork.copy(isActive = false).successFuture)

        userService.handleUpdateRequest(adminRequesterAcl, updateRequest(userInAdminNetwork).copy(isActive = Option(false))) assert { (u: User) => u.isActive shouldBe false }
      }

      "user is active if request have isActive set to true" in new Scope {
        when(networkService.getNetworkById(adminRequesterAcl, userInAdminNetwork.networkId)(traceId)) thenReturn adminNetwork.successFuture
        when(userRepo.getById(meq(userInAdminNetwork.id))(any[Set[String]])(any[TraceId])) thenReturn userInAdminNetwork.copy(isActive = false).some.successFuture
        when(userRepo.updateUser(any[String], matchWithDynamicFieldsExcluded(userInAdminNetwork.copy(
          idpId = userInAdminNetwork.idpId,
          eventInfo = EventInfo(UserDomainEvent.UserActivatedDontSendEmail, adminRequesterAcl.userId, traceId),
          isActive = true
        )))(any(classOf[TraceId]))).thenReturn(userInAdminNetwork.copy(isActive = true).successFuture)

        userService.handleUpdateRequest(adminRequesterAcl, updateRequest(userInAdminNetwork).copy(isActive = Option(true))) assert { (u: User) => u.isActive shouldBe true }
      }


      "mongo is not accessible" in new Scope {
        when(networkService.getNetworkById(adminRequesterAcl, userInAdminNetwork.networkId)(traceId)) thenReturn adminNetwork.successFuture
        when(userRepo.getById(meq(userInAdminNetwork.id))(any[Set[String]])(any[TraceId])) thenReturn Future.failed(new IOException("mongo error"))

        userService.handleUpdateRequest(adminRequesterAcl, updateRequest(userInAdminNetwork)) assertFailure[IOException]()
      }

      "Author does not have entitlement to update user info" in new Scope {
        when(networkService.getNetworkById(adminRequesterAcl.copy(capabilities = Set()), userInAdminNetwork.networkId)(traceId)) thenReturn adminNetwork.successFuture
        when(userRepo.getById(meq(userInAdminNetwork.id))(any[Set[String]])(any[TraceId])) thenReturn userInAdminNetwork.some.successFuture

        userService.handleUpdateRequest(adminRequesterAcl.copy(capabilities = Set()), updateRequest(userInAdminNetwork)) assertFailure[HttpError]()
      }

      "Author has only uploadUserViaPurview capability and still can update a user under purview" in new Scope {
        val purviewNetwork1 = NetworkId("purviewNetwork1")
        val purviewNetwork2 = NetworkId("purviewNetwork2")

        val requesterAcl2 = TestUserACL(userId, adminNetwork.id, roles = Set(UserRole.EqPIPGGSAdmin), capabilities = Set(UploadUserViaPurview), userPurviewIds = Set(purviewNetwork1, purviewNetwork2, adminNetwork.id))

        when(networkService.getNetworkById(requesterAcl2, userInAdminNetwork.networkId)) thenReturn adminNetwork.successFuture
        when(userRepo.getById(meq(userInAdminNetwork.id))(any[Set[String]])(any[TraceId])) thenReturn userInAdminNetwork.some.successFuture

        val updatedName = "updatedName"

        when(userRepo.updateUser(meq(userInAdminNetwork.id), matchWithDynamicFieldsExcluded(userInAdminNetwork.copy(
          firstName = updatedName,
          eventInfo = EventInfo(UserDomainEvent.UserUpdated, adminRequesterAcl.userId, traceId)
        )))(any(classOf[TraceId]))).thenReturn(
          userInAdminNetwork.copy(firstName = updatedName).successFuture)
        when(userRepo.existsByEmailAndNetwork(email, userInAdminNetwork.networkId)).thenReturn(true.successFuture)

        userService.handleUpdateRequest(requesterAcl2, updateRequest(userInAdminNetwork).copy(firstName = updatedName)) assert { user: User => user.firstName == updatedName }
      }

      "admin user can update unified fields" in new Scope {
        val acl = adminRequesterAcl
        val icnGroups = Set("1111")
        val icnRoles = Set("role")
        val request = UpsertUserRequest
          .fromUser(userInAdminNetwork)
          .copy(
            icnGroups = icnGroups.some,
            icnRoles = icnRoles.some,
            upsertField = UpsertField.Email.some
          )

        when(networkService.getNetworkById(meq(acl), meq(request.networkId))(any[TraceId]))
          .thenReturn(Future.successful(adminNetwork))

        when(userRepo.getUserByUniqueId(meq(UniqueUserId.Email(userInAdminNetwork.email, userInAdminNetwork.networkId)))(any[Set[String]])(any[TraceId]))
          .thenReturn(Future.successful(userInAdminNetwork.copy(icnGroups = icnGroups).some))
        when(userRepo.updateUser(meq(userInAdminNetwork.id), matchWithDynamicFieldsExcluded(userInAdminNetwork.copy(
          icnGroups = icnGroups,
          icnRoles = icnRoles,
          eventInfo = EventInfo(UserDomainEvent.UserUpdated, adminRequesterAcl.userId, traceId)
        )))(any(classOf[TraceId])))
          .thenReturn(
            userInAdminNetwork.copy(icnGroups = icnGroups, icnRoles = icnRoles).successFuture)

        val userUpdated = userService.handleInsertRequest(acl, request, None).await

        userUpdated._1.icnGroups shouldBe icnGroups
        userUpdated._1.icnRoles shouldBe icnRoles
      }

      "non-admin user with upload capability can update unified field(s) when in mapped network " in new Scope {
        val acl = faRequesterAcl.copy(capabilities = faRequesterAcl.capabilities + UploadUserViaNetwork)

        val request = UpsertUserRequest
          .fromUser(userInAdminNetwork)
          .copy(
            whiteLabelPartnerId = Some("wlp1"),
            firmId = Some("firm1"),
            icnRoles = Set("role1").some,
            upsertField = UpsertField.Email.some
          )

        when(mappings.get(mappingRequest(acl))).thenReturn(Future.successful(Some(mapping(acl, "wlp1", "firm1"))))

        when(networkService.getNetworkById(meq(acl), meq(request.networkId))(any[TraceId]))
          .thenReturn(Future.successful(adminNetwork))
        when(userRepo.getUserByUniqueId(meq(UniqueUserId.Email(userInAdminNetwork.email, userInAdminNetwork.networkId)))(any[Set[String]])(any[TraceId]))
          .thenReturn(Future.successful(userInAdminNetwork.copy(
            whiteLabelPartnerId = Some("wlp1"),
            firmId = Some("firm1")).some,
          ))
        when(userRepo.updateUser(meq(faRequesterAcl.userId), matchWithDynamicFieldsExcluded(userInAdminNetwork.copy(
          whiteLabelPartnerId = Some("wlp1"),
          firmId = Some("firm1"),
          icnRoles = Set("role1"),
          updatedBy = faRequesterAcl.userId,
          eventInfo = EventInfo(UserDomainEvent.UserUpdated, faRequesterAcl.userId, traceId)
        )))(any(classOf[TraceId])))
          .thenReturn(
            userInAdminNetwork.copy(
              whiteLabelPartnerId = Some("wlp1"),
              icnRoles = Set("role1"),
              firmId = Some("firm1")
            ).successFuture)
        val userUpdated = userService.handleInsertRequest(acl, request, None).await

        userUpdated._1.whiteLabelPartnerId shouldBe Some("wlp1")
        userUpdated._1.firmId shouldBe Some("firm1")
        userUpdated._1.icnRoles shouldBe Set("role1")
      }

      "non-admin user can't update unified field(s) without being in mapped network " in new Scope {
        val acl = faRequesterAcl.copy(capabilities = faRequesterAcl.capabilities + UploadUserViaNetwork)

        val request = UpsertUserRequest
          .fromUser(userInAdminNetwork)
          .copy(
            icnGroups = Set("group1").some
          )

        when(mappings.get(mappingRequest(acl))).thenReturn(Future.successful(Some(mapping(acl, "wlp1", "firm2"))))
        val httpError = recoverToExceptionIf[HttpError] {
          userService.handleInsertRequest(acl, request)
        }.await

        httpError.status shouldBe StatusCodes.Unauthorized
      }
    }

    "handle network update" when {
      "cannot switch networks of a Preview User" in new Scope {
        val authorizedUserAcl = adminRequesterAcl.copy(capabilities = Set(ViewUISwitchUsersNetworkCapability.name))
        val testUser = userInAdminNetwork.copy(email = "<EMAIL>")
        val newNetwork = NetworkId("some-net")
        val testPreviewUser = testUser.copy(userType = UserType.Preview)
        when(impersonationService.getByImpersonatedUserId(testPreviewUser.id)(traceId, systemAcl)).thenReturn(None.successFuture)
        userService.validateUpdate(authorizedUserAcl, updateRequest(testPreviewUser).copy(networkId = newNetwork), testPreviewUser) assertFailureMessage "403 Forbidden {\"messages\":[\"It is forbidden to change Preview User network\"]}"
      }

      "Preview User email is not formatted correctly" in new Scope {
        val authorizedUserAcl = adminRequesterAcl
        val testUser = userInAdminNetwork.copy(email = "<EMAIL>")
        val testPreviewUser = testUser.copy(userType = UserType.Preview)
        when(impersonationService.getByImpersonatedUserId(testPreviewUser.id)(traceId, systemAcl)).thenReturn(None.successFuture)
        userService.validateUpdate(authorizedUserAcl, updateRequest(testPreviewUser).copy(email = "<EMAIL>"), testPreviewUser) assertFailureMessage "403 Forbidden {\"messages\":[\"It is forbidden to change Preview User email\"]}"
      }

      "Preview User Type should not be changed" in new Scope {
        val authorizedUserAcl = adminRequesterAcl.copy(capabilities = Set(ViewUISwitchUsersNetworkCapability.name))
        val testUser = userInAdminNetwork.copy(email = "<EMAIL>")
        val testPreviewUser = testUser.copy(userType = UserType.Preview)
        when(impersonationService.getByImpersonatedUserId(testPreviewUser.id)(traceId, systemAcl)).thenReturn(None.successFuture)
        userService.validateUpdate(authorizedUserAcl, updateRequest(testPreviewUser).copy(userType = Some(UserType.Human)), testPreviewUser) assertFailureMessage "403 Forbidden {\"messages\":[\"It is forbidden to change Preview User UserType\"]}"
      }

      "non-admin user cannot change roles of a Preview User" in new Scope {
        val testUser = userInAdminNetwork.copy(email = "<EMAIL>")
        val testPreviewUser = testUser.copy(userType = UserType.Preview)
        when(impersonationService.getByImpersonatedUserId(testPreviewUser.id)(traceId, systemAcl)).thenReturn(None.successFuture)
        userService.validateUpdate(faRequesterAcl, updateRequest(testPreviewUser).copy(roles = Set(UserRole.Wholesaler)), testPreviewUser) assertFailureMessage "401 Unauthorized {\"messages\":[\"Only admin users can change Preview User roles\"]}"
      }

      "admin user can change roles of a Preview User" in new Scope {
        val testUser = userInAdminNetwork.copy(email = "<EMAIL>")
        val testPreviewUser = testUser.copy(userType = UserType.Preview)
        when(impersonationService.getByImpersonatedUserId(testPreviewUser.id)(traceId, systemAcl)).thenReturn(None.successFuture)
        userService.validateUpdate(adminRequesterAcl, updateRequest(testPreviewUser).copy(roles = Set(UserRole.Wholesaler)), testPreviewUser) assertResult Future.successful(())
      }

      "non-admin user cannot make a user a Preview User" in new Scope {
        val testPreviewUser = userInAdminNetwork.copy(userType = UserType.Preview)
        when(impersonationService.getByImpersonatedUserId(testPreviewUser.id)(traceId, systemAcl)).thenReturn(None.successFuture)
        userService.validateUpdate(faRequesterAcl, updateRequest(testPreviewUser), userInAdminNetwork) assertFailureMessage "401 Unauthorized {\"messages\":[\"User does not have right access keys to create Preview User\"]}"
      }

      "Preview User email is not formatted correctly on user type change" in new Scope {
        val testPreviewUser = userInAdminNetwork.copy(userType = UserType.Preview)
        when(impersonationService.getByImpersonatedUserId(testPreviewUser.id)(traceId, systemAcl)).thenReturn(None.successFuture)
        userService.validateUpdate(adminRequesterAcl, updateRequest(testPreviewUser), userInAdminNetwork) assertFailureMessage "400 Bad Request {\"messages\":[\"Preview user email was not formatted correctly, needs to be preview[Number]@icapitalnetwork.com\"]}"
      }

      "admin user can change user type to Preview" in new Scope {
        val testPreviewUser = userInAdminNetwork.copy(userType = UserType.Preview, email = "<EMAIL>")
        when(impersonationService.getByImpersonatedUserId(testPreviewUser.id)(traceId, systemAcl)).thenReturn(None.successFuture)
        userService.validateUpdate(adminRequesterAcl, updateRequest(testPreviewUser), userInAdminNetwork) assertResult Future.successful(())
      }

      "non-superadmin user cannot update non-simon domains" in new Scope {
        val authorizedUserAcl = faRequesterAcl.copy(capabilities = Set(ViewUISwitchUsersNetworkCapability.name))
        val testUser = userInAdminNetwork.copy(email = "<EMAIL>")
        val newNetwork = NetworkId("some-net")
        when(impersonationService.getByImpersonatedUserId(testUser.id)(traceId, systemAcl)).thenReturn(None.successFuture)
        userService.validateUpdate(authorizedUserAcl, updateRequest(testUser).copy(networkId = newNetwork), testUser) assertFailure[HttpError]()
      }

      "requester without switchUsersNetwork capability cannot update user" in new Scope {
        val authorizedUserAcl = faRequesterAcl.copy(capabilities = Set())
        val simonioUser = userInAdminNetwork.copy(email = "<EMAIL>")
        val newNetwork = NetworkId("some-net")
        when(impersonationService.getByImpersonatedUserId(simonioUser.id)(traceId, systemAcl)).thenReturn(None.successFuture)
        userService.validateUpdate(authorizedUserAcl, updateRequest(simonioUser).copy(networkId = newNetwork), simonioUser) assertFailure[HttpError]()
      }
      "network switching is only allowed for exact simonmarkets.com or simon.io matches" in new Scope {
        val user1 = userInAdminNetwork.copy(email = "<EMAIL>")
        val user2 = userInAdminNetwork.copy(email = "<EMAIL>")
        val user3 = userInAdminNetwork.copy(email = "test.user@fakesimon-io")
        val user4 = userInAdminNetwork.copy(email = "<EMAIL>")
        val user5 = userInAdminNetwork.copy(email = "<EMAIL>")
        val user6 = userInAdminNetwork.copy(email = "test.user@fakesimon-io")
        val user7 = userInAdminNetwork.copy(email = "test.user@simonmarkets-com")
        val newNetwork = NetworkId("some-net")

        val authorizedUserAcl = faRequesterAcl.copy(capabilities = Set("canSwitchUsersNetwork"))

        when(impersonationService.getByImpersonatedUserId(any[String])(meq(traceId), meq(systemAcl))).thenReturn(None.successFuture)

        userService.validateUpdate(authorizedUserAcl, updateRequest(user1).copy(networkId = newNetwork), user1) assertFailure[HttpError]()
        userService.validateUpdate(authorizedUserAcl, updateRequest(user2).copy(networkId = newNetwork), user2) assertFailure[HttpError]()
        userService.validateUpdate(authorizedUserAcl, updateRequest(user3).copy(networkId = newNetwork), user3) assertFailure[HttpError]()
        userService.validateUpdate(authorizedUserAcl, updateRequest(user4).copy(networkId = newNetwork), user4) assertFailure[HttpError]()
        userService.validateUpdate(authorizedUserAcl, updateRequest(user5).copy(networkId = newNetwork), user5) assertFailure[HttpError]()
        userService.validateUpdate(authorizedUserAcl, updateRequest(user6).copy(networkId = newNetwork), user6) assertFailure[HttpError]()
        userService.validateUpdate(authorizedUserAcl, updateRequest(user7).copy(networkId = newNetwork), user7) assertFailure[HttpError]()
      }

      "network switch fails when user has a pending impersonation session targeting them" in new Scope {
        val authorizedUserAcl: UserACL = adminRequesterAcl.copy(capabilities = Set(ViewUISwitchUsersNetworkCapability.name))
        val newNetwork: NetworkId.Type = NetworkId("some-net")
        val userImpersonation: UserImpersonation = UserImpersonation(
          id = "any",
          impersonatorUserId = "any",
          impersonatedUserId = userInAdminNetwork.id,
          impersonatedNetworkId = userInAdminNetwork.networkId.toString,
          reason = "",
          ticketNumber = None,
          status = ImpersonationStatus.Pending,
          createdAt = Instant.now,
          completedAt = None,
          traceId = "",
          approverUserId = None,
          approversUserIds = None,
          entitlements = Set.empty
        )
        when(impersonationService.getByImpersonatedUserId(userInAdminNetwork.id)(traceId, systemAcl))
          .thenReturn(userImpersonation.some.successFuture)
        userService.validateUpdate(authorizedUserAcl, updateRequest(userInAdminNetwork).copy(networkId = newNetwork), userInAdminNetwork) assertFailure[HttpError]()
      }

      "network switch succeeds when user has a complete impersonation session targeting them" in new Scope {
        val authorizedUserAcl: UserACL = adminRequesterAcl.copy(capabilities = Set(ViewUISwitchUsersNetworkCapability.name))
        val newNetwork: NetworkId.Type = NetworkId("some-net")
        val userImpersonation: UserImpersonation = UserImpersonation(
          id = "any",
          impersonatorUserId = "any",
          impersonatedUserId = userInAdminNetwork.id,
          impersonatedNetworkId = userInAdminNetwork.networkId.toString,
          reason = "",
          status = ImpersonationStatus.Complete,
          ticketNumber = None,
          createdAt = Instant.now,
          completedAt = None,
          traceId = "",
          approverUserId = None,
          approversUserIds = None,
          entitlements = Set.empty
        )
        when(impersonationService.getByImpersonatedUserId(userInAdminNetwork.id)(traceId, systemAcl))
          .thenReturn(userImpersonation.some.successFuture)
        userService.validateUpdate(authorizedUserAcl, updateRequest(userInAdminNetwork).copy(networkId = newNetwork), userInAdminNetwork)
          .assertResult(Future(()))
      }
    }
    "partialUserUpdate" when {
      "adds locations to the user" in new Scope {
        val testUser: User = TestUser(id = "test-user-id", networkId = NetworkId("some-net"), faNumbers = Set("fa-3"), entitlements = Set("Some Entitlements"))
        val testNetwork: Network = TestNetwork(id = NetworkId unwrap testUser.networkId)

        when(userRepo.getUserByUniqueId(meq(Oms(testUser.id)))(any[Set[String]])(any[TraceId])) thenReturn Future.successful(Some(testUser))
        when(networkService.getNetworkById(meq(adminRequesterAcl), meq(testUser.networkId))
        (any[TraceId])) thenReturn Future.successful(testNetwork)

        val userMatcher: ArgumentMatcher[User] = (u: User) => u.locations == Set("NYC", "BHM") &&
          u.updatedBy == adminRequesterAcl.userId && !u.entitlements.contains("Some Entitlements")

        when(userRepo.updateUser(any[String], argThat(userMatcher))(any(classOf[TraceId]))).thenReturn(Future.successful(testUser))

        userService.partiallyUpdateUser(adminRequesterAcl,
          Seq(LocationsUpdateRequest(testUser.id.some, None, IdType.Oms, locations = Set("NYC", "BHM")))
        ) assertResult Seq()
      }

      "adds fa numbers to the user" in new Scope {
        val testUser: User = TestUser(id = "test-user-id", networkId = NetworkId("some-net"), faNumbers = Set("fa-3"))
        val testNetwork: Network = TestNetwork(id = NetworkId unwrap testUser.networkId)

        when(userRepo.getUserByUniqueId(meq(Oms(testUser.id)))(any[Set[String]])(any[TraceId])) thenReturn Future.successful(Some(testUser))
        when(networkService.getNetworkById(meq(adminRequesterAcl), meq(testUser.networkId))
        (any[TraceId])) thenReturn Future.successful(testNetwork)

        val userMatcher: ArgumentMatcher[User] = new ArgumentMatcher[User] {
          override def matches(u: User): Boolean =
            u.faNumbers == Set("fa-1", "fa-2") &&
              u.updatedBy == adminRequesterAcl.userId
        }

        when(userRepo.updateUser(any[String], argThat(userMatcher))(any(classOf[TraceId]))).thenReturn(Future.successful(testUser))

        userService.partiallyUpdateUser(adminRequesterAcl,
          Seq(FANumbersUpdateRequest(testUser.id.some, None, IdType.Oms, faNumbers = Set("fa-1", "fa-2")))
        ) assertResult Seq()
      }

      "adds licenses to the user" in new Scope {
        val testUser: User = TestUser(id = "test-user-id", networkId = NetworkId("some-net"), faNumbers = Set("fa-3"))
        val testNetwork: Network = TestNetwork(id = NetworkId unwrap testUser.networkId)
        val request: LicensesUpdateRequest = LicensesUpdateRequest(
          userId = testUser.id.some,
          externalId = None,
          userIdType = IdType.Oms,
          npn = Some("1"),
          crd = Some("2")
        )
        val licenseSet = Set(
          License.NPN("1"),
          License.CRD("2")
        )

        when(userRepo.getUserByUniqueId(meq(Oms(testUser.id)))(any[Set[String]])(any[TraceId])) thenReturn Future.successful(Some(testUser))
        when(networkService.getNetworkById(meq(adminRequesterAcl), meq(testUser.networkId))
        (any[TraceId])) thenReturn Future.successful(testNetwork)

        val userMatcher: ArgumentMatcher[User] = (u: User) => u.licenses == licenseSet &&
          u.updatedBy == adminRequesterAcl.userId

        when(userRepo.updateUser(any[String], argThat(userMatcher))(any(classOf[TraceId]))).thenReturn(Future.successful(testUser))

        userService.partiallyUpdateUser(adminRequesterAcl, Seq(request)) assertResult Seq()
      }

      "update email to user" in new Scope {
        val newEmail = "<EMAIL>"

        val testUser: User = TestUser(id = "test-user-id", email = "<EMAIL>", networkId = NetworkId("some-net"))
        val testNetwork: Network = TestNetwork(id = NetworkId unwrap testUser.networkId)
        val request: EmailUpdateRequest = EmailUpdateRequest(
          userId = testUser.id.some,
          externalId = None,
          userIdType = UserId,
          email = newEmail,
          networkId = None
        )

        when(userRepo.getUserByUniqueId(meq(Guid(testUser.id)))(any[Set[String]])(any[TraceId])) thenReturn Future.successful(Some(testUser))
        when(networkService.getNetworkById(meq(adminRequesterAcl), meq(testUser.networkId))
        (any[TraceId])) thenReturn Future.successful(testNetwork)
        when(userRepo.existsByEmailAndNetwork(newEmail, testNetwork.id)).thenReturn(false.successFuture)

        val userMatcher: ArgumentMatcher[User] = (u: User) => u.email == newEmail &&
          u.updatedBy == adminRequesterAcl.userId

        when(userRepo.updateUser(any[String], argThat(userMatcher))(any(classOf[TraceId]))).thenReturn(Future.successful(testUser))

        userService.partiallyUpdateUser(adminRequesterAcl, Seq(request)).await shouldBe Seq()
      }

      "update email should return a conflict on existing email/network" in new Scope {
        val newEmail = "<EMAIL>"

        val testUser: User = TestUser(id = "test-user-id", email = "<EMAIL>", networkId = NetworkId("some-net"))
        val testNetwork: Network = TestNetwork(id = NetworkId unwrap testUser.networkId)
        val request: EmailUpdateRequest = EmailUpdateRequest(
          userId = testUser.id.some,
          externalId = None,
          userIdType = UserId,
          email = newEmail,
          networkId = None
        )

        when(userRepo.getUserByUniqueId(meq(Guid(testUser.id)))(any[Set[String]])(any[TraceId])) thenReturn Future.successful(Some(testUser))
        when(networkService.getNetworkById(meq(adminRequesterAcl), meq(testUser.networkId))
        (any[TraceId])) thenReturn Future.successful(testNetwork)
        when(userRepo.existsByEmailAndNetwork(newEmail, testNetwork.id)).thenReturn(true.successFuture)

        userService.partiallyUpdateUser(adminRequesterAcl, Seq(request))
          .recover { case HttpError(_, status) =>
            status shouldBe StatusCodes.Conflict
          }
          .await
      }

      "update account number for user" in new Scope {
        val newAccount = "myNewAccount"

        val testUser: User = TestUser(id = "test-user-id", accountInContext = Some("myOldAccount"), networkId = NetworkId("some-net"))
        val testNetwork: Network = TestNetwork(id = NetworkId unwrap testUser.networkId)
        val request: AccountUpdateRequest = AccountUpdateRequest(
          userId = testUser.id.some,
          externalId = None,
          userIdType = UserId,
          account = newAccount,
          networkId = None
        )

        when(userRepo.getUserByUniqueId(meq(Guid(testUser.id)))(any[Set[String]])(any[TraceId])) thenReturn Future.successful(Some(testUser))
        when(networkService.getNetworkById(meq(adminRequesterAcl), meq(testUser.networkId))
        (any[TraceId])) thenReturn Future.successful(testNetwork)

        val userMatcher: ArgumentMatcher[User] = (u: User) => u.accountInContext.contains(newAccount) &&
          u.updatedBy == adminRequesterAcl.userId

        when(userRepo.updateUser(any[String], argThat(userMatcher))(any(classOf[TraceId]))).thenReturn(Future.successful(testUser))

        userService.partiallyUpdateUser(adminRequesterAcl, Seq(request)) assertResult Seq()
      }

      "update cusips for user" in new Scope {
        val newCusips = Set("cusip1", "cusip2")

        val testUser: User = TestUser(id = "test-user-id", cusips = Set("oldCusip"), networkId = NetworkId("some-net"))
        val testNetwork: Network = TestNetwork(id = NetworkId unwrap testUser.networkId)
        val request: CusipsUpdateRequest = CusipsUpdateRequest(
          userId = testUser.id.some,
          externalId = None,
          userIdType = UserId,
          cusips = newCusips,
          networkId = None
        )

        when(userRepo.getUserByUniqueId(meq(Guid(testUser.id)))(any[Set[String]])(any[TraceId])) thenReturn Future.successful(Some(testUser))
        when(networkService.getNetworkById(meq(adminRequesterAcl), meq(testUser.networkId))
        (any[TraceId])) thenReturn Future.successful(testNetwork)

        val userMatcher: ArgumentMatcher[User] = (u: User) => u.cusips == newCusips &&
          u.updatedBy == adminRequesterAcl.userId && u.eventInfo.eventType == UserDomainEvent.UserCusipsUpdated

        when(userRepo.updateUser(any[String], argThat(userMatcher))(any(classOf[TraceId]))).thenReturn(Future.successful(testUser))

        userService.partiallyUpdateUser(adminRequesterAcl, Seq(request)) assertResult Seq()
      }

      "responds with error if user not found" in new Scope {
        val nonExistingUserId = Oms("non-existing-id")

        when(userRepo.getUserByUniqueId(meq(nonExistingUserId))(any[Set[String]])(any[TraceId])) thenReturn Future.successful(None)

        val msg =
          HttpError.notFound(s"User $nonExistingUserId not found").payload
        userService.partiallyUpdateUser(adminRequesterAcl,
          Seq(FANumbersUpdateRequest(nonExistingUserId.id.some, None, IdType.Oms, faNumbers = Set("fa-1", "fa-2")))
        ) assertResult Seq(PartialUserUpdateError(msg))
      }

      "responds with error if unsupported user id type provided" in new Scope {
        assertThrows[NoSuchElementException](IdType("un-supported-user-id-type"))
      }

      "responds with error user has no entitlements to update user" in new Scope {
        val testUser: User = TestUser(id = "test-user-id", networkId = NetworkId("some-net"))
        val testNetwork: Network = TestNetwork(id = NetworkId unwrap testUser.networkId)

        when(userRepo.getUserByUniqueId(meq(Oms(testUser.id)))(any[Set[String]])(any[TraceId])) thenReturn Future.successful(Some(testUser))
        when(networkService.getNetworkById(meq(faRequesterAcl), meq(testUser.networkId))
        (any[TraceId])) thenReturn Future.successful(testNetwork)

        val msg = HttpError.notFound(
          s"User does not have right access keys to upload another user (${testUser.id}) in this network"
        ).payload
        userService.partiallyUpdateUser(faRequesterAcl,
          Seq(FANumbersUpdateRequest(testUser.id.some, None, IdType.Oms, faNumbers = Set("fa-1", "fa-2")))
        ) assertResult Seq(PartialUserUpdateError(msg))
      }

      "uses networkId if provided" in new Scope {
        val networkId = NetworkId("some-net")
        val request = FANumbersUpdateRequest("test-user".some, None, IdType.Distributor, Set("1"), networkId = Some(networkId))

        val testUser: User = TestUser(id = "test-user-id", networkId = networkId, faNumbers = Set("fa-3"))
        val testNetwork: Network = TestNetwork(id = NetworkId unwrap testUser.networkId)

        when(userRepo.getUserByUniqueId(meq(Distributor("test-user", networkId)))(any[Set[String]])(any[TraceId])) thenReturn Future.successful(Some(testUser))
        when(networkService.getNetworkById(any[UserACL], any[NetworkId])(any[TraceId])) thenReturn Future.successful(testNetwork)

        Await.result(userService.partiallyUpdateUser(faRequesterAcl, Seq(request)), 5.seconds)
        verify(userRepo, atLeastOnce).getUserByUniqueId(meq(Distributor("test-user", networkId)))(any[Set[String]])(any[TraceId])
      }

      "updates user external ids" in new Scope {
        val testUser: User = TestUser(id = "test-user-id", networkId = NetworkId("some-net"), faNumbers = Set("fa-3"))
        val testNetwork: Network = TestNetwork(id = NetworkId unwrap testUser.networkId)
        val testExternalId: ExternalId = ExternalId("test-subject", "test-id")
        val upsertedIds: Seq[ExternalId] = Seq(testExternalId, ExternalId("test-subject-2", "test-id-2"))
        val request: ExternalIdsUpdateRequest = ExternalIdsUpdateRequest(
          userId = None,
          externalId = testExternalId.some,
          userIdType = IdType.ExternalId,
          networkId = None,
          externalIds = upsertedIds
        )

        when(userRepo.getUserByUniqueId(meq(External(testExternalId)))(any[Set[String]])(any[TraceId])) thenReturn Future.successful(Some(testUser))
        when(networkService.getNetworkById(meq(adminRequesterAcl), meq(testUser.networkId))
        (any[TraceId])) thenReturn Future.successful(testNetwork)

        val userMatcher: ArgumentMatcher[User] = (u: User) => u.externalIds == upsertedIds &&
          u.updatedBy == adminRequesterAcl.userId

        when(userRepo.updateUser(any[String], argThat(userMatcher))(any(classOf[TraceId]))).thenReturn(Future.successful(testUser))

        userService.partiallyUpdateUser(adminRequesterAcl, Seq(request)) assertResult Seq()
      }
    }

    "listApplicationSecrets" when {
      "responds with error if user does not have ViewClientCredentialsViaNetwork capabilities" in new Scope {
        val testNetwork: Network = TestNetwork()
        val requesterAcl: UserACL = TestUserACL("nonAdminUser", testNetwork.id, capabilities = Set.empty[String])

        when(userRepo.getUsersByIds(any[Set[String]])(any[Set[String]])(any(classOf[TraceId]))) thenReturn List.empty[User].successFuture

        userService.listApplicationSecrets(requesterAcl, "OktaClientId") assertFailureEx HttpError.notFound("OktaClientId does not exist or user does not have access")
      }

      "responds with error if client credential does not exist" in new Scope {
        val testNetwork: Network = TestNetwork()
        val requesterAcl: UserACL = TestUserACL("nonAdminUser", testNetwork.id, capabilities = Set(ViewClientCredentialsViaNetwork))

        when(userRepo.getUsersByIds(any[Set[String]])(any[Set[String]])(any(classOf[TraceId]))) thenReturn List.empty[User].successFuture

        userService.listApplicationSecrets(requesterAcl, "OktaClientId") assertFailureEx HttpError.notFound("OktaClientId does not exist or user does not have access")
      }

      "returns secrets when user has ViewClientCredentialsViaNetwork and client credential exists" in new Scope {
        val testNetwork: Network = TestNetwork()
        val requesterAcl: UserACL = TestUserACL("nonAdminUser", testNetwork.id, capabilities = Set(ViewClientCredentialsViaNetwork))
        val systemUser: User = TestUser(
          id = "OktaClientId",
          email = email,
          createdBy = requesterAcl.userId,
          updatedBy = requesterAcl.userId,
          networkId = testNetwork.id,
          idpLoginId = User.buildIdpLoginId(testNetwork.networkCode, email, multiNetworkEnabled = true),
          loginMode = LoginMode.ClientCredentials,
          userType = UserType.System,
          firstName = "UnitTestNetwork",
          lastName = "API User",
          createdAt = now,
          updatedAt = now
        )

        val secrets: GetOAuthSecretResponse = GetOAuthSecretResponse("AppSecret", now.toInstant(ZoneOffset.UTC), now.toInstant(ZoneOffset.UTC), OAuthSecretStatus.ACTIVE)

        when(userRepo.getUsersByIds(any[Set[String]])(any[Set[String]])(any(classOf[TraceId]))) thenReturn List(systemUser).successFuture
        when(oktaService.listApplicationSecrets(any[String])(any(classOf[TraceId]), any(classOf[ExecutionContext]))) thenReturn List(secrets).successFuture

        userService.listApplicationSecrets(requesterAcl, "OktaClientId") assertResult List(secrets)
      }
    }

    "createApplicationSecret" when {
      "responds with error if user does not have EditClientCredentialsViaNetwork capabilities" in new Scope {
        val testNetwork: Network = TestNetwork()
        val requesterAcl: UserACL = TestUserACL("nonAdminUser", testNetwork.id, capabilities = Set.empty[String])

        when(userRepo.getUsersByIds(any[Set[String]])(any[Set[String]])(any(classOf[TraceId]))) thenReturn List.empty[User].successFuture

        userService.createApplicationSecret(requesterAcl, "OktaClientId") assertFailureEx HttpError.notFound("OktaClientId does not exist or user does not have access")
      }

      "responds with error if client credential does not exist" in new Scope {
        val testNetwork: Network = TestNetwork()
        val requesterAcl: UserACL = TestUserACL("nonAdminUser", testNetwork.id, capabilities = Set(EditClientCredentialsViaNetwork))

        when(userRepo.getUsersByIds(any[Set[String]])(any[Set[String]])(any(classOf[TraceId]))) thenReturn List.empty[User].successFuture

        userService.createApplicationSecret(requesterAcl, "OktaClientId") assertFailureEx HttpError.notFound("OktaClientId does not exist or user does not have access")
      }

      "creates secret when user has capabilities and client credential exists" in new Scope {
        val testNetwork: Network = TestNetwork()
        val requesterAcl: UserACL = TestUserACL("nonAdminUser", testNetwork.id, capabilities = Set.empty[String])
        val systemUser: User = TestUser(
          id = "OktaClientId",
          email = email,
          createdBy = requesterAcl.userId,
          updatedBy = requesterAcl.userId,
          networkId = testNetwork.id,
          idpLoginId = User.buildIdpLoginId(testNetwork.networkCode, email, multiNetworkEnabled = true),
          loginMode = LoginMode.ClientCredentials,
          userType = UserType.System,
          firstName = "UnitTestNetwork",
          lastName = "API User",
          createdAt = now,
          updatedAt = now
        )
        val newSecret: CreateOAuthSecretResponse = CreateOAuthSecretResponse(
          "secretId",
          now.toInstant(ZoneOffset.UTC),
          now.toInstant(ZoneOffset.UTC),
          OAuthSecretStatus.ACTIVE,
          "ClientSecret"
        )

        when(userRepo.getUsersByIds(any[Set[String]])(any[Set[String]])(any(classOf[TraceId]))) thenReturn List(systemUser).successFuture
        when(oktaService.createApplicationSecret(any[String])(any(classOf[TraceId]), any(classOf[ExecutionContext]))) thenReturn Future.successful(newSecret)

        userService.createApplicationSecret(requesterAcl, "OktaClientId") assertResult newSecret
      }
    }
  }

  "legacyExternalIds" should {
    "extract distributor and oms ids from externalIds map" in new Scope {
      val distId = "dist_id_1"
      val omsId = "oms_id_1"
      val testMap = Map(DistributorKey -> distId, OmsKey -> omsId)
      val networkId = NetworkId("test_network")
      userService.legacyExternalIds(testMap, networkId).await shouldBe Tuple3(
        distId.some,
        omsId.some,
        Seq.empty
      )
    }

    "extract distributor and oms ids from externalIds map and pass through acceptable ids" in new Scope {
      val distId = "dist_id_1"
      val omsId = "oms_id_1"
      val testMap = Map("distributor" -> distId, "oms" -> omsId, "other" -> "other_id_1")
      val networkId = NetworkId("test_network")
      val externalIdType = ExternalIdType("other", Set(networkId), "idp_other")
      when(externalIdTypeService.getByNetwork(meq(networkId))(any[TraceId])).thenReturn(Future.successful(Seq(externalIdType)))
      userService.legacyExternalIds(testMap, networkId).await shouldBe Tuple3(
        distId.some,
        omsId.some,
        Seq(ExternalId("other", "other_id_1"))
      )
    }

    "reject payload with ids that are not configured on user's network" in new Scope {
      val testMap = Map("bad_id_type" -> "other_id_1")
      val networkId = NetworkId("test_network")
      when(externalIdTypeService.getByNetwork(meq(networkId))(any[TraceId])).thenReturn(Future.successful(Seq.empty))
      userService.legacyExternalIds(testMap, networkId) assertFailure[HttpError]()
    }
  }

  "handleOktaSyncRequest" should {
    "sync all when syncAll=true" in new Scope {
      private val syncRequest = OktaSyncRequest(
        userIds = None,
        networkIds = None,
        syncAll = Some(true)
      )
      private val ids = Set("id_1")
      private val singleUsers = ids.toList.map(SingleUser)
      private val putResult = mock[PutEventsResult]
      when(userRepo.getUserIds(meq(Set.empty))(any[Set[String]])(any[TraceId])).thenReturn(ids.successFuture)
      when(oktaSyncBus.putEvents(meq(singleUsers), any[String])(meq(traceId), meq(singleUserEncoder))).thenReturn(Seq(putResult).successFuture)
      when(putResult.getFailedEntryCount).thenReturn(0)
      userService.handleOktaSyncRequest(adminRequesterAcl, syncRequest) assertResult OktaSyncResponse(0)
    }

    "sync users in provided networks" in new Scope {
      private val networkId = NetworkId("networkId")
      private val syncRequest = OktaSyncRequest(
        userIds = None,
        networkIds = Some(Set(networkId)),
        syncAll = None
      )
      private val ids = Set("id_1")
      private val singleUsers = ids.toList.map(SingleUser)
      private val putResult = mock[PutEventsResult]
      private val filters = OrFilter(NetworkFilter(networkId.toString))
      when(userRepo.getUserIds(meq(Set(filters)))(any[Set[String]])(any[TraceId])).thenReturn(ids.successFuture)
      when(oktaSyncBus.putEvents(meq(singleUsers), any[String])(meq(traceId), meq(singleUserEncoder))).thenReturn(Seq(putResult).successFuture)
      when(putResult.getFailedEntryCount).thenReturn(0)
      userService.handleOktaSyncRequest(adminRequesterAcl, syncRequest) assertResult OktaSyncResponse(0)
    }

    "sync users provided" in new Scope {
      private val ids = Set("id_1")
      private val syncRequest = OktaSyncRequest(
        userIds = Some(ids),
        networkIds = None,
        syncAll = None
      )
      private val singleUsers = ids.toList.map(SingleUser)
      private val putResult = mock[PutEventsResult]
      when(oktaSyncBus.putEvents(meq(singleUsers), any[String])(meq(traceId), meq(singleUserEncoder))).thenReturn(Seq(putResult).successFuture)
      when(putResult.getFailedEntryCount).thenReturn(0)
      userService.handleOktaSyncRequest(adminRequesterAcl, syncRequest) assertResult OktaSyncResponse(0)
    }
  }

  "handleBumpUsersRequest" should {
    "bump versions of all users provided in request" in new Scope {
      private val ids = Set("id1", "id2")
      private val request = BumpUsersVersionRequest(
        userIds = Some(ids),
        networkIds = None
      )
      when(userRepo.bumpVersions(meq(ids))(any[Set[String]])(any[TraceId])).thenReturn(2.successFuture)
      userService.handleBumpUsersRequest(adminRequesterAcl, request) assertResult BumpUsersVersionResponse(2)
    }
  }

  "resetMfa" should {
    val request = ResetMfaRequest(
      factorTypes = Set(FactorType.sms),
      removeRecoveryEnrollment = None,
      deleteAll = Some(false)
    )
    "remove specified factors from user and okta" in new Scope {
      val smsFactor = "sms" -> Factor(Some("someSms"), isFactorEnrolled = true)
      val emailFactor = "email" -> Factor(Some("someEmail"))
      val userWithMfas = userInAdminNetwork.copy(
        idpId = Some("id"),
        mfas = Map(
          smsFactor,
          emailFactor
        )
      )
      val userWithResetMfas = userWithMfas.copy(mfas = Map(emailFactor))
      when(userRepo.getById(userWithMfas.id)(Set("admin"))).thenReturn(Future.successful(Some(userWithMfas)))
      when(oktaService.deleteFactors(request.factorTypes.map(_.toString), userWithMfas.idpId.get, request.deleteAll.get))
        .thenReturn(Future.unit)
      when(userRepo.updateUser(adminRequesterAcl.userId, userWithResetMfas)).thenReturn(Future.successful(userWithResetMfas))

      val result = userService.resetMfa(adminRequesterAcl, userWithMfas.id, request).await
      result shouldBe UserView(userWithResetMfas)
    }
  }
}

object UserServiceSpec {

  def insertRequest(user: User): UpsertUserRequest = request.UpsertUserRequest(
    user.networkId,
    user.email,
    user.firstName,
    user.lastName,
    Map(
      "oms" -> user.omsId.getOrElse(""),
      "distributor" -> user.distributorId.getOrElse("")
    ).filter { case (_, value) => value != "" },
    user.tradewebEligible.some,
    user.regSEligible.some,
    user.roles,
    user.faNumbers.some,
    user.custodianFaNumbers.some,
    user.locations.some,
    user.customRoles.some,
    user.licenses.some,
    cusips = user.cusips.some,
    id = None,
    loginMode = Some(user.loginMode),
    userType = Some(user.userType),
    idpId = user.idpId,
    upsertField = None,
    landingPage = user.landingPage,
  )

  def updateRequest(user: User): UpsertUserRequest = {
    insertRequest(user).copy(id = user.id.some)
  }

  def upsertRequest(user: User): UpsertUserRequest = {
    insertRequest(user).copy(
      upsertField = Some(UpsertField.Distributor))
  }

}


private trait Scope extends MockitoSugar {

  implicit val traceId: TraceId = TraceId("1234")
  val userId = "test123"
  val adminNetwork: Network = TestNetwork(id = AdminNetworkId.toString)
  val multiNetworkExcludedNetwork: Network = TestNetwork(id = "Excluded")
  val email = "<EMAIL>"
  val distributorId = "distributorId"
  val adminRequesterAcl: UserACL = TestUserACL(userId, adminNetwork.id, roles = Set(UserRole.EqPIPGGSAdmin), capabilities = Set("admin"))
  val systemAcl: UserACL = adminRequesterAcl
  val faRequesterAcl: UserACL = TestUserACL("test-user", adminNetwork.id, capabilities = Set(ViewUserViaNetwork))
  val now: LocalDateTime = LocalDateTime.now

  val userInAdminNetwork: User = TestUser(
    userId,
    email = email,
    createdBy = adminRequesterAcl.userId,
    updatedBy = adminRequesterAcl.userId,
    networkId = adminNetwork.id,
    idpLoginId = User.buildIdpLoginId(adminNetwork.networkCode, email, true),
    createdAt = now,
    updatedAt = now
  )

  val upsertUser: User = TestUser(
    userId,
    email = email,
    createdBy = adminRequesterAcl.userId,
    updatedBy = adminRequesterAcl.userId,
    networkId = adminNetwork.id,
    idpLoginId = User.buildIdpLoginId(adminNetwork.networkCode, email, true),
    distributorId = Some(distributorId),
    createdAt = now,
    updatedAt = now
  )

  val userWithAdminRole: User = TestUser(
    userId,
    email = email,
    createdBy = adminRequesterAcl.userId,
    updatedBy = adminRequesterAcl.userId,
    networkId = adminNetwork.id,
    idpLoginId = User.buildIdpLoginId(adminNetwork.networkCode, email, true),
    roles = Set(UserRole.EqPIPGGSAdmin),
    createdAt = now,
    updatedAt = now
  )

  val systemUserAdminNetwork: User = TestUser(
    id = "generatedUserId",
    email = email,
    createdBy = adminRequesterAcl.userId,
    updatedBy = adminRequesterAcl.userId,
    networkId = adminNetwork.id,
    idpLoginId = User.buildIdpLoginId(adminNetwork.networkCode, email, true),
    loginMode = LoginMode.ClientCredentials,
    userType = UserType.System,
    firstName = "System",
    lastName = "User",
    createdAt = now,
    updatedAt = now
  )

  val faManagerUser: User = TestUser(
    "faManagerTestId",
    createdBy = adminRequesterAcl.userId,
    updatedBy = adminRequesterAcl.userId,
    roles = Set(UserRole.EqPIPGFAManager),
    createdAt = now,
    updatedAt = now
  )

  val oktaUser: OktaUser = OktaUserConversions.toOktaUser(userInAdminNetwork, adminNetwork.customRolesConfig)

  val networkWithAlias: Network = adminNetwork.copy(omsAlias = Some("twd"), distributorAlias = Some(ExternalAlias("rayj", """^(\d+){1,16}$""")))

  val expectedEntitlements = (u: User) => Set(Admin,
    s"$ViewUserViaPurview:${u.networkId}",
    s"$ViewUserViaNetwork:${u.networkId}",
    s"$ImpersonateUserViaPurview:${u.networkId}",
    s"$ImpersonateUserViaNetwork:${u.networkId}",
    s"$UploadUserViaPurview:${u.networkId}",
    s"$UploadUserViaNetwork:${u.networkId}",
  ) ++ LegacyUser.Entitlements(u.asLegacyUser).filter(!_.contains("guid"))

  val maskedIds = Set(MaskedId("a", "n"))

  implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  val oktaSdkUser: OktaSDKUser = mock[OktaSDKUser]
  val userRepo: UserRepository = mock[UserRepository]
  val aclClient: HttpACLClient = mock[HttpACLClient]
  val oktaService: OktaService = mock[OktaService]
  val networkService: BasicNetworkService = mock[BasicNetworkService]
  val oktaSyncBus: EventBridgeClient = mock[EventBridgeClient]
  val externalIdTypeService: ExternalIdTypeService = mock[ExternalIdTypeService]
  val impersonationService: UserImpersonationService = mock[UserImpersonationService]

  val config: UserServiceConfig = UserServiceConfig(
    Set("testExternalTarget"),
    PagingConfig(10000),
    multiNetworksExcluded = Set(multiNetworkExcludedNetwork.id),
    internalDomains = Set("simonmarkets.com", "simon.io", "icapitalnetwork.com", "@excludedemail.com")
  )

  val mappings = mock[UserSyncMappingClient]

  val mappingRequest = (acl: UserACL) => UserNetworkMappingRequest(
    sourceSystem = SourceDestinationSystem.SIMON,
    destinationSystem = SourceDestinationSystem.ICN,
    sourcePrimaryId = acl.networkId.toString,
    sourcePrimaryIdKind = SourceDestinationPrimaryIdKind.`SIMON_NETWORK`,
    sourceSecondaryId = acl.locations.headOption,
    sourceSecondaryIdKind = Some(SourceDestinationSecondaryIdKind.`SIMON_LOCATION`)
  )

  def mapping = (acl: UserACL, destinationPrimary: String, destinationSecondary: String) => UserNetworkMappingResponse(
    destination_system = SourceDestinationSystem.ICN,
    destination_primary = SourceDestinationPrimary(destinationPrimary, `ICN_WHITE_LABEL`),
    destination_secondary = Some(SourceDestinationSecondary(destinationSecondary, `ICN_FIRM`)),
    destination_property_matcher = SourceDestinationMatchByProperty(MatchByPropertyName.email),
    source_property_matcher = None,
    source_system = SourceDestinationSystem(SIMON),
    source_primary = SourceDestinationPrimary(acl.networkId.toString, `SIMON_NETWORK`),
    source_secondary = acl.locations.headOption.map(l => SourceDestinationSecondary(l, `SIMON_LOCATION`)),
    entitlements = Set.empty,
    fields = Set.empty
  )

  val userService = new UserServiceImpl(
    systemAcl = systemAcl,
    userRepository = userRepo,
    networkService = networkService,
    oktaService = oktaService,
    config = config,
    oktaSyncBus = oktaSyncBus,
    externalIdTypeRepo = externalIdTypeService,
    impersonationService = impersonationService,
    mappings = mappings,
    systemUserNetworkLimitationIgnoreList = Set(NetworkId("SIMON Admin"), NetworkId("Test Network 1"), NetworkId("Test Network 2"), NetworkId("Test Network 3"))
  )


  /**
   * removes the fields for matching which are created dynamically inside the user service
   * the following fields are not checked: userId, maskedIds
   * entitlements are partially checked
   * time fields are checked with reduced precision
   *
   * @return
   */
  def matchWithDynamicFieldsExcluded(user: User): User = {
    //There is a bug that this gets called with null argument before the test actually run
    argThat((argument: User) => {
      if (argument == null) {
        true
      } else {
        if (user.entitlements.forall(argument.entitlements.contains)) {
          val user1 = user.copy(
            id = "",
            createdAt = user.createdAt.truncatedTo(ChronoUnit.MINUTES),
            updatedAt = user.updatedAt.truncatedTo(ChronoUnit.MINUTES),
            userSyncedAt = user.userSyncedAt.map(_.truncatedTo(ChronoUnit.MINUTES)),
            maskedIds = Set.empty,
            entitlements = Set.empty
          )
          val user2 = argument.copy(
            id = "",
            createdAt = argument.createdAt.truncatedTo(ChronoUnit.MINUTES),
            updatedAt = argument.updatedAt.truncatedTo(ChronoUnit.MINUTES),
            userSyncedAt = argument.userSyncedAt.map(_.truncatedTo(ChronoUnit.MINUTES)),
            maskedIds = Set.empty,
            entitlements = Set.empty
          )
          user1 == user2
        } else {
          println(s"[Test] Missing expected entitlements: ${user.entitlements.diff(argument.entitlements)}")
          false
        }
      }
    })
  }
}

private trait InsertUserFixture extends Scope {

  def newUsers: Seq[User]

  def conflictUsers: Seq[User] = Seq.empty

  def fatalErrorUsers: Seq[User] = Seq.empty

  newUsers.foreach { newUser =>

    val userWithFields = newUser.copy(
      idpId = None,
      createdBy = adminRequesterAcl.userId,
      updatedBy = adminRequesterAcl.userId,
      entitlements = expectedEntitlements(newUser),
      isActive = true,
      idpLoginId = User.buildIdpLoginId(adminNetwork.networkCode, newUser.email, true)
    )

    when(networkService.getNetworkById(adminRequesterAcl, newUser.networkId)) thenReturn adminNetwork.copy(id = newUser.networkId).successFuture
    when(userRepo.existsByEmailAndNetwork(newUser.email, newUser.networkId)) thenReturn false.successFuture

    //conditionally fail the underlying creation call
    val event = EventInfo(UserDomainEvent.UserCreated, triggeredBy = adminRequesterAcl.userId, traceId)
    if (conflictUsers.contains(newUser))
      when(userRepo.insertUser(any[String], matchWithDynamicFieldsExcluded(userWithFields.copy(eventInfo = event)))(any(classOf[TraceId]))) thenReturn Conflict("conflict", None).failedFuture
    else if (fatalErrorUsers.contains(newUser))
      when(userRepo.insertUser(any[String], matchWithDynamicFieldsExcluded(userWithFields.copy(eventInfo = event)))(any(classOf[TraceId]))) thenReturn HttpError.badRequest("bad request").failedFuture
    else
      when(userRepo.insertUser(any[String], matchWithDynamicFieldsExcluded(userWithFields.copy(eventInfo = event)))(any(classOf[TraceId]))) thenReturn newUser.successFuture
  }

  def assertEquals(actual: Future[BatchUserUpsertResponse],
      expected: BatchUserUpsertResponse): Assertion = {
    actual.await.success should contain theSameElementsAs expected.success
    actual.await.failure should contain theSameElementsAs expected.failure
  }

}

private trait OktaSyncFixture extends Scope {

}