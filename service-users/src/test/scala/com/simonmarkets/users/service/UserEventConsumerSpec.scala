package com.simonmarkets.users.service

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.kafka.ZConsumer
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.common.clients.usersync.{MatchByPropertyName, SourceDestinationMatchByProperty, SourceDestinationSystem, SourceDestinationSystemName, SyncField, UserNetworkMappingRequest, UserNetworkMappingResponse, UserSyncMappingClient}
import com.simonmarkets.networks.common.service.networkservice.BasicNetworkService
import com.simonmarkets.users.TestNetwork
import com.simonmarkets.users.common.UniqueUserId.Email
import com.simonmarkets.users.common.api.request.{UpsertUserRequest, GetUniqueUserIdRequest}
import com.simonmarkets.users.common.{LoginMode, UpsertField, User, UserDomainEvent}
import com.simonmarkets.users.domain.{IcnExternalId, IcnUserEvent, SyncError, TransientState}
import com.simonmarkets.users.repository.TransientStateRepository
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.{any, eq => meq}
import org.mockito.Mockito.when
import org.scalatest.Matchers
import org.scalatest.mockito.MockitoSugar.mock
import zio.test.Assertion._
import zio.test._
import zio.test.junit.{JUnitRunnableSpec, ZTestJUnitRunner}
import zio.{ZIO, ZLayer}

import scala.concurrent.{ExecutionContext, Future}

@RunWith(classOf[ZTestJUnitRunner])
class UserEventConsumerSpec extends JUnitRunnableSpec with Matchers {

  def spec = suite("UserEventConsumerSpec")(

    suite("headers")(
      test("happy path") {
        for {
          mocks <- ZIO.service[MockTemplate]
          _ <- mocks.sync(mocks.event)
        } yield assertCompletes
      },
      test("missing required headers") {
        assertZIO(
          ZIO
            .serviceWithZIO[MockTemplate] { mocks =>
              import mocks._
              service.sync(
                ZConsumer.Record(
                  key = mocks.event.user_id.toString,
                  value = mocks.event,
                  headers = Seq.empty,
                  offset = 0L
                )
              )
            }
            .exit
        )(fails(isSubtype[SyncError.NotFound](anything)))
      }
    ).provide(ZLayer.succeed(MockDefault)),

    suite("external id sync")(
      test("happy path") {
        for {
          mocks <- ZIO.service[MockTemplate]
          _ <- mocks.sync(mocks.event)
        } yield assertCompletes
      }.provide(ZLayer.succeed(ExternalIdMock())),
      test("missing secondary in mapping destination property matcher") {
        assertZIO(
          ZIO.serviceWithZIO[MockTemplate](mocks => mocks.sync(mocks.event)).exit
        )(
          fails(equalTo(SyncError.NotFound("expected subject in secondary property matcher to match by external id")))
        )
      }.provideLayer(ZLayer.succeed(ExternalIdMock(matcherSecondary = None))),
      test("no matching external id subject") {
        assertZIO(
          ZIO.serviceWithZIO[MockTemplate](mocks => mocks.sync(mocks.event)).exit
        )(
          fails(equalTo(SyncError.NotFound("expected external id with subject=notFoundSubject in user, but got None")))
        )
      }.provideLayer(ZLayer.succeed(ExternalIdMock(matcherSecondary = Some("notFoundSubject"))))
    ),

    suite("password sync")(
      test("store when password is sync field and is defined") {

        for {
          mocks <- ZIO.service[MockTemplate]
          _ <- mocks.sync(mocks.event)
        } yield assertCompletes

      }.provideLayer(ZLayer.succeed(PasswordMock)),

      test("not store when loginMode is in disallowed list") {
        for {
          mocks <- ZIO.service[MockTemplate]
          _ <- mocks.sync(mocks.event)
        } yield assertCompletes
      }.provideLayer(ZLayer.succeed(
        new MockTemplate {
          override def user: User = syncedUser2.copy(loginMode = LoginMode.UnifiedPassword)
        }
      )),

      test("not store when password undefined") {
        for {
          mocks <- ZIO.service[MockTemplate]
          _ <- mocks.sync(mocks.event)
        } yield assertCompletes
      }.provideLayer(ZLayer.succeed(
        new MockTemplate {
          override def event: IcnUserEvent = icnUserEvent.copy(encrypted_password = None)
        }
      )),

      test("not store when password not a sync field") {
        for {
          mocks <- ZIO.service[MockTemplate]
          _ <- mocks.sync(mocks.event)
        } yield assertCompletes
      }.provideLayer(ZLayer.succeed(MockDefault)),

      test("not store when password empty string") {
        for {
          mocks <- ZIO.service[MockTemplate]
          _ <- mocks.sync(mocks.event)
        } yield assertCompletes
      }.provideLayer(ZLayer.succeed(
        new MockTemplate {
          override def event: IcnUserEvent = icnUserEvent.copy(encrypted_password = Some(""))
        }
      ))
    )
  )

}

/**
 * fixture pattern for zio tests. Provide as layer some standard object defined below, or define anon trait in-line
 */
trait MockTemplate extends UserEventConsumerSpecData {

  // fixture toggles //
  def mapping: UserNetworkMappingResponse = defaultMapping

  def user: User = syncedUser2

  def event: IcnUserEvent = icnUserEvent
  // fixture toggles //

  val client: UserSyncMappingClient = mock[UserSyncMappingClient]
  val networkService: BasicNetworkService = mock[BasicNetworkService]
  val userService: UserService = mock[UserService]
  val transientStateRepository: TransientStateRepository = mock[TransientStateRepository]
  val systemUser: UserACL = mock[UserACL]

  val service: UserEventConsumer = UserEventConsumer(
    mappings = client,
    networks = networkService,
    users = userService,
    transientState = transientStateRepository,
    systemUser = systemUser
  )

  //  default mocks
  when(client.get(any[UserNetworkMappingRequest])(any[TraceId])).thenReturn(Future.successful(Some(mapping)))
  when(networkService.unentitledGetNetworkById(meq(testNetwork.id))(any[TraceId]))
    .thenReturn(Future.successful(TestNetwork()))
  when(userService.getUserByUniqueUserId(meq(systemUser), any[GetUniqueUserIdRequest], any[UpsertField])(any[TraceId]))
    .thenReturn(Future.successful((None, Email(event.email, testNetwork.id))))
  when(
    userService.handleInsertRequest(
      meq(systemUser),
      any[UpsertUserRequest],
      meq(Some(UserDomainEvent.UserSynced))
    )(
      any[TraceId]
    )
  ).thenReturn(Future.successful((user, None)))

  /**
   * Calls [[UserEventConsumer.sync]] with the required headers.
   */
  final def sync(event: IcnUserEvent) =
    service.sync(
      ZConsumer.Record(
        key = event.user_id.toString,
        value = event,
        headers = Seq(
          UserEventConsumer.ICN_SOURCE_HEADER,
          UserEventConsumer.VERSION_HEADER,
          UserEventConsumer.UPDATED_TYPE_HEADER
        ),
        offset = 0L
      )
    )

}

object MockDefault extends MockTemplate

object PasswordMock extends MockTemplate {
  override def mapping: UserNetworkMappingResponse =
    defaultMapping.copy(fields = defaultSyncFields + SyncField.password)

  when(transientStateRepository.upsert(any[TransientState])(any[ExecutionContext]))
    .thenReturn(Future.unit)
}

final case class ExternalIdMock(
    userExternalId: ExternalId = ExternalId("externalSubject", "1031"),
    eventExternalId: IcnExternalId = IcnExternalId(external_type = "externalSubject", external_id = "1031"),
    matcherSecondary: Option[String] = Some("externalSubject")
) extends MockTemplate {

  override def user: User = syncedUser2.copy(externalIds = Seq(userExternalId))

  // matches from ICN -> SIMON on an external id with subject "externalSubject"
  override def mapping: UserNetworkMappingResponse = UserNetworkMappingResponse(
    destination_system = SourceDestinationSystem(SourceDestinationSystemName.SIMON),
    destination_primary = unitTestNetwork,
    destination_secondary = Some(coreCapitalLocation),
    destination_property_matcher = SourceDestinationMatchByProperty(
      primary = MatchByPropertyName.external_id,
      secondary = matcherSecondary
    ),
    source_system = SourceDestinationSystem(SourceDestinationSystemName.ICN),
    source_primary = flagship,
    source_secondary = Some(firm9001),
    entitlements = Set.empty,
    fields = defaultSyncFields
  )

  override def event: IcnUserEvent = icnUserEvent.copy(
    user_external_ids = Some(Seq(eventExternalId))
  )
}
