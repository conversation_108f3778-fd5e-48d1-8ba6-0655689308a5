package com.simonmarkets.users.service

import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.goldmansachs.marquee.pipg.{NetworkLocation, User => LegacyUser}
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.common.clients.usersync.SourceDestinationPrimaryIdKind.{`ICN_WHITE_LABEL`, `SIMON_NETWORK`}
import com.simonmarkets.networks.common.clients.usersync.SourceDestinationSecondaryIdKind.{`ICN_FIRM`, `SIMON_LOCATION`}
import com.simonmarkets.networks.common.clients.usersync.SourceDestinationSystemName.{ICN, SIMON}
import com.simonmarkets.networks.common.clients.usersync.SyncField.{external_ids, first_name, icn_groups, last_name}
import com.simonmarkets.networks.common.clients.usersync._
import com.simonmarkets.users.TestNetwork
import com.simonmarkets.users.common.api.request.UpsertUserRequest
import com.simonmarkets.users.common.{EventInfo, LoginMode, User, UserDomainEvent, UserType}
import com.simonmarkets.users.domain.{IcnExternalId, IcnUserEvent}
import io.circe.jawn
import simon.Id.NetworkId

import java.time.temporal.ChronoUnit
import java.time.{LocalDateTime, ZoneId}

import scala.io.Source

trait UserEventConsumerSpecData extends UserAcceptedAccessKeysGenerator {

  val testSystemUser = TestUserACL("123", NetworkId("SIMON Admin"))

  val unitTestNetwork = SourceDestinationPrimary(
    "UnitTestNetwork",
    `SIMON_NETWORK`
  )

  val network1 = unitTestNetwork.copy(
    "network01"
  )
  val flagshipNetwork = unitTestNetwork.copy(
    "FlagshipNetwork"
  )
  val sanctuaryNetwork = unitTestNetwork.copy(
    "Sanctuary"
  )

  val riaNetwork = unitTestNetwork.copy(
    "RIANetwork"
  )

  val coreCapitalLocation = SourceDestinationSecondary(
    "Core Capital",
    `SIMON_LOCATION`
  )
  val ameripriseLocation = coreCapitalLocation.copy("Ameriprise")
  val cibcLocation = coreCapitalLocation.copy("CIBC")

  val architectGroup = 1111
  val architectCustomRole = "ArchitectCustomRole"
  val unifiedEducationCustomRole = "UnifiedEducationCustomRole"
  val architectEntitlement = EntitlementMap(
    id = 1,
    source_entitlement = architectGroup.toString,
    destination_entitlement = "ArchitectCustomRole"
  )

  val unifiedEducationGroup = 2222
  val unifiedEduEntitlement = EntitlementMap(
    id = 2,
    source_entitlement = unifiedEducationGroup.toString,
    destination_entitlement = "UnifiedEducationCustomRole"
  )

  val eventFromSource = Source.fromResource("icn_event.json").mkString

  val eventEither = jawn.decode[IcnUserEvent](eventFromSource)(IcnUserEvent.decoder)

  val icnUserEvent = eventEither.right.get

  val flagship = SourceDestinationPrimary(
    "0",
    `ICN_WHITE_LABEL`
  )

  val wlp188 = flagship.copy("188")
  val wlp100 = flagship.copy("100")
  val wlp12111 = flagship.copy("12111")

  val firm9001 = SourceDestinationSecondary(
    "9001",
    `ICN_FIRM`
  )

  val firm41201 = firm9001.copy("41201")
  val firm41000 = firm9001.copy("41000")
  val firm12203 = firm9001.copy("12203")
  val sanctuaryFirm = firm9001.copy("2222")
  val ameripriseFirm = firm9001.copy("3333")
  val cibcFirm = firm9001.copy("4444")
  val externalIdFirm = firm9001.copy("5555")

  val emailPropertyMatcher = SourceDestinationMatchByProperty(
    MatchByPropertyName.email
  )

  val testExternalIds = Seq(ExternalId("test_external", "874711"))
  val mappedIcnExternal = IcnExternalId("874711", "test_external")
  val icnExternalIds = Seq(mappedIcnExternal, IcnExternalId("someOtherId", "someOtherExternal"))

  val externalIdPropertyMatcher = SourceDestinationMatchByProperty(
    MatchByPropertyName.external_id,
    Some("test_external")
  )
  val distributorIdPropertyMatcher = SourceDestinationMatchByProperty(
    MatchByPropertyName.distributor_id
  )

  val mappedCustomRoles = Set("ArchitectCustomRole", "UnifiedEducationCustomRole")
  val entitlementMap = Set(architectEntitlement, unifiedEduEntitlement)

  val defaultSyncFields: Set[SyncField] = Set(
    first_name,
    last_name,
  )

  val defaultMapping: UserNetworkMappingResponse = UserNetworkMappingResponse(
    destination_system = SourceDestinationSystem(
      SIMON
    ),
    destination_primary = unitTestNetwork,
    destination_secondary = Some(coreCapitalLocation),
    destination_property_matcher = emailPropertyMatcher,
    source_system = SourceDestinationSystem(
      ICN
    ),
    source_primary = flagship,
    source_secondary = Some(firm9001),
    entitlements = Set.empty,
    fields = defaultSyncFields
  )

  val mappingWithDefaultSyncFieldsAndGroups = defaultMapping
    .copy(fields = defaultSyncFields ++ Set(icn_groups))


  val mapping2 = defaultMapping.copy(
    source_secondary = Some(externalIdFirm),
    destination_primary = network1,
    destination_secondary = None,
    destination_property_matcher = externalIdPropertyMatcher
  )

  val mappingWithDefaultSyncFieldsAndExternalIds = defaultMapping.copy(
    source_secondary = Some(externalIdFirm),
    fields = defaultSyncFields + external_ids,
    destination_secondary = None,
    destination_property_matcher = externalIdPropertyMatcher
  )

  val mappingWithAllSyncFields = mappingWithDefaultSyncFieldsAndExternalIds.copy(
    fields = SyncField.Values.toSet
  )

  val mapping3 = defaultMapping.copy(
    source_primary = wlp188,
    source_secondary = Some(firm41201),
    destination_primary = unitTestNetwork,
    destination_secondary = Some(coreCapitalLocation)
  )


  val mapping4 = defaultMapping.copy(
    source_primary = wlp100,
    source_secondary = Some(firm41000),
    destination_primary = unitTestNetwork,
    destination_secondary = None,
    destination_property_matcher = distributorIdPropertyMatcher
  )

  val mapping5 = defaultMapping.copy(
    source_primary = wlp12111,
    source_secondary = Some(firm12203),
    destination_primary = unitTestNetwork,
    destination_property_matcher = emailPropertyMatcher
  )

  val flagshipWLPToFlagshipNetwork = defaultMapping.copy(
    destination_primary = flagshipNetwork,
    destination_secondary = None,
    source_secondary = None
  )

  val flagshipWLPAndSanctuaryFirmToSanctuaryNetwork = defaultMapping.copy(
    destination_primary = sanctuaryNetwork,
    source_secondary = Some(sanctuaryFirm),
  )

  val flagshipWLPAmeripriseFirmToRiaNetworkAmeripriseLoc = defaultMapping.copy(
    destination_primary = riaNetwork,
    destination_secondary = Some(ameripriseLocation),
    source_secondary = Some(ameripriseFirm),
  )

  val flagshipWLPCibcFirmtoRIANetworkCibcLoc = defaultMapping.copy(
    destination_primary = riaNetwork,
    destination_secondary = Some(cibcLocation),
    source_secondary = Some(cibcFirm)
  )
  implicit val traceId: TraceId = TraceId.randomize

  val testNetwork = TestNetwork(locations = Set(NetworkLocation("Core Capital", None, Set.empty)))
  val testNetwork2 = testNetwork.copy(id = NetworkId("network01"))

  val testUserId = "test-123"
  val testUserId2 = "test-345"

  val now = LocalDateTime.now()
  val zoneId = ZoneId.systemDefault()
  val fixedInstant = now.atZone(zoneId).toInstant
  val fixedLocalDateTimeTruncated = LocalDateTime.ofInstant(fixedInstant.truncatedTo(ChronoUnit.MILLIS), zoneId)

  val alreadySyncedUser = User(
    id = testUserId,
    networkId = testNetwork.id,
    email = "<EMAIL>",
    firstName = icnUserEvent.first_name,
    lastName = icnUserEvent.last_name,
    distributorId = None,
    roles = Set.empty,
    customRoles = Set("someCustomRole"),
    omsId = None,
    createdAt = now,
    createdBy = "System",
    updatedAt = now,
    updatedBy = "System",
    emailSentAt = None,
    emailSentBy = None,
    lastVisitedAt = None,
    idpLoginId = User.buildIdpLoginId(testNetwork.networkCode, icnUserEvent.email, multiNetworkEnabled = true),
    eventInfo = EventInfo(
      UserDomainEvent.UserCreated,
      "System",
      traceId
    ),
    loginMode = LoginMode.SSO,
    userType = UserType.Human,
    externalIds = Seq.empty,
    firmId = icnUserEvent.firm_id.map(_.toString),
    whiteLabelPartnerId = Some("188"),
    secondaryEmail = None,
    iCapitalUserId = Some(icnUserEvent.user_id.toString),
    locations = Set("someLocation1", "Core Capital", "someLocation2"),
    userSyncedAt = Some(fixedLocalDateTimeTruncated),
  ).resetEntitlements(getAcceptedAccessKeys).updateEntitlements(u => LegacyUser.Entitlements(u.asLegacyUser))

  val syncedUser2: User = alreadySyncedUser.copy(loginMode = LoginMode.ICNUsernamePassword)

  val existingUnsyncedUser = User(
    id = testUserId,
    networkId = testNetwork.id,
    email = icnUserEvent.email,
    firstName = icnUserEvent.first_name,
    lastName = icnUserEvent.last_name,
    distributorId = None,
    roles = Set.empty,
    customRoles = Set("someCustomRole"),
    omsId = None,
    createdAt = now,
    createdBy = "System",
    updatedAt = now,
    updatedBy = "System",
    emailSentAt = None,
    emailSentBy = None,
    lastVisitedAt = None,
    idpLoginId = User.buildIdpLoginId(testNetwork.networkCode, icnUserEvent.email, multiNetworkEnabled = true),
    eventInfo = EventInfo(
      UserDomainEvent.UserCreated,
      "System",
      traceId
    ),
    loginMode = LoginMode.SSOAndUsernamePassword,
    userType = UserType.Human,
    externalIds = Seq.empty,
    locations = Set("someLocation1", "someLocation2"),
  ).resetEntitlements(getAcceptedAccessKeys).updateEntitlements(u => LegacyUser.Entitlements(u.asLegacyUser))

  def mappingRequest(icnUserEvent: IcnUserEvent) = {
    UserNetworkMappingRequest(
      sourceSystem = SourceDestinationSystem(SourceDestinationSystemName.ICN),
      destinationSystem = SourceDestinationSystem(SourceDestinationSystemName.SIMON),
      sourcePrimaryId = icnUserEvent.white_label_partner_id.toString,
      sourcePrimaryIdKind = SourceDestinationPrimaryIdKind.`ICN_WHITE_LABEL`,
      sourceSecondaryId = icnUserEvent.firm_id.flatMap(id => Some(id.toString))
    )
  }

  val baseUpsert = UpsertUserRequest(
    networkId = testNetwork.id,
    email = icnUserEvent.email,
    firstName = icnUserEvent.first_name,
    lastName = icnUserEvent.last_name,
    roles = Set.empty,
    externalIds = Map.empty,
    tradewebEligible = None,
    regSEligible = None,
    loginMode = Some(LoginMode.ICNUsernamePassword)
  )

}

