package com.simonmarkets.users.service

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.logging.TraceId
import com.simonmarkets.mongodb.trigger.event.{MongoEvent, MongoNs, Payload}
import com.simonmarkets.users.common.LoginMode.SSOAndUsernamePassword
import com.simonmarkets.users.common.{EventInfo, User}
import com.simonmarkets.users.common.UserType.Human
import com.simonmarkets.users.common.trigger.UserPayload
import com.simonmarkets.users.config.KafkaProducerConfig
import com.simonmarkets.users.domain.KafkaUserMessageResponse
import com.simonmarkets.users.repository.UserRepository
import io.github.embeddedkafka.{EmbeddedKafka, EmbeddedKafkaConfig}
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.mockito.Mockito.when
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar
import simon.Id.NetworkId
import zio.{Runtime, TaskLayer, _}
import zio.kafka.producer.{Producer, ProducerSettings}
import zio.kafka.admin.AdminClient.NewTopic
import zio.kafka.admin.{AdminClient, AdminClientSettings}
import zio.kafka.consumer.{Consumer, ConsumerSettings, Subscription}
import zio.kafka.consumer.Consumer.{AutoOffsetStrategy, OffsetRetrieval}
import zio.kafka.serde.Serde

import java.time.{LocalDateTime, OffsetDateTime}

import scala.concurrent.ExecutionContext.global
import scala.concurrent.Future


//switch to testcontainer tests once they are able to run in cicd pipeline
class UserEventPublisherSpec extends WordSpec with Matchers with MockitoSugar with EmbeddedKafka with BeforeAndAfterEach {


  val topic = "test"

  implicit val traceId: TraceId = TraceId("test")
  val runtime = Runtime.default


  implicit val embeddedKafkaConfig: EmbeddedKafkaConfig = EmbeddedKafkaConfig(customBrokerProperties = Map("auto.create.topics.enable" -> "false"))

  val kafkaConnection = s"localhost:${embeddedKafkaConfig.kafkaPort}"
  val adminClientSettings = AdminClientSettings(List(kafkaConnection))
  val adminLayer: ZLayer[Any, Throwable, AdminClient] = ZLayer.scoped {
    AdminClient.make(adminClientSettings)
  }
  val createTopicEff: Task[Unit] = {
    for {
      adminClient <- ZIO.service[AdminClient]
      _ <- adminClient.createTopic(NewTopic(topic, 1, 1))
    } yield ()
  }.provideLayer(adminLayer)

  def publisherEff(
      mongoUserEvent: MongoEvent[UserPayload]): ZIO[UserEventPublisher, Throwable, Option[KafkaUserMessageResponse]] =
    for {
      publisher <- ZIO.service[UserEventPublisher]
      res <- publisher.publishUserEventMessage(mongoUserEvent)
    } yield res

  val simonUserEventConsumer: ZLayer[Any, Throwable, Consumer] = ZLayer.scoped {
    Consumer.make(ConsumerSettings(List(kafkaConnection)).withGroupId("test_icn").withOffsetRetrieval(OffsetRetrieval.Auto(AutoOffsetStrategy.Earliest)))
  }

  val userRepo = mock[UserRepository]
  val offsetNow = OffsetDateTime.now()
  val localNow = LocalDateTime.now()

  val user = User(
    id = "id1",
    networkId = NetworkId("SIMON Admin"),
    createdAt = localNow,
    createdBy = "Okta Admin",
    updatedAt = localNow,
    updatedBy = "someUserId2",
    emailSentAt = Some(localNow),
    emailSentBy = Some("Okta Admin"),
    lastVisitedAt = Some(localNow),
    email = "<EMAIL>",
    firstName = "Test",
    lastName = "User",
    distributorId = None,
    omsId = None, roles = Set.empty,
    entitlements = Set("Admin"), idpLoginId = "login", loginMode = SSOAndUsernamePassword,
    userType = Human,
    eventInfo = EventInfo.Default,
    externalIds = Seq.empty,
  )

  val userPayload = UserPayload(
    id = user.id,
    networkId = user.networkId,
    createdAt = user.createdAt,
    createdBy = user.createdBy,
    updatedAt = user.updatedAt,
    updatedBy = user.updatedBy,
    email = user.email,
    firstName = user.firstName,
    lastName = user.lastName,
    roles = Set.empty,
  )

  val producer: ZLayer[Any, Throwable, Producer] = ZLayer.scoped {
    Producer.make(ProducerSettings(List(kafkaConnection)))
  }
  val producer2: ZLayer[Any, Throwable, Producer] = ZLayer.scoped {
    Producer.make(ProducerSettings(List(kafkaConnection)).withProperties(Map("retries" -> "3", "max.block.ms" -> "5000")))
  }
  val producerGroupId = "producerGroupId"
  val source = "simon"
  val producerConfig = KafkaProducerConfig(topic, Some(producerGroupId), Some(source))
  def service(config:KafkaProducerConfig): TaskLayer[UserEventPublisher] = ZLayer.make[UserEventPublisherImpl](
    producer,
    ZLayer.succeed(global),
    ZLayer.succeed(userRepo),
    ZLayer.succeed(config),
    UserEventPublisherImpl.layer,
  )
  val service2: TaskLayer[UserEventPublisher] = ZLayer.make[UserEventPublisherImpl](
    producer2,
    ZLayer.succeed(global),
    ZLayer.succeed(userRepo),
    ZLayer.succeed(producerConfig),
    UserEventPublisherImpl.layer,
  )

  def checkRecordHeaders(
      record: ConsumerRecord[String, Array[Byte]],
      producerGroupIdOpt: Option[String],
      sourceOpt: Option[String]
  ): Unit = {
    val baseRequiredHeaders = List("version", "eventType", "correlationId", "guid")
    val finalRequiredHeaders: List[String] = (producerGroupIdOpt, sourceOpt) match {
      case (Some(_), Some(_)) => baseRequiredHeaders ++ List("producer_group_id", "source")
      case (Some(_), None) => baseRequiredHeaders ++ List("producer_group_id")
      case (None, Some(_)) => baseRequiredHeaders ++ List("source")
      case (_, _) => baseRequiredHeaders
    }
    val requiredHeadersPresent = finalRequiredHeaders.forall(record.headers().toArray.map(_.key).contains)
    if (requiredHeadersPresent) {
      record.headers().toArray.foreach { case (header) =>
        val value = new String(header.value())
        val key = header.key()
        if (key == "source" && value != "simon")
          throw new Error("incorrect source")

        if (key == "version" && value != "1.0")
          throw new Error("incorrect version")

        if (key == "eventType" && (value != "UserCreated" && value != "UserUpdated"))
          throw new Error("incorrect event type")

        if (key == "producer_group_id" && value != producerGroupIdOpt.getOrElse(""))
          throw new Error("incorrect group id")
        if (key == "correlationId" && value != user.eventInfo.correlationId.traceId) throw new Error("wrong correlationId")
        if (key == "guid" && value != traceId.toString()) throw new Error("wrong traceId")
      }
    } else throw new Error("missing header")
  }

  def consumerEff(config: KafkaProducerConfig): ZIO[Consumer, Throwable, Unit] = {
    ZIO.serviceWithZIO[Consumer] { consumer =>
      consumer.consumeWith(Subscription.topics(topic), Serde.string, Serde.byteArray)(record => {
        ZIO.succeed(checkRecordHeaders(record, config.producerGroupId, config.source))
      })
    }
  }

  "UserEventPublisher" should {
    "publishUserEventMessage" in {
      withRunningKafka {
        val mongoEvent: MongoEvent[UserPayload] = MongoEvent(
          id = "mongo_event_1", time = offsetNow, detail = Payload(
            "UPDATE",
            Some(userPayload),
            None,
            MongoNs("pipg", "users")
          )
        )
        when(userRepo.getById("id1")(Set(Admin))).thenReturn(Future.successful(Some(user)))

        Unsafe.unsafe { implicit unsafe =>
          runtime.unsafe.run(createTopicEff)
          val res = runtime.unsafe.run(publisherEff(mongoEvent).provideLayer(service(producerConfig))).getOrThrow()
          res shouldBe Some(KafkaUserMessageResponse(mongoEvent.detail.fullDocument.get.id, topic, 0, user.eventInfo))
        }
      }
    }

    "return none when mongo event cannot be converted to kafka message" in {
      withRunningKafka {
        val mongoEvent: MongoEvent[UserPayload] = MongoEvent(
          id = "mongo_event_1", time = offsetNow, detail = Payload(
            "UPDATE",
            None,
            None,
            MongoNs("pipg", "users")
          )
        )
        Unsafe.unsafe { implicit unsafe =>
          runtime.unsafe.run(createTopicEff)
          val res = runtime.unsafe.run(publisherEff(mongoEvent).provideLayer(service(producerConfig))).getOrThrow()
          res shouldBe None
        }
      }
    }

    "throw error when mongo event cannot be published to topic" in {
      withRunningKafka {
        val mongoEvent: MongoEvent[UserPayload] = MongoEvent(
          id = "mongo_event_1", time = offsetNow, detail = Payload(
            "UPDATE",
            Some(userPayload),
            None,
            MongoNs("pipg", "users")
          )
        )
        when(userRepo.getById("id1")(Set(Admin))).thenReturn(Future.successful(Some(user)))
        Unsafe.unsafe { implicit unsafe =>
          //publishing message to nonexistent topic
          val thrown = the[Error] thrownBy runtime.unsafe.run(publisherEff(mongoEvent).provideLayer(service2)).getOrThrow()
          thrown.getMessage.contains(s"[User Sync Publisher] could not publish mongo event [${mongoEvent.id}] to $topic") shouldBe true
        }
      }
    }

    "record headers published" should {
      when(userRepo.getById("id1")(Set(Admin))).thenReturn(Future.successful(Some(user)))
      val mongoEvent: MongoEvent[UserPayload] = MongoEvent(
        id = "mongo_event_1", time = offsetNow, detail = Payload(
          "UPDATE",
          Some(userPayload),
          None,
          MongoNs("pipg", "users")
        )
      )

      "provide correct record headers in message when producer group id and source are set" in {
        withRunningKafka {
          val produceAndConsumeEff = {
            for {
              _ <- createTopicEff
              _ <- publisherEff(mongoEvent).provideLayer(service(producerConfig))
              _ <- consumerEff(producerConfig)
            } yield ()
          }.provideLayer(simonUserEventConsumer)

          Unsafe.unsafe { implicit unsafe =>
            noException shouldBe thrownBy(runtime.unsafe.run(produceAndConsumeEff.timeout(15.seconds)).getOrThrow())
          }
        }
      }

      "provide correct record headers in message when producer group id and source are not set" in {
        withRunningKafka {
          val config = producerConfig.copy(producerGroupId = None, source = None)
          val produceAndConsumeEff = {
            for {
              _ <- createTopicEff
              _ <- publisherEff(mongoEvent).provideLayer(service(config))
              _ <- consumerEff(config)
            } yield ()
          }.provideLayer(simonUserEventConsumer)

          Unsafe.unsafe { implicit unsafe =>
            noException shouldBe thrownBy(runtime.unsafe.run(produceAndConsumeEff.timeout(15.seconds)).getOrThrow())
          }
        }
      }
    }
  }
}
