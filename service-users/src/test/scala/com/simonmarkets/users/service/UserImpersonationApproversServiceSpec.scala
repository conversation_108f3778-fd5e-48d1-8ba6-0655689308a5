package com.simonmarkets.users.service

import com.goldmansachs.marquee.pipg.UserACL
import com.gs.marquee.foundation.util.AwaitTimeout.FutureTimeoutOps
import com.simonmarkets.logging.TraceId
import com.simonmarkets.users.api.request.UpsertUserImpersonationApproversRequest
import com.simonmarkets.users.domain.UserImpersonationApprovers
import com.simonmarkets.users.repository.MongoUserImpersonationApproversRepository
import com.simonmarkets.users.service.UserImpersonationApproversService.UserImpersonationApproversServiceImpl
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}
import simon.Id.NetworkId

import scala.concurrent.Future

class UserImpersonationApproversServiceSpec extends WordSpec with MockitoSugar with Matchers with BeforeAndAfterEach {

  private implicit val traceId: TraceId = TraceId("user-impersonation-approvers-service-test")
  private implicit val mockUserACL: UserACL = mock[UserACL]

  "UserImpersonationApproversServiceSpec" can {
    "getByNetworkId" should {
      "return the UserImpersonationApprovers by network id successfully" in new GetFixture {
        userImpersonationApproversService.getByNetworkId(getNetworkId.toString).await.get shouldBe getMockUserImpersonationApprovers
      }
    }

    "upsertUserImpersonationApprovers" should {
      "insert user impersonation approvers successfully" in new UpsertFixture {
        userImpersonationApproversService.upsertUserImpersonationApprovers(upsertRequest).await shouldBe upsertMockUserImpersonationApprovers
      }
    }
  }

  private trait GetFixture {
    private val getMockUserImpersonationApproversRepo: MongoUserImpersonationApproversRepository = mock[MongoUserImpersonationApproversRepository]
    protected val getMockUserImpersonationApprovers: UserImpersonationApprovers = mock[UserImpersonationApprovers]
    protected val getNetworkId: NetworkId = NetworkId("networkId1")
    protected val userImpersonationApproversService = new UserImpersonationApproversServiceImpl(getMockUserImpersonationApproversRepo)

    when(getMockUserImpersonationApproversRepo.getByNetworkId(any[String])(any(classOf[TraceId]))) thenReturn Future.successful(Some(getMockUserImpersonationApprovers))

  }

  private trait UpsertFixture {
    private val upsertMockUserImpersonationApproversRepo: MongoUserImpersonationApproversRepository = mock[MongoUserImpersonationApproversRepository]
    protected val upsertMockUserImpersonationApprovers: UserImpersonationApprovers = mock[UserImpersonationApprovers]
    protected val userImpersonationApproversService = new UserImpersonationApproversServiceImpl(upsertMockUserImpersonationApproversRepo)

    private val networkId: NetworkId = NetworkId("networkId1")
    private val upsertMockUserIds: Set[String] = mock[Set[String]]
    protected val upsertRequest: UpsertUserImpersonationApproversRequest = UpsertUserImpersonationApproversRequest(networkId = networkId.toString, userIds = upsertMockUserIds)

    when(upsertMockUserImpersonationApproversRepo.upsertUserImpersonationApprovers(any[UserImpersonationApprovers])(any(classOf[TraceId]))) thenReturn Future.successful(upsertMockUserImpersonationApprovers)
  }
}
