package com.simonmarkets.users.service

import akka.actor.ActorSystem
import com.goldmansachs.marquee.pipg.CustomRoleDefinition
import com.okta.sdk.resource.model.{UserProfile, User => OktaSdkUser}
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.eventbridge.client.EventBridgeClient
import com.simonmarkets.logging.TraceId
import com.simonmarkets.mongodb.trigger.event.{MongoEvent, MongoNs, OperationType, Payload}
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.common.ExternalIdType
import com.simonmarkets.networks.common.repository.{ExternalIdTypeRepository, NetworkRepository}
import com.simonmarkets.networks.common.trigger.NetworkPayload
import com.simonmarkets.okta.BcryptHash
import com.simonmarkets.okta.domain.Factor
import com.simonmarkets.okta.service.OktaService
import com.simonmarkets.okta.testimpl.OktaTestException
import com.simonmarkets.syntax.anyOpsConversion
import com.simonmarkets.users.common.okta.OktaUserConversions
import com.simonmarkets.users.common.trigger.UserPayload
import com.simonmarkets.users.common.{EventInfo, LoginMode, UserDomainEvent}
import com.simonmarkets.users.config.{OktaMultiFactorConfig, OktaSyncConfig}
import com.simonmarkets.users.domain.TransientState
import com.simonmarkets.users.repository.{TransientStateRepository, UserRepository}
import com.simonmarkets.users.service.OktaSyncService.{OktaSyncServiceUser, UserUpdated}
import com.simonmarkets.users.{TestNetwork, TestUser}
import io.simon.encryption.v2.model.EncryptedData
import io.simon.encryption.v3.conversion.{DefaultConverters, EncryptionReader}
import io.simon.encryption.v3.service.EncryptionService
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{reset, when}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{AsyncWordSpec, BeforeAndAfter, Matchers}
import scalacache.CacheConfig
import scalacache.caffeine._
import simon.Id.NetworkId

import java.time.{Instant, LocalDateTime, OffsetDateTime}
import java.util.Base64
import scala.concurrent.Future

class OktaSyncServiceSpec extends AsyncWordSpec with MockitoSugar with BeforeAndAfter with Matchers with JsonCodecs with DefaultConverters {

  implicit val traceId: TraceId = TraceId(getClass.getSimpleName)
  implicit val system: ActorSystem = ActorSystem("OktaSyncActorSystem")
  private val defaultConfig = OktaSyncConfig(serviceEntitlements = Set("admin"), None, OktaMultiFactorConfig("", "", "", "", "", "", "", "", false))
  private val oktaService = mock[OktaService]
  private val encryptionClient = mock[EncryptionService]
  private val usersRepository = mock[UserRepository]
  private val networksRepository = mock[NetworkRepository]
  private val externalIdTypeRepo = mock[ExternalIdTypeRepository]
  private val oktaSyncBus: EventBridgeClient = mock[EventBridgeClient]
  private val transientStateRepository: TransientStateRepository = mock[TransientStateRepository]
  private implicit val customisedCaffeineCache: CaffeineCache[Set[ExternalIdType]] =
    CaffeineCache(CacheConfig.defaultCacheConfig)
  private val oktaSyncService = new OktaSyncService.Impl(
    oktaSyncConfig = defaultConfig,
    oktaService = oktaService,
    usersRepository = usersRepository,
    networksRepository = networksRepository,
    externalIdTypeRepository = externalIdTypeRepo,
    oktaSyncBus = oktaSyncBus,
    encryptionClient = encryptionClient,
    transientStateRepository = transientStateRepository
  )
  private val mfaEnabledRole = "mfaEnabledRole"
  private val mfaEnabledCapability = "shouldEnrollMFASMSAndVoiceCall"
  private val oktaSdkUser = mock[OktaSdkUser]
  private val oktaSdkProfile = mock[UserProfile]

  private val userNetwork = TestNetwork("network-id", customRolesConfig = Set(CustomRoleDefinition(mfaEnabledRole, Set(mfaEnabledCapability))))
  private val idpId = "defaultIdpId"
  private val user = TestUser(
    id = "user-id-1",
    networkId = userNetwork.id,
    idpId = idpId.some
  )
  private val userPassword = user.copy(
    loginMode = LoginMode.ICNUsernamePassword,
    eventInfo = user.eventInfo.copy(eventType = UserDomainEvent.UserSynced)
  )
  private val state = TransientState(
    userId = user.id,
    password = "$2a$10$d64jcnf67sq4wlp0c/fr1.hs5d7t9op[/dt,cra6r.cfs10g63lc5",
    createdAt = Instant.now
  )
  private val userPayload = UserPayload(
    id = user.id,
    networkId = userNetwork.id,
    createdAt = LocalDateTime.now(),
    createdBy = "some-user-id",
    updatedAt = LocalDateTime.now(),
    updatedBy = "some-user-id",
    email = "<EMAIL>",
    firstName = "Test first name",
    lastName = "Test last name",
    roles = Set.empty
  )
  val baseEvent = new MongoEvent[UserPayload](
    id = "event-id",
    time = OffsetDateTime.now(),
    detail = Payload[UserPayload](
      operationType = OperationType.REPLACE,
      fullDocument = Some(userPayload),
      updateDescription = None,
      ns = MongoNs(db = "pipg", coll = "users")
    )
  )
  private val networkPayload = NetworkPayload(
    id = NetworkId("network-id"),
    name = "network-name")
  val baseNetworkEvent = new MongoEvent[NetworkPayload](
    id = "event-id",
    time = OffsetDateTime.now(),
    detail = Payload[NetworkPayload](
      operationType = OperationType.REPLACE,
      fullDocument = Some(networkPayload),
      updateDescription = None,
      ns = MongoNs(db = "pipg", coll = "networks")
    )
  )

  before {
    reset(
      oktaService,
      externalIdTypeRepo,
      usersRepository,
      networksRepository,
      oktaSdkUser,
      oktaSdkProfile,
      transientStateRepository
    )
  }

  "OktaSyncService" can {
    "syncUserToOkta" should {
      "sync user to okta" in {
        when(usersRepository.getById(userPayload.id)(defaultConfig.serviceEntitlements))
          .thenReturn(Future.successful(Some(user)))
        when(networksRepository.getById(user.networkId)(defaultConfig.serviceEntitlements))
          .thenReturn(Future.successful(Some(userNetwork)))
        when(oktaService.enrollFactors(user.id, oktaSdkUser, user.mfas)).thenReturn(Future.successful(Map.empty[String, Factor]))
        when(externalIdTypeRepo.getByNetwork(userNetwork.id)).thenReturn(Future.successful(Seq.empty))
        when(oktaSdkUser.getProfile).thenReturn(oktaSdkProfile)
        when(oktaSdkProfile.getLogin).thenReturn("notValid")
        when(oktaSdkUser.getId).thenReturn(idpId)
        when(oktaService.getSimonId(oktaSdkUser)).thenReturn(Some(user.id))
        when(oktaService.upsertUser(OktaUserConversions.toOktaUser(user, userNetwork.customRolesConfig), includeDeactivated = true)).thenReturn(Future.successful(oktaSdkUser))

        val expected = UserUpdated(
          oktaId = idpId,
          oktaLoginId = "notValid",
          simonId = Some(user.id)
        ).some
        oktaSyncService.syncToOkta(baseEvent).map(r => assert(r == expected))
      }

      "update idpId on user when new user created" in {
        val idpId = "newIpdId"
        val newUser = user.withIdpId(None)
        val updatedUser = user.copy(idpId = idpId.some, eventInfo = EventInfo(UserDomainEvent.UserIdpIdUpdated, OktaSyncServiceUser, traceId, duplicated = false))

        when(networksRepository.getById(userNetwork.id)(defaultConfig.serviceEntitlements))
          .thenReturn(Future.successful(Some(userNetwork)))
        when(usersRepository.getById(userPayload.id)(defaultConfig.serviceEntitlements))
          .thenReturn(Future.successful(Some(newUser)))
        when(oktaService.upsertUser(OktaUserConversions.toOktaUser(newUser, userNetwork.customRolesConfig), includeDeactivated = true))
          .thenReturn(Future.successful(oktaSdkUser))
        when(oktaService.enrollFactors(user.id, oktaSdkUser, user.mfas)).thenReturn(Future.successful(Map.empty[String, Factor]))
        when(oktaSdkUser.getProfile).thenReturn(oktaSdkProfile)
        when(oktaSdkProfile.getLogin).thenReturn(updatedUser.idpId.get)
        when(oktaSdkUser.getId).thenReturn(idpId)
        when(oktaService.getSimonId(oktaSdkUser)).thenReturn(idpId.some)
        when(usersRepository.updateUser(OktaSyncServiceUser, updatedUser))
          .thenReturn(Future.successful(updatedUser))

        val expected = UserUpdated(
          oktaId = idpId,
          oktaLoginId = updatedUser.idpId.get,
          simonId = idpId.some
        ).some
        oktaSyncService.syncToOkta(baseEvent).map(r => assert(r == expected))
      }

      "set password on user" in {
        val asOktaUser = OktaUserConversions.toOktaUser(
          userPassword,
          userNetwork.customRolesConfig,
          password = BcryptHash.fromString(state.password)
        )
        val updatedUser = userPassword.copy(
          eventInfo = EventInfo(UserDomainEvent.UserLoginModeUpdated, OktaSyncServiceUser, traceId),
          loginMode = LoginMode.UnifiedPassword
        )
        when(usersRepository.getById(userPayload.id)(defaultConfig.serviceEntitlements))
          .thenReturn(Future.successful(Some(userPassword)))
        when(networksRepository.getById(userPassword.networkId)(defaultConfig.serviceEntitlements))
          .thenReturn(Future.successful(Some(userNetwork)))
        when(externalIdTypeRepo.getByNetwork(userNetwork.id)).thenReturn(Future.successful(Seq.empty))
        when(oktaSdkUser.getProfile).thenReturn(oktaSdkProfile)
        when(oktaSdkProfile.getLogin).thenReturn("notValid")
        when(oktaSdkUser.getId).thenReturn(idpId)
        when(oktaService.getSimonId(oktaSdkUser)).thenReturn(Some(user.id))
        when(transientStateRepository.getAndDelete(userPassword.id)).thenReturn(Future.successful(Some(state)))
        when(oktaService.upsertUser(asOktaUser, includeDeactivated = true))
          .thenReturn(Future.successful(oktaSdkUser))
        when(oktaService.enrollFactors(user.id, oktaSdkUser, Map.empty))
          .thenReturn(Future.successful(Map.empty[String, Factor]))
        when(usersRepository.updateUser(OktaSyncServiceUser, updatedUser))
          .thenReturn(Future.successful(updatedUser))

        val expected = UserUpdated(
          oktaId = idpId,
          oktaLoginId = "notValid",
          simonId = Some(user.id)
        ).some
        oktaSyncService.syncToOkta(baseEvent).map(r => assert(r == expected))
      }

      "handle user not found gracefully (for deactivated users)" in {
        when(usersRepository.getById(userPayload.id)(defaultConfig.serviceEntitlements))
          .thenReturn(Future.successful(Some(user)))
        when(networksRepository.getById(userNetwork.id)(defaultConfig.serviceEntitlements))
          .thenReturn(Future.successful(Some(userNetwork)))
        when(oktaSdkUser.getId).thenReturn(idpId)

        val exception = OktaTestException.notFoundException

        when(oktaService.upsertUser(OktaUserConversions.toOktaUser(user, userNetwork.customRolesConfig), includeDeactivated = true))
          .thenReturn(Future.failed(exception))
        oktaSyncService.syncToOkta(baseEvent).map(r => assert(r.isEmpty))
      }

      "sync user's unregistered MFAs" in {
        val phoneNumber = "number"
        val encryptedPhoneNumber = Base64.getEncoder.encodeToString(phoneNumber.getBytes)
        val mfas = Map("sms" -> Factor(Some(encryptedPhoneNumber)))
        val decryptedMfas = mfas.updated("sms", Factor(Some(phoneNumber)))
        val updatedMfas = mfas.updated("sms", Factor(Some(encryptedPhoneNumber), isFactorEnrolled = true))
        val userWithoutEnrolledMfas = user.copy(
          mfas = mfas,
          eventInfo = EventInfo(eventType = UserDomainEvent.UserSynced, OktaSyncServiceUser, traceId),
        )
        val updatedUser = userWithoutEnrolledMfas.copy(
          mfas = updatedMfas,
          eventInfo = EventInfo(UserDomainEvent.UserUpdated, OktaSyncServiceUser, traceId)
        )

        when(usersRepository.getById(userPayload.id)(defaultConfig.serviceEntitlements))
          .thenReturn(Future.successful(Some(userWithoutEnrolledMfas)))
        when(transientStateRepository.getAndDelete(userPassword.id)).thenReturn(Future.successful(None))
        when(encryptionClient.bulkDecrypt[String, String](any[Map[String, EncryptedData]])(any[EncryptionReader[String]], any[TraceId]))
          .thenReturn(Future.successful(Map("sms" -> phoneNumber)))
        when(oktaService.enrollFactors(userWithoutEnrolledMfas.id, oktaSdkUser, decryptedMfas))
          .thenReturn(Future.successful(updatedMfas))
        when(usersRepository.updateUser(OktaSyncServiceUser, updatedUser))
          .thenReturn(Future.successful(updatedUser))
        when(networksRepository.getById(user.networkId)(defaultConfig.serviceEntitlements))
          .thenReturn(Future.successful(Some(userNetwork.copy(encryptionClientKey = Some("applicationkey-kms")))))
        when(externalIdTypeRepo.getByNetwork(userNetwork.id)).thenReturn(Future.successful(Seq.empty))
        when(oktaSdkUser.getProfile).thenReturn(oktaSdkProfile)
        when(oktaSdkProfile.getLogin).thenReturn("notValid")
        when(oktaSdkUser.getId).thenReturn(idpId)
        when(oktaService.getSimonId(oktaSdkUser)).thenReturn(Some(user.id))
        when(oktaService.upsertUser(OktaUserConversions.toOktaUser(userWithoutEnrolledMfas, userNetwork.customRolesConfig), includeDeactivated = true))
          .thenReturn(Future.successful(oktaSdkUser))

        val expected = UserUpdated(
          oktaId = idpId,
          oktaLoginId = "notValid",
          simonId = Some(user.id)
        ).some
        oktaSyncService.syncToOkta(baseEvent).map(r => assert(r == expected))
      }

    }

    "defaultedExternalIds" should {

      "correctly pass through matching ids" in {
        val userExternalIds = Seq(ExternalId("subject", "id"))
        val allowableIdTypes = Set(ExternalIdType("subject", Set.empty, "subject"))
        val expected = userExternalIds
        val actual = oktaSyncService.defaultedExternalIds(userExternalIds, allowableIdTypes)
        Future.successful(assert(expected == actual))
      }

      "set not provided ids to empty" in {
        val userExternalIds = Seq(ExternalId("subject", "id"))
        val allowableIdTypes = Set(
          ExternalIdType("subject", Set.empty, "subject"),
          ExternalIdType("other", Set.empty, "other_idp")
        )
        val expected = userExternalIds :+ ExternalId("other_idp", "")
        val actual = oktaSyncService.defaultedExternalIds(userExternalIds, allowableIdTypes)
        Future.successful(assert(expected == actual))
      }
    }

    "user activation/deactivation" should {

      "activate user and send email" in {
        val activationUser = user.copy(eventInfo = EventInfo(UserDomainEvent.UserActivatedSendEmail, "abc", TraceId.randomize))
        val updatedUser = activationUser.copy(eventInfo = EventInfo(UserDomainEvent.UserStateTransitioned, OktaSyncServiceUser, traceId))
        val oktaUser = OktaUserConversions.toOktaUser(activationUser, userNetwork.customRolesConfig)
        when(usersRepository.getById(userPayload.id)(defaultConfig.serviceEntitlements))
          .thenReturn(Future.successful(Some(activationUser)))
        when(networksRepository.getById(activationUser.networkId)(defaultConfig.serviceEntitlements))
          .thenReturn(Future.successful(Some(userNetwork)))
        when(oktaService.enrollFactors(user.id, oktaSdkUser, user.mfas))
          .thenReturn(Future.successful(Map.empty[String, Factor]))
        when(externalIdTypeRepo.getByNetwork(userNetwork.id)).thenReturn(Future.successful(Seq.empty))
        when(oktaSdkUser.getProfile).thenReturn(oktaSdkProfile)
        when(oktaSdkProfile.getLogin).thenReturn("notValid")
        when(oktaSdkUser.getId).thenReturn(idpId)
        when(oktaService.getSimonId(oktaSdkUser)).thenReturn(Some(activationUser.id))
        when(oktaService.upsertUser(oktaUser, includeDeactivated = true)).thenReturn(Future.successful(oktaSdkUser))
        when(oktaService.activateUser(oktaSdkUser, true)).thenReturn(Future.unit)
        when(usersRepository.updateUser(OktaSyncServiceUser, updatedUser))
          .thenReturn(Future.successful(updatedUser))

        val expected = UserUpdated(
          oktaId = idpId,
          oktaLoginId = "notValid",
          simonId = Some(activationUser.id)
        ).some
        oktaSyncService.syncToOkta(baseEvent).map(r => assert(r == expected))
      }

      "activate user and not send email" in {
        val activationUser = user.copy(eventInfo = EventInfo(UserDomainEvent.UserActivatedDontSendEmail, "abc", TraceId.randomize))
        val updatedUser = activationUser.copy(eventInfo = EventInfo(UserDomainEvent.UserStateTransitioned, OktaSyncServiceUser, traceId))
        val oktaUser = OktaUserConversions.toOktaUser(activationUser, userNetwork.customRolesConfig)
        when(usersRepository.getById(userPayload.id)(defaultConfig.serviceEntitlements))
          .thenReturn(Future.successful(Some(activationUser)))
        when(networksRepository.getById(activationUser.networkId)(defaultConfig.serviceEntitlements))
          .thenReturn(Future.successful(Some(userNetwork)))
        when(oktaService.enrollFactors(user.id, oktaSdkUser, user.mfas)).thenReturn(Future.successful(Map.empty[String, Factor]))
        when(externalIdTypeRepo.getByNetwork(userNetwork.id)).thenReturn(Future.successful(Seq.empty))
        when(oktaSdkUser.getProfile).thenReturn(oktaSdkProfile)
        when(oktaSdkProfile.getLogin).thenReturn("notValid")
        when(oktaSdkUser.getId).thenReturn(idpId)
        when(oktaService.getSimonId(oktaSdkUser)).thenReturn(Some(activationUser.id))
        when(oktaService.activateUser(oktaSdkUser, false)).thenReturn(Future.unit)
        when(oktaService.upsertUser(oktaUser, includeDeactivated = true)).thenReturn(Future.successful(oktaSdkUser))
        when(usersRepository.updateUser(OktaSyncServiceUser, updatedUser))
          .thenReturn(Future.successful(updatedUser))

        val expected = UserUpdated(
          oktaId = idpId,
          oktaLoginId = "notValid",
          simonId = Some(activationUser.id)
        ).some
        oktaSyncService.syncToOkta(baseEvent).map(r => assert(r == expected))
      }

      "deactivate user" in {
        val deactivationUser = user.copy(eventInfo = EventInfo(UserDomainEvent.UserDeactivated, "abc", TraceId.randomize), isActive = false)
        val updatedUser = deactivationUser.copy(eventInfo = EventInfo(UserDomainEvent.UserStateTransitioned, OktaSyncServiceUser, traceId))
        val oktaUser = OktaUserConversions.toOktaUser(deactivationUser, userNetwork.customRolesConfig)
        when(usersRepository.getById(userPayload.id)(defaultConfig.serviceEntitlements))
          .thenReturn(Future.successful(Some(deactivationUser)))
        when(networksRepository.getById(deactivationUser.networkId)(defaultConfig.serviceEntitlements))
          .thenReturn(Future.successful(Some(userNetwork)))
        when(oktaService.enrollFactors(user.id, oktaSdkUser, user.mfas)).thenReturn(Future.successful(Map.empty[String, Factor]))
        when(externalIdTypeRepo.getByNetwork(userNetwork.id)).thenReturn(Future.successful(Seq.empty))
        when(oktaSdkUser.getProfile).thenReturn(oktaSdkProfile)
        when(oktaSdkProfile.getLogin).thenReturn("notValid")
        when(oktaSdkUser.getId).thenReturn(idpId)
        when(oktaService.getSimonId(oktaSdkUser)).thenReturn(Some(deactivationUser.id))
        when(oktaService.deactivateUser(oktaSdkUser)).thenReturn(Future.unit)
        when(oktaService.upsertUser(oktaUser, includeDeactivated = true)).thenReturn(Future.successful(oktaSdkUser))
        when(usersRepository.updateUser(OktaSyncServiceUser, updatedUser))
          .thenReturn(Future.successful(updatedUser))

        val expected = UserUpdated(
          oktaId = idpId,
          oktaLoginId = "notValid",
          simonId = Some(deactivationUser.id)
        ).some
        oktaSyncService.syncToOkta(baseEvent).map(r => assert(r == expected))
      }

      "send password reset email using activation flow" in {
        val resetUser = user.copy(eventInfo = EventInfo(UserDomainEvent.UserPasswordReset, "abc", TraceId.randomize))
        val updatedUser = resetUser.copy(eventInfo = EventInfo(UserDomainEvent.UserStateTransitioned, OktaSyncServiceUser, traceId))
        val oktaUser = OktaUserConversions.toOktaUser(resetUser, userNetwork.customRolesConfig)
        when(usersRepository.getById(userPayload.id)(defaultConfig.serviceEntitlements))
          .thenReturn(Future.successful(Some(resetUser)))
        when(networksRepository.getById(resetUser.networkId)(defaultConfig.serviceEntitlements))
          .thenReturn(Future.successful(Some(userNetwork)))
        when(oktaService.enrollFactors(user.id, oktaSdkUser, user.mfas)).thenReturn(Future.successful(Map.empty[String, Factor]))
        when(externalIdTypeRepo.getByNetwork(userNetwork.id)).thenReturn(Future.successful(Seq.empty))
        when(oktaSdkUser.getProfile).thenReturn(oktaSdkProfile)
        when(oktaSdkProfile.getLogin).thenReturn("notValid")
        when(oktaSdkUser.getId).thenReturn(idpId)
        when(oktaService.getSimonId(oktaSdkUser)).thenReturn(Some(resetUser.id))
        when(oktaService.resetPassword(oktaSdkUser)).thenReturn(Future.unit)
        when(oktaService.upsertUser(oktaUser, includeDeactivated = true)).thenReturn(Future.successful(oktaSdkUser))
        when(usersRepository.updateUser(OktaSyncServiceUser, updatedUser))
          .thenReturn(Future.successful(updatedUser))

        val expected = UserUpdated(
          oktaId = idpId,
          oktaLoginId = "notValid",
          simonId = Some(resetUser.id)
        ).some
        oktaSyncService.syncToOkta(baseEvent).map(r => assert(r == expected))
      }
    }
  }

}
