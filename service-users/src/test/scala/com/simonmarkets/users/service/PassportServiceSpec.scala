package com.simonmarkets.users.service

import akka.http.scaladsl.model.StatusCodes
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.{Capabilities, UsersCapabilities}
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.api.{CandidatesRequest, CandidatesResponse}
import com.simonmarkets.networks.common.clients.emailsender.{EmailRequest, EmailSenderClient}
import com.simonmarkets.networks.common.clients.icn.HttpIcnClient
import com.simonmarkets.networks.common.domain.PassportUserCandidate
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.users.TestUser
import com.simonmarkets.users.api.request.{PassportConfirmation, PassportVerificationRequest}
import com.simonmarkets.users.domain.PassportVerification
import com.simonmarkets.users.repository.{PassportRepository, UserRepository}
import io.simon.encryption.v3.conversion.DefaultConverters
import io.simon.encryption.v3.service.EncryptionService
import org.mockito.ArgumentMatchers.{any, eq => meq}
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}

import java.time.Instant

import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}

class PassportServiceSpec extends WordSpec with Matchers with MockitoSugar with DefaultConverters {

  //data
  private val email = "email"
  private val domain1 = "domain1"
  private val domain2 = "domain2"
  private val domain3 = "domain3"
  private val candidate1 = PassportUserCandidate(1, email, s"$domain1.icapitalnetwork.com", "whiteLabelPartnerName_1")
  private val candidate2 = PassportUserCandidate(2, email, s"$domain2.icapitalnetwork.com", "whiteLabelPartnerName_2")
  private val candidate3 = PassportUserCandidate(3, email, s"$domain3.icapitalnetwork.com", "whiteLabelPartnerName_3")
  private val request = PassportVerificationRequest(Set(2, 3))
  private val validationReq = CandidatesRequest(request.userIds + 1)
  private val jti = "jti"
  private val authToken = "authToken"
  private val acl = mock[UserACL]
  private val userId = "userId"
  private val user = TestUser.apply(userId)
  private val code = "code"
  private val hashedCode = code.getBytes
  private val creationTime = Instant.now
  private val verificationDoc = PassportVerification(
    id = "id",
    userId = userId,
    users = List(candidate1, candidate2, candidate3),
    code = hashedCode,
    sessionId = jti,
    creationTime = creationTime,
    completionTime = None,
    pending = true
  )
  private val confirmation = PassportConfirmation(code)

  //dependencies
  private val userRepository = mock[UserRepository]
  private val passportRepository = mock[PassportRepository]
  private val emailClient = mock[EmailSenderClient]
  private val icnClient = mock[HttpIcnClient]
  private val encryptionClient = mock[EncryptionService]
  private val validityWindow = 1.minute
  private val codeLength = 6
  private val random = Iterator.continually(1)
  private val from = "<EMAIL>"
  private implicit val ec: ExecutionContext = ExecutionContext.global
  private implicit val traceId: TraceId = TraceId.randomize

  private val service = PassportService.V1(
    userRepository,
    passportRepository,
    emailClient,
    icnClient,
    encryptionClient,
    validityWindow,
    codeLength,
    random,
    None,
    from
  )

  //global mocks
  when(encryptionClient.hash(code)).thenReturn(hashedCode)

  "PassportService" can {

    "initializeVerification" should {

      val validationResponse = CandidatesResponse(Seq(candidate1, candidate2))
      when(acl.email).thenReturn(email)
      when(passportRepository.insert(any[PassportVerification])).thenReturn(Future.unit)
      when(emailClient.sendEmail(any[EmailRequest])(meq(traceId))).thenReturn(Future.unit)
      when(icnClient.candidates(validationReq, authToken)).thenReturn(Future.successful((validationResponse, None)))

      "succeed" in {
        service.initializeVerification(acl, request, authToken, jti).await
      }

      "fail if not all emails the same" in {
        val badValidationResponse = CandidatesResponse(Seq(candidate1.copy(email = "badEmail"), candidate2))
        when(icnClient.candidates(validationReq, authToken))
          .thenReturn(Future.successful((badValidationResponse, None)))
        intercept[HttpError] {
          service.initializeVerification(acl, request, authToken, jti).await
        }.status shouldBe StatusCodes.BadRequest
      }

      "fail if emails do not match user" in {
        when(acl.email).thenReturn("different")
        val error = intercept[HttpError] {
          service.initializeVerification(acl, request, authToken, jti).await
        }
        error.status shouldBe StatusCodes.BadRequest
        error.payload should include("All passport candidate users must have same email")
      }

    }

    "confirm" should {

      when(passportRepository.findByCode(hashedCode)).thenReturn(Future.successful(Some(verificationDoc)))
      when(acl.iCapitalUserId).thenReturn(Some("1"))
      val updatedPassport = user.copy(
        passport = Map(
          domain1 -> candidate1.icnId,
          domain2 -> candidate2.icnId,
          domain3 -> candidate3.icnId
        )
      )

      "successfully create initial passport" in {

        when(passportRepository.confirm(hashedCode)).thenReturn(Future.unit)
        when(userRepository.getById(verificationDoc.userId)(Set(Capabilities.Admin)))
          .thenReturn(Future.successful(Some(user)))
        when(userRepository.updateUser(userId, updatedPassport)).thenReturn(Future.successful(updatedPassport))
        service.confirm(acl, confirmation, jti).await
      }

      "successfully add to passport" in {
        val userWithExistingPassport = user.copy(
          passport = Map(
            domain1 -> candidate1.icnId,
            domain2 -> candidate2.icnId
          )
        )
        val validationReqAdd = CandidatesRequest(Set(candidate1.icnId, candidate3.icnId))
        val validationResponseAdd = CandidatesResponse(Seq(candidate1, candidate3))
        when(icnClient.candidates(validationReqAdd, authToken))
          .thenReturn(Future.successful((validationResponseAdd, None)))
        when(passportRepository.confirm(hashedCode)).thenReturn(Future.unit)
        when(userRepository.getById(verificationDoc.userId)(Set(Capabilities.Admin)))
          .thenReturn(Future.successful(Some(userWithExistingPassport)))
        when(userRepository.updateUser(userId, updatedPassport)).thenReturn(Future.successful(updatedPassport))
        service.confirm(acl, confirmation, jti).await
      }

      "fail if session id does not match" in {
        when(passportRepository.findByCode(hashedCode))
          .thenReturn(Future.successful(Some(verificationDoc.copy(sessionId = "other"))))
        intercept[HttpError] {
          service.confirm(acl, confirmation, jti).await
        }.status shouldBe StatusCodes.BadRequest
      }

      "fail if verification already completed" in {
        when(passportRepository.findByCode(hashedCode))
          .thenReturn(Future.successful(Some(verificationDoc.copy(pending = false))))
        val error = intercept[HttpError] {
          service.confirm(acl, confirmation, jti).await
        }
        error.status shouldBe StatusCodes.BadRequest
        error.payload should include("Verification already completed")
      }

      "fail if time window expired" in {
        when(passportRepository.findByCode(hashedCode))
          .thenReturn(Future.successful(Some(verificationDoc.copy(creationTime = Instant.now.minusSeconds(3600)))))
        val error = intercept[HttpError] {
          service.confirm(acl, confirmation, jti).await
        }
        error.status shouldBe StatusCodes.BadRequest
        error.payload should include("Verification code expired")
      }

    }

    "removePassport" should {

      val init = user.copy(passport =
        Map(
          candidate1.domain -> candidate1.icnId,
          candidate2.domain -> candidate2.icnId,
        )
      )
      val updated = user.copy(passport = Map.empty)
      when(acl.userId).thenReturn(userId)
      when(acl.capabilities).thenReturn(UsersCapabilities.DeletePassportCapabilities)
      when(userRepository.getById(meq(userId))(any[Set[String]])(meq(traceId)))
        .thenReturn(Future.successful(Some(init)))
      when(userRepository.updateUser(userId, updated)).thenReturn(Future.successful(updated))

      "succeed" in {
        service.removePassport(acl).await
      }

      "fail if user does not have delete passport capability" in {
        when(acl.capabilities).thenReturn(Set.empty[String])
        when(userRepository.getById(userId)(Set.empty[String]))
          .thenReturn(Future.successful(None))
        intercept[HttpError] {
          service.removePassport(acl).await
        }.status shouldBe StatusCodes.NotFound
      }
    }
  }
}
