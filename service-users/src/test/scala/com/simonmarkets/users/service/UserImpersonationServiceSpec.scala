package com.simonmarkets.users.service

import com.goldmansachs.marquee.pipg.UserACL
import com.gs.marquee.foundation.util.AwaitTimeout.FutureTimeoutOps
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.NetworkIds.AdminNetworkId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.networks.common.TestUtils.FutureOps
import com.simonmarkets.networks.common.api.CanImpersonateResponse
import com.simonmarkets.networks.common.clients.emailsender.{EmailRequest, EmailSenderClient, Recipients, TemplateId}
import com.simonmarkets.networks.common.clients.icn.IcnClient
import com.simonmarkets.networks.common.repository.MongoNetworkRepository
import com.simonmarkets.syntax.anyOpsConversion
import com.simonmarkets.users.api.request.{ApproveUserImpersonationRequest, ForceCompleteImpersonationRequest, InsertUserImpersonationRequest}
import com.simonmarkets.users.common.{User, UserType}
import com.simonmarkets.users.domain.{ImpersonationStatus, ImpersonationStatusResponse, UserImpersonation, UserImpersonationApprovalRequestContent, UserImpersonationApprovers, UserImpersonationStatusUpdateContent}
import com.simonmarkets.users.repository.{MongoUserImpersonationApproversRepository, MongoUserImpersonationRepository, MongoUserRepository}
import com.simonmarkets.users.service.UserImpersonationService.UserImpersonationServiceImpl
import io.circe.syntax.EncoderOps
import org.mockito.AdditionalAnswers.answer
import org.mockito.ArgumentMatchers.{any, eq => eqTo}
import org.mockito.Mockito.when
import org.mockito.stubbing.{Answer1, Answer3}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}
import simon.Id
import simon.Id.NetworkId

import java.time.Instant
import java.time.temporal.ChronoUnit

import scala.concurrent.{ExecutionContext, Future}

class UserImpersonationServiceSpec extends WordSpec with MockitoSugar with Matchers with BeforeAndAfterEach {

  private implicit val traceId: TraceId = TraceId("user-impersonation-test")
  private implicit val executionContext: ExecutionContext = ExecutionContext.global
  private val excludedNetwork = NetworkId("excludedNetworkId")

  "UserImpersonationServiceSpec" can {
    "getById" should {
      "get user impersonation by id successfully" in new ServiceFixture {
        when(userImpersonationRepo.getById(any[String])(any[Set[String]])(any(classOf[TraceId]))) thenReturn Future.successful(Some(mockUserImpersonation))
        userImpersonationService.getById(mockUserImpersonation.id).await.get shouldBe mockUserImpersonation
      }
    }

    "getByImpersonatorUserIdThenExpire" should {
      "should return UserImpersonation and not expire for a new Pending User Impersonation" in new ServiceFixture {
        when(userImpersonationRepo.getNonFinishedUserImpersonations(any[String])(any(classOf[TraceId]))) thenReturn Future.successful(Seq(userImpersonation))
        when(userImpersonationRepo.expireAll(any[Seq[String]])(any(classOf[TraceId]))) thenReturn Future.successful(false)
        userImpersonationService.getByImpersonatorUserIdThenExpire(userImpersonation.impersonatorUserId).await.get shouldBe userImpersonation
      }

      "should return None and expire for an old Pending User Impersonation" in new ServiceFixture {
        val oldPendingUserImpersonation: UserImpersonation = userImpersonation.copy(impersonatorUserId = "impersonatorUserId1", createdAt = Instant.now.minus(2, ChronoUnit.DAYS))
        when(userImpersonationRepo.getNonFinishedUserImpersonations(any[String])(any(classOf[TraceId]))) thenReturn Future.successful(Seq(oldPendingUserImpersonation))
        when(userImpersonationRepo.expireAll(any[Seq[String]])(any(classOf[TraceId]))) thenReturn Future.successful(true)
        userImpersonationService.getByImpersonatorUserIdThenExpire(oldPendingUserImpersonation.impersonatorUserId).await shouldBe None
      }

      "should return None and not expire for a new Approved User Impersonation" in new ServiceFixture {
        val newApprovedUserImpersonation: UserImpersonation = userImpersonation.copy(impersonatorUserId = "impersonatorUserId1", status = ImpersonationStatus.Approved)
        when(userImpersonationRepo.getNonFinishedUserImpersonations(any[String])(any(classOf[TraceId]))) thenReturn Future.successful(Seq(newApprovedUserImpersonation))
        when(userImpersonationRepo.expireAll(any[Seq[String]])(any(classOf[TraceId]))) thenReturn Future.successful(false)
        userImpersonationService.getByImpersonatorUserIdThenExpire(newApprovedUserImpersonation.impersonatorUserId).await shouldBe None
      }
    }

    "getByImpersonatedUserId" should {
      "get user impersonation by impersonatedUserId successfully" in new ServiceFixture {
        when(userImpersonationRepo.getByImpersonatedUserId(any[String])(any(classOf[TraceId]))) thenReturn Future.successful(Some(mockUserImpersonation))
        userImpersonationService.getByImpersonatedUserId(mockUserImpersonation.impersonatedUserId).await.get shouldBe mockUserImpersonation
      }
    }

    "getImpersonationStatus" should {
      "return User Impersonation status when user has the capability" in new GetImpersonationStatusFixture {
        userImpersonationService.getImpersonationStatus(mockImpersonatedUser.id).await shouldBe ImpersonationStatusResponse(ImpersonationStatus.Pending.toString)
      }

      "return NotSubmitted when the user does not have the necessary capabilities" in new GetImpersonationStatusFixture {
        // when(mockUserACL.capabilities).thenReturn(Set("none"))
        when(userImpersonationRepo.getByImpersonatorUserIdAndImpersonatedUserId(any[String], any[String])(any[Set[String]])(any[TraceId])) thenReturn Future.successful(None)
        userImpersonationService.getImpersonationStatus(mockImpersonatedUser.id).await shouldBe ImpersonationStatusResponse(ImpersonationStatus.NotSubmitted.toString)
      }

      "return NotSubmitted when there is no existing impersonation" in new GetImpersonationStatusFixture {
        when(userImpersonationRepo.getByImpersonatorUserIdAndImpersonatedUserId(any[String], any[String])(any[Set[String]])(any[TraceId])) thenReturn Future.successful(None)
        userImpersonationService.getImpersonationStatus(mockImpersonatedUser.id).await shouldBe ImpersonationStatusResponse(ImpersonationStatus.NotSubmitted.toString)
      }
    }

    "insert" should {
      "create user impersonation without a ticketNumber successfully" in new InsertFixture {
        userImpersonationService.insert(insertRequest, icnAuthToken).await shouldBe mockUserImpersonation
      }

      "create user impersonation with a ticketNumber successfully" in new InsertFixture {
        private val request = InsertUserImpersonationRequest(impersonatedUserId = mockImpersonatedUser.id, reason = "", ticketNumber = Some("CM-4884"))
        userImpersonationService.insert(request, icnAuthToken).await shouldBe mockUserImpersonation
      }

      "create the user impersonation for a unified impersonator user and a unified impersonated user" in new InsertFixture {
        private val request = InsertUserImpersonationRequest(impersonatedUserId = mockImpersonatedUser.id, reason = "", ticketNumber = None)
        userImpersonationService.insert(request, icnAuthToken).await shouldBe mockUserImpersonation
      }

      "don't create the user impersonation for a unified impersonator user who doesn't have the necessary capabilities" in new InsertFixture {
        when(mockUserACL.capabilities).thenReturn(Set("none"))
        private val request = InsertUserImpersonationRequest(impersonatedUserId = mockImpersonatedUser.id, reason = "", ticketNumber = None)
        an[HttpError] shouldBe thrownBy(userImpersonationService.insert(request, icnAuthToken).await)
      }

      "create the user impersonation for a unified impersonator user and a simon impersonated user" in new InsertFixture {
        when(mockImpersonatedUser.iCapitalUserId).thenReturn(None)
        private val request = InsertUserImpersonationRequest(impersonatedUserId = mockImpersonatedUser.id, reason = "", ticketNumber = None)
        userImpersonationService.insert(request, icnAuthToken).await shouldBe mockUserImpersonation
      }

      "don't create the user impersonation for a unified impersonator and a icn impersonated user" in new InsertFixture {
        when(mockUserACL.capabilities).thenReturn(Set("none"))
        when(mockImpersonatedUser.id).thenReturn(null)
        private val request = InsertUserImpersonationRequest(impersonatedUserId = mockImpersonatedUser.id, reason = "", ticketNumber = None)
        an[HttpError] shouldBe thrownBy(userImpersonationService.insert(request, icnAuthToken).await)
      }

      "don't create the user impersonation for a non admin simon impersonator and a unified impersonated user" in new InsertFixture {
        when(mockUserACL.iCapitalUserId).thenReturn(None)
        when(mockUserACL.capabilities).thenReturn(Set("none"))
        private val request = InsertUserImpersonationRequest(impersonatedUserId = mockImpersonatedUser.id, reason = "", ticketNumber = None)
        an[HttpError] shouldBe thrownBy(userImpersonationService.insert(request, icnAuthToken).await)
      }

      "create the user impersonation for a admin simon impersonator and a unified impersonated user" in new InsertFixture {
        when(mockUserACL.iCapitalUserId).thenReturn(None)
        when(mockUserACL.capabilities).thenReturn(Set("admin"))
        private val request = InsertUserImpersonationRequest(impersonatedUserId = mockImpersonatedUser.id, reason = "", ticketNumber = None)
        userImpersonationService.insert(request, icnAuthToken).await shouldBe mockUserImpersonation
      }

      "create the user impersonation for a simon impersonator user and a simon impersonated user" in new InsertFixture {
        when(mockUserACL.iCapitalUserId).thenReturn(None)
        when(mockImpersonatedUser.iCapitalUserId).thenReturn(None)
        private val request = InsertUserImpersonationRequest(impersonatedUserId = mockImpersonatedUser.id, reason = "", ticketNumber = None)
        userImpersonationService.insert(request, icnAuthToken).await shouldBe mockUserImpersonation
      }

      "don't create the user impersonation for a simon impersonator and a icn impersonated user" in new InsertFixture {
        when(mockUserACL.iCapitalUserId).thenReturn(None)
        when(mockUserACL.capabilities).thenReturn(Set("none"))
        when(mockImpersonatedUser.id).thenReturn(null)
        private val request = InsertUserImpersonationRequest(impersonatedUserId = mockImpersonatedUser.id, reason = "", ticketNumber = None)
        an[HttpError] shouldBe thrownBy(userImpersonationService.insert(request, icnAuthToken).await)
      }

      "fail with Forbidden when user does not have necessary capabilities" in new InsertFixture {
        when(mockUserACL.capabilities).thenReturn(Set("none"))
        an[HttpError] shouldBe thrownBy(userImpersonationService.insert(insertRequest, icnAuthToken).await)
      }

      "fail with Forbidden when impersonated user in excluded network" in new InsertFixture {
        when(mockImpersonatedUser.networkId).thenReturn(excludedNetwork)
        an[HttpError] shouldBe thrownBy(userImpersonationService.insert(insertRequest, icnAuthToken).await)
      }

      "create user impersonation successfully of a Preview User" in new InsertFixture {
        when(mockImpersonatedUser.userType).thenReturn(UserType.Preview)
        userImpersonationService.insert(insertRequest, icnAuthToken).await shouldBe mockUserImpersonation
      }

      "fail when non-admin user tries to impersonate PreviewUser" in new InsertFixture {
        when(mockImpersonatedUser.userType).thenReturn(UserType.Preview)
        when(mockUserACL.networkId).thenReturn(NetworkId("nope"))
        userImpersonationService.insert(insertRequest, icnAuthToken) assertFailureEx HttpError.forbidden("Only users in the iCapital Admin Network can impersonate a Preview User")
      }

      "create a user impersonation approval request without a ticket number" in new InsertApprovalFixture {
        when(userImpersonationRepo.getByImpersonatorUserIdAndImpersonatedUserId(any[String], any[String])(any[Set[String]])(any[TraceId]))
          .thenReturn(None.successFuture)
        val insertRequest: InsertUserImpersonationRequest = InsertUserImpersonationRequest(impersonatedUserId = mockImpersonatedUser.id, reason = "insert with approval", ticketNumber = None)
        val result: UserImpersonation = userImpersonationService.insert(insertRequest, icnAuthToken).await

        result.status shouldBe ImpersonationStatus.Submitted
        result.impersonatedUserId shouldBe mockImpersonatedUser.id
        result.impersonatorUserId shouldBe mockImpersonatorUser.id

        emailCapture.lastEmail match {
          case None => fail("email request was never sent")
          case Some(emailRequest) =>
            emailRequest.eventId shouldBe result.id
            emailRequest.eventType shouldBe TemplateId.`user-impersonation-approval-request`.productPrefix
            emailRequest.templateId shouldBe TemplateId.`user-impersonation-approval-request`
            emailRequest.recipients shouldBe Recipients(userIds = Set("approverUserId1", "approverUserId2"), emails = Set())
            emailRequest.subject shouldBe "User Impersonation Approval Request"
            emailRequest.content shouldBe UserImpersonationApprovalRequestContent(
              impersonationUUID = result.id,
              impersonatorUserId = mockImpersonatorUser.id,
              impersonatorUserName = mockImpersonatorUser.fullName,
              impersonatedUserId = mockImpersonatedUser.id,
              impersonatedUserName = mockImpersonatedUser.fullName,
              networkId = mockImpersonatedUser.networkId,
              networkName = mockNetwork.name,
              reason = insertRequest.reason
            ).asJson
        }
      }

      "create a user impersonation approval request with a ticket number" in new InsertApprovalFixture {
        when(userImpersonationRepo.getByImpersonatorUserIdAndImpersonatedUserId(any[String], any[String])(any[Set[String]])(any[TraceId]))
          .thenReturn(None.successFuture)
        val ticketNumber = "CM-4885"
        val insertRequest: InsertUserImpersonationRequest = InsertUserImpersonationRequest(impersonatedUserId = mockImpersonatedUser.id, reason = "insert with approval", ticketNumber = Some(ticketNumber))
        val result: UserImpersonation = userImpersonationService.insert(insertRequest, icnAuthToken).await

        result.status shouldBe ImpersonationStatus.Submitted
        result.impersonatedUserId shouldBe mockImpersonatedUser.id
        result.impersonatorUserId shouldBe mockImpersonatorUser.id
        result.ticketNumber shouldBe Some(ticketNumber)

        emailCapture.lastEmail match {
          case None => fail("email request was never sent")
          case Some(emailRequest) =>
            emailRequest.eventId shouldBe result.id
            emailRequest.eventType shouldBe TemplateId.`user-impersonation-approval-request`.productPrefix
            emailRequest.templateId shouldBe TemplateId.`user-impersonation-approval-request`
            emailRequest.recipients shouldBe Recipients(userIds = Set("approverUserId1", "approverUserId2"), emails = Set())
            emailRequest.subject shouldBe "User Impersonation Approval Request"
            emailRequest.content shouldBe UserImpersonationApprovalRequestContent(
              impersonationUUID = result.id,
              impersonatorUserId = mockImpersonatorUser.id,
              impersonatorUserName = mockImpersonatorUser.fullName,
              impersonatedUserId = mockImpersonatedUser.id,
              impersonatedUserName = mockImpersonatedUser.fullName,
              networkId = mockImpersonatedUser.networkId,
              networkName = mockNetwork.name,
              reason = insertRequest.reason
            ).asJson
        }
      }

      "not create a user impersonation request if the permissions aren't correct" in new InsertApprovalFixture {
        when(mockUserACL.networkId).thenReturn(NetworkId("nope"))
        when(mockUserACL.capabilities).thenReturn(Set("none"))
        val ticketNumber = "CM-4886"
        val insertRequest: InsertUserImpersonationRequest = InsertUserImpersonationRequest(impersonatedUserId = mockImpersonatedUser.id, reason = "insert with approval", ticketNumber = Some(ticketNumber))
        an[HttpError] shouldBe thrownBy(userImpersonationService.insert(insertRequest, icnAuthToken).await)
      }

      "return the current request if one exists and is pending" in new InsertApprovalFixture {
        when(mockUserImpersonation.status)
          .thenReturn(ImpersonationStatus.Pending)
        val insertRequest: InsertUserImpersonationRequest = InsertUserImpersonationRequest(impersonatedUserId = mockImpersonatedUser.id, reason = "insert with approval", ticketNumber = None)
        val result: UserImpersonation = userImpersonationService.insert(insertRequest, icnAuthToken).await
        result shouldBe mockUserImpersonation
      }

      "update an approved request to pending" in new InsertApprovalFixture {
        when(mockUserImpersonation.status)
          .thenReturn(ImpersonationStatus.Approved)
        when(mockUserImpersonation.id)
          .thenReturn("8675309")
        when(userImpersonationRepo.getByImpersonatorUserIdAndImpersonatedUserId(mockImpersonatorUser.id, mockImpersonatedUser.id)(mockUserACL.capabilities))
          .thenReturn(Some(mockUserImpersonation).successFuture)
        when(userImpersonationRepo.updateStatus(any[String], any[ImpersonationStatus], any[ImpersonationStatus])(any[Set[String]])(any[TraceId]))
          .thenAnswer(answer(new Answer3[Future[Option[UserImpersonation]], String, ImpersonationStatus, ImpersonationStatus] {
            override def answer(impersonationId: String, fromStatus: ImpersonationStatus,
                toStatus: ImpersonationStatus): Future[Option[UserImpersonation]] = {
              impersonationId shouldBe mockUserImpersonation.id
              fromStatus shouldBe ImpersonationStatus.Approved
              toStatus shouldBe ImpersonationStatus.Pending
              Future.successful(Some(mockUserImpersonation))
            }
          }))
        val insertRequest: InsertUserImpersonationRequest = InsertUserImpersonationRequest(impersonatedUserId = mockImpersonatedUser.id, reason = "insert with approval", ticketNumber = None)
        val result: UserImpersonation = userImpersonationService.insert(insertRequest, icnAuthToken).await
        result shouldBe mockUserImpersonation
      }

      "leave the request unmodified but re-send the email if the status is submitted" in new InsertApprovalFixture {
        when(mockUserImpersonation.status)
          .thenReturn(ImpersonationStatus.Submitted)
        when(mockUserImpersonation.id)
          .thenReturn("8675309")
        when(mockUserImpersonation.impersonatorUserId)
          .thenReturn(sourceUserId)
        when(mockUserImpersonation.impersonatedUserId)
          .thenReturn(targetUserId)
        val insertRequest: InsertUserImpersonationRequest = InsertUserImpersonationRequest(impersonatedUserId = mockImpersonatedUser.id, reason = "insert with approval", ticketNumber = None)
        when(mockUserImpersonation.reason)
          .thenReturn(insertRequest.reason)
        when(userImpersonationRepo.getByImpersonatorUserIdAndImpersonatedUserId(mockImpersonatorUser.id, mockImpersonatedUser.id)(mockUserACL.capabilities))
          .thenReturn(Some(mockUserImpersonation).successFuture)

        val result: UserImpersonation = userImpersonationService.insert(insertRequest, icnAuthToken).await

        result shouldBe mockUserImpersonation

        emailCapture.lastEmail match {
          case None => fail("email request was never sent")
          case Some(emailRequest) =>
            emailRequest.eventId shouldBe result.id
            emailRequest.eventType shouldBe TemplateId.`user-impersonation-approval-request`.productPrefix
            emailRequest.templateId shouldBe TemplateId.`user-impersonation-approval-request`
            emailRequest.recipients shouldBe Recipients(userIds = Set("approverUserId1", "approverUserId2"), emails = Set())
            emailRequest.subject shouldBe "User Impersonation Approval Request"
            emailRequest.content shouldBe UserImpersonationApprovalRequestContent(
              impersonationUUID = result.id,
              impersonatorUserId = mockImpersonatorUser.id,
              impersonatorUserName = mockImpersonatorUser.fullName,
              impersonatedUserId = mockImpersonatedUser.id,
              impersonatedUserName = mockImpersonatedUser.fullName,
              networkId = mockImpersonatedUser.networkId,
              networkName = mockNetwork.name,
              reason = insertRequest.reason
            ).asJson
        }
      }

      "create a user impersonation approval request without a ticket number even if a previous request exists" in new InsertApprovalFixture {
        when(mockUserImpersonation.status)
          .thenReturn(ImpersonationStatus.Expired)
        when(mockUserImpersonation.id)
          .thenReturn("8675309")
        when(userImpersonationRepo.getByImpersonatorUserIdAndImpersonatedUserId(mockImpersonatorUser.id, mockImpersonatedUser.id)(mockUserACL.capabilities))
          .thenReturn(Some(mockUserImpersonation).successFuture)

        val insertRequest: InsertUserImpersonationRequest = InsertUserImpersonationRequest(impersonatedUserId = mockImpersonatedUser.id, reason = "insert with approval", ticketNumber = None)
        val result: UserImpersonation = userImpersonationService.insert(insertRequest, icnAuthToken).await

        result should not be mockUserImpersonation
        result.status shouldBe ImpersonationStatus.Submitted
        result.impersonatedUserId shouldBe mockImpersonatedUser.id
        result.impersonatorUserId shouldBe mockImpersonatorUser.id

        emailCapture.lastEmail match {
          case None => fail("email request was never sent")
          case Some(emailRequest) =>
            emailRequest.eventId shouldBe result.id
            emailRequest.eventType shouldBe TemplateId.`user-impersonation-approval-request`.productPrefix
            emailRequest.templateId shouldBe TemplateId.`user-impersonation-approval-request`
            emailRequest.recipients shouldBe Recipients(userIds = Set("approverUserId1", "approverUserId2"), emails = Set())
            emailRequest.subject shouldBe "User Impersonation Approval Request"
            emailRequest.content shouldBe UserImpersonationApprovalRequestContent(
              impersonationUUID = result.id,
              impersonatorUserId = mockImpersonatorUser.id,
              impersonatorUserName = mockImpersonatorUser.fullName,
              impersonatedUserId = mockImpersonatedUser.id,
              impersonatedUserName = mockImpersonatedUser.fullName,
              networkId = mockImpersonatedUser.networkId,
              networkName = mockNetwork.name,
              reason = insertRequest.reason
            ).asJson
        }
      }

    }

    "approve" should {
      "approve a request if a user is an authorized approver and the request is submitted" in new ApproveFixture {
        when(mockUserACL.userId).thenReturn("approverUserId2")
        when(mockUserImpersonation.id).thenReturn("pendingImpersonationId")
        when(mockUserImpersonation.status).thenReturn(ImpersonationStatus.Submitted)
        when(mockUserImpersonation.approversUserIds).thenReturn(Some(Set("approverUserId2")))
        var updateStatusCount: Int = 0
        when(userImpersonationRepo.approveOrRejectStatus(any[String], any[ImpersonationStatus], any[String])(any[Set[String]])(any[TraceId]))
          .thenAnswer(answer(new Answer3[Future[Option[UserImpersonation]], String, ImpersonationStatus, String] {
            override def answer(impersonationId: String, toStatus: ImpersonationStatus,
                approverUserId: String): Future[Option[UserImpersonation]] = {
              impersonationId shouldBe mockUserImpersonation.id
              toStatus shouldBe ImpersonationStatus.Approved
              approverUserId shouldBe "approverUserId2"
              updateStatusCount += 1
              Future.successful(Some(mockUserImpersonation))
            }
          }))
        val request: ApproveUserImpersonationRequest = ApproveUserImpersonationRequest(ImpersonationStatus.Approved, "pendingImpersonationId")
        val result: UserImpersonation = userImpersonationService.approve(request).await

        result shouldBe mockUserImpersonation
        updateStatusCount shouldBe 1

        emailCapture.lastEmail match {
          case None => fail("email request was never sent")
          case Some(emailRequest) =>
            emailRequest.eventId shouldBe result.id
            emailRequest.eventType shouldBe TemplateId.`user-impersonation-status-update`.productPrefix
            emailRequest.templateId shouldBe TemplateId.`user-impersonation-status-update`
            emailRequest.recipients shouldBe Recipients(userIds = Set("sourceUserId"), emails = Set())
            emailRequest.subject shouldBe "User Impersonation Request Approved"
            emailRequest.content shouldBe UserImpersonationStatusUpdateContent(
              impersonatedUserId = mockImpersonatedUser.id,
              impersonatedUserName = mockImpersonatedUser.fullName,
              networkId = mockImpersonatedUser.networkId,
              approverUserName = "approver2 user",
              networkName = mockNetwork.name,
              reason = mockUserImpersonation.reason,
              status = ImpersonationStatus.Approved
            ).asJson
        }
      }

      "reject a request if a user is an authorized approver and the request is submitted" in new ApproveFixture {
        when(mockUserACL.userId).thenReturn("approverUserId2")
        when(mockUserImpersonation.id).thenReturn("pendingImpersonationId")
        when(mockUserImpersonation.status).thenReturn(ImpersonationStatus.Submitted)
        when(mockUserImpersonation.approversUserIds).thenReturn(Some(Set("approverUserId2")))
        var updateStatusCount: Int = 0
        when(userImpersonationRepo.approveOrRejectStatus(any[String], any[ImpersonationStatus], any[String])(any[Set[String]])(any[TraceId]))
          .thenAnswer(answer(new Answer3[Future[Option[UserImpersonation]], String, ImpersonationStatus, String] {
            override def answer(impersonationId: String, toStatus: ImpersonationStatus,
                approverUserId: String): Future[Option[UserImpersonation]] = {
              impersonationId shouldBe mockUserImpersonation.id
              toStatus shouldBe ImpersonationStatus.Rejected
              approverUserId shouldBe "approverUserId2"
              updateStatusCount += 1
              Future.successful(Some(mockUserImpersonation))
            }
          }))
        val request: ApproveUserImpersonationRequest = ApproveUserImpersonationRequest(ImpersonationStatus.Rejected, "pendingImpersonationId")
        val result: UserImpersonation = userImpersonationService.approve(request).await

        result shouldBe mockUserImpersonation
        updateStatusCount shouldBe 1

        emailCapture.lastEmail match {
          case None => fail("email request was never sent")
          case Some(emailRequest) =>
            emailRequest.eventId shouldBe result.id
            emailRequest.eventType shouldBe TemplateId.`user-impersonation-status-update`.productPrefix
            emailRequest.templateId shouldBe TemplateId.`user-impersonation-status-update`
            emailRequest.recipients shouldBe Recipients(userIds = Set("sourceUserId"), emails = Set())
            emailRequest.subject shouldBe "User Impersonation Request Rejected"
            emailRequest.content shouldBe UserImpersonationStatusUpdateContent(
              impersonatedUserId = mockImpersonatedUser.id,
              impersonatedUserName = mockImpersonatedUser.fullName,
              networkId = mockImpersonatedUser.networkId,
              approverUserName = "approver2 user",
              networkName = mockNetwork.name,
              reason = mockUserImpersonation.reason,
              status = ImpersonationStatus.Rejected
            ).asJson
        }
      }

      "return a request unmodified if a user is an authorized approver and the request is pending" in new ApproveFixture {
        when(mockUserACL.userId).thenReturn("approverUserId2")
        when(mockUserImpersonation.id).thenReturn("pendingImpersonationId")
        when(mockUserImpersonation.status).thenReturn(ImpersonationStatus.Pending)
        when(mockUserImpersonation.approversUserIds).thenReturn(Some(Set("approverUserId2")))
        var updateStatusCount: Int = 0
        when(userImpersonationRepo.updateStatus(any[String], any[ImpersonationStatus], any[ImpersonationStatus])(any[Set[String]])(any[TraceId]))
          .thenAnswer(answer(new Answer3[Future[Option[UserImpersonation]], String, ImpersonationStatus, ImpersonationStatus] {
            override def answer(impersonationId: String, fromStatus: ImpersonationStatus,
                toStatus: ImpersonationStatus): Future[Option[UserImpersonation]] = {
              updateStatusCount += 1
              Future.successful(Some(mockUserImpersonation))
            }
          }))
        val request: ApproveUserImpersonationRequest = ApproveUserImpersonationRequest(ImpersonationStatus.Approved, "pendingImpersonationId")
        val result: UserImpersonation = userImpersonationService.approve(request).await

        result shouldBe mockUserImpersonation
        updateStatusCount shouldBe 0

        emailCapture.lastEmail shouldBe None
      }

      "return a request unmodified if a user is an authorized approver and the request is approved" in new ApproveFixture {
        when(mockUserACL.userId).thenReturn("approverUserId2")
        when(mockUserImpersonation.id).thenReturn("pendingImpersonationId")
        when(mockUserImpersonation.status).thenReturn(ImpersonationStatus.Approved)
        when(mockUserImpersonation.approversUserIds).thenReturn(Some(Set("approverUserId2")))
        var updateStatusCount: Int = 0
        when(userImpersonationRepo.updateStatus(any[String], any[ImpersonationStatus], any[ImpersonationStatus])(any[Set[String]])(any[TraceId]))
          .thenAnswer(answer(new Answer3[Future[Option[UserImpersonation]], String, ImpersonationStatus, ImpersonationStatus] {
            override def answer(impersonationId: String, fromStatus: ImpersonationStatus,
                toStatus: ImpersonationStatus): Future[Option[UserImpersonation]] = {
              updateStatusCount += 1
              Future.successful(Some(mockUserImpersonation))
            }
          }))
        val request: ApproveUserImpersonationRequest = ApproveUserImpersonationRequest(ImpersonationStatus.Approved, "pendingImpersonationId")
        val result: UserImpersonation = userImpersonationService.approve(request).await

        result shouldBe mockUserImpersonation
        updateStatusCount shouldBe 0

        emailCapture.lastEmail shouldBe None
      }

      "return the request unmodified if the request is already rejected" in new ApproveFixture {
        when(mockUserACL.userId).thenReturn("approverUserId2")
        when(mockUserImpersonation.id).thenReturn("pendingImpersonationId")
        when(mockUserImpersonation.status).thenReturn(ImpersonationStatus.Rejected)
        when(mockUserImpersonation.approversUserIds).thenReturn(Some(Set("approverUserId2")))
        val request: ApproveUserImpersonationRequest = ApproveUserImpersonationRequest(ImpersonationStatus.Approved, "pendingImpersonationId")
        var updateStatusCount: Int = 0
        when(userImpersonationRepo.updateStatus(any[String], any[ImpersonationStatus], any[ImpersonationStatus])(any[Set[String]])(any[TraceId]))
          .thenAnswer(answer(new Answer3[Future[Option[UserImpersonation]], String, ImpersonationStatus, ImpersonationStatus] {
            override def answer(impersonationId: String, fromStatus: ImpersonationStatus,
                toStatus: ImpersonationStatus): Future[Option[UserImpersonation]] = {
              updateStatusCount += 1
              Future.successful(Some(mockUserImpersonation))
            }
          }))
        val result: UserImpersonation = userImpersonationService.approve(request).await

        result shouldBe mockUserImpersonation
        updateStatusCount shouldBe 0

        emailCapture.lastEmail shouldBe None
      }

      "return an error if the update fails to find a document" in new ApproveFixture {
        when(mockUserACL.userId).thenReturn("approverUserId2")
        when(mockUserImpersonation.id).thenReturn("pendingImpersonationId")
        when(mockUserImpersonation.status).thenReturn(ImpersonationStatus.Submitted)
        when(mockUserImpersonation.approversUserIds).thenReturn(Some(Set("approverUserId2")))
        val request: ApproveUserImpersonationRequest = ApproveUserImpersonationRequest(ImpersonationStatus.Approved, "pendingImpersonationId")
        var updateStatusCount: Int = 0
        when(userImpersonationRepo.approveOrRejectStatus(any[String], any[ImpersonationStatus], any[String])(any[Set[String]])(any[TraceId]))
          .thenAnswer(answer(new Answer3[Future[Option[UserImpersonation]], String, ImpersonationStatus, String] {
            override def answer(impersonationId: String, toStatus: ImpersonationStatus,
                approverUserId: String): Future[Option[UserImpersonation]] = {
              impersonationId shouldBe mockUserImpersonation.id
              toStatus shouldBe ImpersonationStatus.Approved
              approverUserId shouldBe "approverUserId2"
              updateStatusCount += 1
              Future.successful(None)
            }
          }))
        an[HttpError] shouldBe thrownBy(userImpersonationService.approve(request).await)
        updateStatusCount shouldBe 1

        emailCapture.lastEmail shouldBe None
      }

      "not approve a request if a user is not an authorized approver" in new ApproveFixture {
        when(mockUserACL.userId).thenReturn("notApproverUserId")
        when(mockUserImpersonation.id).thenReturn("pendingImpersonationId")
        when(mockUserImpersonation.impersonatorUserId).thenReturn("impersonatorUserId")
        when(mockUserImpersonation.status).thenReturn(ImpersonationStatus.Submitted)
        when(mockUserImpersonation.approversUserIds).thenReturn(Some(Set("approverUserId2")))
        when(userImpersonationRepo.approveOrRejectStatus(any[String], any[ImpersonationStatus], any[String])(any[Set[String]])(any[TraceId]))
          .thenAnswer(answer(new Answer3[Future[Option[UserImpersonation]], String, ImpersonationStatus, String] {
            override def answer(impersonationId: String, toStatus: ImpersonationStatus,
                approverUserId: String): Future[Option[UserImpersonation]] = {
              fail("updateStatus should not be called for a non approving user")
            }
          }))
        val request: ApproveUserImpersonationRequest = ApproveUserImpersonationRequest(ImpersonationStatus.Approved, "pendingImpersonationId")

        userImpersonationService.approve(request) assertFailureEx
          HttpError.forbidden(s"You do not have the capability to approve an impersonation request in network: $targetNetId")
      }

      "not approve a request if the user is the impersonatorUser" in new ApproveFixture {
        when(mockUserACL.userId).thenReturn("impersonatorUserId1")
        when(mockUserImpersonation.id).thenReturn("pendingImpersonationId")
        when(mockUserImpersonation.impersonatorUserId).thenReturn("impersonatorUserId1")
        when(mockUserImpersonation.status).thenReturn(ImpersonationStatus.Submitted)
        when(userImpersonationRepo.approveOrRejectStatus(any[String], any[ImpersonationStatus], any[String])(any[Set[String]])(any[TraceId]))
          .thenAnswer(answer(new Answer3[Future[Option[UserImpersonation]], String, ImpersonationStatus, String] {
            override def answer(impersonationId: String, toStatus: ImpersonationStatus,
                approverUserId: String): Future[Option[UserImpersonation]] = {
              fail("updateStatus should not be called for the impersonator user")
            }
          }))
        val request: ApproveUserImpersonationRequest = ApproveUserImpersonationRequest(ImpersonationStatus.Approved, "pendingImpersonationId")

        userImpersonationService.approve(request) assertFailureEx
          HttpError.forbidden(s"You are not allowed to approve your own impersonation request.")
      }

    }

    "forceComplete" should {
      "successfully force complete user impersonation with a status of Pending" in new ForceCompleteFixture {
        when(mockUserACL.capabilities).thenReturn(Set("admin"))
        when(mockUserImpersonation.impersonatorUserId).thenReturn("impersonatorUserId1")
        when(mockUserImpersonation.status).thenReturn(ImpersonationStatus.Pending)
        when(mockUserImpersonation.entitlements).thenReturn(Set("admin", "readOnlyAdmin"))

        private val forceCompleteRequest: ForceCompleteImpersonationRequest = ForceCompleteImpersonationRequest("impersonatorUserId1")
        userImpersonationService.forceComplete(forceCompleteRequest).await shouldBe mockUserImpersonation
      }

      "fail when no user is found" in new InsertFixture {
        when(mockUserACL.capabilities).thenReturn(Set("admin"))
        when(mockUserImpersonation.impersonatorUserId).thenReturn("impersonatorUserId1")
        when(mockUserImpersonation.status).thenReturn(ImpersonationStatus.Pending)
        when(mockUserImpersonation.entitlements).thenReturn(Set("admin", "readOnlyAdmin"))

        an[Exception] shouldBe thrownBy(userImpersonationService.forceComplete(ForceCompleteImpersonationRequest("noImpersonator")).await)
      }

      "fail when user does not have necessary capabilities" in new InsertFixture {
        when(mockUserACL.capabilities).thenReturn(Set("none"))
        when(mockUserImpersonation.impersonatorUserId).thenReturn("impersonatorUserId1")
        when(mockUserImpersonation.status).thenReturn(ImpersonationStatus.Pending)
        when(mockUserImpersonation.entitlements).thenReturn(Set("admin", "readOnlyAdmin"))

        an[Exception] shouldBe thrownBy(userImpersonationService.forceComplete(ForceCompleteImpersonationRequest("impersonatorUserId1")).await)
      }
    }

  }

  private trait ServiceFixture {
    protected implicit val mockUserACL: UserACL = mock[UserACL]
    protected val userImpersonationRepo: MongoUserImpersonationRepository = mock[MongoUserImpersonationRepository]
    protected val userImpersonationApproversRepo: MongoUserImpersonationApproversRepository = mock[MongoUserImpersonationApproversRepository]
    protected val userRepo: MongoUserRepository = mock[MongoUserRepository]
    protected val networkRepo: MongoNetworkRepository = mock[MongoNetworkRepository]
    private val emailSender = mock[EmailSenderClient]
    protected val icnClient: IcnClient = mock[IcnClient]
    protected val mockImpersonatorUser: User = mock[User]
    protected val mockImpersonatedUser: User = mock[User]

    protected val userImpersonationService = new UserImpersonationServiceImpl(
      userImpersonationRepository = userImpersonationRepo,
      userRepository = userRepo,
      networkRepository = networkRepo,
      userImpersonationApproversRepository = userImpersonationApproversRepo,
      emailSenderClient = emailSender,
      icnClient = icnClient,
      excludedNetworks = Set(excludedNetwork)
    )

    protected val mockNetwork: Network = mock[Network]
    protected val userImpersonation: UserImpersonation = UserImpersonation(
      id = "id1",
      impersonatorUserId = mockImpersonatorUser.id,
      impersonatedUserId = mockImpersonatedUser.id,
      impersonatedNetworkId = "networkId2",
      reason = "reason",
      ticketNumber = Some("ticketNumber"),
      status = ImpersonationStatus.Pending,
      createdAt = Instant.now(),
      completedAt = Some(Instant.now()),
      traceId = traceId.toString(),
      approverUserId = None,
      approversUserIds = None,
      entitlements = Set("admin")
    )

    protected val userImpersonationApprovers: UserImpersonationApprovers = UserImpersonationApprovers(id = "id1", networkId = "networkId2", userIds = Set("approverUserId1", "approverUserId2"))
    protected val mockUserImpersonation: UserImpersonation = mock[UserImpersonation]

    protected val icnAuthToken = "none"

    protected class EmailCapture extends Answer1[Future[Unit], EmailRequest] {
      var lastEmail: Option[EmailRequest] = None

      override def answer(emailRequest: EmailRequest): Future[Unit] = {
        lastEmail = Some(emailRequest)
        Future.successful(Unit)
      }
    }

    protected val emailCapture = new EmailCapture()

    when(icnClient.canImpersonate(any[Int], any[String])(any[TraceId]))
      .thenReturn(CanImpersonateResponse(canImpersonate = true).successFuture)
    when(emailSender.sendEmail(any[EmailRequest])(any[TraceId]))
      .thenAnswer(answer(emailCapture))
    when(mockUserACL.capabilities)
      .thenReturn(Set("admin"))
  }

  private trait InsertFixture extends ServiceFixture {
    when(mockUserACL.capabilities)
      .thenReturn(Set("admin"))
    when(mockUserACL.networkId)
      .thenReturn(AdminNetworkId)
    when(mockUserACL.iCapitalUserId)
      .thenReturn(Some("247177"))

    when(mockImpersonatedUser.id)
      .thenReturn("impersonatedUserId1")
    when(mockImpersonatedUser.entitlements)
      .thenReturn(Set("admin"))
    when(mockImpersonatedUser.networkId)
      .thenReturn(NetworkId("networkId2"))
    when(mockImpersonatedUser.idpId)
      .thenReturn(Some("idpId1"))
    when(mockImpersonatedUser.userType)
      .thenReturn(UserType.Human)
    when(mockImpersonatedUser.iCapitalUserId)
      .thenReturn(Some("358288"))

    when(userRepo.getById(any[String])(any[Set[String]])(any[TraceId]))
      .thenReturn(Some(mockImpersonatedUser).successFuture)

    when(userImpersonationRepo.insert(any[UserImpersonation])(any[TraceId]))
      .thenReturn(mockUserImpersonation.successFuture)

    when(userImpersonationApproversRepo.getByNetworkId(any[String])(any[TraceId]))
      .thenReturn(Some(userImpersonationApprovers).successFuture)

    when(icnClient.canImpersonate(any[Int], any[String])(any[TraceId]))
      .thenReturn(CanImpersonateResponse(canImpersonate = true).successFuture)

    protected val insertRequest: InsertUserImpersonationRequest = InsertUserImpersonationRequest(impersonatedUserId = mockImpersonatedUser.id, reason = "", ticketNumber = None)
  }

  private trait InsertApprovalFixture extends ServiceFixture {
    protected val sourceUserId = "sourceUserId"
    protected val targetUserId = "targetUserId"

    when(mockUserACL.capabilities)
      .thenReturn(Set("impersonateUserViaNetworkWithApproval"))
    when(mockUserACL.userId)
      .thenReturn(sourceUserId)
    when(mockUserACL.iCapitalUserId)
      .thenReturn(Some("247177"))
    when(mockUserACL.networkId)
      .thenReturn(NetworkId("sourceNetId"))

    when(mockUserImpersonation.impersonatorUserId)
      .thenReturn(sourceUserId)
    when(mockUserImpersonation.impersonatedUserId)
      .thenReturn(targetUserId)
    when(mockUserImpersonation.status)
      .thenReturn(ImpersonationStatus.Pending)

    when(mockImpersonatedUser.entitlements)
      .thenReturn(Set("admin", "impersonateUserViaNetworkWithApproval:sourceNetId"))
    when(mockImpersonatedUser.networkId)
      .thenReturn(NetworkId("targetNetId"))
    when(mockImpersonatedUser.idpId)
      .thenReturn(Some("idpId1"))
    when(mockImpersonatedUser.iCapitalUserId)
      .thenReturn(Some("38288"))
    when(mockImpersonatedUser.userType)
      .thenReturn(UserType.Human)
    when(mockImpersonatedUser.id)
      .thenReturn(targetUserId)
    when(mockImpersonatedUser.firstName)
      .thenReturn("target")
    when(mockImpersonatedUser.lastName)
      .thenReturn("user")

    when(mockNetwork.name)
      .thenReturn("target network")

    when(mockImpersonatorUser.id)
      .thenReturn(sourceUserId)
    when(mockImpersonatorUser.firstName)
      .thenReturn("source")
    when(mockImpersonatorUser.lastName)
      .thenReturn("user")

    when(userRepo.getById(eqTo(sourceUserId))(any[Set[String]])(any[TraceId]))
      .thenReturn(Some(mockImpersonatorUser).successFuture)
    when(userRepo.getById(eqTo(targetUserId))(any[Set[String]])(any[TraceId]))
      .thenReturn(Some(mockImpersonatedUser).successFuture)

    when(userImpersonationApproversRepo.getByNetworkId(eqTo("targetNetId"))(any[TraceId]))
      .thenReturn(Some(userImpersonationApprovers).successFuture)

    when(userImpersonationRepo.getByImpersonatorUserIdAndImpersonatedUserId(eqTo(sourceUserId), eqTo(targetUserId))(any[Set[String]])(any[TraceId]))
      .thenReturn(Some(mockUserImpersonation).successFuture)
    when(userImpersonationRepo.insert(any[UserImpersonation])(any[TraceId]))
      .thenAnswer(answer(new Answer1[Future[UserImpersonation], UserImpersonation] {
        override def answer(impersonation: UserImpersonation): Future[UserImpersonation] = {
          Future.successful(impersonation)
        }
      }))

    when(networkRepo.getById(eqTo(NetworkId("targetNetId")))(any[Set[String]]))
      .thenReturn(Some(mockNetwork).successFuture)
  }

  private trait ApproveFixture extends ServiceFixture {
    private val sourceUserId = "sourceUserId"
    private val targetUserId = "targetUserId"
    protected val targetNetId: Id.NetworkId.Type = NetworkId("targetNetId")
    private val approverUserId1 = "approverUserId1"
    private val approverUserId2 = "approverUserId2"
    private val mockApproverUser2: User = mock[User]
    private val approvers = UserImpersonationApprovers("approversId", targetNetId.toString, Set(approverUserId1, approverUserId2))

    when(mockUserACL.capabilities)
      .thenReturn(Set("impersonateUserViaNetwork"))

    when(mockUserImpersonation.impersonatorUserId)
      .thenReturn(sourceUserId)
    when(mockUserImpersonation.impersonatedUserId)
      .thenReturn(targetUserId)

    when(mockImpersonatedUser.networkId)
      .thenReturn(targetNetId)

    when(mockApproverUser2.firstName)
      .thenReturn("approver2")
    when(mockApproverUser2.lastName)
      .thenReturn("user")

    when(networkRepo.getById(eqTo(targetNetId))(any[Set[String]]))
      .thenReturn(Some(mockNetwork).successFuture)

    when(mockNetwork.name)
      .thenReturn("target network")
    when(mockNetwork.id)
      .thenReturn(targetNetId)

    when(userImpersonationRepo.getById(any[String])(any[Set[String]])(any[TraceId]))
      .thenReturn(Some(mockUserImpersonation).successFuture)

    when(userRepo.getById(eqTo(targetUserId))(any[Set[String]])(any[TraceId]))
      .thenReturn(Some(mockImpersonatedUser).successFuture)
    when(userRepo.getById(eqTo(approverUserId2))(any[Set[String]])(any[TraceId]))
      .thenReturn(Some(mockApproverUser2).successFuture)

    when(userImpersonationApproversRepo.getByNetworkId(any[String])(any[TraceId]))
      .thenReturn(Some(approvers).successFuture)
  }

  private trait ForceCompleteFixture extends ServiceFixture {
    private val sourceUserId = "sourceUserId"

    when(mockUserACL.capabilities)
      .thenReturn(Set("admin"))

    when(mockUserImpersonation.impersonatorUserId)
      .thenReturn(sourceUserId)

    when(userImpersonationRepo.forceComplete(any[String])(any[Set[String]])(any[TraceId]))
      .thenReturn(Some(mockUserImpersonation).successFuture)
  }

  private trait GetImpersonationStatusFixture extends InsertFixture {
    when(userRepo.getById(any[String])(any[Set[String]])(any[TraceId]))
      .thenReturn(Some(mockImpersonatedUser).successFuture)

    when(userImpersonationRepo.getByImpersonatorUserIdAndImpersonatedUserId(any[String], any[String])(any[Set[String]])(any[TraceId]))
      .thenReturn(Some(mockUserImpersonation).successFuture)

    when(mockUserImpersonation.status).thenReturn(ImpersonationStatus.Pending)
  }
}
