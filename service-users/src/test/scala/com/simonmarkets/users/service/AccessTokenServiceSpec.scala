package com.simonmarkets.users.service

import com.goldmansachs.marquee.pipg.UserACL
import com.okta.sdk.resource.model.{UserProfile, User => OktaSDKUser}
import com.simonmarkets.api.users.HttpACLClient
import com.simonmarkets.capabilities.EndpointScopes
import com.simonmarkets.capabilities.EndpointScopes.FaScope
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.networks.common.service.networkservice.BasicNetworkService
import com.simonmarkets.okta.domain.{OktaId, SimonId}
import com.simonmarkets.okta.service.OktaService
import com.simonmarkets.syntax.anyOpsConversion
import com.simonmarkets.users.api.request._
import com.simonmarkets.users.common.User.DistributorKey
import com.simonmarkets.users.common.api.request.UpsertUserRequest
import com.simonmarkets.users.common.responses.ServiceEntitlements
import com.simonmarkets.users.common.{IdType, User, UserDomainEvent}
import com.simonmarkets.users.domain._
import com.simonmarkets.users.{ExternalUsersClient, TestNetwork, TestUser}
import org.mockito.ArgumentMatchers.{any, eq => meq}
import org.mockito.Mockito.{reset, verifyZeroInteractions, when}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}
import simon.Id
import simon.Id.NetworkId
import zio.{IO, Runtime, Unsafe, ZIO}

import java.time.Instant
import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.concurrent.{ExecutionContext, Future}

class AccessTokenServiceSpec extends WordSpec with Matchers with MockitoSugar with BeforeAndAfterEach {

  val serviceUser: UserACL = mock[UserACL]
  val aclClient: HttpACLClient = mock[HttpACLClient]
  val oktaService: OktaService = mock[OktaService]
  val userService: UserServiceImpl = mock[UserServiceImpl]
  val oktaSdkUser: OktaSDKUser = mock[OktaSDKUser]
  val networkService: BasicNetworkService = mock[BasicNetworkService]
  val userImpersonationService: UserImpersonationService = mock[UserImpersonationService]
  val externalUsersClient: ExternalUsersClient = mock[ExternalUsersClient]
  val ssoMapper: SsoMapper = mock[SsoMapper]
  val appExpiryConfigs: Map[String, FiniteDuration] = Map(
    "overrideId1" -> 1.hour,
    "overrideId2" -> 2.hour
  )

  val userProfile: UserProfile = mock[UserProfile]
  val upsertRequest: UpsertUserRequest = mock[UpsertUserRequest]

  val networkId: Id.NetworkId.Type = NetworkId("network")
  val tokenExpiration: FiniteDuration = 14.hours
  val networkTokenExpiration: Map[NetworkId.Type, FiniteDuration] = Map(networkId -> tokenExpiration)

  val userId: String = "userID"
  val service: AccessTokenService = new AccessTokenService.Impl(
    serviceUser = serviceUser,
    cachedAclClient = aclClient,
    networkTokenExpiration,
    oktaService = oktaService,
    userService = userService,
    networkService = networkService,
    userImpersonationService = userImpersonationService,
    externalUsersClient = externalUsersClient,
    ssoMapper = ssoMapper,
    appExpiryConfig = appExpiryConfigs
  )

  val userAcl: UserACL = mock[UserACL]
  val runtime = Runtime.default

  val oktaId: OktaId = OktaId("oktaId")
  val simonId: SimonId = SimonId("simonId")
  val externalNetworkId: ExternalId = ExternalId("subject", "id")
  val network: Network = TestNetwork.apply(networkId.toString, externalIds = Set(externalNetworkId))
  val icnId: Option[String] = "icnId".some
  val wlp: Option[String] = "wlp".some

  implicit val traceId: TraceId = TraceId.randomize
  implicit val ec: ExecutionContext = Unsafe.unsafe { implicit unsafe =>
    runtime.unsafe.run(ZIO.executor).getOrThrow.asExecutionContext
  }

  val request = Request("id", "method", Url("url"), "ipAddress")
  val clientCredentialContext = ClientCredentialContext(request)
  val access = Access(AccessClaims(userId), Token(Lifetime(500L)))
  val clientCredentialData = ClientCredentialData(clientCredentialContext, access)
  val clientCredentialRequest = ClientCredentialInlineHookRequest(
    "source", "eventId", Instant.now, "eventType", clientCredentialData)

  //responses
  private val systemOktaData = SystemUser(
    icnId = None,
    wlp = None,
    scopes = Set(FaScope),
    expiration = None,
    trace = traceId
  )

  private val userOktaDataInsert = HumanUser(
    userId = "insertedUser",
    icnId = "icnId".some,
    wlp = "wlp".some,
    scopes = Set(EndpointScopes.FaScope),
    expiration = tokenExpiration.some,
    trace = traceId,
    impersonatorUserId = None,
    impersonatorIcnId = None
  )

  private val userOktaDataUpdate = userOktaDataInsert.copy(userId = "updatedUser")

  override protected def beforeEach: Unit = {
    reset(
      serviceUser,
      aclClient,
      oktaService,
      userService,
      networkService,
      userImpersonationService,
      externalUsersClient,
      ssoMapper,

      //should not have any mocks set, but to be safe
      userProfile,
      upsertRequest
    )
  }


  implicit class ZRunHelp[E <: Throwable, A](z: IO[E, A]) {
    def zRun: A = {
      Unsafe.unsafe { implicit unsafe =>
        runtime.unsafe.run(z).getOrThrow
      }
    }
  }

  "client credentials enrichment" should {

    when(userAcl.iCapitalUserId).thenReturn(None)
    when(userAcl.whiteLabelPartnerId).thenReturn(None)

    "return scopes and no expiration when user is found but network expiration is not configured " in {
      when(aclClient.getUserACL(userId)(traceId)).thenReturn(Future.successful(userAcl))
      when(userAcl.networkId).thenReturn(NetworkId("networkId2"))
      when(userAcl.capabilities).thenReturn(Set(FaScope))

      service.clientCredentialEnrichment(clientCredentialRequest).zRun shouldBe systemOktaData
    }
    "return scopes and expiration when user is found and network expiration is configured for users network " in {
      when(aclClient.getUserACL(userId)(traceId)).thenReturn(Future.successful(userAcl))
      when(userAcl.networkId).thenReturn(networkId)
      when(userAcl.capabilities).thenReturn(Set(FaScope))

      service.clientCredentialEnrichment(clientCredentialRequest).zRun shouldBe systemOktaData.copy(expiration = tokenExpiration.some)
    }
    "return GeneralNotFound domain error when user is not found" in {
      when(aclClient.getUserACL(userId)(traceId)).thenReturn(Future.failed(HttpError.notFound("not found")))

      a[GeneralNotFound] should be thrownBy service.clientCredentialEnrichment(clientCredentialRequest).zRun
    }
  }

  "admin/test enrichment" should {
    "return scopes and no expiration when user is found but network expiration is not configured " in {
      when(aclClient.getUserACL(userId)(traceId)).thenReturn(Future.successful(userAcl))
      when(userAcl.networkId).thenReturn(NetworkId("networkId2"))
      when(userAcl.capabilities).thenReturn(Set(FaScope))

      service.getScopesAndTokenExpiration(SimonId(userId)).zRun shouldBe systemOktaData

    }
    "return scopes and expiration when user is found and network expiration is configured for users network " in {
      when(aclClient.getUserACL(userId)(traceId)).thenReturn(Future.successful(userAcl))
      when(userAcl.networkId).thenReturn(networkId)
      when(userAcl.capabilities).thenReturn(Set(FaScope))

      service.getScopesAndTokenExpiration(SimonId(userId)).zRun shouldBe
        systemOktaData.copy(expiration = tokenExpiration.some)

    }
    "return GeneralNotFound domain error when user is not found" in {

      when(aclClient.getUserACL(userId)(traceId)).thenReturn(Future.failed(HttpError.notFound("not found")))

      a[GeneralNotFound] should be thrownBy service.getScopesAndTokenExpiration(SimonId(userId)).zRun
    }
  }

  "authorization code enrichment" should {

    "insert user if not exists" in new InsertScope {
      service.authCodeEnrichment(authCodeRequest).zRun shouldBe userOktaDataInsert
    }

    "insert user into external id keyed network" in new InsertScope {
      when(ssoMapper.getSsoNetworkId(userProfile)).thenReturn(Right(None))
      when(ssoMapper.getSsoExternalNetworkId(userProfile)).thenReturn(Right(externalNetworkId.some))
      when(networkService.getNetworkByExternalId(serviceUser, externalNetworkId)).thenReturn(network.successFuture)

      service.authCodeEnrichment(authCodeRequest).zRun shouldBe userOktaDataInsert
    }

    //if this test is failing, you may have added a field to the acl without enabling it to be set via UpsertUserRequest
    "update user if exists and there are sso changes" in new UpdateScope {
      override def modifiedUpsertRequest: UpsertUserRequest = upsertUserRequest.copy(customRoles = Set("newRole").some)

      service.authCodeEnrichment(request).zRun shouldBe userOktaDataUpdate
    }

    "update user if exists and there are identity service changes" in new UpdateScope {
      override def modifiedUpsertRequest: UpsertUserRequest = upsertUserRequest.copy(customRoles = Set("newRoleFromIdService").some)

      private val entitlements = ServiceEntitlements(
        customRoles = Set("newRoleFromIdService"),
        Set.empty,
        Set.empty,
      )
      when(ssoMapper.getSsoClientId(userProfile)).thenReturn(Right(updatedUser.distributorId))
      when(externalUsersClient.getCachedIdentityNetworks).thenReturn(Set(updatedUser.networkId).successFuture)
      when(externalUsersClient.getEntitlements(updatedUser.distributorId.get, IdType.Distributor, updatedUser.networkId))
        .thenReturn(entitlements.successFuture)

      service.authCodeEnrichment(request).zRun shouldBe userOktaDataUpdate
    }

    "update user if exists, is not a JIT network and there are identity service changes" in new UpdateScope {
      override def modifiedUpsertRequest: UpsertUserRequest = upsertUserRequest.copy(customRoles = Set("newRoleFromIdService").some)

      private val entitlements = ServiceEntitlements(
        customRoles = Set("newRoleFromIdService"),
        Set.empty,
        Set.empty,
      )

      when(ssoMapper.getSsoNetworkId(userProfile)).thenReturn(Right(None))
      when(ssoMapper.getSsoExternalNetworkId(userProfile)).thenReturn(Right(None))
      when(ssoMapper.getNetworkId(userProfile)).thenReturn(Right(updatedUser.networkId.some))
      when(ssoMapper.setContext(userProfile, upsertUserRequest)).thenReturn(Right(modifiedUpsertRequest))
      when(externalUsersClient.getCachedIdentityNetworks).thenReturn(Set(updatedUser.networkId).successFuture)
      when(externalUsersClient.getEntitlements(updatedUser.distributorId.get, IdType.Distributor, updatedUser.networkId))
        .thenReturn(entitlements.successFuture)

      service.authCodeEnrichment(request).zRun shouldBe userOktaDataUpdate
    }

    "not update existing user if there are no changes" in new UpdateScope {
      override def modifiedUpsertRequest: UpsertUserRequest = upsertUserRequest

      verifyZeroInteractions(userService)

      service.authCodeEnrichment(request).zRun shouldBe userOktaDataUpdate
    }

    "update user context if there are context changes" in new UpdateScope {
      override def modifiedUpsertRequest: UpsertUserRequest = upsertUserRequest

      reset(ssoMapper)
      when(ssoMapper.getSsoNetworkId(userProfile)).thenReturn(Right(None))
      when(ssoMapper.getSsoExternalNetworkId(userProfile)).thenReturn(Right(None))
      when(ssoMapper.getNetworkId(userProfile)).thenReturn(Right(None))
      when(ssoMapper.setContext(userProfile, upsertUserRequest)).thenReturn(Right(modifiedUpsertRequest))

      service.authCodeEnrichment(request).zRun shouldBe userOktaDataUpdate
    }

    "retain pilot custom roles of any casing on user update" in new UpdateScope {
      override def modifiedUpsertRequest: UpsertUserRequest = upsertUserRequest

      val pilotRoles = Set("pilotRole", "PilotRole", "PILOT")
      private val existingUser = upsertUserRequest.copy(customRoles = Some(pilotRoles))

      private val existingAcl: UserACL = UserACL(updatedUser.copy(customRoles = pilotRoles).asLegacyUser, network.asLegacyNetwork)
      private val upsertIncludingPilotRole = modifiedUpsertRequest.copy(customRoles = Some(upsertUserRequest.customRoles.get ++ pilotRoles))

      reset(userService, aclClient)
      when(ssoMapper.applyAll(existingUser, userProfile)).thenReturn(Right(modifiedUpsertRequest))
      when(aclClient.getUserACL(simonId.id)).thenReturn(existingAcl.successFuture)
      when(userService.handleUpdateRequest(serviceUser, upsertIncludingPilotRole, None))
        .thenReturn(updatedUser.successFuture)

      service.authCodeEnrichment(request).zRun shouldBe userOktaDataUpdate
    }

    "swallow errors from identity service failures" in new UpdateScope {
      override def modifiedUpsertRequest: UpsertUserRequest = upsertUserRequest

      when(ssoMapper.getSsoClientId(userProfile)).thenReturn(Right(updatedUser.distributorId))
      when(externalUsersClient.getCachedIdentityNetworks).thenReturn(Set(updatedUser.networkId).successFuture)
      when(externalUsersClient.getEntitlements(any[String], any[IdType], any[NetworkId])(any[TraceId]))
        .thenReturn(Future.failed(HttpError.notFound("uh-oh")))

      service.authCodeEnrichment(request).zRun shouldBe userOktaDataUpdate
    }

    "fail if cannot decode sso field" in new InsertScope {
      when(ssoMapper.getSsoNetworkId(userProfile)).thenReturn(Left(OktaDecodeError("Could not decode ssoNetwork")))

      an[OktaDecodeError] shouldBe thrownBy(service.authCodeEnrichment(authCodeRequest).zRun)
    }

    "return impersonated data when impersonation session is found" in new ImpersonateScope {
      override def modifiedUpsertRequest: UpsertUserRequest = upsertUserRequest

      service.authCodeEnrichment(request).zRun shouldBe userOktaDataUpdate.copy(
        userId = targetImpersonated,
        impersonatorUserId = impersonator.id.some
      )
    }

    "pass passport scenarios" should {

      "swap user icn id if target domain in passport" in new UpdateScope {
        override def modifiedUpsertRequest: UpsertUserRequest = upsertUserRequest

        override def updatedUser: User = updatedUserDefault.copy(
          passport = Map("abc" -> 123, "def" -> 567)
        )

        service.authCodeEnrichment(request).zRun shouldBe userOktaDataUpdate.copy(icnId = "123".some)
      }

      "not swap if no matching domain in passport" in new UpdateScope {
        override def modifiedUpsertRequest: UpsertUserRequest = upsertUserRequest

        override def updatedUser: User = updatedUserDefault.copy(
          passport = Map("def" -> 567, "ghi" -> 123)
        )

        service.authCodeEnrichment(request).zRun shouldBe userOktaDataUpdate
      }

      "not swap if not on icapitalnetwork domain" in new UpdateScope {
        override def modifiedUpsertRequest: UpsertUserRequest = upsertUserRequest

        override def updatedUser: User = updatedUserDefault.copy(
          passport = Map("abc" -> 123, "def" -> 567)
        )

        override def redirectUri: String = "https://www.simonmarkets.com"

        service.authCodeEnrichment(request).zRun shouldBe userOktaDataUpdate
      }

      "not swap if user is impersonating" in new ImpersonateScope {
        override def modifiedUpsertRequest: UpsertUserRequest = upsertUserRequest

        override def updatedUser: User = updatedUserDefault.copy(
          passport = Map("abc" -> 123, "def" -> 567)
        )

        service.authCodeEnrichment(request).zRun shouldBe userOktaDataUpdate.copy(
          userId = targetImpersonated,
          impersonatorUserId = impersonator.id.some
        )
      }
    }

  }

  "saml2bearer enrichment" should {
    "override expiration" in new InsertScope {
      service.samlBearerEnrichment(samlRequest).zRun shouldBe userOktaDataInsert
    }
    "override expiration if configured for app id" in new InsertScope {
      service.samlBearerEnrichment(overrideExpiryFromConfigSamlRequest).zRun shouldBe userOktaDataInsert.copy(expiration = Some(1.hour))
    }
  }

  //provides default stubs for user insert that are overridden as needed in tests
  private trait InsertScope {

    val insertedUser: User = TestUser
      .apply("insertedUser", networkId = networkId, iCapitalUserId = "icnId".some, whiteLabelPartnerId = "wlp".some)
    val insertedUserAndSecret: (User, Option[String]) = (insertedUser, None)

    val protocol = Protocol(
      ProtocolRequest(Some("test.com")),
      ProtocolClient("id", "name", "type")
    )

    val protocol2 = protocol.copy(client = protocol.client.copy(id = "overrideId1"))

    val samlCodeContext = SamlCodeContext(
      Request("id", "method", Url("url"), "ipAddress"),
      UserInfo(oktaId.id, Instant.now.some, Profile(oktaId.id, "first", "last", "locale", "timeZone")),
      protocol = protocol
    )

    val data = AuthorizationCodeData(
      AuthorizationCodeContext(
        Request("id", "method", Url("url"), "ipAddress"),
        Session("id", "userId", "login", Instant.now, Instant.now, "status", Instant.now.some, Nil, Idp("id", IdpType.FEDERATION), false),
        UserInfo(oktaId.id, None, Profile(oktaId.id, "first", "last", "locale", "timeZone")),
        protocol = protocol
      ),
      Access(AccessClaims("undefined"), Token(Lifetime(500L)))

    )
    val samlData = SamlCodeData(
      context = samlCodeContext,
      Access(AccessClaims("undefined"), Token(Lifetime(500L)))
    )

    val authCodeRequest = AuthorizationCodeInlineHookRequest("source", "eventId", Instant.now, "eventType", data)
    val samlRequest = SamlBearerHookRequest("source", "eventId", Instant.now, "eventType", samlData)
    val overrideExpiryFromConfigSamlRequest = samlRequest
      .copy(data = samlData.copy(context = samlCodeContext.copy(protocol = protocol2)))

    //mocks should be in order of their use in service class
    when(oktaService.getUserByOktaId(oktaId.id)).thenReturn(oktaSdkUser.some.successFuture)
    when(ssoMapper.getSsoNetworkId(userProfile)).thenReturn(Right(networkId.some))
    when(ssoMapper.getSsoExternalNetworkId(userProfile)).thenReturn(Right(None))
    when(ssoMapper.getNetworkId(userProfile)).thenReturn(Right(None))
    when(ssoMapper.applyAll(any[UpsertUserRequest], meq(userProfile))).thenReturn(Right(upsertRequest))
    when(networkService.getNetworkById(serviceUser, networkId)).thenReturn(network.successFuture)
    when(oktaService.getSimonId(oktaSdkUser)).thenReturn(None)
    when(externalUsersClient.getCachedIdentityNetworks).thenReturn(Set.empty[NetworkId].successFuture)
    when(userImpersonationService.getByImpersonatorUserIdThenExpire(any[String], any[FiniteDuration])(any[TraceId], any[UserACL])).thenReturn(None.successFuture)
    when(oktaService.getUserByOktaId(authCodeRequest.data.context.user.id)).thenReturn(Some(oktaSdkUser).successFuture)
    when(oktaSdkUser.getProfile).thenReturn(userProfile)
    when(userService.handleInsertRequest(meq(serviceUser), any[UpsertUserRequest], any[Option[UserDomainEvent]])(any[TraceId]))
      .thenReturn(insertedUserAndSecret.successFuture)
  }

  //provides default stubs for user update that are overridden as needed in tests
  private trait UpdateScope {

    def modifiedUpsertRequest: UpsertUserRequest

    def redirectUri: String = "https://abc.icapitalnetwork.com/some/path"

    val updatedUserDefault: User = TestUser.apply(
      id = "updatedUser",
      networkId = networkId,
      distributorId = Some("distributorId"),
      customRoles = Set("customRole"),
      idpId = Some(oktaId.id),
      iCapitalUserId = icnId,
      whiteLabelPartnerId = wlp
    )

    def updatedUser: User = updatedUserDefault

    def userAclNotMocked = UserACL(updatedUser.asLegacyUser, network.asLegacyNetwork)

    val data = AuthorizationCodeData(
      AuthorizationCodeContext(
        Request("id", "method", Url("url"), "ipAddress"),
        Session("id", "userId", "login", Instant.now, Instant.now, "status", Instant.now.some, Nil, Idp("id", IdpType.FEDERATION), false),
        UserInfo(oktaId.id, Instant.now.some, Profile(oktaId.id, "first", "last", "locale", "timeZone")),
        protocol = Protocol(
          ProtocolRequest(
            redirect_uri = Some(redirectUri)
          ),
          ProtocolClient("id", "name", "type")
        )
      ),
      Access(AccessClaims(oktaId.id), Token(Lifetime(500L)))
    )

    val request = AuthorizationCodeInlineHookRequest("source", "eventId", Instant.now, "eventType", data)

    val upsertUserRequest = UpsertUserRequest(
      networkId = updatedUser.networkId,
      email = updatedUser.email,
      firstName = updatedUser.firstName,
      lastName = updatedUser.lastName,
      externalIds = ExternalId.toStringMap(updatedUser.externalIds) + (DistributorKey -> "distributorId"),
      tradewebEligible = updatedUser.tradewebEligible.some,
      regSEligible = updatedUser.regSEligible.some,
      roles = updatedUser.roles,
      customRoles = Some(updatedUser.customRoles),
      isActive = Some(true),
      id = updatedUser.id.some,
      iCapitalUserId = updatedUser.iCapitalUserId,
      whiteLabelPartnerId = updatedUser.whiteLabelPartnerId
    )

    //mocks should be in order of their use in service class
    when(oktaService.getUserByOktaId(oktaId.id)).thenReturn(oktaSdkUser.some.successFuture)
    when(ssoMapper.getSsoNetworkId(userProfile)).thenReturn(Right(networkId.some))
    when(ssoMapper.getSsoExternalNetworkId(userProfile)).thenReturn(Right(None))
    when(ssoMapper.getNetworkId(userProfile)).thenReturn(Right(networkId.some))
    when(oktaService.getSimonId(oktaSdkUser)).thenReturn(simonId.id.some)
    when(aclClient.getUserACL(simonId.id)).thenReturn(userAclNotMocked.successFuture)
    when(ssoMapper.applyAll(upsertUserRequest, userProfile)).thenReturn(Right(modifiedUpsertRequest))
    when(userImpersonationService.getByImpersonatorUserIdThenExpire(any[String], any[FiniteDuration])(any[TraceId], any[UserACL])).thenReturn(None.successFuture)
    when(networkService.getNetworkById(serviceUser, networkId)).thenReturn(network.successFuture)
    when(oktaService.getUserByOktaId(request.data.context.user.id)).thenReturn(Some(oktaSdkUser).successFuture)
    when(externalUsersClient.getCachedIdentityNetworks).thenReturn(Set.empty[NetworkId].successFuture)
    when(userService.handleUpdateRequest(serviceUser, modifiedUpsertRequest, None))
      .thenReturn(updatedUser.successFuture)
  }

  private trait ImpersonateScope extends UpdateScope {

    def impersonator: SimonId = simonId

    def targetImpersonated: String = updatedUser.id

    private val targetUser = TestUser.apply(
      id = targetImpersonated,
      networkId = networkId,
      iCapitalUserId = icnId,
      whiteLabelPartnerId = wlp
    )

    private val impersonatedUserDocument: UserImpersonation = UserImpersonation(
      id = "id",
      impersonatorUserId = impersonator.id,
      impersonatedUserId = targetImpersonated,
      impersonatedNetworkId = "networkId",
      reason = "impersonation session unit test",
      ticketNumber = None,
      status = ImpersonationStatus.Pending,
      createdAt = Instant.now,
      completedAt = None,
      traceId = traceId.traceId,
      approverUserId = None,
      approversUserIds = None,
      entitlements = Set.empty
    )

    private val targetAcl = UserACL(targetUser.asLegacyUser, network.asLegacyNetwork)

    when(userImpersonationService.getByImpersonatorUserIdThenExpire(impersonator.id)(traceId, serviceUser))
      .thenReturn(impersonatedUserDocument.some.successFuture)
    when(oktaService.getUserBySimonId(targetImpersonated)).thenReturn(oktaSdkUser.some.successFuture)
    when(aclClient.getUserACL(targetImpersonated)).thenReturn(targetAcl.successFuture)

  }

}
