package com.simonmarkets.users.api

import com.simonmarkets.users.api.codec.AccessTokenCodecs
import com.simonmarkets.users.api.response._
import org.scalatest.{Matchers, WordSpec}

import scala.concurrent.duration.DurationInt

class InlineHookResponseSpec extends WordSpec with Matchers with JsonAssertions with AccessTokenCodecs {

  private val addScope = ReplaceScopes(Set("fa"))
  private val addSubject = AddSubject("subject")
  private val updateTokenExpiration = UpdateTokenExpiration(10000.seconds)
  private val addTrace = AddTrace("traceId")
  private val addCreatedAction = AddAction(Action.created)
  private val addUpdatedAction = AddAction(Action.updated)
  private val addNoChangeAction = AddAction(Action.`no-change`)
  private val patchAccessTokenWithCreatedAction = PatchAccessToken(
    List(addScope, addSubject, addTrace, addCreatedAction, updateTokenExpiration ))
  private val patchAccessTokenWithUpdatedAction = PatchAccessToken(
    List(addScope, addSubject, addTrace, addUpdatedAction, updateTokenExpiration ))
  private val patchAccessTokenWithNoChangeAction = PatchAccessToken(
    List(addScope, addSubject, addTrace, addNoChangeAction, updateTokenExpiration ))

  val inlineHookResponseJsonForCreateAction =
    """{
      |  "commands" : [
      |    {
      |      "type" : "com.okta.access.patch",
      |      "value" : [
      |        {
      |          "op" : "replace",
      |          "path" : "/claims/scopes",
      |          "value" : [
      |            "fa"
      |          ]
      |        },
      |        {
      |          "op" : "add",
      |          "path" : "/claims/sub",
      |          "value" : "subject"
      |        },
      |        {
      |          "op" : "add",
      |          "path" : "/claims/trace",
      |          "value" : "traceId"
      |        },
      |        {
      |          "op" : "add",
      |          "path" : "/claims/result",
      |          "value" : "created"
      |        },
      |        {
      |          "op" : "replace",
      |          "path" : "/token/lifetime/expiration",
      |          "value" : 10000
      |        }
      |      ]
      |    }
      |  ]
      |}""".stripMargin

  val inlineHookResponseJsonForUpdateAction =
    """{
      |  "commands" : [
      |    {
      |      "type" : "com.okta.access.patch",
      |      "value" : [
      |        {
      |          "op" : "replace",
      |          "path" : "/claims/scopes",
      |          "value" : [
      |            "fa"
      |          ]
      |        },
      |        {
      |          "op" : "add",
      |          "path" : "/claims/sub",
      |          "value" : "subject"
      |        },
      |        {
      |          "op" : "add",
      |          "path" : "/claims/trace",
      |          "value" : "traceId"
      |        },
      |        {
      |          "op" : "add",
      |          "path" : "/claims/result",
      |          "value" : "updated"
      |        },
      |        {
      |          "op" : "replace",
      |          "path" : "/token/lifetime/expiration",
      |          "value" : 10000
      |        }
      |      ]
      |    }
      |  ]
      |}""".stripMargin

  val inlineHookResponseJsonForNoChangeAction =
    """{
      |  "commands" : [
      |    {
      |      "type" : "com.okta.access.patch",
      |      "value" : [
      |        {
      |          "op" : "replace",
      |          "path" : "/claims/scopes",
      |          "value" : [
      |            "fa"
      |          ]
      |        },
      |        {
      |          "op" : "add",
      |          "path" : "/claims/sub",
      |          "value" : "subject"
      |        },
      |        {
      |          "op" : "add",
      |          "path" : "/claims/trace",
      |          "value" : "traceId"
      |        },
      |        {
      |          "op" : "add",
      |          "path" : "/claims/result",
      |          "value" : "no-change"
      |        },
      |        {
      |          "op" : "replace",
      |          "path" : "/token/lifetime/expiration",
      |          "value" : 10000
      |        }
      |      ]
      |    }
      |  ]
      |}""".stripMargin

  "ProcessAccessToken" should {
    "be encoded to JSON with created action " in {
      val json = InlineHookResponse(List(patchAccessTokenWithCreatedAction)).asJson.toString
      assertJsonStringsAreEqual(json, inlineHookResponseJsonForCreateAction)
    }
    "be encoded to JSON with updated action" in {
      val json = InlineHookResponse(List(patchAccessTokenWithUpdatedAction)).asJson.toString
      assertJsonStringsAreEqual(json, inlineHookResponseJsonForUpdateAction)
    }
    "be encoded to JSON with no change action" in {
      val json = InlineHookResponse(List(patchAccessTokenWithNoChangeAction)).asJson.toString
      assertJsonStringsAreEqual(json, inlineHookResponseJsonForNoChangeAction)
    }
  }
}
