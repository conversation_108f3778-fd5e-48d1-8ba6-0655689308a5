package com.simonmarkets.users.api.routes

import akka.http.scaladsl.model.StatusCodes
import akka.http.scaladsl.model.Uri.Host
import akka.http.scaladsl.model.headers.{HttpCookie, `Set-Cookie`, `X-Forwarded-Host`}
import akka.http.scaladsl.server.AuthenticationFailedRejection
import akka.http.scaladsl.server.AuthenticationFailedRejection.CredentialsRejected
import akka.http.scaladsl.testkit.ScalatestRouteTest
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.logging.TraceId
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.resteasy.authn.User
import com.simonmarkets.resteasy.authn.akkahttp.DecodedToken.DecodedUser
import com.simonmarkets.resteasy.authn.akkahttp.{DecodedToken, providers}
import com.simonmarkets.resteasy.requests.AuthorizedDirectives
import com.simonmarkets.users.api.request.{PassportConfirmation, PassportVerificationRequest}
import com.simonmarkets.users.service.PassportService
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}

import scala.concurrent.Future

class PassportRoutesSpec
  extends WordSpec
    with Matchers
    with ScalatestRouteTest
    with MockitoSugar
    with DirectivesWithCirce
    with JsonCodecs {

  //  mocks
  private val service = mock[PassportService]
  private val authDirective = mock[AuthorizedDirectives[UserACL]]
  private val acl = mock[UserACL]

  //data
  private implicit val traceId: TraceId = TraceId.randomize
  private val jti = "jti"
  private val host = "host"
  private val authToken = "authToken"
  private val goodRouteUser = User(
    userId = "",
    token = authToken,
    jti = Some(jti),
    scopes = Seq.empty
  )
  private val verifyRequest = PassportVerificationRequest(Set(1, 2))
  private val confirmRequest = PassportConfirmation(code = "code")
  private val cookie = HttpCookie("test_name", "test_val")

  private val routes = PassportRoutes(service, authDirective).routes
  private val testAttribute = addAttribute(DecodedToken.tokenAttribute, DecodedUser(goodRouteUser))


  "PassportRoutes" should {

    when(authDirective.authorized(any[String])) thenReturn tprovide((traceId, acl))
    when(service.initializeVerification(acl, verifyRequest, authToken, jti)) thenReturn Future.successful(Some(cookie))
    when(service.confirm(acl, confirmRequest, jti)) thenReturn Future.successful(Map.empty[String, Int])
    when(service.removePassport(acl)) thenReturn Future.successful(Map.empty[String, Int])

    "post generate verification" in {
      Post("/simon/api/v2/users/passport/initialize", verifyRequest) ~> testAttribute ~> routes ~> check {
        status shouldEqual StatusCodes.OK
        header[`Set-Cookie`] shouldEqual Some(`Set-Cookie`(cookie))
      }
    }

    "post confirm verification" in {
      Post("/simon/api/v2/users/passport/confirm", confirmRequest)
        .addHeader(`X-Forwarded-Host`(Host(host))) ~> testAttribute ~> routes ~> check {
        status shouldEqual StatusCodes.OK
      }
    }

    "delete" in {
      Delete("/simon/api/v2/users/passport") ~> testAttribute ~> routes ~> check {
        status shouldEqual StatusCodes.OK
      }
    }

    "fail missing jti" in {
      val badRouteUser = goodRouteUser.copy(jti = None)
      val badAttribute = addAttribute(DecodedToken.tokenAttribute, DecodedUser(badRouteUser))
      Post("/simon/api/v2/users/passport/initialize", verifyRequest) ~> badAttribute ~> routes ~> check {
        rejections should contain(AuthenticationFailedRejection(CredentialsRejected, providers.challenge))
      }
    }

    "fail if token includes impersonator claim" in {
      val badRouteUser = goodRouteUser.copy(impersonatorUserId = Some("id"))
      val badAttribute = addAttribute(DecodedToken.tokenAttribute, DecodedUser(badRouteUser))
      Post("/simon/api/v2/users/passport/initialize", verifyRequest) ~> badAttribute ~> routes ~> check {
        rejections should contain(AuthenticationFailedRejection(CredentialsRejected, providers.challenge))
      }
    }
  }
}
