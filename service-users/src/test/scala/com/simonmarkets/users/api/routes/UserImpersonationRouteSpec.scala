package com.simonmarkets.users.api.routes

import akka.http.scaladsl.model.{ContentTypes, StatusCodes}
import akka.http.scaladsl.server.Route
import akka.http.scaladsl.testkit.ScalatestRouteTest
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.logging.TraceId
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.resteasy.authn.User
import com.simonmarkets.useracl.UserAclAuthorizedDirective
import com.simonmarkets.users.api.request.{ForceCompleteImpersonationRequest, InsertUserImpersonationRequest, UpsertUserImpersonationApproversRequest}
import com.simonmarkets.users.domain.{ImpersonationStatus, UserImpersonation, UserImpersonationApprovers}
import com.simonmarkets.users.service.{UserImpersonationApproversService, UserImpersonationService}
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterAll, Matchers, WordSpec, WordSpecLike}

import java.time.Instant

import scala.concurrent.Future

class UserImpersonationRouteSpec extends WordSpec with Matchers with ScalatestRouteTest with MockitoSugar with DirectivesWithCirce with WordSpecLike with BeforeAndAfterAll with JsonCodecs {

  implicit val mockTraceId: TraceId = mock[TraceId]
  implicit val mockUserACL: UserACL = mock[UserACL]
  implicit val mockUser: User = mock[User]

  private val mockUserImpersonationService: UserImpersonationService = mock[UserImpersonationService]
  private val mockUserImpersonationApproversService: UserImpersonationApproversService = mock[UserImpersonationApproversService]
  private val mockUserAclAuthorizedDirective: UserAclAuthorizedDirective = mock[UserAclAuthorizedDirective]
  private val routes: Route = Route.seal(UserImpersonationRoutes(mockUserImpersonationService, mockUserImpersonationApproversService, mockUserAclAuthorizedDirective).routes)

  private val userImpersonation: UserImpersonation = UserImpersonation(
    id = "id1",
    impersonatorUserId = "userId2",
    impersonatedUserId = "userId3",
    impersonatedNetworkId = "networkId4",
    status = ImpersonationStatus.Pending,
    reason = "reason",
    ticketNumber = None,
    createdAt = Instant.now(),
    completedAt = None,
    traceId = TraceId.randomize.toString(),
    approverUserId = None,
    approversUserIds = None,
    entitlements = Set.empty
  )

  private val userImpersonationApprovers: UserImpersonationApprovers = UserImpersonationApprovers(
    id = "id1",
    networkId = "networkId1",
    userIds = Set("userId4", "userId5")
  )

  private val icnAuthToken: String = "none"

  "UserImpersonationRoutesSpec" should {
    when(mockUserAclAuthorizedDirective.authorized(any[String])) thenReturn tprovide((mockTraceId, mockUserACL))
    when(mockUserAclAuthorizedDirective.authorizedUser(any[String])) thenReturn tprovide((mockTraceId, mockUser, mockUserACL))

    "GET a single UserImpersonation" in {
      when(mockUserImpersonationService.getById(userImpersonation.id)) thenReturn Future.successful(Some(userImpersonation))

      Get(s"/simon/api/v2/users/impersonate/${userImpersonation.id}") ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
        entityAs[String] shouldEqual Some(userImpersonation).asJson.deepDropNullValues.asJsonStr
      }
    }

    "POST a new UserImpersonation without a ticket number" in {
      when(mockUser.token).thenReturn(icnAuthToken)
      when(mockUserImpersonationService.insert(InsertUserImpersonationRequest(userImpersonation.impersonatedUserId, userImpersonation.reason, userImpersonation.ticketNumber), icnAuthToken)) thenReturn Future.successful(userImpersonation)

      val req = InsertUserImpersonationRequest(userImpersonation.impersonatedUserId, userImpersonation.reason, userImpersonation.ticketNumber)
      Post(s"/simon/api/v2/users/impersonate", req) ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
        entityAs[String] shouldEqual userImpersonation.asJson.deepDropNullValues.asJsonStr
      }
    }

    "POST a new UserImpersonation with a ticket number" in {
      val userImpersonationEntity: UserImpersonation = UserImpersonation(id = "id4", impersonatorUserId = "userId5", impersonatedUserId = "userId6", impersonatedNetworkId = "networkId7", status = ImpersonationStatus.Pending, reason = "reason", ticketNumber = Some("CM-4884"), createdAt = Instant.now(), completedAt = None, traceId = TraceId.randomize.toString(), approverUserId = None, approversUserIds = None, entitlements = Set.empty)
      when(mockUser.token).thenReturn(icnAuthToken)
      when(mockUserImpersonationService.insert(InsertUserImpersonationRequest(userImpersonationEntity.impersonatedUserId, userImpersonationEntity.reason, userImpersonationEntity.ticketNumber), icnAuthToken)) thenReturn Future.successful(userImpersonation)

      val req = InsertUserImpersonationRequest(userImpersonationEntity.impersonatedUserId, userImpersonationEntity.reason, userImpersonationEntity.ticketNumber)
      Post(s"/simon/api/v2/users/impersonate", req) ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
        entityAs[String] shouldEqual userImpersonation.asJson.deepDropNullValues.asJsonStr
      }
    }

    "POST complete a UserImpersonation" in {
      when(mockUserImpersonationService.complete(mockUser.impersonatorUserId)) thenReturn Future.successful(userImpersonation)

      Post(s"/simon/api/v2/users/impersonate/complete") ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
        entityAs[String] shouldEqual userImpersonation.asJson.deepDropNullValues.asJsonStr
      }
    }

    "POST force-complete a UserImpersonation" in {
      val req = ForceCompleteImpersonationRequest(userImpersonation.impersonatorUserId)
      when(mockUserImpersonationService.forceComplete(req)) thenReturn Future.successful(userImpersonation)

      Post(s"/simon/api/v2/users/impersonate/force-complete", req) ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
        entityAs[String] shouldEqual userImpersonation.asJson.deepDropNullValues.asJsonStr
      }
    }

    "GET a single UserImpersonationApprovers" in {
      when(mockUserImpersonationApproversService.getByNetworkId(userImpersonationApprovers.networkId)) thenReturn Future.successful(Some(userImpersonationApprovers))

      Get(s"/simon/api/v2/users/impersonate/approvers/${userImpersonationApprovers.networkId}") ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
        entityAs[String] shouldEqual Some(userImpersonationApprovers).asJson.deepDropNullValues.asJsonStr
      }
    }

    "PUT insert or update a UserImpersonationApprovers" in {
      val req: UpsertUserImpersonationApproversRequest = UpsertUserImpersonationApproversRequest(userImpersonationApprovers.networkId, userImpersonationApprovers.userIds)
      when(mockUserImpersonationApproversService.upsertUserImpersonationApprovers(req)) thenReturn Future.successful(userImpersonationApprovers)

      Put(s"/simon/api/v2/users/impersonate/approvers", req) ~>
        routes ~> check {
        status shouldEqual StatusCodes.OK
        contentType shouldEqual ContentTypes.`application/json`
        entityAs[String] shouldEqual userImpersonationApprovers.asJson.deepDropNullValues.asJsonStr
      }
    }

  }
}