package com.simonmarkets.users.api

import io.circe.syntax.EncoderOps
import org.scalatest.Matchers.convertToAnyShouldWrapper

trait JsonAssertions {
  def assertJsonStringsAreEqual(json1: String, json2: String): Unit = {
    val tree1 = json1.replaceAll("(\\r|\\n)", "").asJson
    val tree2 = json2.replaceAll("(\\r|\\n)", "").asJson
    tree1 shouldBe tree2
    assert(tree1 == tree2)
  }

  def assertJsonStringsDiffer(json1: String, json2: String): Unit = {
    val tree1 = json1.replaceAll("(\\r|\\n)", "").asJson
    val tree2 = json2.replaceAll("(\\r|\\n)", "").asJson
    assert(tree1 != tree2)
  }

}
