
package com.simonmarkets.users.api

import com.simonmarkets.circe.CirceDecoders
import com.simonmarkets.users.api.request._
import com.simonmarkets.users.decodeFileUnsafe
import io.circe.generic.auto._
import org.scalatest.{Matchers, WordSpec}

import java.time.Instant


class InlineHookRequestSpec extends WordSpec with Matchers with CirceDecoders {

  "InlineHookRequest" should {

    "be deserialized from client credentials request token inline hook JSON for system users" in {
      val decodedRequest = decodeFileUnsafe[ClientCredentialInlineHookRequest]("clientCredentialsHookRequest.json")
      decodedRequest.source shouldEqual "https://${yourOktaDomain}/oauth2/default/v1/authorize"
      decodedRequest.eventId shouldEqual "3OWo4oo-QQ-rBWfRyTmQYw"
      decodedRequest.eventTime shouldEqual Instant.parse("2019-01-15T23:20:47.000Z")
      decodedRequest.eventType shouldEqual "com.okta.oauth2.tokens.transform"
      decodedRequest.data.context.request.id shouldEqual "XcGNaPSTlRlFeydJ3y0k4gAAD6g"
      decodedRequest.data.context.request.method shouldEqual "POST"
      decodedRequest.data.context.request.url.value shouldEqual "https://simongroup.oktapreview.com/oauth2/ausesty3dmANUL4lH0h7/v1/token"
      decodedRequest.data.context.request.ipAddress shouldEqual "************"
      decodedRequest.data.access.token.lifetime.expiration shouldEqual 86400
      decodedRequest.data.access.claims.sub shouldEqual "0oao10y1aqfsavYmA0h7"
    }

    "be deserialized from authorization code token inline hook request JSON for human users" in {
      val decodedRequest = decodeFileUnsafe[AuthorizationCodeInlineHookRequest]("authorizationCodeHookRequest.json")
      decodedRequest.source shouldEqual "https://${yourOktaDomain}/oauth2/default/v1/authorize"
      decodedRequest.eventId shouldEqual "3OWo4oo-QQ-rBWfRyTmQYw"
      decodedRequest.eventTime shouldEqual Instant.parse("2019-01-15T23:20:47.000Z")
      decodedRequest.eventType shouldEqual "com.okta.oauth2.tokens.transform"
      decodedRequest.data.context.request.id shouldEqual "reqv66CbCaCStGEFc8AdfS0ng"
      decodedRequest.data.context.request.method shouldEqual "GET"
      decodedRequest.data.context.request.url.value shouldEqual "https://${yourOktaDomain}/oauth2/default/v1/authorize?scope=openid+profile+email&response_type=token+id_token&redirect_uri=https%3A%2F%2Fhttpbin.org%2Fget&state=foobareere&nonce=asf&client_id=customClientIdNative"
      decodedRequest.data.context.request.ipAddress shouldEqual "127.0.0.1"
      decodedRequest.data.context.user.id shouldEqual "00uq8tMo3zV0OfJON0g3"
      decodedRequest.data.context.user.passwordChanged.get shouldEqual Instant.parse("2018-09-11T23:19:12.000Z")
      decodedRequest.data.context.user.profile.login shouldEqual "<EMAIL>"
      decodedRequest.data.context.user.profile.firstName shouldEqual "Add-Min"
      decodedRequest.data.context.user.profile.lastName shouldEqual "O'Cloudy Tud"
      decodedRequest.data.context.user.profile.locale shouldEqual "en"
      decodedRequest.data.context.user.profile.timeZone shouldEqual "America/Los_Angeles"
      decodedRequest.data.context.session.login shouldEqual "<EMAIL>"
      decodedRequest.data.context.session.userId shouldEqual "00uq8tMo3zV0OfJON0g3"
      decodedRequest.data.context.session.id shouldEqual "102Qoe7t5PcRnSxr8j3I8I6pA"
      decodedRequest.data.context.session.status shouldEqual "ACTIVE"
      decodedRequest.data.context.session.createdAt shouldEqual Instant.parse("2019-01-15T23:17:09.000Z")
      decodedRequest.data.context.session.expiresAt shouldEqual Instant.parse("2019-01-16T01:20:46.000Z")
      decodedRequest.data.context.session.mfaActive shouldEqual false
      decodedRequest.data.context.session.amr shouldEqual List("PASSWORD")
      decodedRequest.data.context.session.idp.id shouldEqual "00oq6kcVwvrDY2YsS0g3"
      decodedRequest.data.context.session.idp.`type` shouldEqual IdpType.OKTA
      decodedRequest.data.access.token.lifetime.expiration shouldEqual 3600
      decodedRequest.data.access.claims.sub shouldEqual "<EMAIL>"
      decodedRequest.data.context.protocol.request.redirect_uri shouldEqual Some("https://httpbin.org/get")
    }


    "be deserialized from saml bearer code token inline hook request JSON for human users" in {
      val decodedRequest = decodeFileUnsafe[SamlBearerHookRequest]("samlBearerHookRequest.json")
      val expected = SamlBearerHookRequest(
        source = "https://auth.int.simonmarkets.com/oauth2/ausesty3dmANUL4lH0h7/v1/token",
        eventId = "A5_VD0hxQteGx43uGAWpbQ",
        eventTime = Instant.parse("2023-01-23T15:53:06Z"),
        eventType = "com.okta.oauth2.tokens.transform",
        data = SamlCodeData(
          context = SamlCodeContext(
            Request(
              "Y86tYaQogClj7Wom6-U8rAAABTE",
              "POST",
              Url("https://auth.int.simonmarkets.com/oauth2/ausesty3dmANUL4lH0h7/v1/token"),
              "3.20.97.155"
            ),
            user = UserInfo(
              "00u1dbc7kmjE3Bvxh0h8",
              Option(Instant.parse("2022-10-19T18:54:48Z")),
              Profile(
                "<EMAIL>",
                "test",
                "envestnet",
                "en",
                "America/Los_Angeles"
              )
            ),
            protocol = Protocol(
              request = ProtocolRequest(),
              client = ProtocolClient("0oa1ggsd0jae9ZSRT0h8", "Envestnet iframe", "TRUSTED")
            )
          ),
          access = Access(
            AccessClaims("undefined"),
            Token(Lifetime(180))
          )
        )
      )

      decodedRequest shouldBe expected
    }
    "be deserialized from saml bearer code token inline hook request JSON for human users without password changed field" in {
      val decodedRequest = decodeFileUnsafe[SamlBearerHookRequest]("samlBearerHookRequest_withoutPasswordChange.json")
      val expected = SamlBearerHookRequest(
        source = "https://auth.int.simonmarkets.com/oauth2/ausesty3dmANUL4lH0h7/v1/token",
        eventId = "A5_VD0hxQteGx43uGAWpbQ",
        eventTime = Instant.parse("2023-01-23T15:53:06Z"),
        eventType = "com.okta.oauth2.tokens.transform",
        data = SamlCodeData(
          context = SamlCodeContext(
            Request(
              "Y86tYaQogClj7Wom6-U8rAAABTE",
              "POST",
              Url("https://auth.int.simonmarkets.com/oauth2/ausesty3dmANUL4lH0h7/v1/token"),
              "3.20.97.155"
            ),
            user = UserInfo(
              "00u1dbc7kmjE3Bvxh0h8",
              None,
              Profile(
                "<EMAIL>",
                "test",
                "envestnet",
                "en",
                "America/Los_Angeles"
              )
            ),
            protocol = Protocol(
              request = ProtocolRequest(),
              client = ProtocolClient("0oa1ggsd0jae9ZSRT0h8", "Envestnet iframe", "TRUSTED")
            )
          ),
          access = Access(
            AccessClaims("undefined"),
            Token(Lifetime(180))
          )
        )
      )

      decodedRequest shouldBe expected
    }
  }
}
