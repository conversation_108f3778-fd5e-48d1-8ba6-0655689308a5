
package com.simonmarkets.users.api

import com.simonmarkets.circe.CirceDecoders
import com.simonmarkets.mongodb.trigger.event.{MfaOperation, NetworkMfaSyncEvent, NetworkMfaSyncEventDetail, SingleUser, UserSyncEvent}
import com.simonmarkets.users.decodeFileUnsafe
import io.circe.generic.auto._
import org.scalatest.{Matchers, WordSpec}

class SyncEventSpec extends WordSpec with Matchers with CirceDecoders {

  "UserSyncEvent" should {
    "be decoded correctly" in {
      val decodedEvent = decodeFileUnsafe[UserSyncEvent]("UserSyncEvent.json")
      val expected = UserSyncEvent(
        detail = SingleUser(
          userId = "a9142a75-7509-4193-98fc-456e67153ae5"
        )
      )
      decodedEvent shouldBe expected
    }
  }

  "NetworkMfaSyncEvent" should {
    "be decoded correctly" in {
      val decodedEvent = decodeFileUnsafe[NetworkMfaSyncEvent]("NetworkMfaSyncEvent.json")
      val expected = NetworkMfaSyncEvent(
        detail = NetworkMfaSyncEventDetail(
          userId = "a9142a75-7509-4193-98fc-456e67153ae5",
          groupId = "00g1kisw957yzLOEJ0h8",
          operation = MfaOperation.add
        )
      )
      decodedEvent shouldBe expected
    }
  }
}
