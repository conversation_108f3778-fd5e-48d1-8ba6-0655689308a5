package com.simonmarkets.users.api

import com.goldmansachs.marquee.pipg.{EmbeddingInfo, SSOPrefix}
import com.simonmarkets.logging.TraceId
import com.simonmarkets.users.{TestNetwork, TestUser}
import com.simonmarkets.users.common.{LandingPage, LoginMode}
import com.simonmarkets.users.service.UserRef
import org.scalatest.{Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar

class UserRefSpec extends WordSpec with Matchers with MockitoSugar {

  private implicit val traceId = TraceId.randomize

  private val ssoPrefix = SSOPrefix(
    ssoSystemName = Some("SSO System Name"),
    baseUrl = "Base Url",
    redirectionKey = "Redirection Key",
    isRedirectionKeyEncoded = None,
    simonBase = Some("SIMON Base")
  )

  private val embeddingInfo = EmbeddingInfo(
    hostApplicationUrl = "Url",
    hostApplicationName = Some("Name")
  )

  "Default User Ref" should {
    "return login as a concatenation of generated network code and email" in {
      val user = UserRef.default("<EMAIL>", Some(LoginMode.SSOAndICNUsernamePassword), Some(LandingPage.SIMON))
      user.login shouldBe "<EMAIL>"
      user.loginMode shouldBe LoginMode.SSOAndICNUsernamePassword
      user.landingPage shouldBe LandingPage.SIMON
      user.ssoPrefix shouldBe None
      user.embeddingInfo shouldBe None
      user.networkName shouldBe None
    }

    "default the login mode to UsernamePassword when not provided" in {
      val user = UserRef.default("<EMAIL>", None, Some(LandingPage.ICN))
      user.login shouldBe "<EMAIL>"
      user.loginMode shouldBe LoginMode.UsernamePassword
      user.landingPage shouldBe LandingPage.ICN
      user.ssoPrefix shouldBe None
      user.embeddingInfo shouldBe None
      user.networkName shouldBe None
    }

    "default the landing page to SIMON when not provided" in {
      val user = UserRef.default("<EMAIL>", Some(LoginMode.UsernamePassword), None)
      user.login shouldBe "<EMAIL>"
      user.loginMode shouldBe LoginMode.UsernamePassword
      user.landingPage shouldBe LandingPage.SIMON
      user.ssoPrefix shouldBe None
      user.embeddingInfo shouldBe None
      user.networkName shouldBe None
    }
  }

  "UserRef from User and Network" should {
    "ignore networks ssoPrefix if the users loginMode is UsernameAndPassword" in {

      val network = TestNetwork("network1", name = "network1", networkCode = "nc1", ssoPrefix = Some(ssoPrefix))
      val user = TestUser("user1", network.id, email = "email", loginMode = LoginMode.UsernamePassword)

      val userRef = UserRef.fromUserAndNetwork(user, network, setNetworkName = false, Option(LandingPage.SIMON))
      userRef shouldBe UserRef(user.idpLoginId, None, None, None, LoginMode.UsernamePassword, LandingPage.SIMON)
    }

    "ignore networks embeddingInfo if the users loginMode is UsernameAndPassword" in {
      val network = TestNetwork("network1", name = "network1", networkCode = "nc1", embeddingInfo = Some(embeddingInfo))
      val user = TestUser("user1", network.id, email = "email", loginMode = LoginMode.UsernamePassword)

      val userRef = UserRef.fromUserAndNetwork(user, network, setNetworkName = false, None)
      userRef shouldBe UserRef(user.idpLoginId, None, None, None, LoginMode.UsernamePassword, LandingPage.EnumNotFound)

    }
    "set loginMode as UsernameAndPassword if ssoPrefix is not set on a network  even if loginMode on the user is SSO" in {
      val network = TestNetwork("network1", name = "network1", networkCode = "nc1")
      val user = TestUser("user1", network.id, email = "email", loginMode = LoginMode.SSO)

      val userRef = UserRef.fromUserAndNetwork(user, network, setNetworkName = false, Option(LandingPage.ICN))
      userRef shouldBe UserRef(user.idpLoginId, None, None, None, LoginMode.UsernamePassword, LandingPage.ICN)
    }
    "set loginMode as UsernameAndPassword if ssoPrefix is not set on a network  even if loginMode on the user is SSOAndUsernamePassword" in {
      val network = TestNetwork("network1", name = "network1", networkCode = "nc1")
      val user = TestUser("user1", network.id, email = "email", loginMode = LoginMode.SSOAndUsernamePassword)

      val userRef = UserRef.fromUserAndNetwork(user, network, setNetworkName = false, Option(LandingPage.Unified))
      userRef shouldBe UserRef(user.idpLoginId, None, None, None, LoginMode.UsernamePassword, LandingPage.Unified)
    }
    "set loginMode as SSO if ssoPrefix is set on a network and loginMode on the user is SSO" in {
      val network = TestNetwork("network1", name = "network1", networkCode = "nc1", ssoPrefix = Some(ssoPrefix))
      val user = TestUser("user1", network.id, email = "email", loginMode = LoginMode.SSO)

      val userRef = UserRef.fromUserAndNetwork(user, network, setNetworkName = false, Option(LandingPage.ICN))
      userRef shouldBe UserRef(user.idpLoginId, None, Some(ssoPrefix), None, LoginMode.SSO, LandingPage.ICN)
    }
    "set loginMode as SSOAndUsernamePassword if ssoPrefix is set on a network and loginMode on the user is SSOAndUsernamePassword" in {
      val network = TestNetwork("network1", name = "network1", networkCode = "nc1", ssoPrefix = Some(ssoPrefix))
      val user = TestUser("user1", network.id, email = "email", loginMode = LoginMode.SSOAndUsernamePassword)

      val userRef = UserRef.fromUserAndNetwork(user, network, setNetworkName = false, None)
      userRef shouldBe UserRef(user.idpLoginId, None, Some(ssoPrefix), None, LoginMode.SSOAndUsernamePassword, LandingPage.EnumNotFound)
    }
    "set loginMode as UsernameAndPassword if embeddingInfo is not set on a network even if loginMode on the user is Embedded" in {
      val network = TestNetwork("network1", name = "network1", networkCode = "nc1")
      val user = TestUser("user1", network.id, email = "email", loginMode = LoginMode.Embedded)

      val userRef = UserRef.fromUserAndNetwork(user, network, setNetworkName = false, None)
      userRef shouldBe UserRef(user.idpLoginId, None, None, None, LoginMode.UsernamePassword, LandingPage.EnumNotFound)

    }

    "set loginMode as Embedded if embeddingInfo is set on a network and loginMode on the user is Embedded" in {
      val network = TestNetwork("network1", name = "network1", networkCode = "nc1", embeddingInfo = Some(embeddingInfo))
      val user = TestUser("user1", network.id, email = "email", loginMode = LoginMode.Embedded)

      val userRef = UserRef.fromUserAndNetwork(user, network, setNetworkName = false, Some(LandingPage.ICN))
      userRef shouldBe UserRef(user.idpLoginId, None, None, Some(embeddingInfo), LoginMode.Embedded, LandingPage.ICN)

    }
    "set loginMode as ICNUsernamePassword if thats the login mode on the user" in {
      val network = TestNetwork("network1", name = "network1", networkCode = "nc1", embeddingInfo = Some(embeddingInfo))
      val user = TestUser("user1", network.id, email = "email", loginMode = LoginMode.ICNUsernamePassword)

      val userRef = UserRef.fromUserAndNetwork(user, network, setNetworkName = false, Some(LandingPage.Unified))
      userRef shouldBe UserRef(user.idpLoginId, None, None, Some(embeddingInfo), LoginMode.ICNUsernamePassword, LandingPage.Unified)
    }
    "return loginMode as UsernameAndPassword if loginMode on the user is ClientCredentials" in {
      val network = TestNetwork("network1", name = "network1", networkCode = "nc1")
      val user = TestUser("user1", network.id, email = "email", loginMode = LoginMode.ClientCredentials)

      val userRef = UserRef.fromUserAndNetwork(user, network, setNetworkName = false, None)
      userRef shouldBe UserRef(user.idpLoginId, None, None, None, LoginMode.UsernamePassword, LandingPage.EnumNotFound)

    }
  }

}




