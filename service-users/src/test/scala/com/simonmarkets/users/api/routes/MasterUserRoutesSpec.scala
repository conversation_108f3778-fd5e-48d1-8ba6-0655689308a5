package com.simonmarkets.users.api.routes

import akka.http.scaladsl.model.Uri.{Host, Query}
import akka.http.scaladsl.model.headers.`X-Forwarded-Host`
import akka.http.scaladsl.model.{StatusCodes, Uri}
import akka.http.scaladsl.server.{Directive, Directive1}
import akka.http.scaladsl.testkit.ScalatestRouteTest
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.logging.TraceId
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.resteasy.authn.User
import com.simonmarkets.resteasy.requests.AuthorizedDirectives
import com.simonmarkets.users.api.request.MasterUserRequest
import com.simonmarkets.users.api.response.MasterUser
import com.simonmarkets.users.service.MasterUserService
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}

import scala.concurrent.Future

class MasterUserRoutesSpec
  extends WordSpec
    with Matchers
    with ScalatestRouteTest
    with MockitoSugar
    with DirectivesWithCirce
    with JsonCodecs {

  //data
  private implicit val traceId: TraceId = TraceId.randomize
  private val host = "host"
  private val email = "<EMAIL>"
  private val emailLower = "<EMAIL>"
  private val wlpId = Some(0)
  private val requestEmail = MasterUserRequest(
    email,
    None
  )
  private val request = MasterUserRequest(
    email,
    wlpId = wlpId
  )

  //  mocks
  private val service = mock[MasterUserService]
  private val authDirective = new AuthorizedDirectives[UserACL] {
    override def authorized(capabilities: Set[String]): Directive[(TraceId, UserACL)] = ???

    override def authorizedUser(capabilities: Set[String]): Directive[(TraceId, User, UserACL)] = ???

    override def unauthenticated: Directive1[TraceId] = provide(traceId)

    override def authenticated: Directive[(TraceId, User)] = ???
  }
  private val responseM = MasterUser(
    email = email,
    users = Nil
  )


  private val routes = MasterUserRoutes(service, authDirective).routes

  "MasterUserRoutes" should {

    when(service.getMasterUser(emailLower, wlpId, Some(host))).thenReturn(Future.successful(responseM))
    when(service.getMasterUser(emailLower, None, None)).thenReturn(Future.successful(responseM))
    when(service.getMasterUser(emailLower, None, Some(host))).thenReturn(Future.successful(responseM))

    "post master user by email" in {
      Post("/simon/api/v2/master-user", requestEmail)
        .addHeader(`X-Forwarded-Host`(Host(host))) ~> routes ~> check {
        status shouldEqual StatusCodes.OK
      }
    }

    "post master user by email and wlp" in {
      Post("/simon/api/v2/master-user", request)
        .addHeader(`X-Forwarded-Host`(Host(host))) ~> routes ~> check {
        status shouldEqual StatusCodes.OK
      }
    }

    "post master user by email no host header" in {
      Post("/simon/api/v2/master-user", requestEmail) ~> routes ~> check {
        status shouldEqual StatusCodes.OK
      }
    }

    "get master user by email" in {
      val uri = Uri("/simon/api/v2/master-user").withQuery(Query("email" -> email))
      Get(uri)
        .addHeader(`X-Forwarded-Host`(Host(host))) ~> routes ~> check {
        status shouldEqual StatusCodes.OK
      }
    }

    "get master user by email and wlp" in {
      val uri = Uri("/simon/api/v2/master-user")
        .withQuery(
          Query(
            "email" -> email,
            "wlpId" -> wlpId.get.toString
          )
        )
      Get(uri)
        .addHeader(`X-Forwarded-Host`(Host(host))) ~> routes ~> check {
        status shouldEqual StatusCodes.OK
      }
    }

  }
}
