package com.simonmarkets.users

import com.goldmansachs.marquee.pipg.Network.Action
import com.goldmansachs.marquee.pipg._
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.enums.CrmProvider
import com.simonmarkets.networks.enums.CrmProvider.{Redtail, Salesforce}
import com.simonmarkets.networks.common.{Network, ProspectusDeadlineConfig}
import com.simonmarkets.shared.MaskedId
import com.simonmarkets.users.common.{LandingPage, LoginMode}
import simon.Id.NetworkId

object TestNetwork  {
  def apply(
      id: String = "UnitTestNetwork",
      name: String = "UnitTestNetworkName",
      purviews: Option[Set[Purview]] = None,
      approverSet: Map[String, List[List[String]]] = Map.empty,
      ioiApproverSet: Map[String, List[List[String]]] = Map.empty,
      accountMappings: Option[List[String]] = None,
      networkTypes: Option[List[NetworkType]] = None,
      to: Option[List[String]] = None,
      cc: Option[List[String]] = None,
      purviewNetworks: Option[Set[IssuerPurview]] = None,
      salesFeeRuleIds: List[String] = Nil,
      capabilities: Map[String, List[String]] = Map.empty,
      payoffEntitlements: Map[String, Map[String, List[String]]] = Map.empty,
      payoffEntitlementsV2: Map[String, Map[String, Set[Action]]] = Map.empty,
      dynamicRoles: Set[String] = Set.empty,
      distributorAlias: Option[ExternalAlias] = None,
      omsAlias: Option[String] = None,
      version: Int = 0,
      customRolesConfig: Set[CustomRoleDefinition] = Set.empty,
      maskedIds: Set[MaskedId] = Set.empty,
      booksCloseConfig: List[BooksCloseConfig] = List.empty,
      booksCloseCustomConfig: List[BooksCloseConfig] = List.empty,
      booksSendConfig: Option[List[BooksCloseConfig]] = None,
      prospectusDeadlineConfig: Option[List[ProspectusDeadlineConfig]] = None,
      dtccId: Option[String] = None,
      locations: Set[NetworkLocation] = Set.empty,
      entitlements: Set[String] = Set.empty,
      annuityEAppProvider: Option[AnnuityEAppProvider] = None,
      ssoPrefix: Option[SSOPrefix] = None,
      contactInfo: Option[ContactInfo] = None,
      @deprecated("This field has been deprecated in favor of learnTracksV2", "86.0.0")
      learnTracks: Seq[String] = Seq.empty,
      learnTracksV2: Seq[LearnTrack] = Seq.empty,
      learnContent: Seq[String] = Seq.empty,
      endUserShareableContent: Seq[String] = Seq.empty,
      idpId: Option[String] = None,
      siCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
      annuityCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
      definedOutcomeETFCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
      certificationProducts: Option[Seq[String]] = None,
      crmProviders: List[CrmProvider] = List(Redtail,Salesforce),
      networkCode: String = "UnitTestNetworkCode",
      embeddingInfo: Option[EmbeddingInfo] = None,
      altCertificationRequirements: Option[Seq[CertificationAlternativesForProduct]] = None,
      externalIds: Set[ExternalId] = Set.empty,
      landingPage: Option[LandingPage] = None,
      maxSystemUsers: Option[Int] = None
  ): Network = Network(
    NetworkId(id),
    name,
    IdHubOrganization(11, "some org"),
    purviews,
    approverSet,
    ioiApproverSet,
    accountMappings,
    networkTypes,
    to,
    cc,
    purviewNetworks,
    salesFeeRuleIds,
    capabilities,
    payoffEntitlements,
    payoffEntitlementsV2,
    dynamicRoles,
    distributorAlias,
    omsAlias,
    version,
    customRolesConfig,
    maskedIds,
    booksCloseConfig,
    booksCloseCustomConfig,
    booksSendConfig,
    prospectusDeadlineConfig,
    dtccId,
    locations,
    entitlements,
    annuityEAppProvider,
    ssoPrefix,
    contactInfo,
    learnTracksV2,
    learnContent,
    endUserShareableContent,
    idpId,
    siCertificationRequirements = siCertificationRequirements,
    annuityCertificationRequirements = annuityCertificationRequirements,
    definedOutcomeETFCertificationRequirements = definedOutcomeETFCertificationRequirements,
    certificationProducts = certificationProducts,
    group = None,
    networkCode = networkCode,
    crmProviders = crmProviders,
    loginMode = LoginMode.SSOAndUsernamePassword,
    eventInfo = com.simonmarkets.networks.common.EventInfo.Default,
    embeddingInfo = embeddingInfo,
    altCertificationRequirements = altCertificationRequirements,
    externalIds = externalIds,
    landingPage = landingPage,
    maxSystemUsers = maxSystemUsers
  )

  def create(count: Int, entitlements: Set[String] = Set("admin")): List[Network] = {
    (0 until count).toList.map(i => TestNetwork(i.toString, entitlements = entitlements))
  }
}
