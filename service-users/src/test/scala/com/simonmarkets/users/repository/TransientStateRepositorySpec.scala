package com.simonmarkets.users.repository

import com.simonmarkets.logging.TraceId
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.users.domain.{PassportVerification, TransientState}
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.MongoCollection
import org.scalacheck.{Arbitrary, Gen}
import org.scalactic.Equality
import org.scalatest.{AsyncWordSpec, BeforeAndAfter, Matchers}

import java.time.Instant
import java.time.temporal.ChronoUnit

import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}

class TransientStateRepositorySpec extends AsyncWordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfter {

  implicit val traceId: TraceId = TraceId.randomize
  implicit val ec: ExecutionContext = ExecutionContext.global
  private lazy val collection: MongoCollection[TransientState] = db
    .getCollection[TransientState]("users.transientState")
    .withCodecRegistry(TransientStateRepository.V1.registry)
  private lazy val repository = new TransientStateRepository.V1(collection)

  //helpers
  private val sampleSize = 10
  private val randDocs: Seq[TransientState] = {
    implicit val arbInstant: Arbitrary[Instant] = Arbitrary(
      Gen.choose(Instant.now.minusSeconds(1.day.toSeconds), Instant.now).map(_.truncatedTo(ChronoUnit.MILLIS))
    )
    val arb = Arbitrary(Gen.resultOf(TransientState)).arbitrary
    for (_ <- 1 to sampleSize) yield arb.sample.get
  }

  implicit val verificationDocEquality: Equality[PassportVerification] = (a: PassportVerification, b: Any) => b match {
    case exp: PassportVerification =>
      exp.code.toSeq == a.code.toSeq && exp.copy(code = Array.empty) == a.copy(code = Array.empty)
    case _ => false
  }

  before(collection.drop().toFuture.await)

  "V1" can {

    "upsert" in {
      for {
        _ <- Future.sequence(randDocs.map(repository.upsert))
        count <- collection.countDocuments.toFuture
      } yield count.toInt shouldBe sampleSize
    }

    "getAndDelete" in {
      val doc = randDocs.head
      for {
        _ <- repository.upsert(doc)
        _ <- repository.getAndDelete(doc.userId).map(_.get shouldEqual doc)
        _ <- collection.countDocuments.toFuture.map(_ shouldEqual 0)
      } yield succeed
    }

  }
}
