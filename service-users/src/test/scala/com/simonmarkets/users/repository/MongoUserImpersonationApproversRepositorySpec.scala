package com.simonmarkets.users.repository

import com.simonmarkets.logging.TraceId
import com.simonmarkets.syntax._
import com.simonmarkets.users.domain.UserImpersonationApprovers
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, OptionValues}

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

class MongoUserImpersonationApproversRepositorySpec extends AsyncWordSpec with EmbeddedMongoLike with OptionValues with BeforeAndAfterEach {

  private implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  private implicit val tid: TraceId = TraceId.randomize

  private lazy val rawCollection = db.getCollection("users.impersonation.approvers")
  private lazy val repository = new MongoUserImpersonationApproversRepository(rawCollection.withDocumentClass[UserImpersonationApprovers])
  private lazy val collection = rawCollection.withDocumentClass[UserImpersonationApprovers].withCodecRegistry(MongoUserImpersonationApproversRepository.userImpersonationApproversCodecRegistry)

  private val preExistingUserImpersonationApprovers = UserImpersonationApprovers(networkId = "networkId1", userIds = Set("userId1", "userId2"))

  override def beforeEach(): Unit = {
    super.beforeEach()

    collection.drop.toFuture().await
    collection.insertOne(preExistingUserImpersonationApprovers).toFuture.await
  }

  "MongoUserImpersonationApproversRepository" can {
    "getByNetworkId" should {
      "return user impersonation approvers" in {
        for {
          result <- repository.getByNetworkId(preExistingUserImpersonationApprovers.networkId)
        } yield {
          assert(result.value == preExistingUserImpersonationApprovers)
        }
      }

      "return None if id is incorrect" in {
        for {
          result <- repository.getByNetworkId(networkId = "None")
        } yield {
          assert(result.isEmpty)
        }
      }
    }

    "insertByImpersonatorUserId" should {
      "return user impersonation" in {
        for {
          result <- repository.upsertUserImpersonationApprovers(preExistingUserImpersonationApprovers)
        } yield {
          assert(result.value == preExistingUserImpersonationApprovers)
        }
      }

    }

  }
}