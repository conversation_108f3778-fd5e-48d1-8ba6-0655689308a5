package com.simonmarkets.users.repository

import com.simonmarkets.capabilities.UserImpersonationCapabilities.{AdminUserImpersonationCapabilities, ApproveUserImpersonationCapabilities, UpdateUserImpersonationCapabilities}
import com.simonmarkets.capabilities.UsersCapabilities.ImpersonateCapabilities
import com.simonmarkets.logging.TraceId
import com.simonmarkets.syntax._
import com.simonmarkets.users.domain.{ImpersonationStatus, UserImpersonation}
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.model.Filters.equal
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, OptionValues}

import java.time.Instant
import java.time.temporal.ChronoUnit

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

class MongoUserImpersonationRepositorySpec extends AsyncWordSpec with EmbeddedMongoLike with OptionValues with BeforeAndAfterEach {

  private implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  private implicit val tid: TraceId = TraceId("user-impersonation-repository-spec")

  private lazy val rawCollection = db.getCollection("users.impersonation")
  private lazy val repository = new MongoUserImpersonationRepository(rawCollection.withDocumentClass[UserImpersonation])
  private lazy val collection = rawCollection.withDocumentClass[UserImpersonation].withCodecRegistry(MongoUserImpersonationRepository.userImpersonationCodecRegistry)

  private val now = Instant.now().truncatedTo(ChronoUnit.MILLIS)
  private val preExistingUserImpersonation = UserImpersonation(id = "id1", impersonatorUserId = "userId1", impersonatedUserId = "userId2", impersonatedNetworkId = "networkId3", reason = "reason", ticketNumber = None, status = ImpersonationStatus.Pending, createdAt = now, completedAt = None, traceId = tid.toString(), approverUserId = None, approversUserIds = None, entitlements = ImpersonateCapabilities)

  override def beforeEach(): Unit = {
    super.beforeEach()

    collection.drop.toFuture().await
    collection.insertOne(preExistingUserImpersonation).toFuture.await
  }

  "MongoUserImpersonationRepository" can {
    "getById" should {
      "return user impersonation" in {
        for {
          result <- repository.getById(preExistingUserImpersonation.id)(ImpersonateCapabilities)
        } yield {
          assert(result.value == preExistingUserImpersonation)
        }
      }

      "return None if id is incorrect" in {
        for {
          result <- repository.getById("none")(Set.empty)
        } yield {
          assert(result.isEmpty)
        }
      }
    }

    "getNonFinishedUserImpersonations" should {
      "return UserImpersonation for a UserImpersonation with a status of Pending" in {
        for {
          result <- repository.getNonFinishedUserImpersonations(preExistingUserImpersonation.impersonatorUserId)
        } yield {
          assert(result.value == List(preExistingUserImpersonation))
        }
      }
      "return UserImpersonations with statuses of Approved & Submitted" in {
        val approvedUserImpersonation = preExistingUserImpersonation.copy(id = "id2", impersonatorUserId = "userId4", status = ImpersonationStatus.Approved)
        val submittedUserImpersonation = preExistingUserImpersonation.copy(id = "id3", impersonatorUserId = "userId4", status = ImpersonationStatus.Submitted)
        collection.insertMany(Seq(approvedUserImpersonation, submittedUserImpersonation)).toFuture.await
        for {
          result <- repository.getNonFinishedUserImpersonations(approvedUserImpersonation.impersonatorUserId)
        } yield {
          assert(result.value == List(approvedUserImpersonation, submittedUserImpersonation))
        }
      }
      "return None if status is Rejected" in {
        val rejectedUserImpersonation = preExistingUserImpersonation.copy(id = "id2", impersonatorUserId = "userId3", status = ImpersonationStatus.Rejected)
        collection.insertOne(rejectedUserImpersonation).toFuture.await
        for {
          result <- repository.getNonFinishedUserImpersonations(rejectedUserImpersonation.impersonatorUserId)
        } yield {
          assert(result.isEmpty)
        }
      }
      "return None if ImpersonatorUserId is incorrect" in {
        for {
          result <- repository.getNonFinishedUserImpersonations("none")
        } yield {
          assert(result.isEmpty)
        }
      }
    }

    "getByImpersonatedUserId" should {
      "return user impersonation" in {
        repository
          .getByImpersonatedUserId(preExistingUserImpersonation.impersonatedUserId)
          .map(result => assert(result.value == preExistingUserImpersonation))
      }

      "return None if impersonatedUserId not exists" in {
        repository
          .getByImpersonatedUserId("none")
          .map(result => assert(result.isEmpty))
      }
    }

    "getByImpersonatorUserIdAndImpersonatedUserId" should {
      "return user impersonation if it exists" in {
        for {
          resultOpt <- repository.getByImpersonatorUserIdAndImpersonatedUserId(preExistingUserImpersonation.impersonatorUserId, preExistingUserImpersonation.impersonatedUserId)(ImpersonateCapabilities)
        } yield {
          assert(resultOpt.value == preExistingUserImpersonation)
        }
      }

      "return None if impersonation does not exist" in {
        for {
          resultOpt <- repository.getByImpersonatorUserIdAndImpersonatedUserId("nonExistingUserId1", "nonExistingUserId2")(ImpersonateCapabilities)
        } yield {
          assert(resultOpt.isEmpty)
        }
      }
    }

    "insert" should {
      "create a new userImpersonation without a ticketNumber" in {
        for {
          newUserImpersonation <- repository.insert(UserImpersonation(id = "id2", impersonatorUserId = "userId3", impersonatedUserId = "userId4", impersonatedNetworkId = "networkId5", reason = "reason", ticketNumber = None, status = ImpersonationStatus.Pending, createdAt = now, completedAt = None, traceId = tid.toString(), approverUserId = None, approversUserIds = None, entitlements = Set.empty))
          dbResult <- collection.find(equal("id", newUserImpersonation.id)).toFuture
        }
        yield {
          val dbInstance = dbResult.head
          assert(dbInstance.id == "id2")
          assert(dbInstance.impersonatorUserId == "userId3")
          assert(dbInstance.impersonatedUserId == "userId4")
          assert(dbInstance.impersonatedNetworkId == "networkId5")
          assert(dbInstance.ticketNumber.isEmpty)
          assert(dbInstance.status == ImpersonationStatus.Pending)
        }
      }

      "create a new userImpersonation with a ticketNumber" in {
        for {
          newUserImpersonation <- repository.insert(UserImpersonation(id = "id5", impersonatorUserId = "userId6", impersonatedUserId = "userId7", impersonatedNetworkId = "networkId8", reason = "reason", ticketNumber = Some("CM-4884"), status = ImpersonationStatus.Pending, createdAt = now, completedAt = None, traceId = tid.toString(), approverUserId = None, approversUserIds = None, entitlements = Set.empty))
          dbResult <- collection.find(equal("id", newUserImpersonation.id)).toFuture
        }
        yield {
          val dbInstance = dbResult.head
          assert(dbInstance.id == "id5")
          assert(dbInstance.impersonatorUserId == "userId6")
          assert(dbInstance.impersonatedUserId == "userId7")
          assert(dbInstance.ticketNumber.contains("CM-4884"))
          assert(dbInstance.status == ImpersonationStatus.Pending)
        }
      }
    }

    "complete" should {
      "complete a userImpersonation" in {
        for {
          resultOpt <- repository.complete(preExistingUserImpersonation.impersonatorUserId)
          dbResultOpt <- collection.find(equal("id", preExistingUserImpersonation.id)).toFuture()
        } yield {
          val dbResult = dbResultOpt.headOption.value
          assert(resultOpt.value != preExistingUserImpersonation)
          assert(dbResult.id == preExistingUserImpersonation.id)
          assert(dbResult.status == ImpersonationStatus.Complete)
        }
      }

      "don't complete if id is incorrect" in {
        for {
          resultOpt <- repository.complete("userId3")
        } yield {
          assert(resultOpt.isEmpty)
        }
      }
    }

    "forceComplete" should {
      "force complete a userImpersonation with a Pending status" in {
        for {
          resultOpt <- repository.forceComplete(preExistingUserImpersonation.impersonatorUserId)(AdminUserImpersonationCapabilities)
          dbResult <- collection.find(equal("id", preExistingUserImpersonation.id)).toFuture()
        } yield {
          val dbInstance = dbResult.head
          assert(resultOpt.value.id == preExistingUserImpersonation.id)
          assert(dbInstance.id == preExistingUserImpersonation.id)
          assert(dbInstance.status == ImpersonationStatus.Complete)
        }
      }

      "don't force complete a userImpersonation with an Approved status" in {
        val approvedUserImpersonation = preExistingUserImpersonation.copy(id = "id2", impersonatorUserId = "userId2", status = ImpersonationStatus.Approved)
        collection.insertOne(approvedUserImpersonation).toFuture.await
        for {
          resultOpt <- repository.forceComplete(approvedUserImpersonation.impersonatorUserId)(AdminUserImpersonationCapabilities)
          dbResult <- collection.find(equal("id", approvedUserImpersonation.id)).toFuture()
        } yield {
          val dbInstance = dbResult.head
          assert(resultOpt.isEmpty)
          assert(dbInstance.id == approvedUserImpersonation.id)
          assert(dbInstance.status == ImpersonationStatus.Approved)
        }
      }

      "don't force complete a userImpersonation with a Submitted status" in {
        val submittedUserImpersonation = preExistingUserImpersonation.copy(id = "id2", impersonatorUserId = "userId2", status = ImpersonationStatus.Submitted)
        collection.insertOne(submittedUserImpersonation).toFuture.await
        for {
          resultOpt <- repository.forceComplete(submittedUserImpersonation.impersonatorUserId)(AdminUserImpersonationCapabilities)
          dbResult <- collection.find(equal("id", submittedUserImpersonation.id)).toFuture()
        } yield {
          val dbInstance = dbResult.head
          assert(resultOpt.isEmpty)
          assert(dbInstance.id == submittedUserImpersonation.id)
          assert(dbInstance.status == ImpersonationStatus.Submitted)
        }
      }
      "don't force complete if the id is incorrect" in {
        for {
          resultOpt <- repository.forceComplete("noUserId")(AdminUserImpersonationCapabilities)
        } yield {
          assert(resultOpt.isEmpty)
        }
      }
    }

    "expire" should {
      "Change a userImpersonation status to expired and return true" in {
        for {
          userImpersonationExpire <- repository.insert(UserImpersonation(id = "id4", impersonatorUserId = "userId6", impersonatedUserId = "userId7", impersonatedNetworkId = "networkId8", reason = "reason", ticketNumber = Some("CM-4884"), status = ImpersonationStatus.Pending, createdAt = now, completedAt = None, traceId = tid.toString(), approverUserId = None, approversUserIds = None, entitlements = Set.empty))
          resultOpt <- repository.expire(userImpersonationExpire.id)
          dbResult <- collection.find(equal("id", userImpersonationExpire.id)).toFuture
        } yield {
          val dbInstance = dbResult.head
          assert(resultOpt.value)
          assert(dbInstance.id == userImpersonationExpire.id)
          assert(dbInstance.status == ImpersonationStatus.Expired)
        }
      }

      "Do not expire if id is incorrect and return false" in {
        for {
          resultOpt <- repository.expire("noId")
        } yield {
          assert(!resultOpt.value)
        }
      }
    }

    "expireAll" should {
      "Return true & Change all the userImpersonations statuses to expired" in {
        for {
          pendingUserImpersonationToExpire <- repository.insert(UserImpersonation(id = "id4", impersonatorUserId = "userId6", impersonatedUserId = "userId7", impersonatedNetworkId = "networkId8", reason = "reason", ticketNumber = Some("CM-0001"), status = ImpersonationStatus.Pending, createdAt = now, completedAt = None, traceId = tid.toString(), approverUserId = None, approversUserIds = None, entitlements = Set.empty))
          approvedUserImpersonationToExpire <- repository.insert(UserImpersonation(id = "id5", impersonatorUserId = "userId7", impersonatedUserId = "userId8", impersonatedNetworkId = "networkId9", reason = "reason", ticketNumber = Some("CM-0002"), status = ImpersonationStatus.Approved, createdAt = now, completedAt = None, traceId = tid.toString(), approverUserId = None, approversUserIds = None, entitlements = Set.empty))
          submittedUserImpersonationToExpire <- repository.insert(UserImpersonation(id = "id6", impersonatorUserId = "userId8", impersonatedUserId = "userId9", impersonatedNetworkId = "networkId10", reason = "reason", ticketNumber = Some("CM-0003"), status = ImpersonationStatus.Submitted, createdAt = now, completedAt = None, traceId = tid.toString(), approverUserId = None, approversUserIds = None, entitlements = Set.empty))
          ids: Seq[String] = Seq(pendingUserImpersonationToExpire.id, approvedUserImpersonationToExpire.id, submittedUserImpersonationToExpire.id)
          resultOpt <- repository.expireAll(ids)
          pendingDbResult <- collection.find(equal("id", pendingUserImpersonationToExpire.id)).toFuture
          approvedDbResult <- collection.find(equal("id", approvedUserImpersonationToExpire.id)).toFuture
          submittedDbResult <- collection.find(equal("id", submittedUserImpersonationToExpire.id)).toFuture
        } yield {
          assert(resultOpt.value)
          val pendingDbInstance = pendingDbResult.head
          assert(pendingDbInstance.id == pendingUserImpersonationToExpire.id)
          assert(pendingDbInstance.status == ImpersonationStatus.Expired)
          val approvedDbInstance = approvedDbResult.head
          assert(approvedDbInstance.id == approvedUserImpersonationToExpire.id)
          assert(approvedDbInstance.status == ImpersonationStatus.Expired)
          val submittedDbInstance = submittedDbResult.head
          assert(submittedDbInstance.id == submittedUserImpersonationToExpire.id)
          assert(submittedDbInstance.status == ImpersonationStatus.Expired)
        }
      }

      "Return false if expireAll does not succeed for every id" in {
        for {
          resultOpt: Boolean <- repository.expireAll(Seq("id1", "noId2"))
        } yield {
          assert(!resultOpt.value)
        }
      }
    }

    "updateStatus" should {
      "Change the status of the UserImpersonation from Approved to Pending" in {
        for {
          updateStatusUserImpersonation <- repository.insert(UserImpersonation(id = "id5", impersonatorUserId = "userId6", impersonatedUserId = "userId7", impersonatedNetworkId = "networkId8", reason = "reason", ticketNumber = Some("CM-4884"), status = ImpersonationStatus.Approved, createdAt = now, completedAt = None, traceId = tid.toString(), approverUserId = None, approversUserIds = None, entitlements = UpdateUserImpersonationCapabilities))
          resultOpt <- repository.updateStatus(id = "id5", fromStatus = ImpersonationStatus.Approved, toStatus = ImpersonationStatus.Pending)(entitlements = UpdateUserImpersonationCapabilities)
          dbResult <- collection.find(equal("id", updateStatusUserImpersonation.id)).toFuture()
        } yield {
          val dbInstance = dbResult.head
          assert(resultOpt.isDefined)
          assert(dbInstance.id == updateStatusUserImpersonation.id)
          assert(dbInstance.status == ImpersonationStatus.Pending)
        }
      }

      "Do not change the status of the UserImpersonation" in {
        for {
          resultOpt <- repository.updateStatus("noId", fromStatus = ImpersonationStatus.Approved, toStatus = ImpersonationStatus.Pending)(entitlements = UpdateUserImpersonationCapabilities)
        } yield {
          assert(resultOpt.isEmpty)
        }
      }
    }

    "approveOrRejectStatus" should {
      "Change a userImpersonation status to approved" in {
        for {
          approveUserImpersonation <- repository.insert(UserImpersonation(id = "id6", impersonatorUserId = "userId6", impersonatedUserId = "userId7", impersonatedNetworkId = "networkId8", reason = "reason", ticketNumber = Some("CM-4884"), status = ImpersonationStatus.Submitted, createdAt = now, completedAt = None, traceId = tid.toString(), approverUserId = None, approversUserIds = Some(Set("userId8", "userId9")), entitlements = ApproveUserImpersonationCapabilities))
          resultOpt <- repository.approveOrRejectStatus(approveUserImpersonation.id, toStatus = ImpersonationStatus.Approved, approverUserId = "userId8")(entitlements = ApproveUserImpersonationCapabilities)
          dbResult <- collection.find(equal("id", approveUserImpersonation.id)).toFuture()
        } yield {
          val dbInstance = dbResult.head
          assert(resultOpt.isDefined)
          assert(dbInstance.id == approveUserImpersonation.id)
          assert(dbInstance.status == ImpersonationStatus.Approved)
        }
      }

      "Change a userImpersonation status to rejected" in {
        for {
          expireUserImpersonation <- repository.insert(UserImpersonation(id = "id7", impersonatorUserId = "userId6", impersonatedUserId = "userId7", impersonatedNetworkId = "networkId8", reason = "reason", ticketNumber = Some("CM-4884"), status = ImpersonationStatus.Submitted, createdAt = now, completedAt = None, traceId = tid.toString(), approverUserId = None, approversUserIds = Some(Set("userId8", "userId9")), entitlements = ApproveUserImpersonationCapabilities))
          resultOpt <- repository.approveOrRejectStatus(expireUserImpersonation.id, toStatus = ImpersonationStatus.Rejected, approverUserId = "userId8")(entitlements = ApproveUserImpersonationCapabilities)
          dbResult <- collection.find(equal("id", expireUserImpersonation.id)).toFuture()
        } yield {
          val dbInstance = dbResult.head
          assert(resultOpt.isDefined)
          assert(dbInstance.id == expireUserImpersonation.id)
          assert(dbInstance.status == ImpersonationStatus.Rejected)
        }
      }

      "Do not expire if id is incorrect and return false" in {
        for {
          resultOpt <- repository.expire("noId")
        } yield {
          assert(!resultOpt.value)
        }
      }
    }

  }
}