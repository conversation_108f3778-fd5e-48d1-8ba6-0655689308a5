package com.simonmarkets.users.repository

import com.simonmarkets.users.repository.filters.{EmailFilter, Filters, FirmIdFilter, LocationsFilter, NetworkFilter, UserIdsFilter}
import org.mongodb.scala.model.Filters._
import org.scalatest.{Matchers, WordSpec}

class FilterSpec extends WordSpec with Matchers {

  "Filters.make" should {

    "create filters from map" in {
      val params = Map(
        "network" -> List("network1"),
        "firmId" -> List("firmId1"),
        "email" -> List("<EMAIL>")
      )

      Filters.make(params) should contain theSameElementsAs Set(
        NetworkFilter("network1"),
        FirmIdFilter("firmId1"),
        Email<PERSON>ilter("<EMAIL>")
      )

    }

    "take only last when many present" in {
      val params = Map("network" -> List("network1", "network2"))
      Filters.make(params) should contain only NetworkFilter("network2")
    }

  }

  "LocationsFilter creates the correct expression" in {
    LocationsFilter(List("loc1", "loc2", "loc3")).expression shouldBe in("locations", "loc1", "loc2", "loc3")
  }

  "UserIdsFilter creates the correct expression" in {
    UserIdsFilter(List("user1", "user2", "user3")).expression shouldBe in("id", "user1", "user2", "user3")
  }

}
