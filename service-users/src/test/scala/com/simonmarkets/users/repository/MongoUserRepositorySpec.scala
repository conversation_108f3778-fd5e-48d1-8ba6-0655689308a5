package com.simonmarkets.users.repository

import com.goldmansachs.marquee.pipg.License
import com.simonmarkets.capabilities.UsersCapabilities.ViewUserViaNetwork
import com.simonmarkets.logging.TraceId
import com.simonmarkets.mongodb.bson.{Format, SnapshotFormat}
import com.simonmarkets.mongodb.snapshots.EntitySnapshot
import com.simonmarkets.networks.common.TestUtils._
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.users.api.Page
import com.simonmarkets.users.common.UniqueUserId._
import com.simonmarkets.users.common._
import com.simonmarkets.users.domain.{UserSnapshotEntityView, UserSnapshotView}
import com.simonmarkets.users.repository.encoders.UserFormat
import com.simonmarkets.users.{TestNetwork, TestUser}
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.Document
import org.scalatest.Matchers._
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, WordSpec}
import simon.Id.NetworkId

import java.time.temporal.ChronoUnit
import java.time.{Instant, LocalDateTime}

import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext, Future}

class MongoUserRepositorySpec extends WordSpec with MockitoSugar with EmbeddedMongoLike with BeforeAndAfter with UserMongoJsonCodecs {

  "MongoUserRepository" can {

    implicit val ec: ExecutionContext = ExecutionContext.global
    implicit val tid: TraceId = TraceId("test")
    lazy val collection = db.getCollection[Document]("users_test")
    lazy val snapshotCollection = db
      .withCodecRegistry(MongoUserRepository.registry)
      .getCollection[Document]("users_test.snapshots")

    // Snapshots are not supported by embedded mongo as they implemented via Mongo transactions
    lazy val userRepository = new MongoUserRepository(
      client,
      entityCollection = collection,
      entitySnapshotCollection = snapshotCollection,
      useSnapshots = false
    )

    val entitlements = Set("admin")

    val duration: Duration = 20.seconds

    val testNetworkId = TestNetwork().id
    val ViewViaTestNetwork = s"$ViewUserViaNetwork:$testNetworkId"
    val omsUser = TestUser(id = "21", omsId = Some("oms-id"), entitlements = Set(ViewViaTestNetwork, "admin"))
    val distributorUser = TestUser(id = "22", distributorId = Some("dist-id"), entitlements = Set(ViewViaTestNetwork, "admin"))
    val distributorUser2 = TestUser(id = "23", distributorId = Some("dist-id-2"), entitlements = Set(ViewViaTestNetwork, "admin"))
    val emailUser = TestUser(id = "email", email = "email", entitlements = Set(ViewViaTestNetwork, "admin"))
    val idpIdUser = TestUser(id = "25", idpId = Some("idpId"), entitlements = Set(ViewViaTestNetwork, "admin"))
    val accountInContextUser = TestUser(id = "24", email = "<EMAIL>", networkId = NetworkId("some-net"), accountInContext = Some("123456"), entitlements = Set(ViewViaTestNetwork, "admin"))
    val contextUser = TestUser(id = "26", email = "<EMAIL>", networkId = NetworkId("some-net"), accountInContext = Some("123456"),
      context = Some(Context(
        accountId = Some("accountId"),
        proposalId = Some("proposalId"),
        taskId = Some("taskId"),
        serviceRequestId = Some("serviceRequestId"),
        bucketId = Some("bucketId"),
        externalRoles = Some(Set("externalRole1", "externalRole2")),
        lastUpdatedAt = Some(Instant.now.truncatedTo(ChronoUnit.MILLIS))
      )),
      entitlements = Set(ViewViaTestNetwork, "admin"))
    val purviewLicenseUser = TestUser(id = "27", purviewLicenses = Set(License.NPN("test_NPN")), entitlements = Set(ViewViaTestNetwork, "admin"))
    val purviewNsccCodesUser = TestUser(id = "28", purviewNsccCodes = Set("NSCC1", "NSCC2"), entitlements = Set(ViewViaTestNetwork, "admin"))
    val wlpUser = TestUser(id = "29", whiteLabelPartnerId = Some("0"), entitlements = Set(ViewViaTestNetwork, "admin"))

    val testUserInactive = TestUser(id = "inactiveId", isActive = false, entitlements = Set(ViewViaTestNetwork, "admin"))

    val individualTestUsers = Seq(
      omsUser,
      distributorUser,
      distributorUser2,
      emailUser,
      idpIdUser,
      accountInContextUser,
      contextUser,
      testUserInactive,
      purviewLicenseUser,
      purviewNsccCodesUser,
      wlpUser
    )

    val testUsers = TestUser.create(count = 20, entitlements = Set("admin")) ++ individualTestUsers
    val userCollection: Seq[Document] = testUsers.map(user => UserFormat.write(user))
    val testSnaps: List[EntitySnapshot[User]] = getTestSnapshots(10, testUsers.slice(0,5))

    implicit val userFormat: Format[User] = UserFormat
    implicit val userSnapshotFormat: Format[EntitySnapshot[User]] = new SnapshotFormat[User](userFormat)
    val snapDocs: Seq[Document] = testSnaps.map(userSnapshotFormat.write)

    before {
      Await.result(collection.drop().toFuture(), duration)
      Await.result(snapshotCollection.drop().toFuture(), duration)
      Await.result(collection.insertMany(userCollection).toFuture(), duration)
      Await.result(snapshotCollection.insertMany(snapDocs).toFuture(), duration)
    }

    "getUsersPage" should {

      "return user page with the first n users if the 'from' parameter is not specified" in {
        val sortedObjectIds = Await.result(collection.find().toFuture(), 4.seconds).map(_.get("_id").get.asObjectId().getValue.toString)

        val limit = 5
        val result = userRepository.getUsersPage(limit, None)(entitlements)

        val assertions = (p: Page[User]) => {
          p.result.size shouldBe limit
          p.result.map(_.id) shouldBe (0 until limit).map(_.toString).toList
          p.next shouldBe Some(sortedObjectIds(limit - 1))
          p.total shouldBe userCollection.size
        }

        result assert assertions
      }
      "return user page with the next n users if the 'from' parameter is specified" in {
        val sortedObjectIds = Await.result(collection.find().toFuture(), 4.seconds).map(_.get("_id").get.asObjectId().getValue.toString)

        val limit = 5
        val result = userRepository.getUsersPage(limit, Some(sortedObjectIds(limit)))(entitlements)

        val assertions = (p: Page[User]) => {
          p.result.size shouldBe limit
          p.result.map(_.id) shouldBe (limit + 1 to limit + limit).map(_.toString).toList
          p.next shouldBe Some(sortedObjectIds(limit + limit))
          p.total shouldBe userCollection.size
        }

        result assert assertions
      }

      "return every user from the collection if every page is requested" in {
        val limit = 9

        def getPage(from: Option[String], limit: Int, pages: List[Page[User]]): Future[List[Page[User]]] = {
          val nextPage = userRepository.getUsersPage(limit, from)(entitlements)
          nextPage.flatMap { page =>
            if (page.next.isEmpty) {
              Future(page :: pages)
            } else {
              getPage(page.next, limit, page :: pages)
            }
          }
        }

        val result = getPage(None, limit, List())

        val assertions = (p: List[Page[User]]) => {
          p.size shouldBe userCollection.size / limit + 1
          p.flatMap(_.result).size shouldBe userCollection.size
          p.flatMap(_.result).sortBy(_.id) shouldBe testUsers.sortBy(_.id)
          p.head.next shouldBe None
        }

        result assert assertions
      }

      "return 0 users if invalid from specified" in {
        val limit = 2
        val result = userRepository.getUsersPage(limit, Some("123"))(entitlements)

        result assertResult Page(userCollection.size, 0, None, List())
      }

      "only return the users which the requester entitled to view" in {

        val usersWithDiffentitlement = ('a' to 'f').map(i => TestUser(i.toString, entitlements = Set("test")))

        val documents = usersWithDiffentitlement.map(user => UserFormat.write(user))

        Await.result(collection.insertMany(documents).toFuture(), duration)

        val res = userRepository.getUsersPage(50, None)(Set("test"))

        res assert ((p: Page[User]) => {
          p.result.size shouldBe 6
        })
      }
    }

    "getUsersStream" should {

      "return all users if no filters provided" in {
        val users = userRepository.getUsersStream(Set.empty)(entitlements).toFuture.await
        users should contain theSameElementsAs userCollection.map(UserFormat.read)
      }

    }

    "getUserByExternalId" should {
      "return user by oms id" in {
        userRepository.getUserByUniqueId(Oms(omsUser.omsId.getOrElse("")))(Set(ViewViaTestNetwork)) assertResult Some(omsUser)
        userRepository.getUserByUniqueId(Distributor(omsUser.omsId.getOrElse(""), testNetworkId))(Set(ViewViaTestNetwork)) assertResult None
      }

      "return user by distributor id" in {
        userRepository.getUserByUniqueId(Distributor(distributorUser.distributorId.getOrElse(""), testNetworkId))(Set(ViewViaTestNetwork)) assertResult Some(distributorUser)
        userRepository.getUserByUniqueId(Oms(distributorUser.distributorId.getOrElse("")))(Set(ViewViaTestNetwork)) assertResult None
      }

      "return user by email (case insensitive)" in {
        userRepository.getUserByUniqueId(Email(emailUser.email.toLowerCase, emailUser.networkId))(Set(ViewViaTestNetwork)) assertResult Some(emailUser)
        userRepository.getUserByUniqueId(Email(emailUser.email.toUpperCase, emailUser.networkId))(Set(ViewViaTestNetwork)) assertResult Some(emailUser)
      }

      "return user by idpId" in {
        userRepository.getUserByUniqueId(Idp(idpIdUser.idpId.get))(Set(ViewViaTestNetwork)) assertResult Some(idpIdUser)
      }

      "check for entitlements" in {
        userRepository.getUserByUniqueId(Distributor(distributorUser2.distributorId.getOrElse(""), testNetworkId))(Set(ViewViaTestNetwork)) assertResult Some(distributorUser2)
        userRepository.getUserByUniqueId(Distributor(distributorUser2.distributorId.getOrElse(""), testNetworkId))(Set()) assertResult None
      }

      "return nothing if user not found due to non existent id" in {
        userRepository.getUserByUniqueId(Distributor("non-existing-id", testNetworkId))(Set("admin")) assertResult None
      }

      "return nothing if user not found due to wrong network context" in {
        userRepository.getUserByUniqueId(Distributor(distributorUser.distributorId.getOrElse(""), NetworkId("different-network")))(Set(ViewViaTestNetwork)) assertResult None
        userRepository.getUserByUniqueId(Distributor(distributorUser.distributorId.getOrElse(""), testNetworkId))(Set(ViewViaTestNetwork)) assertResult Some(distributorUser)
      }
    }

    "update" should {
      "update user" in {
        val user = testUsers.tail.head

        val updated = user.copy(firstName = "New Name")

        userRepository.updateUser(user.id, updated) assertResult updated.copy(version = 1)
        userRepository.getById(user.id)(Set("admin")) assertResult Some(updated.copy(version = 1))
      }

      "not overwrite account in context" in {
        val updated = accountInContextUser.copy(email = "<EMAIL>")

        userRepository.updateUser(accountInContextUser.id, updated) assertResult (updated.copy(version = 1))
        userRepository.getById(accountInContextUser.id)(Set("admin")) assertResult (Some(updated.copy(version = 1)))
      }

      "not overwrite context object" in {
        val updated = contextUser.copy(email = "<EMAIL>")

        userRepository.updateUser(contextUser.id, updated) assertResult (updated.copy(version = 1))
        userRepository.getById(contextUser.id)(Set("admin")) assertResult (Some(updated.copy(version = 1)))
      }
    }

    "getByIdType" should {
      "read account in context" in {
        userRepository.getUserByUniqueId(Guid(accountInContextUser.id))(Set(ViewViaTestNetwork)) assertResult Some(accountInContextUser)
      }

      "read context object" in {
        userRepository.getUserByUniqueId(Guid(contextUser.id))(Set(ViewViaTestNetwork)) assertResult Some(contextUser)
      }
    }

    "refreshLastVisitedAt" should {
      "refresh the last visited at when user is found" in {
        val userId = testUsers.head.id
        val updated = userRepository.refreshLastUpdatedTime(userId)
        updated assertResult 1

      }
      "not refresh the last visited at when user is not found" in {
        val userId = "non existing"
        val updated = userRepository.refreshLastUpdatedTime(userId)

        updated assertResult 0
      }
    }

    "getPriorSnapshotByEntityId" should {

      "return entity in matching entity id" in {
        val expected = UserSnapshotView(
          UserSnapshotEntityView(
            id = "1",
            networkId = TestNetwork().id,
            roles = Set.empty,
            customRoles = Set.empty
          )
        )
        userRepository.getPriorSnapshotByEntityId("1") assertResult Some(expected)
      }

      "return nothing if no entity matched id" in {
        userRepository.getPriorSnapshotByEntityId("not_found") assertResult None
      }

    }


    "bumpVersions" should {
      "update all users version" in {
        val userIds = testUsers.map(_.id).toSet
        val numUpdatedUsers = userRepository.bumpVersions(userIds)(Set("admin"))
        numUpdatedUsers assertResult testUsers.size

        val users = userRepository.getUsersByIds(userIds)(Set("admin")).await
        users.map(user => user.version shouldBe 1)
      }
    }
  }

  def getTestSnapshots(count: Int, users: List[User]): List[EntitySnapshot[User]] = {
    (0 until count).toList.map(i =>
      EntitySnapshot(
        id = "snap_"+i.toString,
        userId = "userId_" + i.toString,
        modificationDate = LocalDateTime.of(1993, 7, 24, 1, 1),
        comment = Some("comment_" + i.toString),
        entity = users(i % users.size)
      )
    )
  }
  //TODO more tests
}
