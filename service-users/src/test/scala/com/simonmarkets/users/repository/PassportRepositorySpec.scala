package com.simonmarkets.users.repository

import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.domain.PassportUserCandidate
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.users.domain.PassportVerification
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.model.Filters
import org.scalactic.Equality
import org.scalatest.{AsyncWordSpec, BeforeAndAfter, Matchers}

import java.time.Instant
import java.time.temporal.ChronoUnit

import scala.concurrent.ExecutionContext

class PassportRepositorySpec extends AsyncWordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfter {

  implicit val traceId: TraceId = TraceId.randomize
  implicit val ec: ExecutionContext = ExecutionContext.global
  private lazy val collection: MongoCollection[PassportVerification] = db
    .getCollection[PassportVerification]("passport_test")
    .withCodecRegistry(PassportRepository.V1.registry)
  private lazy val repository = PassportRepository.V1(collection)

  //helpers
  private val doc = PassportVerification(
    id = "id",
    userId = "userId",
    users = List(
      PassportUserCandidate(1, "email", "domain.icapitalnetwork.com", "whiteLabelPartnerName")
    ),
    code = Array.empty,
    sessionId = "jti",
    creationTime = Instant.now.truncatedTo(ChronoUnit.SECONDS),
    completionTime = None,
    pending = true
  )

  implicit val verificationDocEquality: Equality[PassportVerification] = (a: PassportVerification, b: Any) => b match {
    case exp: PassportVerification =>
      exp.code.toSeq == a.code.toSeq && exp.copy(code = Array.empty) == a.copy(code = Array.empty)
    case _ => false
  }

  before(collection.drop().toFuture.await)

  "V1" can {

    "insert" in {
      for {
        _ <- repository.insert(doc)
        count <- collection.countDocuments.toFuture
      } yield count.toInt shouldBe 1
    }

    "findByCode" in {
      for {
        _ <- repository.insert(doc)
        _ <- repository.findByCode(doc.code).map(_.get shouldEqual doc)
        _ <- repository.findByCode("notFound".getBytes).map(_ shouldBe None)
      } yield succeed
    }

    "confirm" in {
      val nowish = Instant.now.truncatedTo(ChronoUnit.SECONDS)
      for {
        _ <- repository.insert(doc)
        _ <- repository.confirm(doc.code)
        updated <- collection.find(Filters.eq("id", doc.id)).head
        _ = doc
          .copy(pending = false, completionTime = Some(nowish))
          .shouldEqual(updated.copy(completionTime = Some(nowish)))
      } yield succeed
    }

  }

}
