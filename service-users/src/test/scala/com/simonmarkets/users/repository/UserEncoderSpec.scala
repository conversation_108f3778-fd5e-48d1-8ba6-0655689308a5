package com.simonmarkets.users.repository

import com.goldmansachs.marquee.pipg.User.DistributorInfo
import com.goldmansachs.marquee.pipg.{AcordState, GroupIdType, License, UserRole}
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.shared.MaskedId
import com.simonmarkets.users.common.{EventInfo, LandingPage, LoginMode, User, UserType}
import com.simonmarkets.users.repository.encoders.UserFormat
import org.scalatest.{Matchers, WordSpec}
import simon.Id.NetworkId

import java.time.temporal.ChronoUnit
import java.time.{LocalDateTime, ZoneId}

class UserEncoderSpec extends WordSpec with Matchers {
  val now: LocalDateTime = LocalDateTime.now(ZoneId.systemDefault()).truncatedTo(ChronoUnit.MILLIS)

  val nonEmpty: User = User(
    id = "some-id",
    networkId = NetworkId("some-network-id"),
    email = "<EMAIL>",
    firstName = "some-first-name",
    lastName = "some-last-name",
    distributorId = Some("123"),
    omsId = Some("some-tradeweb-id"),
    tradewebEligible = true,
    regSEligible = true,
    isActive = true,
    roles = Set(UserRole.EqPIPGGSAdmin, UserRole.EqPIPGPB),
    entitlements = Set("role:EqPIPGGSAdmin"),
    createdAt = now,
    createdBy = "me",
    updatedAt = now,
    updatedBy = "me",
    emailSentAt = Some(now),
    emailSentBy = Some("foo"),
    lastVisitedAt = Some(now),
    maskedIds = Set(MaskedId("a", "b")),
    distributorInfo = Some(DistributorInfo(Some("role"), Some("branch"), Some("subgroup"), Some(true))),
    licenses = Set(License.StateProducer(id = "12", state = AcordState.AK)),
    idpLoginId = "idpLoginId",
    version = 1,
    loginMode = LoginMode.EnumNotFound,
    userType = UserType.Human,
    eventInfo = EventInfo.Default,
    externalIds = Seq(ExternalId("subject", "id")),
    landingPage = Some(LandingPage.EnumNotFound),
    groups = Map(GroupIdType.DistributorId.productPrefix -> Set("hi :)")),
  )

  val emptyUser: User = User(
    id = "some-id",
    networkId = NetworkId("some-network-id"),
    email = "<EMAIL>",
    firstName = "some-first-name",
    lastName = "some-last-name",
    distributorId = None,
    omsId = None,
    roles = Set.empty,
    entitlements = Set.empty,
    createdAt = now,
    createdBy = "me",
    updatedAt = now,
    updatedBy = "me",
    emailSentAt = None,
    emailSentBy = None,
    lastVisitedAt = None,
    maskedIds = Set.empty[MaskedId],
    idpLoginId = "idpLoginId",
    version = 2,
    loginMode = LoginMode.EnumNotFound,
    userType = UserType.Human,
    eventInfo = EventInfo.Default,
    externalIds = Seq.empty,
    landingPage = Some(LandingPage.EnumNotFound),
    groups = Map.empty,
  )


  "UserEncoder" can {
    "encode user" should {
      "encode non empty" in {
        val encoded = UserFormat.write(nonEmpty)
        val decoded = UserFormat.read(encoded)

        decoded shouldBe nonEmpty
      }

      "encode empty" in {
        val encoded = UserFormat.write(emptyUser)
        val decoded = UserFormat.read(encoded)

        decoded shouldBe emptyUser
      }
    }
  }

}
