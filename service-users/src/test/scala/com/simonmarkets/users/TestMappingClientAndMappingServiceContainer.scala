package com.simonmarkets.users

import akka.actor.ActorSystem
import akka.http.scaladsl.Http
import akka.stream.Materializer
import com.dimafeng.testcontainers.DockerComposeContainer
import com.github.benmanes.caffeine.cache.{AsyncCache, Caffeine}
import com.simonmarkets.http.authentication.NoAuthCredentials
import com.simonmarkets.http.{FutureHttpClient, HttpClientConfig}
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.clients.usersync.{HttpUserSyncMappingClient, UserNetworkMappingRequest, UserNetworkMappingResponse}

import java.io.File

import scala.concurrent.ExecutionContext.global

object TestMappingClientAndMappingServiceContainer {

  val mappingServiceContainer: DockerComposeContainer.Def = DockerComposeContainer.Def(
    new File(getClass.getResource("/mapping-service/docker-compose.yml").getPath),
    env = Map("BUNDLE_GEM__FURY__IO" -> "change-me",
      "NODE_AUTH_TOKEN" -> "change-me")
  )

  implicit val ec = global
  implicit val system: ActorSystem = ActorSystem("test")
  implicit val mat: Materializer = Materializer(system)
  implicit val traceId = TraceId.randomize
  val partnerMappingCache: AsyncCache[UserNetworkMappingRequest, Option[UserNetworkMappingResponse]] = Caffeine
    .newBuilder()
    .buildAsync[UserNetworkMappingRequest, Option[UserNetworkMappingResponse]]

  val mappingClient = new HttpUserSyncMappingClient(
    new FutureHttpClient(Http(),
      HttpClientConfig(auth = NoAuthCredentials,
        proxy = None)),
    "http://localhost:3000/api/mappings",
    "simon",
    None,
    Some(partnerMappingCache)
  )

}
