package com.simonmarkets.users

import com.simonmarkets.users.config.{AppConfiguration, MonolithCacheExpiryAppConfig}
import com.simonmarkets.utils.config.Resolvers._
import com.simonmarkets.utils.config.resolvers.ConfigResolver
import com.typesafe.config.ConfigFactory
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}
import pureconfig.generic.auto._

class ConfigSpec extends WordSpec with Matchers with MockitoSugar {

  class TestResolver extends ConfigResolver {
    override def prefix: String = "sm"

    override def resolve(path: String): String =
      path match {
        case "applicationconfig-mongo-auth-pipg" =>
          """
            |   {
            |      url = "mongoUrl"
            |      authentication {
            |        type = "password"
            |        user = "alpha-pipg-user"
            |        password = "password"
            |        database = "admin"
            |      }
            |    }""".stripMargin
        case "applicationconfig-usersync-kafka-config" =>
          """
            |   {
            |      producer {
            |       topic = "simon-users"
            |       producer-group-id = "simon-alpha"
            |       source = "simon"
            |      }
            |      consumer {
            |       topics = ["icn_users_sync"]
            |       client-id = ""
            |       group-id = "user_sync_simon_consumer"
            |       connection = {
            |         type = no-auth
            |         servers = ["someServer:9092"]
            |       }
            |      }
            |      num-consumers = 3
            |      connection-url = "confluentUrl"
            |      auth {
            |       api-key = "apiKey"
            |       secret = "password"
            |       security-protocol="SASL_SSL"
            |      }
            |      is-user-sync-enabled = true
            |    }""".stripMargin
        case "applicationconfig-usersync-mapping-client-config" =>
          """
            |{
            |  api-key = "Token 123"
            |  base-path = "path"
            |  host = "host.com"
            |  cache-settings = {
            |    cache-enabled = true
            |    max-entries = 10000,
            |    ttl = 300.seconds
            |  }
            |  http-client-config {
            |    auth {
            |      type = NoAuth
            |    }
            |  }
            |}""".stripMargin
        case other => other
      }

    override def resolveBinary(path: String): Array[Byte] =
      path match {
        case "applicationconfig-simon-vertx-ssl-jks" => Array.emptyByteArray
      }
  }

  import com.simonmarkets.users.config.AppConfiguration._

  "Configs" should {
    "not blow up" in {
      val configNames = Set("local.conf", "alpha.conf", "qa.conf", "prod.conf")
      System.setProperty("SM_CREDENTIAL_PROVIDER", "webIdentityProvider")
      ConfigFactory.invalidateCaches()
      configNames.foreach { rawConfig =>
        val config = ConfigFactory.load(rawConfig).resolveSecrets(List(new TestResolver))
        pureconfig.loadConfigOrThrow[AppConfiguration](config)
        pureconfig.loadConfigOrThrow[MonolithCacheExpiryAppConfig](config)
      }
    }
  }
}
