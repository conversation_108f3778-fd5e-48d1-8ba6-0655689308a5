package com.simonmarkets.users.request

import com.simonmarkets.syntax.anyOpsConversion
import com.simonmarkets.users.api.request._
import com.simonmarkets.users.common.IdType.Distributor
import org.scalatest.{BeforeAndAfterAll, FunSpec, Matchers}

class PartialUserUpdateSpec extends FunSpec with BeforeAndAfterAll with Matchers {

  describe("Partial update requests") {
    it("should remove empty locations strings") {
      val request = LocationsUpdateRequest(
        userId = "test".some, None, userIdType = Distributor, locations = Set("", "a", "b", " ")
      )
      request.locationsSet shouldBe Set("a", "b")
    }
    it("should remove empty FA Number strings") {
      val request = FANumbersUpdateRequest(
        userId = "test".some, None, userIdType = Distributor, faNumbers = Set("", "a", "b", " ")
      )
      request.faNumbersSet shouldBe Set("a", "b")
    }

    it("should remove empty NPN or CRD strings") {
      val request = LicensesUpdateRequest(
        userId = "test".some, None, userIdType = Distributor, npn = Some(""), crd = Some(" ")
      )
      request.licenseSet shouldBe Set.empty
    }
  }

}
