{"eventId": "A5_VD0hxQteGx43uGAWpbQ", "eventTime": "2023-01-23T15:53:06.000Z", "eventType": "com.okta.oauth2.tokens.transform", "eventTypeVersion": "1.0", "contentType": "application/json", "cloudEventVersion": "0.1", "source": "https://auth.int.simonmarkets.com/oauth2/ausesty3dmANUL4lH0h7/v1/token", "data": {"context": {"request": {"id": "Y86tYaQogClj7Wom6-U8rAAABTE", "method": "POST", "url": {"value": "https://auth.int.simonmarkets.com/oauth2/ausesty3dmANUL4lH0h7/v1/token"}, "ipAddress": "***********"}, "protocol": {"type": "OAUTH2.0", "request": {"scope": "openid", "grant_type": "urn:ietf:params:oauth:grant-type:saml2-bearer", "client_id": "0oa1ggsd0jae9ZSRT0h8"}, "issuer": {"uri": "https://auth.int.simonmarkets.com/oauth2/ausesty3dmANUL4lH0h7"}, "client": {"id": "0oa1ggsd0jae9ZSRT0h8", "name": "Envestnet iframe", "type": "TRUSTED"}}, "user": {"id": "00u1dbc7kmjE3Bvxh0h8", "passwordChanged": "2022-10-19T18:54:48.000Z", "profile": {"login": "<EMAIL>", "firstName": "test", "lastName": "envestnet", "locale": "en", "timeZone": "America/Los_Angeles"}, "_links": {"groups": {"href": "https://auth.int.simonmarkets.com/api/v1/users/00u1dbc7kmjE3Bvxh0h8/groups"}, "factors": {"href": "https://auth.int.simonmarkets.com/api/v1/users/00u1dbc7kmjE3Bvxh0h8/factors"}}}, "policy": {"id": "00p1ggsx0p1WhJhpf0h8", "rule": {"id": "0pr1ggstqh4bCEzQw0h8"}}}, "identity": {"claims": {"sub": "00u1dbc7kmjE3Bvxh0h8", "ver": 1, "iss": "https://auth.int.simonmarkets.com/oauth2/ausesty3dmANUL4lH0h7", "aud": "0oa1ggsd0jae9ZSRT0h8", "jti": "ID.86_weamCon4yaeiOzO-Sm88_kpJYZTWUIJrW9Fpsrlk", "amr": ["pwd"], "idp": "00oeo3alopku2WjWj0h7", "auth_time": 1674489186}, "token": {"lifetime": {"expiration": 180}}}, "access": {"claims": {"ver": 1, "jti": "AT.bXFhukSw_2J-v_K4wOWRI19sQfZ20gXoxJvlQ_jq73U", "iss": "https://auth.int.simonmarkets.com/oauth2/ausesty3dmANUL4lH0h7", "aud": "simonmarkets.com", "cid": "0oa1ggsd0jae9ZSRT0h8", "uid": "00u1dbc7kmjE3Bvxh0h8", "auth_time": 1674489186, "result": "action", "sub": "undefined", "scopes": ["fa-manager", "fa"]}, "token": {"lifetime": {"expiration": 180}}, "scopes": {"openid": {"id": "scpesty3dniKOWotP0h7", "action": "GRANT"}}}}}