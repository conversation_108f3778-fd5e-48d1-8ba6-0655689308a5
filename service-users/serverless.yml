service: service-users

provider:
  stage: ${opt:stage, 'sandbox'}
  name: aws
  runtime: java11
  region: us-east-1
  profile: ${self:custom.file.provider.profile}
  iam:
    role: ${self:custom.file.provider.iam.role}
  versionFunctions: false
  tracing:
    lambda: true
  vpc:
    securityGroupIds: ${self:custom.file.provider.securityGroupIds}
    subnetIds: ${self:custom.file.provider.subnetIds}
  deploymentBucket:
    name: simon-${self:provider.profile}-serverless
  logRetentionInDays: ${self:custom.file.logRetentionInDays}

custom:
  file: ${file(../config/${opt:stage}.yml)}
  logSubscription: ${self:custom.file.logSubscription}
  warmup:
    default:
      enabled: true
      concurrency: 10
      prewarm: true
      timeout: 60
      events:
        - schedule: rate(5 minutes)
      vpc: false
      role: ${self:provider.iam.role}
  icnFeatures: ${self:custom.file.icnFeatures}
  newRelic:
    javaNewRelicHandler: handleStreamsRequest


package:
  artifact: target/service-users-uber.jar

plugins:
  - serverless-plugin-warmup
  - serverless-plugin-log-subscription

functions:
  monolith-cache-expire:
    handler: com.simonmarkets.users.MonolithCacheExpiryApp::handleRequest
    warmup:
      default:
        enabled: false
    memorySize: 1024
    timeout: 900
    reservedConcurrency: 50
    environment:
      JAVA_TOOL_OPTIONS:
        -Dconfig.file=${opt:stage}.conf
        -DSM_CREDENTIAL_PROVIDER=default
        ${self:custom.file.proxySystemProperty, ''}
    events:
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusUsers}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"
            detail:
              ns:
                db:
                  - pipg
                coll:
                  - users
          inputTransformer:
            inputPathsMap:
              id: "$.id"
              time: "$.time"
              detail: "$.detail"
            inputTemplate: '{"id": <id>, "time": <time>, "detail":<detail>}'
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusNetworks}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"
            detail:
              ns:
                db:
                  - ${self:custom.file.dbNetworks}
                coll:
                  - ${self:custom.file.collectionNetworks}
          inputTransformer:
            inputPathsMap:
              id: "$.id"
              time: "$.time"
              detail: "$.detail"
            inputTemplate: '{"id": <id>, "time": <time>, "detail":<detail>}'
  acl-sync:
    handler: com.simonmarkets.users.AclSyncEventHandler::handleRequest
    warmup:
      default:
        enabled: false
    memorySize: 4096
    timeout: 900
    reservedConcurrency: 50
    events:
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusUsers}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"
            detail:
              ns:
                db:
                  - ${self:custom.file.dbUsers}
                coll:
                  - ${self:custom.file.collectionUsers}
          inputTransformer:
            inputPathsMap:
              id: "$.id"
              time: "$.time"
              detail: "$.detail"
            inputTemplate: '{"id": <id>, "time": <time>, "detail":<detail>}'
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusNetworks}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"
            detail:
              ns:
                db:
                  - ${self:custom.file.dbNetworks}
                coll:
                  - ${self:custom.file.collectionNetworks}
          inputTransformer:
            inputPathsMap:
              id: "$.id"
              time: "$.time"
              detail: "$.detail"
            inputTemplate: '{"id": <id>, "time": <time>, "detail":<detail>}'
    environment:
      JAVA_TOOL_OPTIONS:
        -Dconfig.file=${opt:stage}.conf
        -DSM_CREDENTIAL_PROVIDER=default
        ${self:custom.file.proxySystemProperty, ''}
  okta-sync:
    handler: com.simonmarkets.users.OktaSyncEventHandler::handleRequest
    warmup:
      default:
        enabled: false
    memorySize: 1024
    timeout: 900
    reservedConcurrency: 50
    events:
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusUsers}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"
            detail:
              ns:
                db:
                  - ${self:custom.file.dbUsers}
                coll:
                  - ${self:custom.file.collectionUsers}
          inputTransformer:
            inputPathsMap:
              id: "$.id"
              time: "$.time"
              detail: "$.detail"
            inputTemplate: '{"id": <id>, "time": <time>, "detail":<detail>}'
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusNetworks}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"
            detail:
              ns:
                db:
                  - ${self:custom.file.dbNetworks}
                coll:
                  - ${self:custom.file.collectionNetworks}
          inputTransformer:
            inputPathsMap:
              id: "$.id"
              time: "$.time"
              detail: "$.detail"
            inputTemplate: '{"id": <id>, "time": <time>, "detail":<detail>}'
    environment:
      JAVA_TOOL_OPTIONS:
        -Dconfig.file=${opt:stage}.conf
        -DSM_CREDENTIAL_PROVIDER=default
        ${self:custom.file.proxySystemProperty, ''}
  okta-sync-simple:
    handler: com.simonmarkets.users.OktaSyncEventHandler::handleRequest
    warmup:
      default:
        enabled: false
    memorySize: 1024
    timeout: 900
    reservedConcurrency: 10
    environment:
      JAVA_TOOL_OPTIONS:
        -Dconfig.file=${opt:stage}.conf
        -DSM_CREDENTIAL_PROVIDER=default
        ${self:custom.file.proxySystemProperty, ''}
  okta-sync-network-mfa:
    handler: com.simonmarkets.users.OktaSyncEventHandler::handleRequest
    warmup:
      default:
        enabled: false
    memorySize: 1024
    timeout: 900
    reservedConcurrency: 10
    environment:
      JAVA_TOOL_OPTIONS:
        -Dconfig.file=${opt:stage}.conf
        -DSM_CREDENTIAL_PROVIDER=default
        ${self:custom.file.proxySystemProperty, ''}
  master-user:
    handler: com.simonmarkets.users.MasterUserEventHandler::handleRequest
    warmup:
      default:
        enabled: false
    memorySize: 1024
    timeout: 300
    reservedConcurrency: 0
    events:
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusUsers}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"
            detail:
              ns:
                db:
                  - ${self:custom.file.dbUsers}
                coll:
                  - ${self:custom.file.collectionUsers}
          inputTransformer:
            inputPathsMap:
              id: "$.id"
              time: "$.time"
              detail: "$.detail"
            inputTemplate: '{"id": <id>, "time": <time>, "detail":<detail>}'
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusNetworks}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"
            detail:
              ns:
                db:
                  - ${self:custom.file.dbUsers}
                coll:
                  - ${self:custom.file.collectionNetworks}
          inputTransformer:
            inputPathsMap:
              id: "$.id"
              time: "$.time"
              detail: "$.detail"
            inputTemplate: '{"id": <id>, "time": <time>, "detail":<detail>}'
    environment:
      JAVA_TOOL_OPTIONS:
        -Dconfig.file=${opt:stage}.conf
        -DSM_CREDENTIAL_PROVIDER=default
        ${self:custom.file.proxySystemProperty, ''}


  process-users-upload:
    handler: com.simonmarkets.users.ProcessUsersBatchApp::handleRequest
    warmup:
      default:
        enabled: false
    memorySize: 2048
    timeout: 60
    environment:
      JAVA_TOOL_OPTIONS:
        -Dconfig.file=${opt:stage}.conf
        -DSM_CREDENTIAL_PROVIDER=default
        ${self:custom.file.proxySystemProperty, ''}

resources:
  Resources:
    ManualUserEventBus:
      Type: AWS::Events::EventBus
      Properties:
        Name: ManualUserEvents

    ManualUserEventBusPolicy:
      Type: AWS::Events::EventBusPolicy
      Properties:
        EventBusName:
          Fn::GetAtt:
            - ManualUserEventBus
            - Name
        StatementId: "ManualUserEventBusPolicy"
        Statement:
          Effect: "Allow"
          Principal:
            AWS: ${self:custom.file.provider.iam.role}
          Action: "events:PutEvents"
          Resource:
            Fn::GetAtt:
              - ManualUserEventBus
              - Arn

    ManualUserEventRule:
      Type: AWS::Events::Rule
      Properties:
        Name: "Manual-user-okta-sync"
        EventBusName:
          Fn::GetAtt:
          - ManualUserEventBus
          - Name

        EventPattern:
          source: [ "ManualOktaSync" ]
        State: ENABLED
        Targets:
          - Arn:
              Fn::GetAtt:
              - OktaDashsyncDashsimpleLambdaFunction
              - Arn
            Id: "okta-sync-simple"

    ManualUserOktaSyncPolicy:
      Type: AWS::Lambda::Permission
      Properties:
        Action: lambda:InvokeFunction
        FunctionName:
          Fn::GetAtt:
          - OktaDashsyncDashsimpleLambdaFunction
          - Arn
        Principal: events.amazonaws.com

    NetworkMfaEventRule:
      Type: AWS::Events::Rule
      Properties:
        Name: "Network-mfa-okta-sync"
        EventBusName:
          Fn::GetAtt:
            - ManualUserEventBus
            - Name

        EventPattern:
          source: [ "NetworkMfaOktaSync" ]
        State: ENABLED
        Targets:
          - Arn:
              Fn::GetAtt:
                - OktaDashsyncDashnetworkDashmfaLambdaFunction
                - Arn
            Id: "okta-sync-network-mfa"

    NetworkMfaOktaSyncPolicy:
      Type: AWS::Lambda::Permission
      Properties:
        Action: lambda:InvokeFunction
        FunctionName:
          Fn::GetAtt:
            - OktaDashsyncDashnetworkDashmfaLambdaFunction
            - Arn
        Principal: events.amazonaws.com