### Mongo configuration

Please make sure you are running mongo in clustered mode in local environment, otherwise inserts into mongo will fail.
In order to run it in clustered mode please follow next steps (Those steps are described on this page https://docs.mongodb.com/manual/tutorial/deploy-shard-cluster/):

1. Add `replSet` and `bind_ip` parameters to your mongo command, it should look something like this:
```
mongod --replSet=rs0 --bind_ip localhost
```
2. Connect to mongo shell using following command:
```
mongo --host <hostname> --port <port>
```
3. In mongo shell initiate replica set with following command
```
rs.initiate() 
```
You need to do steps (2) and (3) only once.

### Triggers configuration

Service-user works with mongo triggers. In order to reduce payload size please use following
`projection` in mongo atlas trigger config:

Users (case class UserPayload): 

```json
{
  "ns": 1,
  "account": 1,
  "operationType": 1,
  "fullDocument.id": 1,
  "fullDocument.email": 1,
  "fullDocument.version": 1,
  "fullDocument.networkId": 1,
  "fullDocument.createdAt": 1,
  "fullDocument.createdBy": 1,
  "fullDocument.updatedAt": 1,
  "fullDocument.updatedBy": 1,
  "fullDocument.firstName": 1,
  "fullDocument.lastName": 1,
  "fullDocument.roles": 1
}
```

Networks (case class NetworkPayload):
```json
{
  "ns": 1,
  "account": 1,
  "operationType": 1,
  "fullDocument.id": 1,
  "fullDocument.name": 1,
  "fullDocument.version": 1
}
```

