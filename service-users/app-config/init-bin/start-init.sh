#!/bin/bash
mkdir -p /var/cv/creds
echo "Copying simon-vertx-ssl.jks from S3 in [$config_env] env"

if [[ $config_env == "prod" ]]
then
   aws s3 cp s3://prod-simon-config-data/creds/app-common/simon-vertx-ssl.jks /var/cv/creds/simon-vertx-ssl.jks
elif [[ $config_env == "uat-us" ]]
then
   aws s3 cp s3://uat-icapital-config-data-us/creds/app-common/simon-vertx-ssl.jks /var/cv/creds/simon-vertx-ssl.jks
elif [[ $config_env == "uat-eu" ]]
then
   aws s3 cp s3://uat-icapital-config-data/creds/app-common/simon-vertx-ssl.jks /var/cv/creds/simon-vertx-ssl.jks
elif [[ $config_env == "qa" ]]
then
   aws s3 cp s3://qa-simon-config-data/creds/app-common/simon-vertx-ssl.jks /var/cv/creds/simon-vertx-ssl.jks
elif [[ $config_env == "alpha" ]]
then
   aws s3 cp s3://alpha-simon-config-data/creds/app-common/simon-vertx-ssl.jks /var/cv/creds/simon-vertx-ssl.jks
elif [[ $config_env == "okteto" ]]
then
   echo Skipped
else
  echo "[ERROR] - Environment Specified [$config_env] doesn't exist, so exit..."
  exit 255
fi
