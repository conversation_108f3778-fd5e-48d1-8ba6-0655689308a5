package com.goldmansachs.marquee.pipg.service.networks

import com.goldmansachs.marquee.pipg.NetworkType.HedgeProvider
import com.goldmansachs.marquee.pipg.bson.{LegacyNetworkFormats, NetworkFormat}
import com.goldmansachs.marquee.pipg.mongo._
import com.goldmansachs.marquee.pipg.service.transaction.TransactionRepository
import com.goldmansachs.marquee.pipg.{TestBlockingIODispatcher, _}
import com.gs.marquee.foundation.util.AuditRequestContext
import com.gs.marquee.foundation.util.AwaitTimeout._
import com.gs.marquee.simon.http.Operation.Result
import com.gs.marquee.simon.http.Operation.Result._
import com.gs.marquee.util.embedded.EmbeddedMongo
import com.mongodb.casbah.MongoDB
import com.mongodb.casbah.commons.conversions.scala.RegisterJodaTimeConversionHelpers
import com.simonmarkets.networks.ExternalId
import org.scalatest.OptionValues._
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, BeforeAndAfterAll, FunSpec, Matchers}
import simon.Id.NetworkId

import scala.concurrent.Future

class NetworksRepositorySpec
  extends FunSpec
    with Matchers
    with BeforeAndAfter
    with BeforeAndAfterAll
    with MockitoSugar
    with LegacyNetworkFormats {

  private implicit val biod = TestBlockingIODispatcher
  private implicit val ec = TestBlockingIODispatcher.executionContext

  val db: MongoDB = EmbeddedMongo.client("NetworksRepositorySpec")

  private val networksCol = db("networks").async

  private val networksSnapshotsCol = db("networks.snapshots").async

  private val transactionsCol = db("transactions").async

  RegisterJodaTimeConversionHelpers()

  before {
    networksCol.remove($)
    networksSnapshotsCol.remove($)
  }

  val transactionsRepo = new TransactionRepository.Mongo(transactionsCol)

  val networksRepo = new NetworksRepository.Mongo(
    networksCol,
    networksSnapshotsCol,
    transactionsRepo,
    (ids: Set[String]) => Future(ids.zip(ids).toMap))

  val context = AuditRequestContext.empty

  describe("Networks Repository") {

    it("should persist network snapshot when inserted") {
      val upsertedNetwork = Network(
        id = NetworkId("network1"),
        networkName = "network1",
        idHubOrganization = IdHubOrganization(1234, "foo"),
        cc = Some(List("1@email", "2@email")),
        to = Some(List("1@email", "2@email")),
        salesFeeRuleIds = List("x", "y"),
        approverSet = Map("foo" -> List(List("userId"))),
        purviewNetworks = Some(Set(IssuerPurview(NetworkId("Network 1"), Set("JPM"), Some(NetworkId("wholesaler")), Some(Set(PurviewedDomain.Accounts))))),
        payoffEntitlements = Map("a" -> Map("b" -> List("c", "d"))),
        payoffEntitlementsV2 = Map("a" -> Map("b" -> Set(Network.Action("c", Map.empty),
          Network.Action("d", contractParams = Map("feeOrCommission" -> Set("fee")))))),
        capabilities = Map("c1" -> List("a", "b", "c")),
        distributorAlias = Some(ExternalAlias("rayj", ".*")),
        omsAlias = Some(OmsAlias.TWD.name)
      )

      networksRepo.insert("bob", upsertedNetwork, Some("comment")).await

      val snapshot = networksSnapshotsCol.findOne().await.opt map NetworkFormat.snapshot.read

      snapshot.value.comment.value shouldBe "comment"
      snapshot.value.userId shouldBe "bob"
      snapshot.value.entity shouldBe upsertedNetwork

      networksSnapshotsCol.count().await shouldBe 1

      networksRepo.snapshot(snapshot.value.id).await shouldBe Ok(upsertedNetwork)
      val res = networksRepo.findNetworkById(NetworkId("network1")).await
      res shouldBe Ok(upsertedNetwork)
    }

    it("should persist purview networks when inserted") {
      val purview1 = IssuerPurview(NetworkId("Network 1"), Set("JPM"))
      val purview2 = IssuerPurview(NetworkId("Network 4"), Set("GS"), Some(NetworkId("Wholesaler")),
        Some(Set(PurviewedDomain.Accounts, PurviewedDomain.Users, PurviewedDomain.EditRfqs, PurviewedDomain.ViewRfqs))
      )
      val hpNetwork = Network(
        id = NetworkId("TEST_HPN_UNIT"),
        networkName = "TEST_HPN_UNIT",
        idHubOrganization = IdHubOrganization(1234, "foo"),
        approverSet = Map(
          "foo" -> List(
            List("a", "b", "c"),
            List("d", "e", "f")),
          "bar" -> List(
            List("d", "e", "f"),
            List("a", "b", "c"))),
        accountMappings = None,
        networkTypes = Some(List(HedgeProvider)),
        to = Some(List("<EMAIL>")),
        cc = Some(List("<EMAIL>")),
        purviewNetworks = None)

      networksRepo.insert("userId", hpNetwork, None).await shouldBe Ok(hpNetwork)
      networksRepo.update("userId", hpNetwork.copy(purviewNetworks = Some(Set(purview1, purview2))), None, true)
      networksRepo.findNetworkById(hpNetwork.id).await.get.purviewNetworks shouldBe Some(Set(purview1, purview2))
    }

    it("should increment the version and persist snapshot on update"){
      val upsertedNetwork = Network(
        id = NetworkId("network1"),
        networkName = "network1",
        idHubOrganization = IdHubOrganization(1234, "foo"),
        approverSet = Map.empty)

      networksRepo.insert("bob", upsertedNetwork, Some("comment")).await

      val updateResult = networksRepo.update("bob", networksRepo.findNetworkById(NetworkId("network1")).await.get, Some("comment")).await

      updateResult.isSuccess shouldEqual true
      networksRepo.findNetworkById(NetworkId("network1")).await.get.version shouldBe 1
      networksSnapshotsCol.count().await shouldBe 2
    }

    it("should conflict when version is updated"){
      val upsertedNetwork = Network(
        id = NetworkId("network1"),
        networkName = "network1",
        idHubOrganization = IdHubOrganization(1234, "foo"),
        approverSet = Map.empty)

      networksRepo.insert("bob", upsertedNetwork, Some("comment")).await
      val network = networksRepo.findNetworkById(NetworkId("network1")).await.get
      networksRepo.update("bob", network, Some("comment")).await
      val result = networksRepo.update("bob", network, Some("comment")).await
      result.isSuccess shouldBe false
    }

    it("should return a list of client mappings") {
      upsertNetworkAssertion("Network 1")
      upsertNetworkAssertion("Network 2")

      val result = networksRepo.clientAccountMappings(NetworkId("Network 1")).await
      result should equal(List("Network 1-mapping-1"))
    }

    it("accepts hedge provider properties") {
      val testHedgeProvider = Network(
        id = NetworkId("Test Hedge Provider Network"),
        networkName = "Test Hedge Provider Network",
        idHubOrganization = IdHubOrganization(1234, "foo"),
        approverSet = Map.empty,
        accountMappings = Some(List("thpn-mapping-1")),
        networkTypes = Some(List(HedgeProvider)),
        cc = Some(List("<EMAIL>")),
        to = Some(List("<EMAIL>")))

      networksRepo.insert("userId", testHedgeProvider, None).await shouldBe Ok(testHedgeProvider)
    }

    it("should return external alias") {
      val upsertedNetwork = Network(
        id = NetworkId("network1"),
        networkName = "network1",
        idHubOrganization = IdHubOrganization(1234, "foo"),
        approverSet = Map.empty,
        distributorAlias = Some(ExternalAlias("rayj", ".*")))

      networksRepo.insert("bob", upsertedNetwork, Some("comment")).await

      networksRepo.distributorAlias(NetworkId("network1")).await.get shouldBe upsertedNetwork.distributorAlias
    }

    it("should remove omsId if absent") {
      val upsertedNetwork = Network(
        id = NetworkId("network1"),
        networkName = "network1",
        idHubOrganization = IdHubOrganization(1234, "foo"),
        approverSet = Map.empty,
        omsAlias = Some("twd"))

      networksRepo.insert("bob", upsertedNetwork, Some("comment")).await
      networksRepo.omsAlias(NetworkId("network1")).await.get shouldBe Some("twd")
      networksRepo.update("bob", upsertedNetwork.copy(omsAlias = None), Some("comment")).await

      networksRepo.omsAlias(NetworkId("network1")).await.get shouldBe None
    }

    it("should remove external alias if absent") {
      val upsertedNetwork = Network(
        id = NetworkId("network1"),
        networkName = "network1",
        idHubOrganization = IdHubOrganization(1234, "foo"),
        approverSet = Map.empty,
        distributorAlias = Some(ExternalAlias("rayj", ".*")))

      networksRepo.insert("bob", upsertedNetwork, Some("comment")).await
      networksRepo.distributorAlias(NetworkId("network1")).await.get shouldBe upsertedNetwork.distributorAlias

      networksRepo.update("bob", upsertedNetwork.copy(distributorAlias = None), Some("comment")).await

      networksRepo.distributorAlias(NetworkId("network1")).await.get shouldBe None
    }

    describe("networksWithPurviewOver method should return") {
      it("set of networks when these networks have purview over given network") {
        upsertNetworkAssertion("Network 1", Set.empty)
        upsertNetworkAssertion("Network 2", Set(IssuerPurview(NetworkId("Network 1"), Set("JPM")), IssuerPurview(NetworkId("Network 4"), Set("GS"))))
        upsertNetworkAssertion("Network 3", Set(IssuerPurview(NetworkId("Network 1"), Set("GS")), IssuerPurview(NetworkId("Network 4"), Set("JPM"))))
        upsertNetworkAssertion("Network 4", Set.empty)
        networksRepo.networksWithProductPurviewOver(NetworkId("Network 4"), Some("JPM")).await shouldBe Set("Network 3")
      }
      it("empty set when no networks have purview over given network") {
        upsertNetworkAssertion("Network 1", Set.empty)
        upsertNetworkAssertion("Network 2", Set.empty)
        networksRepo.networksWithProductPurviewOver(NetworkId("Network 1"), Some("GS")).await shouldBe Set.empty
      }
      it("empty set when given network doesn't exist") {
        networksRepo.networksWithProductPurviewOver(NetworkId("Network 1"), Some("GS")).await shouldBe Set.empty
      }
    }
    describe("approverChain method should return") {
      it("Some list of approvals when such approver chain exists") {
        upsertNetworkAssertion("Network 1", approverSet = Map("a" -> List(List("b", "c"))))
        networksRepo.approverChain(NetworkId("Network 1"), "a").await.get shouldBe List(List("b", "c"))
      }
      it("None when network with such id exists, but any without approverSet") {
        upsertNetworkAssertion("Network 1")
        networksRepo.approverChain(NetworkId("Network 1"), "a").await.get shouldBe Nil
      }
      it("None when network with such id and with approverSet exists but there's no such key in approverSet") {
        upsertNetworkAssertion("Network 1", approverSet = Map("a" -> List(List("b", "c"))))
        networksRepo.approverChain(NetworkId("Network 1"), "b").await.get shouldBe Nil
      }
      it("None when no such network exist") {
        networksRepo.approverChain(NetworkId("Network 1"), "a").await shouldBe NotFound
      }
    }

    describe("find networks") {
      it("by email returns a valid network") {
        val hpNetwork = Network(
          id = NetworkId("TEST_HPN_UNIT"),
          networkName = "TEST_HPN_UNIT",
          idHubOrganization = IdHubOrganization(1234, "foo"),
          approverSet = Map.empty,
          accountMappings = None,
          networkTypes = Some(List(HedgeProvider)),
          to = Some(List("<EMAIL>")),
          cc = Some(List("<EMAIL>")),
          purviewNetworks = None)

        networksRepo.insert("userId", hpNetwork, None).await shouldBe Ok(hpNetwork)
        networksRepo.findNetworkByEmail("<EMAIL>").await shouldBe Ok(hpNetwork)
        networksRepo.findNetworkByEmail("<EMAIL>").await shouldBe Ok(hpNetwork)
      }

      it("by external id") {
        val externalId = ExternalId("testSubject", "testId")
        val network = Network(
          id = NetworkId("TEST_HPN_UNIT"),
          networkName = "TEST_HPN_UNIT",
          idHubOrganization = IdHubOrganization(1234, "foo"),
          externalIds = Set(externalId)
        )

        networksRepo.insert("userId", network, None).await shouldBe Ok(network)
        networksRepo.findNetworkByExternalId(externalId).await shouldBe Ok(network)
      }
    }

    describe("removeFromApprovers") {
      it("returns network with specified approver removed") {
        val hpNetwork = Network(
          id = NetworkId("TEST_HPN_UNIT"),
          networkName = "TEST_HPN_UNIT",
          idHubOrganization = IdHubOrganization(1234, "foo"),
          approverSet = Map(
            "foo" -> List(
              List("a", "b", "c"),
              List("d", "e", "f")),
            "bar" -> List(
              List("d", "e", "f"),
              List("a", "b", "c"))),
          accountMappings = None,
          networkTypes = Some(List(HedgeProvider)),
          to = Some(List("<EMAIL>")),
          cc = Some(List("<EMAIL>")),
          purviewNetworks = None)

        networksRepo.insert("userId", hpNetwork, None).await shouldBe Ok(hpNetwork)
        networksRepo.removeFromApprovers(NetworkId("TEST_HPN_UNIT"), "b")("me", None).await shouldBe Ok(Network(
          id = NetworkId("TEST_HPN_UNIT"),
          networkName = "TEST_HPN_UNIT",
          idHubOrganization = IdHubOrganization(1234, "foo"),
          approverSet = Map(
            "foo" -> List(
              List("a", "c"),
              List("d", "e", "f")),
            "bar" -> List(
              List("d", "e", "f"),
              List("a", "c"))),
          accountMappings = None,
          networkTypes = Some(List(HedgeProvider)),
          to = Some(List("<EMAIL>")),
          cc = Some(List("<EMAIL>")),
          purviewNetworks = None))
      }
    }


    describe("entitled method tests") {
      val correctEntitlements = Set("correctEntitlement")
      val incorrectEntitlements = Set("incorrectEntitlements")

      it("entitledFindNetworkById") {
        val (upsertedNetwork, _) = upsertNetworkHelper("network1", approverSet = Map("foo" -> List(List("userId"))), entitlements = correctEntitlements)
        val res1 = networksRepo.entitledFindNetworkById(NetworkId("network1"), Set.empty[String]).await
        val res2 = networksRepo.entitledFindNetworkById(NetworkId("network1"), correctEntitlements).await

        res1 shouldBe Result.NotFound
        res2 shouldBe upsertedNetwork
      }

      it("entitledFindNetworks") {
        val (upsertedNetwork, _) = upsertNetworkHelper("network1", approverSet = Map("foo" -> List(List("userId"))), entitlements = correctEntitlements)
        val res1 = networksRepo.entitledFindNetworks(Set.empty[String]).await
        val res2 = networksRepo.entitledFindNetworks(correctEntitlements).await

        res1 shouldBe List.empty
        res2 shouldBe List(upsertedNetwork.get)
      }

      it("entitledNetworksWithProductPurviewOver - set of networks when these networks have purview AND entitlements over given network") {
        upsertNetworkHelper("Network 1", Set.empty, entitlements = correctEntitlements)
        upsertNetworkHelper("Network 2", Set(IssuerPurview(NetworkId("Network 1"), Set("JPM")), IssuerPurview(NetworkId("Network 4"), Set("GS"))), entitlements = correctEntitlements)
        upsertNetworkHelper("Network 3", Set(IssuerPurview(NetworkId("Network 1"), Set("GS")), IssuerPurview(NetworkId("Network 4"), Set("JPM"))), entitlements = correctEntitlements)
        upsertNetworkHelper("Network 4", Set.empty, entitlements = correctEntitlements)
        networksRepo.entitledNetworksWithProductPurviewOver(NetworkId("Network 4"), Some("JPM"), correctEntitlements).await shouldBe Set("Network 3")
      }

      it("entitledNetworksWithProductPurviewOver - empty set of networks when these networks have purview BUT NOT entitlements over given network") {
        upsertNetworkHelper("Network 1", Set.empty, entitlements = correctEntitlements)
        upsertNetworkHelper("Network 2", Set(IssuerPurview(NetworkId("Network 1"), Set("JPM")), IssuerPurview(NetworkId("Network 4"), Set("GS"))), entitlements = correctEntitlements)
        upsertNetworkHelper("Network 3", Set(IssuerPurview(NetworkId("Network 1"), Set("GS")), IssuerPurview(NetworkId("Network 4"), Set("JPM"))), entitlements = correctEntitlements)
        upsertNetworkHelper("Network 4", Set.empty, entitlements = correctEntitlements)
        networksRepo.entitledNetworksWithProductPurviewOver(NetworkId("Network 4"), Some("JPM"), incorrectEntitlements).await shouldBe Set.empty
      }

      it("entitledNetworksWithProductPurviewOver - empty set when no networks have purview over given network") {
        upsertNetworkHelper("Network 1", Set.empty, entitlements = correctEntitlements)
        upsertNetworkHelper("Network 2", Set.empty, entitlements = correctEntitlements)
        networksRepo.entitledNetworksWithProductPurviewOver(NetworkId("Network 1"), Some("GS"), correctEntitlements).await shouldBe Set.empty
      }

      it("entitledNetworksWithProductPurviewOver - empty set when given network doesn't exist") {
        networksRepo.entitledNetworksWithProductPurviewOver(NetworkId("Network 1"), Some("GS"), correctEntitlements).await shouldBe Set.empty
      }
    }

    describe("findNetworksByCategory") {
      it("returns networks with requested category") {
        upsertNetworkHelper("Network 1", networkTypes = None)
        upsertNetworkHelper("Network 2", networkTypes = Some(List(NetworkType.Issuer)))
        upsertNetworkHelper("Network 3", networkTypes = Some(List(NetworkType.RIA, NetworkType.Issuer)))
        upsertNetworkHelper("Network 4", networkTypes = Some(List(NetworkType.PrivateBank)))

        networksRepo.findNetworksByCategory(NetworkCategory.Issuer).await.map(NetworkId unwrap _.id) shouldBe Seq("Network 2", "Network 3")
      }
    }
  }

  private def upsertNetworkHelper(name: String, purviewNetworks: Set[IssuerPurview] = Set.empty,
      approverSet: Map[String, List[List[String]]] = Map.empty, entitlements: Set[String] = Set.empty,
      networkTypes: Option[List[NetworkType]] = None) = {
    val network = Network(
      NetworkId(name),
      name,
      idHubOrganization = IdHubOrganization(1234, "foo"),
      approverSet = approverSet,
      accountMappings = Some(List(s"$name-mapping-1")),
      purviewNetworks = if (purviewNetworks.isEmpty) None else Some(purviewNetworks),
      entitlements = entitlements,
      networkTypes = networkTypes
    )
    (networksRepo.insert("userId", network, None).await, Ok(network))
  }

  private def upsertNetworkAssertion(name: String, purviewNetworks: Set[IssuerPurview] = Set.empty, approverSet: Map[String, List[List[String]]] = Map.empty) = {
    val (actual, expected) = upsertNetworkHelper(name, purviewNetworks, approverSet)
    actual shouldBe expected
  }
}
