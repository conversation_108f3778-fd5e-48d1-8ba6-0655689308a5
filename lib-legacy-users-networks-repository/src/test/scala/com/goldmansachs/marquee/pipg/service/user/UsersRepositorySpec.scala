package com.goldmansachs.marquee.pipg.service.user

import com.goldmansachs.marquee.pipg.User.DistributorInfo
import com.goldmansachs.marquee.pipg.{UserRole, _}
import com.goldmansachs.marquee.pipg.bson.UserFormat
import com.goldmansachs.marquee.pipg.mongo._
import com.goldmansachs.marquee.pipg.service.networks.NetworksRepository
import com.goldmansachs.marquee.pipg.service.transaction.TransactionRepository
import com.gs.marquee.mongo.FakeMongoSuite
import com.gs.marquee.simon.http.Operation._
import com.gs.marquee.util.{BlockingIODispatcher, CurrentThreadExecutionContext}
import com.mongodb.casbah.MongoDB
import com.simonmarkets.shared.MaskedId
import org.scalatest.Matchers._
import org.scalatest.OptionValues._
import org.scalatest.WordSpec
import org.scalatest.concurrent.ScalaFutures
import simon.Id
import simon.Id.NetworkId

import java.time.temporal.ChronoUnit
import java.time.{LocalDateTime, ZoneId}

import scala.concurrent.{ExecutionContext, Future}


class UsersRepositorySpec extends WordSpec with FakeMongoSuite with ScalaFutures {

  import UsersRepositorySpec._

  implicit def biod: BlockingIODispatcher = TestBlockingIODispatcher

  implicit def ec: ExecutionContext = CurrentThreadExecutionContext

  val mongoDb: MongoDB = client("pipg")

  val (usersColl, networksColl, usersSnapshotsCol, transactionColl) = {
    UsersRepository.Mongo.migrate(mongoDb)
    val usersColl = mongoDb(UsersRepository.Mongo.CollectionName).async
    val networksColl = mongoDb(NetworksRepository.Mongo.CollectionName).async
    val usersSnapshotsCol = mongoDb(UsersRepository.Mongo.CollectionName + ".snapshots").async
    val transactionColl = mongoDb("transactions").async
    (usersColl, networksColl, usersSnapshotsCol, transactionColl)
  }

  before {
    usersColl remove $
    networksColl remove $
    usersSnapshotsCol remove $
    transactionColl remove $

    /** Pretend we have 2 networks defined
     * - id = net1
     * - id = net2
     */
    for {networkId <- List(networkId1, networkId2)}
      networksColl.insert($("id" -> networkId))
  }

  val repository = new UsersRepository.Mongo(networksColl,
    usersColl,
    usersSnapshotsCol,
    new TransactionRepository.Mongo(transactionColl))

  "Users Repository" should {

    "return a list of users" in {
      preInsert(user1, user2)

      val result = repository.findAll.futureValue
      result should contain only(user1, user2)
    }

    "return a specific user" in {
      preInsert(user1, user2)

      val result = repository.findById(user1.id).future.futureValue
      result shouldEqual user1.ok
    }

    "return not-found for absent user" in {
      val result = repository.findById(user1.id).future.futureValue
      result shouldBe NotFound
    }

    "be able to find out if user exists using id" in {
      preInsert(user1)

      repository.existsById(user1.id).futureValue shouldBe true
      repository.existsById(user2.id).futureValue shouldBe false
    }

    "be able to find out if user exists using email" in {
      preInsert(user1)

      repository.existsByEmail(user1.email).futureValue shouldBe true
      repository.existsByEmail(user2.email).futureValue shouldBe false
    }

    "be able to entitled find out if user exists using email" in {
      preInsert(user1, user2)

      repository.entitledFindByEmails(Set("admin"), None, Set(user1.email)).futureValue.toList shouldBe List(user1)
      repository.entitledFindByEmails(Set("admin"), None, Set(user2.email)).futureValue.toList shouldBe List.empty //No entitlements on user2
    }

    "be able to find users if properly entitled" in {
      preInsert(user1, user2, user3)

      repository.entitledFindAllWithInactive(Set("admin"), UsersLookup.All).futureValue.toList shouldBe List(user1, user3)
      repository.entitledFindAllWithInactive(Set("admin"), UsersLookup.Email("email3")).futureValue.toList shouldBe List(user3)
    }

    "be able to find users by npn" in {
      preInsert(user1)

      repository.entitledFindByNpns(Set("admin"), None, Set(npnNum)).futureValue.toList shouldBe
        List(user1)
    }

    "be able to find out if user exists using distributorId and networkId" in {
      preInsert(user1, user2)

      repository.entitledFindByDistributorId(Set("admin"), user1.networkId, user1LPLId).future.futureValue shouldBe user1.ok
      repository.entitledFindByDistributorId(Set("admin"), user2.networkId, "user2").future.futureValue shouldBe NotFound(None)
    }

    "handle deactivation" in {
      preInsert(user1)
      user1.omsId shouldNot be(None)
      user1.distributorId shouldNot be(None)

      val result = repository.deactivate("me", user1.id).future.futureValue
      result shouldBe a[Result.Ok[_]]

      val user = result.get
      user.roles shouldBe Set.empty
      user.omsId shouldBe None
      user.distributorId shouldBe None
      user.isActive shouldBe false
      user.updatedBy shouldBe "me"
      usersSnapshotsCol.findOne().future.futureValue shouldBe a[Result.Ok[_]]
    }

    "handle deactivation (not-found)" in {
      preInsert(user1)

      val result = repository.deactivate("me", user2.id).future.futureValue
      result shouldBe NotFound
      usersSnapshotsCol.findOne().future.futureValue shouldBe a[Result.NotFound]
    }

    "handle insert" in {
      val result = repository.insert(
        "me",
        "some-id",
        networkId1,
        "some-email",
        "some-first-name",
        "some-last-name",
        Some("john.Doe1"),
        Some("some-twd-id"),
        tradewebEligible = false,
        regSEligible = false,
        Set(UserRole.EqPIPGGSAdmin),
        Set("location1"),
        Set("faNumber1"),
        Set("customRole1"),
        Set(License.CRD("number")),
        Set("entitlement1"),
        Set(MaskedId("target", "id")),
        None,
        cusips = Set("cusip"),
        idpLoginId = "idpLoginId"
      ).future.futureValue

      result shouldBe a[Result.Ok[_]]

      val user = result.get
      user.id shouldBe "some-id"
      user.networkId shouldBe networkId1
      user.email shouldBe "some-email"
      user.firstName shouldBe "some-first-name"
      user.lastName shouldBe "some-last-name"
      user.distributorId shouldBe Some("john.Doe1")
      user.omsId shouldBe Some("some-twd-id")
      user.roles should contain only UserRole.EqPIPGGSAdmin
      user.createdBy shouldBe "me"
      user.updatedBy shouldBe "me"
      user.emailSentBy shouldBe 'empty
      user.emailSentAt shouldBe 'empty
      user.locations shouldBe Set("location1")
      user.faNumbers shouldBe Set("faNumber1")
      user.customRoles shouldBe Set("customRole1")
      user.licenses shouldBe Set(License.CRD("number"))
      user.entitlements shouldBe Set("entitlement1")
      user.maskedIds shouldBe Set(MaskedId("target", "id"))
      user.cusips shouldBe Set("cusip")

      usersColl.findOne($("id" -> "some-id")).future.futureValue.isSuccess shouldBe true
      usersSnapshotsCol.findOne().future.futureValue shouldBe a[Result.Ok[_]]
    }

    "handle insert (if user with specified ID already exists)" in {
      preInsert(user1)

      val result = repository.insert(
        "me",
        user1.id,
        networkId1,
        "some-email",
        "some-first-name",
        "some-last-name",
        Some("john.Doe1"),
        Some("some-twd-id"),
        false,
        false,
        Set(UserRole.EqPIPGGSAdmin),
        Set("location1"),
        Set("faNumber1"),
        Set("customRole1"),
        Set(License.CRD("number")),
        Set("entitlement1"),
        Set(MaskedId("target", "id")),
        None,
        idpLoginId = "idpLoginId"
      ).future.futureValue

      result shouldBe a[Result.Conflict]
      usersSnapshotsCol.findOne().future.futureValue shouldBe a[Result.NotFound]
    }

    "handle insert (if user with specified EMAIL already exists)" in {
      preInsert(user1)

      val result = repository.insert(
        "me",
        "some-id",
        networkId1,
        user1.email,
        "some-first-name",
        "some-last-name",
        Some("john.Doe1"),
        Some("some-twd-id"),
        false,
        false,
        Set(UserRole.EqPIPGGSAdmin),
        Set("location1"),
        Set("faNumber1"),
        Set("customRole1"),
        Set(License.CRD("number")),
        Set("entitlement1"),
        Set(MaskedId("target", "id")),
        None,
        idpLoginId = "idpLoginId").future.futureValue
      result shouldBe a[Result.Conflict]
      usersSnapshotsCol.findOne().future.futureValue shouldBe a[Result.NotFound]
    }

    "handle insert (if specified network doesn't exist)" in {
      val result = repository.insert(
        "me",
        "some-id",
        NetworkId("some-absent-network-id"),
        user1.email,
        "some-first-name",
        "some-last-name",
        Some("john.Doe1"),
        Some("some-twd-id"),
        false,
        false,
        Set(UserRole.EqPIPGGSAdmin),
        Set("location1"),
        Set("faNumber1"),
        Set("customRole1"),
        Set(License.CRD("number")),
        Set("entitlement1"),
        Set(MaskedId("target", "id")),
        None,
        idpLoginId = "idpLoginId").future.futureValue

      result shouldBe a[Result.BadRequest]
      usersSnapshotsCol.findOne().future.futureValue shouldBe a[Result.NotFound]
    }

    "handle update" in {
      preInsert(user1, user2)

      val distributorInfo = DistributorInfo(Some("distributor-role"), Some("distributor-branch"), Some("distributor-subgroup"), Some(true))
      val result = repository.updateUser(
        "me",
        user1.id,
        networkId1,
        "some-email",
        "some-first-name",
        "some-last-name",
        Some("john.Doe1"),
        None,
        false,
        false,
        Set(UserRole.EqPIPGGSAdmin),
        Set("location1"),
        Set("faNumber1"),
        Set("customRole1"),
        Set(License.CRD("number")),
        Set("entitlement1"),
        None,
        Some(distributorInfo),
        cusips = Set("cusip")
      ).future.futureValue

      result shouldBe a[Result.Ok[_]]

      val user = result.get
      user.networkId shouldBe networkId1
      user.email shouldBe "some-email"
      user.firstName shouldBe "some-first-name"
      user.lastName shouldBe "some-last-name"
      user.distributorId shouldBe Some("john.Doe1")
      user.omsId shouldBe None
      user.roles should contain only UserRole.EqPIPGGSAdmin
      user.updatedBy shouldBe "me"
      user.locations shouldBe Set("location1")
      user.faNumbers shouldBe Set("faNumber1")
      user.customRoles shouldBe Set("customRole1")
      user.licenses shouldBe Set(License.CRD("number"))
      user.entitlements shouldBe Set("entitlement1")
      user.version shouldBe 1
      user.distributorInfo shouldBe Some(distributorInfo)
      user.cusips shouldBe Set("cusip")

      usersSnapshotsCol.findOne().future.futureValue shouldBe a[Result.Ok[_]]

      val u2 = usersColl.findOne($("id" -> user2.id), $("email" -> 1)).future.futureValue
      u2.isSuccess shouldBe true
      u2.get.get("email") should not be "some-email"
    }

    "handle update (if specified ID not found)" in {
      preInsert(user1, user2)

      val result = repository.updateUser(
        "me",
        "some-id",
        networkId1,
        "some-email",
        "some-first-name",
        "some-last-name",
        Some("john.Doe1"),
        Some("some-twd-id"),
        false,
        false,
        Set(UserRole.EqPIPGGSAdmin),
        Set("location1"),
        Set("faNumber1"),
        Set("customRole1"),
        Set(License.CRD("number")),
        Set("entitlement1"),
        None,
        None).future.futureValue

      result shouldBe Result.NotFound
      usersSnapshotsCol.findOne().future.futureValue shouldBe a[Result.NotFound]
    }

    "handle update (if specified network doesn't exist)" in {
      preInsert(user1, user2)

      val result = repository.updateUser(
        "me",
        user1.id,
        NetworkId("some-network-id"),
        "some-email",
        "some-first-name",
        "some-last-name",
        Some("john.Doe1"),
        Some("some-twd-id"),
        false,
        false,
        Set(UserRole.EqPIPGGSAdmin),
        Set("location1"),
        Set("faNumber1"),
        Set("customRole1"),
        Set(License.CRD("number")),
        Set("entitlement1"),
        None,
        None).future.futureValue

      result shouldBe a[Result.BadRequest]
      usersSnapshotsCol.findOne().future.futureValue shouldBe a[Result.NotFound]
    }

    "handle deactivate (positive)" in {
      user1.roles should not be 'empty

      preInsert(user1)

      val result = repository.deactivate(
        "me",
        user1.id).future.futureValue

      result shouldBe a[Result.Ok[_]]

      val user = result.get
      user.roles shouldBe 'empty

      val u = usersColl.findOne(
        $("id" -> user1.id),
        $("roles" -> 1, "updatedBy" -> 1)).future.futureValue

      u.isSuccess shouldBe true

      u.get.get("updatedBy") shouldBe "me"
      u.get.getStringList("roles").value shouldBe 'empty
      usersSnapshotsCol.findOne().future.futureValue shouldBe a[Result.Ok[_]]
    }

    "handle deactivate (negative. not found)" in {

      val result = repository.deactivate(
        "me",
        user1.id).future.futureValue

      result shouldBe Result.NotFound
      usersSnapshotsCol.findOne().future.futureValue shouldBe a[Result.NotFound]
    }

    "handle lookups" when {

      "findAllByIds" in {
        preInsert(user1, user2, user3)

        val result = repository.findAllByIds(Set(user1.id, user3.id))
        result.futureValue should contain only(user1, user3)
      }

      "findAllByRoles" in {
        preInsert(user1, user2, user3)

        val result = repository.findAllByRoles(roles1 ++ roles3)
        result.futureValue should contain only(user1, user3)
      }

      "findAllByDynamicRoles" in {
        preInsert(user1, user2, user3)

        val result = repository.findAllByDynamicRoles(dynamicRoles1)
        result.futureValue should contain only user1
      }

      "findAllByIdsAndDynamicRoles" in {
        preInsert(user1, user2, user3)

        val result = repository.findAllByIdsAndDynamicRoles(Set(user1.id, user3.id), dynamicRoles1)
        result.futureValue should contain only user1
      }

      "findById" in {
        preInsert(user1, user2, user3)

        val result = repository.findById(user1.id)
        result.future.futureValue shouldBe user1.ok
      }

      "findByEmail" in {
        preInsert(user1, user2, user3)

        val result = repository.findByEmail(user2.email, user2.networkId)
        result.future.futureValue shouldBe user2.ok
      }

      "findByOmsId" in {
        preInsert(user1, user2, user3)

        val result = repository.findByOmsId(user1Twd)
        result.future.futureValue shouldBe user1.ok
      }

      "findByExternalId" in {
        preInsert(user1, user2, user3)

        val result = repository.findByDistributorId(user1LPLId, user1.networkId)
        result.future.futureValue shouldBe user1.ok
      }

      "findAllByNetworkIdsAndRoles" in {
        preInsert(user1, user2, user3)

        val result = repository.findAllByNetworkIdsAndRoles(Set(user3.networkId), roles3)
        result.futureValue should contain only user3
      }

      "findIdByEmail" in {
        preInsert(user1, user2, user3)

        val result = repository.findIdByEmail(user2.email, user2.networkId)
        result.future.futureValue shouldBe user2.id.ok
      }

      "findEmailById" in {
        preInsert(user1, user2, user3)

        val result = repository.findEmailById(user2.id)
        result.future.futureValue shouldBe user2.email.ok
      }

      "findIdByIdOrEmail" in {
        preInsert(user1, user2, user3)

        val result1 = repository.findIdByIdOrEmail(user2.email)
        result1.future.futureValue shouldBe user2.id.ok

        val result2 = repository.findIdByIdOrEmail(user2.id)
        result2.future.futureValue shouldBe user2.id.ok
      }

      "findIdsByIdsAndRoles" in {
        preInsert(user1, user2, user3)

        val result = repository.findIdsByIdsAndRoles(Set(user3.id), roles1 ++ roles3)
        result.futureValue should contain only user3.id
      }

      "findIdsByNameContains" in {
        preInsert(user1, user2, user3)

        val result1 = repository.findIdsByNameContains("gene")
        result1.futureValue should contain only user1.id

        val result2 = repository.findIdsByNameContains("upa")
        result2.futureValue should contain only user1.id

        val result3 = repository.findIdsByNameContains("gene pry")
        result3.futureValue should contain only user1.id
      }

      "findIdsByNetworkIds" in {
        preInsert(
          user1.copy(networkId = NetworkId("shared-network")),
          user2,
          user3.copy(networkId = NetworkId("shared-network")))

        val result = repository.findIdsByNetworkIds(Set(NetworkId("shared-network")))
        result.futureValue should contain only(user1.id, user3.id)
      }

      "findEmailsByIds" in {
        preInsert(user1, user2, user3)

        val result = repository.findEmailsByIds(Set(user2.id, user3.id))
        result.futureValue shouldEqual Map(
          user2.id -> user2.email,
          user3.id -> user3.email)
      }

      "findNetworkIdsByIds" in {
        preInsert(user1, user2, user3)

        val result = repository.findNetworkIdsByIds(Set(user1.id, user2.id))
        result.futureValue shouldEqual Map(
          user1.id -> user1.networkId,
          user2.id -> user2.networkId)
      }

      "findFirstAndLastNamesAndEmailsByIds" in {
        preInsert(user1, user2, user3)

        val result = repository.findFirstAndLastNamesAndEmailsByIds(Set(user1.id, user3.id))
        result.futureValue shouldEqual Map(
          (user1.id, (user1.firstName, user1.lastName, user1.email)),
          (user3.id, (user3.firstName, user3.lastName, user3.email))
        )
      }
    }

    "updates lastVisitedAt" in {
      val ts = LocalDateTime.now(ZoneId.systemDefault())
      val u1 = user1.copy(lastVisitedAt = None)
      val u2 = user2.copy(lastVisitedAt = Some(ts))

      preInsert(u1, u2)

      // Need this in order to guarantee that lastUpdated will be refreshed with new value
      Thread.sleep(200)

      repository.refreshLastVisitedAt(u1.id).future.futureValue
      repository.refreshLastVisitedAt(u2.id).future.futureValue

      val u1newTs = repository.findById(u1.id).map(_.lastVisitedAt).future.futureValue.opt.flatten
      u1newTs shouldBe 'defined
      u1newTs.value.compareTo(ts) should be > 0

      val u2newTs = repository.findById(u2.id).map(_.lastVisitedAt).future.futureValue.opt.flatten
      u2newTs shouldBe 'defined
      u2newTs.value.compareTo(ts) should be > 0
    }

    "updates isActive" in {
      val inactiveUser1 = user1.copy(isActive = false)

      preInsert(inactiveUser1)

      val result = repository.findById(inactiveUser1.id)
      result.future.futureValue shouldBe NotFound
    }

    "handles oms ID query" in {
      preInsert(user1, user2, user3, user4)
      val result = repository.findByOmsId("ab!twd123")
      result.future.futureValue.get.id shouldBe "id4"

      val result2 = repository.entitledFindByOmsIds(Set("admin"), Set("ab!twd1", user1Twd.toUpperCase))

      result2.futureValue.size shouldBe 2
      result2.futureValue should contain only(user1, user4)
    }

    "handle welcome email sending" when {
      "setWelcomeEmailSent positive" in {
        preInsert(user1)

        val result = repository.setWelcomeEmailSent("authorId", user1.id)
        result.future.futureValue shouldBe a[Result.Ok[_]]
        usersSnapshotsCol.findOne().future.futureValue shouldBe a[Result.Ok[_]]
      }

      "setWelcomeEmailSent if user doesn't exist" in {
        preInsert(user1)

        val result = repository.setWelcomeEmailSent("authorId", "anyUserId")
        result.future.futureValue shouldBe a[Result.NotFound]
        usersSnapshotsCol.findOne().future.futureValue shouldBe a[Result.NotFound]
      }
    }
  }

  private def preInsert(user: User, xs: User*): Unit = {
    implicit def ec = biod.executionContext

    val futures = for {u <- user +: xs} yield
      usersColl.insert(UserFormat.write(u)).future

    Future.sequence(futures).futureValue
  }
}

object UsersRepositorySpec {

  val networkId1: Id.NetworkId = NetworkId("net1")

  val networkId2: Id.NetworkId = NetworkId("net2")

  val TestUser: TestUserGen = {
    import com.goldmansachs.marquee.pipg.service.user.TestUserGen._

    new TestUserGen(
      _networkId = Gen(networkId1, networkId2),
      _email = Gen(s"$<EMAIL>"),
      _firstName = Gen(rndStr, rndStr),
      _lastName = Gen(rndStr, rndStr, rndStr, rndStr),
      _author = Gen(rndStr),
      _omsId = Gen(rndStr + "!" + rndStr),
      _distributorId = Gen({
        for (_ <- 1 until 12) yield scala.util.Random.nextInt(10)
      }.mkString("")),
      _role = Gen(UserRole.EqPIPGGSAdmin),
      _idpLoginId = Gen(rndStr)
    )
  }

  private lazy val now = LocalDateTime.now(ZoneId.systemDefault()).truncatedTo(ChronoUnit.MILLIS)

  val roles1: Set[UserRole] = Set(UserRole.EqPIPGPB)

  val user1Twd = "user1Twd"

  val user1LPLId = "john.Doe"

  val roles3: Set[UserRole] = Set(UserRole.EqPIPGGSAdmin)

  val dynamicRoles1: Set[String] = Set("foo", "bar")

  val npnNum = "npn123"

  lazy val user1: User = TestUser(
    id = Some("id1"),
    email = Some("email1"),
    firstName = Some("Eugene"),
    lastName = Some("Prystupa"),
    emailSentAt = Some(Some(now)),
    lastVisitedAt = Some(None),
    roles = Some(roles1),
    dynamicRoles = Some(dynamicRoles1),
    distributorId = Some(user1LPLId),
    omsId = Some(user1Twd),
    licenses = Some(Set(License.CRD("number"),
      License.NPN(npnNum)
    )),
    entitlements = Some(Set("admin")),
    cusips = Some(Set("cusip1", "cusip2"))
  )

  lazy val user2: User = TestUser(
    id = Some("id2"),
    email = Some("email2"),
    roles = Some(Set.empty),
    emailSentAt = Some(Some(now)),
    lastVisitedAt = Some(None)
  )

  lazy val user3: User = TestUser(
    id = Some("id3"),
    email = Some("email3"),
    firstName = Some("Calvin"),
    lastName = Some("Chan"),
    roles = Some(roles3),
    entitlements = Some(Set("admin"))
  )

  lazy val user4 = TestUser(
    id = Some("id4"),
    omsId = Some("ab!TWD123"),
    entitlements = Some(Set("admin"))
  )
}
