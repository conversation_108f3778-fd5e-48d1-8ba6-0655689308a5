package com.goldmansachs.marquee.pipg.service.networks

import com.goldmansachs.marquee.pipg.NetworkType._
import com.goldmansachs.marquee.pipg.{NetworkCategory, NetworkType}
import org.scalatest.Matchers._
import org.scalatest.OptionValues._
import org.scalatest.WordSpec


class NetworkTypeSpec extends WordSpec {

  "NetworkType.fromStringList" when {

    "takes Nil" should {

      "resolve to None" in {
        NetworkType.fromStringList(Nil) should not be 'defined
      }
    }

    NetworkType.Values.filterNot(_ == EnumNotFound) map { _.productPrefix } foreach { str =>
      s"takes singular list of $str" should {
        "be resolved to Some" in {
          NetworkType.fromStringList(List(str)) shouldBe 'defined
        }
      }
    }

    "takes singular list containing illegal value" should {

      "be resolved to None" in {
        NetworkType.fromStringList(List("foo")) should not be 'defined
      }
    }

    "takes multiple illegal value" should {

      "be resolved to None" in {
        NetworkType.fromStringList(List("foo", "bar", EnumNotFound.name)) should not be 'defined
      }
    }

    "takes mix of legal and illegal values" should {

      "be resolved to Some filtering off illegal values" in {
        NetworkType.fromStringList(List(HedgeProvider.name, "bar", RIA.name, EnumNotFound.name)).value should contain only (HedgeProvider, RIA)
      }
    }
  }

  "NetworkCategory.fromTypes" when {

    "taking Nil" should {

      "resolve to None" in {
        NetworkCategory.fromTypes(Nil) should not be 'defined
      }
    }

    NetworkType.Values foreach { networkType =>
      s"taking singular list of $networkType" should {
        s"resolve to Corresponding Category" in {
          NetworkCategory.fromTypes(List(networkType)).value shouldBe networkType.category
        }
      }
    }

    "taking a List of consistent types" should {

      "resolve to Corresponding Category" in {
        NetworkCategory.fromTypes(List(HedgeProvider, Issuer)).value shouldBe NetworkCategory.Issuer
        NetworkCategory.fromTypes(List(RIA, BrokerDealer, PrivateBank)).value shouldBe NetworkCategory.Distributor
        NetworkCategory.fromTypes(List(Wholesaler)).value shouldBe NetworkCategory.Wholesaler
      }
    }

    "taking a List of inconsistent types" should {

      "resolve to type with higher priority" in {
        NetworkCategory.fromTypes(List(HedgeProvider, RIA)).value shouldBe NetworkCategory.Issuer
        NetworkCategory.fromTypes(List(RIA, Wholesaler, PrivateBank)).value shouldBe NetworkCategory.Wholesaler
        NetworkCategory.fromTypes(List(RIA, BrokerDealer, PrivateBank, Issuer)).value shouldBe NetworkCategory.Issuer
      }
    }
  }
}
