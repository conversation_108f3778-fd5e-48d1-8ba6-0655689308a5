package com.goldmansachs.marquee.pipg.bson

import com.mongodb.casbah.commons.MongoDBObject
import com.mongodb.{DefaultDBDecoder, DefaultDBEncoder}
import org.apache.commons.pool2.impl.{DefaultPooledObject, GenericObjectPool}
import org.apache.commons.pool2.{BasePooledObjectFactory, ObjectPool, PooledObject}
import org.bson.BSONObject
import org.scalatest.Matchers._
import resource._

import scala.annotation.nowarn

object TestSerialization {

  private object Pool {

    def apply[T](cinit: => T): ObjectPool[T] = {
      val factory = new BasePooledObjectFactory[T] {
        def create(): T = cinit
        def wrap(obj: T): PooledObject[T] = new DefaultPooledObject(obj)
      }

      new GenericObjectPool(factory)
    }
  }

  private class EncodeDecode(
      encoder: DefaultDBEncoder = new DefaultDBEncoder,
      decoder: DefaultDBDecoder = new DefaultDBDecoder) {

    def encode(x: BSONObject): Array[Byte] = encoder encode x

    def decode(x: Array[Byte]): BSONObject = decoder readObject x
  }

  private val pool = Pool[EncodeDecode](new EncodeDecode)

  private implicit def encodeDecodeResource: Resource[EncodeDecode] = (r: EncodeDecode) => pool returnObject r

  override def hashCode(): Int = super.hashCode()

  def apply[T](fmt: Format[T], items: T*): Unit = for {
    encodeDecode  <- managed(pool.borrowObject)
    item          <- items
  } {
    val odbo      = fmt write item
    val bytes     = encodeDecode encode odbo
    val rbson     = encodeDecode decode bytes
    @nowarn("cat=deprecation")
    val rjson     = com.mongodb.util.JSON.serialize(rbson)
    val rdbo      = MongoDBObject(rjson)
    val restored  = fmt.read(rdbo)

    restored shouldEqual item
  }

  def apply[T](write: Format[T], read: ReaderFactory[T], items: T*): Unit = for {
    encodeDecode  <- managed(pool.borrowObject)
    item          <- items
  } {
    val odbo      = write.write(item)
    val bytes     = encodeDecode encode odbo
    val rbson     = encodeDecode decode bytes
    @nowarn("cat=deprecation")
    val rjson     = com.mongodb.util.JSON.serialize(rbson)
    val rdbo      = MongoDBObject(rjson)
    val restored  = read.newReader(rdbo).materialize

    restored shouldEqual item
  }
}
