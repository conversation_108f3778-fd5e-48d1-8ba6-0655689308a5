package com.goldmansachs.marquee.pipg.service.user

import com.goldmansachs.marquee.pipg.{License, User, UserRole}
import simon.Id.NetworkId
import com.goldmansachs.marquee.pipg.service.user.TestUserGen.Gen


import java.time.temporal.ChronoUnit
import java.time.{LocalDateTime, ZoneId}
import java.util.UUID

class TestUserGen(
    _networkId: Gen[NetworkId],
    _email: Gen[String],
    _firstName: Gen[String],
    _lastName: Gen[String],
    _author: Gen[String],
    _distributorId: Gen[String],
    _omsId: Gen[String],
    _role: Gen[UserRole],
    _idpLoginId: Gen[String]
) {

  private lazy val now = LocalDateTime.now(ZoneId.systemDefault()).truncatedTo(ChronoUnit.MILLIS)

  def apply(
      id: Option[String] = None,
      networkId: Option[NetworkId] = None,
      createdAt: Option[LocalDateTime] = None,
      createdBy: Option[String] = None,
      updatedAt: Option[LocalDateTime] = None,
      updatedBy: Option[String] = None,
      emailSentAt: Option[Option[LocalDateTime]] = None,
      emailSentBy: Option[Option[String]] = None,
      lastVisitedAt: Option[Option[LocalDateTime]] = None,
      email: Option[String] = None,
      firstName: Option[String] = None,
      lastName: Option[String] = None,
      distributorId: Option[String] = None,
      omsId: Option[String] = None,
      roles: Option[Set[UserRole]] = None,
      entitlements: Option[Set[String]] = None,
      dynamicRoles: Option[Set[String]] = None,
      locations: Option[Set[String]] = None,
      faNumbers: Option[Set[String]] = None,
      customRoles: Option[Set[String]] = None,
      licenses: Option[Set[License]] = None,
      cusips: Option[Set[String]] = None,
      idpLoginId: Option[String] = None
  ): User = {

    def genDistributorId = {
      if (scala.util.Random.nextBoolean()) Some(_distributorId.next) else None
    }

    def genOmsId = {
      if (scala.util.Random.nextBoolean()) Some(_omsId.next) else None
    }

    User(
      id = id.getOrElse(UUID.randomUUID.toString),
      networkId = _networkId.next(networkId),
      createdAt = createdAt.getOrElse(now),
      createdBy = _author.next(createdBy),
      updatedAt = updatedAt.getOrElse(now),
      updatedBy = _author.next(updatedBy),
      emailSentAt = emailSentAt.getOrElse(if (scala.util.Random.nextBoolean()) Some(now) else None),
      emailSentBy = emailSentBy.getOrElse(if (scala.util.Random.nextBoolean()) Some(_author.next) else None),
      lastVisitedAt = lastVisitedAt.getOrElse(if (scala.util.Random.nextBoolean()) Some(now) else None),
      email = _email.next(email),
      firstName = _firstName.next(firstName),
      lastName = _lastName.next(lastName),
      distributorId = distributorId.orElse(genDistributorId),
      omsId = omsId.orElse(genOmsId),
      roles = roles.getOrElse(Set(_role.next)),
      entitlements = entitlements.getOrElse(Set.empty),
      dynamicRoles = dynamicRoles.getOrElse(Set.empty),
      locations = locations.getOrElse(Set.empty),
      faNumbers = faNumbers.getOrElse(Set.empty),
      customRoles = customRoles.getOrElse(Set.empty),
      licenses = licenses.getOrElse(Set.empty),
      cusips = cusips.getOrElse(Set.empty),
      idpLoginId = idpLoginId.getOrElse(_idpLoginId.next)
    )
  }
}

object TestUserGen {

  case class Gen[T] private(options: Seq[T]) {

    private lazy val sz = options.size

    def next: T = options.toList((math.random * sz).toInt)

    def next(hint: Option[T]): T = hint getOrElse next
  }

  object Gen {

    def apply[T](x: T, xs: T*): Gen[T] = Gen(x +: xs)
  }

  val stringPool = Vector(
    "jtGonV",
    "9BgnCB",
    "yaB6vG",
    "YutLvf",
    "97e0eS",
    "PDwhv5",
    "PdpzcE",
    "Qg6WLr",
    "DqupM1",
    "8xNv85",
    "pjz0xT",
    "D1xVmb",
    "c6aZdI",
    "MjUnxG",
    "YGGPty",
    "wVSd75",
    "5rkf9x",
    "d36anw",
    "yp3pHf",
    "ZY933u",
    "3P9Qoj",
    "BDuvfU",
    "cIf2uh",
    "NkpqV5",
    "GNeSFL",
    "tWqWQU",
    "1PEkKX",
    "TbYOXk",
    "Qso13b",
    "WMBG5q",
    "GNSiun",
    "iF934x")

  def rndStr: String = stringPool((math.random * stringPool.length).toInt)

  val simple: TestUserGen = {

    new TestUserGen(
      _networkId = Gen(NetworkId(rndStr)),
      _email = Gen(s"$<EMAIL>"),
      _firstName = Gen(rndStr, rndStr),
      _lastName = Gen(rndStr, rndStr, rndStr, rndStr),
      _author = Gen(rndStr),
      _omsId = Gen(rndStr + "!" + rndStr),
      _distributorId = Gen({
        for (_ <- 1 until 12) yield scala.util.Random.nextInt(10)
      }.mkString("")),
      _role = Gen(UserRole.EqPIPGGSAdmin),
      _idpLoginId = Gen(rndStr)
    )
  }
}