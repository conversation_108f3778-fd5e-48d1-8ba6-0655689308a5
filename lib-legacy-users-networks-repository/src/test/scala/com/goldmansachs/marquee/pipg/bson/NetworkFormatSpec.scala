package com.goldmansachs.marquee.pipg.bson
import com.goldmansachs.marquee.pipg.Network.Action
import com.goldmansachs.marquee.pipg._
import com.simonmarkets.asset.Region
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.enums.{CrmProvider, DomicileCode}
import com.simonmarkets.shared.{EAppProvider, MaskedId}
import org.scalatest.WordSpec
import simon.Id._

class NetworkFormatSpec extends WordSpec {

  import NetworkFormatSpec._

  "NetworkFormat" should {
    "serialize and deserialize entity with all fields non-empty" in TestSerialization(NetworkFormat, nonEmpty)
    "serialize and deserialize entity with some fields empty" in TestSerialization(NetworkFormat, empty)
    "serialize and deserialize with distributor list not set" in TestSerialization(NetworkFormat, emptyDistList)

    "order payoff entitlements by action" in {
      val network: Network = Network(
        id = NetworkId("networkId"),
        networkName = "networkId",
        idHubOrganization = IdHubOrganization(11, "some org"),
        version = 1,
        payoffEntitlementsV2 = Map(
          "CITI" -> Map("JumpNote" -> Set(Action("b"), Action("c"), Action("a"))),
          "BCSBank" -> Map("EQGenericIncome" -> Set(Action("a"), Action("b"), Action("c")))
        )
      )

      val dbo = NetworkFormat.write(network)
      val parsed = NetworkFormat.read(dbo)

      assert(parsed.payoffEntitlementsV2 == Map(
        "CITI" -> Map("JumpNote" -> Set(Action("a"), Action("b"), Action("c"))),
        "BCSBank" -> Map("EQGenericIncome" -> Set(Action("a"), Action("b"), Action("c")))
      ))
    }

    // contact info serialization should be compatible with mongo-scala version which puts `null` for None
    "read null in distributors list" in {
      val contactInfo = ContactInfo(Some("123"), Some("456"), None)
      val network: Network = Network(
        id = NetworkId("networkId"),
        networkName = "networkId",
        idHubOrganization = IdHubOrganization(11, "some org"),
        version = 1,
        contactInfo = Some(contactInfo)
      )

      val dbo = NetworkFormat.write(network)
      val contactInfoDbo = ContactInfoFormat.write(contactInfo)
      contactInfoDbo.put("distributionLists", null)
      dbo.put("contactInfo", contactInfoDbo)
      val parsed = NetworkFormat.read(dbo)

      assert(parsed == network)
    }

    "contains domiciles" in {
      val network: Network = Network(
        id = NetworkId("networkId"),
        networkName = "networkId",
        idHubOrganization = IdHubOrganization(11, "some org"),
        version = 1,
        domiciles = Some(Set(DomicileCode.US, DomicileCode.CA))
      )

      val dbo = NetworkFormat.write(network)
      val parsed = NetworkFormat.read(dbo)

      assert(parsed.domiciles.contains(Set(DomicileCode.US, DomicileCode.CA)))
    }
  }
}

object NetworkFormatSpec {

  val nonEmpty: Network = Network(
    id = NetworkId("networkId"),
    networkName = "networkId",
    idHubOrganization = IdHubOrganization(11, "some org"),
    approverSet = Map("foo" -> List(List("bar"))),
    ioiApproverSet = Map("Market Linked Note" -> List(List("bar"))),
    accountMappings = Some(List("foo", "bar")),
    networkTypes = Some(List(NetworkType.Wholesaler)),
    to = Some(List("to")),
    cc = Some(List("cc")),
    purviewNetworks = Some(Set(IssuerPurview(NetworkId("networkId2"), Set("userId1", "userId2"), Some(NetworkId("wholesaler1")), Some(Set(PurviewedDomain.Accounts, PurviewedDomain.Users))))),
    salesFeeRuleIds = List("ruleId1", "ruleId2"),
    capabilities = Map("a" -> List("b, c")),
    payoffEntitlements = Map("a" -> Map("b" -> List("c", "d"))),
    payoffEntitlementsV2 = Map(
      "CITI" -> Map("JumpNote" -> Set(Action(action = "view"), Action(action = "trade"))),
      "BCSBank" -> Map(
        "JumpNote" -> Set(Action(action = "view", contractParams = Map("feeOrCommission" -> Set("Commission"))), Action(action = "trade", contractParams = Map.empty)),
        "CallableYieldNote" -> Set(Action(action = "view", contractParams = Map.empty), Action(action = "trade", contractParams = Map("feeOrCommission" -> Set("Commission"))))),
      "BCS" -> Map("EQGenericIncome" -> Set(Action(action = "view", contractParams = Map("feeOrCommission" -> Set("Commission", "Fee"))),
        Action(action = "trade", contractParams = Map.empty)))
    ),
    dynamicRoles = Set("a", "b"),
    customRolesConfig = Set(
      CustomRoleDefinition("customRole1", Set("customCapability1")),
      CustomRoleDefinition("customRole2", Set("customCapability1", "customCapability2"))),
    version = 2,
    maskedIds = Set(MaskedId("a", "b")),
    booksCloseConfig = List(BooksCloseConfig(Region.Americas, -1, 17, 0)),
    booksCloseCustomConfig = List(BooksCloseConfig(Region.Americas, -1, 17, 0)),
    booksSendConfig = Some(List(BooksCloseConfig(Region.Americas, -1, 17, 0))),
    prospectusDeadlineConfig = Some(List(ProspectusDeadlineConfig(1, DeadlineConfig(2, 3, 4)))),
    dtccId = Some("0000"),
    locations = Set(
      NetworkLocation("location1", None, Set("location2")),
      NetworkLocation("parent2", None, Set.empty),
      NetworkLocation("location2", Some("parent1"), Set.empty)),
    entitlements = Set("admin", "viewNetworkViaNetwork:networkId", "viewNetworkViaPurview"),
    annuityEAppProvider = Some(
      AnnuityEAppProvider(EAppProvider.Firelight, Map("orgId" -> "SMND", "secret" -> "c6cceeaae4e7495b833e48eb25c3e639", "userRoleCode" -> "test"))),
    ssoPrefix = Some(SSOPrefix("https://my-internal-url/simon", "RelayState", Some("https://www.auth.simonmarkets.com/#/bookmark"), None, Some("App Name"))),
    contactInfo = Some(ContactInfo(Some("<EMAIL>"), None, Some(DistributionList(List("<EMAIL>"), List("<EMAIL>"), List("<EMAIL>"))))),
    learnTracksV2 = Seq(
      LearnTrack("track1", isActive = true),
      LearnTrack("track2", isActive = false)
    ),
    siCertificationRequirements = Seq(
      CertificationAlternativesForProduct(productId = "payoffType", productName = "action", certificationAlternatives = Seq(Seq("t1"))),
      CertificationAlternativesForProduct(productId = "payoffType2", productName = "action", certificationAlternatives = Seq(Seq("t2", "t3")), compensationType = Some(CompensationType.Fee)),
      CertificationAlternativesForProduct(productId = "payoffType3", productName = "action",
        certificationAlternatives = Seq(Seq("t4")), compensationType = Some(CompensationType.Fee), underliers = Some(Seq("underlier1", "underlier2")))
    ),
    annuityCertificationRequirements = Seq(CertificationAlternativesForProduct(productId = "payoffType", productName = "action", certificationAlternatives = Seq(Seq("t1"), Seq("t2", "t3")))),
    definedOutcomeETFCertificationRequirements = Seq(CertificationAlternativesForProduct(productId = "payoffType", productName = "action", certificationAlternatives = Seq(Seq("t1"), Seq("t2", "t3")))),
    certificationProducts = Some(Seq("74430A664", "74430A665")),
    externalId = Some(ExternalNetworkId("externalId")),
    externalIds = Set(ExternalId("subject", "id")),
    group = Some("Parent Network"),
    partnerUrls = Set(PartnerUrl("somePartnerName", Set(URL("someURLType", "someURL")))),
    crmProviders = List(CrmProvider.Redtail),
    altCertificationRequirements = Some(
      Seq(CertificationAlternativesForProduct(productId = "payoffType4", productName = "payoff4", certificationAlternatives = Seq(Seq("t5", "t6"), Seq("t7", "t8"))))
    ),
    domiciles = Some(Set(DomicileCode.US, DomicileCode.CA)),
  )

  val empty: Network = Network(
    id = NetworkId("networkId"),
    networkName = "networkId",
    idHubOrganization = IdHubOrganization(11, "some org"),
    version = 1)

  val emptyDistList: Network = Network(
    id = NetworkId("networkId"),
    networkName = "networkId",
    idHubOrganization = IdHubOrganization(11, "some org"),
    contactInfo = Some(ContactInfo(Some("<EMAIL>"), None, None)),
    version = 1)
}
