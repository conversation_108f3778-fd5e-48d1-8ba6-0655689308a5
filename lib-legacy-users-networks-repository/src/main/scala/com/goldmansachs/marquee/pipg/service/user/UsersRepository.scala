package com.goldmansachs.marquee.pipg.service.user

import com.goldmansachs.marquee.pipg.User.DistributorInfo
import com.goldmansachs.marquee.pipg.bson.LegacyUserFormats
import com.goldmansachs.marquee.pipg.{UserRole, _}
import com.goldmansachs.marquee.pipg.mongo.{$, AsyncCollection}
import com.goldmansachs.marquee.pipg.mongo.snapshot.SnapshotableRepository
import com.goldmansachs.marquee.pipg.service.transaction.TransactionRepository
import com.goldmansachs.marquee.pipg.service.user.{UsersLookup => L, UsersProjection => P}
import com.gs.marquee.simon.http.Operation
import com.gs.marquee.simon.http.Operation._
import com.mongodb.casbah.Imports._
import com.mongodb.casbah.MongoDB
import com.mongodb.casbah.commons.MongoDBList
import com.simonmarkets.shared.MaskedId
import com.simonmarkets.users.common.Context
import simon.Id.NetworkId

import java.time.{LocalDateTime, OffsetDateTime, ZoneId}
import scala.concurrent.Future

trait UsersRepository extends SnapshotableRepository[User] with LegacyUserFormats {

  def lookup(lookup: L, projection: P): Future[Iterable[projection.T]]

  def lookupWithInactive(lookup: L, projection: P): Future[Iterable[projection.T]]

  def lookupDistinct(lookup: L, projection: P): Future[Set[projection.T]]

  def lookupOneWithInactive(lookup: L, projection: P): Operation[projection.T]

  def lookupOne(lookup: L, projection: P): Operation[projection.T]

  def exists(lookup: L): Future[Boolean]

  final def entitledFind(
      entitlements: Set[String], networkOpt: Option[NetworkId], ids: Set[String], projection: P = P.Entire
  ): Future[Iterable[projection.T]] =
    lookup(L.Ids(ids) and L.NetworkId(networkOpt) and L.Entitlements(entitlements), projection)

  final def entitledFindByNpns(
      entitlements: Set[String], networkOpt: Option[NetworkId], npns: Set[String], projection: P = P.Entire
  ): Future[Iterable[projection.T]] =
    lookup(L.Npns(npns) and L.NetworkId(networkOpt) and L.Entitlements(entitlements), projection)

  final def entitledFindByEmails(
      entitlements: Set[String], networkOpt: Option[NetworkId], emails: Set[String], projection: P = P.Entire
  ): Future[Iterable[projection.T]] =
    lookup(L.Emails(emails) and L.NetworkId(networkOpt) and L.Entitlements(entitlements), projection)

  final def entitledFindByDistributorId(
      entitlements: Set[String], networkId: NetworkId, distributorId: String, projection: P = P.Entire
  ): Operation[projection.T] =
    lookupOne(L.DistributorId(distributorId) and L.NetworkId(networkId) and L.Entitlements(entitlements), projection)

  final def entitledFindByDistributorIds(
      entitlements: Set[String], networkId: NetworkId, distributorIds: Set[String], projection: P = P.Entire
  ): Future[Iterable[projection.T]] =
    lookup(L.DistributorId(distributorIds) and L.NetworkId(networkId) and L.Entitlements(entitlements), projection)

  final def entitledFindByOmsIds(
      entitlements: Set[String], omsIds: Set[String], projection: UsersProjection = P.Entire
  ): Future[Iterable[projection.T]] =
    lookup(L.OmsId(omsIds) and L.Entitlements(entitlements), projection)

  final def entitledFindByOmsId(
      entitlements: Set[String], omsId: String, projection: UsersProjection = P.Entire
  ): Operation[projection.T] =
    lookupOne(L.OmsId(omsId) and L.Entitlements(entitlements), projection)

  final def entitledFindWithInactive(
      entitlements: Set[String], networkOpt: Option[NetworkId], ids: Set[String], projection: P = P.Entire
  ): Future[Iterable[projection.T]] =
    lookupWithInactive(L.Ids(ids) and L.NetworkId(networkOpt) and L.Entitlements(entitlements), projection)

  final def entitledFindAllWithInactive(
      entitlements: Set[String], lookup: UsersLookup, projection: UsersProjection = P.Entire
  ): Future[Iterable[projection.T]] =
    lookupWithInactive(L.Entitlements(entitlements) and lookup, projection)

  final def findAll: Future[Iterable[User]] = lookup(L.All, P.Entire)

  final def findAllWithInactive: Future[Iterable[User]] = findAllWithInactive(L.All)

  final def findAllWithInactive(lookup: L): Future[Iterable[User]] = lookupWithInactive(lookup, P.Entire)

  final def findAllByIds(ids: Set[String]): Future[Iterable[User]] = lookupWithInactive(L.Ids(ids), P.Entire)

  final def findAllByRoles(roles: Set[UserRole]): Future[Iterable[User]] = lookup(L.Roles(roles), P.Entire)

  final def findAllByIdsAndDynamicRoles(ids: Set[String], dynamicRoles: Set[String]): Future[Iterable[User]] = {
    lookup(L.Ids(ids) and L.DynamicRoles(dynamicRoles), P.Entire)
  }

  final def findAllByDynamicRoles(dynamicRoles: Set[String]): Future[Iterable[User]] =
    lookup(L.DynamicRoles(dynamicRoles), P.Entire)

  final def findById(id: String): Operation[User] = lookupOne(L.Id(id), P.Entire)

  final def findByIdWithInactive(id: String): Operation[User] = lookupOneWithInactive(L.Id(id), P.Entire)

  final def findByIdWithEntitledInactive(id: String, entitlements: Set[String]): Operation[User] = lookupOneWithInactive(L.Id(id) and L.Entitlements(entitlements), P.Entire)

  final def findByEmail(email: String, networkId: NetworkId): Operation[User] = lookupOne(L.Email(email) and L.NetworkId(networkId), P.Entire)

  final def findByDistributorId(id: String, networkId: NetworkId): Operation[User] = lookupOne(L.DistributorId(id) and L.NetworkId(networkId), P.Entire)

  final def findByDistributorIdsInNetwork(distributorIds: Set[String], networkId: NetworkId): Future[Set[User]] = lookupDistinct(L.DistributorId(distributorIds) and L.NetworkId(networkId), P.Entire)

  final def findByDistributorIdInNetworks(distributorId: String, networkIds: List[NetworkId]): Future[Iterable[String]] = lookup(L.DistributorId(distributorId) and L.NetworkIds(networkIds), P.Id)

  final def findByOmsId(id: String): Operation[User] = lookupOne(L.OmsId(id), P.Entire)

  final def findAllByNetworkIdsAndRoles(ids: Set[NetworkId], roles: Set[UserRole]): Future[Iterable[User]] = lookup(L.NetworkIds(ids) and L.Roles(roles), P.Entire)

  final def findIdByEmail(email: String, networkId: NetworkId): Operation[String] = lookupOne(L.Email(email) and L.NetworkId(networkId), P.Id)

  final def findIdByIdOrEmail(idOrEmail: String): Operation[String] = lookupOne(L.Id(idOrEmail) or L.Email(idOrEmail), P.Id)

  final def findEmailById(id: String): Operation[String] = lookupOne(L.Id(id), P.Email)

  final def findIdsByIdsAndRoles(ids: Set[String], roles: Set[UserRole]): Future[Set[String]] = lookupDistinct(L.Ids(ids) and L.Roles(roles), P.Id)

  final def findIdsByNameContains(text: String): Future[Set[String]] = lookupDistinct(L.NameContains(text), P.Id)

  final def findIdsByNetworkIds(ids: Set[NetworkId]): Future[Set[String]] = lookupDistinct(L.NetworkIds(ids), P.Id)

  final def findUsersByNetworkIds(ids: Set[NetworkId]): Future[Iterable[User]] = lookup(L.NetworkIds(ids), P.Entire)

  def findEmailsByIds(ids: Set[String]): Future[Map[String, String]]

  def findNetworkIdsByIds(ids: Set[String]): Future[Map[String, NetworkId]]

  def findFirstAndLastNamesAndEmailsByIds(ids: Set[String]): Future[Map[String, (String, String, String)]]

  def findNetworkIdsVisibleByUserPurview(user: UserACL): Future[Set[NetworkId]]

  def findUserIdsVisibleById(user: UserACL): Future[Set[String]]

  final def existsById(id: String): Future[Boolean] = exists(L.Id(id))

  final def existsByEmail(email: String): Future[Boolean] = exists(L.Email(email))

  def deactivate(authorId: String, id: String): Operation[User]

  def insert(
      authorId: String,
      id: String,
      networkId: NetworkId,
      email: String,
      firstName: String,
      lastName: String,
      distributorId: Option[String],
      omsId: Option[String],
      tradewebEligible: Boolean,
      regSEligible: Boolean,
      roles: Set[UserRole],
      locations: Set[String],
      faNumbers: Set[String],
      customRoles: Set[String],
      licenses: Set[License],
      entitlements: Set[String],
      maskedIds: Set[MaskedId],
      idpId: Option[String],
      accountInContext: Option[String] = None,
      context: Option[Context] = None,
      cusips: Set[String],
      idpLoginId: String
  ): Operation[User]


  def updateUser(
      authorId: String,
      id: String,
      networkId: NetworkId,
      email: String,
      firstName: String,
      lastName: String,
      distributorId: Option[String],
      omsId: Option[String],
      tradewebEligible: Boolean,
      regSEligible: Boolean,
      roles: Set[UserRole],
      locations: Set[String],
      faNumbers: Set[String],
      customRoles: Set[String],
      licenses: Set[License],
      entitlements: Set[String],
      idpId: Option[String],
      distributorInfo: Option[DistributorInfo],
      accountInContext: Option[String] = None,
      context: Option[Context] = None,
      cusips: Set[String]
  ): Operation[User]

  def setWelcomeEmailSent(authorId: String, id: String): Operation[User]

  def refreshLastVisitedAt(id: String): Operation[Unit]

  override protected def emailsOf: EmailsOf = (ids: Set[String]) => findAllByIds(ids).map(_.map(
    user => user.id -> user.email).toMap)
}

object UsersRepository {

  final class Mongo(networkColl: AsyncCollection,
      val entityCollection: AsyncCollection,
      val snapshotCollection: AsyncCollection,
      val transactionRepository: TransactionRepository) extends UsersRepository {

    private implicit def ec = entityCollection.executionContext

    private def lookupQuery(lookup: UsersLookup): DBObject = {
      $("$and" -> MongoDBList("isActive" $eq true, UsersLookupMongo(lookup)))
    }

    private def lookupWithInactive(lookup: UsersLookup): DBObject = UsersLookupMongo(lookup)

    override def lookup(lookup: UsersLookup, projection: UsersProjection): Future[Iterable[projection.T]] = {
      val projMongo = UsersProjectionMongo(projection)
      entityCollection.find(
        query = lookupQuery(lookup),
        fields = projMongo.asQueryFields.orNull) map {
        _.toIterable map projMongo.reader
      }
    }

    override def lookupWithInactive(lookup: UsersLookup, projection: UsersProjection): Future[Iterable[projection.T]] = {
      val projMongo = UsersProjectionMongo(projection)
      entityCollection.find(
        query = lookupWithInactive(lookup),
        fields = projMongo.asQueryFields.orNull) map {
        _.toIterable map projMongo.reader
      }
    }

    override def lookupDistinct(lookup: UsersLookup, projection: UsersProjection): Future[Set[projection.T]] = {
      this.lookup(lookup, projection) map {
        _.toSet
      }
    }

    override def lookupOneWithInactive(lookup: L, projection: P): Operation[projection.T] = {
      val projMongo = UsersProjectionMongo(projection)
      entityCollection.findOne(
        query = lookupWithInactive(lookup),
        fields = projMongo.asQueryFields.orNull) map projMongo.reader
    }

    override def lookupOne(lookup: UsersLookup, projection: UsersProjection): Operation[projection.T] = {
      val projMongo = UsersProjectionMongo(projection)
      entityCollection.findOne(
        query = lookupQuery(lookup),
        fields = projMongo.asQueryFields.orNull) map projMongo.reader
    }

    override def exists(lookup: L): Future[Boolean] = {
      entityCollection.exists(lookupWithInactive(lookup))
    }

    def deactivate(authorId: String, id: String): Operation[User] = {
      val zone = ZoneId.systemDefault()
      val now = LocalDateTime.now(zone)

      for {
        currentUser <- findByIdWithInactive(id).map(
          _.copy(roles = Set.empty,
            omsId = None,
            distributorId = None,
            updatedAt = now,
            updatedBy = authorId,
            isActive = false))
        result <- update(authorId, currentUser, None)
      } yield result
    }

    def setWelcomeEmailSent(authorId: String, id: String): Operation[User] = {
      val zone = ZoneId.systemDefault()
      val now = LocalDateTime.now(zone)

      for {
        currentUser <- findByIdWithInactive(id).map(
          _.copy(emailSentBy = Some(authorId),
            emailSentAt = Some(now)))
        result <- update(authorId, currentUser, None)
      } yield result

    }

    def refreshLastVisitedAt(id: String): Operation[Unit] = {
      val zone = ZoneId.systemDefault()
      val now = LocalDateTime.now(zone)

      entityCollection.findAndModify(
        query = $("id" -> id),
        update = $(
          "$set" -> $(
            "lastVisitedAt" -> java.util.Date.from(now.toInstant(OffsetDateTime.now(zone).getOffset))
          )),
        returnNew = false,
        upsert = false) map { _ => () }
    }

    override def insert(
        authorId: String,
        id: String, // taken from IDHUB
        networkId: NetworkId,
        email: String,
        firstName: String,
        lastName: String,
        distributorId: Option[String],
        omsId: Option[String],
        tradewebEligible: Boolean = false,
        regSEligible: Boolean = false,
        roles: Set[UserRole],
        locations: Set[String],
        faNumbers: Set[String],
        customRoles: Set[String],
        licenses: Set[License],
        entitlements: Set[String],
        maskedIds: Set[MaskedId],
        idpId: Option[String],
        accountInContext: Option[String] = None,
        context: Option[Context] = None,
        cusips: Set[String] = Set.empty,
        idpLoginId: String
    ): Operation[User] = {

      val now = LocalDateTime.now(ZoneId.systemDefault())

      val entity = User(
        id = id,
        networkId = networkId,
        email = email,
        firstName = firstName,
        lastName = lastName,
        distributorId = distributorId,
        omsId = omsId,
        tradewebEligible = tradewebEligible,
        regSEligible = regSEligible,
        roles = roles,
        createdAt = now,
        createdBy = authorId,
        updatedAt = now,
        updatedBy = authorId,
        emailSentAt = None,
        emailSentBy = None,
        lastVisitedAt = None,
        locations = locations,
        faNumbers = faNumbers,
        customRoles = customRoles,
        licenses = licenses,
        entitlements = entitlements,
        maskedIds = maskedIds,
        idpId = idpId,
        accountInContext = accountInContext,
        context = context,
        cusips = cusips,
        idpLoginId = idpLoginId
      ).withDynamicRolesUpdated

      for {
        _ <- networkColl.exists($("id" -> networkId)) trueOr Result.BadRequest(s"Unknown network '$networkId'")
        result <- insert(authorId, entity, None)
      } yield
        result
    }

    override def updateUser(
        authorId: String,
        id: String,
        networkId: NetworkId,
        email: String,
        firstName: String,
        lastName: String,
        distributorId: Option[String],
        omsId: Option[String],
        tradewebEligible: Boolean = false,
        regSEligible: Boolean = false,
        roles: Set[UserRole],
        locations: Set[String],
        faNumbers: Set[String],
        customRoles: Set[String],
        licenses: Set[License],
        entitlements: Set[String],
        idpId: Option[String],
        distributorInfo: Option[DistributorInfo],
        accountInContext: Option[String] = None,
        context: Option[Context] = None,
        cusips: Set[String] = Set.empty
    ): Operation[User] = {

      val now = LocalDateTime.now(ZoneId.systemDefault())

      for {
        _ <- networkColl.exists($("id" -> networkId)) trueOr Result.BadRequest(s"Unknown network '$networkId'")
        updatedUser <- findByIdWithInactive(id).map(currentUser => {
          currentUser.copy(
            networkId = networkId,
            email = email,
            firstName = firstName,
            lastName = lastName,
            distributorId = distributorId,
            omsId = omsId,
            tradewebEligible = tradewebEligible,
            regSEligible = regSEligible,
            roles = roles,
            isActive = if (roles.isEmpty) false else true,
            updatedAt = now,
            updatedBy = authorId,
            locations = locations,
            faNumbers = faNumbers,
            customRoles = customRoles,
            licenses = licenses,
            entitlements = entitlements,
            idpId = idpId,
            distributorInfo = distributorInfo,
            accountInContext = accountInContext,
            context = context,
            cusips = cusips
          ).withDynamicRolesUpdated
        })
        result <- update(authorId, updatedUser, None)
      } yield result
    }

    override def findEmailsByIds(ids: Set[String]): Future[Map[String, String]] =
      lookup(L.Ids(ids), P.Composite(P.Id, P.Email)) map {
        _.toMap
      }

    /** - GS Admin can see everyone
     * - everyone else can see their own network + networks you have purview over
     *
     * @param acl
     * @return
     */
    override def findNetworkIdsVisibleByUserPurview(acl: UserACL): Future[Set[NetworkId]] = {
      if (acl.isAdmin) {
        networkColl.find($, $("id" -> 1)) map {
          _.map { x => NetworkId(x.as[String]("id")) }.toSet
        }
      } else
        Future(acl.userPurviewIds + acl.networkId)
    }

    override def findUserIdsVisibleById(acl: UserACL): Future[Set[String]] =
      lookup(L.Entitlements(acl).forAction(UserProfileActions.view), P.Id).map({
        _.toSet
      })

    override def findNetworkIdsByIds(ids: Set[String]): Future[Map[String, NetworkId]] =
      lookup(L.Ids(ids), (P.Id, P.NetworkId)) map {
        _.toMap
      }

    override def findFirstAndLastNamesAndEmailsByIds(ids: Set[String]): Future[Map[String, (String, String, String)]] =
      lookup(L.Ids(ids), (
        P.Id,
        P.FirstName,
        P.LastName,
        P.Email)) map {
        _.map { case (id, fName, lName, email) => (id, (fName, lName, email)) }.toMap
      }
  }


  object Mongo {

    val CollectionName = "users"

    def migrate(db: MongoDB): Unit = {
      val coll = db.createCollection(CollectionName, $)
      coll.createIndex($("id" -> 1), $("unique" -> true))
      coll.createIndex($("email" -> 1), $("unique" -> true))
    }
  }

}
