package com.goldmansachs.marquee.pipg.service.networks

import com.goldmansachs.marquee.pipg.bson.{LegacyNetworkFormats, NetworkFormat}
import com.goldmansachs.marquee.pipg.mongo.snapshot.SnapshotableRepository
import com.goldmansachs.marquee.pipg.mongo.{AsyncCollection, _}
import com.goldmansachs.marquee.pipg.service.transaction.TransactionRepository
import com.goldmansachs.marquee.pipg.service.user.EmailsOf
import com.goldmansachs.marquee.pipg.{ExternalAlias, Network, NetworkCategory, NetworkType}
import com.gs.marquee.simon.http.Operation
import com.gs.marquee.simon.http.Operation._
import com.simonmarkets.networks.ExternalId
import simon.Id.{ExternalNetworkId, NetworkId}

import scala.concurrent.Future


trait NetworksRepository extends SnapshotableRepository[Network] with LegacyNetworkFormats {

  def findNetworks(): Future[List[Network]]

  def entitledFindNetworks(entitlements: Set[String]): Future[List[Network]]

  final def lookup(lookup: NetworkLookup): Operation[Network] = lookup match {
    case NetworkLookup.Id(id)     => findNetworkById(id)
    case NetworkLookup.Name(name) => findNetworkByName(name)
  }

  def findNetworkById(id: NetworkId): Operation[Network]

  def findNetworkByExternalId(externalId: ExternalId): Operation[Network]

  @deprecated("Use externalIdV2 instead", "3.15.2021")
  def findNetworkByExternalId(id: ExternalNetworkId): Operation[Network]

  def entitledFindNetworkById(id: NetworkId, entitlements: Set[String]): Operation[Network]

  def findNetworksById(ids: List[NetworkId]): Future[List[Network]]

  def entitledFindNetworksById(ids: List[NetworkId], entitlements: Set[String]): Future[List[Network]]

  def findNetworksByDistributorAlias(distributorId: String): Future[List[Network]]

  def findNetworkIdsByNames(ids: Iterable[String]): Future[Map[String, NetworkId]]

  def entitledFindNetworkIdsByNames(ids: Iterable[String], entitlements: Set[String]): Future[Map[String, NetworkId]]

  def findNetworkByName(name: String): Operation[Network]

  def entitledFindNetworkByName(name: String, entitlements: Set[String]): Operation[Network]

  def findNetworkByEmail(email: String): Operation[Network]

  def entitledFindNetworkByEmail(email: String, entitlements: Set[String]): Operation[Network]

  def findNetworkByMaskedId(maskedId: String, target: Option[String]): Operation[Network]

  def entitledFindNetworkByMaskedId(maskedId: String, target: Option[String], entitlements: Set[String]): Operation[Network]

  def containsNetworksWithRuleId(id: String): Future[Boolean]

  def findNetworksByTypes(networkType: Seq[String]): Future[List[Network]]

  def findNetworksByCategory(networkCategory: NetworkCategory): Future[List[Network]]

  def entitledFindNetworksByType(networkType: String, entitlements: Set[String]): Future[List[Network]]

  def clientAccountMappings(id: NetworkId): Future[List[String]]

  def networksWithProductPurviewOver(network: NetworkId, issuerSymbolOpt: Option[String]): Future[Set[NetworkId]]

  def entitledNetworksWithProductPurviewOver(network: NetworkId, issuerSymbolOpt: Option[String], entitlements: Set[String]): Future[Set[NetworkId]]

  def approverChain(id: NetworkId, key: String): Operation[List[List[String]]]

  def removeFromApprovers(id: NetworkId, userId: String)(authorId: String, comment: Option[String]): Operation[Network]

  def dynamicRoles(id: NetworkId): Operation[Set[String]]

  def entitledDynamicRoles(id: NetworkId, entitlements: Set[String]): Operation[Set[String]]

  def dynamicRoles(ids: Set[NetworkId]): Future[Map[NetworkId, Set[String]]]

  def entitledDynamicRoles(ids: Set[NetworkId], entitlements: Set[String]): Future[Map[NetworkId, Set[String]]]

  def distributorAlias(id: NetworkId): Operation[Option[ExternalAlias]]

  def entitledDistributorAlias(id: NetworkId, entitlements: Set[String]): Operation[Option[ExternalAlias]]

  def omsAlias(id: NetworkId): Operation[Option[String]]

  def entitledOmsAlias(id: NetworkId, entitlements: Set[String]): Operation[Option[String]]
}

object NetworksRepository {

  class Mongo(
    val entityCollection: AsyncCollection,
    val snapshotCollection: AsyncCollection,
    val transactionRepository: TransactionRepository,
    val emailsOf: EmailsOf) extends NetworksRepository {

    private implicit def ec = entityCollection.executionContext

    def findNetworks(): Future[List[Network]] =
      entityCollection.findAll map { _.toList map NetworkFormat.read }

    def entitledFindNetworks(entitlements: Set[String]): Future[List[Network]] =
      entityCollection.find($("entitlements" -> $("$in" -> $list(entitlements)))) map { _.toList map NetworkFormat.read }

    def findNetworkById(id: NetworkId): Operation[Network] =
      entityCollection.findOne($("id" -> id)) map NetworkFormat.read

    def findNetworkByExternalId(externalId: ExternalId): Operation[Network] =
      entityCollection.findOne($("externalIds.id" -> externalId.id, "externalIds.subject" -> externalId.subject)) map NetworkFormat.read

    def findNetworkByExternalId(id: ExternalNetworkId): Operation[Network] =
      entityCollection.findOne($("externalId" -> id)) map NetworkFormat.read

    def entitledFindNetworkById(id: NetworkId, entitlements: Set[String]): Operation[Network] =
      entityCollection.findOne($("id" -> id, "entitlements" -> $("$in" -> $list(entitlements)))) map NetworkFormat.read

    def findNetworkByName(name: String): Operation[Network] =
      entityCollection.findOne($("name" -> name)) map NetworkFormat.read

    def entitledFindNetworkByName(name: String, entitlements: Set[String]): Operation[Network] =
      entityCollection.findOne($("name" -> name, "entitlements" -> $("$in" -> $list(entitlements)))) map NetworkFormat.read

    def findNetworksById(id: List[NetworkId]): Future[List[Network]] =
      entityCollection.find($("id" -> $("$in" -> id))) map { _.toList map NetworkFormat.read }

    def entitledFindNetworksById(id: List[NetworkId], entitlements: Set[String]): Future[List[Network]] =
      entityCollection.find($("id" -> $("$in" -> id), "entitlements" -> $("$in" -> $list(entitlements)))) map { _.toList map NetworkFormat.read }

    def findNetworksByDistributorAlias(distributorId: String): Future[List[Network]] = {
      entityCollection.find($("distributorAlias.name" -> distributorId)) map { _.toList map NetworkFormat.read }
    }

    def findNetworkIdsByNames(names: Iterable[String]): Future[Map[String, NetworkId]] =
      entityCollection.find($("name" -> $("$in" -> names)), $("id" -> 1, "name" -> 1)) map {
        _.toIterable.map(NetworkFormat.newReader).map { x => x.name -> x.id }.toMap
      }

    def entitledFindNetworkIdsByNames(names: Iterable[String], entitlements: Set[String]): Future[Map[String, NetworkId]] =
      entityCollection.find($("name" -> $("$in" -> names), "entitlements" -> $("$in" -> $list(entitlements))), $("id" -> 1, "name" -> 1)) map {
        _.toIterable.map(NetworkFormat.newReader).map { x => x.name -> x.id }.toMap
      }

    def findNetworksByTypes(networkTypes: Seq[String]): Future[List[Network]] =
      entityCollection.find($("networkTypes" -> $("$in" -> networkTypes))) map { _.toList map NetworkFormat.read }

    override def findNetworksByCategory(networkCategory: NetworkCategory): Future[List[Network]] = {
      val networkTypes: Seq[NetworkType] = NetworkType.fromCategory(networkCategory)
      findNetworksByTypes(networkTypes.map(_.productPrefix))
    }

    def entitledFindNetworksByType(networkType: String, entitlements: Set[String]): Future[List[Network]] =
      entityCollection.find($("networkTypes" -> $("$in" -> List(networkType)), "entitlements" -> $("$in" -> $list(entitlements)))) map { _.toList map NetworkFormat.read }

    def containsNetworksWithRuleId(id: String): Future[Boolean] =
      entityCollection.count($("salesFeeRuleIds" -> id)) map { _ > 0 }

    def findNetworkByEmail(email: String): Operation[Network] = entityCollection.findOne(
      $("$or" -> List(
        $("to" -> $("$elemMatch" -> $("$regex" -> email, "$options" -> "i"))),
        $("cc" -> $("$elemMatch" -> $("$regex" -> email, "$options" -> "i")))))) map NetworkFormat.read

    def entitledFindNetworkByEmail(email: String, entitlements: Set[String]): Operation[Network] = entityCollection.findOne(
      $("$or" -> List(
        $("to" -> $("$elemMatch" -> $("$regex" -> email, "$options" -> "i"))),
        $("cc" -> $("$elemMatch" -> $("$regex" -> email, "$options" -> "i")))),
        "entitlements" -> $("$in" -> $list(entitlements))
      )) map NetworkFormat.read

    def findNetworkByMaskedId(maskedId: String, target: Option[String]): Operation[Network] = {
      val res = target match {
        case Some(t) => entityCollection.findOne($("maskedIds"->$("$elemMatch" -> $("id" -> maskedId, "target" -> t))))
        case None => entityCollection.findOne($("maskedIds.id" -> maskedId))
      }

      res map NetworkFormat.read
    }

    def entitledFindNetworkByMaskedId(maskedId: String, target: Option[String], entitlements: Set[String]): Operation[Network] = {
      val res = target match {
        case Some(t) => entityCollection.findOne($("maskedIds"->$("$elemMatch" -> $("id" -> maskedId, "target" -> t)), "entitlements" -> $("$in" -> $list(entitlements))))
        case None => entityCollection.findOne($("maskedIds.id" -> maskedId, "entitlements" -> $("$in" -> $list(entitlements))))
      }

      res map NetworkFormat.read
    }

    def clientAccountMappings(id: NetworkId): Future[List[String]] = {
      val res = entityCollection.findOne(
        $("id" -> id),
        $("accountMappings" -> true))

      res map NetworkFormat.newReader fold(_ => Nil, _.accountMappings getOrElse Nil)
    }

    def approverChain(id: NetworkId, key: String): Operation[List[List[String]]] = {
      val approverSet = entityCollection.findOne(
        $("id" -> id),
        $(s"approverSet.$key" -> true))

      approverSet map NetworkFormat.newReader map { _.approverSetOpt getOrElse Map.empty } map { _.getOrElse(key, Nil) }
    }

    def networksWithProductPurviewOver(id: NetworkId, issuerSymbolOpt: Option[String]): Future[Set[NetworkId]] = {
      issuerSymbolOpt match {
        case Some(issuerSymbol) =>
          val res = entityCollection.find(
            query = $("purviewNetworks" -> $("$elemMatch" -> $("network" -> id, "issuers" -> $("$elemMatch" -> $("$in" -> Set(issuerSymbol)))))),
            fields = $("id" -> true))

          res map { _.toSet map NetworkFormat.newReader map { _.id } }
        case None => Future(Set.empty)
      }
    }

    def entitledNetworksWithProductPurviewOver(id: NetworkId, issuerSymbolOpt: Option[String], entitlements: Set[String]): Future[Set[NetworkId]] = {
      issuerSymbolOpt match {
        case Some(issuerSymbol) =>
          val res = entityCollection.find(
            query = $("purviewNetworks" -> $("$elemMatch" -> $("network" -> id, "issuers" -> $("$elemMatch" -> $("$in" -> Set(issuerSymbol))))),
              "entitlements" -> $("$in" -> $list(entitlements))),
            fields = $("id" -> true))

          res map { _.toSet map NetworkFormat.newReader map { _.id } }
        case None => Future(Set.empty)
      }
    }

    def removeFromApprovers(id: NetworkId, userId: String)(authorId: String, comment: Option[String]): Operation[Network] = {
      def cleanApproverList(approverList: List[String]): List[String] = for {
        approver <- approverList if approver != userId
      } yield approver

      def cleanApproverListList(approverListList: List[List[String]]): List[List[String]] = {
        for {
          approverList <- approverListList
          updatedApproverList = cleanApproverList(approverList) if updatedApproverList.nonEmpty
        } yield updatedApproverList
      }

      def cleanApproverSet(
        approverStruct: Map[String, List[List[String]]]): Map[String, List[List[String]]] = {
        for {
          (key, approverListList) <- approverStruct
          updatedApproverListList = cleanApproverListList(approverListList) if updatedApproverListList.nonEmpty
        } yield (key, updatedApproverListList)
      }

      def cleanNetwork(network: Network): Network = {
        val updatedApproverSet = cleanApproverSet(network.approverSet)
        network.copy(approverSet = updatedApproverSet)
      }

      for {
        network       <- findNetworkById(id)
        updatedNetwork = cleanNetwork(network)
        _             <- update(authorId, updatedNetwork, comment)
      } yield updatedNetwork
    }

    override def dynamicRoles(id: NetworkId): Operation[Set[String]] = {
      entityCollection.findOne(
        query = $("id" -> id),
        fields = $("_id" -> false, "dynamicRoles" -> true)) map NetworkFormat.newReader map { _.dynamicRoles }
    }

    override def entitledDynamicRoles(id: NetworkId, entitlements: Set[String]): Operation[Set[String]] = {
      entityCollection.findOne(
        query = $("id" -> id, "entitlements" -> $("$in" -> $list(entitlements))),
        fields = $("_id" -> false, "dynamicRoles" -> true)) map NetworkFormat.newReader map { _.dynamicRoles }
    }

    override def dynamicRoles(ids: Set[NetworkId]): Future[Map[NetworkId, Set[String]]] = {
      entityCollection.find(
        query = $("id" -> $("$in" -> ids)),
        fields = $("_id" -> false, "id" -> true, "dynamicRoles" -> true)) map { cursor =>

        val dynamicRoles = for {dbo <- cursor.toVector} yield {
          val reader = NetworkFormat.newReader(dbo)

          reader.id -> reader.dynamicRoles
        }

        dynamicRoles.toMap
      }
    }

    override def entitledDynamicRoles(ids: Set[NetworkId], entitlements: Set[String]): Future[Map[NetworkId, Set[String]]] = {
      entityCollection.find(
        query = $("id" -> $("$in" -> ids), "entitlements" -> $("$in" -> $list(entitlements))),
        fields = $("_id" -> false, "id" -> true, "dynamicRoles" -> true)) map { cursor =>

        val dynamicRoles = for {dbo <- cursor.toVector} yield {
          val reader = NetworkFormat.newReader(dbo)

          reader.id -> reader.dynamicRoles
        }

        dynamicRoles.toMap
      }
    }

    override def distributorAlias(id: NetworkId): Operation[Option[ExternalAlias]] = {
      entityCollection.findOne(
        query = $("id" -> id),
        fields = $("_id" -> false, "distributorAlias" -> true))
        .map(NetworkFormat.newReader).flatMap(v => v.distributorAlias.ok.op)
    }

    override def entitledDistributorAlias(id: NetworkId, entitlements: Set[String]): Operation[Option[ExternalAlias]] = {
      entityCollection.findOne(
        query = $("id" -> id, "entitlements" -> $("$in" -> $list(entitlements))),
        fields = $("_id" -> false, "distributorAlias" -> true))
        .map(NetworkFormat.newReader).flatMap(v => v.distributorAlias.ok.op)
    }

    override def omsAlias(id: NetworkId): Operation[Option[String]] = {
      entityCollection.findOne(
        query = $("id" -> id),
        fields = $("_id" -> false, "omsAlias" -> true))
        .map(NetworkFormat.newReader).flatMap(v => v.omsAlias.ok.op)
    }

    override def entitledOmsAlias(id: NetworkId, entitlements: Set[String]): Operation[Option[String]] = {
      entityCollection.findOne(
        query = $("id" -> id, "entitlements" -> $("$in" -> $list(entitlements))),
        fields = $("_id" -> false, "omsAlias" -> true))
        .map(NetworkFormat.newReader).flatMap(v => v.omsAlias.ok.op)
    }
  }

  object Mongo {
    val CollectionName = "networks_new"
  }
}
