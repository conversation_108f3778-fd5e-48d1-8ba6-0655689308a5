package com.goldmansachs.marquee.pipg.service.user

import com.goldmansachs.marquee.pipg.User
import com.goldmansachs.marquee.pipg.bson.UserFormat
import com.goldmansachs.marquee.pipg.mongo._
import com.goldmansachs.marquee.pipg.service.user.UsersProjection._
import com.mongodb.DBObject
import com.mongodb.casbah.Imports._

import scala.util.Try

sealed trait UsersProjectionMongo[A] {

  def reader: DBObject => A

  def fieldSet: Set[String]

  def asQueryFields: Option[DBObject] = if (fieldSet.isEmpty) None else Some {
    $(fieldSet.toSeq map {x => (x, true)}: _*)
  }
}

object UsersProjectionMongo {

  private case object EntireUser extends UsersProjectionMongo[User] {
    val fieldSet = Set.empty[String]
    val reader: DBObject => User = dbo => UserFormat.read(dbo)
  }

  private case class F1[T](field: String, r: DBObject => T) extends UsersProjectionMongo[T] {
    val fieldSet = Set(field)
    val reader: DBObject => T = dbo => r(dbo)
  }

  private case class F2[T1, T2](_1: F1[T1], _2: F1[T2]) extends UsersProjectionMongo[(T1, T2)] {
    val fieldSet = Set(_1.field, _2.field)
    val reader: DBObject => (T1, T2) = dbo => (_1.reader(dbo), _2.reader(dbo))
  }

  private case class F3[T1, T2, T3](_1: F1[T1], _2: F1[T2], _3: F1[T3]) extends UsersProjectionMongo[(T1, T2, T3)] {
    val fieldSet = Set(_1.field, _2.field, _3.field)
    val reader: DBObject => (T1, T2, T3) = dbo => (_1.reader(dbo), _2.reader(dbo), _3.reader(dbo))
  }

  private case class F4[T1, T2, T3, T4](_1: F1[T1], _2: F1[T2], _3: F1[T3], _4: F1[T4]) extends UsersProjectionMongo[(T1, T2, T3, T4)] {
    val fieldSet = Set(_1.field, _2.field, _3.field, _4.field)
    val reader: DBObject => (T1, T2, T3, T4) = dbo => (_1.reader(dbo), _2.reader(dbo), _3.reader(dbo), _4.reader(dbo))
  }

  private case class F5[T1, T2, T3, T4, T5](_1: F1[T1], _2: F1[T2], _3: F1[T3], _4: F1[T4], _5: F1[T5]) extends UsersProjectionMongo[(T1, T2, T3, T4, T5)] {
    val fieldSet = Set(_1.field, _2.field, _3.field, _4.field, _5.field)
    val reader: DBObject => (T1, T2, T3, T4, T5) = dbo => (_1.reader(dbo), _2.reader(dbo), _3.reader(dbo), _4.reader(dbo), _5.reader(dbo))
  }

  private case class F6[T1, T2, T3, T4, T5, T6](_1: F1[T1], _2: F1[T2], _3: F1[T3], _4: F1[T4], _5: F1[T5], _6: F1[T6]) extends UsersProjectionMongo[(T1, T2, T3, T4, T5, T6)] {
    val fieldSet = Set(_1.field, _2.field, _3.field, _4.field, _5.field, _6.field)
    val reader: DBObject => (T1, T2, T3, T4, T5, T6) = dbo => (_1.reader(dbo), _2.reader(dbo), _3.reader(dbo), _4.reader(dbo), _5.reader(dbo), _6.reader(dbo))
  }

  def apply(p: UsersProjection): UsersProjectionMongo[p.T] = {

    def simple[T](p: Simple[T]): F1[T] = {
      def str(x: String): F1[T] = F1(x, _.as[String](x)).asInstanceOf[F1[T]]
      def strOpt(x: String): F1[T] = F1(x, dbo => dbo.getAs[String](x)).asInstanceOf[F1[T]]
      def networkId: F1[T] = F1("networkId", dbo => simon.Id.NetworkId(dbo.as[String]("networkId"))).asInstanceOf[F1[T]]
      def ldtOpt(x: String): F1[T] = F1(x, dbo => Try(dbo.getLocalDateTime(x)).toOption).asInstanceOf[F1[T]]
      def licenses: F1[T] = F1("licenses", dbo => UserFormat.newReader(dbo).licenses).asInstanceOf[F1[T]]

      p match {
        case Id             => str("id")
        case NetworkId      => networkId
        case Email          => str("email")
        case FirstName      => str("firstName")
        case LastName       => str("lastName")
        case LastVisitedAt  => ldtOpt("lastVisitedAt")
        case DistributorId  => strOpt("distributorId")
        case Licenses       => licenses
      }
    }

    p match {
      case Entire => EntireUser.asInstanceOf[UsersProjectionMongo[p.T]]
      case x: Simple[_] => simple(x).asInstanceOf[UsersProjectionMongo[p.T]]
      case Composite2(p1, p2) => F2(simple(p1), simple(p2)).asInstanceOf[UsersProjectionMongo[p.T]]
      case Composite3(p1, p2, p3) => F3(simple(p1), simple(p2), simple(p3)).asInstanceOf[UsersProjectionMongo[p.T]]
      case Composite4(p1, p2, p3, p4) => F4(simple(p1), simple(p2), simple(p3), simple(p4)).asInstanceOf[UsersProjectionMongo[p.T]]
      case Composite5(p1, p2, p3, p4, p5) => F5(simple(p1), simple(p2), simple(p3), simple(p4), simple(p5)).asInstanceOf[UsersProjectionMongo[p.T]]
      case Composite6(p1, p2, p3, p4, p5, p6) => F6(simple(p1), simple(p2), simple(p3), simple(p4), simple(p5), simple(p6)).asInstanceOf[UsersProjectionMongo[p.T]]
    }
  }
}
