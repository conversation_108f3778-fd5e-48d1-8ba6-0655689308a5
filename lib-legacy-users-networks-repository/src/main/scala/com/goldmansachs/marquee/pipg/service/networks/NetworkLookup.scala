package com.goldmansachs.marquee.pipg.service.networks

import simon.Id.NetworkId

import scala.language.implicitConversions


sealed trait NetworkLookup

object NetworkLookup {

  case class Id(value: NetworkId) extends NetworkLookup {
    override def toString: String = s"id=$value"
  }

  case class Name(value: String) extends NetworkLookup {
    override def toString: String = s"name=$value"
  }

  implicit def id2lookup(x: NetworkId): NetworkLookup = NetworkLookup.Id(x)
}
