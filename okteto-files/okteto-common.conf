# constant okteto variables
oktetoLocalstackURL = "http://localstack-svc:4566/"
oktetoMongoAuthSecret = "sm:config:applicationconfig-mongo-auth"
oktetoMongoURL = "mongodb://mongodb-svc:27017/admin?retryWrites=true"

# run mode
runMode.httpServerConfig.port = 8086
runMode.httpServerConfig.ssl.enabled = false
runMode.httpServerConfig.ssl.keystore.type = "JKS"
runMode.httpServerConfig.ssl.keystore.file = ""
runMode.httpServerConfig.ssl.keystore.password = ""
run-mode.http-server-config.port = 8086
run-mode.http-server-config.ssl.enabled = false
run-mode.http-server-config.ssl.keystore.type = "JKS"
run-mode.http-server-config.ssl.keystore.file = ""
run-mode.http-server-config.ssl.keystore.password = ""

# resolvers
config.resolvers.sm.endpointUrl = ${oktetoLocalstackURL}
config.resolvers.ssm.endpointUrl = ${oktetoLocalstackURL}

# auth
aclClientConfig.httpClient.proxy = null
aclClientConfig.httpClient.auth.proxy = null
aclClientConfig.cacheConfig.config.serviceEndpoint = ${oktetoLocalstackURL}
acl-client-config.http-client.proxy = null
acl-client-config.http-client.auth.proxy = null
acl-client-config.cache-config.config.service-endpoint = ${oktetoLocalstackURL}

authentication.jwt.validator.proxyHost = null
authentication.jwt.validator.proxyPort = null
authentication.jwt.validator.proxy-host = null
authentication.jwt.validator.proxy-port = null

http-client-config.proxy = null
http-client-config.auth.proxy = null

