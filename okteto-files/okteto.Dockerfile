ARG OKTETO_PROJECT_BASE_IMAGE="okteto/builder-img:latest"

ARG ARTIFACTORY_URL=dev
ARG MVN_EXTRA_ARGS=""

FROM ${ARTIFACTORY_URL}/${OKTETO_PROJECT_BASE_IMAGE} AS build

WORKDIR /okteto/src

COPY . /okteto/rsync

RUN for f in $(find /okteto/rsync -name okteto.conf); do cp -n /okteto/rsync/okteto-files/okteto-common.conf $(dirname $f); done

RUN rsync -acr --delete --include='*/' --include='*.scala' --exclude='*' /okteto/rsync/ ./ >/dev/null 2>&1 && rsync -cr /okteto/rsync/ ./ >/dev/null 2>&1

RUN mvn install -s .m2/settings.xml -f pom.xml -DgenerateBackupPoms=false --batch-mode -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn ${MVN_EXTRA_ARGS}

# Use okteto build image with all pre-added dependecies and libraries needed
#ref: https://gitlab.com/simonmarkets/app-deploy/docker-imgs/-/tree/master/okteto/deploy-img/Dockerfile?ref_type=heads
FROM ${ARTIFACTORY_URL}/okteto/deploy-img:latest

# Copy the built files from the build stage to the working directory of the final stage
COPY --from=build /okteto/src /okteto/src
