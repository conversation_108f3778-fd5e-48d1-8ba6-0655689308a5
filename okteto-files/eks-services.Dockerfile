ARG OKTETO_BUILD_PROJECT_IMAGE=dev

ARG ARTIFACTORY_URL=dev

FROM $OKTETO_BUILD_PROJECT_IMAGE AS project

FROM ${ARTIFACTORY_URL}/simon-amazonlinux:latest AS service-user-acl
WORKDIR /opt/simon
COPY --from=project /okteto/src/service-user-acl/target/service-user-acl.tar /opt/simon/
RUN tar -xf /opt/simon/service-user-acl.tar
ENV AWS_ENDPOINT_URL=http://localstack-svc:4566
EXPOSE 8086
CMD /opt/simon/init-bin/start-init.sh && /opt/simon/bin/start-services.sh

FROM ${ARTIFACTORY_URL}/simon-amazonlinux:latest AS service-users
WORKDIR /opt/simon
COPY --from=project /okteto/src/service-users/target/service-users.tar /opt/simon/
RUN tar -xf /opt/simon/service-users.tar
ENV AWS_ENDPOINT_URL=http://localstack-svc:4566
EXPOSE 8086
CMD /opt/simon/init-bin/start-init.sh && /opt/simon/bin/start-services.sh

