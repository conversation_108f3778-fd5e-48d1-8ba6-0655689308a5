#!/bin/sh

root_install="/opt/simon"
export JAVA_HOME="/opt/software/java/jdk-11"
export MARQUEE_CONFIG=$root_install/config
if [[ $config_env == "prod" ]]
then
  ln -s $root_install/config/prod.conf $root_install/config/prod.conf
  HTTPPP="-Dhttp.proxyHost=internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com -Dhttp.proxyPort=3128"
  HTTPPPS="-Dhttps.proxyHost=internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com -Dhttps.proxyPort=3128"
elif [[ $config_env == "qa" ]]
then
  ln -s $root_install/config/qa.conf $root_install/config/qa.conf
  JAVA_REMOTE_DEBUG="-agentlib:jdwp=transport=dt_socket,address=3100,server=y,suspend=n"
  HTTPPP="-Dhttp.proxyHost=internal-qa-squid-elb-1135562419.us-east-1.elb.amazonaws.com -Dhttp.proxyPort=3128"
  HTTPPPS="-Dhttps.proxyHost=internal-qa-squid-elb-1135562419.us-east-1.elb.amazonaws.com -Dhttps.proxyPort=3128"
  HTTPNONPROXY="-Dhttp.nonProxyHosts=localhost|kubernetes.default*|***************|.s3.amazonaws.com|.s3.us-east-1.amazonaws.com"
elif [[ $config_env == "alpha" ]]
then
  ln -s $root_install/config/alpha.conf $root_install/config/alpha.conf
  JAVA_REMOTE_DEBUG="-agentlib:jdwp=transport=dt_socket,address=3100,server=y,suspend=n"
  HTTPPP="-Dhttp.proxyHost=internal-alpha-squid-proxy-elb-1264059358.us-east-1.elb.amazonaws.com -Dhttp.proxyPort=3128"
  HTTPPPS="-Dhttps.proxyHost=internal-alpha-squid-proxy-elb-1264059358.us-east-1.elb.amazonaws.com -Dhttps.proxyPort=3128"
elif [[ $config_env == "okteto" ]]
then
  JAVA_REMOTE_DEBUG="-agentlib:jdwp=transport=dt_socket,address=3100,server=y,suspend=n"
fi

pkill -f "java.*com.simonmarkets.useracl.UserAclApp"

mkdir -p ./logs

MAIN_CONFIG=$root_install/config
MAIN_CLASS=com.simonmarkets.useracl.UserAclApp
JAVA_OPTS="-XX:MaxRAMPercentage=50.0 -XX:+PrintClassHistogram -XX:+UseCompressedOops -XX:+DisableExplicitGC -XX:+PrintGCDetails -XX:+PrintTenuringDistribution -XX:+PrintGCDateStamps -XX:+PrintAdaptiveSizePolicy -XX:+PrintGCTimeStamps -XX:+HeapDumpOnOutOfMemoryError -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=4 -XX:GCLogFileSize=50M -XX:+IgnoreUnrecognizedVMOptions"
FQDN=`hostname`

cd $root_install/bin

if [[ $simon_apmagent == "true" ]]
then
  JAVA_APM_AGENT="-javaagent:${APM_AGENT}"
fi

$JAVA_HOME/bin/java $JAVA_APM_AGENT $JAVA_OPTS $JAVA_REMOTE_DEBUG -DFQDN=$FQDN -Dconfig.file="$root_install/config/${config_env}.conf" $HTTPPP $HTTPPPS -cp "$root_install/gslibs/dependency/*" $MAIN_CLASS
