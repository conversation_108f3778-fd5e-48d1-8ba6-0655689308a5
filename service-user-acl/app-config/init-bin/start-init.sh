#!/bin/bash

cp /opt/simon/etc/supervisord.conf /etc/.

mkdir -p /var/cv/creds

echo "Copying simon-vertx-ssl.jks from S3 in [$config_env] env"

if [[ $config_env == "prod" ]]
then
   aws s3 cp s3://prod-simon-config-data/creds/app-common/simon-vertx-ssl.jks /var/cv/creds/simon-vertx-ssl.jks
elif [[ $config_env == "qa" ]]
then
   aws s3 cp s3://qa-simon-config-data/creds/app-common/simon-vertx-ssl.jks /var/cv/creds/simon-vertx-ssl.jks
elif [[ $config_env == "alpha" ]]
then
   aws s3 cp s3://alpha-simon-config-data/creds/app-common/simon-vertx-ssl.jks /var/cv/creds/simon-vertx-ssl.jks
elif [[ $config_env == "okteto" ]]
then
   echo Skipped
else
  echo "[ERROR] - Environment Specified [$config_env] does exist, so exit..."
  exit 255
fi
