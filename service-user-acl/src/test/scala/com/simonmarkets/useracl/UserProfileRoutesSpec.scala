package com.simonmarkets.useracl

import akka.actor.ActorSystem
import akka.http.scaladsl.model.headers.OAuth2BearerToken
import akka.http.scaladsl.model.{ContentTypes, StatusCodes}
import akka.http.scaladsl.server._
import akka.http.scaladsl.testkit._
import akka.testkit.javadsl.TestKit
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.goldmansachs.marquee.pipg.{IdHubOrganization, UserACL}
import com.simonmarkets.capabilities.{Capabilities, LearnContentCapabilities, SimonUICapabilities}
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.resteasy.authn.User
import com.simonmarkets.shared.MaskedId
import com.simonmarkets.useracl.api.codec.UserProfileCodecs
import com.simonmarkets.useracl.client.IcnClient
import com.simonmarkets.useracl.domain.{ICapitalProduct, MaskedIds, UnifiedUserProfile, UserProfile}
import com.simonmarkets.useracl.service.UserProfileService
import com.simonmarkets.useracl.user.UserCapabilities
import com.simonmarkets.users.common.{LandingPage, LoginMode}
import org.mockito.ArgumentMatchers.{any, eq => meq}
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterAll, Matchers, WordSpecLike}
import simon.Id.NetworkId

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}

class UserProfileRoutesSpec extends TestKit(ActorSystem("UserProfileRoutesSpec")) with WordSpecLike with BeforeAndAfterAll
  with MockitoSugar with Matchers with ScalatestRouteTest with DirectivesWithCirce with UserProfileCodecs {

  implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  implicit val traceId: TraceId = TraceId("UserProfileRoutesSpec")

  val userProfileService: UserProfileService = mock[UserProfileService]
  val userACLDirective: UserAclAuthorizedDirective = mock[UserAclAuthorizedDirective]
  val icnClient: IcnClient = mock[IcnClient]

  val routes: Route = UserProfileRoutes(userProfileService, userACLDirective).routes

  val admin: UserACL = TestUserACL("admin-user", NetworkId("net-1"), capabilities = Set(Capabilities.Admin))
  val user: User = mock[User]

  override def afterAll(): Unit = TestKit.shutdownActorSystem(system)

  "UserProfileRoutes" should {
    when(userACLDirective.authorizedUser(any[String])) thenReturn tprovide((traceId, user, admin))

    "get profile" in {

      val network = Network(NetworkId("netId"), name = "Test Network",
        idHubOrganization = IdHubOrganization(0, "idhub"),
        maskedIds = Set(MaskedId("target", "id1"), MaskedId("target", "id2")),
        networkCode = "nc",
        loginMode = LoginMode.SSOAndUsernamePassword,
        eventInfo = com.simonmarkets.networks.common.EventInfo.Default,
        landingPage = Some(LandingPage.SIMON),
        wlpUrl = Some("someWlpUrl")
      )
      val userProfile = getTestUserProfile(admin, network)
      when(userProfileService.getOwnUserProfile(any[Option[String]])(any[TraceId], meq(admin))) thenReturn Future.successful(userProfile)

      Get("/simon/api/v1/my-profile") ~> routes ~>
        check {
          status shouldEqual StatusCodes.OK
          contentType shouldEqual ContentTypes.`application/json`
          entityAs[String] shouldEqual userProfile.asJson.deepDropNullValues.asJsonStr
        }
    }

    "get unified profile" in {

      val network = Network(NetworkId("netId"), name = "Test Network",
        idHubOrganization = IdHubOrganization(0, "idhub"),
        maskedIds = Set(MaskedId("target", "id1"), MaskedId("target", "id2")),
        networkCode = "nc",
        loginMode = LoginMode.SSOAndUsernamePassword,
        eventInfo = com.simonmarkets.networks.common.EventInfo.Default,
        landingPage = Some(LandingPage.SIMON),
      )
      val userUnifiedProfile = getTestUserUnifiedProfile(admin, network)
      when(userProfileService.getUnifiedUserProfile(any[Option[String]])(any[TraceId], meq(admin), any[OAuth2BearerToken])) thenReturn Future.successful(userUnifiedProfile)

      Get("/simon/api/v2/my-profile") ~> routes ~>
        check {
          status shouldEqual StatusCodes.OK
          contentType shouldEqual ContentTypes.`application/json`
          entityAs[String] shouldEqual userUnifiedProfile.asJson.deepDropNullValues.asJsonStr
        }
    }

  }

  def getTestUserProfile(userACL: UserACL, network: Network): UserProfile = UserProfile(
    userId = userACL.userId,
    network = userACL.networkInfo,
    firstName = userACL.firstName,
    lastName = userACL.lastName,
    fullName = userACL.fullName,
    email = userACL.email,
    comparisonValue = s"${userACL.lastName} ${userACL.firstName}".trim,
    capabilities = UserCapabilities.toCapabilities(userACL),
    tradewebId = userACL.omsId,
    humanReadableRole = "",
    roles = userACL.roles.map(_.toString),
    showLearningCenterLinksForIssuers = Set.empty,
    maskedIds = MaskedIds(userACL.maskedIds, network.externalIds),
    hasEndClientSharing = userACL.capabilities.contains(LearnContentCapabilities.ExternalShareViaEndClientShareableId),
    assetClassUICapabilityMap = SimonUICapabilities.toAssetClassUserCapabilityMap,
    cobrandingCustomDisclosure = Some("Some Custom Disclosure"),
    paidRoles = Seq(ICapitalProduct.Architect),
    wlpUrl = network.wlpUrl
  )

  def getTestUserUnifiedProfile(userACL: UserACL, network: Network): UnifiedUserProfile = UnifiedUserProfile(
    simon = getTestUserProfile(userACL, network),
    icn = getTestUserProfile(userACL, network).asJson
  )
}
