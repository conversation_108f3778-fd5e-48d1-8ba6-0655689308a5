package com.simonmarkets.useracl.service

import com.goldmansachs.marquee.pipg.Network.{Action, Info}
import com.goldmansachs.marquee.pipg._
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.capabilities.{Capabilities, LearnContentCapabilities, SalesFeeCapabilities, SimonUICapabilities}
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.NetworkIds.AdminNetworkId
import com.simonmarkets.networks.common.domain.{NetworkVisuals, Visuals}
import com.simonmarkets.networks.common.service.NetworkVisualsService
import com.simonmarkets.networks.common.service.networkservice.BasicNetworkService
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.networks.enums.DomicileCode
import com.simonmarkets.shared.MaskedId
import com.simonmarkets.useracl.api.PaidFeaturesResponse
import com.simonmarkets.useracl.client.IcnClient
import com.simonmarkets.useracl.config.{IssuerConfig, SimonConfig}
import com.simonmarkets.useracl.domain.MaskedIds
import com.simonmarkets.users.api.response.MasterUser
import com.simonmarkets.users.common.{LandingPage, LoginMode}
import com.simonmarkets.users.repository.UserRepository
import com.simonmarkets.users.service.{MasterUserService, UserRef}
import org.mockito.ArgumentMatchers.{eq => meq}
import org.mockito.Mockito.when
import org.scalatest.WordSpec
import org.scalatest.mockito.MockitoSugar
import simon.Id.NetworkId

import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext, ExecutionContextExecutor, Future}

class UserProfileServiceSpec extends WordSpec with MockitoSugar {

  implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  implicit val traceId: TraceId = mock[TraceId]

  private val duration = 5.seconds

  private val issuerConfig = IssuerConfig(Some(Set("GSGroup", "GSFC", "GSBank", "WFCo", "WFBank", "BCS", "BCSBank")))
  private val simonConfig = SimonConfig(issuerConfig)

  private val networkId = AdminNetworkId
  private val network: Network = Network(
    id = networkId, name= "Test Network",
    idHubOrganization = IdHubOrganization(0,"idhub"),
    maskedIds = Set(MaskedId("target","id1"), MaskedId("target","id2")),
    networkCode = "nc",
    loginMode = LoginMode.SSOAndUsernamePassword,
    eventInfo = EventInfo.Default,
    cobrandingCustomDisclosure = Some("Some Custom Disclosure"),
    landingPage = Some(LandingPage.SIMON),
    wlpUrl = Some("someWlpUrl")
  )

  private val networkService = mock[BasicNetworkService]

  private val networkVisualsService = mock[NetworkVisualsService]

  private val userRepository = mock[UserRepository]

  private val masterUserService = mock[MasterUserService]

  private val paidFeatureService = mock[PaidFeaturesServiceImpl]

  private val icnClient = mock[IcnClient]

  private val userProfileService = new UserProfileService(simonConfig, networkService, networkVisualsService, userRepository, masterUserService, paidFeatureService, icnClient)

  private val emptyImpersonatorUserId: String = null
  private val admin = TestUserACL(
    userId = "admin-user",
    networkId = networkId,
    networkInfo = Info.empty,
    firstName = Some("First-name"),
    lastName = Some("Last-name"),
    email = Some("email"),
    capabilities = Set(Capabilities.Admin),
    tradewebId = Some("tradewebId"),
    roles = Set(UserRole.EqPIPGGSAdmin),
    maskedIds = Set(MaskedId("target", "id")),
  )
  private val userRef: UserRef = UserRef(
    login = admin.email,
    networkName = None,
    ssoPrefix = None,
    embeddingInfo = None,
    loginMode = LoginMode.UsernamePassword
  )
  private val masterUser: MasterUser = MasterUser(email = "email", users = List(userRef))
  private val networkVisuals: NetworkVisuals = NetworkVisuals(
    networkId = NetworkId.unwrap(networkId),
    visuals = Visuals(accentColor = Some("red"), showPoweredBySpectrumPDF = Some(true))
  )
  private val paidFeatureResponse = Some(PaidFeaturesResponse(admin.userId, List()))
  private val ssoPrefix = SSOPrefix(
    ssoSystemName = Some("SSO System Name"),
    baseUrl = "Base Url",
    redirectionKey = "Redirection Key",
    isRedirectionKeyEncoded = None,
    simonBase = Some("SIMON Base")
  )
  private val embeddingInfo = EmbeddingInfo(
    hostApplicationUrl = "Url",
    hostApplicationName = Some("Name")
  )
  when(networkService.unentitledGetNetworkById(meq(networkId))(meq(traceId))) thenReturn Future.successful(network)
  when(networkVisualsService.get(meq(networkId))(meq(traceId))) thenReturn Future.successful(networkVisuals)
  when(masterUserService.getMasterUser(meq(admin.email), meq(None), meq(None))(meq(traceId))) thenReturn Future.successful(masterUser)
  when(paidFeatureService.getPaidFeatures(meq(admin.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(admin))) thenReturn Future.successful(paidFeatureResponse)
  "UserProfileService" can {
      "return user profile" in {

        val expectedFullName = "First-name Last-name"
        val expectedHumanReadableRole = "Admin"
        val expectedComparisonValue = "last-name first-name"
        val expectedLearningCenterLinksForIssuers = Set("GSGroup", "GSFC", "GSBank", "WFCo", "WFBank", "BCS", "BCSBank")
        when(userRepository.refreshLastUpdatedTime(admin.userId)(traceId)) thenReturn Future.successful(1)
        val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, admin), duration)

        assert(profile.userId == admin.userId)
        assert(profile.network == admin.networkInfo)
        assert(profile.firstName == admin.firstName)
        assert(profile.lastName == admin.lastName)
        assert(profile.fullName == expectedFullName)
        assert(profile.email == admin.email)
        assert(profile.humanReadableRole == expectedHumanReadableRole)
        assert(profile.comparisonValue == expectedComparisonValue)
        assert(profile.roles == admin.roles.collect { case x: HasLongName => x.longName })
        assert(profile.showLearningCenterLinksForIssuers == expectedLearningCenterLinksForIssuers)
        assert(profile.maskedIds == MaskedIds(admin.maskedIds, network.externalIds))
        assert(profile.category.isEmpty)
        assert(profile.capabilities("canSeeWholesalerPortal"))
        assert(profile.capabilities("canSeeLearningCenterDetails"))
        assert(profile.capabilities("canSeeManagementDashboard"))
        assert(profile.capabilities("canSeeSPPortal"))
        assert(profile.capabilities("canSeeFIAPortal"))
        assert(profile.capabilities("canSeeSVAPortal"))
        assert(profile.capabilities("canBroadcastInNetworkOnly"))
        assert(profile.capabilities("canBroadcastOutsideNetwork"))
        assert(profile.capabilities("canExportCurrentOfferingNetworkIOIs"))
        assert(profile.capabilities("canShareOutsideNetwork"))
        assert(profile.capabilities("canUseBrokerDealer"))
        assert(profile.capabilities("canViewMultipleNetworks"))
        assert(profile.capabilities("canSeeLearningCenter"))
        assert(profile.capabilities("canSeeHoldings"))
        assert(!profile.capabilities("canSeeRIAPopUp"))
        assert(profile.capabilities("canSeeBuilder"))
        assert(profile.capabilities("canSeeClientHoldingsByHousehold"))
        assert(profile.capabilities("canSeeLearningCenterMyNetwork"))
        assert(!profile.capabilities("canSeeLearningCenterV2"))
        assert(profile.capabilities("canSeeDeveloperFeatures"))
        assert(profile.capabilities("canSeeBuilderV2"))
        assert(profile.capabilities("canSeeLearningCenterV2TrainingUpload"))
        assert(profile.capabilities("canSeeCarrierPortalRateUpload"))
        assert(profile.assetClassUICapabilityMap.equals(SimonUICapabilities.toAssetClassUserCapabilityMap))
        assert(profile.cobrandingCustomDisclosure.equals(network.cobrandingCustomDisclosure))
        assert(profile.visuals.equals(Option(networkVisuals.visuals)))
        assert(profile.wlpUrl.contains("someWlpUrl"))
      }
      "user in single network returns empty list in otherProfiles" in {

        val user = admin.copy(userId = "singlenetworkuser", networkInfo = Info.test("SIMON Admin"))

        when(masterUserService.getMasterUser(user.email, None)(traceId)) thenReturn Future.successful(masterUser)
        when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
        when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

        val profile1 = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)

        assert(profile1.otherProfiles.isEmpty)
      }

      "user in multiple networks returns filtered list in otherProfiles" in {

        val user = admin.copy(userId = "multiplenetworkuser", networkInfo = Info.test("network2"))
        when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)
        val expectedMasterUserMulti = MasterUser(
          user.email,
          List(
            UserRef(login = "<EMAIL>", networkName = Some("network1"), ssoPrefix = Some(ssoPrefix), embeddingInfo = None, loginMode = LoginMode.SSOAndUsernamePassword),
            UserRef(login = "<EMAIL>", networkName = Some("network2"), ssoPrefix = Some(ssoPrefix), embeddingInfo = None, loginMode = LoginMode.SSOAndUsernamePassword),
            UserRef(login = "<EMAIL>", networkName = Some("network3"), ssoPrefix = None, embeddingInfo = Some(embeddingInfo), loginMode = LoginMode.Embedded),
          )
        )
        val actualOtherProfiles =
          List(
            UserRef(login = "<EMAIL>", networkName = Some("network1"), ssoPrefix = Some(ssoPrefix), embeddingInfo = None, loginMode = LoginMode.SSOAndUsernamePassword),
            UserRef(login = "<EMAIL>", networkName = Some("network3"), ssoPrefix = None, embeddingInfo = Some(embeddingInfo), loginMode = LoginMode.Embedded)
        )

        when(masterUserService.getMasterUser(user.email, None, None)(traceId)) thenReturn Future.successful(expectedMasterUserMulti)
        when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)

        val profile1 = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)

        assert(profile1.otherProfiles == actualOtherProfiles)
      }

      "return NetworkTypes" in {
        val networkLocal = network.copy(networkTypes = Some(List(NetworkType.HedgeProvider, NetworkType.BrokerDealer)))
        val user = admin.copy(userId = "non-admin", capabilities = Set(LearnContentCapabilities.ExternalShareViaEndClientShareableId), networkTypes = networkLocal.networkTypes)
        when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)
        when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)

        val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)

        assert(profile.networkTypes == networkLocal.networkTypes)
      }

    "a non admin user can retrieve network contactInfo2 and contactInfo2Name" in {
      val networkLocal = network.copy(contactInfo2 =
        Some(List(
          ContactInfo2(name = Some("contact1"), url = Some("www.fakeurl.com/userid/123"), urlDisplayText = Some("urlDisplayText1"), email = Some("<EMAIL>"), phone = Some("555-1234"), contactType = Some(ContactType.HomeOffice)),
          ContactInfo2(name = Some("contact2"), url = Some("www.fakeurl.com/userid/234"), urlDisplayText = Some("urlDisplayText2"), email = Some("<EMAIL>"), phone = Some("555-4567"), contactType = Some(ContactType.SIMON)),
          ContactInfo2(name = Some("contact3"), url = Some("www.fakeurl.com/userid/345"), urlDisplayText = Some("urlDisplayText3"), email = Some("<EMAIL>"), phone = Some("888-7890"), contactType = Some(ContactType.HomeSupport))
        )), contactInfo2Name = Some("AlternativeNetworkName"))
      val user = TestUserACL(userId = "non-admin", networkId = networkId)
      when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)
      when(networkService.unentitledGetNetworkById(meq(networkId))(meq(traceId))) thenReturn Future.successful(networkLocal)
      when(masterUserService.getMasterUser(meq(user.email), meq(None) ,meq(None))(meq(traceId))) thenReturn Future.successful(masterUser)
      when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)

      val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)

      assert(profile.networkContactInfo2 == networkLocal.contactInfo2)
      assert(profile.networkContactInfo2Name == networkLocal.contactInfo2Name)
    }

    "a user's profile will have the uiViewCardOverrides that were applied to the network they belong to" in {
      val overriddenNetwork = network.copy(uiViewCardOverrides = Map("StructuredInvestmentsView" -> List("SomePromo", "SomePromo")))
      val user = TestUserACL(userId = "non-admin", networkId = networkId)
      when(networkService.unentitledGetNetworkById(meq(networkId))(meq(traceId))) thenReturn Future.successful(overriddenNetwork)
      when(masterUserService.getMasterUser(meq(user.email), meq(None), meq(None))(meq(traceId))) thenReturn Future.successful(masterUser)
      when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
      when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

      val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)

      assert(profile.uiViewCardOverrides == overriddenNetwork.uiViewCardOverrides)
    }

    "a user's profile will have the uiViewCardOverridesExpiryDate that were applied to the network they belong to" in {
      val overriddenNetwork = network.copy(uiViewCardOverridesExpiryDate = Map("Structured Investments" -> "12/20/23"))
      val user = TestUserACL(userId = "non-admin", networkId = networkId)
      when(networkService.unentitledGetNetworkById(meq(networkId))(meq(traceId))) thenReturn Future.successful(overriddenNetwork)
      when(masterUserService.getMasterUser(meq(user.email), meq(None), meq(None))(meq(traceId))) thenReturn Future.successful(masterUser)
      when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
      when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

      val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)

      assert(profile.uiViewCardOverridesExpiryDate == overriddenNetwork.uiViewCardOverridesExpiryDate)
    }
    /*"can see RIAPopUP is true only when network is Ria even if user has the capability" in {
      val user = admin.copy(userId = "non-admin", capabilities = Set("viewUIRIAPopUpViaNetworkType", "viewUISPManagementDashboard"), networkTypes = Some(List[NetworkType](NetworkType.RIA)))
      val profile = Await.result(userProfileService.getOwnUserProfile()(traceId, user), duration)

      assert(profile.capabilities.canSeeManagementDashboard)
      assert(profile.capabilities.canSeeSPPortal)
      assert(!profile.capabilities.canSeeFIAPortal)
      assert(profile.capabilities.canSeeLearningCenter)
      assert(profile.capabilities.canSeeRIAPopUp)

    }*/

    "getUserViewOfProfile can see holdings doc upload only when the user has configured it" in {
      val user = admin.copy(userId = "non-admin", capabilities = Set("viewUIHoldingsDocUpload"))
      when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
      when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

      val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)


      assert(profile.capabilities("canSeeHoldingsDocUpload"))
    }

    "a non admin user canSeeBuilder when they have one payoff with build action" in {
      val user = admin.copy(userId = "non-admin", capabilities = Set("viewBuilderViaBuildPayoffEntitlements", "viewUIBuilderV2ViaBuildPayoffEntitlement"),
        payoffEntitlementsV2 = {
          Map("CS" -> Map("BufferedLevelNote" -> List("Trade"), "AutoCallable" -> List(), "PointToPoint" -> List("Trade")),
            "GS" -> Map("BufferedLevelNote" -> List("build"), "AutoCallable" -> List(), "PointToPoint" -> List("Trade")),
            "BCS" -> Map("BufferedLevelNote" -> List("Trade"), "AutoCallable" -> List(), "PointToPoint" -> List("Trade")),
            "JPMC" -> Map("BufferedLevelNote" -> List("Trade"), "AutoCallable" -> List(), "PointToPoint" -> List("Trade"))
          ).mapValues(m => m.mapValues(actions => actions.map(Action(_)).toSet))
        })
      when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
      when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

      val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)


      assert(profile.capabilities("canSeeBuilder"))
      assert(profile.capabilities("canSeeBuilderV2"))
    }

    "a non admin user can not see builder when they have no payoff with build action" in {
      val user = admin.copy(userId = "non-admin", capabilities = Set("viewUIHoldingsDocUpload"),
        payoffEntitlementsV2 = Map("GS" -> Map("BufferedLevelNote" -> List("trade")))
          .mapValues(m => m.mapValues(actions => actions.map(Action(_)).toSet)
        ))
      when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
      when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)
      val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)

      assert(!profile.capabilities("canSeeBuilder"))
      assert(!profile.capabilities("canSeeBuilderV2"))
    }

    "a non admin user can see contract edit when they have one payoff with edit action" in {
      val user = admin.copy(userId = "non-admin", capabilities = Set("viewUIContractEditViaEditPayoffEntitlement"),
        payoffEntitlementsV2 = {
          Map("CS" -> Map("BufferedLevelNote" -> List("Trade"), "AutoCallable" -> List(), "PointToPoint" -> List("Trade")),
            "GS" -> Map("BufferedLevelNote" -> List("edit"), "AutoCallable" -> List(), "PointToPoint" -> List("Trade")),
            "BCS" -> Map("BufferedLevelNote" -> List("Trade"), "AutoCallable" -> List(), "PointToPoint" -> List("Trade")),
            "JPMC" -> Map("BufferedLevelNote" -> List("Trade"), "AutoCallable" -> List(), "PointToPoint" -> List("Trade"))
          ).mapValues(m => m.mapValues(actions => actions.map(Action(_)).toSet))
        })
      when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
      when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

      val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)

      assert(profile.capabilities("canSeeContractEditor"))
    }

    "a non admin user can not see contract editor when they have no payoff with build action" in {
      val user = admin.copy(userId = "non-admin", capabilities = Set("viewUIContractEditViaEditPayoffEntitlement"),
        payoffEntitlementsV2 = Map("GS" -> Map("BufferedLevelNote" -> List("trade")))
          .mapValues(m => m.mapValues(actions => actions.map(Action(_)).toSet))
      )
      when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
      when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

      val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)

      assert(!profile.capabilities("canSeeContractEditor"))

    }

    "a non admin user can see upload Carrier Portal Rate Upload when they have the right capabilities" in {
      val user = admin.copy(userId = "non-admin", capabilities = Set("viewUICarrierPortalRateUpload"))

      when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
      when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

      val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)

      assert(profile.capabilities("canSeeCarrierPortalRateUpload"))
    }

    "a non admin user cannot see upload Morningstar upload when they don't have the right capabilities" in {
      val user = admin.copy(userId = "non-admin", capabilities = Set.empty)

      when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
      when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

      val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)

      assert(!profile.capabilities("canSeeCarrierPortalMorningStarMappingUpload"))
    }

    "a non admin user can see Pending Offerings when they have the right capabilities" in {
      val user = admin.copy(userId = "non-admin", capabilities = Set("viewUIPendingOfferings"))

      when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
      when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

      val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)

      assert(profile.capabilities("canSeePendingOfferings"))
    }

    "a non admin user cannot see upload Carrier Portal Rate Upload when they don't have the right capabilities" in {
      val user = admin.copy(userId = "non-admin", capabilities = Set.empty)
      when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
      when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

      val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)

      assert(!profile.capabilities("canSeeCarrierPortalRateUpload"))
    }

    "user with ViewUIRfqHomeOffice capability can see RFQ portal as Home office " in {
      val user = admin.copy(userId = "non-admin", capabilities = Set("viewUIRfqHomeOffice"))

      when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
      when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

      val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)
      assert(profile.capabilities("canSeeRfqAsHomeOffice"))
    }

    "user with ViewUIRfqIssuer capability can see RFQ portal as Issuer" in {
      val user = admin.copy(userId = "non-admin", capabilities = Set("viewUIRfqIssuer"))

      when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
      when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

      val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)
      assert(profile.capabilities("canSeeRfqAsIssuer"))
    }

    "user with ViewUIRfqFA capability can see RFQ portal as FA" in {
      val user = admin.copy(userId = "non-admin", capabilities = Set("viewUIRfqFA"))

      when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
      when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

      val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)
      assert(profile.capabilities("canSeeRfqAsFA"))
    }

    "user with ViewUIRfqWholesaler capability can see RFQ portal as Wholesaler" in {
      val user = admin.copy(userId = "non-admin", capabilities = Set("viewUIRfqWholesaler"))

      when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
      when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

      val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)
      assert(profile.capabilities("canSeeRfqAsWholesaler"))
    }
  }

  "isRegSEligible" in {
    val user1 = admin.copy(userId = "non-admin", regSEligible = true)
    when(userRepository.refreshLastUpdatedTime(user1.userId)(traceId)) thenReturn Future.successful(1)
    when(paidFeatureService.getPaidFeatures(meq(user1.userId), meq(Some(user1.networkId)))(meq(traceId), meq(ec), meq(user1))) thenReturn Future.successful(paidFeatureResponse)


    val profile1 = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user1), duration)
    assert(profile1.capabilities("isRegSEligible"))

    val user2 = admin.copy(userId = "non-admin", regSEligible = false)
    when(userRepository.refreshLastUpdatedTime(user2.userId)(traceId)) thenReturn Future.successful(1)
    when(paidFeatureService.getPaidFeatures(meq(user2.userId), meq(Some(user2.networkId)))(meq(traceId), meq(ec), meq(user2))) thenReturn Future.successful(paidFeatureResponse)


    val profile2 = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user2), duration)
    assert(!profile2.capabilities("isRegSEligible"))
  }

  "no fee-related capabilities by default" in {
    val user = admin.copy(userId = "non-admin", capabilities = Set("viewUIRfqWholesaler"))

    when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
    when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

    val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)
    assert(!profile.capabilities("canSeeFaCommission"))
    assert(!profile.capabilities("canSeeHomeOfficeFee"))
    assert(!profile.capabilities("canSeeReOffer"))
    assert(!profile.capabilities("canSeeWholesalerFee"))
    assert(!profile.capabilities("canEditFaCommission"))
    assert(!profile.capabilities("canEditHomeOfficeFee"))
    assert(!profile.capabilities("canEditReOffer"))
    assert(!profile.capabilities("canEditWholesalerFee"))
  }

  "fee-related capabilities" in {
    val user = admin.copy(userId = "non-admin", capabilities = SalesFeeCapabilities.toSet)

    when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
    when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

    val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)
    assert(profile.capabilities("canSeeFaCommission"))
    assert(profile.capabilities("canSeeHomeOfficeFee"))
    assert(profile.capabilities("canSeeReOffer"))
    assert(profile.capabilities("canSeeWholesalerFee"))
    assert(profile.capabilities("canEditFaCommission"))
    assert(profile.capabilities("canEditHomeOfficeFee"))
    assert(profile.capabilities("canEditReOffer"))
    assert(profile.capabilities("canEditWholesalerFee"))
  }

  "lcV2MyNetworkProductTraining capabilities" in {
    val user = admin.copy(userId = "non-admin", capabilities = Set("viewUILearningCenterV2MyNetworkProductTraining"))

    when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
    when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

    val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)
    assert(profile.capabilities("canSeeLearningCenterV2MyNetworkProductTraining"))
  }

  "canSeeAsOfDates capabilities" in {
    val user1 = admin.copy(userId = "non-admin", capabilities = Set("viewUIHoldingsAsOfDates"))

    when(userRepository.refreshLastUpdatedTime(user1.userId)(traceId)) thenReturn Future.successful(1)
    when(paidFeatureService.getPaidFeatures(meq(user1.userId),meq(Some(user1.networkId)))(meq(traceId), meq(ec), meq(user1))) thenReturn Future.successful(paidFeatureResponse)

    val profile1 = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user1), duration)
    assert(profile1.capabilities("canSeeHoldingsAsOfDates"))

    val user2 = admin.copy(userId = "non-admin", capabilities = Set("viewUIAnalyticsAsOfDate"))
    when(userRepository.refreshLastUpdatedTime(user2.userId)(traceId)) thenReturn Future.successful(1)
    when(paidFeatureService.getPaidFeatures(meq(user2.userId), meq(Some(user2.networkId)))(meq(traceId), meq(ec), meq(user2))) thenReturn Future.successful(paidFeatureResponse)

    val profile2 = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user2), duration)
    assert(profile2.capabilities("canSeeAnalyticsAsOfDate"))
  }

  "canSeePendingSPOffering capabilities" in {
    val user = admin.copy(userId = "non-admin", capabilities = Set("approveSPOfferingViaNetwork"))
    when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
    when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

    val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)
    assert(profile.capabilities("canSeePendingSPOfferings"))
    assert(!profile.capabilities("canSeePendingRegisteredAnnuityOfferings"))
    assert(!profile.capabilities("canSeePendingNonRegisteredAnnuityOfferings"))
  }

  "canSeePendingRegisteredOffering capabilities" in {
    val user = admin.copy(userId = "non-admin", capabilities = Set("approveRegisteredAnnuityOfferingViaNetwork"))
    when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
    when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

    val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)
    assert(!profile.capabilities("canSeePendingSPOfferings"))
    assert(profile.capabilities("canSeePendingRegisteredAnnuityOfferings"))
    assert(!profile.capabilities("canSeePendingNonRegisteredAnnuityOfferings"))
  }

  "canSeePendingNonRegisteredOffering capabilities" in {
    val user = admin.copy(userId = "non-admin", capabilities = Set("approveNonRegisteredAnnuityOfferingViaNetwork"))
    when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
    when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

    val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)
    assert(!profile.capabilities("canSeePendingSPOfferings"))
    assert(!profile.capabilities("canSeePendingRegisteredAnnuityOfferings"))
    assert(profile.capabilities("canSeePendingNonRegisteredAnnuityOfferings"))
    }

  "canBatchStageSPOfferings capabilities" in {
    val user = admin.copy(userId = "non-admin", capabilities = Set("EditBatchStagingUnmatchedCusipViaNetwork"))
    when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
    when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

    val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)
    assert(profile.capabilities("canBatchStageSPOfferings"))

    val user2 = admin.copy(userId = "non-admin", capabilities = Set.empty)

    when(userRepository.refreshLastUpdatedTime(user2.userId)(traceId)) thenReturn Future.successful(1)
    when(paidFeatureService.getPaidFeatures(meq(user2.userId), meq(Some(user2.networkId)))(meq(traceId), meq(ec), meq(user2))) thenReturn Future.successful(paidFeatureResponse)

    val profile2 = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user2), duration)
    assert(!profile2.capabilities("canBatchStageSPOfferings"))
  }

  "valid ExternalShareViaEndClientShareableId return hasEndClientSharing true" in {
    val user = admin.copy(userId = "non-admin", capabilities = Set(LearnContentCapabilities.ExternalShareViaEndClientShareableId))
    when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
    when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

    val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)
    assert(profile.hasEndClientSharing)
  }

  "capabilities does not include ExternalShareViaEndClientShareableId return hasEndClientSharing false" in {
    val user = admin.copy(userId = "non-admin", capabilities = Set.empty)
    when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
    when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

    val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)
    assert(!profile.hasEndClientSharing)
  }

  "a user's profile will have the sessionInactivityTimeout that were applied to the network they belong to" in {
    val overriddenNetwork = network.copy(sessionInactivityTimeout = Some(30))
    val user = TestUserACL(userId = "non-admin", networkId = networkId)
    when(networkService.unentitledGetNetworkById(meq(networkId))(meq(traceId))) thenReturn Future.successful(overriddenNetwork)
    when(masterUserService.getMasterUser(meq(user.email), meq(None), meq(None))(meq(traceId))) thenReturn Future.successful(masterUser)
    when(userRepository.refreshLastUpdatedTime(user.userId)(traceId)) thenReturn Future.successful(1)
    when(paidFeatureService.getPaidFeatures(meq(user.userId), meq(Some(networkId)))(meq(traceId), meq(ec), meq(user))) thenReturn Future.successful(paidFeatureResponse)

    val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)

    assert(profile.sessionInactivityTimeout == overriddenNetwork.sessionInactivityTimeout)
  }

  "return user's network domiciles" in {
    val user = admin
    val usDomiciledNetwork = network.copy(domiciles = Some(Set(DomicileCode.US)))

    when(networkService.unentitledGetNetworkById(meq(networkId))(meq(traceId))) thenReturn Future.successful(usDomiciledNetwork)
    val profile = Await.result(userProfileService.getOwnUserProfile(Option(emptyImpersonatorUserId))(traceId, user), duration)
    assert(profile.domiciles.contains(Set(DomicileCode.US)))
  }

}
