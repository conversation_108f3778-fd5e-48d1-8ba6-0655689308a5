package com.simonmarkets.useracl.service

import com.goldmansachs.marquee.pipg.UserAclField._
import com.goldmansachs.marquee.pipg._
import com.simonmarkets.api.users.cache.UserAclRepository
import com.simonmarkets.api.users.cache.dynamodb.CachedUserAcl
import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.NetworksCapabilities.ViewNetworkViaNetwork
import com.simonmarkets.capabilities.UsersCapabilities.{ImpersonateUserViaNetwork, ViewUserViaNetwork}
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.NetworkIds.AdminNetworkId
import com.simonmarkets.networks.common.repository.NetworkRepository
import com.simonmarkets.networks.common.{Network => NetworkCommon}
import com.simonmarkets.networks.{ExternalId, SmaStrategyAndUnderliers}
import com.simonmarkets.shared.MaskedId
import com.simonmarkets.syntax.{AwaitTimeout, futureOpsConversion}
import com.simonmarkets.useracl.api.{CacheSyncRequest, GetUserAclRequest, UserAclQueryRequest}
import com.simonmarkets.useracl.config.AclServiceConfiguration
import com.simonmarkets.useracl.gen.UserAclFieldGen._
import com.simonmarkets.users.common.AttributeType.FaNumber
import com.simonmarkets.users.common.{AttributeType, Context, IdType, LoginMode, User, UserType}
import com.simonmarkets.users.repository.UserRepository
import com.simonmarkets.users.repository.filters._
import org.mockito.ArgumentMatchers.{any, eq => exact}
import org.mockito.Mockito.when
import org.mockito.{ArgumentMatchers => MM}
import org.scalacheck.{Gen, Shrink}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.prop.PropertyChecks
import org.scalatest.{Matchers, WordSpec}
import simon.Id.NetworkId

import java.time.LocalDateTime

import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class UserAclServiceSpec extends WordSpec with Matchers with MockitoSugar with PropertyChecks {

  private implicit val traceId: TraceId = TraceId("UserAclServiceSpec")
  private implicit val awaitTimeout: AwaitTimeout = AwaitTimeout(5.seconds)

  private def dummyUser(userId: String): User =
    User(
      id = userId,
      networkId = NetworkId(s"network-$userId"),
      createdAt = LocalDateTime.now(),
      createdBy = s"createdBy-$userId",
      updatedAt = LocalDateTime.now(),
      updatedBy = s"updatedBy-$userId",
      emailSentAt = None,
      emailSentBy = None,
      lastVisitedAt = None,
      email = s"email-$userId",
      firstName = s"firstName-$userId",
      lastName = s"lastName-$userId",
      distributorId = Some(s"distributorId-$userId"),
      omsId = Some(s"omsId-$userId"),
      roles = Set.empty,
      idpLoginId = s"idpLoginId-$userId",
      loginMode = LoginMode.SSOAndUsernamePassword,
      userType = UserType.Human,
      eventInfo = com.simonmarkets.users.common.EventInfo.Default,
      externalIds = Seq(ExternalId("subject", s"id-$userId")),
      groups = Map(GroupIdType.DistributorId.productPrefix -> Set("hi", "bye")),
    )

  private def dummyNetwork(networkId: NetworkId): NetworkCommon =
    NetworkCommon(networkId, s"name-$networkId", IdHubOrganization(networkId.hashCode(), s"IdHubOrganizationName-$networkId"), networkCode = "nc",
      loginMode = LoginMode.SSOAndUsernamePassword, eventInfo = com.simonmarkets.networks.common.EventInfo.Default,
      group = Some("groupName"), capabilities = Map("preTradeIOIEnabled" -> List("view")))

  private trait ServiceFixtureBase {
    val userRepository: UserRepository = mock[UserRepository]
    val networkRepository: NetworkRepository = mock[NetworkRepository]
    val userAclRepository: UserAclRepository = mock[UserAclRepository]
    val aclServiceConfiguration: AclServiceConfiguration = mock[AclServiceConfiguration]

    val userAclService: UserAclService =
      new UserAclService(
        userRepository,
        networkRepository,
        userAclRepository,
        aclServiceConfiguration
      )(
        ExecutionContext.global
      )

    val adminRole = "adminRole"
    val testViewer = "testViewer"
    val testImpersonator = "testImpersonator"
    val testNoNetworkKeysRole = "testNoNetworkKeysRole"

    val testCustomRolesConfig = Set(
      CustomRoleDefinition(
        role = adminRole,
        capabilities = Set(Admin)
      ),
      CustomRoleDefinition(
        role = testViewer,
        capabilities = Set(ViewUserViaNetwork, ViewNetworkViaNetwork)
      ),
      CustomRoleDefinition(
        role = testImpersonator,
        capabilities = Set(ImpersonateUserViaNetwork, ViewNetworkViaNetwork)
      ),
      CustomRoleDefinition(
        role = testNoNetworkKeysRole,
        capabilities = Set(ViewUserViaNetwork, ImpersonateUserViaNetwork)
      )
    )

    val requesterId = "requesterId"
    val requester: User = dummyUser(requesterId).copy(networkId = AdminNetworkId, customRoles = Set(adminRole))
  }

  private trait GetUserAclFixture extends ServiceFixtureBase {
    def request: GetUserAclRequest

    def methodCaller: String = requesterId

    val userId = "userId"
    val user: User = dummyUser(userId)

    val requesterNetwork: NetworkCommon = dummyNetwork(requester.networkId)
    val network: NetworkCommon = dummyNetwork(user.networkId)

    val userAcl: UserACL = UserACL(user.asLegacyUser, network.asLegacyNetwork)

    def getRequesterResponse: Future[Option[User]] = Future.successful(Some(requester))

    def getUserResponse: Future[Option[User]] = Future.successful(Some(user))

    def getRequesterNetworkResponse: Future[Option[NetworkCommon]] = Future.successful(Some(requesterNetwork))

    def getUserNetworkResponse: Future[Option[NetworkCommon]] = Future.successful(Some(network))

    when(userRepository.getById(exact(requester.id))(any[Set[String]])(any[TraceId])).thenReturn(getRequesterResponse)
    when(networkRepository.getById(exact(requester.networkId))(any[Set[String]])).thenReturn(getRequesterNetworkResponse)

    when(userRepository.getUser(any[Set[MongoFilter]])(any[Set[String]])(any[TraceId])).thenReturn(getUserResponse)
    when(networkRepository.getById(exact(user.networkId))(any[Set[String]])).thenReturn(getUserNetworkResponse)

    def getUserAcl: UserACL = userAclService.getUserAcl(methodCaller, request).await
  }

  private trait GetUsersAclFixture extends ServiceFixtureBase {
    def request: UserAclQueryRequest

    def methodCaller: String = requesterId

    val userIds = List("userId1", "userId2", "userId3")
    val users: List[User] = userIds.map(dummyUser)

    val requesterNetwork: NetworkCommon = dummyNetwork(requester.networkId).copy(customRolesConfig = testCustomRolesConfig)
    val networks: List[NetworkCommon] = users.map(u => dummyNetwork(u.networkId))

    val userAcls: List[UserACL] = users.zip(networks).map { case (u, n) => UserACL(u.asLegacyUser, n.asLegacyNetwork) }

    def getRequesterResponse: Future[Option[User]] = Future.successful(Some(requester))

    def getUsersResponse: Future[List[User]] = Future.successful(users)

    def getViewUsersResponse: Future[List[User]] = Future.successful(List(users.head))

    def getImpersonateUsersResponse: Future[List[User]] = Future.successful(users.tail)

    def getRequesterNetworkResponse: Future[Option[NetworkCommon]] = Future.successful(Some(requesterNetwork))

    def getUsersNetworksResponse: Future[List[NetworkCommon]] = Future.successful(networks)

    when(userRepository.getById(exact(requester.id))(any[Set[String]])(any[TraceId])).thenReturn(getRequesterResponse)
    when(networkRepository.getById(exact(requester.networkId))(any[Set[String]])).thenReturn(getRequesterNetworkResponse)

    when(userRepository.getUsers(any[Set[MongoFilter]], any[Option[Int]])(any[Set[String]])(any[TraceId])).thenReturn(getUsersResponse)
    when(networkRepository.getByIds(any[Set[NetworkId]])(any[Set[String]])).thenReturn(getUsersNetworksResponse)

    def getUserAcls: List[UserACL] = userAclService.getUserAcls(methodCaller, request).await
  }

  private trait SingleCreateFilterFixture extends ServiceFixtureBase {
    def principalIdType: Option[String]

    def attributeType: Option[String]

    def networkId: Option[NetworkId] = Some(NetworkId("networkId"))

    def subject: Option[String] = None

    def requestedId: String = "requestedId"

    def requestedValue: String = "requestedValue"

    def createFilter: Try[MongoFilter] = userAclService.createFilter(principalIdType.map(IdType.apply), networkId, subject, attributeType.map(AttributeType.apply), Seq(requestedId), Seq(requestedValue))
  }

  private trait GenCreateFilterFixture extends ServiceFixtureBase {
    val _principalIdTypes: Seq[IdType] = IdType.Values.filterNot(Set(IdType.SSN, IdType.ExternalId).contains)
    val principalIdTypes: Seq[Option[IdType]] = _principalIdTypes.map(Some(_)) :+ None

    val stringGen: Gen[String] = Gen.asciiStr.suchThat(_.length < 128)
    val principleAndAttributeGen :Gen[(Option[IdType], Option[AttributeType])] = for {
      pGen <- Gen.oneOf(principalIdTypes)
      attGen <- if (pGen.isDefined) Gen.const[Option[AttributeType]](None) else Gen.const[Option[AttributeType]](Some(FaNumber))
    } yield((pGen, attGen))

    val networkIdGen: Gen[Option[NetworkId]] = Gen.option(stringGen.map(NetworkId(_)))
    val subjectGen: Gen[Option[String]] = Gen.option(stringGen)
    val requestedIdsGen: Gen[List[String]] = Gen.listOf(stringGen)
    val requestedAttributeValuesGen: Gen[List[String]] = Gen.listOf(stringGen)

    implicit val noShrink: Shrink[(Option[IdType], Option[NetworkId], Option[AttributeType], List[String], List[String])] = Shrink.shrinkAny
  }

  private trait GetCachedEntryFixture extends ServiceFixtureBase {
    val rolesConfig =
      Set(CustomRoleDefinition(role = adminRole, capabilities = Set(Capabilities.Admin)))

    val userId = "userId"
    val user: User = dummyUser(userId)

    val requesterNetwork: NetworkCommon = dummyNetwork(requester.networkId).copy(customRolesConfig = rolesConfig)
    val network: NetworkCommon = dummyNetwork(user.networkId)

    val userAcl: UserACL = UserACL(user.asLegacyUser, network.asLegacyNetwork)
    val cachedUserAcl: CachedUserAcl = CachedUserAcl(userAcl, "traceId", "updated")

    def getRequesterResponse: Future[Option[User]] = Future.successful(Some(requester))

    def getRequesterNetworkResponse: Future[Option[NetworkCommon]] = Future.successful(Some(requesterNetwork))

    def getWithMetadataResponse: Future[Option[CachedUserAcl]] = Future.successful(Some(cachedUserAcl))

    when(userRepository.getById(exact(requester.id))(any[Set[String]])(any[TraceId])).thenReturn(getRequesterResponse)
    when(networkRepository.getById(exact(requester.networkId))(any[Set[String]])).thenReturn(getRequesterNetworkResponse)

    when(userAclRepository.getWithMetadata(exact(userId))(any[TraceId])).thenReturn(getWithMetadataResponse)

    def getCacheEntry: Option[CachedUserAcl] = userAclService.getCacheEntry(requesterId, userId).await
  }

  private trait SyncAclCacheFixture extends ServiceFixtureBase {
    val rolesConfig =
      Set(CustomRoleDefinition(role = adminRole, capabilities = Set(Capabilities.Admin)))

    val requesterNetwork: NetworkCommon = dummyNetwork(requester.networkId).copy(customRolesConfig = rolesConfig)

    val userIds: List[String] = List("userId1", "userId2")
    val users: List[User] = userIds.map(dummyUser)

    val networkIds: List[NetworkId] = users.map(_.networkId)
    val networks: List[NetworkCommon] = networkIds.map(dummyNetwork)

    def userAcls: List[UserACL] = users.zip(networks).map { case (u, n) => UserACL(u.asLegacyUser, n.asLegacyNetwork) }

    def request: CacheSyncRequest

    def getRequesterResponse: Future[Option[User]] = Future.successful(Some(requester))

    def getUsersResponse: Future[List[User]] = Future.successful(users)

    def getRequesterNetworkResponse: Future[Option[NetworkCommon]] = Future.successful(Some(requesterNetwork))

    def getUserNetworksResponse: Future[List[NetworkCommon]] = Future.successful(networks)

    when(userRepository.getById(exact(requester.id))(any[Set[String]])(any[TraceId])).thenReturn(getRequesterResponse)
    when(userRepository.getUsers(any[Set[MongoFilter]], any[Option[Int]])(any[Set[String]])(any[TraceId])).thenReturn(getUsersResponse)
    when(userRepository.getUsersByIds(exact(userIds.toSet))(any[Set[String]])(any[TraceId])).thenReturn(getUsersResponse)

    when(networkRepository.getById(exact(requester.networkId))(any[Set[String]])).thenReturn(getRequesterNetworkResponse)
    when(networkRepository.getByIds(exact(networkIds.toSet))(any[Set[String]])).thenReturn(getUserNetworksResponse)
    when(networkRepository.getNetworks(any[Set[String]])(any[TraceId])).thenReturn(getUserNetworksResponse)

    when(userAclRepository.putAll(exact(userAcls))(any[TraceId])).thenReturn(Future.unit)
    when(userAclRepository.recreateTableWithUserAcls(exact(userAcls))(any[TraceId])).thenReturn(Future.unit)

    def syncAclCache(): Unit = userAclService.syncAclCache(requesterId, request).await
  }

  "UserAclService" should {

    "successfully get UserAcl" when {
      "request has requesterId" in new GetUserAclFixture {
        override val methodCaller: String = "should_be_ignored"
        val request: GetUserAclRequest = GetUserAclRequest(userId, Some(requesterId), None, None, None, None)
        getUserAcl shouldBe userAcl
        getUserAcl.group shouldBe network.group
      }

      "request doesn't have requesterId" in new GetUserAclFixture {
        val request: GetUserAclRequest = GetUserAclRequest(userId, None, None, None, None, None)
        getUserAcl shouldBe userAcl
      }

      "only some fields are required" in new GetUserAclFixture {
        val request: GetUserAclRequest = GetUserAclRequest(userId, None, None, None, None, Some(Set(omsId)))
        // get rid of distributorId and networkInfo since they should be removed
        getUserAcl shouldBe userAcl.copy(
          distributorId = None, networkInfo = Network.Info.empty, networkCapabilities = None,
          smaStrategiesAndUnderliers = None, smaRestrictedIssuers = None, externalIds = None, purviewLicenses = None, purviewNsccCodes = None,
          group = None, firmId = None, whiteLabelPartnerId = None, secondaryEmail = None, iCapitalUserId = None, groups = None, icnGroups = None,
          icnRoles = None, passport = None)
      }
    }

    "fail to get UserAcl" when {
      "there is no real requester" in new GetUserAclFixture {
        override def getRequesterResponse: Future[Option[User]] = Future.successful(None)

        val request: GetUserAclRequest = GetUserAclRequest(userId, None, None, None, None, None)
        a[HttpError] should be thrownBy getUserAcl
      }

      "there is no requester network" in new GetUserAclFixture {
        override def getRequesterNetworkResponse: Future[Option[NetworkCommon]] = Future.successful(None)

        val request: GetUserAclRequest = GetUserAclRequest(userId, None, None, None, None, None)
        a[HttpError] should be thrownBy getUserAcl
      }

      "there is no user" in new GetUserAclFixture {
        override def getUserResponse: Future[Option[User]] = Future.successful(None)

        val request: GetUserAclRequest = GetUserAclRequest(userId, None, None, None, None, None)
        a[HttpError] should be thrownBy getUserAcl
      }

      "there is no user network" in new GetUserAclFixture {
        override def getUserNetworkResponse: Future[Option[NetworkCommon]] = Future.successful(None)

        val request: GetUserAclRequest = GetUserAclRequest(userId, None, None, None, None, None)
        a[HttpError] should be thrownBy getUserAcl
      }
    }

    "successfully get UserAcls" when {
      "request has requesterId" in new GetUsersAclFixture {
        override val methodCaller: String = "should_be_ignored"
        val request: UserAclQueryRequest = UserAclQueryRequest(userIds.toSet, from = Some(requesterId))
        getUserAcls shouldBe userAcls
      }

      "request doesn't have requesterId" in new GetUsersAclFixture {
        val request: UserAclQueryRequest = UserAclQueryRequest(userIds.toSet)
        getUserAcls shouldBe userAcls
      }

      "only some of the users are found" in new GetUsersAclFixture {
        override def getUsersResponse: Future[List[User]] = Future.successful(users.tail)

        val request: UserAclQueryRequest = UserAclQueryRequest(userIds.toSet)
        getUserAcls shouldBe userAcls.tail
      }

      "no users found" in new GetUsersAclFixture {
        override def getUsersResponse: Future[List[User]] = Future.successful(Nil)

        val request: UserAclQueryRequest = UserAclQueryRequest(userIds.toSet)
        getUserAcls shouldBe Nil
      }

      "only some fields are required" in new GetUsersAclFixture {
        val request: UserAclQueryRequest = UserAclQueryRequest(userIds.toSet, fields = Some(Set(omsId)))
        // get rid of distributorId and networkInfo since they should be removed
        getUserAcls shouldBe userAcls.map(_.copy(
          distributorId = None, networkInfo = Network.Info.empty, networkCapabilities = None,
          smaStrategiesAndUnderliers = None, smaRestrictedIssuers = None, externalIds = None, purviewLicenses = None, purviewNsccCodes = None,
          group = None, firmId = None, whiteLabelPartnerId = None, secondaryEmail = None, iCapitalUserId = None, groups = None,
          icnGroups = None, icnRoles = None, passport = None))
      }

      "get only view user acls" in new GetUsersAclFixture {
        override def getRequesterResponse: Future[Option[User]] = Future.successful(Some(requester.copy(customRoles = Set(testViewer))))

        when(userRepository.getUsers(any[Set[MongoFilter]], any[Option[Int]])(exact(Set(s"$ViewUserViaNetwork:${requester.networkId.toString}")))(any[TraceId])).thenReturn(getViewUsersResponse)
        override def request: UserAclQueryRequest = UserAclQueryRequest(userIds.toSet, action = Some("view"))

        getUserAcls shouldBe List(userAcls.head)
      }

      "get only impersonate user acls" in new GetUsersAclFixture {
        override def getRequesterResponse: Future[Option[User]] = Future.successful(Some(requester.copy(customRoles = Set(testImpersonator))))

        when(userRepository.getUsers(any[Set[MongoFilter]], any[Option[Int]])(exact(Set(s"$ImpersonateUserViaNetwork:${requester.networkId.toString}")))(any[TraceId])).thenReturn(getViewUsersResponse)
        override def request: UserAclQueryRequest = UserAclQueryRequest(userIds.toSet, action = Some("impersonate"))

        getUserAcls shouldBe List(userAcls.head)
      }
    }

    "fail to get UserAcls" when {
      "there is no real requester" in new GetUsersAclFixture {
        override def getRequesterResponse: Future[Option[User]] = Future.successful(None)

        val request: UserAclQueryRequest = UserAclQueryRequest(userIds.toSet)
        a[HttpError] should be thrownBy userAclService.getUserAcls(requesterId, request).await
      }

      "there is no requester network" in new GetUsersAclFixture {
        override def getRequesterNetworkResponse: Future[Option[NetworkCommon]] = Future.successful(None)

        val request: UserAclQueryRequest = UserAclQueryRequest(userIds.toSet)
        a[HttpError] should be thrownBy userAclService.getUserAcls(requesterId, request).await
      }

      "there is no user network" in new GetUsersAclFixture {
        override def getUsersNetworksResponse: Future[List[NetworkCommon]] = Future.successful(networks.tail)

        val request: UserAclQueryRequest = UserAclQueryRequest(userIds.toSet)
        val res = userAclService.getUserAcls(requesterId, request).await
        res shouldBe userAcls.tail
      }

      "requester is missing network keys" in new GetUsersAclFixture {
        override def getRequesterResponse: Future[Option[User]] = Future.successful(Some(requester.copy(customRoles = Set(testNoNetworkKeysRole))))
        when(networkRepository.getByIds(any[Set[NetworkId]])(MM.eq(Set.empty[String]))).thenReturn(Future.successful(List.empty[NetworkCommon]))

        val request: UserAclQueryRequest = UserAclQueryRequest(userIds.toSet)
        val res = userAclService.getUserAcls(requesterId, request).await
        res shouldBe List.empty[UserACL]
      }
    }

    "successfully get CacheEntry" when {
      "requester is admin and entry is found" in new GetCachedEntryFixture {
        getCacheEntry shouldBe Some(cachedUserAcl)
      }

      "requester is admin and entry is not found" in new GetCachedEntryFixture {
        override def getWithMetadataResponse: Future[Option[CachedUserAcl]] = Future.successful(None)

        getCacheEntry shouldBe None
      }
    }

    "fail to get CacheEntry" when {
      "requester is not admin" in new GetCachedEntryFixture {
        override def getRequesterResponse: Future[Option[User]] =
          Future.successful(Some(requester.copy(customRoles = Set.empty)))

        a[HttpError] should be thrownBy getCacheEntry
      }
    }

    "successfully sync AclCache" when {
      "update is performed by user ids" in new SyncAclCacheFixture {
        val request: CacheSyncRequest = CacheSyncRequest(Some(userIds), None)
        noException should be thrownBy syncAclCache()
      }

      "update is performed by network ids" in new SyncAclCacheFixture {
        val request: CacheSyncRequest = CacheSyncRequest(None, Some(networkIds))
        noException should be thrownBy syncAclCache()
      }

      "update is performed without user nor network ids" in new SyncAclCacheFixture {
        val request: CacheSyncRequest = CacheSyncRequest(None, None)
        noException should be thrownBy syncAclCache()
      }

      "not all networks found for users" in new SyncAclCacheFixture {
        override def userAcls: List[UserACL] = super.userAcls.tail

        override def getUserNetworksResponse: Future[List[NetworkCommon]] = Future.successful(networks.tail)

        val request: CacheSyncRequest = CacheSyncRequest(None, None)
        noException should be thrownBy syncAclCache()
      }
    }

    "fail to sync AclCache" when {
      "requester is not admin" in new SyncAclCacheFixture {
        override def getRequesterResponse: Future[Option[User]] =
          Future.successful(Some(requester.copy(customRoles = Set.empty)))

        val request: CacheSyncRequest = CacheSyncRequest(None, None)
        a[HttpError] should be thrownBy syncAclCache
      }

      "request tries to update cache using both user and network ids" in new SyncAclCacheFixture {
        val request: CacheSyncRequest = CacheSyncRequest(Some(userIds), Some(networkIds))
        a[HttpError] should be thrownBy syncAclCache
      }
    }

    "successfully create filter" when {
      "principalIdType = guid" in new SingleCreateFilterFixture {
        val principalIdType: Option[String] = Some("guid")
        val attributeType: Option[String] = None
        createFilter shouldBe Success(OrFilter(Seq(UserIdFilter(requestedId)):_*))
      }

      "principalIdType = npn" in new SingleCreateFilterFixture {
        val principalIdType: Option[String] = Some("npn")
        val attributeType: Option[String] = None
        createFilter shouldBe Success(OrFilter(Seq(NpnFilter(requestedId)):_*))
      }

      "principalIdType = email" in new SingleCreateFilterFixture {
        val principalIdType: Option[String] = Some("email")
        val attributeType: Option[String] = None
        createFilter shouldBe Success(OrFilter(Seq(EmailFilter(requestedId)):_*))
      }

      "principalIdType = oms" in new SingleCreateFilterFixture {
        val principalIdType: Option[String] = Some("oms")
        val attributeType: Option[String] = None
        createFilter shouldBe Success(OrFilter(Seq(OmsIdFilter(requestedId)):_*))
      }

      "principalIdType = distributorAlias" in new SingleCreateFilterFixture {
        val principalIdType: Option[String] = Some("distributorAlias")
        val attributeType: Option[String] = None
        createFilter shouldBe Success(OrFilter(Seq(DistributorIdFilter(requestedId, networkId.map(_.toString))):_*))
      }

      "principalIdType = idpId" in new SingleCreateFilterFixture {
        val principalIdType: Option[String] = Some("idpId")
        val attributeType: Option[String] = None
        createFilter shouldBe Success(OrFilter(Seq(IdpIdFilter(requestedId)):_*))
      }

      "principalIdType is None" in new SingleCreateFilterFixture {
        val principalIdType: Option[String] = None
        val attributeType: Option[String] = None
        createFilter shouldBe Success(OrFilter(Seq(UserIdFilter(requestedId)):_*))
      }

       "attributeType = faNumber" in new SingleCreateFilterFixture {
        val principalIdType: Option[String] = None
        val attributeType: Option[String] = Some(AttributeType.FaNumber.productPrefix)
        createFilter shouldBe Success(OrFilter(Seq(FaNumberFilter(requestedValue, networkId.map(NetworkId.unwrap))):_*))
      }

      "attributeType = ICapitalUserId" in new SingleCreateFilterFixture {
        val principalIdType: Option[String] = None
        val attributeType: Option[String] = Some(AttributeType.ICapitalUserId.productPrefix)
        createFilter shouldBe Success(OrFilter(Seq(ICapitalUserIdFilter(requestedValue)):_*))
      }

      "there are multiple requested ids" in new ServiceFixtureBase {
        val requestedIds: Seq[String] = Seq("requestedId1", "requestedId2", "requestedId3:_*")

        val actual: Try[MongoFilter] = userAclService.createFilter(None, None, None, None, requestedIds, Seq.empty)
        val expected: Try[MongoFilter] = Success(OrFilter(requestedIds.map(UserIdFilter): _*))

        actual shouldBe expected
      }

      "it processes any valid combination for principal ids" in new GenCreateFilterFixture {
        val validRequestsGen: Gen[(Option[IdType], Option[NetworkId],  Option[String], Option[AttributeType], List[String], List[String])] = for {
          (principalIdType, attributeType) <- principleAndAttributeGen
          networkId <- networkIdGen
          subject <- subjectGen
          if networkId.isDefined || (!principalIdType.contains(IdType.Distributor) && !attributeType.contains(AttributeType.FaNumber))
          if subject.isDefined || !principalIdType.contains(IdType.ExternalId)
          requestedIds <- requestedIdsGen
          requestedAttributeValues <- requestedAttributeValuesGen
          if (attributeType.nonEmpty && requestedAttributeValues.nonEmpty) || (attributeType.isEmpty && requestedIds.nonEmpty)

        } yield (principalIdType, networkId, subject, attributeType, requestedIds, requestedAttributeValues)

        forAll(validRequestsGen) { case (principalIdType, networkId, subject, attributeType, requestedIds, requestedAttributeValues) =>
          val filter = userAclService.createFilter(principalIdType, networkId, subject, attributeType, requestedIds, requestedAttributeValues)
          filter shouldBe a[Success[_]]
        }
      }
    }

    "fail to create filter" when {
      "there are no requested ids or requested values" in new GenCreateFilterFixture {
        val validRequestsGen: Gen[(Option[IdType], Option[NetworkId], Option[AttributeType])] = for {
          (principalIdType, attributeType)  <- principleAndAttributeGen
          networkId <- networkIdGen
          if !principalIdType.contains(IdType.Distributor) || !attributeType.contains(AttributeType.FaNumber) || networkId.nonEmpty
        } yield (principalIdType, networkId, attributeType)

        forAll(validRequestsGen) { case (principalIdType, networkId, attributeType) =>
          val filter = userAclService.createFilter(principalIdType, networkId, None, attributeType, Seq.empty, Seq.empty)
          filter shouldBe a[Failure[_]]
        }
      }

      "principalIdType = distributorAlias but there is no network ids" in new GenCreateFilterFixture {
        forAll(requestedIdsGen) { requestedIds =>
          val filter = userAclService.createFilter(Some(IdType.Distributor), None, None, None, requestedIds, Seq.empty)
          filter shouldBe a[Failure[_]]
        }
      }

      "principalIdType = ssn" in new GenCreateFilterFixture {
        forAll(requestedIdsGen) { requestedIds =>
          val filter = userAclService.createFilter(Some(IdType.SSN), None, None, None, requestedIds, Seq.empty)
          filter shouldBe a[Failure[_]]
        }
      }

      "attributeType is not defined but there are no requested ids" in new GenCreateFilterFixture {
        forAll(requestedAttributeValuesGen) { requestedAttributeValues =>
          val filter = userAclService.createFilter(None, None, None, None, Seq.empty, requestedAttributeValues)
          filter shouldBe a[Failure[_]]
        }
      }

      "attributeType is defined but there are no requested attribute values" in new GenCreateFilterFixture {
        forAll(requestedIdsGen) { requestedIds =>
          val filter = userAclService.createFilter(None, Some(NetworkId("networkId")), None, Some(AttributeType.FaNumber), requestedIds, Seq.empty)
          filter shouldBe a[Failure[_]]
        }
      }

      "attributeType = faNumber but there is no network ids" in new GenCreateFilterFixture {
        forAll(requestedIdsGen) { requestedAttributeValues =>
          val filter = userAclService.createFilter(None, None, None, Some(AttributeType.FaNumber), Seq.empty, requestedAttributeValues)
          filter shouldBe a[Failure[_]]
        }
      }

      "attributeType = faNumber and principalIdType is also defined" in new GenCreateFilterFixture {
        val filter = userAclService.createFilter(None, None, None, None, Seq.empty, Seq.empty)
        filter shouldBe a[Failure[_]]
      }
    }

    "filter UserACL fields" when {
      val userAcl: UserACL =
        UserACL(
          userId = "userId",
          networkId = NetworkId("networkId"),
          lastVisitedAt = Some(LocalDateTime.now()),
          email = "email",
          firstName = "firstName",
          lastName = "lastName",
          roles = Set(UserRole.DataUpdateUser),
          distributorId = Some("distributorId"),
          omsId = Some("omsId"),
          tradewebEligible = false,
          regSEligible = false,
          isActive = Some(true),
          userPurviewIds = Set(NetworkId("userPurviewIds")),
          issuerPurviewIds = Set(mock[IssuerPurview]),
          purviews = Some(Set(mock[Purview])),
          approverMap = ApproverMap.fromRaw(Map("approverMapKey" -> List(List("approverMapValue")))),
          dynamicRoles = Set("dynamicRoles"),
          locations = Set("locations"),
          faNumbers = Set("faNumbers"),
          custodianFaNumbers = Set(CustodianFaNumber(Custodian.Cetera, "123abc")),
          customRoles = Set("customRoles"),
          capabilities = Set("capabilities"),
          payoffEntitlements = Map("payoffEntitlementsKey" -> Map("payoffEntitlementsKey2" -> List("payoffEntitlementsValue"))),
          payoffEntitlementsV2 = Map("payoffEntitlementsV2Key" -> Map("payoffEntitlementsV2Key2" -> Set(mock[Network.Action]))),
          networkInfo = Network.Info.test("networkInfo"),
          networkLocationHierarchy = Some(mock[LocationNode]),
          maskedIds = Set(MaskedId("maskedIdsTarget", "maskedIdsId")),
          ioiApproverSet = Map("ioiApproverSetKey" -> List(List("ioiApproverSetValue"))),
          networkTypes = Some(List(mock[NetworkType])),
          learnTracks = Seq("learnTrack"),
          learnTracksV2 = Seq(mock[LearnTrack]),
          learnContent = Seq("learnContent"),
          endUserShareableContent = Seq("endUserShareableContent"),
          licenses = Set(mock[License]),
          siCertificationRequirements = Seq(mock[CertificationAlternativesForProduct]),
          annuityCertificationRequirements = Seq(mock[CertificationAlternativesForProduct]),
          definedOutcomeETFCertificationRequirements = Seq(mock[CertificationAlternativesForProduct]),
          certificationProducts = Some(Seq("productId")),
          accountInContext = Some("accountInContext"),
          context = Some(mock[Context]),
          cusips = Set("cusip1"),
          group = Some("groupName"),
          networkCapabilities = Some(Map("preTradeIOIEnabled" -> List("view"))),
          smaStrategiesAndUnderliers = Some(Set(SmaStrategyAndUnderliers("123", Set("SPX")))),
          smaRestrictedIssuers = Some(Set("CITI")),
          payoffCertificationRequirementsList = Seq(mock[CertificationRequirementsForPayoff]),
          videoTracksEntitlements = Map("test" -> List("string")),
          spCertificationRequirements = Seq(mock[CertificationRequirementsForPayoff]),
          altCertificationRequirements = Some(Seq(mock[CertificationAlternativesForProduct])),
          externalIds = Some(Seq(mock[ExternalId])),
          purviewLicenses = Some(Set(mock[License])),
          purviewNsccCodes = Some(Set("nsccCodes")),
          firmId = Some("firmId"),
          whiteLabelPartnerId = Some("whiteLabelPartnerId"),
          secondaryEmail = Some("secondaryEmail"),
          iCapitalUserId = Some("iCapitalUserId"),
          groups = Some(Map(GroupIdType.DistributorId.productPrefix -> Set("hey", "how", "are", "you"))),
          icnGroups = Some(Set("hey", "how", "are", "you")),
          icnRoles = Some(Set("hey", "how", "are", "you")),
          passport = Some(Map("passport" -> 1)),
          productTypeCertificationRequirements = Some(Seq(mock[ProductCertificationRequirements]))
        )

      def userAclNonEmptyFields(userACL: UserACL): Seq[Any] =
        Seq[Iterable[Any]](
          Some(userACL.userId),
          Some(userACL.networkId),
          userACL.lastVisitedAt,
          Some(userACL.email),
          Some(userACL.firstName),
          Some(userACL.lastName),
          userACL.roles,
          userACL.distributorId,
          userACL.omsId,
          Some(userACL.tradewebEligible),
          Some(userACL.regSEligible),
          Some(userACL.isActive),
          userACL.userPurviewIds,
          userACL.issuerPurviewIds,
          userACL.purviews,
          userACL.approverMap.map,
          userACL.dynamicRoles,
          userACL.locations,
          userACL.faNumbers,
          userACL.custodianFaNumbers,
          userACL.customRoles,
          userACL.capabilities,
          userACL.payoffEntitlements,
          userACL.payoffEntitlementsV2,
          Some(userACL.networkInfo.name).filter(_.nonEmpty),
          userACL.networkLocationHierarchy,
          userACL.maskedIds,
          userACL.ioiApproverSet,
          userACL.networkTypes,
          userACL.learnTracks,
          userACL.learnTracksV2,
          userACL.learnContent,
          userACL.endUserShareableContent,
          userACL.licenses,
          userACL.siCertificationRequirements,
          userACL.annuityCertificationRequirements,
          userACL.definedOutcomeETFCertificationRequirements,
          userACL.certificationProducts,
          userACL.accountInContext,
          userACL.context,
          userACL.cusips,
          userACL.group,
          userACL.networkCapabilities,
          userACL.smaStrategiesAndUnderliers,
          userACL.smaRestrictedIssuers,
          userACL.payoffCertificationRequirementsList,
          userACL.videoTracksEntitlements,
          userACL.spCertificationRequirements,
          userACL.altCertificationRequirements,
          userACL.externalIds,
          userACL.purviewLicenses,
          userACL.purviewNsccCodes,
          userACL.firmId,
          userACL.whiteLabelPartnerId,
          userACL.secondaryEmail,
          userACL.iCapitalUserId,
          userACL.groups,
          userACL.icnGroups,
          userACL.icnRoles,
          userACL.passport,
          userACL.productTypeCertificationRequirements
        ).flatten

      val requiredFields: Set[UserAclField] =
        Set(userId, networkId, email, firstName, lastName, tradewebEligible, regSEligible, isActive)

      "no fields are passed" in new ServiceFixtureBase {
        userAclService.filterUserACLFields(userAcl, None) shouldBe userAcl
      }

      "some fields are passed" in new ServiceFixtureBase {
        forAll(userAclFieldsGen) { fields =>
          val modifiedUser = userAclService.filterUserACLFields(userAcl, Some(fields))
          val modifiedUserFields = userAclNonEmptyFields(modifiedUser)
          modifiedUserFields should have size (fields ++ requiredFields). size
        }
      }
    }
  }

}
