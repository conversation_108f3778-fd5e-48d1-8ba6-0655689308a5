package com.simonmarkets.useracl.service

import com.simonmarkets.capabilities.{APIRegisteredCapabilities, Capability, CustomRoleCapabilities, OrdersCapabilities}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildGuidKeys, buildNetworkKeys}
import com.simonmarkets.entitlements.AvailableKeyBuilder
import com.simonmarkets.useracl.api.{APIRegisteredCapability, CapabilitiesByDomain}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpecLike}

class CapabilityServiceSpec extends WordSpecLike
  with Matchers
  with MockitoSugar {

  val service = new CapabilitiesServiceImpl(CustomRoleCapabilities.capabilitiesByDomain)

  "CapabilityService" should {

    "return capabilities for user" in {
      service.getCapabilityNamesByDomain shouldBe CustomRoleCapabilities.capabilitiesByDomain.mapValues(_.toSet.toSeq.sorted)
    }

    "return capabilities details for user" in {
      service.getCapabilityDetailsByDomain shouldBe CustomRoleCapabilities.capabilitiesByDomain.mapValues(_.toDetailedCapabilitySet.toSeq)
    }

    "return expected response for external capabilities route" when {
      val testDomain: String = "TestDomain"
      val testDomain2: String = "TestDomain2"
      val viewCap = Capability("viewResourceViaNetwork", "", Some(List("Platform")), None)
      val editCap = Capability("editResourceViaOwner", "", Some(List("Platform")), None)
      val testApiRegisteredCapability = new APIRegisteredCapabilities {
        override val availableAccessKeyBuilders: Map[String, String] = Map(viewCap.name -> "buildNetworkKeys", editCap.name -> "buildGuidKeys")

        override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] =
          Map(viewCap.name -> AvailableKeyBuilder(buildNetworkKeys), editCap.name -> AvailableKeyBuilder(buildGuidKeys))

        override val DomainName: String = testDomain

        override def toDetailedCapabilitySet: Set[Capability] = Set(viewCap, editCap)

        override def DetailedEditCapabilities: Set[Capability] = Set(editCap)

        override def DetailedViewCapabilities: Set[Capability] = Set(viewCap)
      }
      val testApiRegisteredCapability2 = new APIRegisteredCapabilities {
        override val availableAccessKeyBuilders: Map[String, String] = Map(viewCap.name -> "buildLocationKeys", editCap.name -> "buildLicenseKeys")

        override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] =
          Map(viewCap.name -> AvailableKeyBuilder(buildNetworkKeys), editCap.name -> AvailableKeyBuilder(buildGuidKeys))

        override val DomainName: String = testDomain2

        override def toDetailedCapabilitySet: Set[Capability] = Set(viewCap, editCap)

        override def DetailedEditCapabilities: Set[Capability] = Set(editCap)

        override def DetailedViewCapabilities: Set[Capability] = Set(viewCap)
      }

      val test1CapByDomain = CapabilitiesByDomain(testDomain, Set(APIRegisteredCapability(viewCap, Some("buildNetworkKeys"))),
        Set(APIRegisteredCapability(editCap, Some("buildGuidKeys"))))

      val test2CapByDomain = CapabilitiesByDomain(testDomain2, Set(APIRegisteredCapability(viewCap, Some("buildLocationKeys"))),
        Set(APIRegisteredCapability(editCap, Some("buildLicenseKeys"))))

      val serviceWithTestCapabilities = new CapabilitiesServiceImpl(
        CustomRoleCapabilities.capabilitiesByDomain + (testDomain -> testApiRegisteredCapability) + (testDomain2 -> testApiRegisteredCapability2)
      )


      "return external capabilities for specific domains" in {
        serviceWithTestCapabilities.getApiRegisteredCapabilitiesByDomain(List(testDomain)) shouldBe Seq(test1CapByDomain)
      }

      "return only api registered capabilities" in {
        serviceWithTestCapabilities.getApiRegisteredCapabilitiesByDomain(List(testDomain, OrdersCapabilities.DomainName)) shouldBe Seq(test1CapByDomain)
      }

      "return empty list for non-existent domains" in {
        serviceWithTestCapabilities.getApiRegisteredCapabilitiesByDomain(List("test1", "test2")) shouldBe Seq.empty
      }

      "return the capabilities only for the domains which are exists" in {
        serviceWithTestCapabilities.
          getApiRegisteredCapabilitiesByDomain(List(testDomain, testDomain2, "non-existent")) should contain theSameElementsAs Seq(test1CapByDomain, test2CapByDomain)
      }
    }
  }
}
