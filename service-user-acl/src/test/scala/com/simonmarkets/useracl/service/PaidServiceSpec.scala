package com.simonmarkets.useracl.service

import akka.actor.ActorSystem
import akka.testkit.TestKit
import com.goldmansachs.marquee.pipg.UserACL
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.api.users.HttpACLClient
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.useracl.TestUserView
import com.simonmarkets.useracl.api.{BulkUpsertPaidFeaturesRequest, PaidFeaturesRequest, PaidFeaturesResponse}
import com.simonmarkets.useracl.domain.{ICapitalProduct, IdType, PaidFeatures, PaidRole, RoleSource}
import com.simonmarkets.useracl.repository.PaidFeaturesRepositoryImpl
import org.mockito.Mockito._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mockito.MockitoSugar
import org.scalatest.prop.PropertyChecks
import org.scalatest.{BeforeAndAfterAll, Matchers, WordSpecLike}
import simon.Id.NetworkId

import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext, ExecutionContextExecutor, Future}

class PaidServiceSpec
  extends TestKit(ActorSystem("PaidServiceSpec"))
    with WordSpecLike
    with Matchers
    with MockitoSugar
    with PropertyChecks
    with ScalaFutures
    with BeforeAndAfterAll {

  override def afterAll(): Unit = TestKit.shutdownActorSystem(system)

  implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  implicit val traceId: TraceId = mock[TraceId]
  implicit val userACLMock: UserACL = mock[UserACL]

  val paidFeatureRepository: PaidFeaturesRepositoryImpl = mock[PaidFeaturesRepositoryImpl]
  val aclClient: HttpACLClient = mock[HttpACLClient]
  val parallelism = 1
  private val paidFeaturesService = new PaidFeaturesServiceImpl(paidFeatureRepository, aclClient)

  private val paidFeaturesRepoResponse = PaidFeatures(
    idType = IdType.UserId,
    id = "hamster",
    roles = List(PaidRole(ICapitalProduct.Architect, RoleSource.Stripe)),
    acceptedAccessKeys = Set()
  )

  private val paidFeaturesRepoResponse3 = PaidFeatures(
    idType = IdType.UserId,
    id = "hamster3",
    roles = List(),
    acceptedAccessKeys = Set()
  )

  private val networkPaidFeatureRepoResponse = PaidFeatures(
    idType = IdType.NetworkId,
    id = "hamsterNetwork",
    roles = List(PaidRole(ICapitalProduct.Architect, RoleSource.Manual)),
    acceptedAccessKeys = Set()
  )

  private val combinedPaidFeatureResponse = PaidFeaturesResponse(
    "networkUser",
    List(PaidRole(ICapitalProduct.Architect, RoleSource.Stripe), PaidRole(ICapitalProduct.Architect, RoleSource.Manual))
  )

  "PaidService" can {
    "getPaidFeatures" should {
      "SUCCESS found user subscription by id" in {
        val testUserACL = TestUserACL("hamster", NetworkId("hamsterNetwork"))
        when(paidFeatureRepository.findById("hamster", IdType.UserId))
          .thenReturn(Future.successful(Some(paidFeaturesRepoResponse)))
        when(aclClient.getUserACL("hamster")).thenReturn(Future(testUserACL))
        when(paidFeatureRepository.findById("hamsterNetwork", IdType.NetworkId))
          .thenReturn(Future.successful(None))
        val futResponse = paidFeaturesService.getPaidFeatures("hamster")
        whenReady(futResponse) { response =>
          response shouldBe Some(PaidFeaturesResponse(paidFeaturesRepoResponse.id, paidFeaturesRepoResponse.roles))
        }
      }
      "SUCCESS found user by networkId" in {
        val testUserACL = TestUserACL("networkUser", NetworkId("hamsterNetwork"))
        when(paidFeatureRepository.findById("networkUser", IdType.UserId))
          .thenReturn(Future.successful(None))
        when(aclClient.getUserACL("networkUser")).thenReturn(Future(testUserACL))
        when(paidFeatureRepository.findById(TestUserView.userView.networkId.toString, IdType.NetworkId))
          .thenReturn(Future.successful(Some(networkPaidFeatureRepoResponse)))
        val futResponse = paidFeaturesService.getPaidFeatures("networkUser")
        whenReady(futResponse) { response =>
          response shouldBe Some(PaidFeaturesResponse("networkUser", networkPaidFeatureRepoResponse.roles))
        }
      }

      "SUCCESS found user by userId and networkId" in {
        val testUserACL = TestUserACL("networkUser", NetworkId("hamsterNetwork"))
        when(paidFeatureRepository.findById("networkUser", IdType.UserId))
          .thenReturn(Future.successful(Some(paidFeaturesRepoResponse)))
        when(aclClient.getUserACL("networkUser")).thenReturn(Future(testUserACL))
        when(paidFeatureRepository.findById(TestUserView.userView.networkId.toString, IdType.NetworkId))
          .thenReturn(Future.successful(Some(networkPaidFeatureRepoResponse)))
        val futResponse = paidFeaturesService.getPaidFeatures("networkUser")
        whenReady(futResponse) { response =>
          response shouldBe Some(combinedPaidFeatureResponse)
        }
      }

      "ERROR user does not exist" in {
        when(paidFeatureRepository.findById("nonexistentUser", IdType.UserId))
          .thenReturn(Future.successful(None))
        when(aclClient.getUserACL("nonexistentUser")).thenReturn(Future.failed(HttpError.notFound("user not found")))

        val exception = intercept[HttpError] {
          val response: Future[Option[PaidFeaturesResponse]] = paidFeaturesService.getPaidFeatures("nonexistentUser")
          Await.result(response, 20.seconds)
        }
        exception.getMessage should include("user not found")
      }
    }

    "upsertPaidFeatures" should {
      "insert PaidFeatures when user is found" in {
        val request = PaidFeaturesRequest(id = "hamster5", roles = List())
        val testUserACL = TestUserACL("networkUser", NetworkId("hamsterNetwork"))
        when(paidFeatureRepository.findById("hamster5", IdType.UserId))
          .thenReturn(Future.successful(None))
        when(paidFeatureRepository.insertPaidFeatures(request, IdType.UserId, Some("hamsterNetwork")))
          .thenReturn(Future.successful(paidFeaturesRepoResponse3))
        when(aclClient.getUserACL("hamster5")).thenReturn(Future(testUserACL))
        val futResponse: Future[PaidFeaturesResponse] = paidFeaturesService.upsertPaidFeatures(request, IdType.UserId)
        whenReady(futResponse) { response =>
          response shouldBe PaidFeaturesResponse(paidFeaturesRepoResponse3)
        }
      }

      "update PaidFeatures when user is found" in {
        val request = PaidFeaturesRequest(id = "hamster3", roles = List())
        when(paidFeatureRepository.findById("hamster3", IdType.UserId))
          .thenReturn(Future.successful(Some(paidFeaturesRepoResponse)))
        when(paidFeatureRepository.updatePaidFeatures(request, paidFeaturesRepoResponse))
          .thenReturn(Future.successful(paidFeaturesRepoResponse3))
        val futResponse: Future[PaidFeaturesResponse] = paidFeaturesService.upsertPaidFeatures(request, IdType.UserId)

        whenReady(futResponse) { response =>
          response shouldBe PaidFeaturesResponse(paidFeaturesRepoResponse3)
        }
      }
    }
    "bulkUpsertNetworkUsersPaidFeatures" should {
      "upsert PaidFeatures" in {
        val bulkUpsertRequest = BulkUpsertPaidFeaturesRequest(NetworkId("hamsterNetwork"), List())
        val hamsterNetworkPaidFeaturesRepoResponse = PaidFeatures(
          idType = IdType.NetworkId,
          id = "hamsterNetwork",
          roles = List(),
          acceptedAccessKeys = Set()
        )
        val expectedResponse = PaidFeaturesResponse(hamsterNetworkPaidFeaturesRepoResponse)
        // Step 1: Mock findByUserId
        when(paidFeatureRepository.findById("hamsterNetwork", IdType.NetworkId))
          .thenReturn(Future.successful(None))
        // Step 2: Mock insertPaidFeatures
        val hamsterNetworkRequest = PaidFeaturesRequest(id = "hamsterNetwork", roles = List())
        when(paidFeatureRepository.insertPaidFeatures(hamsterNetworkRequest, IdType.NetworkId, None))
          .thenReturn(Future.successful(hamsterNetworkPaidFeaturesRepoResponse))
        when(paidFeatureRepository.removeUserPaidRoles("hamsterNetwork", List()))
          .thenReturn(List(Future(2L)))
        // Step 3: Test Response
        val futResponse: Future[PaidFeaturesResponse] = paidFeaturesService.bulkUpsertNetworkUsersPaidFeatures(bulkUpsertRequest)
        whenReady(futResponse) { response =>
          response shouldBe expectedResponse
        }
      }
    }
  }
}
