package com.simonmarkets.useracl.repository

import com.goldmansachs.marquee.pipg.UserACL
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.capabilities.{Capabilities, PaidFeaturesCapabilities}
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.NetworkIds.AdminNetworkId
import com.simonmarkets.useracl.api.PaidFeaturesRequest
import com.simonmarkets.useracl.domain.{IdType, PaidFeatures}
import com.simonmarkets.users.repository.UserMongoJsonCodecs
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.bson.collection.Document
import org.scalatest.Matchers._
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, WordSpec}
import simon.Id.NetworkId

import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext}

class PaidFeaturesRepositorySpec extends WordSpec with MockitoSugar with EmbeddedMongoLike with BeforeAndAfter with UserMongoJsonCodecs {

  "PaidFeaturesRepository" can {
    implicit val ec: ExecutionContext = ExecutionContext.global
    implicit val tid: TraceId = TraceId("test")
    val networkId1 = NetworkId("network-id-1")
    val testAdminUserAcl: UserACL = TestUserACL("aclId", AdminNetworkId, capabilities = Set(Capabilities.Admin))
    val testUserAcl: UserACL = TestUserACL("aclId", networkId1, capabilities = Set())
    lazy val collection = db.getCollection[Document]("usersPaidFeature_test")
    lazy val snapshotCollection = db
      .withCodecRegistry(PaidFeaturesFormat.codecRegistry)
      .getCollection[Document]("usersPaidFeature_test.snapshots")

    // Snapshots are not supported by embedded mongo as they implemented via Mongo transactions
    lazy val paidFeaturesRepository = new PaidFeaturesRepositoryImpl(client, collection, snapshotCollection)

    val duration: Duration = 20.seconds

    val paidFeatures1 = PaidFeatures(
      roles = List(),
      acceptedAccessKeys = PaidFeaturesCapabilities.UploadCapabilities ++ PaidFeaturesCapabilities.ViewUserPaidFeatureCapabilities,
      id = "hamster",
      idType = IdType.UserId
    )
    val paidFeaturesWithAccessKeys = paidFeatures1.copy(acceptedAccessKeys = PaidFeatureKeyGenerator.getAcceptedAccessKeys(paidFeatures1))

    before {
      Await.result(collection.drop().toFuture(), duration)
      Await.result(snapshotCollection.drop().toFuture(), duration)
      Await.result(collection.insertOne(PaidFeaturesFormat.write(paidFeaturesWithAccessKeys)).toFuture, duration)
    }

    "findByUserId" should {
      "paidFeature found" in {
        val resultOpt = paidFeaturesRepository.findById("hamster", IdType.UserId)(requester = testAdminUserAcl, traceId = tid)
        val result = Await.result(resultOpt, duration)
        result shouldBe Some(paidFeaturesWithAccessKeys)
      }

      "paidFeature not found, insufficient capabilities" in {
        val resultOpt = paidFeaturesRepository.findById("hamster", IdType.UserId)(requester = testUserAcl, traceId = tid)
        val result = Await.result(resultOpt, duration)
        result shouldBe None
      }
    }
    "insertPaidFeatures" should {
      "error when not enough capabilities" in {
        val exception = intercept[HttpError] {
          val result = paidFeaturesRepository.insertPaidFeatures(
            PaidFeaturesRequest("hamster", List()),
            IdType.UserId
          )(requester = testUserAcl, traceId = tid)
          Await.result(result, duration)
        }
        exception.getMessage should include("User does not have right access keys to insert paidFeatures: aclId")
      }
    }

    "updatePaidFeatures" should {
      "error when not enough capabilities" in {
        val exception = intercept[HttpError] {
          val result = paidFeaturesRepository.updatePaidFeatures(
            PaidFeaturesRequest("hamster", List()),
            PaidFeatures(IdType.UserId, "hamster", List(), Set())
          )(requester = testUserAcl, traceId = tid)
          Await.result(result, duration)
        }
        exception.getMessage should include("User does not have right access keys to update paidFeatures: aclId")
      }
    }
  }
}