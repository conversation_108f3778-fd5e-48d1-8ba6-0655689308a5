package com.simonmarkets.useracl

import akka.actor.ActorSystem
import akka.http.scaladsl.model.{ContentTypes, StatusCodes}
import akka.http.scaladsl.server._
import akka.http.scaladsl.testkit._
import akka.testkit.javadsl.TestKit
import com.goldmansachs.marquee.pipg.UserACL
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.capabilities.{APIRegisteredCapabilities, Capabilities, Capability, CustomRoleCapabilities, SimonUICapabilities}
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildGuidKeys, buildNetworkKeys}
import com.simonmarkets.entitlements.AvailableKeyBuilder
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.NetworkIds.AdminNetworkId
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.useracl.api.{APIRegisteredCapability, APIRegisteredCapabilityResponse, CapabilitiesByDomain}
import com.simonmarkets.useracl.service.CapabilitiesServiceImpl
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterAll, Matchers, WordSpecLike}


class CapabilitiesRoutesSpec extends TestKit(ActorSystem("CapabilitiesRouteSpec")) with WordSpecLike with BeforeAndAfterAll
  with MockitoSugar with Matchers with ScalatestRouteTest with DirectivesWithCirce with JsonCodecs {

  implicit val traceId: TraceId = TraceId("CapabilitiesRouteSpec")
  val userACLDirective: UserAclAuthorizedDirective = mock[UserAclAuthorizedDirective]

  val service = new CapabilitiesServiceImpl(CustomRoleCapabilities.capabilitiesByDomain)

  val routes: Route = new CapabilitiesRoutes(userACLDirective, service).routes

  override def afterAll(): Unit = TestKit.shutdownActorSystem(system)

  val capabilitiesRoute = "/simon/api/v1/capabilities"
  val capabilitiesV2Route = "/simon/api/v2/capabilities"

  def capabilitiesStructureRoute(domains: List[String]) = s"/simon/api/v2/capabilities/structure?domain=${domains.mkString("&domain=")}"

  "CapabilitiesRoute" should {

    "return capabilities for user with admin capability" in {
      val adminUserACL: UserACL = TestUserACL("admin-user", AdminNetworkId, capabilities = Set(Capabilities.Admin))

      when(userACLDirective.authorized(SimonUICapabilities.ViewUIOnboardingToolCapability.name)).thenReturn(tprovide((traceId, adminUserACL)))

      Get(capabilitiesRoute) ~> routes ~>
        check {
          status shouldEqual StatusCodes.OK
          contentType shouldEqual ContentTypes.`application/json`
          entityAs[String] shouldEqual CustomRoleCapabilities.capabilitiesByDomain.mapValues(_.toSet.toSeq.sorted).asJson.deepDropNullValues.asJsonStr
        }
    }
    "return capabilities details for user with admin capability" in {
      val adminUserACL: UserACL = TestUserACL("admin-user", AdminNetworkId, capabilities = Set(Capabilities.Admin))

      when(userACLDirective.authorized(SimonUICapabilities.ViewUIOnboardingToolCapability.name)).thenReturn(tprovide((traceId, adminUserACL)))

      Get(capabilitiesV2Route) ~> routes ~>
        check {
          status shouldEqual StatusCodes.OK
          contentType shouldEqual ContentTypes.`application/json`
          entityAs[String] shouldEqual CustomRoleCapabilities.capabilitiesByDomain.mapValues(_.toDetailedCapabilitySet.toSeq).asJson.deepDropNullValues.asJsonStr
        }
    }
    "return response for capabilities structure route" in {
      val testDomain: String = "TestDomain"
      val testDomain2: String = "TestDomain2"
      val viewCap = Capability("viewResourceViaNetwork", "", Some(List("Platform")), None)
      val editCap = Capability("editResourceViaOwner", "", Some(List("Platform")), None)

      val testApiRegisteredCapability = new APIRegisteredCapabilities {
        override val availableAccessKeyBuilders: Map[String, String] = Map(viewCap.name -> "buildNetworkKeys", editCap.name -> "buildGuidKeys")

        override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] =
          Map(viewCap.name -> AvailableKeyBuilder(buildNetworkKeys), editCap.name -> AvailableKeyBuilder(buildGuidKeys))

        override val DomainName: String = testDomain

        override def toDetailedCapabilitySet: Set[Capability] = Set(viewCap, editCap)

        override def DetailedEditCapabilities: Set[Capability] = Set(editCap)

        override def DetailedViewCapabilities: Set[Capability] = Set(viewCap)
      }
      val testApiRegisteredCapability2 = new APIRegisteredCapabilities {
        override val availableAccessKeyBuilders: Map[String, String] = Map(viewCap.name -> "buildLocationKeys", editCap.name -> "buildLicenseKeys")

        override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] =
          Map(viewCap.name -> AvailableKeyBuilder(buildNetworkKeys), editCap.name -> AvailableKeyBuilder(buildGuidKeys))

        override val DomainName: String = testDomain2

        override def toDetailedCapabilitySet: Set[Capability] = Set(viewCap, editCap)

        override def DetailedEditCapabilities: Set[Capability] = Set(editCap)

        override def DetailedViewCapabilities: Set[Capability] = Set(viewCap)
      }

      val routesWithTestCapabilities = new CapabilitiesRoutes(userACLDirective,
        new CapabilitiesServiceImpl(CustomRoleCapabilities.capabilitiesByDomain +
          (testDomain -> testApiRegisteredCapability) + (testDomain2 -> testApiRegisteredCapability2))).routes

      val test1CapByDomain = CapabilitiesByDomain(testDomain, Set(APIRegisteredCapability(viewCap, Some("buildNetworkKeys"))),
        Set(APIRegisteredCapability(editCap, Some("buildGuidKeys"))))

      val test2CapByDomain = CapabilitiesByDomain(testDomain2, Set(APIRegisteredCapability(viewCap, Some("buildLocationKeys"))),
        Set(APIRegisteredCapability(editCap, Some("buildLicenseKeys"))))

        val adminUserACL: UserACL = TestUserACL("admin-user", AdminNetworkId, capabilities = Set(Capabilities.Admin))

        when(userACLDirective.authorized()).thenReturn(tprovide((traceId, adminUserACL)))

        Get(capabilitiesStructureRoute(List(testDomain, testDomain2))) ~> routesWithTestCapabilities ~>
          check {
            status shouldEqual StatusCodes.OK
            contentType shouldEqual ContentTypes.`application/json`
            entityAs[APIRegisteredCapabilityResponse].capabilities should contain theSameElementsAs List(test1CapByDomain, test2CapByDomain)
          }
    }

  }
}
