package com.simonmarkets.useracl

import com.simonmarkets.users.common.{LoginMode, UserType}
import com.simonmarkets.users.common.api.response.UserView
import simon.Id.NetworkId

import java.time.LocalDateTime

object TestUserView {

  val userView = new UserView(
    id = "String",
    networkId = NetworkId("hamsterNetwork"),
    createdAt = LocalDateTime.MIN,
    createdBy = "String",
    updatedAt = LocalDateTime.MIN,
    updatedBy = "String",
    emailSentAt = None,
    emailSentBy = None,
    lastVisitedAt = None,
    email = "String",
    firstName = "String",
    lastName = "String",
    externalIds = Map(),
    tradewebEligible = true,
    regSEligible = true,
    roles = Set(),
    isActive = true,
    locations = Set(),
    faNumbers = Set(),
    custodianFaNumbers = Set(),
    customRoles = Set(),
    licenses = Set(),
    maskedIds = Set(),
    idpId = None,
    distributorInfo = None,
    accountInContext = None,
    context = None,
    cusips = Set(),
    idpLoginId = "String",
    loginMode = LoginMode.SSO,
    userType = UserType.Human,
    purviewLicenses = Set(),
    purviewNsccCodes = Set(),
    clientSecret = None,
    firmId = None,
    whiteLabelPartnerId = None,
    secondaryEmail = None,
    iCapitalUserId = None,
    userSyncedAt = None,
    landingPage = None,
    groups = Map.empty,
    icnGroups = Set(),
    icnRoles = Set(),
    passport = Map.empty,
    mfas = Map.empty
  )

}
