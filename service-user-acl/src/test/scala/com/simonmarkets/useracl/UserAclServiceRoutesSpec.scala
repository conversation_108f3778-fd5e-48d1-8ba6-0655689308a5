package com.simonmarkets.useracl

import akka.http.scaladsl.model.StatusCodes
import akka.http.scaladsl.server.Route
import akka.http.scaladsl.testkit.{RouteTestTimeout, ScalatestRouteTest}
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.goldmansachs.marquee.pipg.{UserACL, UserAclField}
import com.simonmarkets.api.users.cache.dynamodb.CachedUserAcl
import com.simonmarkets.logging.TraceId
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.useracl.api.codec.UserAclServiceCodecs
import com.simonmarkets.useracl.api.{CacheSyncRequest, GetUserAclByIdsRequest, GetUserAclRequest, UserAclQueryRequest}
import com.simonmarkets.useracl.gen.UserAclFieldGen._
import com.simonmarkets.useracl.service.UserAclService
import org.mockito.ArgumentMatchers.{any, eq => exact}
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.prop.PropertyChecks.forAll
import org.scalatest.{Matchers, WordSpec}
import simon.Id.NetworkId

import scala.concurrent.Future
import scala.concurrent.duration._

class UserAclServiceRoutesSpec
  extends WordSpec
    with Matchers
    with ScalatestRouteTest
    with MockitoSugar
    with DirectivesWithCirce
    with UserAclServiceCodecs {

  private val userId = "userId"
  val userACLDirective: UserAclAuthorizedDirective = mock[UserAclAuthorizedDirective]

  implicit val routeTestTimeout: RouteTestTimeout = RouteTestTimeout(5.seconds)

  val traceId: TraceId = TraceId.randomize

  def userAclServiceRoutes(service: UserAclService): Route =
    UserAclServiceRoutes(service, userACLDirective).routes

  def dummyUserACL(id: String): UserACL = TestUserACL(id, NetworkId(s"network-$id"))

  "UserAclServiceRoutes" should {

    "handle GET UserACL" when {
      val idToSearch = "idToSearch"

      val userACL = dummyUserACL(idToSearch).copy(capabilities = Set("capability"))

      val callerUserAcl = dummyUserACL(userId)
      when(userACLDirective.authorized()).thenReturn(tprovide((traceId, callerUserAcl)))
      def toRequest(fields: Option[Set[UserAclField]]): GetUserAclRequest =
        GetUserAclRequest(userACL.userId, None, None, None, None, fields)

      "service returned success" in {
        val service: UserAclService = mock[UserAclService]

        val request = toRequest(None)
        when(service.getUserAcl(exact(userId), exact(request))(any[TraceId])).thenReturn(Future.successful(userACL))
        val routes = userAclServiceRoutes(service)

        Get(s"/simon/api/v2/principals/$idToSearch") ~> routes ~> check {
          status shouldBe StatusCodes.OK
          entityAs[UserACL] shouldBe userACL
        }
      }

      "service returned failure" in {
        val service: UserAclService = mock[UserAclService]

        val request = toRequest(None)
        when(service.getUserAcl(exact(userId), exact(request))(any[TraceId])).thenReturn(Future.failed(new RuntimeException))
        val routes = userAclServiceRoutes(service)

        Get(s"/simon/api/v2/principals/$idToSearch") ~> routes ~> check {
          status shouldBe StatusCodes.InternalServerError
        }
      }

      "required fields are provided" in {
        forAll(userAclFieldsGen.suchThat(_.nonEmpty)) { fields =>
          val service: UserAclService = mock[UserAclService]

          val request = toRequest(Some(fields))
          when(service.getUserAcl(exact(userId), exact(request))(any[TraceId])).thenReturn(Future.successful(userACL))
          val routes = userAclServiceRoutes(service)

          Get(s"/simon/api/v2/principals/$idToSearch?fields=${fields.mkString(",")}") ~> routes ~> check {
            status shouldBe StatusCodes.OK
            entityAs[UserACL] shouldBe userACL
          }
        }
      }
    }

    "handle GET Capabilities" when {
      val idToSearch = "idToSearch"
      val capabilities = Set("capability")
      val userACL = dummyUserACL(idToSearch).copy(capabilities = capabilities)
      val callerUserAcl = dummyUserACL(userId)
      when(userACLDirective.authorized()).thenReturn(tprovide((traceId, callerUserAcl)))


      "service returns success" in {
        val service: UserAclService = mock[UserAclService]

        val request = GetUserAclRequest.byId(userACL.userId)
        when(service.getUserAcl(exact(userId), exact(request))(any[TraceId])).thenReturn(Future.successful(userACL))
        val routes = userAclServiceRoutes(service)

        Get(s"/simon/api/v2/principals/$idToSearch/capabilities") ~> routes ~> check {
          status shouldBe StatusCodes.OK
          entityAs[Set[String]] shouldBe capabilities
        }
      }

      "service returns failure" in {
        val service: UserAclService = mock[UserAclService]

        val request = GetUserAclRequest.byId(userACL.userId)
        when(service.getUserAcl(exact(userId), exact(request))(any[TraceId])).thenReturn(Future.failed(new RuntimeException))
        val routes = userAclServiceRoutes(service)

        Get(s"/simon/api/v2/principals/$idToSearch") ~> routes ~> check {
          status shouldBe StatusCodes.InternalServerError
        }
      }

    }

    "handle POST UserACL" when {
      val idsToSearch = Set("idToSearch1", "idToSearch2", "idToSearch3", "idToSearch4")
      val httpRequest = GetUserAclByIdsRequest(idsToSearch)
      val serviceRequest = UserAclQueryRequest(idsToSearch)

      val userACLs = idsToSearch.map(dummyUserACL)

      "service returned success" in {
        val service: UserAclService = mock[UserAclService]

        when(service.getUserAcls(exact(userId), exact(serviceRequest))(any[TraceId])).thenReturn(Future.successful(userACLs.toList))
        val routes = userAclServiceRoutes(service)

        Post(s"/simon/api/v2/principals", httpRequest) ~> routes ~> check {
          status shouldBe StatusCodes.OK
          entityAs[Set[UserACL]] shouldBe userACLs
        }
      }
      "service returned failure" in {
        val service: UserAclService = mock[UserAclService]

        when(service.getUserAcls(exact(userId), exact(serviceRequest))(any[TraceId])).thenReturn(Future.failed(new RuntimeException))
        val routes = userAclServiceRoutes(service)

        Post(s"/simon/api/v2/principals", httpRequest) ~> routes ~> check {
          status shouldBe StatusCodes.InternalServerError
        }
      }
    }

    "handle POST UserACL query" when {
      val idsToSearch = Set("idToSearch1", "idToSearch2", "idToSearch3", "idToSearch4")
      val request = UserAclQueryRequest(idsToSearch)

      val userACLs = idsToSearch.map(dummyUserACL)

      "service returned success" in {
        val service: UserAclService = mock[UserAclService]

        when(service.getUserAcls(exact(userId), exact(request))(any[TraceId])).thenReturn(Future.successful(userACLs.toList))
        val routes = userAclServiceRoutes(service)

        Post(s"/simon/api/v2/principals/query", request) ~> routes ~> check {
          status shouldBe StatusCodes.OK
          entityAs[Set[UserACL]] shouldBe userACLs
        }
      }
      "service returned failure" in {
        val service: UserAclService = mock[UserAclService]

        when(service.getUserAcls(exact(userId), exact(request))(any[TraceId])).thenReturn(Future.failed(new RuntimeException))
        val routes = userAclServiceRoutes(service)

        Post(s"/simon/api/v2/principals/query", request) ~> routes ~> check {
          status shouldBe StatusCodes.InternalServerError
        }
      }
    }

    "handle GET cache" when {
      val idToSearch = "idToSearch"
      val cachedUserAcl: CachedUserAcl =
        CachedUserAcl(userACL = dummyUserACL(idToSearch), traceId = "traceId", updated = "updated")

      "service returned success with Some" in {
        val service: UserAclService = mock[UserAclService]

        when(service.getCacheEntry(exact(userId), exact(idToSearch))(any[TraceId]))
          .thenReturn(Future.successful(Some(cachedUserAcl)))

        val routes = userAclServiceRoutes(service)

        Get(s"/simon/api/v2/principals/cache/$idToSearch") ~> routes ~> check {
          status shouldBe StatusCodes.OK
          entityAs[CachedUserAcl] shouldBe cachedUserAcl
        }
      }

      "service returned success with None" in {
        val service: UserAclService = mock[UserAclService]

        when(service.getCacheEntry(exact(userId), exact(idToSearch))(any[TraceId]))
          .thenReturn(Future.successful(None))

        val routes = userAclServiceRoutes(service)

        Get(s"/simon/api/v2/principals/cache/$idToSearch") ~> routes ~> check {
          status shouldBe StatusCodes.OK
        }
      }

      "service returned failure" in {
        val service: UserAclService = mock[UserAclService]

        when(service.getCacheEntry(exact(userId), exact(idToSearch))(any[TraceId]))
          .thenReturn(Future.failed(new RuntimeException))

        val routes = userAclServiceRoutes(service)

        Get(s"/simon/api/v2/principals/cache/$idToSearch") ~> routes ~> check {
          status shouldBe StatusCodes.InternalServerError
        }
      }
    }

    "handle GET cached ACL capabilities" when {
      val idToSearch = "idToSearch"
      val capabilities = Set("capability")
      val cachedUserAcl: CachedUserAcl =
        CachedUserAcl(userACL = dummyUserACL(idToSearch).copy(capabilities = capabilities), traceId = "traceId", updated = "updated")

      "service returned success with Some" in {
        val service: UserAclService = mock[UserAclService]

        when(service.getCacheEntry(exact(userId), exact(idToSearch))(any[TraceId]))
          .thenReturn(Future.successful(Some(cachedUserAcl)))

        val routes = userAclServiceRoutes(service)

        Get(s"/simon/api/v2/principals/cache/$idToSearch/capabilities") ~> routes ~> check {
          status shouldBe StatusCodes.OK
          entityAs[Set[String]] shouldBe capabilities
        }
      }

      "service returned not found with None" in {
        val service: UserAclService = mock[UserAclService]

        when(service.getCacheEntry(exact(userId), exact(idToSearch))(any[TraceId]))
          .thenReturn(Future.successful(None))

        val routes = userAclServiceRoutes(service)

        Get(s"/simon/api/v2/principals/cache/$idToSearch/capabilities") ~> routes ~> check {
          status shouldBe StatusCodes.NotFound
          entityAs[String] contains  "User idToSearch not found in cache"
        }
      }

      "service returned failure" in {
        val service: UserAclService = mock[UserAclService]

        when(service.getCacheEntry(exact(userId), exact(idToSearch))(any[TraceId]))
          .thenReturn(Future.failed(new RuntimeException))

        val routes = userAclServiceRoutes(service)

        Get(s"/simon/api/v2/principals/cache/$idToSearch/capabilities") ~> routes ~> check {
          status shouldBe StatusCodes.InternalServerError
        }
      }
    }

    "handle POST cache-sync" when {
      val request: CacheSyncRequest = CacheSyncRequest(None, None)

      "service returned success" in {
        val service: UserAclService = mock[UserAclService]

        when(service.syncAclCache(exact(userId), exact(request))(any[TraceId])).thenReturn(Future.unit)
        val routes = userAclServiceRoutes(service)

        Post(s"/simon/api/v2/principals/cache-sync", request) ~> routes ~> check {
          status shouldBe StatusCodes.OK
        }
      }
      "service returned failure" in {
        val service: UserAclService = mock[UserAclService]

        when(service.syncAclCache(exact(userId), exact(request))(any[TraceId])).thenReturn(Future.failed(new RuntimeException))
        val routes = userAclServiceRoutes(service)

        Post(s"/simon/api/v2/principals/cache-sync", request) ~> routes ~> check {
          status shouldBe StatusCodes.OK
        }
      }
    }
  }
}
