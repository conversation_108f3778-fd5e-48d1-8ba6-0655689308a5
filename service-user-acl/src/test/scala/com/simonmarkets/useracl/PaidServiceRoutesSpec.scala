package com.simonmarkets.useracl

import akka.http.scaladsl.model.StatusCodes
import akka.http.scaladsl.testkit._
import akka.stream.Materializer
import com.goldmansachs.marquee.pipg.UserACL
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.api.users.HttpACLClient
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.logging.TraceId
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.resteasy.requests.AuthorizedDirectives
import com.simonmarkets.useracl.api.{BulkUpsertPaidFeaturesRequest, PaidFeaturesRequest, PaidFeaturesResponse}
import com.simonmarkets.useracl.domain.{IdType, PaidFeatures}
import com.simonmarkets.useracl.repository.PaidFeaturesRepositoryImpl
import com.simonmarkets.useracl.service.PaidFeaturesServiceImpl
import org.mockito.ArgumentMatchers.{any, eq => exact}
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpecLike}
import simon.Id.NetworkId

import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}

class PaidServiceRoutesSpec
  extends WordSpecLike
    with MockitoSugar
    with Matchers
    with ScalatestRouteTest
    with DirectivesWithCirce
    with JsonCodecs {
  private val networkId1 = NetworkId("network-id-1")
  implicit val testUserAcl: UserACL = TestUserACL("acl id", networkId1, capabilities = Set("admin"))
  val userACLDirective: UserAclAuthorizedDirective = mock[UserAclAuthorizedDirective]
  private val authDirective = mock[AuthorizedDirectives[UserACL]]
  val authDirectivesMock: AuthorizedDirectives[UserACL] = mock[AuthorizedDirectives[UserACL]]
  implicit val routeTestTimeout: RouteTestTimeout = RouteTestTimeout(5.seconds)

  implicit val traceId: TraceId = TraceId.randomize

  val paidFeatures = PaidFeatures(IdType.UserId, "hamster", List(), Set())
  val service: PaidFeaturesServiceImpl = mock[PaidFeaturesServiceImpl]
  val aclClient: HttpACLClient = mock[HttpACLClient]
  val parallelism: Int = 1

  "UserAclServiceRoutes" should {
    when(authDirective.authorized("admin")).thenReturn(tprovide((traceId, testUserAcl)))
    "handle GET UserACL" when {
      "service returned success 200" in {
        val userIdSearch = "hamster"
        when(service.getPaidFeatures(exact(userIdSearch), any[Option[NetworkId]])
        (any[TraceId], any[ExecutionContext], any[UserACL]))
          .thenReturn(Future.successful(Some(PaidFeaturesResponse(paidFeatures))))
        val routes = PaidFeaturesRoutes(service, authDirective).routes
        Get(s"/simon/api/v1/paid-features/$userIdSearch") ~> routes ~> check {
          status shouldBe StatusCodes.OK
          entityAs[PaidFeaturesResponse] shouldBe PaidFeaturesResponse(paidFeatures)
        }
      }
      "service returned HttpError NOT FOUND" in {
        val userIdSearch = "hamster"
        val paidFeatureRepository: PaidFeaturesRepositoryImpl = mock[PaidFeaturesRepositoryImpl]
        val paidFeaturesService = new PaidFeaturesServiceImpl(paidFeatureRepository, aclClient)
        val testUserACL = TestUserACL("hamster", NetworkId("hamsterNetwork"))
        when(paidFeatureRepository.findById("hamster", IdType.UserId))
          .thenReturn(Future.successful(None))
        when(aclClient.getUserACL("hamster")(traceId)).thenReturn(Future(testUserACL))
        when(paidFeatureRepository.findById(testUserACL.networkId.toString, IdType.NetworkId))
          .thenReturn(Future.successful(None))
        val routes = PaidFeaturesRoutes(paidFeaturesService, authDirective).routes
        Get(s"/simon/api/v1/paid-features/$userIdSearch") ~!> routes ~> check {
          status shouldBe StatusCodes.NotFound
        }
      }
    }
    "handle PUT Request" when {
      "service returned success 200 when inserting user" in {
        val request = PaidFeaturesRequest(id = "hamster5", roles = List())
        when(service.upsertPaidFeatures(any[PaidFeaturesRequest], any[IdType])
        (any[TraceId], any[ExecutionContext], any[UserACL])).thenReturn(Future.successful(PaidFeaturesResponse(paidFeatures)))
        val routes = PaidFeaturesRoutes(service, authDirective).routes
        Put(s"/simon/api/v1/paid-features", request) ~> routes ~> check {
          status shouldBe StatusCodes.OK
          entityAs[PaidFeaturesResponse] shouldBe PaidFeaturesResponse(paidFeatures.id, paidFeatures.roles)
        }
      }
    }
    "handle bulk-upsert PUT Request" when {
      "service returned success 200 when inserting user" in {
        val request = BulkUpsertPaidFeaturesRequest(networkId = NetworkId("hamsterNetwork"), roles = List())
        val expectedResponse = PaidFeaturesResponse("hamsterNetwork", List())
        when(service.bulkUpsertNetworkUsersPaidFeatures(any[BulkUpsertPaidFeaturesRequest])
        (any[TraceId], any[ExecutionContext], any[UserACL], any[Materializer])).thenReturn(Future.successful(expectedResponse))
        val routes = PaidFeaturesRoutes(service, authDirective).routes
        Put(s"/simon/api/v1/paid-features/bulk-upsert", request) ~> routes ~> check {
          status shouldBe StatusCodes.OK
          entityAs[PaidFeaturesResponse] shouldBe expectedResponse
        }
      }
    }
  }
}