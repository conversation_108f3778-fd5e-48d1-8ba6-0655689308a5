<?xml version="1.0"?>
<assembly>
    <id>service-user-acl</id>
    <formats>
        <format>tar</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>
    <fileSets>
        <fileSet>
            <directory>target</directory>
            <includes>
                <include>*:jar</include>
            </includes>
            <outputDirectory>gfoos</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>src/main/resources</directory>
            <outputDirectory>config</outputDirectory>
            <includes>
                <include>*.conf</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>app-config/bin</directory>
            <outputDirectory>bin</outputDirectory>
            <includes>
                <include>*</include>
            </includes>
            <fileMode>0755</fileMode>
        </fileSet>
        <fileSet>
            <directory>app-config/init-bin/</directory>
            <outputDirectory>init-bin</outputDirectory>
            <includes>
                <include>*</include>
            </includes>
            <fileMode>0755</fileMode>
        </fileSet>
        <fileSet>
            <directory>${project.parent.basedir}/runtime-common/etc/</directory>
            <outputDirectory>/etc</outputDirectory>
            <includes>
                <include>*</include>
            </includes>
        </fileSet>
    </fileSets>
    <dependencySets>
        <dependencySet>
            <outputDirectory>gslibs/dependency</outputDirectory>
            <includes>
                <include>*:jar</include>
            </includes>
        </dependencySet>
    </dependencySets>
</assembly>
