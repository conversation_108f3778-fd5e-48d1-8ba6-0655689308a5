package com.simonmarkets.useracl.user

import com.goldmansachs.marquee.pipg.UserACL
import com.gs.marquee.foundation.util.Configuration
import com.simonmarkets.capabilities.AllOfferingsCapabilities.OverrideTierCapabilities
import com.simonmarkets.capabilities.SimonUICapabilities._
import com.simonmarkets.capabilities._
import com.simonmarkets.networks.common.Network

object UserCapabilities {
  def toCapabilities(user: UserACL, network: Option[Network] = None): Map[String, Boolean] =  {
    Map(
      "canSeeIssuerPortal" -> hasUICapability(IssuerPortalCapabilities.toCapabilityNames, user),
      "canSeeWholesalerPortal" -> hasUICapability(WholesalerPortalCapabilities.toCapabilityNames, user),
      "canSeeLearningCenter" -> hasUICapability(LearningCenterCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterDetails" -> hasUICapability(LearningCenterDetailsCapabilities.toCapabilityNames, user),
      "canManageOffering" -> network.exists(canManageOffering(_)),
      "isRegSEligible" -> user.regSEligible,
      "isTradewebEligible" -> isTradewebEligible(network),
      "isOptInRfqPricingFeedback" -> hasUICapability(ViewUIRfqOptInPricingFeedbackByDefaultCapabilities.toCapabilityNames, user),
      "canSeeFWPDisclaimer" -> canSeeFWPDisclaimer(network),
      "canSeeQuizzes" -> network.exists(hasCapability(_, "canSeeQuizzes")),
      "addLocationKeysToOrder" -> network.exists(hasCapability(_, "addLocationKeysToOrder")),
      "canSeeSimon2" -> network.exists(hasCapability(_, "canSeeSimon2")),
      "canSeeAnnuities" -> network.exists(hasCapability(_, "canSeeAnnuities")),
      "canSeeExpandedOrderEntry" -> network.exists(hasCapability(_, "canSeeExpandedOrderEntry")),
      "canSeeManagementDashboard" -> hasUICapability(SPManagementDashboardCapabilities.toCapabilityNames, user),
      "canSeeSPPortal" -> hasUICapability(SPPortalCapabilities.toCapabilityNames, user),
      "canSeeSIMarketplaceRates" -> hasUICapability(ViewUISIMarketplaceRatesCapabilities.toCapabilityNames, user),
      "canSeeSIEquitiesMarketplaceCapacity" -> hasUICapability(ViewUISIEquitiesMarketplaceCapacityCapabilities.toCapabilityNames, user),
      "canSeeSIRatesMarketplaceCapacity" -> hasUICapability(ViewUISIRatesMarketplaceCapacityCapabilities.toCapabilityNames, user),
      "canSeeSIIssuerPortalSuspend" -> hasUICapability(ViewUISIIssuerPortalSuspendCapabilities.toCapabilityNames, user),
      "canSeeSIIssuerPortalCapacity" -> hasUICapability(ViewUISIIssuerPortalCapacityCapabilities.toCapabilityNames, user),
      "canSeeSIDollarCommission" -> hasUICapability(ViewUISIDollarCommissionCapabilities.toCapabilityNames, user),
      "canSeeFIAPortal" -> hasUICapability(FIAPortalCapabilities.toCapabilityNames, user),
      "canSeeSVAPortal" -> hasUICapability(SVAPortalCapabilities.toCapabilityNames, user),
      "canBroadcastInNetworkOnly" -> hasUICapability(BroadcastInNetworkOnlyCapabilities.toCapabilityNames, user),
      "canBroadcastOutsideNetwork" -> hasUICapability(BroadcastOutsideNetworkCapabilities.toCapabilityNames, user),
      "canExportCurrentOfferingNetworkIOIs" -> hasUICapability(ExportCurrentOfferingNetworkIOIsCapabilities.toCapabilityNames, user),
      "canShareOutsideNetwork" -> hasUICapability(ShareOutsideNetworkCapabilities.toCapabilityNames, user),
      "canUseBrokerDealer" -> hasUICapability(UseBrokerDealerCapabilities.toCapabilityNames, user),
      "canViewMultipleNetworks" -> hasUICapability(MultipleNetworksCapabilities.toCapabilityNames, user),
      "canSeeRIAPopUp" -> hasUICapability(RIACapabilities.toCapabilityNames, user),
      "canSeeHoldings" -> hasUICapability(LifecyclePortalCapabilities.toCapabilityNames, user),
      "canSeeBuilder" -> hasUICapability(SimonUICapabilities.BuilderCapabilities.toCapabilityNames, user),
      "canSeeHeatmap" -> hasUICapability(ViewUIHeatmapCapabilities.toCapabilityNames, user),
      "canSeeBacktesting" -> hasUICapability(BacktestingCapabilities.toCapabilityNames, user),
      "canSeeSPDetails" -> hasUICapability(DetailsCapabilities.toCapabilityNames, user),
      "canSeeHoldingsDocUpload" -> hasUICapability(HoldingsDocUploadCapabilities.toCapabilityNames, user),
      "canSeeClientHoldingsByHousehold" -> hasUICapability(ClientHoldingsGroupByHouseholdCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterMyNetwork" -> hasUICapability(LearningCenterMyNetworkCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV2" -> hasUICapability(ViewLearningCenterV2Capabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3" -> hasUICapability(ViewLearningCenterV3Capabilities.toCapabilityNames, user),
      "canSeeLearningCenterV2StructuredInvestments" -> hasUICapability(ViewLearningCenterV2StructuredInvestmentsCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3StructuredInvestments" -> hasUICapability(ViewLearningCenterV3StructuredInvestmentsCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV2Annuities" -> hasUICapability(ViewLearningCenterV2AnnuitiesCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3Annuities" -> hasUICapability(ViewLearningCenterV3AnnuitiesCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV2DefinedOutcomeETFs" -> hasUICapability(ViewLearningCenterV2DefinedOutcomeETFsCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3DefinedOutcomeETFs" -> hasUICapability(ViewLearningCenterV3DefinedOutcomeETFsCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV2DigitalAssets" -> hasUICapability(ViewLearningCenterV2DigitalAssetsCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3DigitalAssets" -> hasUICapability(ViewLearningCenterV3DigitalAssetsCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV2AlternativeInvestments" -> hasUICapability(ViewLearningCenterV2AlternativeInvestmentsCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3AlternativeInvestments" -> hasUICapability(ViewLearningCenterV3AlternativeInvestmentsCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV2CustomResourceOrder" -> hasUICapability(ViewLearningCenterV2ViewCustomResourceOrderCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3CustomResourceOrder" -> hasUICapability(ViewLearningCenterV3ViewCustomResourceOrderCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV2ResourceWithoutTraining" -> hasUICapability(ViewLearningCenterV2ResourceWithoutTrainingCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3ResourceWithoutTraining" -> hasUICapability(ViewLearningCenterV3ResourceWithoutTrainingCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV2DefinedOutcomeETFMPlusFunds" -> hasUICapability(ViewLearningCenterV2DefinedOutcomeETFMPlusFundsCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3DefinedOutcomeETFMPlusFunds" -> hasUICapability(ViewLearningCenterV3DefinedOutcomeETFMPlusFundsCapabilities.toCapabilityNames, user),
      "canSeeCloseOut" -> hasUICapability(ViewCloseOutCapabilities.toCapabilityNames, user),
      "canSeeSPOrdersCloseoutWorkflow" -> hasUICapability(ViewSPOrdersCloseoutWorkflowCapabilities.toCapabilityNames, user),
      "canSeeSPOrdersAudit" -> hasUICapability(ViewUISPOrdersAuditCapabilities.toCapabilityNames, user),
      "canSeeDeveloperFeatures" -> hasUICapability(ViewDeveloperFeatureCapabilities.toCapabilityNames, user),
      "canSeeRfqAsHomeOffice" -> hasUICapability(ViewRfqHomeOfficeCapabilities.toCapabilityNames, user),
      "canSeeRfqAsIssuer" -> hasUICapability(ViewRfqIssuerCapabilities.toCapabilityNames, user),
      "canSeeMultiIssuerRFQ" -> hasUICapability(ViewRfqMultiIssuerCapabilities.toCapabilityNames, user),
      "canSeeRfqAsFA" -> hasUICapability(ViewRfqFACapabilities.toCapabilityNames, user),
      "canSeeRfqAsWholesaler" -> hasUICapability(ViewRfqWholesalerCapabilities.toCapabilityNames, user),
      "canEditRfqNotes" -> hasRfqCapability(Set(RfqCapabilities.EditRfqAuditNotesFieldViaNetwork), user),
      "canSeeRfqTagsInternalApproval" -> hasRfqCapability(Set(RfqCapabilities.ViewTagsInternalApprovalCapability.name), user),
      "canEditRfqTagsForNetwork" -> hasRfqTagsCapability(RfqTagsCapabilities.EditRfqTagsViaNetworkCapability.name, user),
      "canSeePendingSPOfferings" -> hasOfferingsCapability(SPOfferingsCapabilities.ApproveCapabilities, user),
      "canSeePendingRegisteredAnnuityOfferings" -> hasOfferingsCapability(RegisteredAnnuityOfferingsCapabilities.ApproveCapabilities, user),
      "canSeePendingNonRegisteredAnnuityOfferings" -> hasOfferingsCapability(NonRegisteredAnnuityOfferingsCapabilities.ApproveCapabilities, user),
      "canSeeSIPrintPerformancePdf" -> hasOfferingsV1Capability(Set(OfferingsV1Capabilities.PrintPdfViaPerfPayoffEntitlementCapability.name), user),
      "canSeeExportPDF" -> hasUICapability(ViewExportPDFCapabilities.toCapabilityNames, user),
      "canEditFaCommission" -> hasSalesFeeCapability(SalesFeeCapabilities.OverrideFeeFaCommission, user),
      "canEditHomeOfficeFee" -> hasSalesFeeCapability(SalesFeeCapabilities.OverrideFeeHomeOfficeCommission, user),
      "canEditReOffer" -> hasSalesFeeCapability(SalesFeeCapabilities.OverrideFeeReOffer, user),
      "canEditWholesalerFee" -> hasSalesFeeCapability(SalesFeeCapabilities.OverrideFeeWholesalersCommission, user),
      "canSeeFaCommission" -> hasSalesFeeCapability(SalesFeeCapabilities.ViewFeeFaCommission, user),
      "canSeeHomeOfficeFee" -> hasSalesFeeCapability(SalesFeeCapabilities.ViewFeeHomeOfficeCommission, user),
      "canSeeReOffer" -> hasSalesFeeCapability(SalesFeeCapabilities.ViewFeeReOffer, user),
      "canSeeWholesalerFee" -> hasSalesFeeCapability(SalesFeeCapabilities.ViewFeeWholesalersCommission, user),
      "canSeeBuilderV2" -> hasUICapability(ViewUIBuilderV2Capabilities.toCapabilityNames, user),
      "canSeeLearningCenterV2TrainingUpload" -> hasUICapability(ViewUILearningCenterV2TrainingUploadCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3TrainingUpload" -> hasUICapability(ViewUILearningCenterV3TrainingUploadCapabilities.toCapabilityNames, user),
      "canSeeCarrierPortalRateUpload" -> hasUICapability(ViewUICarrierPortalRateUploadCapabilities.toCapabilityNames, user),
      "canSeeCarrierPortalMorningStarMappingUpload" -> hasUICapability(ViewUICarrierPortalMorningStarMappingUploadCapabilities.toCapabilityNames, user),
      "cannotBuySingleSIFeeOrderProduct" -> hasOrdersCapability(OrdersCapabilities.DetailedBlockSingleSIFeeOrderProductCapabilities.map(_.name), user),
      "canSeeOrderPreTradeValidation" -> network.exists(hasCapability(_, "canSeePreTradeIOITab")),
      "canSeeOrderCreateNewOrderAccount" -> hasUICapability(ViewUIOrderCreateNewOrderAccountCapabilities.toCapabilityNames, user),
      "canSeeOrderBulkUpload" -> hasUICapability(ViewUIOrderBulkUploadCapabilities.toCapabilityNames, user),
      "canSeeOrderBulkUploadDownloadForLPL" -> hasUICapability(ViewUIOrderBulkUploadDownloadForLPLCapabilities.toCapabilityNames, user),
      "canSeeOrderEntryClientSolicitation" -> hasUICapability(ViewUIOrderEntryClientSoliticationCapabilities.toCapabilityNames, user),
      "canSeeOrderEntryDiscretionary" -> hasUICapability(ViewUIOrderEntryDiscretionaryCapabilities.toCapabilityNames, user),
      "canSeeOrderEntryOnBehalfOf" -> hasUICapability(ViewUIOrderEntryOnBehalfOfCapabilities.toCapabilityNames, user),
      "canSeeOrderEntryCustomerDateTime" -> hasUICapability(ViewUIOrderEntryCustomerDateTimeCapabilities.toCapabilityNames, user),
      "canSeeOrderEntryFaNumber" -> hasUICapability(ViewUIOrderEntryRepCodeCapabilities.toCapabilityNames, user),
      "canSeeOrderEntryFeesCommission" -> hasUICapability(ViewUIOrderEntryFeesCommissionCapabilities.toCapabilityNames, user),
      "canSeeOrderEntryAccountTypeNumberPhoneRepName" -> hasUICapability(ViewUIOrderEntryAccountTypeNumberPhoneRepNameCapabilities.toCapabilityNames, user),
      "canSeeOrderEntryAcceptedByRepCode" -> hasUICapability(ViewUIOrderEntryAcceptedByFaNumberCapabilities.toCapabilityNames, user),
      "canSeeOrderEntryNetwork" -> hasUICapability(ViewUIOrderEntryNetworkCapabilities.toCapabilityNames, user),
      "canSeeOrderEntryAccountSearch" -> hasUICapability(ViewUIOrderEntryAccountSearchCapabilities.toCapabilityNames, user),
      "canSeeOrderEntryAccountInputNumbersOnlyValidation" -> hasUICapability(ViewUIOrderEntryAccountInputNumbersOnlyValidationCapabilities.toCapabilityNames, user),
      "canSeeOrderEntryComment" -> hasUICapability(ViewUIOrderEntryCommentCapabilities.toCapabilityNames, user),
      "canHideOrderEntryDefaultAttestation" -> hasUICapability(ViewUIOrderEntryHideDefaultAttestationCapabilities.toCapabilityNames, user),
      "canSeeOrderEntryRockefeller" -> hasUICapability(ViewUIOrderEntryRockefellerCapabilities.toCapabilityNames, user),
      "canSeeOrderCustodian" -> hasOrdersCapability(OrdersCapabilities.DetailedEnableOrderCustodianCapabilities.map(_.name), user),
      "canSeeIOIStructuringFeeColumn" -> hasOrdersCapability(OrdersCapabilities.DetailedIncludeStructuringFeeColumnCapabilities.map(_.name), user),
      "canSeeIOILocationsColumn" -> hasUICapability(ViewUIIOILocationsColumnCapabilities.toCapabilityNames, user),
      "canSeeBroadridgeIOIDownload" -> hasUICapability(ViewUIBroadridgeIOIDownloadCapabilities.toCapabilityNames, user),
      "canSeePershingIOIDownload" -> hasUICapability(ViewUIPershingIOIDownloadCapabilities.toCapabilityNames, user),
      "canSeeBloombergMTEIOIDownload" -> hasUICapability(ViewUIBloombergMTEIOIDownloadCapabilities.toCapabilityNames, user),
      "canSeeMTDIOIDownload" -> hasUICapability(ViewUIMTDIOIDownloadCapabilities.toCapabilityNames, user),
      "canSeeCusipIOIDownload" -> hasUICapability(ViewUICusipIOIDownloadCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV2MyNetworkProductTraining" -> hasUICapability(ViewUILearningCenterV2MyNetworkProductTrainingCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3MyNetworkProductTraining" -> hasUICapability(ViewUILearningCenterV3MyNetworkProductTrainingCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV2AnnuitiesMyNetworkEducation" -> hasUICapability(ViewUILearningCenterV2AnnuitiesMyNetworkEducationCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3AnnuitiesMyNetworkEducation" -> hasUICapability(ViewUILearningCenterV3AnnuitiesMyNetworkEducationCapabilities.toCapabilityNames, user),
      "canSeePendingOfferings" -> hasUICapability(ViewUIPendingOfferingsCapabilities.toCapabilityNames, user),
      "canSeeAnalyticsAsOfDate" -> hasUICapability(ViewUIAnalyticsAsOfDateCapabilities.toCapabilityNames, user),
      "canSeeHoldingsAsOfDates" -> hasUICapability(ViewUIHoldingsAsOfDatesCapabilities.toCapabilityNames, user),
      "canSeeRetriggerDTCC" -> hasUICapability(ViewUIRetriggerDTCCCapabilities.toCapabilityNames, user),
      "canSeeContractEditor" -> hasUICapability(ViewUIContractEditCapabilities.toCapabilityNames, user),
      "canSeeDistributorSymbol" -> hasUICapability(ViewUIDistributorSymbolCapabilities.toCapabilityNames, user),
      "canSeeProductNomenclatureLongName" -> hasUICapability(ViewUIProductNomenclatureLongNameCapabilities.toCapabilityNames, user),
      "canSeeRejectedIOIs" -> hasUICapability(ViewUIRejectedIOIsCapabilities.toCapabilityNames, user),
      "canSeeExportToTwdXls" -> hasUICapability(ViewExportToTwdXlsCapabilities.toCapabilityNames, user),
      "canSeeTMCReport" -> hasUICapability(ViewUITMCReportCapabilities.toCapabilityNames, user),
      "canSeeOlark" -> hasUICapability(ViewUIOlarkCapabilities.toCapabilityNames, user),
      "canSeeQuoteType" -> hasUICapability(ViewUIQuoteTypeCapabilities.toCapabilityNames, user),
      "canSeeCustomAttestation" -> hasUICapability(ViewUICustomAttestationViaNetworkCapabilities.toCapabilityNames, user),
      "canSeeDefinedOutcomeETF" -> hasUICapability(ViewUIDefinedOutcomeETFCapabilities.toCapabilityNames, user),
      "canSeeDefinedOutcomeETFViewAs" -> hasUICapability(ViewUIDefinedOutcomeETFViewAsCapabilities.toCapabilityNames, user),
      "canSeeDefinedOutcomeETFMPlusFunds" -> hasUICapability(ViewUIDefinedOutcomeETFMPlusFundsCapabilities.toCapabilityNames, user),
      "canSeeSpectrumLC2SP" -> hasUICapability(ViewUISpectrumLC2SPCapabilities.toCapabilityNames, user),
      "canSeeContractRanges" -> hasUICapability(ViewUIContractRangesCapabilities.toCapabilityNames, user),
      "canSeeRejectedOfferings" -> hasUICapability(ViewUIRejectedOfferingsCapabilities.toCapabilityNames, user),
      "canSeeStagedOfferings" -> hasUICapability(ViewUIStagedOfferingsCapabilities.toCapabilityNames, user),
      "canSeeFAPortal" -> hasUICapability(ViewUIFAPortalCapabilities.toCapabilityNames, user),
      "canSeeVAPortal" -> hasUICapability(ViewUIVAPortalCapabilities.toCapabilityNames, user),
      "canSeeFAPortalYieldToSurrender" -> hasUICapability(ViewUIFAPortalYieldToSurrenderCapabilities.toCapabilityNames, user),
      "canSeeNewSILifecycleDashboard" -> hasUICapability(ViewNewSILifecycleDashboardCapabilities.toCapabilityNames, user),
      "canSeeOfferingsReport" -> hasUICapability(ViewUIOfferingsReportCapabilities.toCapabilityNames, user),
      "canSeeOfferingDocViaModal" -> hasUICapability(ViewUIOfferingDocViaModalCapabilities.toCapabilityNames, user),
      "canSeeMyProductsDashboard" -> hasUICapability(ViewUIMyProductsDashboardCapabilities.toCapabilityNames, user),
      "canSeeLifecycleEventsDashboard" -> hasUICapability(ViewUILifecycleEventsDashboardCapabilities.toCapabilityNames, user),
      "canSeeNotificationsNewsfeedDashboard" -> hasUICapability(ViewUINotificationsNewsfeedDashboardCapabilities.toCapabilityNames, user),
      "canSeeSearchBar" -> hasUICapability(ViewUISearchBarCapabilities.toCapabilityNames, user),
      "canSeeLifecycleNotificationSettings" -> hasUICapability(ViewUILifecycleNotificationSettingsCapabilities.toCapabilityNames, user),
      "canSeeRILAIndexStrategyBacktestingPDF" -> hasUICapability(ViewUIRILAIndexStrategyBacktestingPDFCapabilities.toCapabilityNames, user),
      "canEditNetworkLifecycleNotificationSettings" -> hasUICapability(ViewUIEditNetworkLifecycleNotificationSettingsCapabilities.toCapabilityNames, user),
      "canSeeLifecyclePortal" -> hasUICapability(ViewUILifecyclePortalSPCapabilities.toCapabilityNames, user),
      "canSeeLifecyclePortalShareViaLink" -> hasUICapability(ViewUILifecyclePortalSPShareViaLinkCapabilities.toCapabilityNames, user),
      "canSeeLifecyclePortalEditNetworkDefault" -> hasUICapability(ViewUILifecyclePortalSPEditNetworkDefaultCapabilities.toCapabilityNames, user),
      "canSeeLifecyclePortalInsurance" -> hasUICapability(ViewUILifecyclePortalInsuranceCapabilities.toCapabilityNames, user),
      "canSeeLifecyclePortalSPWidgetContractOfferings" -> hasUICapability(ViewUILifecyclePortalSPWidgetContractOfferingsCapabilities.toCapabilityNames, user),
      "canSeeLifecyclePortalSPBarrierAnalysisWidget" -> hasUICapability(ViewUILifecyclePortalSPBarrierAnalysisWidgetCapabilities.toCapabilityNames, user),
      "canSeeLifecyclePortalSPCapAnalysisWidget" -> hasUICapability(ViewUILifecyclePortalSPCapAnalysisWidgetCapabilities.toCapabilityNames, user),
      "canSeeLifecyclePortalSPSectorAllocationWidget" -> hasUICapability(ViewUILifecyclePortalSPSectorAllocationWidgetCapabilities.toCapabilityNames, user),
      "canSeeLifecyclePortalSPUnderlierPerformanceWidget" -> hasUICapability(ViewUILifecyclePortalSPUnderlierPerformanceWidgetCapabilities.toCapabilityNames, user),
      "canSeeLifecyclePortalSIClientsWidget" -> hasUICapability(ViewUILifecyclePortalSIClientsWidgetCapabilities.toCapabilityNames, user),
      "canSeeLifecyclePortalPartialSearch" -> hasUICapability(ViewUILifecyclePortalPartialSearchCapabilities.toCapabilityNames, user),
      "canSeeLifecyclePortalPartitionedQuery" -> hasUICapability(ViewUILifecyclePortalPartitionedQueryCapabilities.toCapabilityNames, user),
      "canSeeViewAsLifecycleSP" -> hasUICapability(ViewUIViewAsLifecycleSPCapabilities.toCapabilityNames, user),
      "canSeeFANumberSelectorDashboard" -> hasUICapability(ViewFANumberSelectorDashboardCapabilities.toCapabilityNames, user),
      "canSeeViewUILifecyclePortalAnnuities" -> hasUICapability(ViewUILifecyclePortalAnnuitiesCapabilities.toCapabilityNames, user),
      "canSeeViewUISIEquitiesLifecycleDTCCPayment" -> hasUICapability(ViewUISIEquitiesLifecycleDTCCPaymentCapabilities.toCapabilityNames, user),
      "canSeeViewUISIEquitiesBufferedLeveredNoteLifecycleDTCCPayment" -> hasUICapability(ViewUISIEquitiesBufferedLeveredNoteLifecycleDTCCPaymentCapabilities.toCapabilityNames, user),
      "canSeeProductNomenclature" -> hasUICapability(ViewUIProductNomenclatureCapabilities.toCapabilityNames, user),
      "canSeeUISpectrumMarketplaceSI" -> hasUICapability(ViewUISpectrumMarketplaceSICapabilities.toCapabilityNames, user),
      "canSeeRefinitivDeepLinks" -> hasUICapability(ViewUIRefinitivDeepLinksCapabilities.toCapabilityNames, user),
      "canSeeFABranding" -> hasUICapability(ViewUIFABrandingCapabilities.toCapabilityNames, user),
      "canSeeBetaPlusMarketplace" -> hasUICapability(ViewUIBetaPlusMarketplaceCapabilities.toCapabilityNames, user),
      "canManageProductNomenclatureList" -> hasProductNomenclatureCapability(ProductNomenclatureCapabilities.toSet, user),
      "canSeePerfPDFClientName" -> hasUICapability(ViewUIPerfPDFClientNameCapabilities.toCapabilityNames, user),
      "canSeePerfPDFViaService" -> hasUICapability(ViewUIPerfPDFViaServiceCapabilities.toCapabilityNames, user),
      "canSeePerfPDFFaGenerateInvestorReportMessage" -> hasUICapability(ViewUIPerfPDFFaGenerateInvestorReportMessageCapabilities.toCapabilityNames, user),
      "canImpersonateAnnuityMarketplace" -> hasUICapability(ViewUIImpersonateAnnuityMarketplaceCapabilities.toCapabilityNames, user),
      "canSeeSpectrumBuilder" -> hasUICapability(ViewUISpectrumBuilderCapabilities.toCapabilityNames, user),
      "canGenerateTradewebOrderToken" -> hasUICapability(ViewUITradewebOrderTokenCapabilities.toCapabilityNames, user),
      "canViewUITradewebEquitiesManageApproval" -> hasUICapability(ViewUITradewebEquitiesManageApprovalCapabilities.toCapabilityNames, user),
      "canViewUITradewebRatesManageApproval" -> hasUICapability(ViewUITradewebRatesManageApprovalCapabilities.toCapabilityNames, user),
      "canSeeBuilderDistributorNomenclature" -> hasUICapability(ViewUIBuilderDistributorNomenclatureCapabilities.toCapabilityNames, user),
      "canSeeLifecyclePortalExportToExcel" -> !hasUICapability(HideUILifecyclePortalExportToExcelCapabilities.toCapabilityNames, user),
      "canSeeUISpectrumLC2Annuities" -> hasUICapability(ViewUISpectrumLC2AnnuitiesCapabilities.toCapabilityNames, user),
      "canSeePendingDefinedOutcomeETFOfferings" -> hasUICapability(SimonUICapabilities.ViewUIStructuredETFPendingOfferingsCapabilities.toCapabilityNames, user),
      "canSeeDefinedOutcomeETFIssuerPortal" -> hasUICapability(ViewUIStructuredETFIssuerPortalCapabilities.toCapabilityNames, user),
      "canSeeAppcuesDropdown" -> hasUICapability(ViewUIAppcuesDropdownCapabilities.toCapabilityNames, user),
      "canSeeUISpectrumMarketplaceAnnuities" -> hasUICapability(ViewUISpectrumMarketplaceAnnuitiesCapabilities.toCapabilityNames, user),
      "canSeeUISpectrumAnnPDF" -> hasUICapability(ViewUISpectrumPDFAnnCapabilities.toCapabilityNames, user),
      "canSeeUISpectrumSICUSIPModal" -> hasUICapability(ViewUISpectrumSICUSIPModalCapabilities.toCapabilityNames, user),
      "canSeeUILifecyclePortalSpectrum" -> hasUICapability(ViewUILifecyclePortalSpectrumCapabilities.toCapabilityNames, user),
      "canSeeUISpectrumSIPDF" -> hasUICapability(ViewUISpectrumPDFSICapabilities.toCapabilityNames, user),
      "canSeeSpectrumLC2FixedIndexedAnnuities" -> hasUICapability(ViewUISpectrumLC2FixedIndexedAnnuitiesCapabilities.toCapabilityNames, user),
      "canSeeOptimizationToolFixedIndexedAnnuities" -> hasUICapability(ViewUIOptimizationToolFixedIndexedAnnuitiesCapabilities.toCapabilityNames, user),
      "canSeeSpectrumAdvisorFIAPDF" -> hasUICapability(ViewUISpectrumAdvisorFIAPDFCapabilities.toCapabilityNames, user),
      "canSeeOptimizationToolAdvisorFIAPDF" -> hasUICapability(ViewUIOptimizationToolAdvisorFIAPDFCapabilities.toCapabilityNames, user),
      "canSeeUIEnhancedAnnuityOrder" -> hasUICapability(ViewUIEnhancedAnnuityOrderCapabilities.toCapabilityNames, user),
      "canSeeLevelSystem" -> hasUICapability(ViewUILevelSystemCapabilities.toCapabilityNames, user),
      "canBatchStageSPOfferings" -> hasBatchStagingCapability(BatchStagingCapabilities.EditCapabilities, user),
      "canSeeDefinedOutcomeETFUpsideShield" -> hasUICapability(ViewUIDefinedOutcomeETFUpsideShieldCapabilities.toCapabilityNames, user),
      "canSeeEmbeddedExperience" -> hasUICapability(ViewUIEmbeddedExperienceCapabilities.toCapabilityNames, user),
      "canSeeUnderlierMarket" -> hasUICapability(ViewUIUnderlierMarketCapabilities.toCapabilityNames, user),
      "canSeeBeacon" -> hasUICapability(ViewUIBeaconCapabilities.toCapabilityNames, user),
      "canSeeDefinedOutcomeETFPerformanceAnalysis" -> hasUICapability(ViewUIDefinedOutcomeETFPerformanceAnalysisCapabilities.toCapabilityNames, user),
      "canSeeDefinedOutcomeETFComparisonCalculator" -> hasUICapability(ViewUIDefinedOutcomeETFComparisonCalculatorCapabilities.toCapabilityNames, user),
      "canSeeAnnuityIncomeBacktestingTool" -> hasUICapability(ViewUIIncomeBacktestingToolCapabilities.toCapabilityNames, user),
      "canRunQuickSearch2" -> hasUICapability(ViewUIQuickSearch2Capabilities.toCapabilityNames, user),
      "canSeeQuickSearchYMBICategory" -> hasUICapability(ViewUIQuickSearchYMBICategoryCapabilities.toCapabilityNames, user),
      "canSeeProductSearch" -> hasUICapability(ViewUIProductSearchCapabilities.toCapabilityNames, user),
      "canSeeSIMON3" -> hasUICapability(ViewUISIMON3Capabilities.toCapabilityNames, user),
      "canSeeHistoricalHoldingsSI" -> hasUICapability(ViewUIHistoricalHoldingsSICapabilities.toCapabilityNames, user),
      "canViewWhiteLabeling" -> hasUICapability(ViewUIWhiteLabelingCapabilities.toCapabilityNames, user),
      "canSeeCustomAltsHomepage" -> hasUICapability(ViewUICustomAltsHomepageCapabilities.toCapabilityNames, user),
      "canSeeBottomDisclosure" -> hasUICapability(ViewUIBottomDisclosureCapabilities.toCapabilityNames, user),
      "canHideHomepageLCWidget" -> hasUICapability(ViewUIHideHomepageLCWidgetCapabilities.toCapabilityNames, user),
      "canHideIOIWidget" -> hasUICapability(ViewUIHideIOIWidgetCapabilities.toCapabilityNames, user),
      "canSeeMarketplaceViewAs" -> hasUICapability(ViewUIMarketplaceViewAsCapabilities.toCapabilityNames, user),
      "canSeeUIAltsPortal" -> hasUICapability(ViewUIAltsPortalCapabilities.toCapabilityNames, user),
      "canSeeIpipeline" -> hasUICapability(ViewUIIpipelineCapabilities.toCapabilityNames, user),
      "canSeeBuilderPDF" -> hasUICapability(ViewUIBuilderPDFCapabilities.toCapabilityNames, user),
      "canUploadPastOfferingsEntitlements" -> hasOfferingsCapability(SPOfferingsCapabilities.UploadPastOfferingsEntitlementsCapabilities, user),
      "canSeeMarketplaceRollsWidget" -> hasUICapability(ViewUIMarketplaceRollsWidgetCapabilities.toCapabilityNames, user),
      "canSeeSIMarketplaceExportExcel" -> hasUICapability(ViewUISIMarketplaceExportExcelCapabilities.toCapabilityNames, user),
      "canSeeLifecyclePortalRollsRfq" -> hasRfqCapability(RfqCapabilities.DetailedRequestCapabilities.map(_.name), user),
      "canSeeLifecyclePortalRollsRfqIdeaGeneration" -> hasRfqCapability(RfqCapabilities.DetailedRequestRfqIdeaCapabilities.map(_.name), user),
      "canSeeLifecyclePortalRollsActions" -> hasUICapability(ViewUILifecyclePortalRollsActionsCapabilities.toCapabilityNames, user),
      "canSeeSIMarketplaceDenomination" -> hasUICapability(ViewUISIMarketplaceDenominationCapabilities.toCapabilityNames, user),
      "canSeeOptimizationToolSI" -> hasUICapability(ViewUIOptimizationToolSICapabilities.toCapabilityNames, user),
      "canSeeOptimizationToolAnnuity" -> hasUICapability(ViewUIOptimizationToolAnnuityCapabilities.toCapabilityNames, user),
      "canSeeUploadAdditionalDocument" -> hasUICapability(ViewUIUploadAdditionalDocumentCapabilities.toCapabilityNames, user),
      "canSeePlusSubscribeAlts" -> hasUICapability(ViewUIPlusSubscribeAltsCapabilities.toCapabilityNames, user),
      "canSeeAltsPerformance" -> hasUICapability(ViewUIAltsPerformanceCapabilities.toCapabilityNames, user),
      "canOverrideSPOfferingBooksCloseTime" -> hasUICapability(ViewUIOverrideSPOfferingsBooksCloseTimeCapabilities.toCapabilityNames, user),
      "canSeeTermInMonths" -> hasUICapability(ViewUITermInMonthsCapabilities.toCapabilityNames, user),
      "canSeeOnboardingTool" -> hasUICapability(ViewUIOnboardingToolCapabilities.toCapabilityNames, user),
      "canSwitchUsersNetwork" -> hasUICapability(ViewUISwitchUsersNetworkCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV2DefinedOutcomeETFsMyNetworkEducation" -> hasUICapability(ViewUILearningCenterV2DefinedOutcomeETFsMyNetworkEducationCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3DefinedOutcomeETFsMyNetworkEducation" -> hasUICapability(ViewUILearningCenterV3DefinedOutcomeETFsMyNetworkEducationCapabilities.toCapabilityNames, user),
      "canSeeRFQIssuerSelectionRestrictions" -> hasRfqCapability(Set(RfqCapabilities.UseRFQIssuerSelectionRestrictions), user),
      "isOnboardingToolSuperUser" -> (hasUICapability(Set(Capabilities.Admin), user) && !hasUICapability(ViewUISwitchUsersNetworkCapabilities.toCapabilityNames, user)),
      "isOnboardingToolSpecialistAdmin" -> (hasUICapability(Set(Capabilities.Admin), user) && hasUICapability(ViewUISwitchUsersNetworkCapabilities.toCapabilityNames, user)),
      "canSeeAltsPendingOfferings" -> hasUICapability(AltsPendingOfferingsCapabilities.toCapabilityNames, user),
      "canSeeAltsStagedOfferings" -> hasUICapability(AltsStagedOfferingsCapabilities.toCapabilityNames, user),
      "canSeeAltsClosedOfferings" -> hasUICapability(AltsClosedOfferingsCapabilities.toCapabilityNames, user),
      "canSeeSubscribeInvestmentEntityOptions" -> hasUICapability(SubscribeInvestmentEntityOptionsCapabilities.toCapabilityNames, user),
      "canSeeOptimizationToolSIPDF" -> hasUICapability(ViewUIOptimizationToolSIPDFCapabilities.toCapabilityNames, user),
      "canSeeOptimizationToolAnnuityPDF" -> hasUICapability(ViewUIOptimizationToolAnnuityPDFCapabilities.toCapabilityNames, user),
      "canSeeAddRfqIssuersToAuction" -> hasUICapability(ViewUIAddRfqIssuersToAuctionCapabilities.toCapabilityNames, user),
      "canSeeGrantMode" -> hasUICapability(ViewUIGrantModeCapabilities.toCapabilityNames, user),
      "canSeeSMAAccountPortal" -> hasUICapability(ViewUISMAAccountPortalCapabilities.toCapabilityNames, user),
      "canSeeSMASelectAccountStrategy" -> hasUICapability(ViewUISMASelectAccountStrategyCapabilities.toCapabilityNames, user),
      "canSeeBeaconIframe" -> hasUICapability(ViewUIBeaconIframeCapabilities.toCapabilityNames, user),
      "canSeeSiloedExperience" -> hasUICapability(ViewUISiloedExperienceCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV2AlternativesMyNetworkEducation" -> hasUICapability(ViewUILearningCenterV2AlternativesMyNetworkEducationCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3AlternativesMyNetworkEducation" -> hasUICapability(ViewUILearningCenterV3AlternativesMyNetworkEducationCapabilities.toCapabilityNames, user),
      "canSeeRfqGenericsModal" -> hasUICapability(ViewRfqGenericsModalCapabilities.toCapabilityNames, user),
      "canSeeRfqCreateTemplateForNetwork" -> hasUICapability(ViewRfqCreateTemplateForNetworkCapabilities.toCapabilityNames, user),
      "canSeeRfqQuantoCompoField" -> hasUICapability(ViewRfqQuantoCompoFieldCapabilities.toCapabilityNames, user),
      "canSeeRfqStrikeDateField" -> hasUICapability(ViewUIRfqStrikeDateFieldCapabilities.toCapabilityNames, user),
      "canSeeRfqSettlementDateField" -> hasUICapability(ViewUIRfqSettlementDateFieldCapabilities.toCapabilityNames, user),
      "canSeeRfqPricingFeedback" -> hasUICapability(ViewUIRfqPricingFeedbackCapabilities.toCapabilityNames, user),
      "canSeeRfqWholesalerInfo" -> hasUICapability(ViewUIRfqWholesalerInfoCapabilities.toCapabilityNames, user),
      "canSeeRfqDeclineReason" -> hasUICapability(ViewUIRfqDeclineReasonsCapabilities.toCapabilityNames, user),
      "canSeeRfqRulesEngineResults" -> hasUICapability(ViewUIRfqRulesEngineResultsCapabilities.toCapabilityNames, user),
      "canSeeRfqCrowdsourcingPortal" -> hasUICapability(ViewUIRfqCrowdsourcingPortalCapabilities.toCapabilityNames, user),
      "canSeeRfqAuditTrail" -> hasUICapability(ViewUIRfqAuditTrailCapabilities.toCapabilityNames, user),
      "canSeeRfqNewSubmissionLayout" -> hasUICapability(ViewUIRfqNewSubmissionLayoutCapabilities.toCapabilityNames, user),
      "canSeeRfqIssuerNewSubmissionLayout" -> hasUICapability(ViewUIRfqIssuerNewSubmissionLayoutCapabilities.toCapabilityNames, user),
      "canSeeRfqNewCardLayout" -> hasUICapability(ViewUIRfqNewCardLayoutCapabilities.toCapabilityNames, user),
      "canSeeRfqIssuerNewCardLayout" -> hasUICapability(ViewUIRfqIssuerNewCardLayoutCapabilities.toCapabilityNames, user),
      "canSeeRfqCopyToClipboard" -> hasUICapability(ViewUIRfqCopyToClipboardCapabilities.toCapabilityNames, user),
      "canSeeRfqIdeaGeneration" -> hasUICapability(ViewUIRfqIdeaGenerationCapabilities.toCapabilityNames, user),
      "canSeeRfqCreateTemplate" -> hasUICapability(ViewUIRfqCreateTemplateCapabilities.toCapabilityNames, user),
      "canSeeRfqSubmitButton" -> hasUICapability(ViewUIRfqSubmitButtonCapabilities.toCapabilityNames, user),
      "canSeeRfqTemplateNameInPlaceOfDisplayName" -> hasUICapability(ViewUIRfqTemplateNameInPlaceOfDisplayNameCapabilities.toCapabilityNames, user),
      "canSeeRfqTemplateRequestToTrade" -> hasUICapability(ViewUIRfqTemplateRequestToTradeCapabilities.toCapabilityNames, user),
      "canSeeRfqTemplateVariation" -> hasUICapability(ViewUIRfqTemplateVariationCapabilities.toCapabilityNames, user),
      "canSeeRfqTemplateSubTabs" -> hasRfqTemplatesCapability(Set(RfqTemplatesCapabilities.ViewRfqTemplateViaNetworkCapability.name), user),
      "canSeeRfqQuoteDetails" -> hasUICapability(ViewUIRfqQuoteDetailsCapabilities.toCapabilityNames, user),
      "canSeeRfqDocumentReviewWorkflow" -> hasUICapability(ViewUIRfqDocumentReviewWorkflowCapabilities.toCapabilityNames, user),
      "canSeeRfqToContractOfferingWorkflow" -> hasUICapability(ViewUIRfqToContractOfferingWorkflowCapabilities.toCapabilityNames, user),
      "canSeeRfqDealTypeSelection" -> hasUICapability(ViewUIRfqDealTypeSelectionCapabilities.toCapabilityNames, user),
      "canSeeRfqAveragingPaymentDetails" -> hasUICapability(ViewUIRfqAveragingPaymentDetailsCapabilities.toCapabilityNames, user),
      "canSeeRfqJpmPb" -> hasUICapability(ViewUIRfqJpmPbCapabilities.toCapabilityNames, user),
      "canSeeRfqModal54WeekToggle" -> hasUICapability(ViewUIRfqModal54WeekToggleCapabilities.toCapabilityNames, user),
      "canSeeContractExportToExcel" -> hasUICapability(ViewUIContractExportToExcelCapabilities.toCapabilityNames, user),
      "canSeeRfqDenomination" -> hasUICapability(ViewUIRfqDenominationCapabilities.toCapabilityNames, user),
      "canSeeRfqSortedByLevel" -> hasUICapability(ViewUIRfqSortedByLevelCapabilities.toCapabilityNames, user),
      "canSeeRfqRequestFirmQuote" -> hasUICapability(ViewUIRfqRequestFirmQuoteCapabilities.toCapabilityNames, user),
      "canSeeRfqCloning" -> hasUICapability(ViewUIRfqCloningCapabilities.toCapabilityNames, user),
      "canSeeRfqExternalIdInput" -> hasUICapability(ViewUIRfqExternalIdInputCapabilities.toCapabilityNames, user),
      "canSeeRfqContractUploadedState" -> hasUICapability(ViewUIRfqContractUploadedStateCapabilities.toCapabilityNames, user),
      "canSeeRfqModalAutopopulateFA" -> hasUICapability(ViewUIRfqModalAutopopulateFACapabilities.toCapabilityNames, user),
      "canSeeRfqModalAutopopulateNetwork" -> hasUICapability(ViewUIRfqModalAutopopulateNetworkCapabilities.toCapabilityNames, user),
      "canSeeRfqSolvedRange" -> hasUICapability(ViewUIRfqSolvedRangeCapabilities.toCapabilityNames, user),
      "canSeeBuilderRfqButton" -> hasUICapability(ViewUIBuilderRfqButtonCapabilities.toCapabilityNames, user),
      "canSeeRfqIdeaGenerationTab" -> hasUICapability(ViewUIRfqIdeaGenerationTabCapabilities.toCapabilityNames, user),
      "canSeeRfqPendingIdeaTab" -> hasUICapability(ViewUIRfqPendingIdeaTabCapabilities.toCapabilityNames, user),
      "canSeeRfqActiveRfqsTab" -> hasUICapability(ViewUIRfqActiveRfqsTabCapabilities.toCapabilityNames, user),
      "canSeeRfqActiveRftsTab" -> hasUICapability(ViewUIRfqActiveRftsTabCapabilities.toCapabilityNames, user),
      "canSeeUIAltsViewDetails" -> hasUICapability(ViewUIAltsViewDetailsCapabilities.toCapabilityNames, user),
      "canSeeUIOptimizationGrowthOnlyProducts" -> hasUICapability(ViewUIOptimizationGrowthOnlyProductsCapabilities.toCapabilityNames, user),
      "canSeeUISpectrumSICUSIPModalScores" -> hasUICapability(ViewUISpectrumSICUSIPModalScoresCapabilities.toCapabilityNames, user),
      "canSeeFIDxCarrierPortal" -> hasUICapability(ViewUIFIDxCarrierPortalCapabilities.toCapabilityNames, user),
      "canSeeUIOptimizationToolPDFDisclosureRJ" -> hasUICapability(ViewUIOptimizationToolPDFDisclosureRJCapabilities.toCapabilityNames, user),
      "canSeeOptimizationToolPDFBranding" -> hasUICapability(ViewUIOptimizationToolPDFBrandingCapabilities.toCapabilityNames, user),
      "canSeeOptimizationToolPDFDisclosureModalClient" -> hasUICapability(ViewUIOptimizationToolPDFDisclosureModalClientCapabilities.toCapabilityNames, user),
      "canSeeOptimizationToolPDFDisclosureModal" -> hasUICapability(ViewUIOptimizationToolPDFDisclosureModalCapabilities.toCapabilityNames, user),
      "canViewCoBrandingCustomRJ" -> hasUICapability(ViewUICoBrandingCustomRJCapabilities.toCapabilityNames, user),
      "canSeeCalculatedLifecycleEventsStatus" -> hasUICapability(ViewUICalculatedLifecycleEventsStatusCapabilities.toCapabilityNames, user),
      "canSeeDigitalAssetsPortal" -> hasUICapability(ViewUIDigitalAssetsPortalCapabilities.toCapabilityNames, user),
      "canSeeAltsSubscriptionWorkflow" -> hasUICapability(ViewUIAltsSubscriptionWorkflow.toCapabilityNames, user),
      "canSeeRiskTypeModal" -> hasUICapability(ViewUIRiskTypeModalCapabilities.toCapabilityNames, user),
      "canSeeRiskTypeModalOverride" -> hasUICapability(ViewUIRiskTypeModalOverrideCapabilities.toCapabilityNames, user),
      "canSeeRiskType" -> hasUICapability(ViewUIRiskTypeCapabilities.toCapabilityNames, user),
      "canHideIncomeCalculator" -> hasUICapability(ViewUIHideIncomeCalculatorCapabilities.toCapabilityNames, user),
      "canSeeEnvestnetIntegration" -> hasUICapability(ViewUIEnvestnetCapabilities.toCapabilityNames, user),
      "canSeeEnvestnetSkipClientQuestionnaire" -> hasUICapability(ViewUIEnvestnetSkipClientQuestionnaireCapabilities.toCapabilityNames, user),
      "canSeeEnvestnetArchitectIntegration" -> hasUICapability(ViewUIEnvestnetArchitectIntegrationCapabilities.toCapabilityNames, user),
      "canSeeArchitectExportPortfolioButton" -> hasUICapability(ViewUIArchitectExportPortfolioButtonCapabilities.toCapabilityNames, user),
      "canSeeUserImpersonation" -> hasUICapability(ViewUIUserImpersonationCapabilities.toCapabilityNames, user),
      "canSeeUserImpersonationForHOUsers" -> hasUICapability(ViewUIUserImpersonationForHOUsersCapabilities.toCapabilityNames, user),
      "canHideBacktestingComparison" -> hasUICapability(HideUIBacktestingComparisonCapabilities.toCapabilityNames, user),
      "canSeeEstimatedEodValuations" -> hasUICapability(ViewUIEstimatedEodValuationsCapabilities.toCapabilityNames, user),
      "canSeeAGGridOnLifecyclePortal" -> hasUICapability(ViewUIAGGridOnLifecyclePortalCapabilities.toCapabilityNames, user),
      "canSeeAGGridOnLifecyclePortalSkipMigration" -> hasUICapability(ViewUIAGGridOnLifecyclePortalSkipMigrationsCapabilities.toCapabilityNames, user),
      "canSeeAGGridOnRfq" -> hasUICapability(ViewUIAGGridOnRfqCapabilities.toCapabilityNames, user),
      "canSeeNetworkVisualsFromProfile" -> hasUICapability(ViewUINetworkVisualsFromProfileCapabilities.toCapabilityNames, user),
      "canSeeOptimizationToolClientFIAPDF" -> hasUICapability(ViewUIOptimizationToolClientFIAPDFCapabilities.toCapabilityNames, user),
      "canSeeSpectrumClientFIAPDF" -> hasUICapability(ViewUISpectrumClientFIAPDFCapabilities.toCapabilityNames, user),
      "canHideSIMONBranding" -> hasUICapability(ViewUIHideSIMONBrandingCapabilities.toCapabilityNames, user),
      "canSeeICapitalAltOfferingsLink" -> hasUICapability(ViewUIICapitalAltOfferingsLinkCapabilities.toCapabilityNames, user),
      "canSeeMarketDataEndpointForUnderlierPrices" -> hasUICapability(ViewUIMarketDataEndpointForUnderlierPricesCapabilities.toCapabilityNames, user),
      "canSeeFIDxActivityWorkflow" -> hasUICapability(ViewUIFIDxActivityWorkflowCapabilities.toCapabilityNames, user),
      "canHideUIAllocationSummaryCapability" -> hasUICapability(HideUIAllocationSummaryCapabilities.toCapabilityNames, user),
      "canSeeAGGridClientPages" -> hasUICapability(ViewUIAGGridClientPagesCapabilities.toCapabilityNames, user),
      "canSeeAGGridOnSalesBook" -> hasUICapability(ViewUIAGGridOnSalesBookCapabilities.toCapabilityNames, user),
      "canSeeNewLifecycleEmailsAccountDetails" -> hasUICapability(ViewUINewLifecycleEmailsAccountDetailsCapabilities.toCapabilityNames, user),
      "canSeeVAEnhancedFlow" -> hasUICapability(ViewUIVAEnhancedExperienceCapabilities.toCapabilityNames, user),
      "canSeeGPPortalApp" ->  hasUICapability(ViewUIGPPortalAppCapabilities.toCapabilityNames, user),
      "canSeeAIChatApp" -> hasUICapability(ViewUIAIChatAppCapabilities.toCapabilityNames, user),
      "canSeeAIChatExperimentalPlugins" -> hasUICapability(ViewUIAIChatExperimentalPluginsCapabilities.toCapabilityNames, user),
      "canSeeArchitect" -> hasUICapability(ViewUIArchitectCapabilities.toCapabilityNames, user),
      "canSeeArchitectAnalysisPageSpectrumAnalysis" -> hasUICapability(ViewUIArchitectAnalysisPageSpectrumAnalysisCapabilities.toCapabilityNames, user),
      "canSeeArchitectScenariosChart" -> hasUICapability(ViewUIArchitectScenariosChartCapabilities.toCapabilityNames, user),
      "canSeeArchitectGrowthChart" -> hasUICapability(ViewUIArchitectGrowthChartCapabilities.toCapabilityNames, user),
      "canSeeArchitectCorrelationMatrixChart" -> hasUICapability(ViewUIArchitectCorrelationMatrixChartCapabilities.toCapabilityNames, user),
      "canSeeArchitectFactorsChart" -> hasUICapability(ViewUIArchitectFactorsChartCapabilities.toCapabilityNames, user),
      "canSeeArchitectAdvisorPDFs" -> hasUICapability(ViewUIArchitectAdvisorPDFsCapabilities.toCapabilityNames, user),
      "canSeeArchitectClients" -> hasUICapability(ViewUIArchitectClientsCapabilities.toCapabilityNames, user),
      "canSeeArchitectBuilderGenericAlts" -> hasUICapability(ViewUIArchitectBuilderGenericAltsCapabilities.toCapabilityNames, user),
      "canSeeArchitectBuilderRealAlts" -> hasUICapability(ViewUIArchitectBuilderRealAltsCapabilities.toCapabilityNames, user),
      "canSeeArchitectBuilderGenericSI" -> hasUICapability(ViewUIArchitectBuilderGenericSICapabilities.toCapabilityNames, user),
      "canSeeArchitectBuilderRealSI" -> hasUICapability(ViewUIArchitectBuilderRealSICapabilities.toCapabilityNames, user),
      "canSeeArchitectBuilderGenericTraditional" -> hasUICapability(ViewUIArchitectBuilderGenericTraditionalCapabilities.toCapabilityNames, user),
      "canSeeArchitectBuilderGenericAltsSpectrumScores" -> hasUICapability(ViewUIArchitectBuilderGenericAltsSpectrumScoresCapabilities.toCapabilityNames, user),
      "canSeeArchitectBuilderRealAltsSpectrumScores" -> hasUICapability(ViewUIArchitectBuilderRealAltsSpectrumScoresCapabilities.toCapabilityNames, user),
      "canSeeArchitectBuilderGenericSISpectrumScores" -> hasUICapability(ViewUIArchitectBuilderGenericSISpectrumScoresCapabilities.toCapabilityNames, user),
      "canSeeArchitectBuilderRealSISpectrumScores" -> hasUICapability(ViewUIArchitectBuilderRealSISpectrumScoresCapabilities.toCapabilityNames, user),
      "canSeeArchitectBuilderGenericTraditionalSpectrumScores" -> hasUICapability(ViewUIArchitectBuilderGenericTraditionalSpectrumScoresCapabilities.toCapabilityNames, user),
      "hideUIArchitectNavBar" -> hasUICapability(HideUIArchitectNavBarCapabilities.toCapabilityNames, user),
      "hideUIArchitectPDFsWatermarks" -> hasUICapability(HideUIArchitectPDFsWatermarksCapabilities.toCapabilityNames, user),
      "canHideSpectrumPDFNetworkName" -> hasUICapability(HideSpectrumPDFNetworkNameCapabilities.toCapabilityNames, user),
      "canSeeContactUs" -> hasUICapability(ViewUIContactUsCapabilities.toCapabilityNames, user),
      "canSeeArchitectAdminTool" -> hasUICapability(ViewUIArchitectAdminToolCapabilities.toCapabilityNames, user),
      "canSeeOnlyCommissionOnSimon" -> hasUICapability(ViewUIOnlyCommissionOnSimonCapabilities.toCapabilityNames, user),
      "canSeeFixedAnnuityRatesWidget" -> hasUICapability(ViewUIFixedAnnuityRatesWidgetCapabilities.toCapabilityNames, user),
      "canSeeFeeAnalysisWidget" -> hasUICapability(ViewUIFeeAnalysisWidgetCapabilities.toCapabilityNames, user),
      "canSeeIncomeRiderNoWithdrawalWidget" -> hasUICapability(ViewUIIncomeRiderNoWithdrawalWidgetCapabilities.toCapabilityNames, user),
      "canSeeBenefitsAnalysisWidget" -> hasUICapability(ViewUIBenefitsAnalysisWidgetCapabilities.toCapabilityNames, user),
      "canSeeAnnuityExplorerWidget" -> hasUICapability(ViewUIAnnuityExplorerWidgetCapabilities.toCapabilityNames, user),
      "canSeeAnnuityClientAgeDistributionWidget" -> hasUICapability(ViewUIAnnuityClientAgeDistributionWidgetCapabilities.toCapabilityNames, user),
      "canSeeAnnuityLowAccountValueWidget" -> hasUICapability(ViewUIAnnuityLowAccountValueWidgetCapabilities.toCapabilityNames, user),
      "canSeePlatformToggle" -> hasUICapability(ViewUIPlatformToggleCapabilities.toCapabilityNames, user),
      "canSeePlatformEnhancementsPage" -> hasUICapability(ViewUIPlatformEnhancementsPageCapabilities.toCapabilityNames, user),
      "canSeeUnifiedEducation" -> hasUICapability(ViewUIUnifiedEducationCapabilities.toCapabilityNames, user),
      "canSeeAnnuityFullApprovalModal" -> hasUICapability(ViewUIAnnuityFullApprovalModalCapabilities.toCapabilityNames, user),
      "canSeeDeveloperHub" -> hasUICapability(ViewUIDeveloperHubCapabilities.toCapabilityNames, user),
      "canSeeEditClientCredentialsViaDeveloperHub" -> hasUICapability(ViewUIEditDeveloperHubCapabilities.toCapabilityNames, user),
      "canHideManagedAccountHoldingsInDashboardWidgets" -> hasUICapability(HideUIManagedAccountHoldingsInDashboardWidgetsCapabilities.toCapabilityNames, user),
      "canSeeUnifiedEducationGeneralAndRegulatory" -> hasUICapability(ViewUIUnifiedEducationGeneralAndRegulatoryCapabilities.toCapabilityNames, user),
      "canSeeAccountIdGlobalFilter" -> hasUICapability(ViewUIAccountIdGlobalFilterCapabilities.toCapabilityNames, user),
      "canSeeIssuerDashboard" -> hasUICapability(ViewUIIssuerDashboardCapabilities.toCapabilityNames, user),
      "canSeeForYouOverride" -> hasUICapability(ViewUIForYouOverrideCapabilities.toCapabilityNames, user),
      "canSeeHoldingViaNetwork" -> hasHoldingsCapability(Set(HoldingsCapabilities.ViewHoldingViaNetworkCapability.name), user),
      "canSeeHoldingViaLocation" -> hasHoldingsCapability(Set(HoldingsCapabilities.ViewHoldingViaLocationCapability.name), user),
      "canSeeHoldingViaDistributorId" -> hasHoldingsCapability(Set(HoldingsCapabilities.ViewHoldingViaDistributorIdCapability.name), user),
      "canSeeArchitectShareCustomAssets" -> hasUICapability(ViewUIArchitectShareCustomAssetsCapabilities.toCapabilityNames, user),
      "canSeeArchitectShareCustomAssetsAll" -> hasUICapability(ViewUIArchitectShareCustomAssetsAllCapabilities.toCapabilityNames, user),
      "canSeeArchitectSharePortfolios" -> hasUICapability(ViewUIArchitectSharePortfoliosCapabilities.toCapabilityNames, user),
      "canSeeArchitectSharePortfoliosAll" -> hasUICapability(ViewUIArchitectSharePortfoliosAllCapabilities.toCapabilityNames, user),
      "canSeeArchitectCreateCustomAssets" -> hasUICapability(ViewUIArchitectCreateCustomAssetsCapabilities.toCapabilityNames, user),
      "canSeeArchitectPortfolioGroupBrowse" -> hasUICapability(ViewUIArchitectPortfolioGroupCapabilities.toCapabilityNames, user),
      "canSeeArchitectAssetClassOptimizations" -> hasUICapability(ViewUIArchitectAssetClassOptimizationsCapabilities.toCapabilityNames, user),
      "canSeeArchitectFindSimilarAssets" -> hasUICapability(ViewUIArchitectFindSimilarAssetsCapabilities.toCapabilityNames, user),
      "canSeeArchitectBuilderSleeves" -> hasUICapability(viewUIArchitectBuilderSleevesCapabilities.toCapabilityNames, user),
      "canSeeUIArchitectBuilderInvest" -> hasUICapability(ViewUIArchitectBuilderInvestCapabilities.toCapabilityNames, user),
      "canSeeUIArchitectDownloadPDFs" -> hasUICapability(ViewUIArchitectDownloadPDFsCapabilities.toCapabilityNames, user),
      "canSeeUIArchitectClientPDFs" -> hasUICapability(ViewUIArchitectClientPDFsCapabilities.toCapabilityNames, user),
      "canSeeOffPlatformAnnuities" -> hasUICapability(ViewUIOffPlatformAnnuitiesCapabilities.toCapabilityNames, user),
      "canSeeSIOrdersEnhanced"-> hasUICapability(ViewUISIOrdersEnhancedCapabilities.toCapabilityNames, user),
      "canSeeUIMultiDocsInIssuerPortal"-> hasUICapability(ViewUIMultiDocsInIssuerPortalCapabilities.toCapabilityNames, user),
      "canSeeUIMultiCurrencyInLifecyclePortal"-> hasUICapability(ViewUIMultiCurrencyInLifecyclePortalCapabilities.toCapabilityNames, user),
      "canSeeUITierInCurrentOfferings" -> hasUICapability(Set(ViewUITierCapability.name), user),
      "canSeeUIOverrideTierInCurrentOfferings" -> hasOfferingsCapability(OverrideTierCapabilities, user),
      "canSeeAnnuityHoldingsViaIssuer" -> hasAnnuityHoldingsCapability(Set(AnnuityHoldingsCapabilities.viewAnnuityHoldingsViaIssuer), user),
      "canSeeAnnuityHoldingsViaLicense" -> hasAnnuityHoldingsCapability(Set(AnnuityHoldingsCapabilities.viewAnnuityHoldingsViaLicenseCapability.name), user),
      "canSeeAnnuityHoldingsViaPurviewLicenses" -> hasAnnuityHoldingsCapability(Set(AnnuityHoldingsCapabilities.viewAnnuityHoldingsViaPurviewLicensesCapability.name), user),
      "canSeeBatchUnentitle"-> hasUICapability(ViewUIBatchUnentitleCapabilities.toCapabilityNames, user),
      "canSeeSIUnblockedPerformanceAnalysis"-> hasUICapability(ViewUISIUnblockedPerformanceAnalysisCapabilities.toCapabilityNames, user),
      "canSeeUserManagement" -> hasUICapability(ViewUIUserManagementCapabilities.toCapabilityNames, user),
      "canSeeUserManagementUnified" -> hasUICapability(ViewUIUserManagementCapabilitiesUnified.toCapabilityNames, user),
      "canSeeAnnuitiesLifecycleFANumberSearch" -> hasUICapability(ViewUIAnnuitiesLifecycleFANumberSearchCapabilities.toCapabilityNames, user),
      "canSeeFixedAnnuitiesRatesOutputPDF" -> hasUICapability(ViewUIFixedAnnuitiesRatesOutputPDFCapabilities.toCapabilityNames, user),
      "canSeeArchitectTermsOfServiceModal" -> hasUICapability(ViewUIArchitectTermsOfServiceModalCapabilities.toCapabilityNames, user),
      "canSeeArchitectProRataAllocations"-> hasUICapability(ViewUIArchitectProRataAllocationsCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3Insights"-> hasUICapability(ViewUILearningCenterV3InsightsCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3Glossary"-> hasUICapability(ViewUILearningCenterV3GlossaryCapabilities.toCapabilityNames, user),
      "canSeeAnnuityLifecycleNotification" -> hasUICapability(ViewUIAnnuityLifecycleNotificationCapabilities.toCapabilityNames, user),
      "canSeeAllocationBacktestingV2" -> hasUICapability(ViewUIAllocationBacktestingV2Capabilities.toCapabilityNames, user),
      "canSeeUnifiedHomepage" -> hasUICapability(ViewUIUnifiedHomepageCapabilities.toCapabilityNames, user),
      "canSeeUnifiedSIMarketplace" -> hasUICapability(ViewUIUnifiedSIMarketplaceCapabilities.toCapabilityNames, user),
      "canSeeUnifiedAnnuitiesMarketplace" -> hasUICapability(ViewUIUnifiedAnnuitiesMarketplaceCapabilities.toCapabilityNames, user),
      "canSeeUnifiedETFMarketplace" -> hasUICapability(ViewUIUnifiedETFMarketplaceCapabilities.toCapabilityNames, user),
      "canSeeUnifiedSMAMarketplace" -> hasUICapability(ViewUIUnifiedSMAMarketplaceCapabilities.toCapabilityNames, user),
      "canSeeApprovalImpersonate" -> hasUICapability(ViewUIApprovalImpersonateCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3AlternativeInvestmentsResources" -> hasUICapability(ViewUILearningCenterV3AlternativeInvestmentsResourcesCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3StructuredInvestmentsResources"  -> hasUICapability(ViewUILearningCenterV3StructuredInvestmentsResourcesCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3AnnuitiesResources"              -> hasUICapability(ViewUILearningCenterV3AnnuitiesResourcesCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3DefinedOutcomeETFsResources"     -> hasUICapability(ViewUILearningCenterV3DefinedOutcomeETFsResourcesCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3GeneralAndRegulatoryResources"   -> hasUICapability(ViewUILearningCenterV3GeneralAndRegulatoryResourcesCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3CustomizationsPortal"            -> hasUICapability(ViewUILearningCenterV3CustomizationsPortalCapabilities.toCapabilityNames, user),
      "canSeeSIAIUploads"            -> hasUICapability(ViewUISIAIUploadsCapabilities.toCapabilityNames, user),
      "canSeeLearningCenterV3CourseFinder" -> hasUICapability(ViewUILearningCenterV3CourseFinderCapabilities.toCapabilityNames, user),
      "canSeeSITradeTMinus2"       -> hasUICapability(Set(ViewUISITradeTminus2Capability.name), user),
      "canSeeSIAdditionalDetailsCard"       -> hasUICapability(Set(ViewUISIAdditionalDetailsCardCapability.name), user),
      "canSeeArchitectSharePortfoliosIndividuals" -> hasUICapability(ViewUIArchitectSharePortfoliosIndividualsCapabilities.toCapabilityNames, user),
      "canSeeArchitectShareCustomAssetsIndividuals" -> hasUICapability(ViewUIArchitectShareCustomAssetsIndividualsCapabilities.toCapabilityNames, user),
      "canSeeArchitectSharePortfoliosNetworks" -> hasUICapability(ViewUIArchitectSharePortfoliosNetworksCapabilities.toCapabilityNames, user),
      "canSeeArchitectShareCustomAssetsNetworks" -> hasUICapability(ViewUIArchitectShareCustomAssetsNetworksCapabilities.toCapabilityNames, user),
      "canSeeArchitectBuilderInvestInvestmentStatusAlts" -> hasUICapability(ViewUIArchitectBuilderInvestInvestmentStatusAltsCapabilities.toCapabilityNames, user),
      "canSeeArchitectBuilderInvestInvestmentStatusSI" -> hasUICapability(ViewUIArchitectBuilderInvestInvestmentStatusSICapabilities.toCapabilityNames, user),
      "canSeeArchitectBuilderReplaceHoldings" -> hasUICapability(ViewUIArchitectBuilderReplaceHoldingsCapabilities.toCapabilityNames, user),

      "canUseSIContractsOfferingsUpdatesApi"  -> hasUICapability(Set(ViewUISIContractsOfferingsUpdatesApiCapability.name),  user),
      "canUseSIContractsOfferingsUbertable" -> hasUICapability(Set(ViewUISIContractsOfferingsUbertableCapability.name), user),
      "canUseSIContractsOfferingsBulkApis"  -> hasUICapability(Set(ViewUISIContractsOfferingsBulkApisCapability.name),  user),
      "canUseSIContractsOfferingsApprovalApi"  -> hasUICapability(Set(ViewUISIContractsOfferingsApprovalApiCapability.name),  user),
      "canSeeSIPortfolioPDF"       -> hasUICapability(Set(ViewUISIPortfolioPDFCapability.name), user),
      "canSeeSIPortfolioPDFClientName"       -> hasUICapability(Set(ViewUISIPortfolioPDFClientNameCapability.name), user),
      "canSeeSIPortfolioPDFCusipLevel"       -> hasUICapability(Set(ViewUISIPortfolioPDFCusipLevelCapability.name), user),

      "canSeeWholesalerExchange"  -> hasUICapability(ViewUIWholesalerExchangeCapabilities.toCapabilityNames,  user),
      "canSeeWholesalerExchangeAsTechUser"  -> hasUICapability(viewUIWholesalerExchangeAsTechUserCapabilities.toCapabilityNames,  user),
      "canSeeArchitectHoldingsOptimizations" -> hasUICapability(ViewUIArchitectHoldingsOptimizationsCapabilities.toCapabilityNames, user),
      "canSeeRegBIEvaluation" -> hasUICapability(Set(ViewUIRegBIEvaluation.name), user),

      "canSeeArchitectExpressSurvey" -> hasUICapability(ViewUIArchitectExpressSurveyCapabilities.toCapabilityNames, user),
      "canSeeArchitectStreamlinedSurvey" -> hasUICapability(ViewUIArchitectStreamlinedSurveyCapabilities.toCapabilityNames, user),
      "canSeeArchitectLongFormSurvey" -> hasUICapability(ViewUIArchitectLongFormSurveyCapabilities.toCapabilityNames, user),
      "canSeeArchitectAnalysisPageUAF" -> hasUICapability(ViewUIArchitectAnalysisPageUAFCapabilities.toCapabilityNames, user),
      "canSeeArchitectMFE" -> hasUICapability(ViewUIArchitectMFECapabilities.toCapabilityNames, user),
    )
  }

  private def hasSalesFeeCapability(capability: String, user: UserACL): Boolean = {
    SalesFeeCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(capability, user).nonEmpty
  }

  private def hasRfqCapability(capabilities: Set[String], user: UserACL): Boolean = {
    RfqCapabilities.getAvailableAccessKeysForCapabilities(capabilities, user).nonEmpty
  }

  private def hasRfqTemplatesCapability(capabilities: Set[String], user: UserACL): Boolean = {
    RfqTemplatesCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapabilities(capabilities, user).nonEmpty
  }

  private def hasRfqTagsCapability(capability: String, user: UserACL): Boolean = {
    RfqTagsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(capability, user).nonEmpty
  }

  private def hasOrdersCapability(capabilities: Set[String], user: UserACL): Boolean = {
    OrdersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapabilities(capabilities, user).nonEmpty
  }

  private def hasCapability(network: Network, capability: String): Boolean = {
    network.capabilities.get(capability).flatMap(list => list.find(_.equals("view"))).isDefined
  }

  private def hasUICapability(capabilities: Set[String], user: UserACL): Boolean = {
    SimonUICapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapabilities(capabilities, user).nonEmpty
  }

  private def hasOfferingsCapability(capabilities: Set[String], user: UserACL): Boolean = {
    AllOfferingsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapabilities(capabilities, user).nonEmpty
  }

  private def hasOfferingsV1Capability(capabilities: Set[String], user: UserACL): Boolean = {
    OfferingsV1Capabilities.availableAccessKeysGen.getAvailableAccessKeysForCapabilities(capabilities, user).nonEmpty
  }

  private def hasProductNomenclatureCapability(capabilities: Set[String], user: UserACL): Boolean = {
    ProductNomenclatureCapabilities.AvailableAccessKeysGenerator.getAvailableAccessKeysForCapabilities(
      capabilities, user).nonEmpty
  }

  private def hasBatchStagingCapability(capabilities: Set[String], user: UserACL): Boolean = {
    BatchStagingCapabilities.getAvailableAccessKeysForCapabilities(capabilities, user).nonEmpty
  }

  private def hasHoldingsCapability(capabilities: Set[String], user: UserACL): Boolean = {
    HoldingsCapabilities.HoldingsAvailableAccessKeys.getAvailableAccessKeysForCapabilities(capabilities, user).nonEmpty
  }

  private def hasAnnuityHoldingsCapability(capabilities: Set[String], user: UserACL): Boolean = {
    AnnuityHoldingsCapabilities.AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeysForCapabilities(capabilities, user).nonEmpty
  }

  private lazy val canManageOffering: Network => Boolean = {
    val networksOpt = Configuration.appProperties.get(s"simon.config.ui.tradeweb.offeringDropdownDisabledNetworks")
    networksOpt match {
      case Some(networks) if networks.trim.nonEmpty =>
        val dropList = networks.split(",").toSet
        x => !dropList.contains(x.name)
      case _ =>
        _ => true
    }
  }

  private def networkCapabilitiesHelper(networkOpt: Option[Network], capabilityName: String): Boolean = {
    val viewCapability = "view"

    networkOpt match {
      case None => false
      case Some(network) =>
        network
          .capabilities
          .get(capabilityName)
          .fold(false) {
            capabilityToCheck => capabilityToCheck.contains(viewCapability)
          }
    }
  }

  private def canSeeFWPDisclaimer(networkOpt: Option[Network]): Boolean = {
    networkCapabilitiesHelper(networkOpt, "fwpDisclaimer")
  }

  private def isTradewebEligible(networkOpt: Option[Network]): Boolean = {
    networkCapabilitiesHelper(networkOpt, "tradewebEligible")
  }
}
