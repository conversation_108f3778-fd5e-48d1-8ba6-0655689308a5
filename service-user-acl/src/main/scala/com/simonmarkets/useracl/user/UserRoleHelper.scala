package com.simonmarkets.useracl.user

import com.goldmansachs.marquee.pipg.{HasLongName, Primary, UserACL, UserRole}
import com.goldmansachs.marquee.pipg.UserRole.PrimaryOrdered

object UserRoleHelper {

  def primary(x: UserACL): Option[UserRole with Primary with HasLongName] = PrimaryOrdered.find(x.roles.contains)

  def longNameRoles(x: UserACL): Set[String] = x.roles.collect { case x: HasLongName => x.longName }

}
