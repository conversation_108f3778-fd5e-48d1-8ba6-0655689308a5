package com.simonmarkets.useracl

import akka.http.scaladsl.server.Directives.complete
import akka.http.scaladsl.server.{Directive0, Directive1, Exception<PERSON><PERSON><PERSON>, Route}
import akka.stream.Materializer
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.mongodb.error.Conflict
import com.simonmarkets.resteasy.requests.AuthorizedDirectives
import com.simonmarkets.resteasy.{DirectivesWithCirce, HttpErrorHandler}
import com.simonmarkets.useracl.PaidFeaturesRoutes.customExceptionHandler
import com.simonmarkets.useracl.api.{BulkUpsertPaidFeaturesRequest, PaidFeaturesRequest}
import com.simonmarkets.useracl.domain.IdType
import com.simonmarkets.useracl.service.PaidFeaturesServiceImpl

import scala.concurrent.ExecutionContext

case class PaidFeaturesRoutes(
    service: PaidFeaturesServiceImpl,
    authDirectives: AuthorizedDirectives[UserACL])
  (implicit ec: ExecutionContext, materializer: Materializer)
  extends DirectivesWithCirce with JsonCodecs {

  import authDirectives._

  lazy val routes: Route = handleExceptions(customExceptionHandler) {
    authorized(Capabilities.Admin) { (trace: TraceId, requester: UserACL) =>
      implicit val traceId: TraceId = trace
      implicit val userACL: UserACL = requester
      PaidFeaturesPathPrefix {
        concat(
          `GET /:id` { userId =>
            rejectEmptyResponse {
              complete(service.getPaidFeatures(userId))
            }
          },
          `PUT /` {
            entity(as[PaidFeaturesRequest]) { request =>
              complete(service.upsertPaidFeatures(request, IdType.UserId))
            }
          },
          `PUT /bulk-upsert` {
            entity(as[BulkUpsertPaidFeaturesRequest]) { request =>
              complete(service.bulkUpsertNetworkUsersPaidFeatures(request))
            }
          }
        )
      }
    }
  }

  val PaidFeaturesPathPrefix: Directive0 = pathPrefix("simon" / "api" / "v1" / "paid-features")

  def `GET /:id`: Directive1[String] = get & path(Segment) & pathEnd

  def `PUT /`: Directive0 = put & pathEndOrSingleSlash

  def `PUT /bulk-upsert`: Directive0 = put & path("bulk-upsert") & pathEnd

}

object PaidFeaturesRoutes {
  val customExceptionHandler: ExceptionHandler = HttpErrorHandler.errorHandler.withFallback(
    ExceptionHandler {
      case ex: Conflict => complete(HttpError.conflict(ex.message).httpResponse)
    }
  )
}