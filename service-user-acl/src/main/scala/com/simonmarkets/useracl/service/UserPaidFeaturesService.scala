package com.simonmarkets.useracl.service

import akka.stream.Materializer
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.api.users.HttpACLClient
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.useracl.api.{BulkUpsertPaidFeaturesRequest, PaidFeaturesRequest, PaidFeaturesResponse}
import com.simonmarkets.useracl.domain.{IdType, PaidRole, RoleSource}
import com.simonmarkets.useracl.repository.PaidFeaturesRepositoryImpl
import simon.Id.NetworkId

import scala.concurrent.{ExecutionContext, Future}

trait PaidFeaturesService {
  def upsertPaidFeatures(request: PaidFeaturesRequest, idType: IdType)
    (implicit traceId: TraceId, ec: ExecutionContext, userACL: UserACL): Future[PaidFeaturesResponse]

  def getPaidFeatures(userId: String, networkId: Option[NetworkId] = None)
    (implicit traceId: TraceId, ec: ExecutionContext, userACL: UserACL): Future[Option[PaidFeaturesResponse]]

  def bulkUpsertNetworkUsersPaidFeatures(request: BulkUpsertPaidFeaturesRequest)
    (implicit traceId: TraceId, ec: ExecutionContext, userACL: UserACL,
        materializer: Materializer): Future[PaidFeaturesResponse]
}

class PaidFeaturesServiceImpl(
    repository: PaidFeaturesRepositoryImpl,
    aclClient: HttpACLClient
) extends PaidFeaturesService with TraceLogging {

  override def upsertPaidFeatures(request: PaidFeaturesRequest, idType: IdType)
    (implicit traceId: TraceId, ec: ExecutionContext, userACL: UserACL): Future[PaidFeaturesResponse] = {
    for {
      existingPaidFeatureOpt <- repository.findById(request.id, idType)
      paidFeatures <- existingPaidFeatureOpt match {
        case Some(existingPaidFeature) =>
          log.info("Updating PaidFeature", idType)
          repository.updatePaidFeatures(request, existingPaidFeature)
        case None if idType == IdType.UserId =>
          log.info("Inserting PaidFeature", idType)
          aclClient.getUserACL(request.id).flatMap { acl =>
            repository.insertPaidFeatures(request, idType, Some(NetworkId.unwrap(acl.networkId)))
          }
        case None =>
          log.info("Inserting PaidFeature", idType)
          repository.insertPaidFeatures(request, idType, None)
      }
      paidFeaturesResponse = PaidFeaturesResponse(paidFeatures)
    } yield paidFeaturesResponse
  }

  def bulkUpsertNetworkUsersPaidFeatures(request: BulkUpsertPaidFeaturesRequest)
    (implicit traceId: TraceId, ec: ExecutionContext, userACL: UserACL,
        materializer: Materializer): Future[PaidFeaturesResponse] = {
    val roles = request.roles.map(role => PaidRole(role, RoleSource.Manual))
    val paidFeaturesRequest = PaidFeaturesRequest(request.networkId.toString, roles)
    //removes the roles from individual paidFeatures that have users under the request network
    repository.removeUserPaidRoles(request.networkId.toString, roles)
    upsertPaidFeatures(paidFeaturesRequest, IdType.NetworkId)
  }

  override def getPaidFeatures(userId: String, networkId: Option[NetworkId] = None)
    (implicit traceId: TraceId, ec: ExecutionContext, userACL: UserACL): Future[Option[PaidFeaturesResponse]] = {
    //Gets PaidFeatures for User and User's network and combines the roles if there are any
    log.info("Getting paidFeatures", userId)
    val paidFeatureByUserIdOpt = getPaidFeaturesResponseById(userId, IdType.UserId)
    val paidFeatureByNetworkIdOpt = getUserNetworkId(userId, networkId).flatMap { networkId =>
      getPaidFeaturesResponseById(networkId.toString, IdType.NetworkId)
    }
    for {
      paidFeatureByUser <- paidFeatureByUserIdOpt
      paidFeatureByNetworkId <- paidFeatureByNetworkIdOpt
      combinedPaidFeature = combinedPaidFeaturesResponse(paidFeatureByUser, paidFeatureByNetworkId, userId)
    } yield (combinedPaidFeature)
  }

  def combinedPaidFeaturesResponse(
      userPaidFeature: Option[PaidFeaturesResponse],
      networkPaidFeature: Option[PaidFeaturesResponse],
      userId: String
  ): Option[PaidFeaturesResponse] = {
    (userPaidFeature, networkPaidFeature) match {
      case (Some(userPaidFeature), Some(networkPaidFeature)) =>
        val paidRoles = userPaidFeature.roles ++ networkPaidFeature.roles
        Some(PaidFeaturesResponse(userId, paidRoles))
      case (Some(response1), None) =>
        Some(PaidFeaturesResponse(userId, response1.roles))
      case (None, Some(response2)) =>
        Some(PaidFeaturesResponse(userId, response2.roles))
      case (None, None) => None
    }
  }

  def getPaidFeaturesResponseById(id: String, idType: IdType)
    (implicit traceId: TraceId, ec: ExecutionContext, userACL: UserACL): Future[Option[PaidFeaturesResponse]] = {
    repository.findById(id, idType).map {
      case Some(paidFeatures) => Some(PaidFeaturesResponse(paidFeatures))
      case None => None
    }
  }

  def getUserNetworkId(userId: String, networkId: Option[NetworkId])
    (implicit traceId: TraceId, ec: ExecutionContext): Future[NetworkId] = {
    networkId match {
      case Some(networkId) => Future.successful(networkId)
      case None => aclClient.getUserACL(userId).map(_.networkId)
    }
  }
}
