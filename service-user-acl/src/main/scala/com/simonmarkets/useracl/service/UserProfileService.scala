package com.simonmarkets.useracl.service

import akka.http.scaladsl.model.headers.{OAuth2BearerToken => AccessToken}
import com.goldmansachs.marquee.pipg.{NetworkCategory, UserACL}
import com.simonmarkets.capabilities.{AssetClassToUICapabilities, LearnContentCapabilities, SimonUICapabilities}
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.domain.Visuals
import com.simonmarkets.networks.common.service.NetworkVisualsService
import com.simonmarkets.useracl.config.SimonConfig
import com.simonmarkets.useracl.domain.{MaskedIds, UnifiedUserProfile, UserProfile}
import com.simonmarkets.useracl.user.{UserCapabilities, UserRoleHelper}
import com.simonmarkets.users.repository.UserRepository
import com.simonmarkets.users.service.MasterUserService
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.networks.common.service.networkservice.BasicNetworkService
import com.simonmarkets.useracl.client.IcnClient

import scala.concurrent.{ExecutionContext, Future}

class UserProfileService(
    config: SimonConfig,
    networkService: BasicNetworkService,
    networkVisualsService: NetworkVisualsService,
    userRepository: UserRepository,
    masterUserService: MasterUserService,
    paidFeatureService: PaidFeaturesService,
    icnClient: IcnClient
)
  (implicit ec: ExecutionContext) extends TraceLogging with JsonCodecs {

  def getOwnUserProfile(impersonatorUserId: Option[String])
    (implicit traceId: TraceId, userACL: UserACL): Future[UserProfile] = {
    log.info("Getting user profile for: ", userACL.userId)

    val tradewebId = userACL.omsId
    val primaryRole = UserRoleHelper.primary(userACL).map(_.longName).getOrElse("")
    val reversedUserName = s"${userACL.lastName} ${userACL.firstName}".trim
    val simonUserRoles = UserRoleHelper.longNameRoles(userACL)
    //pre-declaring futures
    val networkFut = networkService.unentitledGetNetworkById(userACL.networkId)
    val paidFeaturesFut = paidFeatureService.getPaidFeatures(userACL.userId, Some(userACL.networkId))
    val visualsFut = getVisuals()
    val masterUserProfileFut = masterUserService.getMasterUser(userACL.email, None, None)
    for {
      network <- networkFut
      paidFeaturesOpt <- paidFeaturesFut
      visuals <- visualsFut
      masterUserProfiles <- masterUserProfileFut
      profile = UserProfile(
        userId = userACL.userId,
        network = userACL.networkInfo,
        networkCapabilities = userACL.networkCapabilities,
        networkTypes = userACL.networkTypes,
        firstName = userACL.firstName,
        lastName = userACL.lastName,
        fullName = userACL.fullName,
        email = userACL.email,
        comparisonValue = reversedUserName.toLowerCase,
        capabilities = UserCapabilities.toCapabilities(userACL, Some(network)),
        tradewebId = tradewebId,
        humanReadableRole = primaryRole,
        roles = simonUserRoles,
        showLearningCenterLinksForIssuers = config.issuers.learningCenterEnabled.getOrElse(Set.empty),
        maskedIds = MaskedIds(userACL.maskedIds, network.externalIds),
        category = network.networkTypes.flatMap(NetworkCategory.fromTypes),
        accountInContext = userACL.accountInContext,
        context = userACL.context,
        partnerUrls = network.partnerUrls,
        hasEndClientSharing = userACL.capabilities.contains(LearnContentCapabilities.ExternalShareViaEndClientShareableId),
        otherProfiles = masterUserProfiles.users.filter(user => user.networkName.exists(_ != userACL.networkInfo.name)),
        networkContactInfo2 = network.contactInfo2,
        networkContactInfo2Name = network.contactInfo2Name,
        uiViewCardOverrides = network.uiViewCardOverrides,
        uiViewCardOverridesExpiryDate = network.uiViewCardOverridesExpiryDate,
        assetClassUICapabilityMap = getCapabilityMappings,
        historicHoldingsStartFrom = network.historicHoldingsStartFrom,
        cobrandingCustomDisclosure = network.cobrandingCustomDisclosure,
        visuals = visuals,
        sessionInactivityTimeout = network.sessionInactivityTimeout,
        paidRoles = paidFeaturesOpt.toSeq.flatMap(_.roles.map(_.product)),
        passport = userACL.passport.getOrElse(Map.empty),
        domiciles = network.domiciles,
        wlpUrl = network.wlpUrl
      )
      // only call refreshLastUpdateTime if the user is not impersonating
      _ <- if (impersonatorUserId.isEmpty) userRepository.refreshLastUpdatedTime(userACL.userId) else Future.successful(())
    } yield profile
  }

  def getUnifiedUserProfile(impersonatorUserId: Option[String])(implicit traceId: TraceId, userACL: UserACL, token: AccessToken): Future[UnifiedUserProfile] = {
    val simonProfileF = getOwnUserProfile(impersonatorUserId)
    val icnProfileF = icnClient.getUserProfile
    for {
      simonProfile <- simonProfileF
      icnProfile <- icnProfileF
    } yield UnifiedUserProfile(simonProfile, icnProfile)
  }

  def getCapabilityMappings: AssetClassToUICapabilities = {
    SimonUICapabilities.toAssetClassUserCapabilityMap
  }

  def getVisuals()(implicit traceId: TraceId, userACL: UserACL): Future[Option[Visuals]] = {
    networkVisualsService.get(userACL.networkId).map { networkVisuals =>
      Some(networkVisuals.visuals)
    }.recover {
      case _: HttpError =>
        None
    }
  }

}
