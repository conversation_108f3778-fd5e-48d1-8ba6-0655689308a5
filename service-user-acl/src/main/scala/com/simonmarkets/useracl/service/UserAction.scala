package com.simonmarkets.useracl.service

import com.simonmarkets.capabilities.UsersCapabilities.{ImpersonateCapabilities, ImpersonateWithApprovalCapabilities, ViewCapabilities}
import com.simonmarkets.util.{EnumEntry, ProductEnums}

sealed trait UserAction extends EnumEntry {
  val capabilities: Set[String]
}

object UserAction extends ProductEnums[UserAction] {

  case object View extends UserAction {
    override val capabilities: Set[String] = ViewCapabilities
  }

  case object Impersonate extends UserAction {
    override val capabilities: Set[String] = ImpersonateCapabilities ++ ImpersonateWithApprovalCapabilities
  }

  override def Values: Seq[UserAction] = Seq(View, Impersonate)
}
