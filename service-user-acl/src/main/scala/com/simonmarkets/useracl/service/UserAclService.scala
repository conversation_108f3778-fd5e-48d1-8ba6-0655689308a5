package com.simonmarkets.useracl.service

import com.goldmansachs.marquee.pipg.{ApproverMap, UserACL, UserAclField, Network => NetworkP}
import com.simonmarkets.api.users.cache.UserAclRepository
import com.simonmarkets.api.users.cache.dynamodb.CachedUserAcl
import com.simonmarkets.capabilities.UsersCapabilities._
import com.simonmarkets.capabilities.{Capabilities, NetworksCapabilities}
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.Network
import com.simonmarkets.networks.common.repository.NetworkRepository
import com.simonmarkets.networks.{ExternalId => ExId}
import com.simonmarkets.syntax._
import com.simonmarkets.useracl.api.{CacheSyncRequest, GetUserAclRequest, UserAclQueryRequest}
import com.simonmarkets.useracl.config.AclServiceConfiguration
import com.simonmarkets.useracl.service.UserAction.Impersonate
import com.simonmarkets.users.common.AttributeType.{FaN<PERSON>ber, ICapitalUserId}
import com.simonmarkets.users.common.IdType._
import com.simonmarkets.users.common.{AttributeType, IdType, User}
import com.simonmarkets.users.repository.UserRepository
import com.simonmarkets.users.repository.filters._
import com.simonmarkets.users.service.UserAcceptedAccessKeysGenerator
import com.simonmarkets.resteasy.utils.FutureOps._
import simon.Id.NetworkId

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class UserAclService(
    userRepo: UserRepository, networkRepo: NetworkRepository,
    userAclRepository: UserAclRepository, config: AclServiceConfiguration)
  (implicit ec: ExecutionContext) extends TraceLogging with UserAcceptedAccessKeysGenerator {

  def getUserAcl(requesterId: String, request: GetUserAclRequest)
    (implicit traceId: TraceId): Future[UserACL] = {
    val requester = request.from.getOrElse(requesterId)
    log.info("Processing GetUserAClRequest", requesterId, request)

    for {
      (userActionKeys, networkViewKeys) <- getUserActionAvailableKeysAndNetworkViewAvailableKeys(requester)
      _ = log.debug(s"Entitlements: $userActionKeys, $networkViewKeys")
      filters <- Future.fromTry(createFilter(request.principalIdType, request.networkId, request.subject, None, Seq(request.id), Seq.empty))

      user <- userRepo.getUser(Set(filters))(userActionKeys)
        .flattenOption(s"User not exists or requester is not entitled to view user information, requesterId=$requesterId, filters=${filters}")
      network <- networkRepo.getById(user.networkId)(networkViewKeys).flattenOption(s"Network not found with id=${user.networkId}")
      userAclAllFields = UserACL(user.asLegacyUser, network.asLegacyNetwork)
      userAcl = filterUserACLFields(userAclAllFields, request.fields)
    } yield userAcl
  }

  def getUserAcls(requesterId: String, request: UserAclQueryRequest)
    (implicit traceId: TraceId): Future[List[UserACL]] = {
    val requester = request.from.getOrElse(requesterId)
    log.info("Processing AclRequest", "originalRequester" -> requesterId, requester, request)
    val action = request.action.flatMap(UserAction.unapply).getOrElse(Impersonate)
    for {
      (userActionKeys, networkViewKeys) <- getUserActionAvailableKeysAndNetworkViewAvailableKeys(requester, action)
      filters <- Future.fromTry(createFilter(request.principalIdType, request.networkId, request.subject, request.attributeType, request.ids.toSeq, request.attributeValues.toSeq))
      users <- userRepo.getUsers(Set(filters))(userActionKeys)
      networks <- networkRepo.getByIds(users.map(_.networkId).toSet)(networkViewKeys)
      accessibleNetworkIds = networks.map(_.id).toSet
      aclsAllFields <- Future {
        users.filter(user => accessibleNetworkIds.contains(user.networkId)).map(user =>
          UserACL(user.asLegacyUser,
            networks.find(n => user.networkId == n.id)
              .getOrElse(throw HttpError.notFound(s"Couldn't query network with id=${user.networkId}")).asLegacyNetwork)
        )
      }
      acls = aclsAllFields.map(filterUserACLFields(_, request.fields))
    } yield acls
  }

  def getCacheEntry(requesterId: String, userId: String)(implicit traceId: TraceId): Future[Option[CachedUserAcl]] = {
    log.info("Getting acl cache entry", requesterId, userId)
    getUserActionAvailableKeysAndNetworkViewAvailableKeys(requesterId).flatMap {
      case (userActionKeys, _) =>
        if (userActionKeys.contains(Capabilities.Admin)) {
          userAclRepository.getWithMetadata(userId)
        } else {
          Future.failed(HttpError.unauthorized("Not authorized"))
        }
    }
  }

  def syncAclCache(requesterId: String, request: CacheSyncRequest)(implicit traceId: TraceId): Future[Unit] = {
    log.info("Processing cache sync request", requesterId, request)
    getUserActionAvailableKeysAndNetworkViewAvailableKeys(requesterId).flatMap {
      case (userActionKeys, _) =>
        if (userActionKeys.contains(Capabilities.Admin)) {
          (request.userIds, request.networkIds) match {
            case (Some(_), Some(_)) => Future.failed(HttpError.badRequest("the 'userIds' and 'networkIds' parameter shouldn't be set in the same request!"))

            case (Some(userIds), None) => refreshAclCacheForUsers(userIds, userActionKeys)

            case (None, Some(networkIds)) => refreshAclCacheForNetworks(networkIds, userActionKeys)

            case (None, None) => recreateUserACLTable(userActionKeys)
          }
        } else {
          Future.failed(HttpError.unauthorized("Not authorized"))
        }
    }
  }

  private def recreateUserACLTable(entitlements: Set[String])(implicit traceId: TraceId): Future[Unit] = {
    val users = userRepo.getUsers()(entitlements)
    val networks = networkRepo.getNetworks(entitlements)

    val res = for {
      users <- users
      networks <- networks
      userAcls = toUserAcls(users, networks)
      _ <- userAclRepository.recreateTableWithUserAcls(userAcls)
    } yield ()

    res.foreach(_ => log.info("UserAcl cache reload has successfully finished."))
    res
  }

  private def refreshAclCache(users: List[User], networks: List[Network])(implicit traceId: TraceId): Future[Unit] = {
    val acls = toUserAcls(users, networks)
    log.info(s"Refreshing ${acls.size} acls in the cache")
    userAclRepository.putAll(acls)
  }

  private def refreshAclCacheForUsers(userIds: List[String], entitlements: Set[String])
    (implicit traceId: TraceId): Future[Unit] = {
    val res = for {
      users <- userRepo.getUsersByIds(userIds.toSet)(entitlements)
      networks <- networkRepo.getByIds(users.map(_.networkId).toSet)(entitlements)
      _ <- refreshAclCache(users, networks)
    } yield ()

    res.foreach(_ => log.info(s"Acl cache sync finished for users $userIds"))
    res
  }

  private def refreshAclCacheForNetworks(networkIds: List[NetworkId], entitlements: Set[String])
    (implicit traceId: TraceId): Future[Unit] = {
    val filter: MongoFilter = OrFilter(networkIds.map(id => NetworkFilter(id.toString)): _*)
    val usersFuture = userRepo.getUsers(Set(filter))(entitlements)
    val networksFuture = networkRepo.getByIds(networkIds.toSet)(entitlements)

    val res = for {
      users <- usersFuture
      networks <- networksFuture
      _ <- refreshAclCache(users, networks)
    } yield ()

    res.foreach(_ => log.info(s"Acl cache sync finished for networks $networkIds"))
    res
  }

  private def toUserAcls(users: List[User], networks: List[Network]): List[UserACL] = {
    val legacyNetworks = networks.map(n => n.id -> n.asLegacyNetwork).toMap
    for {
      user <- users
      network <- legacyNetworks.get(user.networkId)
    } yield UserACL(user.asLegacyUser, network)
  }

  private def getUserActionAvailableKeysAndNetworkViewAvailableKeys(requesterId: String, action: UserAction = UserAction.View)
    (implicit traceId: TraceId): Future[(Set[String], Set[String])] = {
    val ents = for {
      requesterUser <- userRepo.getById(requesterId)(config.serviceEntitlements).flattenOption(s"User not found with id=$requesterId")
      requesterNetwork <- networkRepo.getById(requesterUser.networkId)(config.serviceEntitlements).flattenOption(s"Network not found with id=${requesterUser.networkId}")
      requesterAcl <- UserACL(requesterUser.asLegacyUser, requesterNetwork.asLegacyNetwork).successFuture
      userActionKeys = availableAccessKeysGen.getAvailableAccessKeysForCapabilities(action.capabilities, requesterAcl)
      networkViewKeys = NetworksCapabilities.getAvailableAccessKeysForCapabilities(NetworksCapabilities.ViewCapabilities, requesterAcl)
    } yield {
      log.debug(s"User keys for action $action: ${userActionKeys.mkString(", ")}")
      log.debug(s"Network view keys: ${networkViewKeys.mkString(", ")}")
      (userActionKeys, networkViewKeys)
    }
    ents
  }

  private[service] def createFilter(principalIdType: Option[IdType], networkId: Option[NetworkId],  subject: Option[String],
      attributeType: Option[AttributeType], requestedIds: Seq[String], requestedAttributeValues: Seq[String]): Try[MongoFilter] = {
    def toFilter(filter: String => MongoFilter, filterInputs: Seq[String]): Try[MongoFilter] = {
      if (filterInputs.nonEmpty) {
        Success(OrFilter(filterInputs.map(filter): _*))
      } else {
        Failure(HttpError.badRequest("No Ids were requested"))
      }
    }

    (principalIdType, attributeType) match {
      case (Some(_), Some(_)) => Failure(HttpError.badRequest("Cannot apply both ID and Attribute filters"))
      case (None, Some(attrType)) =>
        attrType match {
          case FaNumber if networkId.isEmpty => Failure(HttpError.badRequest("NetworkId should be defined if fa number is set"))
          case FaNumber => toFilter(id => FaNumberFilter(id, networkId.map(_.toString)), requestedAttributeValues)
          case ICapitalUserId => toFilter(id => ICapitalUserIdFilter(id), requestedAttributeValues)
        }
      case (idType, None) =>
        val f: Try[String => MongoFilter] = idType match {
          case Some(UserId) | None => Success(UserIdFilter)
          case Some(NPN) => Success(NpnFilter)
          case Some(Email) => Success(EmailFilter)
          case Some(Oms) => Success(OmsIdFilter)
          case Some(Distributor) =>
            if (networkId.isEmpty) Failure(HttpError.badRequest("NetworkId should be defined if distributor alias is set"))
            else Success(id => DistributorIdFilter(id, networkId.map(_.toString)))
          case Some(IdpId) => Success(IdpIdFilter)
          case Some(ExternalId) =>
            subject.toRight(HttpError.badRequest("Subject should be defined if external id is set")).toTry.map(s => {id => ExternalIdFilter(ExId(s, id))})
          case Some(SSN) => Failure(HttpError.badRequest(s"Invalid principalIdType: $principalIdType"))
        }
        f.flatMap(toFilter(_, requestedIds))
    }
  }

  private[service] def filterUserACLFields(user: UserACL, fieldsToInclude: Option[Set[UserAclField]]): UserACL = {

    def filterFields(user: UserACL, fieldsToInclude: Set[UserAclField]): UserACL = {
      val fieldsToExclude = UserAclField.Values.toSet -- fieldsToInclude

      fieldsToExclude.foldLeft(user) { (user, field) =>
        // required fields (i.e. non-optional or not a collections) won't be excluded
        field match {
          case UserAclField.userId => user
          case UserAclField.networkId => user
          case UserAclField.lastVisitedAt => user.copy(lastVisitedAt = None)
          case UserAclField.email => user
          case UserAclField.firstName => user
          case UserAclField.lastName => user
          case UserAclField.roles => user.copy(roles = Set.empty)
          case UserAclField.distributorId => user.copy(distributorId = None)
          case UserAclField.omsId => user.copy(omsId = None)
          case UserAclField.tradewebEligible => user
          case UserAclField.regSEligible => user
          case UserAclField.isActive => user
          case UserAclField.userPurviewIds => user.copy(userPurviewIds = Set.empty)
          case UserAclField.issuerPurviewIds => user.copy(issuerPurviewIds = Set.empty)
          case UserAclField.purviews => user.copy(purviews = None)
          case UserAclField.approverMap => user.copy(approverMap = ApproverMap.empty)
          case UserAclField.dynamicRoles => user.copy(dynamicRoles = Set.empty)
          case UserAclField.locations => user.copy(locations = Set.empty)
          case UserAclField.faNumbers => user.copy(faNumbers = Set.empty)
          case UserAclField.custodianFaNumbers => user.copy(custodianFaNumbers = Set.empty)
          case UserAclField.customRoles => user.copy(customRoles = Set.empty)
          case UserAclField.capabilities => user.copy(capabilities = Set.empty)
          case UserAclField.payoffEntitlements => user.copy(payoffEntitlements = Map.empty)
          case UserAclField.payoffEntitlementsV2 => user.copy(payoffEntitlementsV2 = Map.empty)
          case UserAclField.networkInfo => user.copy(networkInfo = NetworkP.Info.empty)
          case UserAclField.networkLocationHierarchy => user.copy(networkLocationHierarchy = None)
          case UserAclField.maskedIds => user.copy(maskedIds = Set.empty)
          case UserAclField.ioiApproverSet => user.copy(ioiApproverSet = Map.empty)
          case UserAclField.networkTypes => user.copy(networkTypes = None)
          case UserAclField.learnTracks => user.copy(learnTracks = Seq.empty)
          case UserAclField.learnTracksV2 => user.copy(learnTracksV2 = Seq.empty)
          case UserAclField.learnContent => user.copy(learnContent = Seq.empty)
          case UserAclField.endUserShareableContent => user.copy(endUserShareableContent = Seq.empty)
          case UserAclField.licenses => user.copy(licenses = Set.empty)
          case UserAclField.siCertificationRequirements => user.copy(siCertificationRequirements = Seq.empty)
          case UserAclField.annuityCertificationRequirements => user.copy(annuityCertificationRequirements = Seq.empty)
          case UserAclField.definedOutcomeETFCertificationRequirements => user.copy(definedOutcomeETFCertificationRequirements = Seq.empty)
          case UserAclField.certificationProducts => user.copy(certificationProducts = None)
          case UserAclField.accountInContext => user.copy(accountInContext = None)
          case UserAclField.context => user.copy(context = None)
          case UserAclField.cusips => user.copy(cusips = Set.empty)
          case UserAclField.group => user.copy(group = None)
          case UserAclField.networkCapabilities => user.copy(networkCapabilities = None)
          case UserAclField.smaStrategiesAndUnderliers => user.copy(smaStrategiesAndUnderliers = None)
          case UserAclField.smaRestrictedIssuers => user.copy(smaRestrictedIssuers = None)
          case UserAclField.payoffCertificationRequirementsList => user.copy(payoffCertificationRequirementsList = Seq.empty)
          case UserAclField.spCertificationRequirements => user.copy(spCertificationRequirements = Seq.empty)
          case UserAclField.videoTracksEntitlements => user.copy(videoTracksEntitlements = Map.empty)
          case UserAclField.altCertificationRequirements => user.copy(altCertificationRequirements = None)
          case UserAclField.externalIds => user.copy(externalIds = None)
          case UserAclField.purviewLicenses => user.copy(purviewLicenses = None)
          case UserAclField.purviewNsccCodes => user.copy(purviewNsccCodes = None)
          case UserAclField.firmId => user.copy(firmId = None)
          case UserAclField.whiteLabelPartnerId => user.copy(whiteLabelPartnerId = None)
          case UserAclField.secondaryEmail => user.copy(secondaryEmail = None)
          case UserAclField.iCapitalUserId => user.copy(iCapitalUserId = None)
          case UserAclField.groups => user.copy(groups = None)
          case UserAclField.icnGroups => user.copy(icnGroups = None)
          case UserAclField.icnRoles => user.copy(icnRoles = None)
          case UserAclField.passport => user.copy(passport = None)
          case UserAclField.productTypeCertificationRequirements => user.copy(productTypeCertificationRequirements = None)
        }
      }
    }

    fieldsToInclude.fold(user)(filterFields(user, _))
  }
}
