package com.simonmarkets.useracl.service

import com.simonmarkets.capabilities.{APIRegisteredCapabilities, Capabilities, Capability}
import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.useracl.api.{APIRegisteredCapability, CapabilitiesByDomain}

trait CapabilitiesService {
  def getCapabilityNamesByDomain: Map[String, Seq[String]]

  def getCapabilityDetailsByDomain: Map[String, Seq[Capability]]

  def getApiRegisteredCapabilitiesByDomain(domains: Seq[String]): Seq[CapabilitiesByDomain]
}

class CapabilitiesServiceImpl(capabilitiesByDomain: Map[String, Capabilities]) extends CapabilitiesService with TraceLogging {
  override def getCapabilityNamesByDomain: Map[String, Seq[String]] = {
    capabilitiesByDomain.mapValues(_.toSet.toSeq.sorted)
  }

  override def getCapabilityDetailsByDomain: Map[String, Seq[Capability]] = {
    capabilitiesByDomain.mapValues(_.toDetailedCapabilitySet.toSeq)
  }

  override def getApiRegisteredCapabilitiesByDomain(domains: Seq[String]): Seq[CapabilitiesByDomain] = {
    capabilitiesByDomain.collect {
      case (domain, capabilities: APIRegisteredCapabilities) if domains.contains(domain) =>
        val viewCapabilities = capabilities.DetailedViewCapabilities.map { viewCapability =>
          APIRegisteredCapability(viewCapability, capabilities.availableAccessKeyBuilders.get(viewCapability.name))
        }

        val editCapabilities = capabilities.DetailedEditCapabilities.map { editCapability =>
          APIRegisteredCapability(editCapability, capabilities.availableAccessKeyBuilders.get(editCapability.name))
        }

        CapabilitiesByDomain(domain, viewCapabilities, editCapabilities)
    }.toSeq
  }
}