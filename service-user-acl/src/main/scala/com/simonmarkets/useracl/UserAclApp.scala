package com.simonmarkets.useracl

import akka.http.scaladsl.Http
import akka.http.scaladsl.server.RouteConcatenation._
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.api.users.HttpACLClient
import com.simonmarkets.api.users.cache.dynamodb.DynamoDbUserAclRepository
import com.simonmarkets.capabilities.CustomRoleCapabilities
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.logging.TraceId
import com.simonmarkets.mongodb.Client
import com.simonmarkets.networks.common.domain.NetworkVisuals
import com.simonmarkets.networks.common.repository.MongoNetworkRepository
import com.simonmarkets.networks.common.repository.mongo.MongoNetworkVisualsRepository
import com.simonmarkets.networks.common.service.NetworkVisualsService
import com.simonmarkets.networks.common.service.networkservice.BasicNetworkService
import com.simonmarkets.resteasy.authn.User
import com.simonmarkets.resteasy.framework.{Environment, RestEasyModule}
import com.simonmarkets.ui.assets.utils.PathHandler
import com.simonmarkets.useracl.api.GetUserAclRequest
import com.simonmarkets.useracl.client.IcnHttpClient
import com.simonmarkets.useracl.config.UserACLAppConfiguration._
import com.simonmarkets.useracl.config.{DbConfiguration, UserACLAppConfiguration}
import com.simonmarkets.useracl.repository.PaidFeaturesRepositoryImpl
import com.simonmarkets.useracl.service.{CapabilitiesServiceImpl, PaidFeaturesServiceImpl, UserAclService, UserProfileService}
import com.simonmarkets.users.repository.MongoUserRepository
import com.simonmarkets.users.service.MasterUserService
import io.simon.openapi.generator.OpenApiGenerator
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.{Document, MongoClient, MongoCollection}
import pureconfig.generic.auto._

import scala.concurrent.Future


object UserAclApp extends RestEasyModule[UserACLAppConfiguration] with App {

  override def serviceName = "UserAclApp"

  OpenApiGenerator.generateOpenApiDocumentation()

  override def init(environment: Environment): Unit = {
    log.info("Creating mongo client")

    val mongoClient = Client.create(config.mongoDB.client)

    val userCollection: MongoCollection[Document] = getCollection(mongoClient, config.mongoDB.users)
    val usersSnapshotsCollection: MongoCollection[Document] = getCollection(mongoClient, config.mongoDB.usersSnapshots)
    val networkCollection: MongoCollection[Document] = getCollection(mongoClient, config.mongoDB.networks)
    val networkVisualsCollection: MongoCollection[NetworkVisuals] = {
      mongoClient
        .getDatabase(config.mongoDB.networkVisuals.database)
        .withCodecRegistry(MongoNetworkVisualsRepository.registry)
        .getCollection[NetworkVisuals](config.mongoDB.networkVisuals.collection)
    }
    val networksSnapshotsCollection: MongoCollection[Document] = getCollection(mongoClient, config.mongoDB.networksSnapshots)
    val paidFeaturesCollection: MongoCollection[Document] = getCollection(mongoClient, config.mongoDB.userPaidFeatures)
    val paidFeaturesSnapshotCollection: MongoCollection[Document] = getCollection(mongoClient, config.mongoDB.userPaidFeaturesSnapshots)
    val userRepository = new MongoUserRepository(mongoClient, entityCollection = userCollection,
      entitySnapshotCollection = usersSnapshotsCollection)
    val networkRepository = new MongoNetworkRepository(networkCollection, networksSnapshotsCollection, mongoClient)
    val networkVisualsRepository = new MongoNetworkVisualsRepository(networkVisualsCollection)
    val paidFeaturesRepository = new PaidFeaturesRepositoryImpl(mongoClient, paidFeaturesCollection, paidFeaturesSnapshotCollection)
    val aclRepo = new DynamoDbUserAclRepository(config.dynamoDbConfig)

    val uiAssetsPathHandler = new PathHandler(config.cloudfrontURLConfig)

    val aclService = new UserAclService(userRepository, networkRepository, aclRepo, config.serviceConfig)
    val networkService = new BasicNetworkService(networkRepository)
    val networkVisualsService = new NetworkVisualsService(networkVisualsRepository, uiAssetsPathHandler)
    val masterUserService = new MasterUserService(userRepository, networkRepository, List.empty)
    val futureHttpClient: FutureHttpClient = new FutureHttpClient(Http(), config.httpClientConfig)
    val paidFeatureHttpClient: FutureHttpClient = new FutureHttpClient(Http(), config.aclClient.httpClient)
    val aclClient = new HttpACLClient(paidFeatureHttpClient, config.aclClient.baseUrl)
    val paidFeaturesService = new PaidFeaturesServiceImpl(paidFeaturesRepository, aclClient)
    val icnClient = new IcnHttpClient(futureHttpClient, config.icnConfig)
    val profileService = new UserProfileService(config.simonConfig, networkService, networkVisualsService, userRepository, masterUserService, paidFeaturesService, icnClient)
    val userAclSupplier: User => TraceId => Future[UserACL] = user => tid => {
      implicit val traceId: TraceId = tid
      aclRepo.get(user.userId).map(Future.successful).getOrElse(aclService.getUserAcl(config.systemUserId, GetUserAclRequest.byId(user.userId)))
    }
    val capabilitiesService = new CapabilitiesServiceImpl(CustomRoleCapabilities.capabilitiesByDomain)

    val userACLDirective = UserAclAuthorizedDirective(userAclSupplier)

    val aclRoutes = UserAclServiceRoutes(aclService, userACLDirective).routes
    val profileRoutes = UserProfileRoutes(profileService, userACLDirective).routes
    val capabilitiesRoute = new CapabilitiesRoutes(userACLDirective, capabilitiesService).routes
    val paidFeaturesRoute = PaidFeaturesRoutes(paidFeaturesService, userACLDirective).routes

    val routes = aclRoutes ~ profileRoutes ~ capabilitiesRoute ~ paidFeaturesRoute

    environment.addRoutes(routes)
  }

  private def getCollection(mongoClient: MongoClient, config: DbConfiguration): MongoCollection[Document] = {
    mongoClient
      .getDatabase(config.database)
      .withCodecRegistry(DEFAULT_CODEC_REGISTRY)
      .getCollection[Document](config.collection)
  }

  RestEasy.start()

  override def servicePath: String = "simon/api/v2/principals"
}
