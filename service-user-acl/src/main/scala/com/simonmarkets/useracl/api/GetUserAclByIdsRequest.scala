package com.simonmarkets.useracl.api

import com.goldmansachs.marquee.pipg.UserAclField
import io.simon.openapi.annotation.ClassReference
import io.simon.openapi.annotation.Field.{EnumValues, TypeArgRef}

case class GetUserAclByIdsRequest(
    ids: Set[String],
    @TypeArgRef(ClassReference(classOf[UserAclField]))
    fields: Option[Set[UserAclField]] = None,
    @EnumValues("view", "impersonate")
    action: Option[String] = None
)
