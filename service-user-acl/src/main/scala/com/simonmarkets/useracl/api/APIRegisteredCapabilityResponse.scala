package com.simonmarkets.useracl.api

import com.simonmarkets.capabilities.Capability

case class APIRegisteredCapability(name: String, assetClasses: Option[Seq[String]], description: String, availableAccessKeyBuilder: Option[String])

object APIRegisteredCapability {
  def apply(capability: Capability, builder: Option[String]): APIRegisteredCapability = {
    new APIRegisteredCapability(capability.name, capability.assetClass, capability.description, builder)
  }
}

case class CapabilitiesByDomain(domain: String, viewCapabilities: Set[APIRegisteredCapability], editCapabilities: Set[APIRegisteredCapability])

case class APIRegisteredCapabilityResponse(capabilities: Seq[CapabilitiesByDomain])
