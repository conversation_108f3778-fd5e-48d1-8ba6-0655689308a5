package com.simonmarkets.useracl.api.codec

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.api.users.cache.dynamodb.CachedUserAcl
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.useracl.api.{CacheSyncRequest, GetUserAclByIdsRequest, UserAclQueryRequest}
import io.circe.Codec
import io.circe.generic.extras.semiauto.deriveConfiguredCodec
import io.circe.generic.semiauto.deriveCodec
import io.circe.generic.extras.Configuration

trait UserAclServiceCodecs extends JsonCodecs {

  implicit lazy val userACLCodec: Codec[UserACL] = deriveCodec

  implicit lazy val cachedUserAclCodec: Codec[CachedUserAcl] = deriveCodec

  implicit lazy val cacheSyncRequestCodec: Codec[CacheSyncRequest] = deriveCodec

  implicit lazy val getUserAclByIdsRequestCodec: Codec[GetUserAclByIdsRequest] = deriveCodec

  implicit lazy val userAclQueryRequestCodec: Codec[UserAclQueryRequest] = deriveConfiguredCodec[UserAclQueryRequest]

  implicit val config: Configuration = Configuration.default.withDefaults
}
