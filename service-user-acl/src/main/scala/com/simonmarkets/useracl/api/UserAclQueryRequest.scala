package com.simonmarkets.useracl.api

import com.goldmansachs.marquee.pipg.UserAclField
import com.simonmarkets.users.common.{AttributeType, IdType}
import io.simon.openapi.annotation.ClassReference
import io.simon.openapi.annotation.Field.{EnumValues, Ref, TypeArgRef}
import io.simon.openapi.definitions.CommonDefinitions
import simon.Id.NetworkId

case class UserAclQueryRequest(
    ids: Set[String] = Set.empty,
    from: Option[String] = None,
    @Ref(ClassReference(classOf[IdType]))
    principalIdType: Option[IdType] = None,
    @Ref(CommonDefinitions.NetworkName)
    networkId: Option[NetworkId] = None,
    subject: Option[String] = None,
    @TypeArgRef(ClassReference(classOf[UserAclField]))
    fields: Option[Set[UserAclField]] = None,
    @EnumValues("view", "impersonate")
    action: Option[String] = None,
    @Ref(ClassReference(classOf[AttributeType]))
    attributeType: Option[AttributeType] = None,
    attributeValues: Set[String] = Set.empty
)