package com.simonmarkets.useracl.api

import com.goldmansachs.marquee.pipg.UserAclField
import com.simonmarkets.users.common.IdType
import simon.Id.NetworkId

case class GetUserAclRequest(
    id: String,
    from: Option[String],
    principalIdType: Option[IdType],
    networkId: Option[NetworkId],
    subject: Option[String],
    fields: Option[Set[UserAclField]]
)

object GetUserAclRequest {

  def byId(id: String): GetUserAclRequest = GetUserAclRequest(id, None, None, None, None, None)
}
