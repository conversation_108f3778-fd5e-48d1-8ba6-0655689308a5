package com.simonmarkets.useracl.repository

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.PaidFeaturesCapabilities
import com.simonmarkets.capabilities.PaidFeaturesCapabilities.{UploadCapabilities, ViewUserPaidFeatureCapabilities}
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.mongodb.bson.{MacrosFormat, SnapshotFormat}
import com.simonmarkets.mongodb.snapshots.SnapshotableRepository
import com.simonmarkets.mongodb.snapshots.SnapshotableRepository.RecoverFuture
import com.simonmarkets.useracl.api.PaidFeaturesRequest
import com.simonmarkets.useracl.domain.{IdType, PaidFeatures, PaidRole}
import com.simonmarkets.users.repository.UserMongoJsonCodecs
import org.mongodb.scala.bson.collection.immutable.Document
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.Updates._
import org.mongodb.scala.{MongoClient, MongoCollection}

import scala.concurrent.{ExecutionContext, Future}

trait PaidFeaturesRepository {

  def findById(id: String, idType: IdType)(implicit traceId: TraceId, requester: UserACL): Future[Option[PaidFeatures]]

  def updatePaidFeatures(paidFeaturesRequest: PaidFeaturesRequest, existingUser: PaidFeatures)
    (implicit traceId: TraceId, requester: UserACL): Future[PaidFeatures]

  def insertPaidFeatures(paidFeaturesRequest: PaidFeaturesRequest, idType: IdType, networkId: Option[String] = None)
    (implicit traceId: TraceId, requester: UserACL): Future[PaidFeatures]
}

class PaidFeaturesRepositoryImpl(
    val client: MongoClient,
    val entityCollection: MongoCollection[Document],
    val entitySnapshotCollection: MongoCollection[Document],
)(implicit
    val ec: ExecutionContext) extends PaidFeaturesRepository with SnapshotableRepository[PaidFeatures] with UserMongoJsonCodecs with TraceLogging {

  implicit val paidFeaturesFormat: MacrosFormat[PaidFeatures] = PaidFeaturesFormat
  implicit val paidFeaturesSnapshotFormat: SnapshotFormat[PaidFeatures] = new SnapshotFormat[PaidFeatures](paidFeaturesFormat)

  private val networkIdFieldName = "networkId"
  private val productFieldName = "product"
  private val roleSourceFieldName = "roleSource"
  private val rolesFieldName = "roles"
  private val idFieldName = "id"
  private val idTypeFieldName = "idType"
  private val acceptedAccessKeysFieldName = "acceptedAccessKeys"

  override def findById(id: String, idType: IdType)
    (implicit traceId: TraceId, requester: UserACL): Future[Option[PaidFeatures]] = {
    val availableAccessKeys = PaidFeaturesCapabilities.getAvailableAccessKeysForCapabilities(ViewUserPaidFeatureCapabilities, requester)

    entityCollection.find(
      and(
        equal(idFieldName, id),
        equal(idTypeFieldName, idType.toString),
        in(acceptedAccessKeysFieldName, availableAccessKeys.toSeq: _*)
      )).limit(1).headOption.map(_.map(PaidFeaturesFormat.read))
  }

  override def updatePaidFeatures(paidFeaturesRequest: PaidFeaturesRequest, existingPaidFeature: PaidFeatures)
    (implicit traceId: TraceId, requester: UserACL): Future[PaidFeatures] = {
    import MongoPaidFeaturesRepository.{ExtId, IncVer}
    if (hasUploadAccess(requester, existingPaidFeature)) {
      val updatedPaidFeature: PaidFeatures = existingPaidFeature.copy(
        roles = paidFeaturesRequest.roles
      )
      update(requester.userId, updatedPaidFeature, None)
    } else {
      Future.failed(HttpError.unauthorized(s"User does not have right access keys to update paidFeatures: ${requester.userId}"))
    }
  }

  override def insertPaidFeatures(
      paidFeaturesRequest: PaidFeaturesRequest,
      idType: IdType,
      networkId: Option[String] = None
  )
    (implicit traceId: TraceId, requester: UserACL): Future[PaidFeatures] = {
    val entity: PaidFeatures = PaidFeatures(
      idType = idType,
      id = paidFeaturesRequest.id,
      roles = paidFeaturesRequest.roles,
      acceptedAccessKeys = Set(),
      networkId = networkId
    )
    val entityWithAccessKeys = entity.copy(acceptedAccessKeys = PaidFeatureKeyGenerator.getAcceptedAccessKeys(entity))
    if (hasUploadAccess(requester, entityWithAccessKeys)) {
      insert(requester.userId, entityWithAccessKeys, None).refineDuplicateKey
    } else {
      Future.failed(HttpError.unauthorized(s"User does not have right access keys to insert paidFeatures: ${requester.userId}"))
    }
  }

  def hasUploadAccess(requester: UserACL, paidFeatures: PaidFeatures): Boolean = {
    val availableAccessKeys = PaidFeaturesCapabilities.getAvailableAccessKeysForCapabilities(UploadCapabilities, requester)
    val acceptedAccessKeys = PaidFeatureKeyGenerator.getAcceptedAccessKeys(paidFeatures)
    availableAccessKeys.intersect(acceptedAccessKeys).nonEmpty
  }

  def removeUserPaidRoles(networkId: String, paidRoles: List[PaidRole])
    (implicit traceId: TraceId): List[Future[Long]] = {
    val updateFilter = and(
      equal(networkIdFieldName, networkId)
    )
    paidRoles.map(paidRole => {
      val roleToRemove = Document(productFieldName -> paidRole.product.toString, roleSourceFieldName -> paidRole.roleSource.toString)
      val updateOperation = pull(rolesFieldName, roleToRemove)

      entityCollection.updateMany(updateFilter, updateOperation).toFuture().map { result =>
        val modifiedCount = result.getModifiedCount
        log.info(s"[removeUserPaidRoles] Modified Count: $modifiedCount", paidRole, networkId)
        modifiedCount
      }
    })
  }
}