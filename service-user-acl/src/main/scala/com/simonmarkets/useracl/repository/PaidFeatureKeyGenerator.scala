package com.simonmarkets.useracl.repository

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.PaidFeaturesCapabilities.{EditUserPaidFeatureViaNetwork, EditUserPaidFeatureViaOwner, ViewUserPaidFeatureViaNetwork, ViewUserPaidFeatureViaOwner}
import com.simonmarkets.entitlements.{AcceptedAccessKeysGenerator, AcceptedKeyBuilder}
import com.simonmarkets.useracl.domain.PaidFeatures

object PaidFeatureKeyGenerator extends AcceptedAccessKeysGenerator[PaidFeatures] {

  override def capabilityToAcceptedKeyBuilders: Map[String, AcceptedKeyBuilder[PaidFeatures]] = Map(
    Admin -> AcceptedKeyBuilder(buildAdminKeys),
    ViewUserPaidFeatureViaOwner -> AcceptedKeyBuilder(buildPaidFeaturesIdKeys),
    EditUserPaidFeatureViaOwner -> AcceptedKeyBuilder(buildPaidFeaturesIdKeys),
    ViewUserPaidFeatureViaNetwork -> AcceptedKeyBuilder(buildPaidFeaturesViaNetworkKeys),
    EditUserPaidFeatureViaNetwork -> AcceptedKeyBuilder(buildPaidFeaturesViaNetworkKeys)
  )

  def buildPaidFeaturesIdKeys(capability: String, paidFeatures: PaidFeatures): Set[String] = {
    Set(s"$capability:${paidFeatures.id}")
  }

  def buildPaidFeaturesViaNetworkKeys(capability: String, paidFeatures: PaidFeatures): Set[String] = {
    val networksId = paidFeatures.networkId.getOrElse(paidFeatures.id)
    Set(s"$capability:$networksId")
  }
}