package com.simonmarkets.useracl.repository


import com.simonmarkets.mongodb.bson.MacrosFormat
import com.simonmarkets.mongodb.codec.EnumEntryCodecProvider
import com.simonmarkets.mongodb.snapshots.{ExtractId, IncrementVersion}
import com.simonmarkets.useracl.domain.{ICapitalProduct, IdType, PaidFeatures, PaidRole, RoleSource}
import org.bson.codecs.Codec
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient
import org.mongodb.scala.bson.codecs.Macros

object PaidFeaturesFormat extends MacrosFormat[PaidFeatures] {
  override protected def entityCodecProvider: Codec[PaidFeatures] = Macros.createCodec[PaidFeatures](codecRegistry)

  lazy val codecRegistry: CodecRegistry = fromRegistries(
    MongoClient.DEFAULT_CODEC_REGISTRY,
    fromProviders(
      Macros.createCodecProviderIgnoreNone[PaidFeatures](),
      Macros.createCodecProviderIgnoreNone[PaidRole](),
      EnumEntryCodecProvider[ICapitalProduct],
      EnumEntryCodecProvider[RoleSource],
      EnumEntryCodecProvider[IdType]
    ))
}

object MongoPaidFeaturesRepository {

  implicit val IncVer: IncrementVersion[PaidFeatures] = (x: PaidFeatures) => x.copy(version = x.version + 1)

  implicit val ExtId: ExtractId[PaidFeatures] = new ExtractId[PaidFeatures] {
    type Id = String

    def id(x: PaidFeatures): String = x.id
  }
}
