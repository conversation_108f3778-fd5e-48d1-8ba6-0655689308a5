package com.simonmarkets.useracl

import akka.http.scaladsl.model.headers.OAuth2BearerToken
import akka.http.scaladsl.server.Route
import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.useracl.api.codec.UserProfileCodecs
import com.simonmarkets.useracl.service.UserProfileService

case class UserProfileRoutes(
    userProfileService: UserProfileService,
    authorizationDirective: UserAclAuthorizedDirective)
  extends DirectivesWithCirce
    with UserProfileCodecs
    with TraceLogging
    with UserAclAuthorizedDirectives {

  lazy val routes: Route = v1Route ~ v2Route

  private val v1Route: Route = pathPrefix("simon" / "api" / "v1" / "my-profile"){
    authorizedUser { (_traceId, userImpersonator, _userACL) =>
      implicit val (traceId, userAcl) = (_traceId, _userACL)
      pathEnd {
        get {
          complete(userProfileService.getOwnUserProfile(userImpersonator.impersonatorUserId))
        }
      }
    }
  }

  private val v2Route: Route = pathPrefix("simon" / "api" / "v2" / "my-profile"){
    authorizedUser { (_traceId, userImpersonator, _userACL) =>
      implicit val (traceId, token, userAcl) = (_traceId, OAuth2BearerToken(userImpersonator.token), _userACL)
      pathEnd {
        get {
          complete(userProfileService.getUnifiedUserProfile(userImpersonator.impersonatorUserId))
        }
      }
    }
  }
}
