package com.simonmarkets.useracl.domain

import com.simonmarkets.mongodb.snapshots.Snapshotable
import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field._
import io.simon.openapi.annotation.ClassReference

@EnumValues("Manual", "Stripe")
sealed trait RoleSource extends EnumEntry

object RoleSource extends ProductEnums[RoleSource] {
  case object Manual extends RoleSource
  case object Stripe extends RoleSource
  override def Values: Seq[RoleSource] = Seq(Manual, Stripe)
}

@EnumValues("Architect")
sealed trait ICapitalProduct extends EnumEntry

object ICapitalProduct extends ProductEnums[ICapitalProduct] {
  case object Architect extends ICapitalProduct

  override def Values: Seq[ICapitalProduct] = Seq(Architect)
}

@EnumValues("UserId", "NetworkId")
sealed trait IdType extends EnumEntry

object IdType extends ProductEnums[IdType] {
  case object UserId extends IdType
  case object NetworkId extends IdType

  override def Values: Seq[IdType] = Seq(UserId, NetworkId)
}

case class PaidRole(
    @Required
    @Ref(ClassReference(classOf[ICapitalProduct]))
    product: ICapitalProduct,
    @Required
    @Ref(ClassReference(classOf[RoleSource]))
    roleSource: RoleSource
)

case class PaidFeatures(
    idType: IdType,
    id: String,
    roles: List[PaidRole],
    acceptedAccessKeys: Set[String],
    version: Int = 0,
    networkId: Option[String] = None,
) extends Snapshotable