package com.simonmarkets.useracl.domain

import com.goldmansachs.marquee.pipg.{ContactInfo2, Network, NetworkCategory, NetworkType, PartnerUrl}
import com.simonmarkets.capabilities.AssetClassToUICapabilities
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.common.domain.Visuals
import com.simonmarkets.networks.enums.DomicileCode
import com.simonmarkets.shared.MaskedId
import com.simonmarkets.users.common.Context
import com.simonmarkets.users.service.UserRef

case class UserProfile(
    userId: String,
    network: Network.Info,
    firstName: String,
    lastName: String,
    fullName: String,
    email: String,
    humanReadableRole: String,
    capabilities: Map[String, Boolean],
    comparisonValue: String,
    tradewebId: Option[String],
    roles: Set[String],
    showLearningCenterLinksForIssuers: Set[String],
    maskedIds: MaskedIds,
    category: Option[NetworkCategory] = None,
    accountInContext: Option[String] = None,
    context: Option[Context] = None,
    networkTypes: Option[List[NetworkType]] = None,
    partnerUrls: Set[PartnerUrl] = Set.empty,
    hasEndClientSharing: Boolean,
    otherProfiles: List[UserRef] = List.empty,
    networkContactInfo2: Option[List[ContactInfo2]] = None,
    networkContactInfo2Name: Option[String] = None,
    uiViewCardOverrides: Map[String, List[String]] = Map.empty,
    uiViewCardOverridesExpiryDate: Map[String, String] = Map.empty,
    assetClassUICapabilityMap: AssetClassToUICapabilities,
    historicHoldingsStartFrom: Option[String] = None,
    cobrandingCustomDisclosure: Option[String] = None,
    visuals: Option[Visuals] = None,
    sessionInactivityTimeout: Option[Int] = None,
    paidRoles: Seq[ICapitalProduct] = Seq.empty,
    passport: Map[String, Int] = Map.empty,
    domiciles: Option[Set[DomicileCode]] = None,
    wlpUrl: Option[String] = None,
    networkCapabilities: Option[Map[String, List[String]]] = None,
)

case class MaskedIds(userIds: Set[MaskedId], networkIds: Set[ExternalId])
