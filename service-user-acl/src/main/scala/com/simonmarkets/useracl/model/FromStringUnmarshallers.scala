package com.simonmarkets.useracl.model

import akka.http.scaladsl.unmarshalling.PredefinedFromStringUnmarshallers.CsvSeq
import akka.http.scaladsl.unmarshalling.{FromStringUnmarshaller, Unmarshaller}
import com.goldmansachs.marquee.pipg.UserAclField

object FromStringUnmarshallers {

  implicit val userACLFieldFromStringUnmarshaller: FromStringUnmarshaller[UserAclField] =
    Unmarshaller.strict[String, UserAclField](UserAclField.apply)

  implicit def CsvSet[T](implicit unmarshaller: FromStringUnmarshaller[T]): FromStringUnmarshaller[Set[T]] =
    CsvSeq[T].map(_.toSet)

}
