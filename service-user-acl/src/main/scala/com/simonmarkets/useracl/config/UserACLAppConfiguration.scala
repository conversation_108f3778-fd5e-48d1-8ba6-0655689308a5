package com.simonmarkets.useracl.config

import com.simonmarkets.api.users.config.AclClientConfig
import com.simonmarkets.dynamodb.config.DynamoDbConfiguration
import com.simonmarkets.http.HttpClientConfig
import com.simonmarkets.mongodb.config.MongoClientConfig
import com.simonmarkets.resteasy.framework.RunMode
import com.simonmarkets.resteasy.framework.config.RestEasyAppConfiguration
import com.simonmarkets.resteasy.framework.config.RestEasyAppConfiguration.SystemRoutesConfig
import com.simonmarkets.resteasy.framework.config.authn.AuthenticationConfiguration
import com.simonmarkets.resteasy.framework.info.InfoConfig
import com.simonmarkets.ui.assets.utils.config.CloudfrontURLConfig
import pureconfig._
import pureconfig.generic.ProductHint

case class UserACLAppConfiguration(
    authentication: Option[AuthenticationConfiguration],
    runMode: RunMode,
    mongoDB: MongoConfiguration,
    dynamoDbConfig: DynamoDbConfiguration,
    serviceConfig: AclServiceConfiguration,
    systemRoutes: Option[SystemRoutesConfig],
    simonConfig: SimonConfig,
    systemUserId: String,
    cloudfrontURLConfig: CloudfrontURLConfig,
    info: Option[InfoConfig] = None,
    httpClientConfig: HttpClientConfig,
    icnConfig: IcnConfig,
    serverPath: String,
    aclClient: AclClientConfig
) extends RestEasyAppConfiguration

case class MongoConfiguration(
    users: DbConfiguration,
    usersSnapshots: DbConfiguration,
    networks: DbConfiguration,
    networkVisuals: DbConfiguration,
    networksSnapshots: DbConfiguration,
    client: MongoClientConfig,
    userPaidFeatures: DbConfiguration,
    userPaidFeaturesSnapshots: DbConfiguration
)

case class DbConfiguration(
    collection: String,
    database: String
)

case class AclServiceConfiguration(
    serviceEntitlements: Set[String]
)

case class SimonConfig(issuers: IssuerConfig)

case class IssuerConfig(learningCenterEnabled: Option[Set[String]])

case class IcnConfig(
    simonServerPath: String,
    host: String
)

object UserACLAppConfiguration {
  implicit def hint[T]: ProductHint[T] = ProductHint(ConfigFieldMapping(CamelCase, KebabCase))
}
