package com.simonmarkets.useracl

import akka.http.scaladsl.server.Route
import com.simonmarkets.capabilities.SimonUICapabilities
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.useracl.api.APIRegisteredCapabilityResponse
import com.simonmarkets.useracl.service.CapabilitiesService
import io.circe.generic.auto._

class CapabilitiesRoutes(val authorizationDirective: UserAclAuthorizedDirective, capabilitiesService: CapabilitiesService)
  extends DirectivesWithCirce with TraceLogging with UserAclAuthorizedDirectives {

  lazy val routes: Route = v1Route ~ capabilitiesStructureRoute ~ v2Route

  private val v1Route = pathPrefix("simon" / "api" / "v1" / "capabilities") {
    authorizationDirective.authorized(SimonUICapabilities.ViewUIOnboardingToolCapability.name) { (_traceId, _) =>
      implicit val traceId: TraceId = _traceId
      get {
        log.info("User has viewUIOnboardingTool capability; returning capabilities by domain")
        complete(capabilitiesService.getCapabilityNamesByDomain)
      }
    }
  }

  private val v2Route = pathPrefix("simon" / "api" / "v2" / "capabilities") {
    authorizationDirective.authorized(SimonUICapabilities.ViewUIOnboardingToolCapability.name) { (_traceId, _) =>
      implicit val traceId: TraceId = _traceId
      get {
        log.info("User has viewUIOnboardingTool capability; returning capabilities by domain")
        complete(capabilitiesService.getCapabilityDetailsByDomain)
      }
    }
  }

  private val capabilitiesStructureRoute = pathPrefix("simon" / "api" / "v2" / "capabilities" / "structure") {
    authorizationDirective.authorized() { (_traceId, _) =>
      implicit val traceId: TraceId = _traceId
      get {
        parameter("domain".repeated) { domains =>
          log.info("Getting capabilities for domains", "domains" -> domains.mkString(","))
          complete(APIRegisteredCapabilityResponse(capabilitiesService.getApiRegisteredCapabilitiesByDomain(domains.toSeq)))
        }
      }
    }
  }
}
