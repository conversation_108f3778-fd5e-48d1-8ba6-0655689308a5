package com.simonmarkets.useracl

import akka.http.scaladsl.server.{Directive0, Directive1, Route}
import com.goldmansachs.marquee.pipg.{UserACL, UserAclField}
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.useracl.api.codec.UserAclServiceCodecs
import com.simonmarkets.useracl.api.{CacheSyncRequest, GetUserAclByIdsRequest, GetUserAclRequest, UserAclQueryRequest}
import com.simonmarkets.useracl.model.FromStringUnmarshallers._
import com.simonmarkets.useracl.service.UserAclService
import com.simonmarkets.users.common.IdType

import simon.Id.NetworkId
import scala.concurrent.ExecutionContext

case class UserAclServiceRoutes(
    service: UserAclService,
    authorizationDirective: UserAclAuthorizedDirective)
  (implicit ec: ExecutionContext)
  extends DirectivesWithCirce
    with UserAclServiceCodecs
    with UserAclAuthorizedDirectives {

  // @formatter:off
  lazy val routes: Route =
    authorized { (trace: TraceId, requester: UserACL) =>
      implicit val traceId: TraceId = trace
        PRINCIPALS {
          `GET /:id` { userId =>
            parameters('from.?, 'principalIdType.?, 'networkId.?, 'subject.?, 'fields.as[Set[UserAclField]].?) {
              (from, principalIdType, networkId, subject, fields) => {
                val getUserAclRequest =
                  GetUserAclRequest(userId, from, principalIdType.map(IdType.apply), networkId.map(NetworkId(_)), subject, fields)
                complete(service.getUserAcl(requester.userId, getUserAclRequest))
              }
            }
          } ~
          `GET /:id/capabilities` { userId =>
            val getUserAclRequest = GetUserAclRequest.byId(userId)
            complete(service.getUserAcl(requester.userId, getUserAclRequest).map(_.capabilities))
          } ~
          `POST /` {
            entity(as[GetUserAclByIdsRequest]) { request =>
              val queryRequest = UserAclQueryRequest(request.ids, fields = request.fields, action = request.action)
              complete(service.getUserAcls(requester.userId, queryRequest))
            }
          } ~
          `POST /query` {
            entity(as[UserAclQueryRequest]) { request =>
              complete(service.getUserAcls(requester.userId, request))
            }
          } ~
          `GET /cache/:id` { userId =>
              complete(service.getCacheEntry(requester.userId, userId))
          } ~
          `GET /cache/:id/capabilities` { userId =>
              complete(service.getCacheEntry(requester.userId, userId).map {
                case Some(aclEntry) => Right(aclEntry.userACL.capabilities)
                case None => Left(HttpError.notFound(s"User ${userId} not found in cache").httpResponse)
              })
          } ~
          `POST /cache-sync` {
            entity(as[CacheSyncRequest]){ request =>
              service.syncAclCache(requester.userId, request)
              complete(s"the cache sync has started with traceId=$traceId")
            }
          }
        }
      }

  // @formatter:on

  val PRINCIPALS: Directive0 = pathPrefix("simon" / "api" / "v2" / "principals")

  def `GET /:id`: Directive1[String] = get & path(Segment) & pathEnd

  def `GET /:id/capabilities`: Directive1[String] = get & path(Segment / "capabilities") & pathEnd

  def `POST /`: Directive0 = post & pathEndOrSingleSlash

  def `POST /query`: Directive0 = post & path("query") & pathEnd

  def `GET /cache/:id`: Directive1[String] = get & path("cache" / Segment) & pathEnd

  def `GET /cache/:id/capabilities`: Directive1[String] = get & path("cache" / Segment / "capabilities") & pathEnd

  def `POST /cache-sync`: Directive0 = post & path("cache-sync") & pathEnd

}

