package com.simonmarkets.useracl.client

import akka.http.scaladsl.model.headers.{Authorization, Host, OAuth2BearerToken => AccessToken}
import akka.stream.Materializer
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.http.{FutureHttpClient, HttpError}
import com.simonmarkets.http.FutureHttpClient.Tag
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.useracl.config.IcnConfig
import io.circe.Json

import scala.concurrent.{ExecutionContext, Future}

trait IcnClient {
  def getUserProfile(implicit traceId: TraceId, token: AccessToken): Future[Json]
}

class IcnHttpClient(
    client: FutureHttpClient,
    icnConfig: IcnConfig
)(implicit ec: ExecutionContext, mat: Materializer) extends IcnClient with TraceLogging with JsonCodecs {

  implicit val tag: Tag = Tag("icn-client")

  def getUserProfile(implicit traceId: TraceId, token: AccessToken): Future[Json] = {
    val uri = s"${icnConfig.simonServerPath}/api/v1/users/angular_bootstrap_identity.json"
    log.info("Getting ICN user profile", uri)

    client.get[Json](
      uri,
      requestHeaders = List(Authorization(token), Host(icnConfig.host))
    ).recover {
      case error : HttpError => error.getMessage().asJson
    }
  }
}