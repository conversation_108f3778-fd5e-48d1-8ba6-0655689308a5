info {
  name = "User ACL Service"
  description = "A service responsible for returning User ACL"
  repository = "networks"
  module = "service-users-acl"
  version = "${project.version}"
  revision = "${git.commit.id.abbrev}"
  build-timestamp = "${build.timestamp.millis}"
  product-line = "CrossProduct"
  used-by = ["CrossProduct", "Storefront", "Spectrum", "SMA", "Annuity", "ETF", "StructuredInvestment"]
  owner = "CrossProduct"
  support-distro = ["<EMAIL>"]
  metrics-url = "",
  documentation-url = "https://simonmarkets.atlassian.net/wiki/spaces/ENG/pages/445349961/REST+Easy+Framework"
}

config.resolvers {
  sm {
    region = "us-east-1"
    credentialsProvider = "webIdentityToken"
  }
  file {
    root = "app-config/dev"
  }
}

run-mode {
  type = "server-mode"
  http-server-config {
    port = 443
    interface = "0.0.0.0"
    ssl {
      enabled = true
      keystore {
        type = "JKS"
        file = "/var/cv/creds/simon-vertx-ssl.jks"
        password = "sm:applicationconfig-simon-vertx-ssl-jks-pass"
      }
    }
  }
}

mongo-db {
  users {
    database = "pipg"
    collection = "users"
  }
  users-snapshots {
    database = "pipg"
    collection = "users.snapshots"
  }
  networks {
    database = "pipg"
    collection = "networks_new"
  }
  network-visuals {
    database = "pipg"
    collection = "network.visuals"
  }
  networks-snapshots {
    collection = "networks_new.snapshots"
    database = "pipg"
  }
  user-paid-features {
    collection = "users.paidFeatures"
    database = "pipg"
  }
  user-paid-features-snapshots {
    collection = "users.paidFeatures.snapshots"
    database = "pipg"
  }
  client {
    app-name = "service-user-acl"
    connection = "sm:config:applicationconfig-mongo-auth-pipg"
  }
}

system-routes {
  health-check {
    path = "simon/api/v2/principals/healthcheck"
  }
  service-up {
    path = "simon/api/v2/principals/uptime"
  }
  service-info {
    path = "simon/api/v2/principals/info"
  }
}
