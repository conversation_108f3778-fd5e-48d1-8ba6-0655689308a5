include classpath("service-user-acl-common.conf")

akka.http.server.request-timeout = "60.seconds"

authentication {
  type = "jwt"
  jwt {
    source {
      type = "cookieOrHeader"
      name = "SimSSO:X-Simon-AccessToken"
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clock-offset-in-seconds = 0
      proxy-host = "internal-prod-squid-elb-*********.us-east-1.elb.amazonaws.com"
      proxy-port = 3128
    }
  }
  user-info {
    source {
      type = "header"
      name = "x-userinfo"
    }
  }
}

dynamo-db-config {
  service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
  signin-region = "us-east-1"
}

service-config {
  service-entitlements = ["admin"]
}


simon-config {
  issuers {
    learning-center-enabled = ["GSGroup", "GSFC", "GSBank", "WFCo", "WFBank", "BCS", "BCSBank"]
  }
}

cloudfront-url-config {
  global-domain-name = "cdn.simon.io"
  domestic-domain-name = "cdn.simonmarkets.com"
  cloudfront-key-pair-id = "sm:cloudFrontKeyPairId"
  cloudfront-private-key = "sm:binary:cloudFrontPrivateKeyFile"
  expiration-millis = "********"
}

http-client-config {
  auth: {
     type = NoAuth
  }
  proxy {
    port: 3128
    address: "internal-prod-squid-elb-*********.us-east-1.elb.amazonaws.com"
  }
}

icn-config {
  simon-server-path = "https://origin-dc1.simonmarkets.com"
  host = "s.icapitalnetwork.com"
}

system-user-id = "c388df581a28404a94bbc9885c1650fd"

server-path = "https://origin-dc1.api.simonmarkets.com/simon/api"

acl-client {
  http-client {
    auth: {
      type = OAuth
      client-id = "0oa2h4re4ym0Fh3s82p7"
      client-secret-file = ""
      site = "sm:applicationconfig-issuer-uri"
      authorization-path = ""
      token-path = "/v1/token"
      scope = "read_product_data"
      token-type: {
        type: Cookie
        name: SimSSO
      }
      client-secret = "sm:applicationconfig-oktaclientsecret"
    }
    proxy {
      port: 3128
      address: "internal-prod-squid-elb-*********.us-east-1.elb.amazonaws.com"
    }
  }
  base-url = "https://origin-dc1.api.simonmarkets.com/simon/api"
  cache-config {
    enabled = true
    config {
      service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
      signin-region = "us-east-1"
    }
  }
}