openapi: 3.0.0
info:
  title: UserACL Service
  version: 1.0.0
x-basepath: /simon/api
x-kong-service-defaults:
  read_timeout: 600000
tags:
  - name: principals
    description: User ACl Service
paths:
  /v2/principals/info:
    get:
      x-scopes:
        - admin
        - simon-system-user
      tags:
        - principals
      summary: Retrieve service info
      responses:
        200:
          description: Success
  /v2/principals/healthcheck:
    get:
      x-scopes:
        - admin
        - simon-system-user
      tags:
        - principals
      summary: Retrieve service healthcheck
      responses:
        200:
          description: Success
  /v2/principals/uptime:
    get:
      x-scopes:
        - admin
        - simon-system-user
      tags:
        - principals
      summary: Retrieve service uptime
      responses:
        200:
          description: Success
  /v2/principals/query:
    x-scopes:
      - simon-system-user
      - fa
      - fa-manager
      - issuer
      - admin
    post:
      summary: Get information about one or more users with impersonation
      tags:
        - principals
      requestBody:
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.useracl.api.UserAclQueryRequest}
  /v2/principals:
    x-scopes:
      - simon-system-user
      - fa
      - fa-manager
      - issuer
      - admin
    post:
      summary: Get information about one or more users
      tags:
        - principals
      requestBody:
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.useracl.api.GetUserAclByIdsRequest}
  /v2/principals/{id}:
    x-scopes:
      - simon-system-user
      - fa
      - fa-manager
      - issuer
      - admin
    get:
      summary: Get information about one user, possibly on behalf of another user
      tags:
        - principals
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.PrincipalId}
        - name: from
          in: query
          required: false
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UserId}
        - name: principalIdType
          in: query
          required: false
          schema:
            $ref: ${com.simonmarkets.users.common.IdType}
        - name: networkId
          in: query
          required: false
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
  /v2/principals/{id}/capabilities:
    x-kong-route-defaults:
      regex_priority: -5
    x-scopes:
      - simon-system-user
      - fa
      - fa-manager
      - issuer
      - admin
    get:
      tags:
        - principals
      summary: Gets capabilities for the passed in user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UserId}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
  /v2/principals/cache-sync:
    x-scopes: [admin]
    post:
      summary: Reload the userACL cache
      tags:
        - principals
      requestBody:
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.useracl.api.CacheSyncRequest}
        required: true
  /v2/principals/cache/{id}:
    x-scopes:
      - simon-system-user
      - fa-manager
      - admin
    get:
      summary: Get an ACL from the cache
      tags:
        - principals
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.PrincipalId}
  /v2/principals/cache/{id}/capabilities:
    x-scopes:
      - simon-system-user
      - fa-manager
      - admin
    get:
      summary: Gets capabilities of cached ACL
      tags:
        - principals
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UserId}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
  /v1/my-profile:
    get:
      description: Get own user profile
      responses:
        200:
          description: User profile response
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.useracl.domain.UserProfile}
  /v2/my-profile:
    get:
      description: Get own simon user profile and icn user profile
      responses:
        200:
          description: Unified User profile response
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.useracl.domain.UnifiedUserProfile}
  /v1/capabilities:
    x-kong-route-defaults:
      regex_priority: 1000
    x-scopes:
        - admin
        - fa-manager
        - fa
        - internal
    get:
      tags: [ networks ]
      summary: Get a list of valid custom role capabilities
  /v2/capabilities:
    x-scopes:
      - admin
      - fa-manager
      - fa
      - internal
    get:
      tags: [ networks ]
      summary: Get a list of valid custom role capabilities, description, and asset class
  /v2/capabilities/structure:
    x-scopes:
      - simon-system-user
      - admin
      - internal
    get:
      tags: [ networks ]
      parameters:
        - name: domain
          in: query
          schema:
            type: array
            minLength: 1
            maxLength: 128
            items:
              type: string
      summary: Get a list of api registered capabilities by domain
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.useracl.api.APIRegisteredCapabilityResponse}
  /v1/paid-features/{id}:
    x-scopes:
      - admin
    get:
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UserId}
      responses:
        200:
          description: Gets PaidFeatures item
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.useracl.api.PaidFeaturesResponse}
        404:
          description: User is not found
  /v1/paid-features:
    put:
      requestBody:
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.useracl.api.PaidFeaturesRequest}
      responses:
        200:
          description: Upserts PaidFeatures item
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.useracl.api.PaidFeaturesResponse}
  /v1/paid-features/bulk-upsert:
    put:
      requestBody:
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.useracl.api.BulkUpsertPaidFeaturesRequest}
      responses:
        200:
          description: Upserts PaidFeatures items based on networkId
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.useracl.api.PaidFeaturesResponse}
