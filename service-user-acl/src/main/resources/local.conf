include classpath("service-user-acl-common.conf")

akka.http.server.request-timeout = "60.seconds"

run-mode {
  type = "server-mode"
  http-server-config {
    port = 1984
    interface = "0.0.0.0"
    ssl {
      enabled = false
      keystore {
        type = "JKS"
        file = ""
        password = ""
      }
    }
  }
}

config.resolvers {
  sm {
    region = "us-east-1"
    credentialsProvider = ""
  }
  file {
    root = ""
  }
}

authentication {
  type = "jwt"
  jwt {
    source {
      type = "bearer"
      name = ""
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      keys-url = "sm:applicationconfig-okta-validator"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clock-offset-in-seconds = 0
    }
  }
  user-info {
    source {
      type = "header"
      name = "x-userinfo"
    }
  }
}

dynamo-db-config {
  service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
  signin-region = "us-east-1"
}

service-config {
  service-entitlements = ["admin"]
}

simon-config {
  issuers {
    learning-center-enabled = ["GSGroup", "GSFC", "GSBank", "WFCo", "WFBank", "BCS", "BCSBank"]
  }
}

cloudfront-url-config {
  global-domain-name = "alpha.cdn.simon.io"
  domestic-domain-name = "d3imx7n7j1sdie.cloudfront.net"
  cloudfront-key-pair-id = "sm:cloudFrontKeyPairId"
  cloudfront-private-key = "sm:binary:cloudFrontPrivateKeyFile"
  expiration-millis = "********"
}

mongo-db {
  users {
    database = "pipg"
    collection = "users"
  }
  users-snapshots {
    database = "pipg"
    collection = "users.snapshots"
  }
  networks {
    database = "pipg"
    collection = "networks_new"
  }
  network-visuals {
    database = "pipg"
    collection = "network.visuals"
  }
  networks-snapshots {
    collection = "networks_new.snapshots"
    database = "pipg"
  }
  user-paid-features {
    collection = "users.paidFeatures"
    database = "pipg"
  }
  user-paid-features-snapshots {
    collection = "users.paidFeatures.snapshots"
    database = "pipg"
  }
  client {
    app-name = "service-user-acl"
    connection = "sm:config:applicationconfig-mongo-auth-pipg"
  }
}

http-client-config {
  auth: {
     type = NoAuth
  }
}

icn-config {
  simon-server-path = "https://origin-a.dev.simonmarkets.com"
  host = "icn-int-1.stg.icapitalnetwork.com"
}

system-user-id = "0oaevl54gplnFvyx70h7"

server-path = "https://www.dev.simonmarkets.com/simon/api"

acl-client {
  http-client {
    auth: {
      type = OAuth
      client-id = "0oaevl54gplnFvyx70h7"
      client-secret-file = ""
      site = "sm:applicationconfig-issuer-uri"
      authorization-path = ""
      token-path = "/v1/token"
      scope = "read_product_data"
      token-type: {
        type: Cookie
        name: SimSSO
      }
      client-secret = "sm:Okta-secret"
    }
  }
  base-url = "https://origin-a.dev.simonmarkets.com/simon/api"
  cache-config {
    enabled = true
    config {
      service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
      signin-region = "us-east-1"
    }
  }
}