##
# AWS eks cluster settings
##

# AWS eks IRSA settings
serviceAccount:
  created: false
  name: "service-users"
  iamARN: ""

# SIMON service settings
service:
  name: service-user-acl
  scheme: https
  port: 443
  cpu: "250m"
  memory: "2G"
  healthCheck:
    # Default: /simon/api/v1/healthcheck
    path: /simon/api/v2/principals/uptime
    initialDelay: 300
    interval: 60
    timeout: 15
    failureThreshold: 3
