package com.goldmansachs.marquee.pipg.service.user

import com.simonmarkets.capabilities.{HouseholdsCapabilities, NetworksCapabilities}
import org.scalatest.{Matchers, WordSpec}
import simon.Id.NetworkId

class TestUserACLTest extends WordSpec with Matchers {

  import TestUserACL._

  "withEntitlement" should {
    val baseUserAcl = TestUserACL.apply("user1", NetworkId("network1"))

    "add single entitlement, ViaNetwork" in {
      val updatedUser = baseUserAcl.withEntitlement("viewNetworkViaNetwork:BNP")

      NetworksCapabilities of updatedUser shouldBe Set("viewNetworkViaNetwork:BNP")
    }
    "add single entitlement, ViaPurview" in {
      val updatedUser = baseUserAcl.withEntitlement("viewNetworkViaPurview:BNP")

      NetworksCapabilities of updatedUser shouldBe Set("viewNetworkViaPurview:BNP")
    }

    "add single entitlement, ViaFaNumber" in {
      val updatedUser = baseUserAcl.withEntitlement("viewHouseholdViaFaNumber:Test Network 2:W754")

      HouseholdsCapabilities of updatedUser shouldBe Set("viewHouseholdViaFaNumber:Test Network 2:W754")
    }

    "add single entitlement, ViaLocation" in {
      val updatedUser = baseUserAcl.withEntitlement("viewHouseholdViaLocation:Test Network 2:TestLocation2")

      HouseholdsCapabilities of updatedUser shouldBe Set("viewHouseholdViaLocation:Test Network 2:TestLocation2")
    }

    "add entitlement next to existing entitlement" in {
      val updatedUser = baseUserAcl
        .copy(userPurviewIds = Set(NetworkId("BNP")))
        .withEntitlement("viewNetworkViaPurview:BNP2")

      NetworksCapabilities of updatedUser shouldBe Set("viewNetworkViaPurview:BNP", "viewNetworkViaPurview:BNP2")
    }

  }
}
