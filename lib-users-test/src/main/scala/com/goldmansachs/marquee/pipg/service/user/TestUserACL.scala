package com.goldmansachs.marquee.pipg.service.user

import com.goldmansachs.marquee.pipg.{UserRole, _}
import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.shared.MaskedId
import simon.Id.NetworkId

import java.time.LocalDateTime

object TestUserACL {

  def apply(
      userId: String,
      networkId: NetworkId,
      email: Option[String] = None,
      firstName: Option[String] = None,
      lastName: Option[String] = None,
      roles: Set[UserRole] = Set.empty,
      distributorId: Option[String] = None,
      tradewebId: Option[String] = None,
      tradewebEligible: Boolean = false,
      regSEligible: Boolean = false,
      isActive: Option[Boolean] = Some(true),
      userPurviewIds: Set[NetworkId] = Set.empty,
      issuerPurviewIds: Set[IssuerPurview] = Set.empty,
      purviews: Option[Set[Purview]] = None,
      lastVisitedAt: Option[LocalDateTime] = None,
      approverMap: ApproverMap = ApproverMap.empty,
      dynamicRoles: Set[String] = Set.empty,
      locations: Set[String] = Set.empty,
      faNumbers: Set[String] = Set.empty,
      custodianFaNumbers: Set[CustodianFaNumber] = Set.empty,
      customRoles: Set[String] = Set.empty,
      capabilities: Set[String] = Set.empty,
      payoffEntitlements: Map[String, Map[String, Set[Network.Action]]] = Map.empty,
      networkInfo: Network.Info = Network.Info.empty,
      maskedIds: Set[MaskedId] = Set.empty,
      ioiApproverSet: Map[String, List[List[String]]] = Map.empty,
      licenses: Set[License] = Set.empty,
      annuityCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
      learnTracks: Seq[String] = Seq.empty,
      learnTracksV2: Seq[LearnTrack] = Seq.empty,
      learnContent: Seq[String] = Seq.empty,
      definedOutcomeETFCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
      siCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
      group: Option[String] = None,
      networkCapabilities: Option[Map[String, List[String]]] = None,
      altCertificationRequirements: Option[Seq[CertificationAlternativesForProduct]] = None,
      externalIds: Option[Seq[ExternalId]] = None,
      purviewLicenses: Option[Set[License]] = None,
      purviewNsccCodes: Option[Set[String]] = None,
      firmId: Option[String] = None,
      whiteLabelPartnerId: Option[String] = None,
      secondaryEmail: Option[String] = None,
      iCapitalUserId: Option[String] = None,
      productTypeCertificationRequirements: Option[Seq[ProductCertificationRequirements]] = None,
      networkTypes: Option[List[NetworkType]] = None,
  ): UserACL =
    UserACL(
      userId = userId,
      networkId = networkId,
      lastVisitedAt = lastVisitedAt,
      email = email.getOrElse(""),
      firstName = firstName.getOrElse(""),
      lastName = lastName.getOrElse(""),
      roles = roles,
      distributorId = distributorId,
      omsId = tradewebId,
      tradewebEligible = tradewebEligible,
      regSEligible = regSEligible,
      isActive = isActive,
      userPurviewIds = userPurviewIds,
      issuerPurviewIds = issuerPurviewIds,
      purviews = purviews,
      approverMap = approverMap,
      dynamicRoles = dynamicRoles,
      locations = locations,
      faNumbers = faNumbers,
      custodianFaNumbers = custodianFaNumbers,
      customRoles = customRoles,
      capabilities = capabilities,
      payoffEntitlementsV2 = payoffEntitlements,
      networkInfo = networkInfo,
      maskedIds = maskedIds,
      ioiApproverSet = ioiApproverSet,
      networkTypes = networkTypes,
      licenses = licenses,
      learnTracks = learnTracks,
      learnTracksV2 = learnTracksV2,
      learnContent = learnContent,
      siCertificationRequirements = siCertificationRequirements,
      annuityCertificationRequirements = annuityCertificationRequirements,
      definedOutcomeETFCertificationRequirements = definedOutcomeETFCertificationRequirements,
      group = group,
      networkCapabilities = networkCapabilities,
      altCertificationRequirements = altCertificationRequirements,
      externalIds = externalIds,
      purviewLicenses = purviewLicenses,
      purviewNsccCodes = purviewNsccCodes,
      firmId = firmId,
      whiteLabelPartnerId = whiteLabelPartnerId,
      secondaryEmail = secondaryEmail,
      iCapitalUserId = iCapitalUserId,
      productTypeCertificationRequirements = productTypeCertificationRequirements,
    )


  /**
   * @param domainCapabilities Typically an object that implements the capabilities and the generation for the given domain
   */
  implicit class DomainCapabilitiesOps(domainCapabilities: Capabilities with AvailableAccessKeysGenerator) {
    /**
     * @return the generated capabilities for `userAcl` for the given domain
     */
    def of(userAcl: UserACL): Set[String] =
      domainCapabilities.getAvailableAccessKeysForCapabilities(domainCapabilities.toSet, userAcl)
  }

  implicit class UserAclOps(userAcl: UserACL) {

    /**
     * Given an `entitlementString` change the userAcl, so it also produces the `entitlementString` on entitlement generation
     * Note that some fields can be overwritten, eg: NetworkId through ViaNetworkId because it just changes the only value
     */
    def withEntitlement(entitlementString: String): UserACL =
      parseEntitlement(entitlementString).addToUserAcl(userAcl)

    private def parseEntitlement(entitlementString: String): Entitlement =
      entitlementString.split(":") match {
        case Array("admin") => AdminEntitlement
        case Array(name, values@_*) =>
          name.split("(?=[A-Z])").toList match {
            case List(action, domain, "Via", via@_*) =>
              CommonEntitlement(action, domain, via.mkString, values)
            case _ =>
              throw new Exception(s"Cannot parse $entitlementString")
          }
        case _ =>
          throw new Exception(s"Cannot parse $entitlementString")
      }
  }

}
