package com.goldmansachs.marquee.pipg.service.user

import com.goldmansachs.marquee.pipg.{UserACL, UserRole}
import com.simonmarkets.capabilities.Capabilities
import simon.Id.NetworkId

sealed trait Entitlement {
  // The fix part of Entitlement that's independent of the UserACL's data
  def capability: String

  /**
   * @return an updated userAcl which produces this Entitlement on entitlement generation
   */
  final def addToUserAcl(userAcl: UserACL): UserACL =
    addToUserAclImpl(userAcl).copy(capabilities = userAcl.capabilities + capability)

  protected def addToUserAclImpl(userAcl: UserACL): UserACL

  protected def setNetworkAndMap(userACL: UserACL, values: Seq[String])
    (mapUserAcl: (UserACL, String) => UserACL): UserACL = {
    val Seq(networkId, other) = values
    mapUserAcl(userACL.copy(networkId = NetworkId(networkId)), other)
  }
}

case object AdminEntitlement extends Entitlement {
  def capability: String = Capabilities.Admin

  def addToUserAclImpl(userAcl: UserACL): UserACL =
    userAcl.copy(
      roles = userAcl.roles + UserRole.EqPIPGGSAdmin,
      capabilities = userAcl.capabilities + Capabilities.Admin
    )
}

/** Most Entitlements follow a pattern, they can be handled with this class */
case class CommonEntitlement(action: String, domain: String, via: String, values: Seq[String]) extends Entitlement {
  def capability: String = s"$action${domain}Via$via"

  def addToUserAclImpl(userAcl: UserACL): UserACL =
    via match {
      // These are some common entitlements, feel free to add new ones that are not handled
      case "Network" => userAcl.copy(networkId = NetworkId(values.head))
      case "Purview" => userAcl.copy(userPurviewIds = userAcl.userPurviewIds + NetworkId(values.head))
      case "Location" | "LocationHierarchy" => setNetworkAndMap(userAcl, values)((u, location) =>
        u.copy(locations = u.locations + location)
      )
      case "FaNumber" => setNetworkAndMap(userAcl, values)((u, faNumber) =>
        u.copy(faNumbers = userAcl.faNumbers + faNumber)
      )
      case "DistributorId" => setNetworkAndMap(userAcl, values)((u, distributorId) =>
        u.copy(distributorId = Some(distributorId))
      )
      case _ => throw new Exception(s"No defined case for $capability")
    }
}
