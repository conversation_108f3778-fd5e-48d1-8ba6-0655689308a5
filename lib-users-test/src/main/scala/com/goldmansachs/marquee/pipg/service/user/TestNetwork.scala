package com.goldmansachs.marquee.pipg.service.user

import com.goldmansachs.marquee.pipg._
import com.simonmarkets.shared.MaskedId
import simon.Id.NetworkId


object TestNetwork {

  val testIdHubOrganization: IdHubOrganization = IdHubOrganization(0, "test")

  val nameAsId: String = "name-as-id"

  def apply(
      id: NetworkId,
      name: String = nameAsId,
      idHubOrganization: IdHubOrganization = testIdHubOrganization,
      purviews: Option[Set[Purview]] = None,
      approverSet: Map[String, List[List[String]]] = Map.empty,
      accountMappings: Option[List[String]] = None,
      networkTypes: Option[List[NetworkType]] = None,
      to: Option[List[String]] = None,
      cc: Option[List[String]] = None,
      purviewNetworks: Option[Set[IssuerPurview]] = None,
      salesFeeRuleIds: List[String] = Nil,
      capabilities: Map[String, List[String]] = Map.empty,
      payoffEntitlements: Map[String, Map[String, List[String]]] = Map.empty,
      payoffEntitlementsV2: Map[String, Map[String, Set[Network.Action]]] = Map.empty,
      omsAlias: Option[String] = None,
      distributorAlias: Option[ExternalAlias] = None,
      dynamicRoles: Set[String] = Set.empty,
      customRolesConfig: Set[CustomRoleDefinition] = Set.empty,
      version: Int = 0,
      maskedIds: Set[MaskedId] = Set.empty,
      booksCloseConfig: List[BooksCloseConfig] = List.empty,
      locations: Set[NetworkLocation] = Set.empty,
      contactInfo: Option[ContactInfo] = None,
      siCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
      annuityCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
      definedOutcomeETFCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
      certificationProducts: Option[Seq[String]] = None,
      altCertificationRequirements: Option[Seq[CertificationAlternativesForProduct]] = None
  ): Network = {

    val generatedName =
      if (name == nameAsId) NetworkId unwrap id
      else name

    Network(
      id = id,
      networkName = generatedName,
      idHubOrganization = idHubOrganization,
      purviews = purviews,
      approverSet = approverSet,
      accountMappings = accountMappings,
      networkTypes = networkTypes,
      to = to,
      cc = cc,
      purviewNetworks = purviewNetworks,
      salesFeeRuleIds = salesFeeRuleIds,
      capabilities = capabilities,
      payoffEntitlements = payoffEntitlements,
      payoffEntitlementsV2 = payoffEntitlementsV2,
      dynamicRoles = dynamicRoles,
      distributorAlias = distributorAlias,
      omsAlias = omsAlias,
      version = version,
      customRolesConfig = customRolesConfig,
      maskedIds = maskedIds,
      booksCloseConfig = booksCloseConfig,
      locations = locations,
      contactInfo = contactInfo,
      siCertificationRequirements = siCertificationRequirements,
      annuityCertificationRequirements = annuityCertificationRequirements,
      definedOutcomeETFCertificationRequirements = definedOutcomeETFCertificationRequirements,
      certificationProducts = certificationProducts,
      altCertificationRequirements = altCertificationRequirements
    )
  }
}
