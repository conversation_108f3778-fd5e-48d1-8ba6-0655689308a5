package com.simonmarkets.networks.common

import org.scalatest.Matchers.{a, convertToAnyShouldWrapper}
import org.scalatest.concurrent.{PatienceConfiguration, ScalaFutures}
import org.scalatest.time.Seconds

import scala.concurrent.Future
import scala.reflect.ClassTag

object TestUtils {

  implicit class FutureOps[T](future: Future[T]) {
    def assertResult(expectedResult: T, timeoutInSeconds: Int = 60): Unit = {
      assert((x: T) => x shouldBe expectedResult, timeoutInSeconds)
    }

    def assert(f: T => _, timeoutInSeconds: Int = 60): Unit = {
      ScalaFutures.whenReady(future, timeout(timeoutInSeconds)) {
        f(_)
      }
    }

    def assertFailure[E: ClassTag](timeoutInSeconds: Int = 60): Unit = {
      ScalaFutures.whenReady(future.failed, timeout(timeoutInSeconds)) {
        _ shouldBe a[E]
      }
    }

    def assertFailureEx(ex: Exception, timeoutInSeconds: Int = 60): Unit = {
      ScalaFutures.whenReady(future.failed, timeout(timeoutInSeconds)) {
        _ shouldBe ex
      }
    }

    def assertFailureMessage(message: String, timeoutInSeconds: Int = 60): Unit = {
      ScalaFutures.whenReady(future.failed, timeout(timeoutInSeconds)) {
        _.getMessage shouldBe message
      }
    }
  }

  private def timeout[T](timeoutInSeconds: Int) =
    PatienceConfiguration.Timeout(org.scalatest.time.Span(timeoutInSeconds, Seconds))
}