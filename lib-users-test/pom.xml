<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.simonmarkets</groupId>
        <artifactId>users-networks</artifactId>
        <version>sandbox-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>lib-users-test</artifactId>
    <version>sandbox-SNAPSHOT</version>


    <dependencies>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
        </dependency>

        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-users-resteasy</artifactId>
            <version>sandbox-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.goldmansachs.marquee</groupId>
            <artifactId>simon-entitlements</artifactId>
            <version>sandbox-SNAPSHOT</version>
        </dependency>

        <!-- TEST DEPENDENCIES-->
        <!--scope manually specified else bom sets it to test scope-->
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_${scala.version.short}</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.scalactic</groupId>
            <artifactId>scalactic_${scala.version.short}</artifactId>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
