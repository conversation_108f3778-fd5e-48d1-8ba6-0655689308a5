package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.buildAdminKeys
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

object CryptoOfferingsCapabilities extends Capabilities with AvailableAccessKeysGenerator with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {
  override val DomainName: String = "CryptoOfferings"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.DigitalAssetClass))

  val ViewCryptoOfferingViaNetwork = "ViewCryptoOfferingViaNetwork"
  val EditCryptoOfferingViaNetwork = "EditCryptoOfferingViaNetwork"

  val ViewCryptoOfferingViaNetworkCapability: Capability = Capability(ViewCryptoOfferingViaNetwork,
    "Allows one to view a CryptoOffering if one's Network Id matches the CryptoOffering's Network Id", assetClasses)
  val EditCryptoOfferingViaNetworkCapability: Capability = Capability(EditCryptoOfferingViaNetwork,
    "Allows one to edit a CryptoOffering if one's Network Id matches the CryptoOffering's Network Id", assetClasses)

  override val ViewCapabilities: Set[String] = Set(Admin, ViewCryptoOfferingViaNetwork)
  override val EditCapabilities: Set[String] = Set(Admin, EditCryptoOfferingViaNetwork)

  override def DetailedViewCapabilities: Set[Capability] = Set(AdminCapability, ViewCryptoOfferingViaNetworkCapability)

  override def DetailedEditCapabilities: Set[Capability] = Set(AdminCapability, EditCryptoOfferingViaNetworkCapability)

  override def toSet: Set[String] = ViewCapabilities ++ EditCapabilities

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewCryptoOfferingViaNetwork -> availableKeyBuilder.add(_.networkId),
    EditCryptoOfferingViaNetwork -> availableKeyBuilder.add(_.networkId),
  )

}