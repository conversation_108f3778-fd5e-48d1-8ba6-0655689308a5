package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.capabilities.AssetClasses._
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedViewCapabilities, HasViewCapabilities}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator._

/**
 * This class contains offering capabilities foroffering V1. This is only to run
 */
object OfferingsV1Capabilities extends Capabilities with HasViewCapabilities with HasDetailedViewCapabilities {
  override val DomainName: String = "OfferingsV1"

  val ViewViaDynamicRolesCapability: Capability = Capability("view", "", Option(Seq(Platform)))
  // controls the buy button visibility in the SP marketplace. used by networks like US Bank to hide the buy button for some custom roles.
  val TradeOfferingViaTradePayoffEntitlementCapability: Capability = Capability(
    "tradeOfferingViaTradePayoffEntitlement",
    "Allow buy button in SI. Also requires trade capabilities to be enabled",
    Option(Seq(StructuredInvestmentsAssetClass, AnnuitiesAssetClass)))
  val TradeOfferingViaProductIdEntitlementCapability: Capability = Capability(
    "tradeOfferingViaProductIdEntitlement",
    "", //TO BE REMOVED
    Option(Seq(AnnuitiesAssetClass)))
  val PrintPdfViaPerfPayoffEntitlementCapability: Capability = Capability(
    "printPdfViaPerfPayoffEntitlement",
    "Grants user to print performance pdf for any contract they have access to",
    Option(Seq(StructuredInvestmentsAssetClass)))

  val ViewViaDynamicRoles: String = ViewViaDynamicRolesCapability.name
  val TradeOfferingViaTradePayoffEntitlement: String = TradeOfferingViaTradePayoffEntitlementCapability.name
  val TradeOfferingViaProductIdEntitlement: String = TradeOfferingViaProductIdEntitlementCapability.name

  val TradeCapabilities: Set[String] = Set(Admin, TradeOfferingViaTradePayoffEntitlement, TradeOfferingViaProductIdEntitlement)

  override val DetailedViewCapabilities: Set[Capability] = Set(AdminCapability, ViewViaDynamicRolesCapability)
  val DetailedTradeCapabilities: Set[Capability] = Set(AdminCapability, TradeOfferingViaTradePayoffEntitlementCapability, TradeOfferingViaProductIdEntitlementCapability)
  val DetailedPrintCapabilities: Set[Capability] = Set(AdminCapability, PrintPdfViaPerfPayoffEntitlementCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedTradeCapabilities ++ DetailedPrintCapabilities

  val availableAccessKeysGen = new AvailableAccessKeysGenerator {

    override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys),
      ViewViaDynamicRoles -> AvailableKeyBuilder(buildDynamicRolesKeys),
      TradeOfferingViaTradePayoffEntitlement -> AvailableKeyBuilder(undecoratedKeyBuilder),
      TradeOfferingViaProductIdEntitlement -> AvailableKeyBuilder(buildCusipsKeys),
      PrintPdfViaPerfPayoffEntitlementCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder)
    )
  }

  def buildDynamicRolesKeys(capability: String, acl: UserACL): Set[String] = {
    acl.dynamicRoles.map {
      dynamicRole => s"$capability:$dynamicRole"
    }
  }
}
