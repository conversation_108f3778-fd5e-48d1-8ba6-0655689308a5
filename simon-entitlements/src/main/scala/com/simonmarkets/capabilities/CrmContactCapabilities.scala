package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.capabilities.OrdersCapabilities.ViewCapabilities
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.buildAdminKeys
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

import scala.collection.mutable

object CrmContactCapabilities extends Capabilities with HasViewCapabilities with HasDetailedViewCapabilities{
  override val DomainName: String = "CrmContacts"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Platform))

  val ViewCrmContactViaOwnerCapability: Capability = Capability("viewCrmContactViaOwner", "", assetClasses)
  val ViewCrmContactViaOwner: String = ViewCrmContactViaOwnerCapability.name

  override val DetailedViewCapabilities: Set[Capability] = Set(ViewCrmContactViaOwnerCapability, AdminCapability)

  val availableAccessKeysGenerator: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {
    override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys),
      ViewCrmContactViaOwner -> AvailableKeyBuilder(getAvailableKeysViaOwner),
    )
  }

  private def getAvailableKeysViaOwner(capability: String, userACL: UserACL): Set[String] = Set(s"$capability:${userACL.userId}")

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities
}
