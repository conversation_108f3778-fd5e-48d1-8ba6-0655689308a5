package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.buildAdminKeys
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

import scala.collection.mutable

object CrmAuthorizationCapabilities extends Capabilities with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {
  override val DomainName: String = "CrmAuthorizations"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Platform))

  val ViewCrmAuthorizationViaOwnerCapability: Capability = Capability("viewCrmAuthorizationViaOwner", "", assetClasses)
  val EditCrmAuthorizationViaOwnerCapability: Capability = Capability("editCrmAuthorizationViaOwner", "", assetClasses)

  val ViewCrmAuthorizationViaOwner: String = ViewCrmAuthorizationViaOwnerCapability.name
  val EditCrmAuthorizationViaOwner: String = EditCrmAuthorizationViaOwnerCapability.name

  override val DetailedViewCapabilities: Set[Capability] = Set(ViewCrmAuthorizationViaOwnerCapability, AdminCapability)
  override val DetailedEditCapabilities: Set[Capability] = Set(EditCrmAuthorizationViaOwnerCapability, AdminCapability)

  val availableAccessKeysGenerator: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {
    override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys),
      ViewCrmAuthorizationViaOwner -> AvailableKeyBuilder(getAvailableKeysViaOwner),
      EditCrmAuthorizationViaOwner -> AvailableKeyBuilder(getAvailableKeysViaOwner)
    )
  }

  private def getAvailableKeysViaOwner(capability: String, userACL: UserACL): Set[String] = Set(s"$capability:${userACL.userId}")

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities
}