package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator._
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

object SmaCompositeDataCapabilities extends Capabilities with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities
  with AvailableAccessKeysGenerator {

  override val DomainName: String = "SmaCompositeData"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.SMAsAssetClass))

  val ViewSmaCompositeDataViaStrategyAndUnderlierCapability: Capability = Capability("viewSmaCompositeDataViaStrategyAndUnderlier", "", assetClasses)
  val EditSmaCompositeDataViaStrategyAndUnderlierCapability: Capability = Capability("editSmaCompositeDataViaStrategyAndUnderlier", "", assetClasses)

  val ViewSmaCompositeDataViaStrategyAndUnderlier: String = ViewSmaCompositeDataViaStrategyAndUnderlierCapability.name
  val EditSmaCompositeDataViaStrategyAndUnderlier: String = EditSmaCompositeDataViaStrategyAndUnderlierCapability.name

  val DeleteCapabilities: Set[String] = Set(Admin)

  override val DetailedViewCapabilities: Set[Capability] = Set(
    AdminCapability,
    ViewSmaCompositeDataViaStrategyAndUnderlierCapability
  )
  override val DetailedEditCapabilities: Set[Capability] = Set(
    AdminCapability,
    EditSmaCompositeDataViaStrategyAndUnderlierCapability
  )

  val DetailedDeleteCapabilities: Set[Capability] = Set(AdminCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities ++ DetailedDeleteCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewSmaCompositeDataViaStrategyAndUnderlier -> AvailableKeyBuilder(buildSmaStrategyAndUnderlierKeys),
    EditSmaCompositeDataViaStrategyAndUnderlier -> AvailableKeyBuilder(buildSmaStrategyAndUnderlierKeys)
  )
}
