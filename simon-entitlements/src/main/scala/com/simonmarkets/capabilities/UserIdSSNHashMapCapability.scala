package com.simonmarkets.capabilities

import com.simonmarkets.entitlements.{HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

object UserIdSSNHashMapCapability extends Capabilities with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {
  override val DomainName: String = "UserIdHashSSNMap"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Platform))

  val viewUserSSNMapCapability: Capability = Capability("viewUserSSNMap", "", assetClasses)
  val editUserSSNMapCapability: Capability = Capability("editUserSSNMap", "", assetClasses)

  val viewUserSSNMap: String = viewUserSSNMapCapability.name
  val editUserSSNMap: String = editUserSSNMapCapability.name

  override def toDetailedCapabilitySet: Set[Capability] = DetailedEditCapabilities ++ DetailedViewCapabilities

  override val DetailedEditCapabilities: Set[Capability] = Set(editUserSSNMapCapability)
  override val DetailedViewCapabilities: Set[Capability] = Set(viewUserSSNMapCapability)


}
