package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.{NetworkType, UserACL}
import com.goldmansachs.marquee.pipg.NetworkType._
import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.capabilities.utils.CapabilityDefinition
import com.simonmarkets.entitlements.{HasEditCapabilities, HasDetailedEditCapabilities, HasViewCapabilities, HasDetailedViewCapabilities}

@CapabilityDefinition
object AltOfferingCapabilities extends Capabilities with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {
  override val DomainName = "AltOfferings"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.AlternativesAssetClass))

  val ALT_OFFERING_PAYOFF_TYPE = "Alternative"

  //Via NetworkType
  val ViewAltOfferingViaNetworkTypeCapability: Capability = Capability("viewAltOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val ViewAltOfferingViaNetworkType: String = ViewAltOfferingViaNetworkTypeCapability.name
  val EditAltOfferingViaNetworkTypeCapability: Capability = Capability("editAltOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val EditAltOfferingViaNetworkType: String = EditAltOfferingViaNetworkTypeCapability.name
  val ApproveAltOfferingViaNetworkTypeCapability: Capability = Capability("approveAltOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val ApproveAltOfferingViaNetworkType: String = ApproveAltOfferingViaNetworkTypeCapability.name
  val TradeAltOfferingViaNetworkTypeCapability: Capability = Capability("tradeAltOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val TradeAltOfferingViaNetworkType: String = TradeAltOfferingViaNetworkTypeCapability.name
  val OpenCloseAltOfferingViaNetworkTypeCapability: Capability = Capability("openCloseAltOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val OpenCloseAltOfferingViaNetworkType: String = OpenCloseAltOfferingViaNetworkTypeCapability.name

  //Via Network Manager
  val ViewAltOfferingViaNetworkManagerCapability: Capability = Capability("viewAltOfferingViaNetworkManager", "Allows a network manager to always see a network's offering regardless of current status and location/individual/fa number approvals", assetClasses)
  val ApproveAltOfferingViaNetworkManagerCapability: Capability = Capability("approveAltOfferingViaNetworkManager", "Allows a network manager to always modify a network's offering status regardless of current status and location/individual/fa number approvals", assetClasses)
  val ViewAltOfferingViaPurviewNetworkManagerCapability: Capability = Capability("viewAltOfferingViaPurviewNetworkManager", "Allows a network manager to always see a network's offering regardless of current status and location/individual/fa number approvals", assetClasses)
  val ApproveAltOfferingViaPurviewNetworkManagerCapability: Capability = Capability("approveAltOfferingViaPurviewNetworkManager", "Allows a network manager to always modify a network's offering status regardless of current status and location/individual/fa number approvals", assetClasses)

  val ViewAltOfferingViaNetworkManager: String = ViewAltOfferingViaNetworkManagerCapability.name
  val ApproveAltOfferingViaNetworkManager: String = ApproveAltOfferingViaNetworkManagerCapability.name
  val ViewAltOfferingViaPurviewNetworkManager: String = ViewAltOfferingViaPurviewNetworkManagerCapability.name
  val ApproveAltOfferingViaPurviewNetworkManager: String = ApproveAltOfferingViaPurviewNetworkManagerCapability.name

  //Via Network
  val ViewApprovedAltOfferingViaNetworkCapability: Capability = Capability("viewApprovedAltOfferingViaNetwork", "Allows a user to see a network's approved offerings", assetClasses)
  val ViewClosedAltOfferingViaNetworkCapability: Capability = Capability("viewClosedAltOfferingViaNetwork", "Allows a user to see a network's closed offerings", assetClasses)
  val ViewPendingAltOfferingViaNetworkCapability: Capability = Capability("viewPendingAltOfferingViaNetwork", "Allows a user to see a network's pending offerings", assetClasses)
  val EditAltOfferingViaNetworkCapability: Capability = Capability("editAltOfferingViaNetwork", "Allows a user to edit a network's offerings", assetClasses)
  val ApproveAltOfferingViaNetworkCapability: Capability = Capability("approveAltOfferingViaNetwork", "Allows a user to approve a network's offerings", assetClasses)
  val TradeAltOfferingViaNetworkCapability: Capability = Capability("tradeAltOfferingViaNetwork", "Allows a user to trade a network's offerings", assetClasses)
  val OpenCloseAltOfferingViaNetworkCapability: Capability = Capability("openCloseAltOfferingViaNetwork", "Allows a user to open/close a network's offerings", assetClasses)

  val ViewApprovedAltOfferingViaNetwork: String = ViewApprovedAltOfferingViaNetworkCapability.name
  val ViewClosedAltOfferingViaNetwork: String = ViewClosedAltOfferingViaNetworkCapability.name
  val ViewPendingAltOfferingViaNetwork: String = ViewPendingAltOfferingViaNetworkCapability.name
  val EditAltOfferingViaNetwork: String = EditAltOfferingViaNetworkCapability.name
  val ApproveAltOfferingViaNetwork: String = ApproveAltOfferingViaNetworkCapability.name
  val TradeAltOfferingViaNetwork: String = TradeAltOfferingViaNetworkCapability.name
  val OpenCloseAltOfferingViaNetwork: String = OpenCloseAltOfferingViaNetworkCapability.name

  //Via Purview
  val ViewApprovedAltOfferingViaPurviewCapability: Capability = Capability("viewApprovedAltOfferingViaPurview", "Allows a user to see a purviewed network's approved offerings", assetClasses)
  val ViewClosedAltOfferingViaPurviewCapability: Capability = Capability("viewClosedAltOfferingViaPurview", "Allows a user to see a purviewed network's closed offerings", assetClasses)
  val ViewPendingAltOfferingViaPurviewCapability: Capability = Capability("viewPendingAltOfferingViaPurview", "Allows a user to see a purviewed network's pending offerings", assetClasses)
  val EditAltOfferingViaPurviewCapability: Capability = Capability("editAltOfferingViaPurview", "Allows a user to edit a purviewed network's offerings", assetClasses)
  val ApproveAltOfferingViaPurviewCapability: Capability = Capability("approveAltOfferingViaPurview", "Allows a user to edit a purviewed network's offerings", assetClasses)
  val OpenCloseAltOfferingViaPurviewCapability: Capability = Capability("openCloseAltOfferingViaPurview", "Allows a user to open/close a purviewed network's offerings", assetClasses)

  val ViewApprovedAltOfferingViaPurview: String = ViewApprovedAltOfferingViaPurviewCapability.name
  val ViewClosedAltOfferingViaPurview: String = ViewClosedAltOfferingViaPurviewCapability.name
  val ViewPendingAltOfferingViaPurview: String = ViewPendingAltOfferingViaPurviewCapability.name
  val EditAltOfferingViaPurview: String = EditAltOfferingViaPurviewCapability.name
  val ApproveAltOfferingViaPurview: String = ApproveAltOfferingViaPurviewCapability.name
  val OpenCloseAltOfferingViaPurview: String = OpenCloseAltOfferingViaPurviewCapability.name

  //Via View Payoff Entitlement
  val ViewApprovedAltOfferingViaNetworkAndViewPayoffEntitlementCapability: Capability = Capability("viewApprovedAltOfferingViaNetworkAndViewPayoffEntitlement", "Allows a user to see a network's approved offerings if they have the view payoff entitlement for the offering's issuer key and asset class", assetClasses)
  val ViewClosedAltOfferingViaNetworkAndViewPayoffEntitlementCapability: Capability = Capability("viewClosedAltOfferingViaNetworkAndViewPayoffEntitlement", "Allows a user to see a network's closed offerings if they have the view payoff entitlements for the offering's issuer key and asset class", assetClasses)
  val ViewPendingAltOfferingViaNetworkAndViewPayoffEntitlementCapability: Capability = Capability("viewPendingAltOfferingViaNetworkAndViewPayoffEntitlement", "Allows a user to see a network's pending offerings if they have the view payoff entitlements for the offering's issuer key and asset class", assetClasses)

  val ViewApprovedAltOfferingViaPurviewAndViewPayoffEntitlementCapability: Capability = Capability("viewApprovedAltOfferingViaPurviewAndViewPayoffEntitlement", "Allows a user to see a purviewed network's approved offerings if they have the view payoff entitlement for the offering's issuer key and asset class", assetClasses)
  val ViewClosedAltOfferingViaPurviewAndViewPayoffEntitlementCapability: Capability = Capability("viewClosedAltOfferingViaPurviewAndViewPayoffEntitlement", "Allows a user to see a purviewed network's closed offerings if they have the view payoff entitlement for the offering's issuer key and asset class", assetClasses)
  val ViewPendingAltOfferingViaPurviewAndViewPayoffEntitlementCapability: Capability = Capability("viewPendingAltOfferingViaPurviewAndViewPayoffEntitlement", "Allows a user to see a purviewed network's pending offerings if they have the view payoff entitlement for the offering's issuer key and asset class", assetClasses)

  val ViewApprovedAltOfferingViaNetworkAndViewPayoffEntitlement: String = ViewApprovedAltOfferingViaNetworkAndViewPayoffEntitlementCapability.name
  val ViewClosedAltOfferingViaNetworkAndViewPayoffEntitlement: String = ViewClosedAltOfferingViaNetworkAndViewPayoffEntitlementCapability.name
  val ViewPendingAltOfferingViaNetworkAndViewPayoffEntitlement: String = ViewPendingAltOfferingViaNetworkAndViewPayoffEntitlementCapability.name

  val ViewApprovedAltOfferingViaPurviewAndViewPayoffEntitlement: String = ViewApprovedAltOfferingViaPurviewAndViewPayoffEntitlementCapability.name
  val ViewClosedAltOfferingViaPurviewAndViewPayoffEntitlement: String = ViewClosedAltOfferingViaPurviewAndViewPayoffEntitlementCapability.name
  val ViewPendingAltOfferingViaPurviewAndViewPayoffEntitlement: String = ViewPendingAltOfferingViaPurviewAndViewPayoffEntitlementCapability.name

  //Via Trade Payoff Entitlement
  val TradeAltOfferingViaNetworkAndTradePayoffEntitlementCapability: Capability = Capability("tradeAltOfferingViaNetworkAndTradePayoffEntitlement", "Allows a user to trade a network's offerings if they have the trade payoff entitlements for the offering's issuer key and asset class", assetClasses)
  val TradeAltOfferingViaNetworkAndTradePayoffEntitlement: String = TradeAltOfferingViaNetworkAndTradePayoffEntitlementCapability.name

  //Via Product Issuer Network Type - intended only for +Subscribe
  val ViewAltOfferingViaProductIssuerNetworkTypeCapability: Capability = Capability("viewAltOfferingViaProductIssuerNetworkType", "", assetClasses)
  val EditAltOfferingViaProductIssuerNetworkTypeCapability: Capability = Capability("editAltOfferingViaProductIssuerNetworkType", "", assetClasses)
  val ApproveAltOfferingViaProductIssuerNetworkTypeCapability: Capability = Capability("approveAltOfferingViaProductIssuerNetworkType", "", assetClasses)
  val OpenCloseAltOfferingViaProductIssuerNetworkTypeCapability: Capability = Capability("openCloseAltOfferingViaProductIssuerNetworkType", "", assetClasses)

  val ViewAltOfferingViaProductIssuerNetworkType: String = ViewAltOfferingViaProductIssuerNetworkTypeCapability.name
  val EditAltOfferingViaProductIssuerNetworkType: String = EditAltOfferingViaProductIssuerNetworkTypeCapability.name
  val ApproveAltOfferingViaProductIssuerNetworkType: String = ApproveAltOfferingViaProductIssuerNetworkTypeCapability.name
  val OpenCloseAltOfferingViaProductIssuerNetworkType: String = OpenCloseAltOfferingViaProductIssuerNetworkTypeCapability.name


  //Via Location
  val ViewApprovedAltOfferingViaLocationCapability: Capability = Capability("viewApprovedAltOfferingViaLocation","Allows a user to view an approved offering if the offering has been approved for their location", assetClasses)
  val ViewPendingAltOfferingViaLocationCapability: Capability = Capability("viewPendingAltOfferingViaLocation","Allows a user to view a pending offering if the offering has been approved for their location", assetClasses)
  val ViewClosedAltOfferingViaLocationCapability: Capability = Capability("viewClosedAltOfferingViaLocation","Allows a user to view a closed offering if the offering was previously approved for their location", assetClasses)

  //Via User
  val ViewApprovedAltOfferingViaUserCapability: Capability = Capability("viewApprovedAltOfferingViaUser","Allows a user to view an approved offering if the offering has been approved for their user id", assetClasses)
  val ViewPendingAltOfferingViaUserCapability: Capability = Capability("viewPendingAltOfferingViaUser","Allows a user to view a pending offering if the offering has been approved for their user id", assetClasses)
  val ViewClosedAltOfferingViaUserCapability: Capability = Capability("viewClosedAltOfferingViaUser","Allows a user to view a closed offering if the offering was previously approved for their user id", assetClasses)

  //Via FA Number
  val ViewApprovedAltOfferingViaFaNumberCapability: Capability = Capability("viewApprovedAltOfferingViaFaNumber","Allows a user to view an approved offering if the offering has been approved for their fa number", assetClasses)
  val ViewPendingAltOfferingViaFaNumberCapability: Capability = Capability("viewPendingAltOfferingViaFaNumber","Allows a user to view a pending offering if the offering has been approved for their fa number", assetClasses)
  val ViewClosedAltOfferingViaFaNumberCapability: Capability = Capability("viewClosedAltOfferingViaFaNumber","Allows a user to view a closed offering if the offering was previously approved for their fa number", assetClasses)

  val ProductIssuerNetworkTypes: Set[NetworkType] = Set(Issuer, Carrier)

  //view,edit,approve,trade,reject

  val ApproveCapabilities = Set(
    Admin,
    ApproveAltOfferingViaNetworkManager,
    ApproveAltOfferingViaPurviewNetworkManager,
    ApproveAltOfferingViaNetwork,
    ApproveAltOfferingViaPurview,
    ApproveAltOfferingViaProductIssuerNetworkType,
    ApproveAltOfferingViaNetworkType
  )

  val TradeCapabilities = Set(
    Admin,
    TradeAltOfferingViaNetwork,
    TradeAltOfferingViaNetworkAndTradePayoffEntitlement,
    TradeAltOfferingViaNetworkType
  )

  val OpenCloseOfferingCapabilities = Set(
    Admin,
    OpenCloseAltOfferingViaNetwork,
    OpenCloseAltOfferingViaPurview,
    OpenCloseAltOfferingViaProductIssuerNetworkType,
    OpenCloseAltOfferingViaNetworkType
  )

  override val DetailedViewCapabilities = Set(
    AdminCapability,
    ViewAltOfferingViaNetworkTypeCapability,
    ViewAltOfferingViaNetworkManagerCapability,
    ViewAltOfferingViaPurviewNetworkManagerCapability,
    ViewApprovedAltOfferingViaNetworkCapability,
    ViewClosedAltOfferingViaNetworkCapability,
    ViewPendingAltOfferingViaNetworkCapability,
    ViewApprovedAltOfferingViaNetworkAndViewPayoffEntitlementCapability,
    ViewClosedAltOfferingViaNetworkAndViewPayoffEntitlementCapability,
    ViewPendingAltOfferingViaNetworkAndViewPayoffEntitlementCapability,
    ViewApprovedAltOfferingViaPurviewCapability,
    ViewClosedAltOfferingViaPurviewCapability,
    ViewPendingAltOfferingViaPurviewCapability,
    ViewApprovedAltOfferingViaPurviewAndViewPayoffEntitlementCapability,
    ViewClosedAltOfferingViaPurviewAndViewPayoffEntitlementCapability,
    ViewPendingAltOfferingViaPurviewAndViewPayoffEntitlementCapability,
    ViewAltOfferingViaProductIssuerNetworkTypeCapability,
    ViewApprovedAltOfferingViaLocationCapability,
    ViewApprovedAltOfferingViaUserCapability,
    ViewApprovedAltOfferingViaFaNumberCapability,
    ViewPendingAltOfferingViaLocationCapability,
    ViewPendingAltOfferingViaUserCapability,
    ViewPendingAltOfferingViaFaNumberCapability,
    ViewClosedAltOfferingViaLocationCapability,
    ViewClosedAltOfferingViaUserCapability,
    ViewClosedAltOfferingViaFaNumberCapability
  )

  override val DetailedEditCapabilities = Set(
    AdminCapability,
    EditAltOfferingViaNetworkTypeCapability,
    EditAltOfferingViaNetworkCapability,
    EditAltOfferingViaPurviewCapability,
    EditAltOfferingViaProductIssuerNetworkTypeCapability
  )

  val DetailedApproveCapabilities = Set(
    AdminCapability,
    ApproveAltOfferingViaNetworkManagerCapability,
    ApproveAltOfferingViaPurviewNetworkManagerCapability,
    ApproveAltOfferingViaNetworkCapability,
    ApproveAltOfferingViaPurviewCapability,
    ApproveAltOfferingViaProductIssuerNetworkTypeCapability,
    ApproveAltOfferingViaNetworkTypeCapability
  )

  val DetailedTradeCapabilities = Set(
    AdminCapability,
    TradeAltOfferingViaNetworkCapability,
    TradeAltOfferingViaNetworkAndTradePayoffEntitlementCapability,
    TradeAltOfferingViaNetworkTypeCapability
  )

  val DetailedOpenCloseOfferingCapabilities = Set(
    AdminCapability,
    OpenCloseAltOfferingViaNetworkCapability,
    OpenCloseAltOfferingViaPurviewCapability,
    OpenCloseAltOfferingViaProductIssuerNetworkTypeCapability,
    OpenCloseAltOfferingViaNetworkTypeCapability
  )

  // This method returns an undecorated key if the user is of a product owner network.
  // Otherwise it returns no keys.
  def altProductOwnerNetworkTypeKeyBuilder(capability: String, user: UserACL): Set[String] = {
    val userNetworkTypes = user.networkTypes.getOrElse(List.empty[NetworkType]).toSet
    if (userNetworkTypes.intersect(ProductIssuerNetworkTypes).nonEmpty) {
      Set(capability)
    } else {
      Set.empty[String]
    }
  }

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities ++ DetailedApproveCapabilities ++ DetailedTradeCapabilities ++ DetailedOpenCloseOfferingCapabilities

}
