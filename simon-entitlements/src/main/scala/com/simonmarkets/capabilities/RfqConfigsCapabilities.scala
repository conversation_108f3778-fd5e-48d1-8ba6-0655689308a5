package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator._
import com.simonmarkets.entitlements._

object RfqConfigsCapabilities extends Capabilities with HasDetailedViewCapabilities with HasDetailedEditCapabilities {
  override val DomainName = "RfqConfigs"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.StructuredInvestmentsAssetClass))

  val ViewRfqConfigsViaNetworkCapability: Capability = Capability("viewRfqConfigsViaNetwork", "Rfq configs view capability for user within the same network", assetClasses)
  val ViewRfqConfigsViaNetworkIssuerPurviewCapability: Capability = Capability("viewRfqConfigsViaNetworkIssuerPurview", "Allow the rfq network issuer purview to view rfq configs", assetClasses)

  override val DetailedViewCapabilities = Set(ViewRfqConfigsViaNetworkCapability, ViewRfqConfigsViaNetworkIssuerPurviewCapability, AdminCapability)
  override val DetailedEditCapabilities = Set(AdminCapability)

  val availableAccessKeysGen: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {
    override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys),
      ViewRfqConfigsViaNetworkCapability.name -> AvailableKeyBuilder(buildNetworkKeys),
      ViewRfqConfigsViaNetworkIssuerPurviewCapability.name -> AvailableKeyBuilder(buildUserPurviewKeys)
    )
  }

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities
}
