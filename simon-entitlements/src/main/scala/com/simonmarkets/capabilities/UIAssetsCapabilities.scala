package com.simonmarkets.capabilities
import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, undecoratedKeyBuilder}

object UIAssetsCapabilities extends Capabilities with AvailableAccessKeysGenerator {
  override val DomainName: String = "UIAssets"

  // UIAssets are global and should only be accessible by users in SIMON Admin so
  // byOwner or byNetwork (and the like) are not needed
  val ViewUIAssets = "viewUIAssets"
  val EditUIAssets = "editUIAssets"

  val ViewUIAssetsCapability: Capability = Capability(ViewUIAssets, "Allow user to view all UI Assets")
  val EditUIAssetsCapability: Capability = Capability(EditUIAssets, "Allow user to create and edit all UI Assets")

  val ViewUIAssetsCapabilities: Set[String] = Set(ViewUIAssets, Admin)
  val EditUIAssetsCapabilities: Set[String] = Set(EditUIAssets, Admin)

  val DetailedViewUIAssetsCapabilities: Set[Capability] = Set(ViewUIAssetsCapability, AdminCapability)
  val DetailedEditUIAssetsCapabilities: Set[Capability] = Set(EditUIAssetsCapability, AdminCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewUIAssetsCapabilities ++ DetailedEditUIAssetsCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewUIAssets -> AvailableKeyBuilder(undecoratedKeyBuilder),
    EditUIAssets -> AvailableKeyBuilder(undecoratedKeyBuilder),
  )
}
