package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.{HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

object RegisteredAnnuityOfferingsCapabilities extends Capabilities with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {
  override val DomainName = "RegisteredAnnuityOfferings"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.AnnuitiesAssetClass))

  // RegisteredAnnuity issuers should have these capabilities. Note that NetworkType.Issuer applies to product originators of all asset classes distributed on SIMON.
  val ViewRegisteredAnnuityOfferingViaNetworkTypeCapability: Capability = Capability("viewRegisteredAnnuityOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val EditRegisteredAnnuityOfferingViaNetworkTypeCapability: Capability = Capability("editRegisteredAnnuityOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val TradeRegisteredAnnuityOfferingViaNetworkTypeCapability: Capability = Capability("tradeRegisteredAnnuityOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val ApproveRegisteredAnnuityOfferingViaNetworkTypeCapability: Capability = Capability("approveRegisteredAnnuityOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val ApplyRegisteredAnnuityOfferingViaNetworkTypeCapability: Capability = Capability("applyRegisteredAnnuityOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val ViewRegisteredAnnuityOfferingViaNetworkType: String = ViewRegisteredAnnuityOfferingViaNetworkTypeCapability.name
  val EditRegisteredAnnuityOfferingViaNetworkType: String = EditRegisteredAnnuityOfferingViaNetworkTypeCapability.name
  val TradeRegisteredAnnuityOfferingViaNetworkType: String = TradeRegisteredAnnuityOfferingViaNetworkTypeCapability.name
  val ApproveRegisteredAnnuityOfferingViaNetworkType: String = ApproveRegisteredAnnuityOfferingViaNetworkTypeCapability.name
  val ApplyRegisteredAnnuityOfferingViaNetworkType: String = ApplyRegisteredAnnuityOfferingViaNetworkTypeCapability.name

  // For RegisteredAnnuity FA, FA Manager, Wholesalers
  val ViewNonRegSRegisteredAnnuityPastOfferingCapability: Capability = Capability("viewNonRegSRegisteredAnnuityPastOffering", "", assetClasses)
  val ViewNonRegSRegisteredAnnuityPastOffering: String = ViewNonRegSRegisteredAnnuityPastOfferingCapability.name

  // userACL.userPurviewIds contains a networkId this offering is offeredTo
  // Wholesaler
  val ViewRegisteredAnnuityOfferingViaPurviewCapability: Capability = Capability("viewRegisteredAnnuityOfferingViaPurview", "", assetClasses)
  val TradeRegisteredAnnuityOfferingViaPurviewCapability: Capability = Capability("tradeRegisteredAnnuityOfferingViaPurview", "", assetClasses)
  val EditRegisteredAnnuityOfferingViaPurviewCapability: Capability = Capability("editRegisteredAnnuityOfferingViaPurview", "", assetClasses)
  val ViewRegisteredAnnuityOfferingViaPurview: String = ViewRegisteredAnnuityOfferingViaPurviewCapability.name
  val TradeRegisteredAnnuityOfferingViaPurview: String = TradeRegisteredAnnuityOfferingViaPurviewCapability.name
  val EditRegisteredAnnuityOfferingViaPurview: String = EditRegisteredAnnuityOfferingViaPurviewCapability.name

  // userACL.issuerPurviewIds contains a (networkId, issuerSymbol) pair that matches the pair (a networkId in offering.offeredTo, offering.issuerSymbol)
  val ViewRegisteredAnnuityOfferingViaNetworkIssuerPurviewCapability: Capability = Capability("viewRegisteredAnnuityOfferingViaNetworkIssuerPurview", "", assetClasses) // FA Manager, Issuer, Wholesaler
  val TradeRegisteredAnnuityOfferingViaNetworkIssuerPurviewCapability: Capability = Capability("tradeRegisteredAnnuityOfferingViaNetworkIssuerPurview", "", assetClasses) // FA Manager, Issuer, Wholesaler
  val ApproveRegisteredAnnuityOfferingViaNetworkIssuerPurviewCapability: Capability = Capability("approveRegisteredAnnuityOfferingViaNetworkIssuerPurview", "", assetClasses) // FA Manager
  val ViewRegisteredAnnuityOfferingViaNetworkIssuerPurview: String = ViewRegisteredAnnuityOfferingViaNetworkIssuerPurviewCapability.name
  val TradeRegisteredAnnuityOfferingViaNetworkIssuerPurview: String = TradeRegisteredAnnuityOfferingViaNetworkIssuerPurviewCapability.name
  val ApproveRegisteredAnnuityOfferingViaNetworkIssuerPurview: String = ApproveRegisteredAnnuityOfferingViaNetworkIssuerPurviewCapability.name

  // All 4 capabilities below, as well as the user-guid, FA Number, and Location capabilities following them, apply to Reg-S offerings only if userACL.regSEligible is true.
  // If userACL.networkId matches a networkId this offering is offeredTo, and view/trade/approve action is specified in legacy FA OR FAM roles' actions.
  val ViewUnapprovedRegisteredAnnuityOfferingViaNetworkCapability: Capability = Capability("viewUnapprovedRegisteredAnnuityOfferingViaNetwork", "", assetClasses) // FA Manager
  val ViewApprovedRegisteredAnnuityOfferingViaNetworkCapability: Capability = Capability("viewApprovedRegisteredAnnuityOfferingViaNetwork", "", assetClasses) // FA, FA Manager
  val TradeRegisteredAnnuityOfferingViaNetworkCapability: Capability = Capability("tradeRegisteredAnnuityOfferingViaNetwork", "", assetClasses) // FA, FA Manager. Note that unlike in the legacy roles-based approach, now if FA Managers can trade an offering, FAs will be able to do so as well; i.e. there is no separate "tradeUnapproved" capability.
  val ApproveRegisteredAnnuityOfferingViaNetworkCapability: Capability = Capability("approveRegisteredAnnuityOfferingViaNetwork", "", assetClasses) // FA Manager
  val ApplyRegisteredAnnuityViaNetworkCapability: Capability = Capability("applyRegisteredAnnuityViaNetwork", "Allows annuity IOI and application submission for registered products", assetClasses) //launch annuity application for networks with integrated e-app
  val IllustrateRegisteredAnnuityViaPayoffEntitlementCapability: Capability = Capability("illustrateRegisteredAnnuityViaPayoffEntitlement", "", assetClasses) //illustrate annuity for products/networks with integrated illustration
  val CanSellRegisteredAnnuityViaPayoffEntitlementCapability: Capability = Capability("canSellRegisteredAnnuityViaPayoffEntitlement", "", assetClasses)
  val ViewUnapprovedRegisteredAnnuityOfferingViaNetwork: String = ViewUnapprovedRegisteredAnnuityOfferingViaNetworkCapability.name
  val ViewApprovedRegisteredAnnuityOfferingViaNetwork: String = ViewApprovedRegisteredAnnuityOfferingViaNetworkCapability.name
  val TradeRegisteredAnnuityOfferingViaNetwork: String = TradeRegisteredAnnuityOfferingViaNetworkCapability.name
  val ApproveRegisteredAnnuityOfferingViaNetwork: String = ApproveRegisteredAnnuityOfferingViaNetworkCapability.name
  val ApplyRegisteredAnnuityViaNetwork: String = ApplyRegisteredAnnuityViaNetworkCapability.name
  val IllustrateRegisteredAnnuityViaPayoffEntitlement: String = IllustrateRegisteredAnnuityViaPayoffEntitlementCapability.name
  val CanSellRegisteredAnnuityViaPayoffEntitlement: String = CanSellRegisteredAnnuityViaPayoffEntitlementCapability.name

  // userACL.userId matches an userId in offeredTo.users, with action view/trade specified respectively
  val ViewRegisteredAnnuityOfferingViaUserGuidCapability: Capability = Capability("viewRegisteredAnnuityOfferingViaUserGuid", "", assetClasses)
  val TradeRegisteredAnnuityOfferingViaUserGuidCapability: Capability = Capability("tradeRegisteredAnnuityOfferingViaUserGuid", "", assetClasses)
  val ViewRegisteredAnnuityOfferingViaUserGuid: String = ViewRegisteredAnnuityOfferingViaUserGuidCapability.name
  val TradeRegisteredAnnuityOfferingViaUserGuid: String = TradeRegisteredAnnuityOfferingViaUserGuidCapability.name

  // userACL.faNumbers contains an faNumber that matches one item in offeredTo.faNumbers.faNumber (scoped by offeredTo.networkId), with view/trade action specified respectively
  val ViewRegisteredAnnuityOfferingViaFaNumberCapability: Capability = Capability("viewRegisteredAnnuityOfferingViaFaNumber", "", assetClasses)
  val TradeRegisteredAnnuityOfferingViaFaNumberCapability: Capability = Capability("tradeRegisteredAnnuityOfferingViaFaNumber", "", assetClasses)
  val ViewRegisteredAnnuityOfferingViaFaNumber: String = ViewRegisteredAnnuityOfferingViaFaNumberCapability.name
  val TradeRegisteredAnnuityOfferingViaFaNumber: String = TradeRegisteredAnnuityOfferingViaFaNumberCapability.name

  // userACL.locations contains a location (scoped by networkId of user) that matches an item in offeredTo.locations.location (scoped by offeredTo.networkId), with view/trade action specified respectively
  val ViewRegisteredAnnuityOfferingViaLocationCapability: Capability = Capability("viewRegisteredAnnuityOfferingViaLocation", "", assetClasses)
  val TradeRegisteredAnnuityOfferingViaLocationCapability: Capability = Capability("tradeRegisteredAnnuityOfferingViaLocation", "", assetClasses)
  val ViewRegisteredAnnuityOfferingViaLocation: String = ViewRegisteredAnnuityOfferingViaLocationCapability.name
  val TradeRegisteredAnnuityOfferingViaLocation: String = TradeRegisteredAnnuityOfferingViaLocationCapability.name

  // userACL.cusips matches the offeredTo.networkId and CUSIP of the offering
  val ViewRegisteredAnnuityOfferingViaCusipCapability: Capability = Capability("viewRegisteredAnnuityOfferingViaCusip", "", assetClasses)
  val TradeRegisteredAnnuityOfferingViaCusipCapability: Capability = Capability("tradeRegisteredAnnuityOfferingViaCusip", "", assetClasses)
  val ViewRegisteredAnnuityOfferingViaCusip: String = ViewRegisteredAnnuityOfferingViaCusipCapability.name
  val TradeRegisteredAnnuityOfferingViaCusip: String = TradeRegisteredAnnuityOfferingViaCusipCapability.name

  val ApproveCapabilities = Set(Admin, ApproveRegisteredAnnuityOfferingViaNetwork, ApproveRegisteredAnnuityOfferingViaNetworkIssuerPurview)
  val TradeCapabilities = Set(Admin, TradeRegisteredAnnuityOfferingViaNetworkType, TradeRegisteredAnnuityOfferingViaNetwork, TradeRegisteredAnnuityOfferingViaPurview, TradeRegisteredAnnuityOfferingViaNetworkIssuerPurview, TradeRegisteredAnnuityOfferingViaUserGuid, TradeRegisteredAnnuityOfferingViaFaNumber, TradeRegisteredAnnuityOfferingViaLocation, TradeRegisteredAnnuityOfferingViaCusip)
  val ApplyCapabilities = Set(Admin, ApplyRegisteredAnnuityViaNetwork)
  val IllustrateCapabilities = Set(Admin, IllustrateRegisteredAnnuityViaPayoffEntitlement)

  override val DetailedViewCapabilities = Set(AdminCapability, ViewRegisteredAnnuityOfferingViaNetworkTypeCapability, ViewNonRegSRegisteredAnnuityPastOfferingCapability, ViewUnapprovedRegisteredAnnuityOfferingViaNetworkCapability, ViewApprovedRegisteredAnnuityOfferingViaNetworkCapability, ViewRegisteredAnnuityOfferingViaPurviewCapability, ViewRegisteredAnnuityOfferingViaNetworkIssuerPurviewCapability, ViewRegisteredAnnuityOfferingViaUserGuidCapability, ViewRegisteredAnnuityOfferingViaFaNumberCapability, ViewRegisteredAnnuityOfferingViaLocationCapability, ViewRegisteredAnnuityOfferingViaCusipCapability)
  override val DetailedEditCapabilities = Set(EditRegisteredAnnuityOfferingViaNetworkTypeCapability, EditRegisteredAnnuityOfferingViaPurviewCapability, AdminCapability)
  val DetailedApproveCapabilities = Set(AdminCapability, ApproveRegisteredAnnuityOfferingViaNetworkCapability, ApproveRegisteredAnnuityOfferingViaNetworkIssuerPurviewCapability, ApproveRegisteredAnnuityOfferingViaNetworkTypeCapability)
  val DetailedTradeCapabilities = Set(AdminCapability, TradeRegisteredAnnuityOfferingViaNetworkTypeCapability, TradeRegisteredAnnuityOfferingViaNetworkCapability, TradeRegisteredAnnuityOfferingViaPurviewCapability, TradeRegisteredAnnuityOfferingViaNetworkIssuerPurviewCapability, TradeRegisteredAnnuityOfferingViaUserGuidCapability, TradeRegisteredAnnuityOfferingViaFaNumberCapability, TradeRegisteredAnnuityOfferingViaLocationCapability, TradeRegisteredAnnuityOfferingViaCusipCapability)
  val DetailedApplyCapabilities = Set(AdminCapability, ApplyRegisteredAnnuityViaNetworkCapability, ApplyRegisteredAnnuityOfferingViaNetworkTypeCapability)
  val DetailedIllustrateCapabilities = Set(AdminCapability, IllustrateRegisteredAnnuityViaPayoffEntitlementCapability)
  val DetailedCanSellCapabilities = Set(AdminCapability, CanSellRegisteredAnnuityViaPayoffEntitlementCapability)

  override def toDetailedCapabilitySet: Set[Capability] =
    DetailedViewCapabilities ++
      DetailedEditCapabilities ++
      DetailedApproveCapabilities ++
      DetailedTradeCapabilities ++
      DetailedApplyCapabilities ++
      DetailedIllustrateCapabilities ++
      DetailedCanSellCapabilities
}
