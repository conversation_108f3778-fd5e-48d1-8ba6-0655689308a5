package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.buildAdminKeys
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}


object CryptoAccountsCapabilities extends Capabilities with AvailableAccessKeysGenerator with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {
  override val DomainName: String = "CryptoAccounts"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.DigitalAssetClass))

  val ViewCryptoAccountViaNetwork: String = "viewCryptoAccountViaNetwork"
  val EditCryptoAccountViaNetwork: String = "editCryptoAccountViaNetwork"

  val ViewCryptoAccountViaNetworkCapability: Capability = Capability(ViewCryptoAccountViaNetwork,
    "Allows one to view a CryptoAccount if one's Network Id matches the CryptoAccount's Network Id", assetClasses)
  val EditCryptoAccountViaNetworkCapability: Capability = Capability(EditCryptoAccountViaNetwork,
    "Allows one to edit a CryptoAccount if one's Network Id matches the CryptoAccount's Network Id", assetClasses)


  override val ViewCapabilities: Set[String] = Set(Admin, ViewCryptoAccountViaNetwork)
  override val EditCapabilities: Set[String] = Set(Admin, EditCryptoAccountViaNetwork)

  override def DetailedViewCapabilities: Set[Capability] = Set(AdminCapability, ViewCryptoAccountViaNetworkCapability)

  override def DetailedEditCapabilities: Set[Capability] = Set(AdminCapability, EditCryptoAccountViaNetworkCapability)

  override def toSet: Set[String] = ViewCapabilities ++ EditCapabilities

  def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewCryptoAccountViaNetwork -> availableKeyBuilder.add(_.networkId),
    EditCryptoAccountViaNetwork -> availableKeyBuilder.add(_.networkId),
  )
}