package com.simonmarkets.capabilities

import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.undecoratedKeyBuilder
import com.simonmarkets.entitlements._

object SalesFeeCapabilities extends Capabilities with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {
  override val DomainName: String = "Fees"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.StructuredInvestmentsAssetClass))

  val ViewFeeFaCommissionCapability: Capability = Capability("ViewFeeFaCommission", "", assetClasses)
  val ViewFeeHomeOfficeCommissionCapability: Capability = Capability("ViewFeeHomeOfficeCommission", "", assetClasses)
  val ViewFeeWholesalersCommissionCapability: Capability = Capability("ViewFeeWholesalersCommission", "", assetClasses)
  val ViewFeeReOfferCapability: Capability = Capability("ViewFeeReOffer", "", assetClasses)
  val OverrideFeeFaCommissionCapability: Capability = Capability("OverrideFeeFaCommission", "", assetClasses)
  val OverrideFeeHomeOfficeCommissionCapability: Capability = Capability("OverrideFeeHomeOfficeCommission", "", assetClasses)
  val OverrideFeeWholesalersCommissionCapability: Capability = Capability("OverrideFeeWholesalersCommission", "", assetClasses)
  val OverrideFeeReOfferCapability: Capability = Capability("OverrideFeeReOffer", "", assetClasses)

  val ViewFeeFaCommission: String = ViewFeeFaCommissionCapability.name
  val ViewFeeHomeOfficeCommission: String = ViewFeeHomeOfficeCommissionCapability.name
  val ViewFeeWholesalersCommission: String = ViewFeeWholesalersCommissionCapability.name
  val ViewFeeReOffer: String = ViewFeeReOfferCapability.name
  val OverrideFeeFaCommission: String = OverrideFeeFaCommissionCapability.name
  val OverrideFeeHomeOfficeCommission: String = OverrideFeeHomeOfficeCommissionCapability.name
  val OverrideFeeWholesalersCommission: String = OverrideFeeWholesalersCommissionCapability.name
  val OverrideFeeReOffer: String = OverrideFeeReOfferCapability.name

  override val DetailedViewCapabilities: Set[Capability] = Set(ViewFeeFaCommissionCapability, ViewFeeHomeOfficeCommissionCapability, ViewFeeWholesalersCommissionCapability, ViewFeeReOfferCapability)
  override val DetailedEditCapabilities: Set[Capability] = Set(OverrideFeeFaCommissionCapability, OverrideFeeHomeOfficeCommissionCapability, OverrideFeeWholesalersCommissionCapability, OverrideFeeReOfferCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities

  val availableAccessKeysGen: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {

    override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      ViewFeeFaCommission -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewFeeHomeOfficeCommission -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewFeeWholesalersCommission -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewFeeReOffer -> AvailableKeyBuilder(undecoratedKeyBuilder),
      OverrideFeeFaCommission -> AvailableKeyBuilder(undecoratedKeyBuilder),
      OverrideFeeHomeOfficeCommission -> AvailableKeyBuilder(undecoratedKeyBuilder),
      OverrideFeeWholesalersCommission -> AvailableKeyBuilder(undecoratedKeyBuilder),
      OverrideFeeReOffer -> AvailableKeyBuilder(undecoratedKeyBuilder)
    )
  }
}
