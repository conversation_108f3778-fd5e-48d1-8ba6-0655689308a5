package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.buildAdminKeys
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

object StructuredETFCapabilities extends Capabilities with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {
  override val DomainName = "StructuredETF"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.DefinedOutcomeETFsAssetClass))

  val STRUCTURED_ETF_PAYOFF_TYPE = "StructuredETF"

  val ViewStructuredETFViaViewPayoffEntitlementCapability: Capability = Capability("viewStructuredETFViaViewPayoffEntitlement", "", assetClasses)
  val EditStructuredETFViaEditPayoffEntitlementCapability: Capability = Capability("editStructuredETFViaEditPayoffEntitlement", "", assetClasses)

  val ViewStructuredETFViaViewPayoffEntitlement:String = ViewStructuredETFViaViewPayoffEntitlementCapability.name
  val EditStructuredETFViaEditPayoffEntitlement:String = EditStructuredETFViaEditPayoffEntitlementCapability.name

  override val DetailedViewCapabilities = Set(AdminCapability, ViewStructuredETFViaViewPayoffEntitlementCapability)
  override val DetailedEditCapabilities = Set(AdminCapability, EditStructuredETFViaEditPayoffEntitlementCapability)

  val availableAccessKeysGen: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {
    override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys),
      ViewStructuredETFViaViewPayoffEntitlement -> AvailableKeyBuilder(etfPayoffEntitlementKeysForView),
      EditStructuredETFViaEditPayoffEntitlement -> AvailableKeyBuilder(etfPayoffEntitlementKeysForEdit)
    )
  }

  private def etfPayoffEntitlementKeysForView(capability: String, userACL: UserACL): Set[String] = {
    etfPayoffEntitlementKeysForAction(capability, userACL)("view")
  }

  private def etfPayoffEntitlementKeysForEdit(capability: String, userACL: UserACL): Set[String] = {
    etfPayoffEntitlementKeysForAction(capability, userACL)("edit")
  }

  private def etfPayoffEntitlementKeysForAction(capability: String, userACL: UserACL)(action: String): Set[String] =
    AvailableAccessKeysGenerator.payoffEntitlementKeysForPayoffTypeAndAction(capability, userACL)(STRUCTURED_ETF_PAYOFF_TYPE, action)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities
}
