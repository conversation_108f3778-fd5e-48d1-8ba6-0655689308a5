package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildNetworkKeys}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities}

object RfqTagsCapabilities extends Capabilities with HasDetailedViewCapabilities with HasDetailedEditCapabilities {
  override val DomainName = "RfqTags"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.StructuredInvestmentsAssetClass))

  val ViewRfqTagsViaNetworkCapability: Capability = Capability("viewRfqTagsViaNetwork", "Rfq tags view capability for user within the same network", assetClasses)

  val EditRfqTagsViaNetworkCapability: Capability = Capability("editRfqTagsViaNetwork", "Rfq tags edit capability for user within the same network", assetClasses)

  override val DetailedViewCapabilities = Set(ViewRfqTagsViaNetworkCapability, AdminCapability)
  override val DetailedEditCapabilities = Set(EditRfqTagsViaNetworkCapability, AdminCapability)

  val availableAccessKeysGen: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {
    override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys),
      ViewRfqTagsViaNetworkCapability.name -> AvailableKeyBuilder(buildNetworkKeys),
      EditRfqTagsViaNetworkCapability.name -> AvailableKeyBuilder(buildNetworkKeys)
    )
  }

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities
}

