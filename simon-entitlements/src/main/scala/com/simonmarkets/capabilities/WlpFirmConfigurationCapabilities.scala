package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildFirmKeys, buildWhiteLabelPartnerAndFirmKeys, buildWhiteLabelPartnerKeys}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

object WlpFirmConfigurationCapabilities extends Capabilities with AvailableAccessKeysGenerator with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {
  override val DomainName: String = "Learn"

  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Platform))

  val ViewWlpFirmConfigurationViaWlpCapability: Capability = Capability("viewWlpFirmConfigurationViaWlp", "View WLP or Firm configuration details by matching a user's white label partner id", assetClasses)
  val ViewWlpFirmConfigurationViaWlpAndFirmCapability: Capability = Capability("viewWlpFirmConfigurationViaWlpAndFirm", "View WLP or Firm configuration details by matching a user's white label partner id and firm id", assetClasses)
  val ViewWlpFirmConfigurationViaFirmCapability: Capability = Capability("viewWlpFirmConfigurationViaFirm", "View WLP or Firm configuration details by matching a user's firm id", assetClasses)

  val ViewWlpFirmConfigurationViaWlp: String = ViewWlpFirmConfigurationViaWlpCapability.name
  val ViewWlpFirmConfigurationViaWlpAndFirm: String = ViewWlpFirmConfigurationViaWlpAndFirmCapability.name
  val ViewWlpFirmConfigurationViaFirm: String = ViewWlpFirmConfigurationViaFirmCapability.name

  override val ViewCapabilities: Set[String] = Set(Admin, ViewWlpFirmConfigurationViaWlp, ViewWlpFirmConfigurationViaWlpAndFirm, ViewWlpFirmConfigurationViaFirm)
  override val EditCapabilities: Set[String] = Set(Admin)
  override val DetailedViewCapabilities: Set[Capability] = Set(AdminCapability, ViewWlpFirmConfigurationViaWlpCapability, ViewWlpFirmConfigurationViaWlpAndFirmCapability, ViewWlpFirmConfigurationViaFirmCapability)
  override val DetailedEditCapabilities: Set[Capability] = Set(AdminCapability)

  override def toSet: Set[String] = ViewCapabilities ++ EditCapabilities

  def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewWlpFirmConfigurationViaWlp -> AvailableKeyBuilder(buildWhiteLabelPartnerKeys),
    ViewWlpFirmConfigurationViaWlpAndFirm -> AvailableKeyBuilder(buildWhiteLabelPartnerAndFirmKeys),
    ViewWlpFirmConfigurationViaFirm -> AvailableKeyBuilder(buildFirmKeys),
  )
}
