package com.simonmarkets.capabilities

case class Capability(
    name: String,
    description: String,
    assetClass: Option[Seq[String]] = None,
    linkedToCapability: Option[String] = None
)

case class CapabilitySet(capabilities: Set[Capability]) {
  def toCapabilityNames: Set[String] = {
    capabilities.map(_.name)
  }

  def getCapabilitiesByAssetClass(assetClass: String): Set[Capability] = {
    capabilities.filter(capability => capability.assetClass.exists(_.contains(assetClass)))
  }
}

object CapabilitySet {

  def apply(capabilities: Capability*): CapabilitySet = CapabilitySet(capabilities.toSet)

  def empty: CapabilitySet = CapabilitySet()

}
