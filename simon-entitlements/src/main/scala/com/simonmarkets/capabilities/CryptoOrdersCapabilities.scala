package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.buildAdminKeys
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

object CryptoOrdersCapabilities extends Capabilities with AvailableAccessKeysGenerator with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {
  override val DomainName: String = "CryptoOrders"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.DigitalAssetClass))

  val ViewCryptoOrderViaNetwork: String = "viewCryptoOrderViaNetwork"
  val EditCryptoOrderViaNetwork: String = "editCryptoOrderViaNetwork"

  val ViewCryptoOrderViaNetworkCapability: Capability = Capability(ViewCryptoOrderViaNetwork,
    "Allows one to view a CryptoOrder if one's Network Id matches the CryptoOrder's Network Id", assetClasses)
  val EditCryptoOrderViaNetworkCapability: Capability = Capability(EditCryptoOrderViaNetwork,
    "Allows one to edit a CryptoOrder if one's Network Id matches the CryptoOrder's Network Id", assetClasses)

  override val ViewCapabilities: Set[String] = Set(Admin, ViewCryptoOrderViaNetwork)
  override val EditCapabilities: Set[String] = Set(Admin, EditCryptoOrderViaNetwork)

  override def DetailedViewCapabilities: Set[Capability] = Set(AdminCapability, ViewCryptoOrderViaNetworkCapability)

  override def DetailedEditCapabilities: Set[Capability] = Set(AdminCapability, EditCryptoOrderViaNetworkCapability)

  override def toSet: Set[String] = ViewCapabilities ++ EditCapabilities

  def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewCryptoOrderViaNetwork -> availableKeyBuilder.add(_.networkId),
    EditCryptoOrderViaNetwork -> availableKeyBuilder.add(_.networkId),
  )
}
