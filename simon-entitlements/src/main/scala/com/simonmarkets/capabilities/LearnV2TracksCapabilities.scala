package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildNetworkTypeKeys}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder}

object LearnV2TracksCapabilities extends Capabilities with AvailableAccess<PERSON>eysGenerator {
  override val DomainName: String = "LearnV2Tracks"

  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Platform))

  val ViewAllTracksViaNetworkType: String = "viewAllTracksViaNetworkType"
  val EditAllTracksViaNetworkType: String = "editAllTracksViaNetworkType"
  val ViewAllTracksViaNetworkTypeCapability: Capability = Capability(ViewAllTracksViaNetworkType, "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val EditAllTracksViaNetworkTypeCapability: Capability = Capability(EditAllTracksViaNetworkType, "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)

  val ViewActiveTracksViaNetworkType: String = "viewActiveTracksViaNetworkType"
  val ViewActiveTracksViaNetworkTypeCapability: Capability = Capability(ViewActiveTracksViaNetworkType, "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)

  val ViewActiveTracksViaTrackId: String = "viewActiveTracksViaTrackId"
  val ViewAllTracksViaTrackId: String = "viewAllTracksViaTrackId"

  val ViewActiveTracksViaTrackIdCapability: Capability = Capability(ViewActiveTracksViaTrackId, "", assetClasses)
  val ViewAllTracksViaTrackIdCapability: Capability = Capability(ViewAllTracksViaTrackId, "", assetClasses)

  val ViewActiveTrackCapabilities: Set[String] = Set(ViewActiveTracksViaTrackId, ViewActiveTracksViaNetworkType, Admin)
  val ViewAllTrackCapabilities: Set[String] = Set(ViewAllTracksViaTrackId, ViewAllTracksViaNetworkType, Admin)
  val EditTrackCapabilities: Set[String] = Set(Admin, EditAllTracksViaNetworkType)
  val DeleteTrackCapabilities: Set[String] = Set(Admin)

  val DetailedViewActiveTrackCapabilities: Set[Capability] = Set(ViewActiveTracksViaTrackIdCapability, ViewActiveTracksViaNetworkTypeCapability, AdminCapability)
  val DetailedViewAllTrackCapabilities: Set[Capability] = Set(ViewAllTracksViaTrackIdCapability, EditAllTracksViaNetworkTypeCapability, ViewAllTracksViaNetworkTypeCapability, AdminCapability)
  val DetailedEditTrackCapabilities: Set[Capability] = Set(AdminCapability, EditAllTracksViaNetworkTypeCapability)
  val DetailedDeleteTrackCapabilities: Set[Capability] = Set(AdminCapability)

  override def toDetailedCapabilitySet: Set[Capability] =
    DetailedViewActiveTrackCapabilities ++ DetailedViewAllTrackCapabilities ++ DetailedEditTrackCapabilities ++ DetailedDeleteTrackCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewAllTracksViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
    EditAllTracksViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
    ViewActiveTracksViaTrackId -> AvailableKeyBuilder(buildActiveTrackIdKeys),
    ViewAllTracksViaTrackId -> AvailableKeyBuilder(buildAllTrackIdKeys)
  )

  def buildActiveTrackIdKeys(capability: String, userACL: UserACL): Set[String] = {
    userACL.learnTracksV2.filter(_.isActive).map(track => s"$capability:${track.trackId}").toSet
  }

  def buildAllTrackIdKeys(capability: String, userACL: UserACL): Set[String] = {
    userACL.learnTracksV2.map(track => s"$capability:${track.trackId}").toSet
  }
}
