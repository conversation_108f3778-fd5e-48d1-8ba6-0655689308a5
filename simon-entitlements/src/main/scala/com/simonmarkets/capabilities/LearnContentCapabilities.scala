package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildNetworkTypeKeys}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

object LearnContentCapabilities extends Capabilities with AvailableAccessKeysGenerator
  with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {

  override val DomainName: String = "LearnContent"

  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Platform))

  val ViewContentViaNetworkTypeCapability: Capability = Capability("viewContentViaNetworkTypeCapability", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val EditContentViaNetworkTypeCapability: Capability = Capability("editContentViaNetworkTypeCapability", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)

  val ViewContentViaContentId: String = "ViewContentViaContentId"
  val ViewContentViaNetwork: String = "ViewContentViaNetwork"
  val ViewContentViaPurview: String = "ViewContentViaPurview"
  val EditContentViaNetwork: String = "EditContentViaNetwork"
  val EditContentViaPurview: String = "EditContentViaPurview"
  val ExternalShareViaEndClientShareableId: String = "ExternalShareViaEndClientShareableId"
  val InternalShareViaContentId: String = "InternalShareViaContentId"
  val ViewContentViaNetworkType: String = ViewContentViaNetworkTypeCapability.name
  val EditContentViaNetworkType: String = EditContentViaNetworkTypeCapability.name

  val ViewContentViaContentIdCapability: Capability = Capability(ViewContentViaContentId, "", assetClasses)
  val ViewContentViaNetworkCapability: Capability = Capability(ViewContentViaNetwork, "", assetClasses)
  val ViewContentViaPurviewCapability: Capability = Capability(ViewContentViaPurview, "", assetClasses)
  val EditContentViaNetworkCapability: Capability = Capability(EditContentViaNetwork, "", assetClasses)
  val EditContentViaPurviewCapability: Capability = Capability(EditContentViaPurview, "", assetClasses)
  val ExternalShareViaEndClientShareableIdCapability: Capability = Capability(ExternalShareViaEndClientShareableId, "", assetClasses)
  val InternalShareViaContentIdCapability: Capability = Capability(InternalShareViaContentId, "", assetClasses)

  val EndClientShareContentCapabilities: Set[String] = Set(Admin, ExternalShareViaEndClientShareableId)
  val ShareContentCapabilities: Set[String] = Set(Admin, InternalShareViaContentId)

  override val DetailedEditCapabilities: Set[Capability] = Set(
    AdminCapability,
    EditContentViaNetworkTypeCapability,
    EditContentViaNetworkCapability,
    EditContentViaPurviewCapability
  )
  override val DetailedViewCapabilities: Set[Capability] = Set(
    AdminCapability,
    ViewContentViaNetworkTypeCapability,
    ViewContentViaContentIdCapability,
    ViewContentViaNetworkCapability,
    ViewContentViaPurviewCapability
  )

  val DetailedEndClientShareContentCapabilities: Set[Capability] = Set(AdminCapability, ExternalShareViaEndClientShareableIdCapability)
  val DetailedShareContentCapabilities: Set[Capability] = Set(AdminCapability, InternalShareViaContentIdCapability)

  override def toDetailedCapabilitySet: Set[Capability] =
    DetailedEditCapabilities ++
    DetailedViewCapabilities ++
    DetailedEndClientShareContentCapabilities ++
    DetailedShareContentCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewContentViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
    EditContentViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
    ViewContentViaContentId -> availableKeyBuilder.addList(_.learnContent),
    ViewContentViaNetwork -> availableKeyBuilder.add(_.networkId),
    ViewContentViaPurview -> availableKeyBuilder.add(_.networkId),
    EditContentViaNetwork -> availableKeyBuilder.add(_.networkId),
    EditContentViaPurview -> availableKeyBuilder.add(_.networkId),
    ExternalShareViaEndClientShareableId -> availableKeyBuilder.addList(_.endUserShareableContent),
    InternalShareViaContentId -> availableKeyBuilder.addList(_.learnContent)
  )
}
