package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.AdminCapability
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildNetworkTypeKeys}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder}

object LearnV2QuestionCapabilities extends Capabilities with AvailableAccessKeysGenerator {
  override val DomainName: String = "LearnQuestionCapabilities"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Platform))

  val ViewQuestionCapabilitiesViaNetworkTypeCapability: Capability = Capability("viewQuestionCapabilitiesViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val EditQuestionCapabilitiesViaNetworkTypeCapability: Capability = Capability("editQuestionCapabilitiesViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)

  val ViewQuestionCapabilities: Set[String] = Set(Capabilities.Admin, ViewQuestionCapabilitiesViaNetworkTypeCapability.name)
  val EditQuestionCapabilities: Set[String] = Set(Capabilities.Admin, EditQuestionCapabilitiesViaNetworkTypeCapability.name)
  val VerifyQuestionCapabilities: Set[String] = Set(Capabilities.Admin)

  val DetailedViewQuestionCapabilities: Set[Capability] = Set(AdminCapability, ViewQuestionCapabilitiesViaNetworkTypeCapability)
  val DetailedEditQuestionCapabilities: Set[Capability] = Set(AdminCapability, EditQuestionCapabilitiesViaNetworkTypeCapability)
  val DetailedVerifyQuestionCapabilities: Set[Capability] = Set(AdminCapability)

  override val capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Capabilities.Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewQuestionCapabilitiesViaNetworkTypeCapability.name -> AvailableKeyBuilder(buildNetworkTypeKeys),
    EditQuestionCapabilitiesViaNetworkTypeCapability.name -> AvailableKeyBuilder(buildNetworkTypeKeys),
  )

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewQuestionCapabilities ++ DetailedEditQuestionCapabilities ++ DetailedVerifyQuestionCapabilities
}
