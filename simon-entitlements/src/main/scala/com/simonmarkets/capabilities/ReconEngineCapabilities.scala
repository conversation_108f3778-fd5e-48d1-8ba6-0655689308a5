package com.simonmarkets.capabilities

import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildNetworkKeys}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedViewCapabilities, HasViewCapabilities}

object ReconEngineCapabilities extends Capabilities with AvailableAccessKeysGenerator with HasViewCapabilities with HasDetailedViewCapabilities {
  override val DomainName: String = "ReconEngine"

  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.ReconEngine))

  val ViewReconViaNetworkCapability: Capability = Capability("viewReconViaNetwork", "Enables user to view recons within the recond engine based on their network", assetClasses)
  val ViewReconViaPurviewCapability: Capability = Capability("viewReconViaPurview", "Enables user to view recons within the recond engine based on their purview", assetClasses)

  val ViewReconViaNetwork: String = ViewReconViaNetworkCapability.name
  val ViewReconViaPurview: String = ViewReconViaPurviewCapability.name

  override def DetailedViewCapabilities: Set[Capability] = Set(ViewReconViaNetworkCapability, ViewReconViaPurviewCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    ViewReconViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    ViewReconViaPurview -> AvailableKeyBuilder(buildNetworkKeys)
  )
}