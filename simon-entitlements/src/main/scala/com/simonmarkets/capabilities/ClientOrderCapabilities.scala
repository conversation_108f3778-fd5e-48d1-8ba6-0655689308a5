package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildGuidKeys, buildNetworkKeys}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities}

object ClientOrderCapabilities extends Capabilities with HasDetailedViewCapabilities
  with HasDetailedEditCapabilities with AvailableAccessKeysGenerator {
  override val DomainName: String = "ClientOrders"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.StructuredInvestmentsAssetClass))

  val ViewClientOrdersViaNetworkCapability: Capability = Capability("viewClientOrdersViaNetwork", "view Client Orders Via Network", assetClasses)
  val EditClientOrdersViaNetworkCapability: Capability = Capability("editClientOrdersViaNetwork", "edit Client Orders Via Network", assetClasses)
  val ViewClientOrdersViaOwnerCapability: Capability = Capability("viewClientOrdersViaOwner", "view Client Orders Via Owner", assetClasses)
  val EditClientOrdersViaOwnerCapability: Capability = Capability("editClientOrdersViaOwner", "edit Client Orders Via Owner", assetClasses)

  val ViewClientOrdersViaNetwork: String = ViewClientOrdersViaNetworkCapability.name
  val EditClientOrdersViaNetwork: String = EditClientOrdersViaNetworkCapability.name
  val ViewClientOrdersViaOwner: String = ViewClientOrdersViaOwnerCapability.name
  val EditClientOrdersViaOwner: String = EditClientOrdersViaOwnerCapability.name

  override val DetailedViewCapabilities: Set[Capability] = Set(AdminCapability, ViewClientOrdersViaNetworkCapability, ViewClientOrdersViaOwnerCapability)
  override val DetailedEditCapabilities: Set[Capability] = Set(AdminCapability, EditClientOrdersViaNetworkCapability, EditClientOrdersViaOwnerCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewClientOrdersViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    EditClientOrdersViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    ViewClientOrdersViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    EditClientOrdersViaOwner -> AvailableKeyBuilder(buildGuidKeys),
  )
}
