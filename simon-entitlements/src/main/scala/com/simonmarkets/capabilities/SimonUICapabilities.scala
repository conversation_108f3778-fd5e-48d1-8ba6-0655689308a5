package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.AssetClasses._
import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability, ReadOnlyAdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator._
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder}

/*
* Unlike with most other domains, keys generated for the UI domain generally don't need UserACL properties embedded in them.
* This is because the UI "resources" that these keys grant access to aren't specific to a user.
* canSeeSPPortal is enough to grant access to a front end page so canSeeSPPortal:${user.networkId} adds little value
* if there is no custom page per network.
*/
object SimonUICapabilities extends Capabilities {
  override val DomainName = "UI"

  //ALTS
  val ViewUILearningCenterV2AlternativeInvestmentsCapability: Capability =  Capability("viewUILearningCenterV2AlternativeInvestments","Gives access to the Alternative Investments Learning Center.", Some(Seq(AlternativesAssetClass)), Some("canSeeLearningCenterV2AlternativeInvestments"))
  val ViewUILearningCenterV3AlternativeInvestmentsCapability: Capability =  Capability("viewUILearningCenterV3AlternativeInvestments","Gives access to the Alternative Investments Learning Center.", Some(Seq(AlternativesAssetClass)), Some("canSeeLearningCenterV3AlternativeInvestments"))
  val ViewUIAltsPortalCapability: Capability =  Capability("viewUIAltsPortal","Gives access to SIMON's Alternative Investments platform.", Some(Seq(AlternativesAssetClass)), Some("canSeeUIAltsPortal"))
  val ViewUIPlusSubscribeAltsCapability: Capability =  Capability("viewUIPlusSubscribeAlts","Gives access to the +Subscribe iframe flow.", Some(Seq(AlternativesAssetClass)), Some("canSeePlusSubscribeAlts"))
  val ViewUIAltsPerformanceCapability: Capability =  Capability("ViewUIAltsPerformance","Gives access to alts performance data via UI.", Some(Seq(AlternativesAssetClass)), Some("canSeeAltsPerformance"))
  val ViewUIAltsPendingOfferingsCapability: Capability =  Capability("viewUIAltsPendingOfferings","Gives access to a marketplace tab for pending offerings (for managers).", Some(Seq(AlternativesAssetClass)), Some("canSeeAltsPendingOfferings"))
  val ViewUIAltsStagedOfferingsCapability: Capability =  Capability("viewUIAltsStagedOfferings","Gives access to a marketplace tab for staged offerings (for managers).", Some(Seq(AlternativesAssetClass)), Some("canSeeAltsStagedOfferings"))
  val ViewUIAltsClosedOfferingsCapability: Capability =  Capability("viewUIAltsClosedOfferings","Gives access to a marketplace tab for closed offerings.", Some(Seq(AlternativesAssetClass)), Some("canSeeAltsClosedOfferings"))
  val ViewUICustomAltsHomepageCapability: Capability = Capability("viewUICustomAltsHomepage","Gives access to a custom Alts homepage originally built for AMPF.",Some(Seq(AlternativesAssetClass)), Some("canSeeCustomAltsHomepage"))
  val ViewUILearningCenterV2AlternativesMyNetworkEducationCapability: Capability = Capability("viewUILearningCenterV2AlternativesMyNetworkEducation","Gives access to the Alternative Investments Learning Center: My Network widget.", Some(Seq(AlternativesAssetClass)), Some("canSeeLearningCenterV2AlternativesMyNetworkEducation"))
  val ViewUILearningCenterV3AlternativesMyNetworkEducationCapability: Capability = Capability("viewUILearningCenterV3AlternativesMyNetworkEducation","Gives access to the Alternative Investments Learning Center: My Network widget.", Some(Seq(AlternativesAssetClass)), Some("canSeeLearningCenterV3AlternativesMyNetworkEducation"))
  val ViewUIIAltsSubscriptionWorkflowCapability: Capability = Capability("viewUIAltsSubscriptionWorkflow", "Gives access to Alts subscription workflow via iCapital's core platform.", Some(Seq(AlternativesAssetClass)), Some("canSeeAltsSubscriptionWorkflow"))
  val ViewUIICapitalAltOfferingsLinkCapability: Capability = Capability("viewUIICapitalAltOfferingsLink", "Gives access to link to iCapital Alternative Offerings", Some(Seq(AlternativesAssetClass)), Some("canSeeICapitalAltOfferingsLink"))

  //SIs
  val ViewNewSILifecycleDashboardCapability:Capability = Capability("viewNewSILifecycleDashboard" ,"",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeNewSILifecycleDashboard"))
  val ViewUIContractExportToExcelCapability:Capability = Capability("viewUIContractExportToExcel", "Gives access to a button allowing users to export a contract to excel", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeContractExportToExcel"))
  val ViewUILearningCenterV2StructuredInvestmentsCapability:Capability = Capability("viewUILearningCenterV2StructuredInvestments","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeLearningCenterV2StructuredInvestments"))
  val ViewUILearningCenterV3StructuredInvestmentsCapability:Capability = Capability("viewUILearningCenterV3StructuredInvestments","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeLearningCenterV3StructuredInvestments"))
  val ViewUISPPortalCapability:Capability = Capability("viewUISPPortal","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeSPPortal"))
  val ViewUISIMarketplaceRatesCapability: Capability = Capability("viewUISIMarketplaceRates","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeSIMarketplaceRates"))
  val ViewUISIEquitiesMarketplaceCapacityCapability: Capability = Capability("viewUISIEquitiesMarketplaceCapacity", "", Some(Seq(StructuredInvestmentsAssetClass, Platform)), Some("canSeeSIEquitiesMarketplaceCapacity"))
  val ViewUISIRatesMarketplaceCapacityCapability: Capability = Capability("viewUISIRatesMarketplaceCapacity", "", Some(Seq(StructuredInvestmentsAssetClass, Platform)), Some("canSeeSIRatesMarketplaceCapacity"))
  val ViewUISIIssuerPortalSuspendCapability: Capability = Capability("viewUISIIssuerPortalSuspend", "Allow issuer to view and edit orders suspend status", Some(Seq(StructuredInvestmentsAssetClass, Platform)), Some("canSeeSIIssuerPortalSuspend"))
  val ViewUISIIssuerPortalCapacityCapability: Capability = Capability("viewUISIIssuerPortalCapacity", "Allow issuer to view and edit orders capacity", Some(Seq(StructuredInvestmentsAssetClass, Platform)), Some("canSeeSIIssuerPortalCapacity"))
  val ViewUISIDollarCommissionCapability: Capability = Capability("viewUISIDollarCommission", "Allow user to view dollar commission in the marketplace and IOIs", Some(Seq(StructuredInvestmentsAssetClass, Platform)), Some("canSeeSIDollarCommission"))
  val ViewUIIssuerPortalCapability:Capability = Capability("viewUIIssuerPortal","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeIssuerPortal"))
  val ViewUIWholesalerPortalCapability:Capability = Capability("viewUIWholesalerPortal","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeWholesalerPortal"))
  val ViewBuilderCapability:Capability = Capability("viewBuilderViaBuildPayoffEntitlements","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeBuilder"))
  val ViewUIHeatmapCapability:Capability = Capability("viewUIHeatmap","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeHeatmap"))
  val ViewUIBacktestingCapability:Capability = Capability("viewUIBacktestingViaBacktestPayOffEntitlements","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeBacktesting"))
  val ViewUISPDetailsCapability:Capability = Capability("viewUISPDetailsViaDetailsPayoffEntitlements","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeSPDetails"))
  val ViewUILearningCenterMyNetworkCapability:Capability = Capability("viewUILearningCenterMyNetwork","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeLearningCenterMyNetwork"))
  val ViewUILifecyclePortalRollsActionsCapability:Capability = Capability("viewUILifecyclePortalRollsActions", "Gives visibility to Roll Pricing CTA in Lifecycle Portal", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeLifecyclePortalRollsActions"))
  val ViewUICloseOutCapability:Capability = Capability("viewUICloseOut","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeCloseOut"))
  val ViewUISPOrdersCloseoutWorkflowCapability:Capability = Capability("viewUISPOrdersCloseoutWorkflow","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeSPOrdersCloseoutWorkflow"))
  val ViewUISPOrdersAuditCapability: Capability = Capability("ViewUISPOrdersAudit", "Allow user to see View Audit Trail button for ioi's", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeSPOrdersAudit"))
  val ViewUIRfqNewSubmissionLayoutCapability:Capability = Capability("viewUIRfqNewSubmissionLayout", "Gives access to new RFQ submission layout", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqNewSubmissionLayout"))
  val ViewUIRfqIssuerNewSubmissionLayoutCapability:Capability = Capability("viewUIRfqIssuerNewSubmissionLayout", "Gives access to new RFQ submission layout for Issuers", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqIssuerNewSubmissionLayout"))
  val ViewUIRfqNewCardLayoutCapability:Capability = Capability("viewUIRfqNewCardLayout", "Gives access to new RFQ card layout", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqNewCardLayout"))
  val ViewUIRfqIssuerNewCardLayoutCapability:Capability = Capability("viewUIRfqIssuerNewCardLayout", "Gives access to new RFQ card layout for Issuers", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqIssuerNewCardLayout"))
  val ViewUIRfqCopyToClipboardCapability: Capability = Capability("viewUIRfqCopyToClipboard", "Gives access to copy quotes/auctions to clipboard feature", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqCopyToClipboard"))
  val ViewUIRfqHomeOfficeCapability:Capability = Capability("viewUIRfqHomeOffice","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqAsHomeOffice"))
  val ViewUIRfqIssuerCapability:Capability = Capability("viewUIRfqIssuer","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqAsIssuer"))
  val ViewUIRfqMultiIssuerCapability:Capability = Capability("viewUIRfqMultiIssuer","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeMultiIssuerRFQ"))
  val ViewUIRfqFACapability:Capability = Capability("viewUIRfqFA","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqAsFA"))
  val ViewUIRfqWholesalerCapability:Capability = Capability("viewUIRfqWholesaler","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqAsWholesaler"))
  val ViewUIRfqGenericsModalCapability:Capability = Capability("viewUIRfqGenericsModal","Give access to RFQ generics modal via UI",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqGenericsModal"))
  val ViewUIRfqCreateTemplateForNetworkCapability: Capability = Capability("viewUIRfqCreateTemplateForNetwork", "Allow user to view the create rfq template for network button", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqCreateTemplateForNetwork"))
  val ViewUIRfqQuantoCompoFieldCapability:Capability = Capability("viewUIRfqQuantoCompoField","Give access to RFQ quanto/compo field via UI",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqQuantoCompoField"))
  val ViewUIRfqStrikeDateFieldCapability: Capability = Capability("viewUIRfqStrikeDateField","Give access to RFQ strike date field via UI",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqStrikeDateField"))
  val ViewUIRfqSettlementDateFieldCapability: Capability = Capability("viewUIRfqSettlementDateField","Give access to RFQ settlement date field via UI",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqSettlementDateField"))
  val ViewUIRfqPricingFeedbackCapability: Capability = Capability("viewUIRfqPricingFeedbackField", "Give access to RFQ pricing feedback via UI", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqPricingFeedback"))
  val ViewUIRfqWholesalerInfoCapability: Capability = Capability("viewUIRfqWholesalerInfo", "Gives access to view wholesaler fields on RFQ modals", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqWholesalerInfo"))
  val ViewUIRfqDeclineReasonsCapability: Capability = Capability("viewUIRfqDeclineReasons", "Gives access to view decline reason", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqDeclineReason"))
  val ViewUIRfqRulesEngineResultsCapability: Capability = Capability("viewUIRfqRulesEngineResults", "Gives access to view rules engine result", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqRulesEngineResults"))
  val ViewUIRfqCrowdsourcingPortalCapability: Capability = Capability("viewUIRfqCrowdsourcingPortal", "Gives access to view RFQ crowdsourcing portal", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqCrowdsourcingPortal"))
  val ViewUIRfqQuoteDetailsCapability: Capability = Capability("viewUIRfqQuoteDetails", "Gives access to quote details link", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqQuoteDetails"))
  val ViewUIRfqAuditTrailCapability: Capability = Capability("viewUIRfqAuditTrail", "Gives access to audit trail link", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqAuditTrail"))
  val ViewUIRfqIdeaGenerationCapability: Capability = Capability("viewUIRfqIdeaGeneration", "Gives access to rfq idea generation tabs", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqIdeaGeneration"))
  val ViewUIRfqCreateTemplateCapability: Capability = Capability("viewUIRfqCreateTemplate", "Gives access to create rfq template", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqCreateTemplate"))
  val ViewUIRfqSubmitButtonCapability: Capability = Capability("viewUIRfqSubmitButton", "Gives access to submit rfq button", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqSubmitButton"))
  val ViewUIRfqTemplateNameInPlaceOfDisplayNameCapability: Capability = Capability("viewUIRfqTemplateNameInPlaceOfDisplayName", "Gives access to see the template name in place of display name", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqTemplateNameInPlaceOfDisplayName"))
  val ViewUIRfqTemplateRequestToTradeCapability: Capability = Capability("viewUIRfqTemplateRequestToTrade", "Gives access to see request to trade button in templates tab", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqTemplateRequestToTrade"))
  val ViewUIRfqTemplateVariationCapability: Capability = Capability("viewUIRfqTemplateVariation", "Gives access to create template variations in RFQ", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqTemplateVariation"))
  val ViewUIRfqDocumentReviewWorkflowCapability: Capability = Capability("viewUIRfqDocumentReviewWorkflow", "Gives access to rft document review workflow in RFQ Portal", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqDocumentReviewWorkflow"))
  val ViewUIRfqToContractOfferingWorkflowCapability: Capability = Capability("viewUIRfqToContractOfferingWorkflow", "Gives access to rfq to contract offering workflow", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqToContractOfferingWorkflow"))
  val ViewUIRfqDealTypeSelectionCapability: Capability = Capability("viewUIRfqDealTypeSelection", "Gives access to select deal type on rfq", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqDealTypeSelection"))
  val ViewUIRfqAveragingPaymentDetailsCapability: Capability = Capability("ViewUIRfqAveragingPaymentDetails", "Gives access to see averaging payment details in rfq", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqAveragingPaymentDetails"))
  val ViewUIRfqJpmPbCapability: Capability = Capability("viewUIRfqJpmPb", "Gives access to see JPM PB specific elements", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqJpmPb"))
  val ViewUIRfqModal54WeekToggleCapability: Capability = Capability("viewUIRfqModal54WeekToggle", "Gives visibility to a months/weeks toggle in RFQ modals for Term Value", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqModal54WeekToggle"))
  val ViewUIRfqDenominationCapability: Capability = Capability("viewUIRfqDenomination", "Gives access to see the denomination", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqDenomination"))
  val ViewUIRfqSortedByLevelCapability: Capability = Capability("viewUIRfqSortedByLevel", "Gives access to view rfqs in the same auction sorted by level", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqSortedByLevel"))
  val ViewUIRfqRequestFirmQuoteCapability: Capability = Capability("viewUIRfqRequestFirmQuote", "Gives access to Request Firm Quote CTA in Active RFQs tab", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqRequestFirmQuote"))
  val ViewUIRfqCloningCapability: Capability = Capability("viewUIRfqCloning", "Gives access to Cloning CTA in RFQ portal", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqCloning"))
  val ViewUIRfqExternalIdInputCapability: Capability = Capability("viewUIRfqExternalIdInput", "Gives access to input external ID in RFQ portal", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqExternalIdInput"))
  val ViewUIRfqContractUploadedStateCapability: Capability = Capability("viewUIRfqContractUploadedState", "Gives access to ContractUploaded RFTs in RFQ portal", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqContractUploadedState"))
  val ViewUIRfqModalAutopopulateFACapability: Capability = Capability("viewUIRfqModalAutopopulateFA", "Gives access to autopopulate fa in new rfq modal", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqModalAutopopulateFA"))
  val ViewUIRfqModalAutopopulateNetworkCapability: Capability = Capability("viewUIRfqModalAutopopulateNetwork", "Gives access to autopopulate network in new rfq modal", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqModalAutopopulateNetwork"))
  val ViewUIRfqSolvedRangeCapability: Capability = Capability("viewUIRfqSolvedRange", "Gives access to see the solved range column", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqSolvedRange"))
  val ViewUIRfqOptInPricingFeedbackByDefaultCapability: Capability = Capability("viewUIRfqOptInPricingFeedbackByDefault", "Opts in pricing feedback option by default", Some(Seq(StructuredInvestmentsAssetClass)), Some("isOptInRfqPricingFeedback"))
  val ViewUIBuilderRfqButtonCapability: Capability = Capability("viewUIBuilderRfqButton", "Gives access to view the rfq button in builder", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeBuilderRfqButton"))
  val ViewUIRfqIdeaGenerationTabCapability: Capability = Capability("viewUIRfqIdeaGenerationTab", "Gives access to rfq idea generation tab", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqIdeaGenerationTab"))
  val ViewUIRfqPendingIdeaTabCapability: Capability = Capability("viewUIRfqPendingIdeaTab", "Gives access to rfq pending idea tab", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqPendingIdeaTab"))
  val ViewUIRfqActiveRfqsTabCapability: Capability = Capability("viewUIRfqActiveRfqsTab", "Gives access to active rfqs tab", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqActiveRfqsTab"))
  val ViewUIRfqActiveRftsTabCapability: Capability = Capability("viewUIRfqActiveRftsTab", "Gives access to active rfts tab", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeRfqActiveRftsTab"))
  val ViewUIBuilderV2ViaBuildPayoffEntitlementCapability:Capability = Capability("viewUIBuilderV2ViaBuildPayoffEntitlement","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeBuilderV2"))
  val ViewUILifecyclePortalSPCapability:Capability = Capability("viewUILifecyclePortalSP","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeLifecyclePortal"))
  val ViewUILifecyclePortalSPShareViaLinkCapability:Capability = Capability("viewUILifecyclePortalSPShareViaLink","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeLifecyclePortalShareViaLink"))
  val ViewUILifecyclePortalSPEditNetworkDefaultCapability:Capability = Capability("viewUILifecyclePortalSPEditNetworkDefault","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeLifecyclePortalEditNetworkDefault"))
  val ViewUILifecyclePortalSPWidgetContractOfferingsCapability:Capability = Capability("viewUILifecyclePortalSPWidgetContractOfferings","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeLifecyclePortalSPWidgetContractOfferings"))
  val ViewUILifecyclePortalSPBarrierAnalysisWidgetCapability:Capability = Capability("viewUILifecyclePortalSPBarrierAnalysisWidget","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeLifecyclePortalSPBarrierAnalysisWidget"))
  val ViewUILifecyclePortalSPCapAnalysisWidgetCapability:Capability = Capability("viewUILifecyclePortalSPCapAnalysisWidget","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeLifecyclePortalSPCapAnalysisWidget"))
  val ViewUILifecyclePortalSPSectorAllocationWidgetCapability:Capability = Capability("viewUILifecyclePortalSPSectorAllocationWidget","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeLifecyclePortalSPSectorAllocationWidget"))
  val ViewUILifecyclePortalSPUnderlierPerformanceWidgetCapability:Capability = Capability("viewUILifecyclePortalSPUnderlierPerformanceWidget","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeLifecyclePortalSPUnderlierPerformanceWidget"))
  val ViewUILifecyclePortalSIClientsWidgetCapability:Capability = Capability("viewUILifecyclePortalSIClientsWidget", " Allow user to view SI clients dashboard in Lifecycle Portal", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeLifecyclePortalSIClientsWidget"))
  val ViewUILifecyclePortalPartialSearchCapability: Capability = Capability("viewUILifecyclePortalPartialSearch", "Allow user to view autocomplete search fields in Lifecycle Portal", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeLifecyclePortalPartialSearch"))
  val ViewUILifecyclePortalPartitionedQueryCapability: Capability = Capability("viewUILifecyclePortalPartitionedQuery", "Allow user to access Lifecycle Portal data via partitioned queries", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeLifecyclePortalPartitionedQuery"))
  val ViewUISIEquitiesLifecycleDTCCPaymentCapability: Capability = Capability("viewUISIEquitiesLifecycleDTCCPayment", "Allow user to view DTCC event payments for equities in Lifecycle Portal", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeViewUISIEquitiesLifecycleDTCCPayment"))
  val ViewUISIEquitiesBufferedLeveredNoteLifecycleDTCCPaymentCapability: Capability = Capability("viewUISIEquitiesBufferedLeveredNoteLifecycleDTCCPayment", "Allow user to view BufferedLeveredNotes DTCC event payments in Lifecycle Portal and emails", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeViewUISIEquitiesBufferedLeveredNoteLifecycleDTCCPayment"))
  val ViewUIViewAsLifecycleSPCapability:Capability = Capability("viewUIViewAsLifecycleSP","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeViewAsLifecycleSP"))
  val ViewUIOverrideSPOfferingsBooksCloseTimeCapability:Capability = Capability("viewUIOverrideSPOfferingsBooksCloseTime","",Some(Seq(StructuredInvestmentsAssetClass)), Some("canOverrideSPOfferingBooksCloseTime"))
  val ViewUICalculatedLifecycleEventsStatusCapability:Capability = Capability("viewUICalculatedLifecycleEventsStatus", "Allow distributor to view Simon calculated lifecycle event status", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeCalculatedLifecycleEventsStatus"))
  val ViewUICalculatedLifecycleEventsStatusEmailCapability:Capability = Capability("viewUICalculatedLifecycleEventsStatusEmail", "Allow distributor to view Simon calculated lifecycle event status in the notification email", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeCalculatedLifecycleEventsStatusEmail"))
  val ViewUILifecycleNotificationSettingsCapability:Capability = Capability("viewUILifecycleNotificationSettings", "Gives access to view lifecycle notification settings", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeLifecycleNotificationSettings"))
  val ViewUIEditNetworkLifecycleNotificationSettingsCapability:Capability = Capability("viewUIEditNetworkLifecycleNotificationSettings", "Gives access to edit network lifecycle notification settings", Some(Seq(StructuredInvestmentsAssetClass)), Some("canEditNetworkLifecycleNotificationSettings"))
  val ViewUIEstimatedEodValuationsCapability: Capability = Capability("viewUIEstimatedEodValuations", "Gives access to view SIMON estimated EOD valuations", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeEstimatedEodValuations"))
  val ViewUINewLifecycleEmailsAccountDetailsCapability: Capability = Capability("viewUINewLifecycleEmailsAccountDetails", "Gives access to view new lifecycle emails account details", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeNewLifecycleEmailsAccountDetails"))
  val ViewUISIOrdersEnhancedCapability: Capability = Capability("viewUISIOrdersEnhancedCapability", "Enhanced SI Orders page, backed by ubertable", Some(Seq(StructuredInvestmentsAssetClass)))
  val ViewUIMultiDocsInIssuerPortalCapability: Capability = Capability("viewUIMultiDocsInIssuerPortalCapability", "Enables multi-language document management features within the issuer portal", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeUIMultiDocsInIssuerPortal"))
  val ViewUIMultiCurrencyInLifecyclePortalCapability: Capability = Capability("ViewUIMultiCurrencyInLifecyclePortal", "Enables multi-currency features within the Lifecycle Portal", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeUIMultiCurrencyInLifecyclePortal"))
  val ViewUIBatchUnentitleCapability: Capability = Capability("viewUIBatchUnentitle", "Enables un-entitling multiple CUSIPs via Batch Approve Modal in the Marketplace", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeBatchUnentitle"))
  val ViewUISIUnblockedPerformanceAnalysisCapability: Capability = Capability("viewUISIUnblockedPerformanceAnalysis", "Enables users to view unblocked performance analysis tab in investment details", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeSIUnblockedPerformanceAnalysis"))
  val ViewUISIAIUploadsCapability: Capability = Capability("viewUISIAIUploadsCapability", "Enables users to view AI Uploads", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeSIAIUploads"))
  val ViewUISITradeTminus2Capability: Capability = Capability("viewUISITradeTminus2Capability", "Enables users to see T Minus 2 in IOI portal banner", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeSITradeTMinus2"))
  val ViewUISIAdditionalDetailsCardCapability: Capability = Capability("viewUISIAdditionalDetailsCardCapability", "Enables users to see Additional Details section in Investment Details", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeSIAdditionalDetailsCard"))
  val ViewUISIContractsOfferingsUpdatesApiCapability: Capability = Capability("viewUISIContractsOfferingsUpdatesApi", "Enables pointing GET requests to the 2025 SI contracts offerings rewrite", Some(Seq(StructuredInvestmentsAssetClass)), Some("canUseSIContractsOfferingsUpdatesApi"))
  val ViewUISIContractsOfferingsUbertableCapability: Capability = Capability("viewUISIContractsOfferingsUbertable", "Enables pointing POST/DELETE requests in the 2025 SI contracts offerings rewrite", Some(Seq(StructuredInvestmentsAssetClass)), Some("canUseSIContractsOfferingsUbertable"))
  val ViewUISIContractsOfferingsBulkApisCapability: Capability = Capability("viewUISIContractsOfferingsBulkApis", "Enables GET/POST to bulk upload and history, new to the 2025 SI contracts offerings rewrite", Some(Seq(StructuredInvestmentsAssetClass)), Some("canUseSIContractsOfferingsBulkApis"))
  val ViewUISIContractsOfferingsApprovalApiCapability: Capability = Capability("viewUISIContractsOfferingsApprovalApi", "Enables GET/POST to bulk upload and history, new to the 2025 SI contracts offerings rewrite", Some(Seq(StructuredInvestmentsAssetClass)), Some("canUseSIContractsOfferingsApprovalApi"))
  val ViewUISIPortfolioPDFCapability: Capability = Capability("viewUISIPortfolioPDF", "Enables users to generate the portfolio pdf", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeSIPortfolioPDF"))
  val ViewUISIPortfolioPDFClientNameCapability: Capability = Capability("viewUISIPortfolioPDFClientName", "Enables users to add a client name to the portfolio pdf", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeSIPortfolioPDFClientName"))
  val ViewUISIPortfolioPDFCusipLevelCapability: Capability = Capability("viewUISIPortfolioPDFCusipLevel", "Enables users to generate portfolio pdfs at the cusip level", Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeSIPortfolioPDFCusipLevel"))

  //ANNUITIES
  val ViewUILearningCenterV2AnnuitiesCapability: Capability = Capability("viewUILearningCenterV2Annuities","", Some(Seq(AnnuitiesAssetClass)), Some("canSeeLearningCenterV2Annuities"))
  val ViewUILearningCenterV3AnnuitiesCapability: Capability = Capability("viewUILearningCenterV3Annuities","", Some(Seq(AnnuitiesAssetClass)), Some("canSeeLearningCenterV3Annuities"))
  val ViewUILearningCenterV2AnnuitiesMyNetworkEducationCapability: Capability = Capability("viewUILearningCenterV2AnnuitiesMyNetworkEducation","", Some(Seq(AnnuitiesAssetClass)), Some("canSeeLearningCenterV2AnnuitiesMyNetworkEducation"))
  val ViewUILearningCenterV3AnnuitiesMyNetworkEducationCapability: Capability = Capability("viewUILearningCenterV3AnnuitiesMyNetworkEducation","", Some(Seq(AnnuitiesAssetClass)), Some("canSeeLearningCenterV3AnnuitiesMyNetworkEducation"))
  val ViewUILifecyclePortalAnnuitiesCapability: Capability = Capability("viewUILifecyclePortalAnnuities","", Some(Seq(AnnuitiesAssetClass)), Some("canSeeViewUILifecyclePortalAnnuities"))
  val ViewUIOptimizationToolAnnuityPDFCapability:Capability = Capability("viewUIOptimizationToolAnnuityPDF","", Some(Seq(AnnuitiesAssetClass)), Some("canSeeOptimizationToolAnnuityPDF"))
  val ViewUIImpersonateAnnuityMarketplaceCapability:Capability = Capability("viewUIImpersonateAnnuityMarketplace","", Some(Seq(AnnuitiesAssetClass)), Some("canImpersonateAnnuityMarketplace"))
  val ViewUIEnhancedAnnuityOrderCapability:Capability = Capability("viewUIEnhancedAnnuityOrder","", Some(Seq(AnnuitiesAssetClass)), Some("canSeeUIEnhancedAnnuityOrder"))
  val ViewUIFIAPortalCapability:Capability = Capability("viewUIFIAPortal","",Some(Seq(AnnuitiesAssetClass)), Some("canSeeFIAPortal"))
  val ViewUISVAPortalCapability:Capability = Capability("viewUISVAPortal","",Some(Seq(AnnuitiesAssetClass)), Some("canSeeSVAPortal"))
  val ViewUICarrierPortalRateUploadCapability:Capability = Capability("viewUICarrierPortalRateUpload","",Some(Seq(AnnuitiesAssetClass)), Some("canSeeCarrierPortalRateUpload"))
  val ViewUICarrierPortalMorningStarMappingUploadCapability:Capability = Capability("viewUICarrierPortalMorningStarMappingUpload", "", Some(Seq(AnnuitiesAssetClass)), Some("canSeeCarrierPortalMorningStarMappingUpload"))
  val ViewUIFAPortalCapability:Capability = Capability("viewUIFAPortal","",Some(Seq(AnnuitiesAssetClass)), Some("canSeeFAPortal"))
  val ViewUIVAPortalCapability:Capability = Capability("viewUIVAPortal","",Some(Seq(AnnuitiesAssetClass)), Some("canSeeVAPortal"))
  val ViewUIFAPortalYieldToSurrenderCapability:Capability = Capability("viewUIFAPortalYieldToSurrender","",Some(Seq(AnnuitiesAssetClass)), Some("canSeeFAPortalYieldToSurrender"))
  val ViewUIIncomeBacktestingToolCapability:Capability = Capability("viewUIIncomeBacktestingTool","",Some(Seq(AnnuitiesAssetClass)), Some("canSeeAnnuityIncomeBacktestingTool"))
  val ViewUIAnnuitiesSaveAllocationsCapability:Capability = Capability("viewUIAnnuitiesSaveAllocationsCapability", "", Some(Seq(AnnuitiesAssetClass)), Some("canSeeUIAnnuitiesSaveAllocationsCapability"))
  val ViewUIHideIncomeCalculatorCapability : Capability = Capability("viewUIHideIncomeCalculatorCapability", "Determines if the UI should hide the income calculator from the user", Some(Seq(AnnuitiesAssetClass)), Some("canSeeUIHideIncomeCalculatorCapability"))
  val HideUIBacktestingComparisonCapability: Capability = Capability("hideUIBacktestingComparison", "", Some(Seq(AnnuitiesAssetClass)), Some("canHideBacktestingComparison"))
  val ViewUIHideSIMONBrandingCapability : Capability = Capability("viewUIHideSIMONBrandingCapability", "Determines if the UI should hide all SIMON references from the user", Some(Seq(AnnuitiesAssetClass)), Some("canSeeUIHideSIMONBrandingCapability"))
  val HideUIAllocationSummaryCapability: Capability = Capability("hideUIAllocationSummaryCapability", "Determines if the UI should disable the Allocation Summary button for the user", Some(Seq(AnnuitiesAssetClass)), Some("canHideUIAllocationSummaryCapability"))
  val ViewUIVAEnhancedExperience : Capability = Capability("viewUIVAEnhancedExperience", "Determines if the UI should show the new VA Flow", Some(Seq(AnnuitiesAssetClass)), Some("canSeeVAEnhancedFlow"))
  val ViewUIFixedAnnuityRatesWidgetCapability : Capability = Capability("viewUIFixedAnnuityRatesWidget", "Allows users to view and interact with the Fixed Annuity Rates Widget on the LCP Dashboard", Some(Seq(AnnuitiesAssetClass)), Some("canSeeFixedAnnuityRatesWidget"))
  val ViewUIFeeAnalysisWidgetCapability: Capability = Capability("viewUIFeeAnalysisWidget", "Allows users to view and interact with the Fee Analysis Widget on the LCP Dashboard", Some(Seq(AnnuitiesAssetClass)), Some("canSeeFeeAnalysisWidget"))
  val ViewUIIncomeRiderNoWithdrawalWidgetCapability: Capability = Capability("viewUIIncomeRiderNoWithdrawalWidget", "Allows users to view and interact with the Income Riders & No Withdrawals Widget on the LCP Dashboard", Some(Seq(AnnuitiesAssetClass)), Some("canSeeIncomeRiderNoWithdrawalWidget"))
  val ViewUIBenefitsAnalysisWidgetCapability: Capability = Capability("viewUIBenefitsAnalysisWidget", "Allows users to view and interact with the Benefits Analysis Widget on the LCP Dashboard", Some(Seq(AnnuitiesAssetClass)), Some("canSeeBenefitsAnalysisWidget"))
  val ViewUIAnnuityExplorerWidgetCapability: Capability = Capability("viewUIAnnuityExplorerWidget", "Allows users to view and interact with the Annuity Explorer Widget on the LCP Dashboard", Some(Seq(AnnuitiesAssetClass)), Some("canSeeAnnuityExplorerWidget"))
  val ViewUIAnnuityClientAgeDistributionWidgetCapability: Capability = Capability("viewUIAnnuityClientAgeDistributionWidget", "Allows users to view and interact with the Client Age Distribution Widget on the LCP Dashboard", Some(Seq(AnnuitiesAssetClass)), Some("canSeeAnnuityClientAgeDistributionWidget"))
  val ViewUIAnnuityLowAccountValueWidgetCapability: Capability = Capability("viewUIAnnuityLowAccountValueWidget", "Allows users to view and interact with the Low Account Value Widget on the LCP Dashboard", Some(Seq(AnnuitiesAssetClass)), Some("canSeeAnnuityLowAccountValueWidget"))
  val ViewUIAnnuityFullApprovalModalCapability: Capability = Capability("viewUIAnnuityFullApprovalModal", "Allows user to view and interact with the Full Approval button on the Approval Modal", Some(Seq(AnnuitiesAssetClass)), Some("canSeeAnnuityFullApprovalModal"))
  val ViewUIAnnuitiesLifecycleFANumberSearchCapability: Capability = Capability("viewUIAnnuitiesLifecycleFANumberSearch", "Allows user to view and interact with the FA number search bar at the top of Annuities Lifecycle", Some(Seq(AnnuitiesAssetClass)), Some("canSeeAnnuitiesLifecycleFANumberSearch"))
  val ViewUIFixedAnnuitiesRatesOutputPDFCapability: Capability = Capability("viewUIFixedAnnuitiesRatesOutputPDF", "Allows user to view and interact with the Download Rates button on the FA Marketplace", Some(Seq(AnnuitiesAssetClass)), Some("canSeeFixedAnnuitiesRatesOutputPDF"))
  val ViewUIAnnuityLifecycleNotificationCapability: Capability = Capability("viewUIAnnuityLifecycleNotification", "Allows user to view and interact with the Annuity Lifecycle Email Notification Subscription buttons on LCP Dashboard", Some(Seq(AnnuitiesAssetClass)), Some("canSeeAnnuityLifecycleNotification"))
  val ViewUIAllocationBacktestingV2Capability: Capability = Capability("viewUIAllocationBacktestingV2", "Allows user to view the all features of allocation backtesting V2", Some(Seq(AnnuitiesAssetClass)), Some("canSeeAllocationBacktestingV2"))
  val ViewUIRILAIndexStrategyBacktestingPDFCapability: Capability = Capability("viewUIRILAIndexStrategyBacktestingPDF", "Allows users to view the RILA Index Strategy Backtesting PDF", Some(Seq(AnnuitiesAssetClass)), Some("canSeeRILAIndexStrategyBacktestingPDF"))

  //INSURANCE
  val ViewUILifecyclePortalInsuranceCapability: Capability = Capability("viewUILifecyclePortalInsurance", "Allows user to view and interact with the Insurance Lifecycle Portal", Some(Seq(InsuranceAssetClass)), Some("canSeeLifecyclePortalInsurance"))

  //Integrations / Partners
  val ViewUIFIDxCarrierPortalCapability: Capability = Capability("viewUIFIDxCarrierPortal", "", Some(Seq(AnnuitiesAssetClass)), Some("canSeeFIDxCarrierPortal"))
  val ViewUIEnvestnetCapability: Capability = Capability("viewUIEnvestnetCapability", "Access to Envestnet integrations", Some(Seq(StructuredInvestmentsAssetClass)))
  val ViewUIFIDxActivityWorkflowCapability: Capability = Capability("viewUIFIDxActivityWorkflowCapability", "Allows users to view and interact with the FIDx Illustrations-Proposal modals and workflow", Some(Seq(AnnuitiesAssetClass)), Some("canSeeFIDxActivityWorkflow"))
  val ViewUIOnlyCommissionOnSimonCapability: Capability = Capability("viewUIOnlyCommissionOnSimon", "Allows the user to see only commission-based annuity policies on the Annuity Marketplace when accessing SIMON directly", Some(Seq(AnnuitiesAssetClass)), Some("canSeeOnlyCommissionOnSimon"))
  val ViewUIOffPlatformAnnuitiesCapability: Capability = Capability("viewUIOffPlatformAnnuities", "Allows the user to access the Off-Platform Annuities Marketplace tab", Some(Seq(AnnuitiesAssetClass)), Some("canSeeOffPlatformAnnuities"))

  //DEFINED OUTCOME ETFS
  val ViewUILearningCenterV2DefinedOutcomeETFsCapability:Capability = Capability("viewUILearningCenterV2DefinedOutcomeETFs","",Some(Seq(DefinedOutcomeETFsAssetClass)), Some("canSeeLearningCenterV2DefinedOutcomeETFs"))
  val ViewUILearningCenterV3DefinedOutcomeETFsCapability:Capability = Capability("viewUILearningCenterV3DefinedOutcomeETFs","",Some(Seq(DefinedOutcomeETFsAssetClass)), Some("canSeeLearningCenterV3DefinedOutcomeETFs"))
  val ViewUIDefinedOutcomeETFCapability:Capability = Capability("viewUIDefinedOutcomeETF","",Some(Seq(DefinedOutcomeETFsAssetClass)), Some("canSeeDefinedOutcomeETF"))
  val ViewUIDefinedOutcomeETFViewAsCapability:Capability= Capability("viewUIDefinedOutcomeETFViewAs","",Some(Seq(DefinedOutcomeETFsAssetClass)), Some("canSeeDefinedOutcomeETFViewAs"))
  val ViewUIDefinedOutcomeETFUpsideShieldCapability:Capability = Capability("viewUIDefinedOutcomeETFUpsideShield","",Some(Seq(DefinedOutcomeETFsAssetClass)), Some("canSeeDefinedOutcomeETFUpsideShield"))
  val ViewUIDefinedOutcomeETFPerformanceAnalysisCapability:Capability = Capability("viewUIDefinedOutcomeETFPerformanceAnalysis","",Some(Seq(DefinedOutcomeETFsAssetClass)), Some("canSeeDefinedOutcomeETFPerformanceAnalysis"))
  val ViewUIDefinedOutcomeETFComparisonCalculatorCapability:Capability = Capability("viewUIDefinedOutcomeETFComparisonCalculator","",Some(Seq(DefinedOutcomeETFsAssetClass)), Some("canSeeDefinedOutcomeETFComparisonCalculator"))
  val ViewUILearningCenterV2DefinedOutcomeETFsMyNetworkEducationCapability:Capability = Capability("viewUILearningCenterV2DefinedOutcomeETFsMyNetworkEducation","",Some(Seq(DefinedOutcomeETFsAssetClass)), Some("canSeeLearningCenterV2DefinedOutcomeETFsMyNetworkEducation"))
  val ViewUILearningCenterV3DefinedOutcomeETFsMyNetworkEducationCapability:Capability = Capability("viewUILearningCenterV3DefinedOutcomeETFsMyNetworkEducation","",Some(Seq(DefinedOutcomeETFsAssetClass)), Some("canSeeLearningCenterV3DefinedOutcomeETFsMyNetworkEducation"))
  val ViewUIStructuredETFIssuerPortalCapability:Capability = Capability("viewUIStructuredETFIssuerPortal","",Some(Seq(DefinedOutcomeETFsAssetClass)), Some("canSeeDefinedOutcomeETFIssuerPortal"))
  val ViewUIStructuredETFPendingOfferingsCapability:Capability = Capability("viewUIStructuredETFPendingOfferings","",Some(Seq(DefinedOutcomeETFsAssetClass)), Some("canSeePendingDefinedOutcomeETFOfferings"))
  val ViewUIDefinedOutcomeETFMPlusFundsCapability:Capability = Capability("viewUIDefinedOutcomeETFMPlusFunds", "Allows the user to see m+ funds", Some(Seq(DefinedOutcomeETFsAssetClass, Platform)), Some("canSeeDefinedOutcomeETFMPlusFunds"))
  val ViewUILearningCenterV2DefinedOutcomeETFMPlusFundsCapability:Capability = Capability("viewUILearningCenterV2DefinedOutcomeETFMPlusFundsCapability", "Allow the user to see m+ funds in the Learning Center", Some(Seq(DefinedOutcomeETFsAssetClass, Platform)), Some("canSeeLearningCenterV2DefinedOutcomeETFMPlusFunds"))
  val ViewUILearningCenterV3DefinedOutcomeETFMPlusFundsCapability:Capability = Capability("viewUILearningCenterV3DefinedOutcomeETFMPlusFundsCapability", "Allow the user to see m+ funds in the Learning Center", Some(Seq(DefinedOutcomeETFsAssetClass, Platform)), Some("canSeeLearningCenterV3DefinedOutcomeETFMPlusFunds"))

  //SMAS/BETAPLUS
  val ViewUISMAAccountPortalCapability:Capability = Capability("viewUISMAAccountPortal","", Some(Seq(SMAsAssetClass)), Some("canSeeSMAAccountPortal"))
  val ViewUISMASelectAccountStrategyCapability:Capability = Capability("viewUISMASelectAccountStrategy","", Some(Seq(SMAsAssetClass)), Some("canSeeSMASelectAccountStrategy"))
  val ViewUIBetaPlusMarketplaceCapability:Capability = Capability("viewUIBetaPlusMarketplace","", Some(Seq(SMAsAssetClass)), Some("canSeeBetaPlusMarketplace"))

  //DIGITAL ASSETS
  val ViewUILearningCenterV2DigitalAssetsCapability:Capability = Capability("viewUILearningCenterV2DigitalAssets","", Some(Seq(DigitalAssetClass, AlternativesAssetClass)), Some("canSeeLearningCenterV2DigitalAssets")) // Digital Asset LC needs to show up in Alts-mode too
  val ViewUILearningCenterV3DigitalAssetsCapability:Capability = Capability("viewUILearningCenterV3DigitalAssets","", Some(Seq(DigitalAssetClass, AlternativesAssetClass)), Some("canSeeLearningCenterV3DigitalAssets")) // Digital Asset LC needs to show up in Alts-mode too
  val ViewUIDigitalAssetsPortalCapability: Capability = Capability("viewUIDigitalAssetsPortalCapability", "", Some(Seq(DigitalAssetClass)), Some("canSeeDigitalAssetsPortal"))

  //PLATFORM
  val ViewUISPManagementDashboardCapability:Capability = Capability("viewUISPManagementDashboard","",Some(Seq(Platform)), Some("canSeeManagementDashboard"))
  val ViewUIMultipleNetworksCapability:Capability = Capability("viewUIMultipleNetworks","",Some(Seq(Platform)), Some("canViewMultipleNetworks"))
  val ViewUIBroadcastInNetworkOnlyCapability:Capability = Capability("viewUIBroadcastInNetworkOnly","",Some(Seq(Platform)), Some("canBroadcastInNetworkOnly"))
  val ViewUIBroadcastOutsideNetworkCapability:Capability = Capability("viewUIBroadcastOutsideNetwork","",Some(Seq(Platform)), Some("canBroadcastOutsideNetwork"))
  val ViewUIExportCurrentOfferingNetworkIOIsCapability:Capability = Capability("viewUIExportCurrentOfferingNetworkIOIs","",Some(Seq(Platform)), Some("canExportCurrentOfferingNetworkIOIs"))
  val ViewUIShareOutsideNetworkCapability:Capability = Capability("viewUIShareOutsideNetwork","",Some(Seq(Platform)), Some("canShareOutsideNetwork"))
  val ViewUIUseBrokerDealerCapability:Capability = Capability("viewUIUseBrokerDealer","",Some(Seq(Platform)), Some("canUseBrokerDealer"))
  val ViewUILearningCenterDetailsCapability:Capability = Capability("viewUILearningCenterDetails","",Some(Seq(Platform)), Some("canSeeLearningCenterDetails"))
  val ViewUILearningCenterCapability:Capability = Capability("viewUILearningCenter","",Some(Seq(Platform)), Some("canSeeLearningCenter"))
  val ViewUIHoldingsCapability:Capability = Capability("viewUIHoldings","",Some(Seq(Platform)), Some("canSeeHoldings"))
  val ViewUIRIAPopUpCapability:Capability = Capability("viewUIRIAPopUpViaNetworkType","",Some(Seq(Platform)), Some("canSeeRIAPopUp"))
  val ViewUIHoldingsDocUploadCapability:Capability = Capability("viewUIHoldingsDocUpload","",Some(Seq(Platform)), Some("canSeeHoldingsDocUpload"))
  val ViewUIClientHoldingsGroupByHouseholdCapability:Capability = Capability("viewUIClientHoldingsGroupByHousehold","",Some(Seq(Platform)), Some("canSeeClientHoldingsByHousehold"))
  val ViewUILearningCenterV2Capability:Capability = Capability("viewUILearningCenterV2","",Some(Seq(Platform)), Some("canSeeLearningCenterV2"))
  val ViewUILearningCenterV3Capability:Capability = Capability("viewUILearningCenterV3","",Some(Seq(Platform)), Some("canSeeLearningCenterV3"))
  val ViewUILearningCenterV2CustomResourceOrderCapability:Capability = Capability("viewUILearningCenterV2CustomResourceOrder","",Some(Seq(Platform)), Some("canSeeLearningCenterV2CustomResourceOrder"))
  val ViewUILearningCenterV3CustomResourceOrderCapability:Capability = Capability("viewUILearningCenterV3CustomResourceOrder","",Some(Seq(Platform)), Some("canSeeLearningCenterV3CustomResourceOrder"))
  val ViewUILearningCenterV2ResourceWithoutTrainingCapability:Capability = Capability("viewUILearningCenterV2ResourceWithoutTraining","",Some(Seq(Platform)), Some("canSeeLearningCenterV2ResourceWithoutTraining"))
  val ViewUILearningCenterV3ResourceWithoutTrainingCapability:Capability = Capability("viewUILearningCenterV3ResourceWithoutTraining","",Some(Seq(Platform)), Some("canSeeLearningCenterV3ResourceWithoutTraining"))
  val ViewUIDeveloperFeatureCapability:Capability = Capability("viewUIDeveloperFeature","",Some(Seq(Platform)), Some("canSeeDeveloperFeatures"))
  val ViewUITierCapability:Capability = Capability("viewUITier","",Some(Seq(Platform)))
  val ViewUIExportPDFCapability:Capability = Capability("viewUIExportPDF","",Some(Seq(Platform)), Some("canSeeExportPDF"))
  val ViewUILearningCenterV2TrainingUploadCapability:Capability = Capability("viewUILearningCenterV2TrainingUpload","",Some(Seq(Platform)), Some("canSeeLearningCenterV2TrainingUpload"))
 val ViewUILearningCenterV3TrainingUploadCapability:Capability = Capability("viewUILearningCenterV3TrainingUpload","",Some(Seq(Platform)), Some("canSeeLearningCenterV3TrainingUpload"))
  val ViewUIOrderPreTradeValidationCapability:Capability = Capability("viewUIOrderPreTradeValidation","Allow users to see the Pre-Trade tab in IOIs",Some(Seq(Platform)))
  val ViewUIOrderCreateNewOrderAccountCapability:Capability = Capability("viewUIOrderCreateNewOrderAccount","",Some(Seq(Platform)), Some("canSeeOrderCreateNewOrderAccount"))
  val ViewUIOrderBulkUploadCapability:Capability = Capability("viewUIOrderBulkUpload","",Some(Seq(Platform)), Some("canSeeOrderBulkUpload"))
  val ViewUIOrderBulkUploadDownloadForLPLCapability:Capability = Capability("viewUIOrderBulkUploadDownloadForLPL","Allow users to download the LPL specific bulk upload template and see the LPL language in the post-submission modals",Some(Seq(Platform)), Some("canSeeOrderBulkUploadDownloadForLPL"))
  val ViewUIOrderEntryClientSoliticationCapability:Capability = Capability("viewUIOrderEntryClientSolitication","",Some(Seq(Platform)), Some("canSeeOrderEntryClientSolicitation"))
  val ViewUIOrderEntryDiscretionaryCapability:Capability = Capability("viewUIOrderEntryDiscretionary","",Some(Seq(Platform)), Some("canSeeOrderEntryDiscretionary"))
  val ViewUIOrderEntryOnBehalfOfCapability:Capability = Capability("viewUIOrderEntryOnBehalfOf","",Some(Seq(Platform)), Some("canSeeOrderEntryOnBehalfOf"))
  val ViewUIOrderEntryCustomerDateTimeCapability:Capability = Capability("viewUIOrderEntryCustomerDateTime","",Some(Seq(Platform)), Some("canSeeOrderEntryCustomerDateTime"))
  val ViewUIOrderEntryRepCodeCapability:Capability = Capability("viewUIOrderEntryRepCode","",Some(Seq(Platform)), Some("canSeeOrderEntryFaNumber"))
  val ViewUIOrderEntryFeesCommissionCapability:Capability = Capability("viewUIOrderEntryFeesCommission","",Some(Seq(Platform)), Some("canSeeOrderEntryFeesCommission"))
  val ViewUIOrderEntryAccountTypeNumberPhoneRepNameCapability:Capability = Capability("viewUIOrderEntryAccountTypeNumberPhoneRepName","",Some(Seq(Platform)), Some("canSeeOrderEntryAccountTypeNumberPhoneRepName"))
  val ViewUIOrderEntryAcceptedByFaNumberCapability:Capability = Capability("viewUIOrderEntryAcceptedByFaNumber","",Some(Seq(Platform)), Some("canSeeOrderEntryAcceptedByRepCode"))
  val ViewUIOrderEntryNetworkCapability:Capability = Capability("viewUIOrderEntryNetwork","",Some(Seq(Platform)), Some("canSeeOrderEntryNetwork"))
  val ViewUIOrderEntryAccountSearchCapability:Capability = Capability("viewUIOrderEntryAccountSearch","",Some(Seq(Platform)), Some("canSeeOrderEntryAccountSearch"))
  val ViewUIOrderEntryAccountInputNumbersOnlyValidationCapability:Capability = Capability("viewUIOrderEntryAccountInputNumbersOnlyValidation","Allow users to see 8-digit number input validation for order entry account",Some(Seq(Platform)), Some("canSeeOrderEntryAccountInputNumbersOnlyValidation"))
  val ViewUIOrderEntryCommentCapability:Capability = Capability("viewUIOrderEntryComment","",Some(Seq(Platform)), Some("canSeeOrderEntryComment"))
  val ViewUIOrderEntryHideDefaultAttestationCapability:Capability = Capability("viewUIOrderEntryHideDefaultAttestation","",Some(Seq(Platform)), Some("canHideOrderEntryDefaultAttestation"))
  val ViewUIOrderEntryRockefellerCapability: Capability = Capability("viewUIOrderEntryRockefeller","Give access to Rockefeller order entry link via UI",Some(Seq(Platform)), Some("canSeeOrderEntryRockefeller"))
  val ViewUIIOILocationsColumnCapability:Capability = Capability("viewUIIOILocationsColumn","",Some(Seq(Platform)), Some("canSeeIOILocationsColumn"))
  val ViewUIBroadridgeIOIDownloadCapability: Capability = Capability("viewUIBroadridgeIOIDownload","Give access to Export to Broadridge button via UI",Some(Seq(Platform)), Some("canSeeBroadridgeIOIDownload"))
  val ViewUIPershingIOIDownloadCapability: Capability = Capability("viewUIPershingIOIDownload","Give access to Export to Pershing button via UI",Some(Seq(Platform)), Some("canSeePershingIOIDownload"))
  val ViewUIBloombergMTEIOIDownloadCapability: Capability = Capability("viewUIBloombergMTEIOIDownload", "Give access to Export to Bloomberg Multi Ticket Entry button via UI",Some(Seq(Platform)), Some("canSeeBloombergMTEIOIDownload"))
  val ViewUIMTDIOIDownloadCapability:Capability = Capability("viewUIMTDIOIDownload","",Some(Seq(Platform)), Some("canSeeMTDIOIDownload"))
  val ViewUICusipIOIDownloadCapability:Capability = Capability("viewUICusipIOIDownload","",Some(Seq(Platform)), Some("canSeeCusipIOIDownload"))
  val ViewUILearningCenterV2MyNetworkProductTrainingCapability:Capability = Capability("viewUILearningCenterV2MyNetworkProductTraining","",Some(Seq(Platform)), Some("canSeeLearningCenterV2MyNetworkProductTraining"))
  val ViewUILearningCenterV3MyNetworkProductTrainingCapability:Capability = Capability("viewUILearningCenterV3MyNetworkProductTraining","",Some(Seq(Platform)), Some("canSeeLearningCenterV3MyNetworkProductTraining"))
  val ViewUIPendingOfferingsCapability:Capability =Capability("viewUIPendingOfferings","",Some(Seq(Platform)), Some("canSeePendingOfferings"))
  val ViewUIAnalyticsAsOfDateCapability:Capability =Capability("viewUIAnalyticsAsOfDate","",Some(Seq(Platform)), Some("canSeeAnalyticsAsOfDate"))
  val ViewUIHoldingsAsOfDatesCapability:Capability =Capability("viewUIHoldingsAsOfDates","",Some(Seq(Platform)), Some("canSeeHoldingsAsOfDates"))
  val ViewUIRetriggerDTCCCapability:Capability =Capability("viewUIRetriggerDTCC","",Some(Seq(Platform)), Some("canSeeRetriggerDTCC"))
  val ViewUIContractEditCapability:Capability =Capability("viewUIContractEditViaEditPayoffEntitlement","",Some(Seq(Platform)), Some("canSeeContractEditor"))
  val ViewUIDistributorSymbolCapability:Capability =Capability("viewUIDistributorSymbol","",Some(Seq(Platform)), Some("canSeeDistributorSymbol"))
  val ViewUIProductNomenclatureLongNameCapability:Capability =Capability("viewUIProductNomenclatureLongName","",Some(Seq(Platform)), Some("canSeeProductNomenclatureLongName"))
  val ViewUIRejectedIOIsCapability:Capability =Capability("viewUIRejectedIOIs","",Some(Seq(Platform)), Some("canSeeRejectedIOIs"))
  val ViewFANumberSelectorDashboardCapability:Capability = Capability("viewFANumberSelectorDashboard","",Some(Seq(Platform)), Some("canSeeFANumberSelectorDashboard"))
  val ViewUIOnboardingToolCapability:Capability = Capability("viewUIOnboardingTool","",Some(Seq(Platform)), Some("canSeeOnboardingTool"))
  val ViewUISwitchUsersNetworkCapability:Capability = Capability("viewUISwitchUsersNetwork","",Some(Seq(Platform)), Some("canSwitchUsersNetwork"))
  val ViewUIExportToTwdXlsCapability:Capability = Capability("viewUIExportToTwdXls","",Some(Seq(Platform)), Some("canSeeExportToTwdXls"))
  val ViewUITMCReportCapability:Capability = Capability("viewUITMCReport","",Some(Seq(Platform)), Some("canSeeTMCReport"))
  val ViewUIOlarkCapability:Capability = Capability("viewUIOlark","",Some(Seq(Platform)), Some("canSeeOlark"))
  val ViewUIQuoteTypeCapability:Capability = Capability("viewUIQuoteType","",Some(Seq(Platform)), Some("canSeeQuoteType"))
  val ViewUICustomAttestationViaNetworkCapability:Capability = Capability("viewUICustomAttestationViaNetwork","",Some(Seq(Platform)), Some("canSeeCustomAttestation"))
  val ViewUIContractRangesCapability:Capability = Capability("viewUIContractRanges","",Some(Seq(Platform)), Some("canSeeContractRanges"))
  val ViewUIStagedOfferingsCapability:Capability = Capability("viewUIStagedOfferings","",Some(Seq(Platform)), Some("canSeeStagedOfferings"))
  val ViewUIRejectedOfferingsCapability:Capability = Capability("viewUIRejectedOfferings","",Some(Seq(Platform)), Some("canSeeRejectedOfferings"))
  val ViewUIMyProductsDashboardCapability:Capability = Capability("viewUIMyProductsDashboard","",Some(Seq(Platform)), Some("canSeeMyProductsDashboard"))
  val ViewUILifecycleEventsDashboardCapability:Capability = Capability("viewUILifecycleEventsDashboard","",Some(Seq(Platform)), Some("canSeeLifecycleEventsDashboard"))
  val ViewUINotificationsNewsfeedDashboardCapability:Capability = Capability("viewUINotificationsNewsfeedDashboard","",Some(Seq(Platform)), Some("canSeeNotificationsNewsfeedDashboard"))
  val ViewUIOfferingsReportCapability:Capability = Capability("viewUIOfferingsReport","",Some(Seq(Platform)), Some("canSeeOfferingsReport"))
  val ViewUIOfferingDocViaModalCapability:Capability = Capability("viewUIOfferingDocViaModal","",Some(Seq(Platform)), Some("canSeeOfferingDocViaModal"))
  val ViewUISearchBarCapability:Capability= Capability("viewUISearchBar","",Some(Seq(Platform)), Some("canSeeSearchBar"))
  val ViewUIProductNomenclatureCapability:Capability = Capability("viewUIProductNomenclature","",Some(Seq(Platform)), Some("canSeeProductNomenclature"))
  val ViewUIRefinitivDeepLinksCapability:Capability = Capability("viewUIRefinitivDeepLinks","",Some(Seq(Platform)), Some("canSeeRefinitivDeepLinks"))
  val ViewUIFABrandingCapability:Capability = Capability("viewUIFABranding","",Some(Seq(Platform)), Some("canSeeFABranding"))
  val ViewUIPerfPDFClientNameCapability:Capability = Capability("viewUIPerfPDFClientName","",Some(Seq(Platform)), Some("canSeePerfPDFClientName"))
  val ViewUIPerfPDFViaServiceCapability:Capability = Capability("viewUIPerfPDFViaService","",Some(Seq(Platform)), Some("canSeePerfPDFViaService"))
  val ViewUIPerfPDFFaGenerateInvestorReportMessageCapability:Capability = Capability("viewUIPerfPDFFaGenerateInvestorReportMessage","",Some(Seq(Platform)), Some("canSeePerfPDFFaGenerateInvestorReportMessage"))
  val ViewUITradewebOrderTokenCapability:Capability = Capability("viewUITradewebOrderToken","",Some(Seq(Platform)), Some("canGenerateTradewebOrderToken"))
  val ViewUITradewebEquitiesManageApprovalCapability:Capability = Capability("ViewUITradewebEquitiesManageApproval","",Some(Seq(Platform)), Some("canViewUITradewebEquitiesManageApproval"))
  val ViewUITradewebRatesManageApprovalCapability:Capability = Capability("ViewUITradewebRatesManageApproval","",Some(Seq(Platform)), Some("canViewUITradewebRatesManageApproval"))
  val ViewUIBuilderDistributorNomenclatureCapability:Capability = Capability("viewUIBuilderDistributorNomenclature","",Some(Seq(Platform)), Some("canSeeBuilderDistributorNomenclature"))
  val HideUILifecyclePortalExportToExcelCapability:Capability = Capability("hideUILifecyclePortalExportToExcel","",Some(Seq(Platform)), Some("canSeeLifecyclePortalExportToExcel"))
  val ViewUIEmbeddedExperienceCapability:Capability = Capability("viewUIEmbeddedExperience","",Some(Seq(Platform)), Some("canSeeEmbeddedExperience"))
  val ViewUIUnderlierMarketCapability:Capability = Capability("viewUIUnderlierMarket","",Some(Seq(Platform)), Some("canSeeUnderlierMarket"))
  val ViewUIAppcuesDropdownCapability:Capability = Capability("viewUIAppcuesDropdown","",Some(Seq(Platform)), Some("canSeeAppcuesDropdown"))
  val ViewUIBeaconCapability:Capability = Capability("viewUIBeacon","",Some(Seq(Platform)), Some("canSeeBeacon"))
  val ViewUILevelSystemCapability:Capability = Capability("viewUILevelSystem","",Some(Seq(Platform)), Some("canSeeLevelSystem"))
  val ViewUIQuickSearch2Capability:Capability = Capability("viewUIQuickSearch2","",Some(Seq(Platform)), Some("canRunQuickSearch2"))
  val ViewUIQuickSearchYMBICategoryCapability: Capability = Capability("viewUIQuickSearchYMBICategory","Allow users to see the you might be interested feature in the quick search toolbar", Some(Seq(Platform)), Some("canSeeQuickSearchYMBICategory"))
  val ViewUIProductSearchCapability:Capability= Capability("viewUIProductSearch","",Some(Seq(Platform)), Some("canSeeProductSearch"))
  val ViewUISIMON3Capability:Capability = Capability("viewUISIMON3","",Some(Seq(Platform)), Some("canSeeSIMON3"))
  val ViewUIHistoricalHoldingsSICapability:Capability = Capability("viewUIHistoricalHoldingsSI","Allow user to view structured investment historic holdings",Some(Seq(StructuredInvestmentsAssetClass)), Some("canSeeHistoricalHoldingsSI"))
  val ViewUIWhiteLabelingCapability:Capability = Capability("viewUIWhiteLabeling","",Some(Seq(Platform)), Some("canViewWhiteLabeling"))
  val ViewUIBottomDisclosureCapability:Capability = Capability("viewUIBottomDisclosure","",Some(Seq(Platform)),Some("canSeeBottomDisclosure"))
  val ViewUIHideHomepageLCWidgetCapability:Capability = Capability("viewUIHideHomepageLCWidget","",Some(Seq(Platform)), Some("canHideHomepageLCWidget"))
  val ViewUIHideIOIWidgetCapability:Capability = Capability("viewUIHideIOIWidget","",Some(Seq(Platform)), Some("canHideIOIWidget"))
  val ViewUIMarketplaceViewAsCapability:Capability = Capability("viewUIMarketplaceViewAs","",Some(Seq(Platform)), Some("canSeeMarketplaceViewAs"))
  val ViewUIIpipelineCapability:Capability = Capability("viewUIIpipeline","",Some(Seq(Platform)), Some("canSeeIpipeline"))
  val ViewUIBuilderPDFCapability:Capability = Capability("viewUIBuilderPDF","",Some(Seq(Platform)), Some("canSeeBuilderPDF"))
  val ViewUIMarketplaceRollsWidgetCapability:Capability = Capability("viewUIMarketplaceRollsWidget","",Some(Seq(Platform)), Some("canSeeMarketplaceRollsWidget"))
  val ViewUISIMarketplaceExportExcelCapability:Capability = Capability("viewUISIMarketplaceExportExcel","Allow user to export marketplace offerings to excel",Some(Seq(Platform)), Some("canSeeSIMarketplaceExportExcel"))
  val ViewUISIMarketplaceDenominationCapability:Capability = Capability("viewUISIMarketplaceDenomination","",Some(Seq(Platform)), Some("canSeeSIMarketplaceDenomination"))
  val ViewUIUploadAdditionalDocumentCapability:Capability = Capability("viewUIUploadAdditionalDocument","",Some(Seq(Platform)), Some("canSeeUploadAdditionalDocument"))
  val ViewUITermInMonthsCapability:Capability = Capability("viewUITermInMonths","",Some(Seq(Platform)), Some("canSeeTermInMonths"))
  val ViewUISubscribeInvestmentEntityOptionsCapability:Capability = Capability("viewUISubscribeInvestmentEntityOptions","",Some(Seq(Platform)), Some("canSeeSubscribeInvestmentEntityOptions"))
  val ViewUIAddRfqIssuersToAuctionCapability:Capability = Capability("viewUIAddRfqIssuersToAuction","",Some(Seq(Platform)), Some("canSeeAddRfqIssuersToAuction"))
  val ViewUIGrantModeCapability:Capability = Capability("viewUIGrantMode","",Some(Seq(Platform)), Some("canSeeGrantMode"))
  val ViewUIBeaconIframeCapability:Capability = Capability("viewUIBeaconIframe","",Some(Seq(Platform)), Some("canSeeBeaconIframe"))
  val ViewUISiloedExperienceCapability:Capability = Capability("viewUISiloedExperience", "", Some(Seq(Platform)), Some("canSeeSiloedExperience"))
  val ViewUIAltsViewDetailsCapability: Capability = Capability("viewUIAltsViewDetails", "", Some(Seq(AlternativesAssetClass, Platform)), Some("canSeeUIAltsViewDetails"))
  val ViewUICoBrandingCustomRJCapability: Capability = Capability("viewUICoBrandingCustomRJ", "Custom placement of Powered by SIMON text for co-branding with RJ", Some(Seq(Platform)), Some("canSeeUICoBrandingCustomRJ"))
  val ViewUIUserImpersonationCapability: Capability = Capability("viewUIUserImpersonation","",Some(Seq(Platform)), Some("canSeeUserImpersonation"))
  val ViewUIUserImpersonationForHOUsersCapability:Capability = Capability("viewUIUserImpersonationForHOUsers","",Some(Seq(Platform)), Some("canSeeUserImpersonationForHOUsers"))
  val ViewUIAGGridOnLifecyclePortalCapability:Capability = Capability("viewUIAGGridOnLifecyclePortal","Allow users to see AG Grid on lifecycle portal",Some(Seq(Platform)), Some("canSeeAGGridOnLifecyclePortal"))
  val ViewUIAGGridOnLifecyclePortalSkipMigrationsCapability:Capability = Capability("viewUIAGGridOnLifecyclePortalSkipMigrations","Allow users to see AG Grid on lifecycle portal but skip migration to hide product tab",Some(Seq(Platform)), Some("canSeeAGGridOnLifecyclePortalSkipMigrations"))
  val ViewUIAGGridOnRfqCapability:Capability = Capability("ViewUIAGGridOnRfq","Allow users to see AG Grid on rfql",Some(Seq(Platform)), Some("canSeeAGGridOnRfq"))
  val ViewUINetworkVisualsFromProfileCapability:Capability = Capability("viewUINetworkVisualsFromProfile", "Use 'visuals' field for whitelabel config", Some(Seq(Platform)), Some("canSeeNetworkVisualsFromProfile"))
  val ViewUIMarketDataEndpointForUnderlierPricesCapability:Capability = Capability("ViewUIMarketDataEndpointForUnderlierPrices", "Use the Market Data endpoint instead of using get-underlier-prices endpoint", Some(Seq(Platform)), Some("canSeeMarketDataEndpointForUnderlierPrices"))
  val ViewUIAGGridClientPages: Capability = Capability("ViewUIAGGridClientPages", "Allow users to see AG Grid on client pages", Some(Seq(Platform)), Some("canSeeAGGridOnClientPages"))
  val ViewUIAGGridOnSalesBook: Capability = Capability("ViewUIAGGridOnSalesBook", "Allow users to see AG Grid on sales book", Some(Seq(Platform)), Some("canSeeAGGridOnSalesBook"))
  val ViewUIGPPortalApp: Capability = Capability("ViewUIGPPortalApp", "Allow users to see GP Portal app", Some(Seq(Platform)), Some("canSeeGPPortalApp"))
  val ViewUIAIChatApp: Capability = Capability("ViewUIAIChatApp", "Allow users to see ICN chat app", Some(Seq(Platform)), Some("canSeeAIChatApp"))
  val ViewUIAIChatExperimentalPlugins: Capability = Capability("ViewUIAIChatExperimentalPlugins", "Allow users to see experimental plugins", Some(Seq(Platform)), Some("canSeeAIChatExperimentalPlugin"))
  val ViewUIContactUs: Capability = Capability("ViewUIContactUs", "Allow users to see the contact us button", Some(Seq(Platform)), Some("canSeeContactUs"))
  val ViewUIPlatformToggleCapability: Capability = Capability("viewUIPlatformToggle", "Allow users to see platform toggle", Some(Seq(Platform)), Some("canSeePlatformToggle"))
  val ViewUIPlatformEnhancementsPageCapability: Capability = Capability("ViewUIPlatformEnhancementsPage", "Allow users to see platform enhancements page", Some(Seq(Platform)), Some("canSeePlatformEnhancementsPage"))
  val ViewUIUnifiedEducationCapability: Capability = Capability("viewUIUnifiedEducation", "Allow users to see unified education page", Some(Seq(Platform)), Some("canSeeUnifiedEducation"))
  val ViewUIDeveloperHubCapability: Capability = Capability("viewUIDeveloperHub", "Allow users to see Developer Hub", Some(Seq(Platform)), Some("canSeeDeveloperHub"))
  val ViewUIEditClientCredentialsViaDeveloperHubCapability: Capability = Capability("viewUIEditClientCredentialsViaDeveloperHub", "Allow users to see edit functionality for managing System Users via Developer Hub", Some(Seq(Platform)), Some("canSeeEditClientCredentialsViaDeveloperHub"))
  val HideUIManagedAccountHoldingsInDashboardWidgetsCapability: Capability = Capability("hideUIManagedAccountHoldingsInDashboardWidgets", "Excludes managed account holdings in dashboard widgets", Some(Seq(Platform)), Some("canHideManagedAccountHoldingsInDashboardWidgets"))
  val ViewUIUnifiedEducationGeneralAndRegulatoryCapability: Capability = Capability("viewUIUnifiedEducationGeneralAndRegulatory", "Allows user to view General and Regulatory content within the Unified Education platform", Some(Seq(Platform)), Some("canSeeUnifiedEducationGeneralAndRegulatory"))
  val ViewUIAccountIdGlobalFilterCapability: Capability = Capability("viewUIAccountIDGlobalFilter", "Allows user to see a Account ID global filter on the Accounts page", Some(Seq(Platform)), Some("canSeeAccountIdGlobalFilter"))
  val ViewUIIssuerDashboardCapability: Capability = Capability("viewUIIssuerDashboard", "Allows user to see Issuer Dashboard Page", Some(Seq(Platform)), Some("canSeeIssuerDashboard"))
  val ViewUIForYouOverrideCapability: Capability = Capability("ViewUIForYouOverride", "", Some(Seq(Platform)), Some("canSeeForYouOverride"))
  val ViewUIUserManagementCapability: Capability = Capability("ViewUIUserManagement", "Allows user to see User Management page", Some(Seq(Platform)), Some("canSeeUserManagement"))
  val ViewUIUserManagementUnifiedCapability: Capability = Capability("ViewUIUserManagementUnified", "Allows user to see Unified User Management page", Some(Seq(Platform)), Some("canSeeUnifiedUserManagement"))
  val ViewUILearningCenterV3InsightsCapability: Capability = Capability("ViewUILearningCenterV3Insights", "Allows user to see Learning Center V3 Insights", Some(Seq(Platform)), Some("canSeeLearningCenterV3Insights"))
  val ViewUILearningCenterV3GlossaryCapability: Capability = Capability("ViewUILearningCenterV3Glossary", "Allows user to see Learning Center V3 Glossary", Some(Seq(Platform)), Some("canSeeLearningCenterV3Glossary"))
  val ViewUIUnifiedHomepageCapability: Capability = Capability("ViewUIUnifiedHomepage", "Allows user to see unified Homepage, (icn + simon)", Some(Seq(Platform)), Some("canSeeUnifiedHomepage"))
  val ViewUIUnifiedSIMarketplaceCapability: Capability = Capability("viewUIUnifiedSIMarketplace", "Allows user to see the SI asset class for unified marketplace", Some(Seq(Platform)), Some("canSeeUnifiedSIMarketplace"))
  val ViewUIUnifiedAnnuitiesMarketplaceCapability: Capability = Capability("viewUIUnifiedAnnuitiesMarketplace", "Allows user to see the Annuities asset class for unified marketplace", Some(Seq(Platform)), Some("canSeeUnifiedAnnuitiesMarketplace"))
  val ViewUIUnifiedETFMarketplaceCapability: Capability = Capability("viewUIUnifiedETFMarketplace", "Allows user to see the ETF asset class for unified marketplace", Some(Seq(Platform)), Some("canSeeUnifiedETFMarketplace"))
  val ViewUIUnifiedSMAMarketplaceCapability: Capability = Capability("viewUIUnifiedSMAMarketplace", "Allows user to see the SMA asset class for unified marketplace", Some(Seq(Platform)), Some("canSeeUnifiedSMAMarketplace"))
  val ViewUIApprovalImpersonateCapability: Capability = Capability("ViewUIApprovalImpersonate", "Require approval for impersonating another user", Some(Seq(Platform)), Some("canSeeApprovalImpersonate"))
  val ViewUILearningCenterV3AlternativeInvestmentsResourcesCapability: Capability = Capability("ViewUILearningCenterV3AlternativeInvestmentsResources", "Allows user to see Unified Education V3 Resources for Alternate Investment",  Some(Seq(Platform)), Some("canSeeLearningCenterV3AlternativeInvestmentsResources"))
  val ViewUILearningCenterV3StructuredInvestmentsResourcesCapability: Capability  = Capability("ViewUILearningCenterV3StructuredInvestmentsResources", "Allows user to see Unified Education V3 Resources for Structured Investment",  Some(Seq(Platform)), Some("canSeeLearningCenterV3StructuredInvestmentsResources"))
  val ViewUILearningCenterV3AnnuitiesResourcesCapability: Capability              = Capability("ViewUILearningCenterV3AnnuitiesResources", "Allows user to see Unified Education V3 Resources for Annuities",                          Some(Seq(Platform)), Some("canSeeLearningCenterV3AnnuitiesResources"))
  val ViewUILearningCenterV3DefinedOutcomeETFsResourcesCapability: Capability     = Capability("ViewUILearningCenterV3DefinedOutcomeETFsResources", "Allows user to see Unified Education V3 Resources for DefinedOutcomeETFs",        Some(Seq(Platform)), Some("canSeeLearningCenterV3DefinedOutcomeETFsResources"))
  val ViewUILearningCenterV3GeneralAndRegulatoryResourcesCapability: Capability   = Capability("ViewUILearningCenterV3GeneralAndRegulatoryResources", "Allows user to see Unified Education V3 Resources for GeneralAndRegulatory",    Some(Seq(Platform)), Some("canSeeLearningCenterV3GeneralAndRegulatoryResources"))
  val ViewUILearningCenterV3CustomizationsPortalCapability: Capability                           = Capability("ViewUILearningCenterV3CustomizationsPortal", "Allows user to see Unified Education V3 Admin",                                                         Some(Seq(Platform)), Some("canSeeLearningCenterV3CustomizationsPortal"))
  val ViewUILearningCenterV3CourseFinderCapability: Capability = Capability("ViewUILearningCenterV3CourseFinder", "Allows user to see Learning Center V3 Course Finder", Some(Seq(Platform)), Some("canSeeLearningCenterV3CourseFinder"))
  val ViewUIWholesalerExchangeCapability: Capability = Capability("viewUIWholesalerExchange", "Baseline capability – allows user to access the Wholesaler Exchange", Some(Seq(Platform)), Some("canSeeWholesalerExchange"))
  val ViewUIWholesalerExchangeAsTechUserCapability: Capability = Capability("viewUIWholesalerExchangeAsTechUser", "Allows user to access tech user exclusive features", Some(Seq(Platform)), Some("canSeeWholesalerExchangeAsTechUser"))

  //Spectrum
  val ViewUIOptimizationToolSICapability: Capability = Capability("viewUIOptimizationToolSI", "Allow users to access generic Structured Investments for both hypothetical and uploaded portfolios", Some(Seq(StructuredInvestmentsAssetClass, Spectrum)), Some("canSeeOptimizationToolSI"))
  val ViewUIOptimizationToolSIPDFCapability: Capability = Capability("viewUIOptimizationToolSIPDF", "Allow users to view and download the client-friendly PDFs with generic Structured Investments using an uploaded or hypothetical portfolio", Some(Seq(StructuredInvestmentsAssetClass, Spectrum)), Some("canSeeOptimizationToolSIPDF"))
  val ViewUISpectrumLC2SPCapability: Capability = Capability("viewUISpectrumLC2SP", "Allows user to view the spectrum mvp calibration tool for SP", Some(Seq(Platform, Spectrum)), Some("canSeeSpectrumLC2SP"))
  val ViewUIOptimizationToolAnnuityCapability: Capability = Capability("viewUIOptimizationToolAnnuity", "Allow users to access generic Structured Annuities for both hypothetical and uploaded portfolios", Some(Seq(AnnuitiesAssetClass, Spectrum)), Some("canSeeOptimizationToolAnnuity"))
  val ViewUISpectrumLC2AnnuitiesCapability: Capability = Capability("viewUISpectrumLC2Annuities", "Allow users to access generic Structured Annuities for hypothetical portfolios", Some(Seq(AnnuitiesAssetClass, Spectrum)), Some("canSeeUISpectrumLC2Annuities"))
  val ViewUISpectrumMarketplaceAnnuitiesCapability: Capability = Capability("viewUISpectrumMarketplaceAnnuities", "Allow users to view Spectrum scores in the Structured Annuities Marketplace", Some(Seq(AnnuitiesAssetClass, Spectrum)), Some("canSeeUISpectrumMarketplaceAnnuities"))
  val ViewUISpectrumMarketplaceSICapability: Capability = Capability("viewUISpectrumMarketplaceSI", "Allows users view Spectrum scores in the SI Marketplace, including a tab on investment highlights. This also controls seeing Spectrum scores in the Current Offerings Analysis modal within the Spectrum tool, if the user has that enabled as well", Some(Seq(StructuredInvestmentsAssetClass, Spectrum)), Some("canSeeUISpectrumMarketplaceSI"))
  val ViewUISpectrumSICUSIPModalCapability: Capability = Capability("viewUISpectrumSICUSIPModal", "Enable users with the real SI CUSIPs modal", Some(Seq(StructuredInvestmentsAssetClass, Spectrum)), Some("canSeeUISpectrumSICUSIPModal"))
  val ViewUILifecyclePortalSpectrumCapability: Capability = Capability("viewUILifecyclePortalSpectrum", "Enable user view Spectrum scores on Lifecycle Portal", Some(Seq(StructuredInvestmentsAssetClass, Spectrum)), Some("canSeeUILifecyclePortalSpectrum"))
  val ViewUISpectrumSIPDFCapability: Capability = Capability("viewUISpectrumSIPDF", "Allow users to view and download Spectrum’s client-friendly pdf with a generic Structured Investment using a hypothetical portfolio", Some(Seq(StructuredInvestmentsAssetClass, Spectrum)), Some("canSeeUISpectrumSIPDF"))
  val ViewUISpectrumAnnPDFCapability: Capability = Capability("viewUISpectrumAnnPDF", "Allow users to view and download Spectrum’s client-friendly pdf with a generic Structured Annuity using a hypothetical portfolio", Some(Seq(AnnuitiesAssetClass, Spectrum)), Some("canSeeUISpectrumAnnPDF"))
  val ViewUISpectrumBuilderCapability: Capability = Capability("viewUISpectrumBuilder", "Allows user to view Spectrum in Builder", Some(Seq(Platform, Spectrum)), Some("canSeeSpectrumBuilder"))
  val ViewUIOptimizationGrowthOnlyProductsCapability: Capability = Capability("viewUIOptimizationGrowthOnlyProducts", "Removes income products from the tool, including hiding the income/growth Step 2 subquestion, and changing the left-most sample analysis on the Spectrum landing page to be a MLGN rather than a CMLIN.", Some(Seq(Platform, Spectrum)), Some("canSeeUIOptimizationGrowthOnlyProducts"))
  val ViewUIOptimizationToolPDFDisclosureRJCapability: Capability = Capability("viewUIOptimizationToolPDFDisclosureRJ", "Allows users to see custom RJ disclosures in the Spectrum client-friendly pdfs", Some(Seq(Platform, Spectrum)), Some("canSeeUIOptimizationToolPDFDisclosureRJ"))
  val ViewUIOptimizationToolPDFBrandingCapability: Capability = Capability("viewUIOptimizationToolPDFBranding", "Shows respective network’s co-branding on Spectrum’s pdf reports", Some(Seq(Platform, Spectrum)), Some("canSeeOptimizationToolPDFBranding"))
  val ViewUIOptimizationToolPDFDisclosureModalClientCapability: Capability = Capability("viewUIOptimizationToolPDFDisclosureModalClient", "Allows the user to enter a clients name in addition to checking the box, which will appear on the pdf", Some(Seq(Platform, Spectrum)), Some("canSeeOptimizationToolPDFDisclosureModalClient"))
  val ViewUIOptimizationToolPDFDisclosureModalCapability: Capability = Capability("viewUIOptimizationToolPDFDisclosureModal", "Show a pop-up modal prior to downloading pdf with some terms and a checkbox to indicate if the user understands the terms. Checking the box will enable the download pdf button", Some(Seq(Platform, Spectrum)), Some("canSeeOptimizationToolPDFDisclosureModal"))
  val ViewUIRiskTypeModalCapability: Capability = Capability("viewUIRiskTypeModal", "", Some(Seq(Platform, Spectrum)), Some("canSeeRiskTypeModal"))
  val ViewUIRiskTypeModalOverrideCapability: Capability = Capability("viewUIRiskTypeModalOverride", "", Some(Seq(Platform, Spectrum)), Some("canSeeRiskTypeModalOverride"))
  val ViewUIRiskTypeCapability: Capability = Capability("viewUIRiskType", "", Some(Seq(Platform, Spectrum)), Some("canSeeRiskType"))
  val ViewUISpectrumSICUSIPModalScoresCapability: Capability = Capability("viewUISpectrumSICUSIPModalScores", "Shows Spectrum scores in the Marketplace view within Spectrum’s COA modal, but does not impact anywhere within the SI Marketplace (i.e., any user with this capability and without viewUISpectrumMarketplaceSI should not see Spectrum anywhere within the actual SI Marketplace, including the product details pages, Spectrum disclaimers, etc.).", Some(Seq(Platform, Spectrum)), Some("canSeeUISpectrumSICUSIPModalScores"))
  val ViewUISpectrumLC2FixedIndexedAnnuitiesCapability: Capability = Capability("viewUISpectrumLC2FixedIndexedAnnuities", "Allows user to view only hypothetical portfolios in the PCT, and optimize with generic FIAs + view new income tab", Some(Seq(Platform, Spectrum)), Some("canSeeSpectrumLC2FixedIndexedAnnuities"))
  val ViewUIOptimizationToolFixedIndexedAnnuitiesCapability: Capability = Capability("viewUIOptimizationToolFixedIndexedAnnuities", "allow users to use both hypothetical and uploaded portfolios in the PCT, and optimize with generic FIAs + view new income tab", Some(Seq(Platform, Spectrum)), Some("canSeeOptimizationToolFixedIndexedAnnuities"))
  val ViewUISpectrumAdvisorFIAPDFCapability: Capability = Capability("viewUISpectrumAdvisorFIAPDF", "Allows users to view and download the advisor-friendly PDFs with generic FIAs using a hypothetical portfolio", Some(Seq(Platform, Spectrum)), Some("canSeeSpectrumAdvisorFIAPDF"))
  val ViewUIOptimizationToolAdvisorFIAPDFCapability: Capability = Capability("viewUIOptimizationToolAdvisorFIAPDF", "Allows users to view and download the advisor-friendly PDFs with generic FIAs using a hypothetical or uploaded portfolio", Some(Seq(Platform, Spectrum)), Some("canSeeOptimizationToolAdvisorFIAPDF"))
  val ViewUIOptimizationToolClientFIAPDFCapability: Capability = Capability("viewUIOptimizationToolClientFIAPDF", "Allows users to view and download the client-friendly PDFs with generic FIAs using a hypothetical or uploaded portfolio", Some(Seq(Platform, Spectrum)), Some("canSeeOptimizationToolClientFIAPDF"))
  val ViewUISpectrumClientFIAPDFCapability: Capability = Capability("viewUISpectrumClientFIAPDF", "Allows users to view and download the client-friendly PDFs with generic FIAs using a hypothetical portfolio", Some(Seq(Platform, Spectrum)), Some("canSeeSpectrumClientFIAPDF"))
  val HideSpectrumPDFNetworkNameCapability: Capability = Capability("hideSpectrumPDFNetworkName", "Hides the network name throughout the PDF for both the hypothetical portfolio and uploaded portfolio Spectrum pdfs, regardless of the generic product used in the PDF.", Some(Seq(Platform, Spectrum)), Some("canHideSpectrumPDFNetworkName"))

  //Architect
  val ViewUIArchitectCapability: Capability = Capability("viewUIArchitect", "Enables user with Architect baseline functionality.  This functionality includes the Architect homepage, Portfolios tab, Analysis page with Allocations section, and Portfolio Builder without any marketplaces or generic products.  This capability also controls the button in the nav bar that brings the user to Architect.", Some(Seq(Platform, Architect)), Some("canSeeArchitect"))
  val ViewUIArchitectAnalysisPageSpectrumAnalysis: Capability = Capability("viewUIArchitectAnalysisPageSpectrumAnalysis", "Enables user with the Spectrum scores/alignment scores section on the Analysis page of Architect", Some(Seq(Platform, Architect)), Some("canSeeArchitectAnalysisPageSpectrumAnalysis"))
  val ViewUIArchitectScenariosChart: Capability = Capability("viewUIArchitectScenariosChart", "Enables user with the Scenarios chart on the Analysis page of Architect", Some(Seq(Platform, Architect)), Some("canSeeArchitectScenariosChart"))
  val ViewUIArchitectGrowthChart: Capability = Capability("viewUIArchitectGrowthChart", "Enables user with the Growth chart on the Analysis page of Architect", Some(Seq(Platform, Architect)), Some("canSeeArchitectGrowthChart"))
  val ViewUIArchitectCorrelationMatrixChart: Capability = Capability("viewUIArchitectCorrelationMatrixChart", "Enables user with the correlation matrix chart on the Analysis page of Architect", Some(Seq(Platform, Architect)), Some("canSeeArchitectCorrelationMatrixChart"))
  val ViewUIArchitectFactorsChart: Capability = Capability("viewUIArchitectFactorsChart", "Enables user with the factor exposures chart on the Analysis page of Architect",Some(Seq(Platform, Architect)), Some("canSeeArchitectFactorsChart"))
  val ViewUIArchitectAdvisorPDFs: Capability = Capability("viewUIArchitectAdvisorPDFs", "Enables user with the Architect advisor-friendly pdfs", Some(Seq(Platform, Architect)), Some("canSeeArchitectAdvisorPDFs"))
  val ViewUIArchitectClients: Capability = Capability("viewUIArchitectClients", "Enables user with the Clients tab in Architect, including the questionnaire and client profile pages", Some(Seq(Platform, Architect)), Some("canSeeArchitectClients"))
  val ViewUIArchitectBuilderGenericAlts: Capability = Capability("viewUIArchitectBuilderGenericAlts", "Enables user with the generic Alts marketplace in Architect’s portfolio builder", Some(Seq(Platform, Architect)), Some("canSeeArchitectBuilderGenericAlts"))
  val ViewUIArchitectBuilderRealAlts: Capability = Capability("viewUIArchitectBuilderRealAlts", "Enables user with the real Alts marketplace in Architect’s portfolio builder", Some(Seq(Platform, Architect)), Some("canSeeArchitectBuilderRealAlts"))
  val ViewUIArchitectBuilderGenericSI: Capability = Capability("viewUIArchitectBuilderGenericSI", "Enables user with the generic SI marketplace in Architect’s portfolio builder", Some(Seq(Platform, Architect)), Some("canSeeArchitectBuilderGenericSI"))
  val ViewUIArchitectBuilderRealSI: Capability = Capability("viewUIArchitectBuilderRealSI", "Enables user with the real SI marketplace in Architect’s portfolio builder", Some(Seq(Platform, Architect)), Some("canSeeArchitectBuilderRealSI"))
  val ViewUIArchitectBuilderGenericTraditional: Capability = Capability("viewUIArchitectBuilderGenericTraditional", "Enables user with the generic traditional assets marketplace in Architect’s portfolio builder", Some(Seq(Platform, Architect)), Some("canSeeArchitectBuilderGenericTraditional"))
  val ViewUIArchitectBuilderGenericAltsSpectrumScores: Capability = Capability("viewUIArchitectBuilderGenericAltsSpectrumScores", "Enables user with Spectrum scores in the generic Alts marketplace in Architect’s portfolio builder", Some(Seq(Platform, Architect)), Some("canSeeArchitectBuilderGenericAltsSpectrumScores"))
  val ViewUIArchitectBuilderRealAltsSpectrumScores: Capability = Capability("viewUIArchitectBuilderRealAltsSpectrumScores", "Enables user with Spectrum scores in the real Alts marketplace in Architect’s portfolio builder", Some(Seq(Platform, Architect)), Some("canSeeArchitectBuilderRealAltsSpectrumScores"))
  val ViewUIArchitectBuilderGenericSISpectrumScores: Capability = Capability("viewUIArchitectBuilderGenericSISpectrumScores", "Enables user with Spectrum scores in the generic SI marketplace in Architect’s portfolio builder", Some(Seq(Platform, Architect)), Some("canSeeArchitectBuilderGenericSISpectrumScores"))
  val ViewUIArchitectBuilderRealSISpectrumScores: Capability = Capability("viewUIArchitectBuilderRealSISpectrumScores", "Enables user with Spectrum scores in the real SI marketplace in Architect’s portfolio builder", Some(Seq(Platform, Architect)), Some("canSeeArchitectBuilderRealSISpectrumScores"))
  val ViewUIArchitectBuilderGenericTraditionalSpectrumScores: Capability = Capability("viewUIArchitectBuilderGenericTraditionalSpectrumScores", "Enables user with Spectrum scores in the generic traditional assets marketplace in Architect’s portfolio builder", Some(Seq(Platform, Architect)), Some("canSeeArchitectBuilderGenericTraditionalSpectrumScores"))
  val HideUIArchitectNavBar: Capability = Capability("hideUIArchitectNavBar", "Hides all nav bar items when a user enters Architect from ICN platform and uses ICN Architect branding logo in top left corner, rather than the usual SIMON logo", Some(Seq(Platform, Architect)), Some("hideUIArchitectNavBar"))
  val HideUIArchitectPDFsWatermarks: Capability = Capability("hideUIArchitectPDFsWatermarks", "Hides watermark and bottom disclaimer of Architect PDFs", Some(Seq(Platform, Architect)), Some("hideUIArchitectPDFsWatermarks"))
  val ViewUIArchitectAdminTool: Capability = Capability("viewUIArchitectAdminTool", "Allows user to access Architect’s admin tool, which controls on-platform copy, disclaimers, and methodology documents for Architect", Some(Seq(Platform, Architect)), Some("canSeeArchitectAdminTool"))
  val ViewUIArchitectShareCustomAssets: Capability = Capability("viewUIArchitectShareCustomAssets", "Allows access to users within their network/location when looking to share a custom asset", Some(Seq(Platform, Architect)), Some("canSeeArchitectShareCustomAssets"))
  val ViewUIArchitectShareCustomAssetsAll: Capability = Capability("viewUIArchitectShareCustomAssetsAll", "Allows access to users outside of their network/location when looking to share a custom asset", Some(Seq(Platform, Architect)), Some("canSeeArchitectShareCustomAssetsAll"))
  val ViewUIArchitectSharePortfolios: Capability = Capability("viewUIArchitectSharePortfolios", "Allows access to users within their network/location when looking to share portfolios.", Some(Seq(Platform, Architect)), Some("canSeeArchitectSharePortfolios"))
  val ViewUIArchitectSharePortfoliosAll: Capability = Capability("viewUIArchitectSharePortfoliosAll", "Allows access to users outside of their network/location when looking to share portfolios", Some(Seq(Platform, Architect)), Some("canSeeArchitectSharePortfoliosAll"))
  val ViewUIArchitectCreateCustomAssets: Capability = Capability("viewUIArchitectCreateCustomAssets", "Allows the user to create custom assets and add them to portfolios.", Some(Seq(Platform, Architect)), Some("canSeeArchitectCreateCustomAssets"))
  val ViewUIArchitectTermsOfServiceModal: Capability = Capability("viewUIArchitectTermsOfServiceModal", "To control when or not the terms of service should appear.", Some(Seq(Platform, Architect)), Some("canSeeArchitectTermsOfServiceModal"))
  val ViewUIArchitectPortfolioGrouping: Capability = Capability("viewUIArchitectPortfolioGrouping", "Enables user to view the Portfolio Group search and create tab.", Some(Seq(Platform, Architect)), Some("canSeeArchitectPortfolioGroupBrowse"))
  val ViewUIArchitectAssetClassOptimizations: Capability = Capability("viewUIArchitectAssetClassOptimizations", "Enables user to view the Portfolio Asset class optimization", Some(Seq(Platform, Architect)), Some("canSeeArchitectAssetClassOptimizations"))
  val ViewUIArchitectFindSimilarAssets: Capability = Capability("viewUIArchitectFindSimilarAssets", "Enables user to view the find similar assets button", Some(Seq(Platform, Architect)), Some("canSeeArchitectFindSimilarAssets"))
  val ViewUIArchitectProRataAllocations: Capability = Capability("viewUIArchitectProRataAllocations", "Allows control of the pro-rata feature to appear", Some(Seq(Platform, Architect)), Some("canSeeArchitectProRataAllocations"))
  val ViewUIEnvestnetSkipClientQuestionnaire: Capability = Capability("viewUIEnvestnetSkipClientQuestionnaire", "Allows user to skip the client profile creation step for the Envestnet integration", Some(Seq(Architect)), Some("canSeeEnvestnetSkipClientQuestionnaire"))
  val ViewUIEnvestnetArchitectIntegration: Capability = Capability("viewUIEnvestnetArchitectIntegration", "Allows user to use the new Architect-Envestnet integration", Some(Seq(Architect)), Some("canSeeEnvestnetArchitectIntegration"))
  val ViewUIArchitectExportPortfolioButton: Capability = Capability("viewUIArchitectExportPortfolioButton", "Allows users to see the button to export their portfolio", Some(Seq(Architect)), Some("canSeeArchitectExportPortfolioButton"))
  val ViewUIArchitectBuilderSleeves: Capability = Capability("viewUIArchitectBuilderSleeves", "Allows users to see the Asset Sleeves table component in Builder", Some(Seq(Platform, Architect)), Some("canSeeArchitectBuilderSleeves"))
  val ViewUIArchitectBuilderInvest: Capability = Capability("viewUIArchitectBuilderInvest", "Allows users to see the Invest tab and CTAs in Architect", Some(Seq(Architect)), Some("canSeeUIArchitectBuilderInvest"))
  val ViewUIArchitectDownloadPDFs: Capability = Capability("viewUIArchitectDownloadPDFs", "Allows users to see the download advisor pdf button in Architect", Some(Seq(Architect)), Some("canSeeUIArchitectDownloadPDFs"))
  val ViewUIArchitectClientPDFs: Capability = Capability("viewUIArchitectClientPDFs", "Allows users to see the download client pdf button in Architect", Some(Seq(Architect)), Some("canSeeUIArchitectClientPDFs"))
  val ViewUIArchitectSharePortfoliosIndividuals: Capability = Capability("viewUIArchitectSharePortfoliosIndividuals", "Allows users to see share portfolio buttons in the portfolio list, analysis page, or Settings tab", Some(Seq(Architect)), Some("canSeeArchitectSharePortfoliosIndividuals"))
  val ViewUIArchitectShareCustomAssetsIndividuals: Capability = Capability("viewUIArchitectShareCustomAssetsIndividuals", "Allows users to see share option in the custom assets tab", Some(Seq(Architect)), Some("canSeeArchitectShareCustomAssetsIndividuals"))
  val ViewUIArchitectSharePortfoliosNetworks: Capability = Capability("viewUIArchitectSharePortfoliosNetworks", "Allows network users to see share portfolio buttons in the portfolio list, analysis page, or Settings tab", Some(Seq(Architect)), Some("canSeeArchitectSharePortfoliosNetworks"))
  val ViewUIArchitectShareCustomAssetsNetworks: Capability = Capability("viewUIArchitectShareCustomAssetsNetworks", "Allows network users to see share option in the custom assets tab", Some(Seq(Architect)), Some("canSeeArchitectShareCustomAssetsNetworks"))
  val ViewUIArchitectBuilderInvestInvestmentStatusAlts: Capability = Capability("viewUIArchitectBuilderInvestInvestmentStatusAlts", "Allows user to see investment status button for Alts in invest tab", Some(Seq(Architect)), Some("canSeeArchitectBuilderInvestInvestmentStatusAlts"))
  val ViewUIArchitectBuilderInvestInvestmentStatusSI: Capability = Capability("viewUIArchitectBuilderInvestInvestmentStatusSI", "Allows user to see investment status button for SIs in invest tab", Some(Seq(Architect)), Some("canSeeArchitectBuilderInvestInvestmentStatusSI"))
  val ViewUIArchitectBuilderReplaceHoldings: Capability = Capability("viewUIArchitectBuilderReplaceHoldings", "Allows user to see the allocation method select in the asset allocation modal", Some(Seq(Architect)), Some("canSeeArchitectBuilderReplaceHoldings"))
  val ViewUIArchitectHoldingsOptimizations: Capability = Capability("viewUIArchitectHoldingsOptimizations", "Allows user to view the Portfolio Calibration wizard", Some(Seq(Architect)), Some("canSeeArchitectHoldingsOptimizations"))
  val ViewUIRegBIEvaluation: Capability = Capability("viewUIRegBIEvaluation", "Allows user to see the RegBI evaluation pages", Some(Seq(Architect, StructuredInvestmentsAssetClass)), Some("canSeeRegBIEvaluation"))
  val ViewUIArchitectExpressSurvey: Capability = Capability("viewUIArchitectExpressSurvey", "Allows user to view the express survey", Some(Seq(Architect)), Some("canSeeArchitectExpressSurvey"))
  val ViewUIArchitectStreamlinedSurvey: Capability = Capability("viewUIArchitectStreamlinedSurvey", "Allows user to view the streamline survey", Some(Seq(Architect)), Some("canSeeArchitectStreamlinedSurvey"))
  val ViewUIArchitectLongFormSurvey: Capability = Capability("viewUIArchitectLongFormSurvey", "Allows user to view the comprehensive survey", Some(Seq(Architect)), Some("canSeeArchitectLongFormSurvey"))
  val ViewUIArchitectAnalysisPageUAF: Capability = Capability("viewUIArchitectAnalysisPageUAF", "Allows user to view the uaf chart in analysis page", Some(Seq(Architect)), Some("canSeeArchitectAnalysisPageUAF"))
  val ViewUIArchitectMFE: Capability = Capability("viewUIArchitectMFE", "Allows user to view the MFE of Architect", Some(Seq(Architect)), Some("canSeeArchitectMFE"))

  val availableAccessKeysGen: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {

    override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys),
      ViewUISPPortalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIMarketplaceRatesCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIEquitiesMarketplaceCapacityCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIRatesMarketplaceCapacityCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIIssuerPortalSuspendCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIIssuerPortalCapacityCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIDollarCommissionCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIFIAPortalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISVAPortalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISPManagementDashboardCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIMultipleNetworksCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIBroadcastInNetworkOnlyCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIBroadcastOutsideNetworkCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIExportCurrentOfferingNetworkIOIsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIShareOutsideNetworkCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIUseBrokerDealerCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIIssuerPortalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIWholesalerPortalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterDetailsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIHoldingsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRIAPopUpCapability.name -> AvailableKeyBuilder(hasNetworkTypeRIAorIMOKeyBuilder),
      ViewBuilderCapability.name -> AvailableKeyBuilder(anyPayoffBuildEntitlementsKeyBuilder),
      ViewUIBacktestingCapability.name -> AvailableKeyBuilder(anyPayOffBacktestEntitlementsKeyBuilder),
      ViewUISPDetailsCapability.name -> AvailableKeyBuilder(anyPayoffDetailsEntitlementsKeyBuilder),
      ViewUIHoldingsDocUploadCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIClientHoldingsGroupByHouseholdCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterMyNetworkCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV2Capability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3Capability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV2StructuredInvestmentsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3StructuredInvestmentsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV2AnnuitiesCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3AnnuitiesCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV2DefinedOutcomeETFsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3DefinedOutcomeETFsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV2DigitalAssetsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3DigitalAssetsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV2AlternativeInvestmentsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3AlternativeInvestmentsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV2CustomResourceOrderCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3CustomResourceOrderCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV2ResourceWithoutTrainingCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3ResourceWithoutTrainingCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV2DefinedOutcomeETFMPlusFundsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3DefinedOutcomeETFMPlusFundsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILifecyclePortalRollsActionsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUICloseOutCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISPOrdersCloseoutWorkflowCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISPOrdersAuditCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIDeveloperFeatureCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUITierCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqHomeOfficeCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqCopyToClipboardCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqNewSubmissionLayoutCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqIssuerNewSubmissionLayoutCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqNewCardLayoutCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqIssuerNewCardLayoutCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqIssuerCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqFACapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqWholesalerCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqMultiIssuerCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqGenericsModalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqCreateTemplateForNetworkCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqQuantoCompoFieldCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqStrikeDateFieldCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqSettlementDateFieldCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqPricingFeedbackCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqWholesalerInfoCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqDeclineReasonsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqRulesEngineResultsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqCrowdsourcingPortalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqQuoteDetailsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqAuditTrailCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqIdeaGenerationCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqCreateTemplateCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqSubmitButtonCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqTemplateNameInPlaceOfDisplayNameCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqTemplateRequestToTradeCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqTemplateVariationCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqDocumentReviewWorkflowCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqToContractOfferingWorkflowCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqDealTypeSelectionCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqAveragingPaymentDetailsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqJpmPbCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqModal54WeekToggleCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIContractExportToExcelCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqDenominationCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqSortedByLevelCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqRequestFirmQuoteCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqCloningCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqExternalIdInputCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqContractUploadedStateCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqModalAutopopulateFACapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqModalAutopopulateNetworkCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqSolvedRangeCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqOptInPricingFeedbackByDefaultCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIBuilderRfqButtonCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqIdeaGenerationTabCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqPendingIdeaTabCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqActiveRfqsTabCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRfqActiveRftsTabCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIExportPDFCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIBuilderV2ViaBuildPayoffEntitlementCapability.name -> AvailableKeyBuilder(anyPayoffBuildEntitlementsKeyBuilder),
      ViewUILearningCenterV2TrainingUploadCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3TrainingUploadCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUICarrierPortalRateUploadCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUICarrierPortalMorningStarMappingUploadCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOrderPreTradeValidationCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOrderCreateNewOrderAccountCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOrderBulkUploadCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOrderBulkUploadDownloadForLPLCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOrderEntryClientSoliticationCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOrderEntryCustomerDateTimeCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOrderEntryDiscretionaryCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOrderEntryOnBehalfOfCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOrderEntryRepCodeCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOrderEntryFeesCommissionCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOrderEntryAccountTypeNumberPhoneRepNameCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOrderEntryAcceptedByFaNumberCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOrderEntryNetworkCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOrderEntryAccountSearchCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOrderEntryAccountInputNumbersOnlyValidationCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOrderEntryCommentCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOrderEntryHideDefaultAttestationCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOrderEntryRockefellerCapability.name ->  AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIIOILocationsColumnCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIBroadridgeIOIDownloadCapability.name ->  AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIPershingIOIDownloadCapability.name ->  AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIBloombergMTEIOIDownloadCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIMTDIOIDownloadCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUICusipIOIDownloadCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV2MyNetworkProductTrainingCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3MyNetworkProductTrainingCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV2AnnuitiesMyNetworkEducationCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3AnnuitiesMyNetworkEducationCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV2AlternativesMyNetworkEducationCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3AlternativesMyNetworkEducationCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIPendingOfferingsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAnalyticsAsOfDateCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIHoldingsAsOfDatesCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRetriggerDTCCCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIContractEditCapability.name -> AvailableKeyBuilder(anyPayOffEditEntitlementsKeyBuilder),
      ViewUIDistributorSymbolCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIProductNomenclatureLongNameCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRejectedIOIsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewNewSILifecycleDashboardCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewFANumberSelectorDashboardCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOnboardingToolCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISwitchUsersNetworkCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIExportToTwdXlsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUITMCReportCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOlarkCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUITMCReportCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIQuoteTypeCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUICustomAttestationViaNetworkCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIDefinedOutcomeETFCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIDefinedOutcomeETFViewAsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIDefinedOutcomeETFMPlusFundsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISpectrumLC2SPCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIContractRangesCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIStagedOfferingsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRejectedOfferingsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIFAPortalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIVAPortalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIFAPortalYieldToSurrenderCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIMyProductsDashboardCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILifecycleEventsDashboardCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUINotificationsNewsfeedDashboardCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOfferingsReportCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOfferingDocViaModalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILifecycleNotificationSettingsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIEditNetworkLifecycleNotificationSettingsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILifecyclePortalSPCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILifecyclePortalSPShareViaLinkCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILifecyclePortalSPEditNetworkDefaultCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILifecyclePortalSPWidgetContractOfferingsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILifecyclePortalSPBarrierAnalysisWidgetCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILifecyclePortalSPCapAnalysisWidgetCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILifecyclePortalSPSectorAllocationWidgetCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILifecyclePortalSPUnderlierPerformanceWidgetCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILifecyclePortalSIClientsWidgetCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILifecyclePortalPartialSearchCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILifecyclePortalPartitionedQueryCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISearchBarCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIEquitiesLifecycleDTCCPaymentCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIEquitiesBufferedLeveredNoteLifecycleDTCCPaymentCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIViewAsLifecycleSPCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIHeatmapCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILifecyclePortalAnnuitiesCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILifecyclePortalInsuranceCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIProductNomenclatureCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISpectrumMarketplaceSICapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRefinitivDeepLinksCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIFABrandingCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIBetaPlusMarketplaceCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIPerfPDFClientNameCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIPerfPDFViaServiceCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIPerfPDFFaGenerateInvestorReportMessageCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIImpersonateAnnuityMarketplaceCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISpectrumBuilderCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUITradewebOrderTokenCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUITradewebEquitiesManageApprovalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUITradewebRatesManageApprovalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIBuilderDistributorNomenclatureCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      HideUILifecyclePortalExportToExcelCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIEmbeddedExperienceCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISpectrumLC2AnnuitiesCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIUnderlierMarketCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIStructuredETFIssuerPortalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIStructuredETFPendingOfferingsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAppcuesDropdownCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISpectrumMarketplaceAnnuitiesCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIBeaconCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISpectrumAnnPDFCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISpectrumSICUSIPModalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILifecyclePortalSpectrumCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISpectrumSIPDFCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIEnhancedAnnuityOrderCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILevelSystemCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIDefinedOutcomeETFUpsideShieldCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIDefinedOutcomeETFPerformanceAnalysisCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIDefinedOutcomeETFComparisonCalculatorCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIIncomeBacktestingToolCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIFIDxCarrierPortalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIQuickSearch2Capability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIQuickSearchYMBICategoryCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIProductSearchCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIMON3Capability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIHistoricalHoldingsSICapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIWhiteLabelingCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUICustomAltsHomepageCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIBottomDisclosureCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIHideHomepageLCWidgetCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIHideIOIWidgetCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIMarketplaceViewAsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAltsPortalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIIpipelineCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIBuilderPDFCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIMarketplaceRollsWidgetCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIMarketplaceExportExcelCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIMarketplaceDenominationCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOptimizationToolSICapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOptimizationToolAnnuityCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIUploadAdditionalDocumentCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIPlusSubscribeAltsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAltsPerformanceCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOverrideSPOfferingsBooksCloseTimeCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUITermInMonthsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV2DefinedOutcomeETFsMyNetworkEducationCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3DefinedOutcomeETFsMyNetworkEducationCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAltsPendingOfferingsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAltsStagedOfferingsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAltsClosedOfferingsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISubscribeInvestmentEntityOptionsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOptimizationToolSIPDFCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOptimizationToolAnnuityPDFCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAddRfqIssuersToAuctionCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIGrantModeCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISMAAccountPortalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISMASelectAccountStrategyCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIBeaconIframeCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISiloedExperienceCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAltsViewDetailsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOptimizationGrowthOnlyProductsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOptimizationToolPDFDisclosureRJCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOptimizationToolPDFBrandingCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOptimizationToolPDFDisclosureModalClientCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOptimizationToolPDFDisclosureModalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAnnuitiesSaveAllocationsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUICoBrandingCustomRJCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUICalculatedLifecycleEventsStatusCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUICalculatedLifecycleEventsStatusEmailCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIDigitalAssetsPortalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIIAltsSubscriptionWorkflowCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIHideIncomeCalculatorCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRiskTypeModalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRiskTypeModalOverrideCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRiskTypeCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISpectrumLC2FixedIndexedAnnuitiesCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOptimizationToolFixedIndexedAnnuitiesCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISpectrumAdvisorFIAPDFCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOptimizationToolAdvisorFIAPDFCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISpectrumSICUSIPModalScoresCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIEnvestnetCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIEnvestnetSkipClientQuestionnaire.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIEnvestnetArchitectIntegration.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectExportPortfolioButton.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIUserImpersonationCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIUserImpersonationForHOUsersCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      HideUIBacktestingComparisonCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIEstimatedEodValuationsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAGGridOnLifecyclePortalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAGGridOnLifecyclePortalSkipMigrationsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAGGridOnRfqCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUINetworkVisualsFromProfileCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIMarketDataEndpointForUnderlierPricesCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOptimizationToolClientFIAPDFCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISpectrumClientFIAPDFCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIHideSIMONBrandingCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIICapitalAltOfferingsLinkCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIFIDxActivityWorkflowCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      HideUIAllocationSummaryCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAGGridClientPages.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAGGridOnSalesBook.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUINewLifecycleEmailsAccountDetailsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIVAEnhancedExperience.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIGPPortalApp.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAIChatApp.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAIChatExperimentalPlugins.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIContactUs.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectAnalysisPageSpectrumAnalysis.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectScenariosChart.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectGrowthChart.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectCorrelationMatrixChart.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectFactorsChart.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectAdvisorPDFs.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectClients.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectBuilderGenericAlts.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectBuilderRealAlts.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectBuilderGenericSI.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectBuilderRealSI.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectBuilderGenericTraditional.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectBuilderGenericAltsSpectrumScores.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectBuilderRealAltsSpectrumScores.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectBuilderGenericSISpectrumScores.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectBuilderRealSISpectrumScores.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectBuilderGenericTraditionalSpectrumScores.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      HideUIArchitectNavBar.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      HideUIArchitectPDFsWatermarks.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      HideSpectrumPDFNetworkNameCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectAdminTool.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOnlyCommissionOnSimonCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIFixedAnnuityRatesWidgetCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIFeeAnalysisWidgetCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIIncomeRiderNoWithdrawalWidgetCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIBenefitsAnalysisWidgetCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAnnuityExplorerWidgetCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAnnuityClientAgeDistributionWidgetCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAnnuityLowAccountValueWidgetCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIPlatformToggleCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIPlatformEnhancementsPageCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIUnifiedEducationCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAnnuityFullApprovalModalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIDeveloperHubCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      HideUIManagedAccountHoldingsInDashboardWidgetsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIUnifiedEducationGeneralAndRegulatoryCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAccountIdGlobalFilterCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIIssuerDashboardCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIForYouOverrideCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectShareCustomAssets.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectShareCustomAssetsAll.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectSharePortfolios.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectSharePortfoliosAll.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectCreateCustomAssets.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectPortfolioGrouping.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectAssetClassOptimizations.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectFindSimilarAssets.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectBuilderSleeves.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectBuilderInvest.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectDownloadPDFs.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectClientPDFs.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIOffPlatformAnnuitiesCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIOrdersEnhancedCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIMultiDocsInIssuerPortalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIMultiCurrencyInLifecyclePortalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIBatchUnentitleCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIUnblockedPerformanceAnalysisCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIUserManagementCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIUserManagementUnifiedCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAnnuitiesLifecycleFANumberSearchCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIFixedAnnuitiesRatesOutputPDFCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectTermsOfServiceModal.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectProRataAllocations.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3InsightsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3Capability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAnnuityLifecycleNotificationCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIAllocationBacktestingV2Capability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRILAIndexStrategyBacktestingPDFCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIUnifiedHomepageCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIUnifiedSIMarketplaceCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIUnifiedAnnuitiesMarketplaceCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIUnifiedETFMarketplaceCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIUnifiedSMAMarketplaceCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIApprovalImpersonateCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3GlossaryCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3AlternativeInvestmentsResourcesCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3StructuredInvestmentsResourcesCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3AnnuitiesResourcesCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3DefinedOutcomeETFsResourcesCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3GeneralAndRegulatoryResourcesCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3CustomizationsPortalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIAIUploadsCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUILearningCenterV3CourseFinderCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISITradeTminus2Capability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIAdditionalDetailsCardCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectSharePortfoliosIndividuals.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectShareCustomAssetsIndividuals.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectSharePortfoliosNetworks.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectShareCustomAssetsNetworks.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectBuilderInvestInvestmentStatusAlts.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectBuilderInvestInvestmentStatusSI.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectBuilderReplaceHoldings.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIContractsOfferingsUpdatesApiCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIContractsOfferingsUbertableCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIContractsOfferingsBulkApisCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIContractsOfferingsApprovalApiCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIPortfolioPDFCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIPortfolioPDFClientNameCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUISIPortfolioPDFCusipLevelCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIWholesalerExchangeCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIWholesalerExchangeAsTechUserCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectHoldingsOptimizations.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIRegBIEvaluation.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectExpressSurvey.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectStreamlinedSurvey.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectLongFormSurvey.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectAnalysisPageUAF.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewUIArchitectMFE.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
    )

    private def hasNetworkTypeRIAorIMOKeyBuilder(capability: String, user:UserACL): Set[String] =
      if (user.hasRIANetworkType || user.hasIMONetworkType) Set(capability) else Set.empty

    private def anyPayoffBuildEntitlementsKeyBuilder(capabilities: String, user: UserACL): Set[String] =
      if (user.canBuildAnyPayoff) Set(capabilities) else Set.empty

    private def anyPayOffBacktestEntitlementsKeyBuilder(capabilities: String, user: UserACL): Set[String] =
      if (user.canBacktestAnyPayOff) Set(capabilities) else Set.empty

    private def anyPayOffEditEntitlementsKeyBuilder(capabilities: String, user: UserACL): Set[String] =
      if (user.canEditAnyPayOff) Set(capabilities) else Set.empty

    private def anyPayoffDetailsEntitlementsKeyBuilder(capabilities: String, user: UserACL): Set[String] =
      if (user.canDetailsAnyPayOff) Set(capabilities) else Set.empty

  }

  val SPPortalCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISPPortalCapability))
  val ViewUISIMarketplaceRatesCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISIMarketplaceRatesCapability))
  val ViewUISIEquitiesMarketplaceCapacityCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISIEquitiesMarketplaceCapacityCapability))
  val ViewUISIRatesMarketplaceCapacityCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISIRatesMarketplaceCapacityCapability))
  val ViewUISIIssuerPortalSuspendCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISIIssuerPortalSuspendCapability))
  val ViewUISIIssuerPortalCapacityCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISIIssuerPortalCapacityCapability))
  val ViewUISIDollarCommissionCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISIDollarCommissionCapability))
  val FIAPortalCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIFIAPortalCapability))
  val SVAPortalCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISVAPortalCapability))
  val SPManagementDashboardCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISPManagementDashboardCapability))
  val MultipleNetworksCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIMultipleNetworksCapability))
  val BroadcastInNetworkOnlyCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIBroadcastInNetworkOnlyCapability))
  val BroadcastOutsideNetworkCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIBroadcastOutsideNetworkCapability))
  val ExportCurrentOfferingNetworkIOIsCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIExportCurrentOfferingNetworkIOIsCapability))
  val ShareOutsideNetworkCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIShareOutsideNetworkCapability))
  val UseBrokerDealerCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIUseBrokerDealerCapability))
  val IssuerPortalCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIIssuerPortalCapability))
  val WholesalerPortalCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIWholesalerPortalCapability))
  val LearningCenterDetailsCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUILearningCenterDetailsCapability))
  val LearningCenterCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUILearningCenterCapability))
  val LifecyclePortalCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIHoldingsCapability))
  val RIACapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRIAPopUpCapability))
  val BuilderCapabilities: CapabilitySet = CapabilitySet(Set(ViewBuilderCapability, AdminCapability))
  val BacktestingCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIBacktestingCapability))
  val DetailsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUISPDetailsCapability))
  val HoldingsDocUploadCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIHoldingsDocUploadCapability))
  val ClientHoldingsGroupByHouseholdCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIClientHoldingsGroupByHouseholdCapability))
  val ViewUIOnboardingToolCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIOnboardingToolCapability))
  val ViewUISwitchUsersNetworkCapabilities: CapabilitySet = CapabilitySet(Set(ViewUISwitchUsersNetworkCapability))
  val LearningCenterMyNetworkCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterMyNetworkCapability, AdminCapability))
  val ViewLearningCenterV2Capabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV2Capability)) //Specifically not auto turned on for Admin
  val ViewLearningCenterV3Capabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV3Capability)) //Specifically not auto turned on for Admin
  val ViewLearningCenterV2StructuredInvestmentsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV2StructuredInvestmentsCapability)) //Specifically not auto turned on for Admin
  val ViewLearningCenterV3StructuredInvestmentsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV3StructuredInvestmentsCapability)) //Specifically not auto turned on for Admin
  val ViewLearningCenterV2AnnuitiesCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV2AnnuitiesCapability)) //Specifically not auto turned on for Admin
  val ViewLearningCenterV3AnnuitiesCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV3AnnuitiesCapability)) //Specifically not auto turned on for Admin
  val ViewLearningCenterV2DefinedOutcomeETFsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV2DefinedOutcomeETFsCapability)) //Specifically not auto turned on for Admin
  val ViewLearningCenterV3DefinedOutcomeETFsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV3DefinedOutcomeETFsCapability)) //Specifically not auto turned on for Admin
  val ViewLearningCenterV2DigitalAssetsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV2DigitalAssetsCapability)) //Specifically not auto turned on for Admin
  val ViewLearningCenterV3DigitalAssetsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV3DigitalAssetsCapability)) //Specifically not auto turned on for Admin
  val ViewLearningCenterV2AlternativeInvestmentsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV2AlternativeInvestmentsCapability)) //Specifically not auto turned on for Admin
  val ViewLearningCenterV3AlternativeInvestmentsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV3AlternativeInvestmentsCapability)) //Specifically not auto turned on for Admin
  val ViewLearningCenterV2ViewCustomResourceOrderCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV2CustomResourceOrderCapability))
  val ViewLearningCenterV3ViewCustomResourceOrderCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV3CustomResourceOrderCapability))
  val ViewLearningCenterV2ResourceWithoutTrainingCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV2ResourceWithoutTrainingCapability)) //Specifically not auto turned on for Admin
  val ViewLearningCenterV3ResourceWithoutTrainingCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV3ResourceWithoutTrainingCapability)) //Specifically not auto turned on for Admin
  val ViewUILearningCenterV2MyNetworkProductTrainingCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV2MyNetworkProductTrainingCapability)) //Specifically not auto turned on for Admin
  val ViewUILearningCenterV3MyNetworkProductTrainingCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV3MyNetworkProductTrainingCapability)) //Specifically not auto turned on for Admin
  val ViewUILearningCenterV2AnnuitiesMyNetworkEducationCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV2AnnuitiesMyNetworkEducationCapability, AdminCapability))
  val ViewUILearningCenterV3AnnuitiesMyNetworkEducationCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV3AnnuitiesMyNetworkEducationCapability, AdminCapability))
  val ViewUILearningCenterV2AlternativesMyNetworkEducationCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV2AlternativesMyNetworkEducationCapability, AdminCapability))
  val ViewUILearningCenterV3AlternativesMyNetworkEducationCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV3AlternativesMyNetworkEducationCapability, AdminCapability))
  val ViewUILifecyclePortalRollsActionsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILifecyclePortalRollsActionsCapability, AdminCapability))
  val ViewLearningCenterV2DefinedOutcomeETFMPlusFundsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV2DefinedOutcomeETFMPlusFundsCapability, AdminCapability))
  val ViewLearningCenterV3DefinedOutcomeETFMPlusFundsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV3DefinedOutcomeETFMPlusFundsCapability, AdminCapability))
  val ViewCloseOutCapabilities: CapabilitySet = CapabilitySet(Set(ViewUICloseOutCapability)) //Specifically not auto turned on for Admin
  val ViewSPOrdersCloseoutWorkflowCapabilities: CapabilitySet = CapabilitySet(Set(ViewUISPOrdersCloseoutWorkflowCapability)) //Specifically not auto turned on for Admin
  val ViewUISPOrdersAuditCapabilities: CapabilitySet = CapabilitySet(Set(ViewUISPOrdersAuditCapability, AdminCapability))
  val ViewDeveloperFeatureCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIDeveloperFeatureCapability, AdminCapability))
  val ViewTierCapabilities: CapabilitySet = CapabilitySet(Set(ViewUITierCapability, AdminCapability))
  val ViewRfqHomeOfficeCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqHomeOfficeCapability))
  val ViewRfqIssuerCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqIssuerCapability))
  val ViewRfqMultiIssuerCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqMultiIssuerCapability))
  val ViewRfqFACapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqFACapability))
  val ViewRfqWholesalerCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqWholesalerCapability))
  val ViewRfqGenericsModalCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqGenericsModalCapability, AdminCapability))
  val ViewRfqCreateTemplateForNetworkCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqCreateTemplateForNetworkCapability, AdminCapability))
  val ViewRfqQuantoCompoFieldCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqQuantoCompoFieldCapability, AdminCapability))
  val ViewUIRfqNewSubmissionLayoutCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqNewSubmissionLayoutCapability))
  val ViewUIRfqCopyToClipboardCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqCopyToClipboardCapability))
  val ViewUIRfqIssuerNewSubmissionLayoutCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqIssuerNewSubmissionLayoutCapability))
  val ViewUIRfqNewCardLayoutCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqNewCardLayoutCapability))
  val ViewUIRfqIssuerNewCardLayoutCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqIssuerNewCardLayoutCapability))
  val ViewUIRfqStrikeDateFieldCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqStrikeDateFieldCapability, AdminCapability))
  val ViewUIRfqSettlementDateFieldCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqSettlementDateFieldCapability, AdminCapability))
  val ViewUIRfqWholesalerInfoCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqWholesalerInfoCapability, AdminCapability))
  val ViewUIRfqDeclineReasonsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqDeclineReasonsCapability, AdminCapability))
  val ViewUIRfqRulesEngineResultsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqRulesEngineResultsCapability, AdminCapability))
  val ViewUIRfqCrowdsourcingPortalCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqCrowdsourcingPortalCapability, AdminCapability))
  val ViewUIRfqAuditTrailCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqAuditTrailCapability, AdminCapability))
  val ViewUIRfqIdeaGenerationCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqIdeaGenerationCapability, AdminCapability))
  val ViewUIRfqCreateTemplateCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqCreateTemplateCapability, AdminCapability))
  val ViewUIRfqSubmitButtonCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqSubmitButtonCapability, AdminCapability))
  val ViewUIRfqTemplateNameInPlaceOfDisplayNameCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqTemplateNameInPlaceOfDisplayNameCapability))
  val ViewUIRfqTemplateRequestToTradeCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqTemplateRequestToTradeCapability, AdminCapability))
  val ViewUIRfqTemplateVariationCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqTemplateVariationCapability))
  val ViewUIRfqQuoteDetailsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqQuoteDetailsCapability, AdminCapability))
  val ViewUIRfqSortedByLevelCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqSortedByLevelCapability, AdminCapability))
  val ViewUIBuilderRfqButtonCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIBuilderRfqButtonCapability, AdminCapability))
  val ViewUIRfqIdeaGenerationTabCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqIdeaGenerationTabCapability, AdminCapability))
  val ViewUIRfqPendingIdeaTabCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqPendingIdeaTabCapability, AdminCapability))
  val ViewUIRfqActiveRfqsTabCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqActiveRfqsTabCapability, AdminCapability))
  val ViewUIRfqActiveRftsTabCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqActiveRftsTabCapability, AdminCapability))
  val ViewUIRfqPricingFeedbackCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqPricingFeedbackCapability, AdminCapability))
  val ViewUIRfqDocumentReviewWorkflowCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqDocumentReviewWorkflowCapability, AdminCapability))
  val ViewUIRfqToContractOfferingWorkflowCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqToContractOfferingWorkflowCapability, AdminCapability))
  val ViewUIRfqDealTypeSelectionCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqDealTypeSelectionCapability, AdminCapability))
  val ViewUIRfqAveragingPaymentDetailsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqAveragingPaymentDetailsCapability, AdminCapability))
  val ViewUIRfqJpmPbCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqJpmPbCapability, AdminCapability))
  val ViewUIRfqModal54WeekToggleCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqModal54WeekToggleCapability, AdminCapability))
  val ViewUIContractExportToExcelCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIContractExportToExcelCapability, AdminCapability))
  val ViewUIRfqDenominationCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqDenominationCapability, AdminCapability))
  val ViewUIRfqRequestFirmQuoteCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqRequestFirmQuoteCapability, AdminCapability))
  val ViewUIRfqCloningCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqCloningCapability, AdminCapability))
  val ViewUIRfqExternalIdInputCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqExternalIdInputCapability, AdminCapability))
  val ViewUIRfqContractUploadedStateCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqContractUploadedStateCapability, AdminCapability))
  val ViewUIRfqModalAutopopulateFACapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqModalAutopopulateFACapability, AdminCapability))
  val ViewUIRfqModalAutopopulateNetworkCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqModalAutopopulateNetworkCapability, AdminCapability))
  val ViewUIRfqSolvedRangeCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqSolvedRangeCapability))
  val ViewUIRfqOptInPricingFeedbackByDefaultCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRfqOptInPricingFeedbackByDefaultCapability))
  val ViewExportPDFCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIExportPDFCapability, AdminCapability))
  val ViewUIBuilderV2Capabilities: CapabilitySet = CapabilitySet(Set(ViewUIBuilderV2ViaBuildPayoffEntitlementCapability, AdminCapability))
  val ViewUILearningCenterV2TrainingUploadCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV2TrainingUploadCapability, AdminCapability))
  val ViewUILearningCenterV3TrainingUploadCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV3TrainingUploadCapability, AdminCapability))
  val ViewUICarrierPortalRateUploadCapabilities: CapabilitySet = CapabilitySet(Set(ViewUICarrierPortalRateUploadCapability, AdminCapability))
  val ViewUICarrierPortalMorningStarMappingUploadCapabilities: CapabilitySet = CapabilitySet(Set(ViewUICarrierPortalMorningStarMappingUploadCapability, AdminCapability))
  val ViewUIIOILocationsColumnCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIIOILocationsColumnCapability, AdminCapability))
  val ViewUIOrderEntryAcceptedByFaNumberCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOrderEntryAcceptedByFaNumberCapability))
  val ViewUIOrderEntryFeesCommissionCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOrderEntryFeesCommissionCapability))
  val ViewUIOrderEntryAccountTypeNumberPhoneRepNameCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOrderEntryAccountTypeNumberPhoneRepNameCapability))
  val ViewUIOrderEntryRepCodeCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOrderEntryRepCodeCapability))
  val ViewUIOrderEntryOnBehalfOfCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOrderEntryOnBehalfOfCapability))
  val ViewUIOrderEntryDiscretionaryCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOrderEntryDiscretionaryCapability))
  val ViewUIOrderEntryCustomerDateTimeCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOrderEntryCustomerDateTimeCapability))
  val ViewUIOrderEntryClientSoliticationCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOrderEntryClientSoliticationCapability))
  val ViewUIOrderEntryNetworkCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOrderEntryNetworkCapability))
  val ViewUIOrderEntryAccountSearchCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOrderEntryAccountSearchCapability))
  val ViewUIOrderEntryAccountInputNumbersOnlyValidationCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOrderEntryAccountInputNumbersOnlyValidationCapability))
  val ViewUIOrderEntryCommentCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOrderEntryCommentCapability))
  val ViewUIOrderPreTradeValidationCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOrderPreTradeValidationCapability))
  val ViewUIOrderCreateNewOrderAccountCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOrderCreateNewOrderAccountCapability))
  val ViewUIOrderBulkUploadCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOrderBulkUploadCapability))
  val ViewUIOrderBulkUploadDownloadForLPLCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOrderBulkUploadDownloadForLPLCapability))
  val ViewUIOrderEntryHideDefaultAttestationCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOrderEntryHideDefaultAttestationCapability))
  val ViewUIOrderEntryRockefellerCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOrderEntryRockefellerCapability))
  val ViewUIBroadridgeIOIDownloadCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIBroadridgeIOIDownloadCapability))
  val ViewUIPershingIOIDownloadCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIPershingIOIDownloadCapability))
  val ViewUIBloombergMTEIOIDownloadCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIBloombergMTEIOIDownloadCapability))
  val ViewUIMTDIOIDownloadCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIMTDIOIDownloadCapability))
  val ViewUICusipIOIDownloadCapabilities: CapabilitySet = CapabilitySet(Set(ViewUICusipIOIDownloadCapability))
  val ViewUIPendingOfferingsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIPendingOfferingsCapability, AdminCapability))
  val ViewUIAnalyticsAsOfDateCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIAnalyticsAsOfDateCapability))
  val ViewUIHoldingsAsOfDatesCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIHoldingsAsOfDatesCapability))
  val ViewUIRetriggerDTCCCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRetriggerDTCCCapability, AdminCapability))
  val ViewUIContractEditCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIContractEditCapability, AdminCapability))
  val ViewUIDistributorSymbolCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIDistributorSymbolCapability))
  val ViewUIProductNomenclatureLongNameCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIProductNomenclatureLongNameCapability))
  val ViewUIRejectedIOIsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRejectedIOIsCapability))
  val ViewNewSILifecycleDashboardCapabilities: CapabilitySet = CapabilitySet(Set(ViewNewSILifecycleDashboardCapability))
  val ViewFANumberSelectorDashboardCapabilities: CapabilitySet = CapabilitySet(Set(ViewFANumberSelectorDashboardCapability))
  val ViewExportToTwdXlsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIExportToTwdXlsCapability, AdminCapability))
  val ViewUITMCReportCapabilities: CapabilitySet = CapabilitySet(Set(ViewUITMCReportCapability, AdminCapability))
  val ViewUIOlarkCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOlarkCapability, AdminCapability))
  val ViewUIQuoteTypeCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIQuoteTypeCapability))
  val ViewUICustomAttestationViaNetworkCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUICustomAttestationViaNetworkCapability))
  val ViewUIDefinedOutcomeETFCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIDefinedOutcomeETFCapability))
  val ViewUIDefinedOutcomeETFViewAsCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIDefinedOutcomeETFViewAsCapability))
  val ViewUIDefinedOutcomeETFMPlusFundsCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIDefinedOutcomeETFMPlusFundsCapability))
  val ViewUISpectrumLC2SPCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISpectrumLC2SPCapability))
  val ViewUIContractRangesCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIContractRangesCapability))
  val ViewUIStagedOfferingsCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIStagedOfferingsCapability))
  val ViewUIRejectedOfferingsCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIRejectedOfferingsCapability))
  val ViewUIFAPortalCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIFAPortalCapability))
  val ViewUIVAPortalCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIVAPortalCapability))
  val ViewUIFAPortalYieldToSurrenderCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIFAPortalYieldToSurrenderCapability))
  val ViewUIMyProductsDashboardCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIMyProductsDashboardCapability))
  val ViewUILifecycleEventsDashboardCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUILifecycleEventsDashboardCapability))
  val ViewUINotificationsNewsfeedDashboardCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUINotificationsNewsfeedDashboardCapability))
  val ViewUIOfferingsReportCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIOfferingsReportCapability))
  val ViewUIOfferingDocViaModalCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOfferingDocViaModalCapability))
  val ViewUILifecyclePortalSPCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUILifecyclePortalSPCapability))
  val ViewUILifecyclePortalSPShareViaLinkCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILifecyclePortalSPShareViaLinkCapability))
  val ViewUILifecyclePortalSPEditNetworkDefaultCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILifecyclePortalSPEditNetworkDefaultCapability))
  val ViewUILifecyclePortalSPWidgetContractOfferingsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILifecyclePortalSPWidgetContractOfferingsCapability))
  val ViewUILifecyclePortalSPBarrierAnalysisWidgetCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILifecyclePortalSPBarrierAnalysisWidgetCapability))
  val ViewUILifecyclePortalSPCapAnalysisWidgetCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILifecyclePortalSPCapAnalysisWidgetCapability))
  val ViewUILifecyclePortalSPSectorAllocationWidgetCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILifecyclePortalSPSectorAllocationWidgetCapability))
  val ViewUILifecyclePortalSPUnderlierPerformanceWidgetCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILifecyclePortalSPUnderlierPerformanceWidgetCapability))
  val ViewUILifecyclePortalSIClientsWidgetCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILifecyclePortalSIClientsWidgetCapability))
  val ViewUILifecyclePortalPartialSearchCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUILifecyclePortalPartialSearchCapability))
  val ViewUILifecyclePortalPartitionedQueryCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILifecyclePortalPartitionedQueryCapability))
  val ViewUISearchBarCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISearchBarCapability))
  val ViewUISIEquitiesLifecycleDTCCPaymentCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISIEquitiesLifecycleDTCCPaymentCapability))
  val ViewUISIEquitiesBufferedLeveredNoteLifecycleDTCCPaymentCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISIEquitiesBufferedLeveredNoteLifecycleDTCCPaymentCapability))
  val ViewUIViewAsLifecycleSPCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIViewAsLifecycleSPCapability))
  val ViewUIHeatmapCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIHeatmapCapability))
  val ViewUILifecyclePortalAnnuitiesCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUILifecyclePortalAnnuitiesCapability))
  val ViewUILifecyclePortalInsuranceCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUILifecyclePortalInsuranceCapability))
  val ViewUIProductNomenclatureCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIProductNomenclatureCapability))
  val ViewUISpectrumMarketplaceSICapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISpectrumMarketplaceSICapability))
  val ViewUIRefinitivDeepLinksCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIRefinitivDeepLinksCapability))
  val ViewUIFABrandingCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIFABrandingCapability))
  val ViewUIBetaPlusMarketplaceCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIBetaPlusMarketplaceCapability))
  val ViewUIPerfPDFClientNameCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIPerfPDFClientNameCapability))
  val ViewUIPerfPDFViaServiceCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIPerfPDFViaServiceCapability))
  val ViewUIPerfPDFFaGenerateInvestorReportMessageCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIPerfPDFFaGenerateInvestorReportMessageCapability))
  val ViewUIImpersonateAnnuityMarketplaceCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIImpersonateAnnuityMarketplaceCapability))
  val ViewUISpectrumBuilderCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISpectrumBuilderCapability))
  val ViewUITradewebOrderTokenCapabilities: CapabilitySet = CapabilitySet(Set(ViewUITradewebOrderTokenCapability))
  val ViewUITradewebEquitiesManageApprovalCapabilities: CapabilitySet = CapabilitySet(Set(ViewUITradewebEquitiesManageApprovalCapability))
  val ViewUITradewebRatesManageApprovalCapabilities: CapabilitySet = CapabilitySet(Set(ViewUITradewebRatesManageApprovalCapability))
  val ViewUIBuilderDistributorNomenclatureCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIBuilderDistributorNomenclatureCapability))
  val HideUILifecyclePortalExportToExcelCapabilities: CapabilitySet= CapabilitySet(Set(HideUILifecyclePortalExportToExcelCapability))
  val ViewUIEmbeddedExperienceCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIEmbeddedExperienceCapability))
  val ViewUISpectrumLC2AnnuitiesCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISpectrumLC2AnnuitiesCapability))
  val ViewUIUnderlierMarketCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIUnderlierMarketCapability))
  val ViewUIStructuredETFIssuerPortalCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIStructuredETFIssuerPortalCapability))
  val ViewUIStructuredETFPendingOfferingsCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIStructuredETFPendingOfferingsCapability))
  val ViewUIAppcuesDropdownCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIAppcuesDropdownCapability))
  val ViewUISpectrumMarketplaceAnnuitiesCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISpectrumMarketplaceAnnuitiesCapability))
  val ViewUIBeaconCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIBeaconCapability))
  val ViewUISpectrumPDFAnnCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISpectrumAnnPDFCapability))
  val ViewUISpectrumSICUSIPModalCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISpectrumSICUSIPModalCapability))
  val ViewUILifecyclePortalSpectrumCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUILifecyclePortalSpectrumCapability))
  val ViewUISpectrumPDFSICapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISpectrumSIPDFCapability))
  val ViewUISpectrumLC2FixedIndexedAnnuitiesCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISpectrumLC2FixedIndexedAnnuitiesCapability))
  val ViewUIOptimizationToolFixedIndexedAnnuitiesCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIOptimizationToolFixedIndexedAnnuitiesCapability))
  val ViewUISpectrumAdvisorFIAPDFCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISpectrumAdvisorFIAPDFCapability))
  val ViewUIOptimizationToolAdvisorFIAPDFCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIOptimizationToolAdvisorFIAPDFCapability))
  val ViewUIOptimizationToolClientFIAPDFCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIOptimizationToolClientFIAPDFCapability))
  val ViewUIEnhancedAnnuityOrderCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIEnhancedAnnuityOrderCapability))
  val ViewUILevelSystemCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUILevelSystemCapability))
  val ViewUIDefinedOutcomeETFUpsideShieldCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIDefinedOutcomeETFUpsideShieldCapability))
  val ViewUIDefinedOutcomeETFPerformanceAnalysisCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIDefinedOutcomeETFPerformanceAnalysisCapability))
  val ViewUIDefinedOutcomeETFComparisonCalculatorCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIDefinedOutcomeETFComparisonCalculatorCapability))
  val ViewUIIncomeBacktestingToolCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIIncomeBacktestingToolCapability))
  val ViewUIFIDxCarrierPortalCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIFIDxCarrierPortalCapability))
  val ViewUIQuickSearch2Capabilities: CapabilitySet = CapabilitySet(Set(ViewUIQuickSearch2Capability))
  val ViewUIQuickSearchYMBICategoryCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIQuickSearchYMBICategoryCapability))
  val ViewUIProductSearchCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIProductSearchCapability))
  val ViewUISIMON3Capabilities: CapabilitySet = CapabilitySet(Set(ViewUISIMON3Capability))
  val ViewUIHistoricalHoldingsSICapabilities: CapabilitySet = CapabilitySet(Set(ViewUIHistoricalHoldingsSICapability))
  val ViewUIWhiteLabelingCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIWhiteLabelingCapability))
  val ViewUICustomAltsHomepageCapabilities: CapabilitySet = CapabilitySet(Set(ViewUICustomAltsHomepageCapability))
  val ViewUIBottomDisclosureCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIBottomDisclosureCapability))
  val ViewUIHideHomepageLCWidgetCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIHideHomepageLCWidgetCapability))
  val ViewUIHideIOIWidgetCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIHideIOIWidgetCapability))
  val ViewUIMarketplaceViewAsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIMarketplaceViewAsCapability))
  val ViewUIAltsPortalCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIAltsPortalCapability))
  val ViewUIIpipelineCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIIpipelineCapability))
  val ViewUIBuilderPDFCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIBuilderPDFCapability))
  val ViewUIMarketplaceRollsWidgetCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIMarketplaceRollsWidgetCapability))
  val ViewUISIMarketplaceExportExcelCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISIMarketplaceExportExcelCapability))
  val ViewUISIMarketplaceDenominationCapabilities: CapabilitySet = CapabilitySet(Set(ViewUISIMarketplaceDenominationCapability))
  val ViewUIOptimizationToolSICapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIOptimizationToolSICapability))
  val ViewUIOptimizationToolAnnuityCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIOptimizationToolAnnuityCapability))
  val ViewUIUploadAdditionalDocumentCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIUploadAdditionalDocumentCapability))
  val ViewUIPlusSubscribeAltsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIPlusSubscribeAltsCapability))
  val ViewUIAltsPerformanceCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIAltsPerformanceCapability))
  val ViewUIOverrideSPOfferingsBooksCloseTimeCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIOverrideSPOfferingsBooksCloseTimeCapability))
  val ViewUITermInMonthsCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUITermInMonthsCapability))
  val ViewUILearningCenterV2DefinedOutcomeETFsMyNetworkEducationCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUILearningCenterV2DefinedOutcomeETFsMyNetworkEducationCapability))
  val ViewUILearningCenterV3DefinedOutcomeETFsMyNetworkEducationCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUILearningCenterV3DefinedOutcomeETFsMyNetworkEducationCapability))
  val AltsPendingOfferingsCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIAltsPendingOfferingsCapability))
  val AltsStagedOfferingsCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIAltsStagedOfferingsCapability))
  val AltsClosedOfferingsCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIAltsClosedOfferingsCapability))
  val SubscribeInvestmentEntityOptionsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUISubscribeInvestmentEntityOptionsCapability))
  val ViewUIOptimizationToolSIPDFCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIOptimizationToolSIPDFCapability))
  val ViewUIOptimizationToolAnnuityPDFCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIOptimizationToolAnnuityPDFCapability))
  val ViewUIAddRfqIssuersToAuctionCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIAddRfqIssuersToAuctionCapability))
  val ViewUIGrantModeCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIGrantModeCapability))
  val ViewUISMAAccountPortalCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISMAAccountPortalCapability))
  val ViewUISMASelectAccountStrategyCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISMASelectAccountStrategyCapability))
  val ViewUIBeaconIframeCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIBeaconIframeCapability))
  val ViewUISiloedExperienceCapabilities: CapabilitySet = CapabilitySet(Set(ViewUISiloedExperienceCapability))
  val ViewUIAltsViewDetailsCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIAltsViewDetailsCapability))
  val ViewUIOptimizationGrowthOnlyProductsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOptimizationGrowthOnlyProductsCapability))
  val ViewUISpectrumSICUSIPModalScoresCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISpectrumSICUSIPModalScoresCapability))
  val ViewUIOptimizationToolPDFDisclosureRJCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOptimizationToolPDFDisclosureRJCapability))
  val ViewUIOptimizationToolPDFBrandingCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOptimizationToolPDFBrandingCapability))
  val ViewUIOptimizationToolPDFDisclosureModalClientCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOptimizationToolPDFDisclosureModalClientCapability))
  val ViewUIOptimizationToolPDFDisclosureModalCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOptimizationToolPDFDisclosureModalCapability))
  val ViewUIAnnuitiesSaveAllocationsCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIAnnuitiesSaveAllocationsCapability))
  val ViewUICoBrandingCustomRJCapabilities: CapabilitySet = CapabilitySet(Set(ViewUICoBrandingCustomRJCapability))
  val ViewUICalculatedLifecycleEventsStatusCapabilities: CapabilitySet = CapabilitySet(Set(ViewUICalculatedLifecycleEventsStatusCapability))
  val ViewUICalculatedLifecycleEventsStatusEmailCapabilities: CapabilitySet = CapabilitySet(Set(ViewUICalculatedLifecycleEventsStatusEmailCapability))
  val ViewUIDigitalAssetsPortalCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIDigitalAssetsPortalCapability))
  val ViewUIAltsSubscriptionWorkflow: CapabilitySet = CapabilitySet(Set(ViewUIIAltsSubscriptionWorkflowCapability))
  val ViewUIHideIncomeCalculatorCapabilities: CapabilitySet = CapabilitySet(Set(SimonUICapabilities.ViewUIHideIncomeCalculatorCapability))
  val ViewUIRiskTypeModalCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRiskTypeModalCapability))
  val ViewUIRiskTypeModalOverrideCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRiskTypeModalOverrideCapability))
  val ViewUIRiskTypeCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRiskTypeCapability))
  val ViewUIEnvestnetCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIEnvestnetCapability))
  val ViewUIUserImpersonationCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIUserImpersonationCapability))
  val ViewUIUserImpersonationForHOUsersCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIUserImpersonationForHOUsersCapability))
  val HideUIBacktestingComparisonCapabilities: CapabilitySet = CapabilitySet(Set(HideUIBacktestingComparisonCapability))
  val ViewUILifecycleNotificationSettingsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILifecycleNotificationSettingsCapability))
  val ViewUIEditNetworkLifecycleNotificationSettingsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIEditNetworkLifecycleNotificationSettingsCapability))
  val ViewUIEstimatedEodValuationsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIEstimatedEodValuationsCapability))
  val ViewUIAGGridOnLifecyclePortalCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIAGGridOnLifecyclePortalCapability))
  val ViewUIAGGridOnLifecyclePortalSkipMigrationsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIAGGridOnLifecyclePortalSkipMigrationsCapability))
  val ViewUIAGGridOnRfqCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIAGGridOnRfqCapability))
  val ViewUINetworkVisualsFromProfileCapabilities: CapabilitySet = CapabilitySet(Set(ViewUINetworkVisualsFromProfileCapability))
  val ViewUIMarketDataEndpointForUnderlierPricesCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIMarketDataEndpointForUnderlierPricesCapability))
  val ViewUISpectrumClientFIAPDFCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUISpectrumClientFIAPDFCapability))
  val ViewUIHideSIMONBrandingCapabilities: CapabilitySet = CapabilitySet(Set(SimonUICapabilities.ViewUIHideSIMONBrandingCapability))
  val ViewUIArchitectCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectCapability))
  val ViewUIArchitectAnalysisPageSpectrumAnalysisCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectAnalysisPageSpectrumAnalysis))
  val ViewUIArchitectScenariosChartCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectScenariosChart))
  val ViewUIArchitectGrowthChartCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectGrowthChart))
  val ViewUIArchitectCorrelationMatrixChartCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectCorrelationMatrixChart))
  val ViewUIArchitectFactorsChartCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectFactorsChart))
  val ViewUIArchitectAdvisorPDFsCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectAdvisorPDFs))
  val ViewUIArchitectClientsCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectClients))
  val ViewUIArchitectBuilderGenericAltsCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectBuilderGenericAlts))
  val ViewUIArchitectBuilderRealAltsCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectBuilderRealAlts))
  val ViewUIArchitectBuilderGenericSICapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectBuilderGenericSI))
  val ViewUIArchitectBuilderRealSICapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectBuilderRealSI))
  val ViewUIArchitectBuilderGenericTraditionalCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectBuilderGenericTraditional))
  val ViewUIArchitectBuilderGenericAltsSpectrumScoresCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectBuilderGenericAltsSpectrumScores))
  val ViewUIArchitectBuilderRealAltsSpectrumScoresCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectBuilderRealAltsSpectrumScores))
  val ViewUIArchitectBuilderGenericSISpectrumScoresCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectBuilderGenericSISpectrumScores))
  val ViewUIArchitectBuilderRealSISpectrumScoresCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectBuilderRealSISpectrumScores))
  val ViewUIArchitectBuilderGenericTraditionalSpectrumScoresCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectBuilderGenericTraditionalSpectrumScores))
  val HideUIArchitectNavBarCapabilities: CapabilitySet = CapabilitySet(Set(HideUIArchitectNavBar))
  val HideUIArchitectPDFsWatermarksCapabilities: CapabilitySet = CapabilitySet(Set(HideUIArchitectPDFsWatermarks))
  val ViewUIICapitalAltOfferingsLinkCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIICapitalAltOfferingsLinkCapability))
  val ViewUIFIDxActivityWorkflowCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIFIDxActivityWorkflowCapability))
  val HideUIAllocationSummaryCapabilities: CapabilitySet = CapabilitySet(Set(HideUIAllocationSummaryCapability))
  val ViewUIAGGridClientPagesCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIAGGridClientPages))
  val ViewUIAGGridOnSalesBookCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIAGGridOnSalesBook))
  val ViewUINewLifecycleEmailsAccountDetailsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUINewLifecycleEmailsAccountDetailsCapability))
  val ViewUIVAEnhancedExperienceCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIVAEnhancedExperience))
  val ViewUIGPPortalAppCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIGPPortalApp))
  val ViewUIAIChatAppCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIAIChatApp))
  val ViewUIAIChatExperimentalPluginsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIAIChatExperimentalPlugins))
  val ViewUIContactUsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIContactUs))
  val HideSpectrumPDFNetworkNameCapabilities: CapabilitySet = CapabilitySet(Set(HideSpectrumPDFNetworkNameCapability))
  val ViewUIArchitectAdminToolCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectAdminTool))
  val ViewUIArchitectShareCustomAssetsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectShareCustomAssets))
  val ViewUIArchitectShareCustomAssetsAllCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectShareCustomAssetsAll))
  val ViewUIArchitectSharePortfoliosCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectSharePortfolios))
  val ViewUIArchitectSharePortfoliosAllCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectSharePortfoliosAll))
  val ViewUIArchitectCreateCustomAssetsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectCreateCustomAssets))
  val ViewUIArchitectPortfolioGroupCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectPortfolioGrouping))
  val ViewUIArchitectAssetClassOptimizationsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectAssetClassOptimizations))
  val ViewUIArchitectFindSimilarAssetsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectFindSimilarAssets))
  val viewUIArchitectBuilderSleevesCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectBuilderSleeves))
  val ViewUIOnlyCommissionOnSimonCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOnlyCommissionOnSimonCapability))
  val ViewUIFixedAnnuityRatesWidgetCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIFixedAnnuityRatesWidgetCapability))
  val ViewUIFeeAnalysisWidgetCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIFeeAnalysisWidgetCapability))
  val ViewUIIncomeRiderNoWithdrawalWidgetCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIIncomeRiderNoWithdrawalWidgetCapability))
  val ViewUIBenefitsAnalysisWidgetCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIBenefitsAnalysisWidgetCapability))
  val ViewUIAnnuityExplorerWidgetCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIAnnuityExplorerWidgetCapability))
  val ViewUIAnnuityClientAgeDistributionWidgetCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIAnnuityClientAgeDistributionWidgetCapability))
  val ViewUIAnnuityLowAccountValueWidgetCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIAnnuityLowAccountValueWidgetCapability))
  val ViewUIPlatformToggleCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIPlatformToggleCapability, AdminCapability))
  val ViewUIPlatformEnhancementsPageCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIPlatformEnhancementsPageCapability))
  val ViewUIUnifiedEducationCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIUnifiedEducationCapability))
  val ViewUIAnnuityFullApprovalModalCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIAnnuityFullApprovalModalCapability))
  val ViewUIDeveloperHubCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIDeveloperHubCapability))
  val ViewUIEditDeveloperHubCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIEditClientCredentialsViaDeveloperHubCapability))
  val HideUIManagedAccountHoldingsInDashboardWidgetsCapabilities: CapabilitySet = CapabilitySet(Set(HideUIManagedAccountHoldingsInDashboardWidgetsCapability))
  val ViewUIUnifiedEducationGeneralAndRegulatoryCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIUnifiedEducationGeneralAndRegulatoryCapability))
  val ViewUIAccountIdGlobalFilterCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIAccountIdGlobalFilterCapability))
  val ViewUIIssuerDashboardCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIIssuerDashboardCapability))
  val ViewUIForYouOverrideCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIForYouOverrideCapability))
  val ViewUIOffPlatformAnnuitiesCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIOffPlatformAnnuitiesCapability))
  val ViewUISIOrdersEnhancedCapabilities: CapabilitySet = CapabilitySet(ViewUISIOrdersEnhancedCapability)
  val ViewUIMultiDocsInIssuerPortalCapabilities: CapabilitySet = CapabilitySet(ViewUIMultiDocsInIssuerPortalCapability)
  val ViewUIMultiCurrencyInLifecyclePortalCapabilities: CapabilitySet = CapabilitySet(ViewUIMultiCurrencyInLifecyclePortalCapability)
  val ViewUIBatchUnentitleCapabilities: CapabilitySet = CapabilitySet(ViewUIBatchUnentitleCapability)
  val ViewUISIUnblockedPerformanceAnalysisCapabilities: CapabilitySet = CapabilitySet(ViewUISIUnblockedPerformanceAnalysisCapability)
  val ViewUIUserManagementCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIUserManagementCapability))
  val ViewUIUserManagementCapabilitiesUnified: CapabilitySet = CapabilitySet(Set(ViewUIUserManagementUnifiedCapability))
  val ViewUIAnnuitiesLifecycleFANumberSearchCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIAnnuitiesLifecycleFANumberSearchCapability))
  val ViewUIFixedAnnuitiesRatesOutputPDFCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIFixedAnnuitiesRatesOutputPDFCapability))
  val ViewUIArchitectTermsOfServiceModalCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectTermsOfServiceModal))
  val ViewUIArchitectProRataAllocationsCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIArchitectProRataAllocations))
  val ViewUIEnvestnetSkipClientQuestionnaireCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIEnvestnetSkipClientQuestionnaire))
  val ViewUIEnvestnetArchitectIntegrationCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIEnvestnetArchitectIntegration))
  val ViewUIArchitectExportPortfolioButtonCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectExportPortfolioButton))
  val ViewUIArchitectBuilderInvestCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectBuilderInvest))
  val ViewUIArchitectDownloadPDFsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectDownloadPDFs))
  val ViewUIArchitectClientPDFsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectClientPDFs))
  val ViewUILearningCenterV3InsightsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV3InsightsCapability))
  val ViewUILearningCenterV3GlossaryCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV3GlossaryCapability))
  val ViewUIAnnuityLifecycleNotificationCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIAnnuityLifecycleNotificationCapability))
  val ViewUIAllocationBacktestingV2Capabilities: CapabilitySet = CapabilitySet(Set(ViewUIAllocationBacktestingV2Capability))
  val ViewUIRILAIndexStrategyBacktestingPDFCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIRILAIndexStrategyBacktestingPDFCapability))
  val ViewUIUnifiedHomepageCapabilities: CapabilitySet = CapabilitySet(Set(AdminCapability, ViewUIUnifiedHomepageCapability))
  val ViewUIUnifiedSIMarketplaceCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIUnifiedSIMarketplaceCapability))
  val ViewUIUnifiedAnnuitiesMarketplaceCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIUnifiedAnnuitiesMarketplaceCapability))
  val ViewUIUnifiedETFMarketplaceCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIUnifiedETFMarketplaceCapability))
  val ViewUIUnifiedSMAMarketplaceCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIUnifiedSMAMarketplaceCapability))
  val ViewUIApprovalImpersonateCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIApprovalImpersonateCapability))
  val ViewUILearningCenterV3AlternativeInvestmentsResourcesCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV3AlternativeInvestmentsResourcesCapability, AdminCapability))
  val ViewUILearningCenterV3StructuredInvestmentsResourcesCapabilities: CapabilitySet  = CapabilitySet(Set(ViewUILearningCenterV3StructuredInvestmentsResourcesCapability, AdminCapability))
  val ViewUILearningCenterV3AnnuitiesResourcesCapabilities: CapabilitySet              = CapabilitySet(Set(ViewUILearningCenterV3AnnuitiesResourcesCapability, AdminCapability))
  val ViewUILearningCenterV3DefinedOutcomeETFsResourcesCapabilities: CapabilitySet     = CapabilitySet(Set(ViewUILearningCenterV3DefinedOutcomeETFsResourcesCapability, AdminCapability))
  val ViewUILearningCenterV3GeneralAndRegulatoryResourcesCapabilities: CapabilitySet   = CapabilitySet(Set(ViewUILearningCenterV3GeneralAndRegulatoryResourcesCapability, AdminCapability))
  val ViewUILearningCenterV3CustomizationsPortalCapabilities: CapabilitySet                           = CapabilitySet(Set(ViewUILearningCenterV3CustomizationsPortalCapability, AdminCapability))
  val ViewUISIAIUploadsCapabilities: CapabilitySet                           = CapabilitySet(Set(ViewUISIAIUploadsCapability))
  val ViewUILearningCenterV3CourseFinderCapabilities: CapabilitySet = CapabilitySet(Set(ViewUILearningCenterV3CourseFinderCapability, AdminCapability))
  val ViewUISITradeTminus2Capabilities: CapabilitySet = CapabilitySet(Set(ViewUISITradeTminus2Capability, AdminCapability))
  val ViewUISIAdditionalDetailsCardCapabilities: CapabilitySet = CapabilitySet(Set(ViewUISIAdditionalDetailsCardCapability, AdminCapability))
  val ViewUIArchitectSharePortfoliosIndividualsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectSharePortfoliosIndividuals))
  val ViewUIArchitectShareCustomAssetsIndividualsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectShareCustomAssetsIndividuals))
  val ViewUIArchitectSharePortfoliosNetworksCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectSharePortfoliosNetworks))
  val ViewUIArchitectShareCustomAssetsNetworksCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectShareCustomAssetsNetworks))
  val ViewUIArchitectBuilderInvestInvestmentStatusAltsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectBuilderInvestInvestmentStatusAlts))
  val ViewUIArchitectBuilderInvestInvestmentStatusSICapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectBuilderInvestInvestmentStatusSI))
  val ViewUIArchitectBuilderReplaceHoldingsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectBuilderReplaceHoldings))
  val ViewUISIContracstOfferingsRewriteCapabilities: CapabilitySet = CapabilitySet(Set(
    ViewUISIContractsOfferingsUpdatesApiCapability,
    ViewUISIContractsOfferingsUbertableCapability,
    ViewUISIContractsOfferingsBulkApisCapability,
    ViewUISIContractsOfferingsApprovalApiCapability
  ))
  val ViewUISIPortfolioPDFCapabilities: CapabilitySet = CapabilitySet(Set(ViewUISIPortfolioPDFCapability))
  val ViewUISIPortfolioPDFClientNameCapabilities: CapabilitySet = CapabilitySet(Set(ViewUISIPortfolioPDFClientNameCapability))
  val ViewUISIPortfolioPDFCusipLevelCapabilities: CapabilitySet = CapabilitySet(Set(ViewUISIPortfolioPDFCusipLevelCapability))

  val ViewUIWholesalerExchangeCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIWholesalerExchangeCapability))
  val viewUIWholesalerExchangeAsTechUserCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIWholesalerExchangeAsTechUserCapability))
  val ViewUIArchitectHoldingsOptimizationsCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectHoldingsOptimizations))

  val ViewUIRegBIEvaluationCapabilities: CapabilitySet = CapabilitySet(Set(
    AdminCapability,
    ViewUIRegBIEvaluation
  ))

  val ViewUIArchitectExpressSurveyCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectExpressSurvey))
  val ViewUIArchitectStreamlinedSurveyCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectStreamlinedSurvey))
  val ViewUIArchitectLongFormSurveyCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectLongFormSurvey))
  val ViewUIArchitectAnalysisPageUAFCapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectAnalysisPageUAF))
  val ViewUIArchitectMFECapabilities: CapabilitySet = CapabilitySet(Set(ViewUIArchitectMFE))


  def toCapabilitySet: CapabilitySet = {
    val nonAdminCapabilities = Set(
      SPPortalCapabilities,
      ViewUISIMarketplaceRatesCapabilities,
      ViewUISIEquitiesMarketplaceCapacityCapabilities,
      ViewUISIRatesMarketplaceCapacityCapabilities,
      ViewUISIIssuerPortalSuspendCapabilities,
      ViewUISIIssuerPortalCapacityCapabilities,
      ViewUISIDollarCommissionCapabilities,
      FIAPortalCapabilities,
      SVAPortalCapabilities,
      SPManagementDashboardCapabilities,
      MultipleNetworksCapabilities,
      BroadcastInNetworkOnlyCapabilities,
      BroadcastOutsideNetworkCapabilities,
      ExportCurrentOfferingNetworkIOIsCapabilities,
      ShareOutsideNetworkCapabilities,
      UseBrokerDealerCapabilities,
      IssuerPortalCapabilities,
      WholesalerPortalCapabilities,
      LearningCenterDetailsCapabilities,
      LearningCenterCapabilities,
      LifecyclePortalCapabilities,
      RIACapabilities,
      BuilderCapabilities,
      BacktestingCapabilities,
      DetailsCapabilities,
      HoldingsDocUploadCapabilities,
      ClientHoldingsGroupByHouseholdCapabilities,
      ViewUIOnboardingToolCapabilities,
      ViewUISwitchUsersNetworkCapabilities,
      LearningCenterMyNetworkCapabilities,
      ViewLearningCenterV2Capabilities,
      ViewLearningCenterV3Capabilities,
      ViewLearningCenterV2StructuredInvestmentsCapabilities,
      ViewLearningCenterV3StructuredInvestmentsCapabilities,
      ViewLearningCenterV2AnnuitiesCapabilities,
      ViewLearningCenterV3AnnuitiesCapabilities,
      ViewLearningCenterV2DefinedOutcomeETFsCapabilities,
      ViewLearningCenterV3DefinedOutcomeETFsCapabilities,
      ViewLearningCenterV2DigitalAssetsCapabilities,
      ViewLearningCenterV3DigitalAssetsCapabilities,
      ViewLearningCenterV2AlternativeInvestmentsCapabilities,
      ViewLearningCenterV3AlternativeInvestmentsCapabilities,
      ViewLearningCenterV2ViewCustomResourceOrderCapabilities,
      ViewLearningCenterV3ViewCustomResourceOrderCapabilities,
      ViewLearningCenterV2ResourceWithoutTrainingCapabilities,
      ViewLearningCenterV3ResourceWithoutTrainingCapabilities,
      ViewLearningCenterV2DefinedOutcomeETFMPlusFundsCapabilities,
      ViewLearningCenterV3DefinedOutcomeETFMPlusFundsCapabilities,
      ViewCloseOutCapabilities,
      ViewSPOrdersCloseoutWorkflowCapabilities,
      ViewUISPOrdersAuditCapabilities,
      ViewDeveloperFeatureCapabilities,
      ViewTierCapabilities,
      ViewRfqHomeOfficeCapabilities,
      ViewUIRfqCopyToClipboardCapabilities,
      ViewUIRfqNewSubmissionLayoutCapabilities,
      ViewUIRfqIssuerNewSubmissionLayoutCapabilities,
      ViewUIRfqNewCardLayoutCapabilities,
      ViewUIRfqIssuerNewCardLayoutCapabilities,
      ViewRfqIssuerCapabilities,
      ViewRfqMultiIssuerCapabilities,
      ViewRfqFACapabilities,
      ViewRfqWholesalerCapabilities,
      ViewRfqGenericsModalCapabilities,
      ViewRfqCreateTemplateForNetworkCapabilities,
      ViewRfqQuantoCompoFieldCapabilities,
      ViewUILifecyclePortalRollsActionsCapabilities,
      ViewUIRfqStrikeDateFieldCapabilities,
      ViewUIRfqSettlementDateFieldCapabilities,
      ViewUIRfqPricingFeedbackCapabilities,
      ViewUIRfqWholesalerInfoCapabilities,
      ViewUIRfqDeclineReasonsCapabilities,
      ViewUIRfqRulesEngineResultsCapabilities,
      ViewUIRfqCrowdsourcingPortalCapabilities,
      ViewUIRfqAuditTrailCapabilities,
      ViewUIRfqIdeaGenerationCapabilities,
      ViewUIRfqCreateTemplateCapabilities,
      ViewUIRfqSubmitButtonCapabilities,
      ViewUIRfqTemplateNameInPlaceOfDisplayNameCapabilities,
      ViewUIRfqTemplateRequestToTradeCapabilities,
      ViewUIRfqTemplateVariationCapabilities,
      ViewUIRfqQuoteDetailsCapabilities,
      ViewUIRfqDocumentReviewWorkflowCapabilities,
      ViewUIRfqToContractOfferingWorkflowCapabilities,
      ViewUIRfqDealTypeSelectionCapabilities,
      ViewUIRfqAveragingPaymentDetailsCapabilities,
      ViewUIRfqJpmPbCapabilities,
      ViewUIRfqModal54WeekToggleCapabilities,
      ViewUIContractExportToExcelCapabilities,
      ViewUIRfqDenominationCapabilities,
      ViewUIRfqSortedByLevelCapabilities,
      ViewUIRfqRequestFirmQuoteCapabilities,
      ViewUIRfqCloningCapabilities,
      ViewUIRfqExternalIdInputCapabilities,
      ViewUIRfqContractUploadedStateCapabilities,
      ViewUIRfqModalAutopopulateFACapabilities,
      ViewUIRfqModalAutopopulateNetworkCapabilities,
      ViewUIRfqSolvedRangeCapabilities,
      ViewUIRfqOptInPricingFeedbackByDefaultCapabilities,
      ViewUIBuilderRfqButtonCapabilities,
      ViewUIRfqIdeaGenerationTabCapabilities,
      ViewUIRfqPendingIdeaTabCapabilities,
      ViewUIRfqActiveRfqsTabCapabilities,
      ViewUIRfqActiveRftsTabCapabilities,
      ViewExportPDFCapabilities,
      ViewUIBuilderV2Capabilities,
      ViewUILearningCenterV2TrainingUploadCapabilities,
      ViewUILearningCenterV3TrainingUploadCapabilities,
      ViewUICarrierPortalRateUploadCapabilities,
      ViewUICarrierPortalMorningStarMappingUploadCapabilities,
      ViewUIOrderPreTradeValidationCapabilities,
      ViewUIOrderCreateNewOrderAccountCapabilities,
      ViewUIOrderBulkUploadCapabilities,
      ViewUIOrderBulkUploadDownloadForLPLCapabilities,
      ViewUIOrderEntryClientSoliticationCapabilities,
      ViewUIOrderEntryNetworkCapabilities,
      ViewUIOrderEntryAccountSearchCapabilities,
      ViewUIOrderEntryAccountInputNumbersOnlyValidationCapabilities,
      ViewUIOrderEntryCommentCapabilities,
      ViewUIOrderEntryCustomerDateTimeCapabilities,
      ViewUIOrderEntryDiscretionaryCapabilities,
      ViewUIOrderEntryOnBehalfOfCapabilities,
      ViewUIOrderEntryRepCodeCapabilities,
      ViewUIOrderEntryFeesCommissionCapabilities,
      ViewUIOrderEntryAccountTypeNumberPhoneRepNameCapabilities,
      ViewUIOrderEntryAcceptedByFaNumberCapabilities,
      ViewUIOrderEntryHideDefaultAttestationCapabilities,
      ViewUIOrderEntryRockefellerCapabilities,
      ViewUIIOILocationsColumnCapabilities,
      ViewUIBroadridgeIOIDownloadCapabilities,
      ViewUIPershingIOIDownloadCapabilities,
      ViewUIBloombergMTEIOIDownloadCapabilities,
      ViewUIMTDIOIDownloadCapabilities,
      ViewUICusipIOIDownloadCapabilities,
      ViewUILearningCenterV2MyNetworkProductTrainingCapabilities,
      ViewUILearningCenterV3MyNetworkProductTrainingCapabilities,
      ViewUILearningCenterV2AnnuitiesMyNetworkEducationCapabilities,
      ViewUILearningCenterV3AnnuitiesMyNetworkEducationCapabilities,
      ViewUILearningCenterV2AlternativesMyNetworkEducationCapabilities,
      ViewUILearningCenterV3AlternativesMyNetworkEducationCapabilities,
      ViewUIPendingOfferingsCapabilities,
      ViewUIAnalyticsAsOfDateCapabilities,
      ViewUIHoldingsAsOfDatesCapabilities,
      ViewUIRetriggerDTCCCapabilities,
      ViewUIContractEditCapabilities,
      ViewUIDistributorSymbolCapabilities,
      ViewUIProductNomenclatureLongNameCapabilities,
      ViewUIRejectedIOIsCapabilities,
      ViewNewSILifecycleDashboardCapabilities,
      ViewFANumberSelectorDashboardCapabilities,
      ViewExportToTwdXlsCapabilities,
      ViewUITMCReportCapabilities,
      ViewUIOlarkCapabilities,
      ViewUIQuoteTypeCapabilities,
      ViewUICustomAttestationViaNetworkCapabilities,
      ViewUIDefinedOutcomeETFCapabilities,
      ViewUIDefinedOutcomeETFViewAsCapabilities,
      ViewUIDefinedOutcomeETFMPlusFundsCapabilities,
      ViewUISpectrumLC2SPCapabilities,
      ViewUIContractRangesCapabilities,
      ViewUIStagedOfferingsCapabilities,
      ViewUIRejectedOfferingsCapabilities,
      ViewUIFAPortalCapabilities,
      ViewUIVAPortalCapabilities,
      ViewUIFAPortalYieldToSurrenderCapabilities,
      ViewUIMyProductsDashboardCapabilities,
      ViewUILifecycleEventsDashboardCapabilities,
      ViewUINotificationsNewsfeedDashboardCapabilities,
      ViewUIOfferingsReportCapabilities,
      ViewUIOfferingDocViaModalCapabilities,
      ViewUILifecycleNotificationSettingsCapabilities,
      ViewUIEditNetworkLifecycleNotificationSettingsCapabilities,
      ViewUILifecyclePortalSPCapabilities,
      ViewUILifecyclePortalSPShareViaLinkCapabilities,
      ViewUILifecyclePortalSPEditNetworkDefaultCapabilities,
      ViewUILifecyclePortalSPWidgetContractOfferingsCapabilities,
      ViewUILifecyclePortalSPBarrierAnalysisWidgetCapabilities,
      ViewUILifecyclePortalSPCapAnalysisWidgetCapabilities,
      ViewUILifecyclePortalSPSectorAllocationWidgetCapabilities,
      ViewUILifecyclePortalSPUnderlierPerformanceWidgetCapabilities,
      ViewUILifecyclePortalPartialSearchCapabilities,
      ViewUILifecyclePortalPartitionedQueryCapabilities,
      ViewUILifecyclePortalSIClientsWidgetCapabilities,
      ViewUISearchBarCapabilities,
      ViewUISIEquitiesLifecycleDTCCPaymentCapabilities,
      ViewUISIEquitiesBufferedLeveredNoteLifecycleDTCCPaymentCapabilities,
      ViewUIViewAsLifecycleSPCapabilities,
      ViewUIHeatmapCapabilities,
      ViewUILifecyclePortalAnnuitiesCapabilities,
      ViewUILifecyclePortalInsuranceCapabilities,
      ViewUIProductNomenclatureCapabilities,
      ViewUISpectrumMarketplaceSICapabilities,
      ViewUISpectrumSICUSIPModalScoresCapabilities,
      ViewUIRefinitivDeepLinksCapabilities,
      ViewUIFABrandingCapabilities,
      ViewUIBetaPlusMarketplaceCapabilities,
      ViewUIPerfPDFClientNameCapabilities,
      ViewUIPerfPDFViaServiceCapabilities,
      ViewUIPerfPDFFaGenerateInvestorReportMessageCapabilities,
      ViewUIImpersonateAnnuityMarketplaceCapabilities,
      ViewUISpectrumBuilderCapabilities,
      ViewUITradewebOrderTokenCapabilities,
      ViewUITradewebEquitiesManageApprovalCapabilities,
      ViewUITradewebRatesManageApprovalCapabilities,
      ViewUIBuilderDistributorNomenclatureCapabilities,
      HideUILifecyclePortalExportToExcelCapabilities,
      ViewUIEmbeddedExperienceCapabilities,
      ViewUISpectrumLC2AnnuitiesCapabilities,
      ViewUIUnderlierMarketCapabilities,
      ViewUIStructuredETFIssuerPortalCapabilities,
      ViewUIStructuredETFPendingOfferingsCapabilities,
      ViewUIAppcuesDropdownCapabilities,
      ViewUISpectrumMarketplaceAnnuitiesCapabilities,
      ViewUIBeaconCapabilities,
      ViewUISpectrumPDFAnnCapabilities,
      ViewUISpectrumSICUSIPModalCapabilities,
      ViewUILifecyclePortalSpectrumCapabilities,
      ViewUISpectrumPDFSICapabilities,
      ViewUIEnhancedAnnuityOrderCapabilities,
      ViewUILevelSystemCapabilities,
      ViewUIDefinedOutcomeETFUpsideShieldCapabilities,
      ViewUIDefinedOutcomeETFPerformanceAnalysisCapabilities,
      ViewUIDefinedOutcomeETFComparisonCalculatorCapabilities,
      ViewUIIncomeBacktestingToolCapabilities,
      ViewUIFIDxCarrierPortalCapabilities,
      ViewUIQuickSearch2Capabilities,
      ViewUIQuickSearchYMBICategoryCapabilities,
      ViewUIProductSearchCapabilities,
      ViewUISIMON3Capabilities,
      ViewUIHistoricalHoldingsSICapabilities,
      ViewUIWhiteLabelingCapabilities,
      ViewUICustomAltsHomepageCapabilities,
      ViewUIBottomDisclosureCapabilities,
      ViewUIHideHomepageLCWidgetCapabilities,
      ViewUIHideIOIWidgetCapabilities,
      ViewUIMarketplaceViewAsCapabilities,
      ViewUIAltsPortalCapabilities,
      ViewUIIpipelineCapabilities,
      ViewUIBuilderPDFCapabilities,
      ViewUIMarketplaceRollsWidgetCapabilities,
      ViewUISIMarketplaceExportExcelCapabilities,
      ViewUISIMarketplaceDenominationCapabilities,
      ViewUIOptimizationToolSICapabilities,
      ViewUIOptimizationToolAnnuityCapabilities,
      ViewUIUploadAdditionalDocumentCapabilities,
      ViewUIPlusSubscribeAltsCapabilities,
      ViewUIAltsPerformanceCapabilities,
      ViewUIOverrideSPOfferingsBooksCloseTimeCapabilities,
      ViewUITermInMonthsCapabilities,
      ViewUILearningCenterV2DefinedOutcomeETFsMyNetworkEducationCapabilities,
      ViewUILearningCenterV3DefinedOutcomeETFsMyNetworkEducationCapabilities,
      AltsPendingOfferingsCapabilities,
      AltsStagedOfferingsCapabilities,
      AltsClosedOfferingsCapabilities,
      SubscribeInvestmentEntityOptionsCapabilities,
      ViewUIOptimizationToolSIPDFCapabilities,
      ViewUIOptimizationToolAnnuityPDFCapabilities,
      ViewUIAddRfqIssuersToAuctionCapabilities,
      ViewUIGrantModeCapabilities,
      ViewUISMAAccountPortalCapabilities,
      ViewUISMASelectAccountStrategyCapabilities,
      ViewUIBeaconIframeCapabilities,
      ViewUISiloedExperienceCapabilities,
      ViewUIAltsViewDetailsCapabilities,
      ViewUIOptimizationGrowthOnlyProductsCapabilities,
      ViewUIOptimizationToolPDFDisclosureRJCapabilities,
      ViewUIOptimizationToolPDFBrandingCapabilities,
      ViewUIOptimizationToolPDFDisclosureModalClientCapabilities,
      ViewUIOptimizationToolPDFDisclosureModalCapabilities,
      ViewUIAnnuitiesSaveAllocationsCapabilities,
      ViewUICoBrandingCustomRJCapabilities,
      ViewUICalculatedLifecycleEventsStatusCapabilities,
      ViewUICalculatedLifecycleEventsStatusEmailCapabilities,
      ViewUIDigitalAssetsPortalCapabilities,
      ViewUIAltsSubscriptionWorkflow,
      ViewUIHideIncomeCalculatorCapabilities,
      ViewUIRiskTypeModalCapabilities,
      ViewUIRiskTypeModalOverrideCapabilities,
      ViewUIRiskTypeCapabilities,
      ViewUIEnvestnetCapabilities,
      ViewUIUserImpersonationCapabilities,
      ViewUIUserImpersonationForHOUsersCapabilities,
      HideUIBacktestingComparisonCapabilities,
      ViewUISpectrumLC2FixedIndexedAnnuitiesCapabilities,
      ViewUIOptimizationToolFixedIndexedAnnuitiesCapabilities,
      ViewUISpectrumAdvisorFIAPDFCapabilities,
      ViewUIOptimizationToolAdvisorFIAPDFCapabilities,
      ViewUIEstimatedEodValuationsCapabilities,
      ViewUIAGGridOnLifecyclePortalCapabilities,
      ViewUIAGGridOnLifecyclePortalSkipMigrationsCapabilities,
      ViewUIAGGridOnRfqCapabilities,
      ViewUINetworkVisualsFromProfileCapabilities,
      ViewUIMarketDataEndpointForUnderlierPricesCapabilities,
      ViewUIOptimizationToolClientFIAPDFCapabilities,
      ViewUISpectrumClientFIAPDFCapabilities,
      ViewUIHideSIMONBrandingCapabilities,
      ViewUIArchitectCapabilities,
      ViewUIArchitectAnalysisPageSpectrumAnalysisCapabilities,
      ViewUIArchitectScenariosChartCapabilities,
      ViewUIArchitectGrowthChartCapabilities,
      ViewUIArchitectCorrelationMatrixChartCapabilities,
      ViewUIArchitectFactorsChartCapabilities,
      ViewUIArchitectAdvisorPDFsCapabilities,
      ViewUIArchitectClientsCapabilities,
      ViewUIArchitectBuilderGenericAltsCapabilities,
      ViewUIArchitectBuilderRealAltsCapabilities,
      ViewUIArchitectBuilderGenericSICapabilities,
      ViewUIArchitectBuilderRealSICapabilities,
      ViewUIArchitectBuilderGenericTraditionalCapabilities,
      ViewUIArchitectBuilderGenericAltsSpectrumScoresCapabilities,
      ViewUIArchitectBuilderRealAltsSpectrumScoresCapabilities,
      ViewUIArchitectBuilderGenericSISpectrumScoresCapabilities,
      ViewUIArchitectBuilderRealSISpectrumScoresCapabilities,
      ViewUIArchitectBuilderGenericTraditionalSpectrumScoresCapabilities,
      HideUIArchitectNavBarCapabilities,
      HideUIArchitectPDFsWatermarksCapabilities,
      ViewUIICapitalAltOfferingsLinkCapabilities,
      ViewUIFIDxActivityWorkflowCapabilities,
      HideUIAllocationSummaryCapabilities,
      ViewUIAGGridClientPagesCapabilities,
      ViewUIAGGridOnSalesBookCapabilities,
      ViewUINewLifecycleEmailsAccountDetailsCapabilities,
      ViewUIVAEnhancedExperienceCapabilities,
      ViewUIGPPortalAppCapabilities,
      ViewUIAIChatAppCapabilities,
      ViewUIAIChatExperimentalPluginsCapabilities,
      ViewUIContactUsCapabilities,
      HideSpectrumPDFNetworkNameCapabilities,
      ViewUIArchitectAdminToolCapabilities,
      ViewUIOnlyCommissionOnSimonCapabilities,
      ViewUIFixedAnnuityRatesWidgetCapabilities,
      ViewUIFeeAnalysisWidgetCapabilities,
      ViewUIIncomeRiderNoWithdrawalWidgetCapabilities,
      ViewUIBenefitsAnalysisWidgetCapabilities,
      ViewUIAnnuityExplorerWidgetCapabilities,
      ViewUIAnnuityClientAgeDistributionWidgetCapabilities,
      ViewUIAnnuityLowAccountValueWidgetCapabilities,
      ViewUIPlatformToggleCapabilities,
      ViewUIPlatformEnhancementsPageCapabilities,
      ViewUIUnifiedEducationCapabilities,
      ViewUIAnnuityFullApprovalModalCapabilities,
      ViewUIDeveloperHubCapabilities,
      ViewUIEditDeveloperHubCapabilities,
      HideUIManagedAccountHoldingsInDashboardWidgetsCapabilities,
      ViewUIUnifiedEducationGeneralAndRegulatoryCapabilities,
      ViewUIAccountIdGlobalFilterCapabilities,
      ViewUIIssuerDashboardCapabilities,
      ViewUIForYouOverrideCapabilities,
      ViewUIArchitectShareCustomAssetsCapabilities,
      ViewUIArchitectShareCustomAssetsAllCapabilities,
      ViewUIArchitectSharePortfoliosCapabilities,
      ViewUIArchitectSharePortfoliosAllCapabilities,
      ViewUIArchitectCreateCustomAssetsCapabilities,
      ViewUIArchitectPortfolioGroupCapabilities,
      ViewUIArchitectAssetClassOptimizationsCapabilities,
      ViewUIArchitectFindSimilarAssetsCapabilities,
      viewUIArchitectBuilderSleevesCapabilities,
      ViewUIArchitectBuilderInvestCapabilities,
      ViewUIArchitectDownloadPDFsCapabilities,
      ViewUIArchitectClientPDFsCapabilities,
      ViewUIOffPlatformAnnuitiesCapabilities,
      ViewUISIOrdersEnhancedCapabilities,
      ViewUIMultiDocsInIssuerPortalCapabilities,
      ViewUIMultiCurrencyInLifecyclePortalCapabilities,
      ViewUIBatchUnentitleCapabilities,
      ViewUISIUnblockedPerformanceAnalysisCapabilities,
      ViewUIUserManagementCapabilities,
      ViewUIUserManagementCapabilitiesUnified,
      ViewUIAnnuitiesLifecycleFANumberSearchCapabilities,
      ViewUIFixedAnnuitiesRatesOutputPDFCapabilities,
      ViewUIArchitectTermsOfServiceModalCapabilities,
      ViewUIArchitectProRataAllocationsCapabilities,
      ViewUIEnvestnetSkipClientQuestionnaireCapabilities,
      ViewUIEnvestnetArchitectIntegrationCapabilities,
      ViewUIArchitectExportPortfolioButtonCapabilities,
      ViewUILearningCenterV3InsightsCapabilities,
      ViewUILearningCenterV3GlossaryCapabilities,
      ViewUIAnnuityLifecycleNotificationCapabilities,
      ViewUIAllocationBacktestingV2Capabilities,
      ViewUIRILAIndexStrategyBacktestingPDFCapabilities,
      ViewUIUnifiedHomepageCapabilities,
      ViewUIUnifiedSIMarketplaceCapabilities,
      ViewUIUnifiedAnnuitiesMarketplaceCapabilities,
      ViewUIUnifiedETFMarketplaceCapabilities,
      ViewUIUnifiedSMAMarketplaceCapabilities,
      ViewUIApprovalImpersonateCapabilities,
      ViewUILearningCenterV3AlternativeInvestmentsResourcesCapabilities,
      ViewUILearningCenterV3StructuredInvestmentsResourcesCapabilities,
      ViewUILearningCenterV3AnnuitiesResourcesCapabilities,
      ViewUILearningCenterV3DefinedOutcomeETFsResourcesCapabilities,
      ViewUILearningCenterV3GeneralAndRegulatoryResourcesCapabilities,
      ViewUILearningCenterV3CustomizationsPortalCapabilities,
      ViewUISIAIUploadsCapabilities,
      ViewUILearningCenterV3CourseFinderCapabilities,
      ViewUISITradeTminus2Capabilities,
      ViewUISIAdditionalDetailsCardCapabilities,
      ViewUIArchitectSharePortfoliosIndividualsCapabilities,
      ViewUIArchitectShareCustomAssetsIndividualsCapabilities,
      ViewUIArchitectSharePortfoliosNetworksCapabilities,
      ViewUIArchitectShareCustomAssetsNetworksCapabilities,
      ViewUIArchitectBuilderInvestInvestmentStatusAltsCapabilities,
      ViewUIArchitectBuilderInvestInvestmentStatusSICapabilities,
      ViewUIArchitectBuilderReplaceHoldingsCapabilities,
      ViewUISIContracstOfferingsRewriteCapabilities,
      ViewUISIPortfolioPDFCapabilities,
      ViewUISIPortfolioPDFClientNameCapabilities,
      ViewUISIPortfolioPDFCusipLevelCapabilities,
      ViewUIWholesalerExchangeCapabilities,
      viewUIWholesalerExchangeAsTechUserCapabilities,
      ViewUIArchitectHoldingsOptimizationsCapabilities,
      ViewUIRegBIEvaluationCapabilities,
      ViewUIArchitectExpressSurveyCapabilities,
      ViewUIArchitectStreamlinedSurveyCapabilities,
      ViewUIArchitectLongFormSurveyCapabilities,
      ViewUIArchitectAnalysisPageUAFCapabilities,
      ViewUIArchitectMFECapabilities,
    ).flatMap(_.capabilities)
    CapabilitySet(nonAdminCapabilities + AdminCapability + ReadOnlyAdminCapability)
  }

  def toAssetClassUserCapabilityMap: AssetClassToUICapabilities = {

    val uiCapabilities = this.toCapabilitySet
    AssetClassToUICapabilities(
      alternativeInvestments = uiCapabilities.getCapabilitiesByAssetClass(AlternativesAssetClass).flatMap(_.linkedToCapability),
      structuredInvestments = uiCapabilities.getCapabilitiesByAssetClass(StructuredInvestmentsAssetClass).flatMap(_.linkedToCapability),
      insurance = uiCapabilities.getCapabilitiesByAssetClass(InsuranceAssetClass).flatMap(_.linkedToCapability),
      platform = uiCapabilities.getCapabilitiesByAssetClass(Platform).flatMap(_.linkedToCapability),
      annuities = uiCapabilities.getCapabilitiesByAssetClass(AnnuitiesAssetClass).flatMap(_.linkedToCapability),
      digitalAssets = uiCapabilities.getCapabilitiesByAssetClass(DigitalAssetClass).flatMap(_.linkedToCapability),
      smas = uiCapabilities.getCapabilitiesByAssetClass(SMAsAssetClass).flatMap(_.linkedToCapability),
      definedOutcomeETFs = uiCapabilities.getCapabilitiesByAssetClass(DefinedOutcomeETFsAssetClass).flatMap(_.linkedToCapability),
      spectrum = uiCapabilities.getCapabilitiesByAssetClass(Spectrum).flatMap(_.linkedToCapability),
      architect = uiCapabilities.getCapabilitiesByAssetClass(Architect).flatMap(_.linkedToCapability),
      aiChat = uiCapabilities.getCapabilitiesByAssetClass(AIChat).flatMap(_.linkedToCapability),
    )
  }
  override def toDetailedCapabilitySet: Set[Capability] = this.toCapabilitySet.capabilities
}


case class AssetClassToUICapabilities(
    alternativeInvestments: Set[String],
    structuredInvestments: Set[String],
    insurance: Set[String],
    definedOutcomeETFs: Set[String],
    annuities: Set[String],
    digitalAssets: Set[String],
    smas: Set[String],
    platform: Set[String],
    spectrum: Set[String],
    architect: Set[String],
    aiChat: Set[String],
)
