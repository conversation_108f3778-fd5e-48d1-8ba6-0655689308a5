package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.{NetworkCategory, PurviewedDomain, UserACL}
import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator._
import com.simonmarkets.entitlements._
import simon.Id.NetworkId

import scala.collection.mutable


object OrdersCapabilities extends Capabilities with HasDetailedViewCapabilities with HasDetailedEditCapabilities {
  override val DomainName = "Orders"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.StructuredInvestmentsAssetClass, AssetClasses.AnnuitiesAssetClass))
  val SIAssetClass: Option[Seq[String]] = Option(Seq(AssetClasses.StructuredInvestmentsAssetClass))

  val ViewOrderViaNetworkCapability: Capability = Capability("viewOrderViaNetwork", "", assetClasses)
  val ViewOrderViaLocationCapability: Capability = Capability("viewOrderViaLocation", "", assetClasses)
  val ViewOrderViaPurviewCapability: Capability = Capability("viewOrderViaPurview", "", assetClasses)
  val ViewOrderViaPurviewNetworkCustodianCapability: Capability = Capability("viewOrderViaPurviewNetworkCustodian", "Purviewed Domain needs to be SIEquitiesOrders and purview type must be Custodian for the capability to take effect", SIAssetClass)
  val ViewOrderViaOwnerCapability: Capability = Capability("viewOrderViaOwner", "", assetClasses)
  val ViewOrderViaFaNumberCapability: Capability = Capability("viewOrderViaFaNumber", "", assetClasses)
  val ViewOrderViaSystemUserCapability: Capability = Capability("viewOrderViaSystemUser", "Deprecated. This capability will be removed in the future.", assetClasses)
  val ViewOrderViaUserCreatedCapability: Capability = Capability("viewOrderViaUserCreated", "", assetClasses)
  val ViewOrderViaNetworkTypeCapability: Capability = Capability("viewOrderViaNetworkTypeCapability", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)

  val ViewOrderViaNetwork: String = ViewOrderViaNetworkCapability.name
  val ViewOrderViaLocation: String = ViewOrderViaLocationCapability.name
  val ViewOrderViaPurview: String = ViewOrderViaPurviewCapability.name
  val ViewOrderViaOwner: String = ViewOrderViaOwnerCapability.name
  val ViewOrderViaFaNumber: String = ViewOrderViaFaNumberCapability.name
  val ViewOrderViaSystemUser: String = ViewOrderViaSystemUserCapability.name
  val ViewOrderViaUserCreated: String = ViewOrderViaUserCreatedCapability.name
  val ViewOrderViaNetworkType: String = ViewOrderViaNetworkTypeCapability.name

  val EditOrderViaOwnerCapability: Capability = Capability("editOrderViaOwner", "", assetClasses)
  val EditOrderViaNetworkCapability: Capability = Capability("editOrderViaNetwork", "", assetClasses)
  val EditOrderViaLocationCapability: Capability = Capability("editOrderViaLocation", "Deprecated. This capability will be removed in the future.", assetClasses)
  val EditOrderViaPurviewCapability: Capability = Capability("editOrderViaPurview", "", assetClasses)
  val EditOrderOnBehalfOfViaSystemUserCapability: Capability = Capability("editOrderOnBehalfOfViaSystemUser", "Deprecated. This capability will be removed in the future.", assetClasses)
  val EditOrderViaPurviewNetworkCustodianCapability: Capability = Capability("editOrderViaPurviewNetworkCustodian", "Purviewed Domain needs to be SIEquitiesOrders and purview type must be Custodian for the capability to take effect", SIAssetClass)
  val EditOrderViaUserCreatedCapability: Capability = Capability("editOrderViaUserCreated", "", assetClasses)
  val EditOrderViaNetworkTypeCapability: Capability = Capability("editOrderViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)

  val EditOrderViaOwner: String = EditOrderViaOwnerCapability.name
  val EditOrderViaNetwork: String = EditOrderViaNetworkCapability.name
  val EditOrderViaLocation: String = EditOrderViaLocationCapability.name
  val EditOrderViaPurview: String = EditOrderViaPurviewCapability.name
  val EditOrderOnBehalfOfViaSystemUser: String = EditOrderOnBehalfOfViaSystemUserCapability.name
  val EditOrderViaUserCreated: String = EditOrderViaUserCreatedCapability.name
  val EditOrderViaNetworkType: String = EditOrderViaNetworkTypeCapability.name

  val CancelOrderViaNetworkCapability: Capability = Capability("cancelOrderViaNetwork", "", assetClasses)
  val CancelOrderViaLocationCapability: Capability = Capability("cancelOrderViaLocation", "Deprecated. This capability will be removed in the future.", assetClasses)
  val CancelOrderViaOwnerCapability: Capability = Capability("cancelOrderViaOwner", "", assetClasses)
  val CancelOrderViaPurviewCapability: Capability = Capability("cancelOrderViaPurview", "", assetClasses)
  val CancelOrderViaPurviewNetworkCustodianCapability: Capability = Capability("cancelOrderViaPurviewNetworkCustodian", "Purviewed Domain needs to be SIEquitiesOrders and purview type must be Custodian for the capability to take effect", SIAssetClass)
  val CancelOrderOnBehalfOfViaSystemUserCapability: Capability = Capability("cancelOrderOnBehalfOfViaSystemUser", "Deprecated. This capability will be removed in the future.", assetClasses)
  val CancelOrderViaUserCreatedCapability: Capability = Capability("cancelOrderViaUserCreated", "", assetClasses)
  val CancelOrderViaNetworkTypeCapability: Capability = Capability("cancelOrderViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)

  val CancelOrderViaNetwork: String = CancelOrderViaNetworkCapability.name
  val CancelOrderViaLocation: String = CancelOrderViaLocationCapability.name
  val CancelOrderViaOwner: String = CancelOrderViaOwnerCapability.name
  val CancelOrderViaPurview: String = CancelOrderViaPurviewCapability.name
  val CancelOrderOnBehalfOfViaSystemUser: String = CancelOrderOnBehalfOfViaSystemUserCapability.name
  val CancelOrderViaUserCreated: String = CancelOrderViaUserCreatedCapability.name
  val CancelOrderViaNetworkType: String = CancelOrderViaNetworkTypeCapability.name

  val ApproveOrderViaApproversCapability: Capability = Capability("approveOrderViaApprovers", "", assetClasses)
  val ApproveOrderOnBehalfOfViaSystemUserCapability: Capability = Capability("approveOrderOnBehalfOfViaSystemUser", "Deprecated. This capability will be removed in the future.", assetClasses)

  val ApproveOrderViaApprovers: String = ApproveOrderViaApproversCapability.name
  val ApproveOrderOnBehalfOfViaSystemUser: String = ApproveOrderOnBehalfOfViaSystemUserCapability.name

  val SubmitOrderOnBehalfOfViaNetworkTypeCapability: Capability = Capability("submitOrderOnBehalfOfViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val SubmitOrderOnBehalfOfViaNetworkCapability: Capability = Capability("submitOrderOnBehalfOfViaNetwork", "", assetClasses)
  val SubmitOrderOnBehalfOfViaPurviewCapability: Capability = Capability("submitOrderOnBehalfOfViaPurview", "", assetClasses)
  val SubmitOrderOnBehalfOfViaPurviewNetworkCustodianCapability: Capability = Capability("submitOrderOnBehalfOfViaPurviewNetworkCustodian", "Purviewed Domain needs to be SIEquitiesOrders and purview type must be Custodian for the capability to take effect", SIAssetClass)

  val SubmitOrderOnBehalfOfViaNetworkType: String = SubmitOrderOnBehalfOfViaNetworkTypeCapability.name
  val SubmitOrderOnBehalfOfViaNetwork: String = SubmitOrderOnBehalfOfViaNetworkCapability.name
  val SubmitOrderOnBehalfOfViaPurview: String = SubmitOrderOnBehalfOfViaPurviewCapability.name


  /** Feature Flag Capabilities */
  val HidePershingTabInExcelCapability: Capability = Capability("hidePershingTabInExcel", "", assetClasses)
  val IncludeLocationsColumnInExcelCapability: Capability = Capability("includeLocationsColumnInExcel", "", assetClasses)
  val IncludeAdditionalAccountInfoInExcelCapability: Capability = Capability("includeAdditionalAccountInfoInExcel", "", assetClasses)
  val IncludeStructuringFeeColumnCapability: Capability = Capability("includeStructuringFeeColumnInExcel", "Enable user to see structuring fee for orders in UI and in Excel reports", assetClasses)

  val HidePershingTabInExcel: String = HidePershingTabInExcelCapability.name
  val IncludeLocationsColumnInExcel: String = IncludeLocationsColumnInExcelCapability.name
  val IncludeAdditionalAccountInfoInExcel: String = IncludeAdditionalAccountInfoInExcelCapability.name

  val BlockSingleSIFeeOrderProductCapability: Capability = Capability("blockSingleSIFeeOrderProduct", "This capability is for disabling buys on fee based products that are required to go through external flows", assetClasses)
  val BlockSingleSIFeeOrderProduct: String = BlockSingleSIFeeOrderProductCapability.name
  val ActionOrderAsAdminViaPurviewCapability: Capability = Capability("actionOrderAsAdminViaPurview", "", assetClasses)
  val ActionOrderAsAdminViaPurview: String = ActionOrderAsAdminViaPurviewCapability.name
  val CancelOrderAfterBooksCloseCapability: Capability = Capability("cancelOrderAfterBooksClose", "Allows a user to cancel an order after books close, if they could already cancel the order otherwise", assetClasses)
  val EditOrderAfterBooksCloseCapability: Capability = Capability("editOrderAfterBooksClose", "Allows a user to edit an order after books close, if they could already edit the order otherwise", assetClasses)


  val OverrideUserNetworkIdCapability: Capability = Capability("overrideUserNetworkId", "", assetClasses)
  val OverrideUserNetworkId: String = OverrideUserNetworkIdCapability.name

  val EnableOrderCustodianCapability: Capability = Capability("enableOrderCustodian", "Enable user for all order custodian related features.", assetClasses, Some("canSeeOrderCustodian"))

  val ViewOrdersAuditTrailViaPurviewCapability: Capability = Capability("viewOrdersAuditTrailViaPurview", "Allow user to view audit trail for order under purview network", assetClasses)
  val ViewOrdersAuditTrailViaPurviewNetworkCustodianCapability: Capability = Capability("viewOrdersAuditTrailViaPurviewNetworkCustodian", "Allow user to view audit trail for order under purview network. Purviewed Domain needs to be SIEquitiesOrders and purview type must be Custodian for the capability to take effect", SIAssetClass)

  /** Rates */
  val ViewRatesOrderViaNetworkTypeCapability: Capability = Capability("viewRatesOrderViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val ViewRatesOrderViaNetworkCapability: Capability = Capability("viewRatesOrderViaNetwork", "", assetClasses)
  val ViewRatesOrderViaLocationCapability: Capability = Capability("viewRatesOrderViaLocation", "", assetClasses)
  val ViewRatesOrderViaPurviewCapability: Capability = Capability("viewRatesOrderViaPurview", "", assetClasses)
  val ViewRatesOrderViaPurviewNetworkCustodianCapability: Capability = Capability("viewRatesOrderViaPurviewNetworkCustodian", "Purviewed Domain needs to be SIRatesOrders and purview type must be Custodian for the capability to take effect", SIAssetClass)
  val ViewRatesOrderViaOwnerCapability: Capability = Capability("viewRatesOrderViaOwner", "", assetClasses)
  val ViewRatesOrderViaFaNumberCapability: Capability = Capability("viewRatesOrderViaFaNumber", "", assetClasses)
  val ViewRatesOrderViaUserCreatedCapability: Capability = Capability("viewRatesOrderViaUserCreated", "", assetClasses)
  val ViewRatesOrderViaNetworkType: String = ViewRatesOrderViaNetworkTypeCapability.name
  val ViewRatesOrderViaNetwork: String = ViewRatesOrderViaNetworkCapability.name
  val ViewRatesOrderViaLocation: String = ViewRatesOrderViaLocationCapability.name
  val ViewRatesOrderViaPurview: String = ViewRatesOrderViaPurviewCapability.name
  val ViewRatesOrderViaOwner: String = ViewRatesOrderViaOwnerCapability.name
  val ViewRatesOrderViaFaNumber: String = ViewRatesOrderViaFaNumberCapability.name
  val ViewRatesOrderViaUserCreated: String = ViewRatesOrderViaUserCreatedCapability.name

  val EditRatesOrderViaNetworkTypeCapability: Capability = Capability("editRatesOrderViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val EditRatesOrderViaOwnerCapability: Capability = Capability("editRatesOrderViaOwner", "", assetClasses)
  val EditRatesOrderViaNetworkCapability: Capability = Capability("editRatesOrderViaNetwork", "", assetClasses)
  val EditRatesOrderViaPurviewCapability: Capability = Capability("editRatesOrderViaPurview", "", assetClasses)
  val EditRatesOrderViaPurviewNetworkCustodianCapability: Capability = Capability("editRatesOrderViaPurviewNetworkCustodian", "Purviewed Domain needs to be SIRatesOrders and purview type must be Custodian for the capability to take effect", SIAssetClass)
  val EditRatesOrderViaNetworkType: String = EditRatesOrderViaNetworkTypeCapability.name
  val EditRatesOrderViaOwner: String = EditRatesOrderViaOwnerCapability.name
  val EditRatesOrderViaNetwork: String = EditRatesOrderViaNetworkCapability.name
  val EditRatesOrderViaPurview: String = EditRatesOrderViaPurviewCapability.name

  val CancelRatesOrderViaNetworkTypeCapability: Capability = Capability("cancelRatesOrderViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val CancelRatesOrderViaNetworkCapability: Capability = Capability("cancelRatesOrderViaNetwork", "", assetClasses)
  val CancelRatesOrderViaOwnerCapability: Capability = Capability("cancelRatesOrderViaOwner", "", assetClasses)
  val CancelRatesOrderViaPurviewCapability: Capability = Capability("cancelRatesOrderViaPurview", "", assetClasses)
  val CancelRatesOrderViaPurviewNetworkCustodianCapability: Capability = Capability("cancelRatesOrderViaPurviewNetworkCustodian", "Purviewed Domain needs to be SIRatesOrders and purview type must be Custodian for the capability to take effect", SIAssetClass)
  val CancelRatesOrderViaNetworkType: String = CancelRatesOrderViaNetworkTypeCapability.name
  val CancelRatesOrderViaNetwork: String = CancelRatesOrderViaNetworkCapability.name
  val CancelRatesOrderViaOwner: String = CancelRatesOrderViaOwnerCapability.name
  val CancelRatesOrderViaPurview: String = CancelRatesOrderViaPurviewCapability.name

  val ApproveRatesOrderViaApproversCapability: Capability = Capability("approveRatesOrderViaApprovers", "", assetClasses)
  val ApproveRatesOrderViaApprovers: String = ApproveRatesOrderViaApproversCapability.name
  val ApproveRatesOrderOnBehalfOfViaSystemUserCapability: Capability = Capability("approveRatesOrderOnBehalfOfViaSystemUser", "This capability will be removed in the future. tbd because of TWD interactions", assetClasses)

  val SubmitRatesOrderOnBehalfOfViaNetworkTypeCapability: Capability = Capability("submitRatesOrderOnBehalfOfViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val SubmitRatesOrderOnBehalfOfViaNetworkCapability: Capability = Capability("submitRatesOrderOnBehalfOfViaNetwork", "", assetClasses)
  val SubmitRatesOrderOnBehalfOfViaPurviewCapability: Capability = Capability("submitRatesOrderOnBehalfOfViaPurview", "", assetClasses)
  val SubmitRatesOrderOnBehalfOfViaPurviewNetworkCustodianCapability: Capability = Capability("submitRatesOrderOnBehalfOfViaPurviewNetworkCustodian", "Purviewed Domain needs to be SIRatesOrders and purview type must be Custodian for the capability to take effect", SIAssetClass)
  val SubmitRatesOrderOnBehalfOfViaNetworkType: String = SubmitRatesOrderOnBehalfOfViaNetworkTypeCapability.name
  val SubmitRatesOrderOnBehalfOfViaNetwork: String = SubmitRatesOrderOnBehalfOfViaNetworkCapability.name
  val SubmitRatesOrderOnBehalfOfViaPurview: String = SubmitRatesOrderOnBehalfOfViaPurviewCapability.name


  /** Capabilities Sets */
  val CancelCapabilities = Set(
    Admin,
    CancelOrderViaNetworkType,
    ActionOrderAsAdminViaPurview,
    CancelOrderViaNetwork,
    CancelOrderViaLocation,
    CancelOrderViaPurview,
    CancelOrderViaPurviewNetworkCustodianCapability.name,
    CancelOrderViaOwner,
    CancelOrderOnBehalfOfViaSystemUser,
    CancelOrderViaUserCreated,
    CancelRatesOrderViaNetworkType,
    CancelRatesOrderViaNetwork,
    CancelRatesOrderViaOwner,
    CancelRatesOrderViaPurview,
    CancelRatesOrderViaPurviewNetworkCustodianCapability.name,
    CancelOrderAfterBooksCloseCapability.name
  )
  val ApproveCapabilities = Set(
    ApproveOrderViaApprovers,
    ApproveOrderOnBehalfOfViaSystemUser,
    ApproveRatesOrderViaApprovers,
    ApproveRatesOrderOnBehalfOfViaSystemUserCapability.name,
  ) //Note: We're excluding Admin from ApproveCapabilities as a business requirement
  val SubmitOnBehalfOfCapabilities = Set(
    Admin,
    ActionOrderAsAdminViaPurview,
    SubmitOrderOnBehalfOfViaNetworkType,
    SubmitOrderOnBehalfOfViaNetwork,
    SubmitOrderOnBehalfOfViaPurview,
    SubmitOrderOnBehalfOfViaPurviewNetworkCustodianCapability.name,
    SubmitRatesOrderOnBehalfOfViaNetworkType,
    SubmitRatesOrderOnBehalfOfViaNetwork,
    SubmitRatesOrderOnBehalfOfViaPurview,
    SubmitRatesOrderOnBehalfOfViaPurviewNetworkCustodianCapability.name
  )
  val HidePershingTabInExcelCapabilities = Set(HidePershingTabInExcel)
  val IncludeLocationsColumnInExcelCapabilities = Set(IncludeLocationsColumnInExcel)
  val IncludeAdditionalAccountInfoInExcelCapabilities = Set(IncludeAdditionalAccountInfoInExcel)
  val ActionOrderAsAdminViaPurviewCapabilities = Set(ActionOrderAsAdminViaPurview, Admin)
  val OverrideUserNetworkIdCapabilities = Set(OverrideUserNetworkId)
  val ViewOrdersAuditTrailViaPurviewCapabilities = Set(ViewOrdersAuditTrailViaPurviewCapability.name)
  val ViewOrdersAuditTrailViaPurviewNetworkCustodianCapabilities = Set(ViewOrdersAuditTrailViaPurviewNetworkCustodianCapability.name)

  override val DetailedViewCapabilities = Set(
    AdminCapability,
    ViewOrderViaNetworkTypeCapability,
    ViewOrderViaNetworkCapability,
    ViewOrderViaLocationCapability,
    ViewOrderViaPurviewCapability,
    ViewOrderViaPurviewNetworkCustodianCapability,
    ViewOrderViaOwnerCapability,
    ViewOrderViaFaNumberCapability,
    ViewOrderViaSystemUserCapability,
    ViewOrderViaUserCreatedCapability,
    ViewRatesOrderViaNetworkTypeCapability,
    ViewRatesOrderViaNetworkCapability,
    ViewRatesOrderViaLocationCapability,
    ViewRatesOrderViaPurviewCapability,
    ViewRatesOrderViaPurviewNetworkCustodianCapability,
    ViewRatesOrderViaOwnerCapability,
    ViewRatesOrderViaFaNumberCapability,
    ViewRatesOrderViaUserCreatedCapability
  )
  override val DetailedEditCapabilities = Set(
    AdminCapability,
    EditOrderViaNetworkTypeCapability,
    ActionOrderAsAdminViaPurviewCapability,
    EditOrderViaNetworkCapability,
    EditOrderViaLocationCapability,
    EditOrderViaOwnerCapability,
    EditOrderViaPurviewCapability,
    EditOrderViaPurviewNetworkCustodianCapability,
    EditOrderOnBehalfOfViaSystemUserCapability,
    EditOrderViaUserCreatedCapability,
    EditRatesOrderViaNetworkTypeCapability,
    EditRatesOrderViaOwnerCapability,
    EditRatesOrderViaNetworkCapability,
    EditRatesOrderViaPurviewCapability,
    EditRatesOrderViaPurviewNetworkCustodianCapability,
    EditOrderAfterBooksCloseCapability
  )
  val DetailedCancelCapabilities = Set(
    AdminCapability,
    ActionOrderAsAdminViaPurviewCapability,
    CancelOrderViaNetworkTypeCapability,
    CancelOrderViaNetworkCapability,
    CancelOrderViaLocationCapability,
    CancelOrderViaPurviewCapability,
    CancelOrderViaPurviewNetworkCustodianCapability,
    CancelOrderViaOwnerCapability,
    CancelOrderOnBehalfOfViaSystemUserCapability,
    CancelOrderViaUserCreatedCapability,
    CancelRatesOrderViaNetworkTypeCapability,
    CancelRatesOrderViaNetworkCapability,
    CancelRatesOrderViaOwnerCapability,
    CancelRatesOrderViaPurviewCapability,
    CancelRatesOrderViaPurviewNetworkCustodianCapability,
    CancelOrderAfterBooksCloseCapability
  )
  val DetailedApproveCapabilities = Set(
    ApproveOrderViaApproversCapability,
    ApproveOrderOnBehalfOfViaSystemUserCapability,
    ApproveRatesOrderViaApproversCapability,
    ApproveRatesOrderOnBehalfOfViaSystemUserCapability,
  ) //Note: We're excluding Admin from ApproveCapabilities as a business requirement
  val DetailedSubmitOnBehalfOfCapabilities = Set(
    AdminCapability,
    ActionOrderAsAdminViaPurviewCapability,
    SubmitOrderOnBehalfOfViaNetworkTypeCapability,
    SubmitOrderOnBehalfOfViaNetworkCapability,
    SubmitOrderOnBehalfOfViaPurviewCapability,
    SubmitOrderOnBehalfOfViaPurviewNetworkCustodianCapability,
    SubmitRatesOrderOnBehalfOfViaNetworkTypeCapability,
    SubmitRatesOrderOnBehalfOfViaNetworkCapability,
    SubmitRatesOrderOnBehalfOfViaPurviewCapability,
    SubmitRatesOrderOnBehalfOfViaPurviewNetworkCustodianCapability
  )
  val DetailedHidePershingTabInExcelCapabilities = Set(HidePershingTabInExcelCapability)
  val DetailedIncludeLocationsColumnInExcelCapabilities = Set(IncludeLocationsColumnInExcelCapability)
  val DetailedIncludeAdditionalAccountInfoInExcelCapabilities = Set(IncludeAdditionalAccountInfoInExcelCapability)
  val DetailedIncludeStructuringFeeColumnCapabilities = Set(IncludeStructuringFeeColumnCapability)
  val DetailedActionOrderAsAdminViaPurviewCapabilities = Set(AdminCapability, ActionOrderAsAdminViaPurviewCapability)
  val DetailedOverrideUserNetworkIdCapabilities = Set(OverrideUserNetworkIdCapability)
  val DetailedBlockSingleSIFeeOrderProductCapabilities = Set(BlockSingleSIFeeOrderProductCapability)
  val DetailedEnableOrderCustodianCapabilities = Set(AdminCapability, EnableOrderCustodianCapability)
  val DetailedViewOrdersAuditTrailViaPurviewCapabilities = Set(ViewOrdersAuditTrailViaPurviewCapability)
  val DetailedViewOrdersAuditTrailViaPurviewNetworkCustodianCapabilities = Set(ViewOrdersAuditTrailViaPurviewNetworkCustodianCapability)

  /** Defines available access keys for orders */
  val availableAccessKeysGen: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {
    override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys),
      ApproveOrderViaApprovers -> AvailableKeyBuilder(getAvailableKeysViaOwner),
      ApproveOrderOnBehalfOfViaSystemUser -> AvailableKeyBuilder(getAvailableKeysForSystemUser),
      ViewOrderViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      EditOrderViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      CancelOrderViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      SubmitOrderOnBehalfOfViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      ViewRatesOrderViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      EditRatesOrderViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      CancelRatesOrderViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      SubmitRatesOrderOnBehalfOfViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      ViewOrderViaOwner -> AvailableKeyBuilder(getAvailableKeysViaOwner),
      ViewOrderViaNetwork -> AvailableKeyBuilder(getAvailableKeysViaNetwork),
      ViewOrderViaPurview -> AvailableKeyBuilder(getAvailableKeysViaPurview),
      ViewOrderViaPurviewNetworkCustodianCapability.name -> AvailableKeyBuilder(getAvailableKeysViaPurviewNetworkCustodian(PurviewedDomain.SIEquitiesOrders)),
      ViewOrderViaFaNumber -> AvailableKeyBuilder(getAvailableKeysViaFaNumber),
      ViewOrderViaLocation -> AvailableKeyBuilder(getAvailableKeysViaLocations),
      ViewOrderViaSystemUser -> AvailableKeyBuilder(getAvailableKeysForSystemUser),
      ViewOrderViaUserCreated -> AvailableKeyBuilder(getAvailableKeysViaOwner),
      EditOrderViaNetwork -> AvailableKeyBuilder(getAvailableKeysViaNetwork),
      EditOrderViaLocation -> AvailableKeyBuilder(getAvailableKeysViaLocations),
      EditOrderViaPurview -> AvailableKeyBuilder(getAvailableKeysViaPurview),
      EditOrderViaPurviewNetworkCustodianCapability.name -> AvailableKeyBuilder(getAvailableKeysViaPurviewNetworkCustodian(PurviewedDomain.SIEquitiesOrders)),
      EditOrderOnBehalfOfViaSystemUser -> AvailableKeyBuilder(getAvailableKeysForSystemUser),
      EditOrderViaUserCreated -> AvailableKeyBuilder(getAvailableKeysViaOwner),
      EditOrderViaOwner -> AvailableKeyBuilder(getAvailableKeysViaOwner),
      EditOrderAfterBooksCloseCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      CancelOrderViaNetwork -> AvailableKeyBuilder(getAvailableKeysViaNetwork),
      CancelOrderViaOwner -> AvailableKeyBuilder(getAvailableKeysViaOwner),
      CancelOrderViaLocation -> AvailableKeyBuilder(getAvailableKeysViaLocations),
      CancelOrderViaPurview -> AvailableKeyBuilder(getAvailableKeysViaPurview),
      CancelOrderViaPurviewNetworkCustodianCapability.name -> AvailableKeyBuilder(getAvailableKeysViaPurviewNetworkCustodian(PurviewedDomain.SIEquitiesOrders)),
      CancelOrderOnBehalfOfViaSystemUser -> AvailableKeyBuilder(getAvailableKeysForSystemUser),
      CancelOrderViaUserCreated -> AvailableKeyBuilder(getAvailableKeysViaOwner),
      CancelOrderAfterBooksCloseCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      HidePershingTabInExcel -> AvailableKeyBuilder(undecoratedKeyBuilder),
      IncludeLocationsColumnInExcel -> AvailableKeyBuilder(undecoratedKeyBuilder),
      IncludeAdditionalAccountInfoInExcel -> AvailableKeyBuilder(undecoratedKeyBuilder),
      IncludeStructuringFeeColumnCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ActionOrderAsAdminViaPurview -> AvailableKeyBuilder(getAvailableKeyViaNetworkPurview),
      OverrideUserNetworkId -> AvailableKeyBuilder(undecoratedKeyBuilder),
      BlockSingleSIFeeOrderProduct -> AvailableKeyBuilder(undecoratedKeyBuilder),
      EnableOrderCustodianCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewOrdersAuditTrailViaPurviewCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewOrdersAuditTrailViaPurviewNetworkCustodianCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),

      ViewRatesOrderViaOwner -> AvailableKeyBuilder(getAvailableKeysViaOwner),
      ViewRatesOrderViaNetwork -> AvailableKeyBuilder(getAvailableKeysViaNetwork),
      ViewRatesOrderViaPurview -> AvailableKeyBuilder(getAvailableKeysViaPurview),
      ViewRatesOrderViaPurviewNetworkCustodianCapability.name -> AvailableKeyBuilder(getAvailableKeysViaPurviewNetworkCustodian(PurviewedDomain.SIRatesOrders)),
      ViewRatesOrderViaLocation -> AvailableKeyBuilder(getAvailableKeysViaLocations),
      ViewRatesOrderViaFaNumber -> AvailableKeyBuilder(getAvailableKeysViaFaNumber),
      ViewRatesOrderViaUserCreated -> AvailableKeyBuilder(getAvailableKeysViaOwner),
      EditRatesOrderViaOwner -> AvailableKeyBuilder(getAvailableKeysViaOwner),
      EditRatesOrderViaNetwork -> AvailableKeyBuilder(getAvailableKeysViaNetwork),
      EditRatesOrderViaPurview -> AvailableKeyBuilder(getAvailableKeysViaPurview),
      EditRatesOrderViaPurviewNetworkCustodianCapability.name -> AvailableKeyBuilder(getAvailableKeysViaPurviewNetworkCustodian(PurviewedDomain.SIRatesOrders)),
      CancelRatesOrderViaOwner -> AvailableKeyBuilder(getAvailableKeysViaOwner),
      CancelRatesOrderViaNetwork -> AvailableKeyBuilder(getAvailableKeysViaNetwork),
      CancelRatesOrderViaPurview -> AvailableKeyBuilder(getAvailableKeysViaPurview),
      CancelRatesOrderViaPurviewNetworkCustodianCapability.name -> AvailableKeyBuilder(getAvailableKeysViaPurviewNetworkCustodian(PurviewedDomain.SIRatesOrders)),
      ApproveRatesOrderViaApprovers -> AvailableKeyBuilder(getAvailableKeysViaOwner),
      ApproveRatesOrderOnBehalfOfViaSystemUserCapability.name -> AvailableKeyBuilder(getAvailableKeysForSystemUser),
    )

    private def getAvailableKeysForSystemUser(capability: String, userACL: UserACL): Set[String] = {
      Set(capability)
    }

    private def getAvailableKeysViaFaNumber(capability: String, userACL: UserACL): Set[String] = {
      val buffer = mutable.Buffer.empty[String]
      userACL.faNumbers.foreach {
        faNumber => buffer.append(s"$capability:${userACL.networkId}:$faNumber")
      }
      buffer.toSet
    }

    private def getAvailableKeysViaOwner(capability: String, userACL: UserACL): Set[String] = {
      val buffer = mutable.Buffer.empty[String]
      buffer.append(s"$capability:${userACL.userId}")
      buffer.toSet
    }

    private def getAvailableKeysViaNetwork(capability: String, userACL: UserACL): Set[String] = {
      val buffer = mutable.Buffer.empty[String]
      buffer.append(s"$capability:${NetworkId.unwrap(userACL.networkId)}")
      buffer.toSet
    }

    private def getAvailableKeysViaPurview(capability: String, userACL: UserACL): Set[String] = {
      val buffer = mutable.Buffer.empty[String]
      userACL.purviews.getOrElse(Set.empty).foreach { purview =>
        purview.purviewEntities.foreach { entity =>
            if (entity.purviewType.contains(NetworkCategory.Issuer)) {
              buffer.append(s"$capability:${purview.networkId}:${entity.key}")
          }
        }
      }
      buffer.toSet
    }

    private def getAvailableKeysViaPurviewNetworkCustodian(purviewedDomain: PurviewedDomain)(capability: String, userACL: UserACL): Set[String] = {
      val buffer = mutable.Buffer.empty[String]
      userACL.purviews.getOrElse(Set.empty).foreach { purview =>
        purview.purviewEntities.foreach { entity =>
            if (entity.purviewType.contains(NetworkCategory.Custodian)) {
              entity.domains.foreach { domains =>
                domains.foreach { domain =>
                  if (domain == purviewedDomain) {
                    buffer.append(s"$capability:${purview.networkId}:${entity.key}")
                }
              }
            }
          }
        }
      }
      buffer.toSet
    }

    private def getAvailableKeyViaNetworkPurview(capability: String, userACL: UserACL): Set[String] = {
      val buffer = mutable.Buffer.empty[String]
      for {
        network <- userACL.userPurviewIds
      } yield buffer.append(s"$capability:${NetworkId.unwrap(network)}")
      buffer.toSet
    }

    private def getAvailableKeysViaLocations(capability: String, userACL: UserACL): Set[String] = {
      val buffer = mutable.Buffer.empty[String]
      userACL.locations.foreach {
        location =>
          buffer.append(s"$capability:${NetworkId.unwrap(userACL.networkId)}:$location")
      }
      buffer.toSet
    }
  }

  override def toDetailedCapabilitySet: Set[Capability] = {
    DetailedViewCapabilities ++
    DetailedEditCapabilities ++
    DetailedCancelCapabilities ++
    DetailedApproveCapabilities ++
    DetailedSubmitOnBehalfOfCapabilities ++
    DetailedHidePershingTabInExcelCapabilities ++
    DetailedIncludeLocationsColumnInExcelCapabilities ++
    DetailedIncludeAdditionalAccountInfoInExcelCapabilities ++
    DetailedIncludeStructuringFeeColumnCapabilities ++
    DetailedActionOrderAsAdminViaPurviewCapabilities ++
    DetailedOverrideUserNetworkIdCapabilities ++
    DetailedBlockSingleSIFeeOrderProductCapabilities ++
    DetailedEnableOrderCustodianCapabilities ++
    DetailedViewOrdersAuditTrailViaPurviewCapabilities ++
    DetailedViewOrdersAuditTrailViaPurviewNetworkCustodianCapabilities
  }
}
