package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.UsersCapabilities.{ImpersonateUserViaFANumber, ImpersonateUserViaFANumberWithApproval, ImpersonateUserViaLocation, ImpersonateUserViaLocationWithApproval, ImpersonateUserViaNetwork, ImpersonateUserViaNetworkWithApproval, ImpersonateUserViaPurview, ImpersonateUserViaPurviewWithApproval}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildFANumberKeys, buildLocationKeys, buildNetworkKeys, buildUserPurviewedDomainPurviewKeys}
import com.simonmarkets.entitlements.{AcceptedAccessKeysGenerator, AcceptedKeyBuilder, AvailableAccessKeysGenerator, AvailableKeyBuilder}
import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues
import simon.Id.NetworkId

/*
* Helper case objects to signal 3-way test for approval: Yes, No, With Approval
* */
@EnumValues("CannotImpersonate", "CanImpersonateWithApproval", "CanImpersonate")
sealed trait CanImpersonateStatus extends EnumEntry

object CanImpersonateStatus extends ProductEnums[CanImpersonateStatus] {
  case object CannotImpersonate extends CanImpersonateStatus
  case object CanImpersonateWithApproval extends CanImpersonateStatus
  case object CanImpersonate extends CanImpersonateStatus

  override def Values: Seq[CanImpersonateStatus] = Seq(CannotImpersonate, CanImpersonateWithApproval, CanImpersonate)
}

/*
* A file created specifically to help generate accepted access keys for the widely-used virtual object, the UserACL.
* This class dictates that having UsersCapabilities.ImpersonateCapabilities gives you UserACL impersonation access as well.
* */
object UserACLCapabilitiesHelper {

  val availableAccessKeysGen: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {
    override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys),
      ImpersonateUserViaPurview -> AvailableKeyBuilder(buildUserPurviewedDomainPurviewKeys),
      ImpersonateUserViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      ImpersonateUserViaLocation -> AvailableKeyBuilder(buildLocationKeys),
      ImpersonateUserViaFANumber -> AvailableKeyBuilder(buildFANumberKeys),
    )
  }

  val availableAccessKeysWithApprovalGen = new AvailableAccessKeysGenerator {
    override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      ImpersonateUserViaPurviewWithApproval -> AvailableKeyBuilder(buildUserPurviewedDomainPurviewKeys),
      ImpersonateUserViaNetworkWithApproval -> AvailableKeyBuilder(buildNetworkKeys),
      ImpersonateUserViaLocationWithApproval -> AvailableKeyBuilder(buildLocationKeys),
      ImpersonateUserViaFANumberWithApproval -> AvailableKeyBuilder(buildFANumberKeys),
    )
  }

  abstract class AcceptedAccessKeysGeneratorHelper extends AcceptedAccessKeysGenerator[UserACL] {
    protected def buildAcceptedNetworkKeys(capability: String, userACL: UserACL): Set[String] = Set(s"$capability:${userACL.networkId}")

    protected def buildAcceptedUserPurviewKeys(capability: String, userACL: UserACL): Set[String] = Set(s"$capability:${NetworkId.unwrap(userACL.networkId)}")

    protected def buildAcceptedLocationKeys(capability: String, userACL: UserACL): Set[String] = userACL.locations.map { location => s"$capability:${NetworkId.unwrap(userACL.networkId)}:$location" }

    protected def buildAcceptedFANumberKeys(capability: String, userACL: UserACL): Set[String] = userACL.faNumbers.map(faNumber => s"$capability:${NetworkId.unwrap(userACL.networkId)}:$faNumber")
  }

  val acceptedAccessKeysGen = new AcceptedAccessKeysGeneratorHelper {
    override def capabilityToAcceptedKeyBuilders: Map[String, AcceptedKeyBuilder[UserACL]] = Map(
      Admin -> AcceptedKeyBuilder(buildAdminKeys),
      ImpersonateUserViaPurview -> AcceptedKeyBuilder(buildAcceptedUserPurviewKeys),
      ImpersonateUserViaNetwork -> AcceptedKeyBuilder(buildAcceptedNetworkKeys),
      ImpersonateUserViaLocation -> AcceptedKeyBuilder(buildAcceptedLocationKeys),
      ImpersonateUserViaFANumber -> AcceptedKeyBuilder(buildAcceptedFANumberKeys),
    )
  }

  val acceptedAccessKeysWithApprovalGen = new AcceptedAccessKeysGeneratorHelper {
    override def capabilityToAcceptedKeyBuilders: Map[String, AcceptedKeyBuilder[UserACL]] = Map(
      ImpersonateUserViaPurviewWithApproval -> AcceptedKeyBuilder(buildAcceptedUserPurviewKeys),
      ImpersonateUserViaNetworkWithApproval -> AcceptedKeyBuilder(buildAcceptedNetworkKeys),
      ImpersonateUserViaLocationWithApproval -> AcceptedKeyBuilder(buildAcceptedLocationKeys),
      ImpersonateUserViaFANumberWithApproval -> AcceptedKeyBuilder(buildAcceptedFANumberKeys),
    )

  }

  def canImpersonate(impersonator: UserACL, impersonated: UserACL): CanImpersonateStatus = {
    import CanImpersonateStatus._

    val availKeys = availableAccessKeysGen.getAvailableAccessKeys(impersonator)
    val acceptedKeys = acceptedAccessKeysGen.getAcceptedAccessKeys(impersonated)
    lazy val availApprovalKeys = availableAccessKeysWithApprovalGen.getAvailableAccessKeys(impersonator)
    lazy val acceptedApprovalKeys = acceptedAccessKeysWithApprovalGen.getAcceptedAccessKeys(impersonated)

    if (availKeys.exists(acceptedKeys.contains)) {
      CanImpersonate
    } else if (availApprovalKeys.exists(acceptedApprovalKeys.contains)) {
      CanImpersonateWithApproval
    } else {
      CannotImpersonate
    }
  }
}
