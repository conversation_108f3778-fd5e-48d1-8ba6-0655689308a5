package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator._
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

object SmaStrategyCapabilities extends Capabilities with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities
  with AvailableAccessKeysGenerator {

  override val DomainName: String = "SmaStrategy"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.SMAsAssetClass))

  val ViewSmaStrategyViaStrategyIdCapability: Capability = Capability("viewSmaStrategyViaStrategyId", "", assetClasses)
  val EditSmaStrategyViaStrategyIdCapability: Capability = Capability("editSmaStrategyViaStrategyId", "", assetClasses)

  val ViewSmaStrategyViaStrategyId: String = ViewSmaStrategyViaStrategyIdCapability.name
  val EditSmaStrategyViaStrategyId: String = EditSmaStrategyViaStrategyIdCapability.name

  val DeleteCapabilities: Set[String] = Set(Admin)

  override val DetailedViewCapabilities: Set[Capability] = Set(
    AdminCapability,
    ViewSmaStrategyViaStrategyIdCapability
  )
  override val DetailedEditCapabilities: Set[Capability] = Set(
    AdminCapability,
    EditSmaStrategyViaStrategyIdCapability
  )

  val DetailedDeleteCapabilities: Set[Capability] = Set(AdminCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities ++ DetailedDeleteCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewSmaStrategyViaStrategyId -> AvailableKeyBuilder(buildSmaStrategyKeys),
    EditSmaStrategyViaStrategyId -> AvailableKeyBuilder(buildSmaStrategyKeys)
  )
}
