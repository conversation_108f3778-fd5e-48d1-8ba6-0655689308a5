package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator._
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder}

object LearnContentActivityCapabilities extends Capabilities with AvailableAccessKeysGenerator {
  override val DomainName: String = "LearnContentActivity"

  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Platform))

  val ViewContentActivityViaNetworkTypeCapability: Capability = Capability("viewContentActivityViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val EditContentActivityViaNetworkTypeCapability: Capability = Capability("editContentActivityViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)

  val ViewContentActivityViaNetwork: String = "viewContentActivityViaNetwork"
  val ViewContentActivityViaOwner: String = "viewContentActivityViaOwner"
  val ViewContentActivityViaPurview: String = "viewContentActivityViaPurview"
  val ViewContentActivityViaLocation: String = "viewContentActivityViaLocation"
  val EditContentActivityViaOwner: String = "editContentActivityViaOwner"
  val ViewContentActivityViaNetworkType: String = ViewContentActivityViaNetworkTypeCapability.name
  val EditContentActivityViaNetworkType: String = EditContentActivityViaNetworkTypeCapability.name

  val ViewContentActivityViaNetworkCapability: Capability = Capability(ViewContentActivityViaNetwork, "", assetClasses)
  val ViewContentActivityViaOwnerCapability: Capability = Capability(ViewContentActivityViaOwner, "", assetClasses)
  val ViewContentActivityViaPurviewCapability: Capability = Capability(ViewContentActivityViaPurview, "", assetClasses)
  val ViewContentActivityViaLocationCapability: Capability = Capability(ViewContentActivityViaLocation, "", assetClasses)
  val EditContentActivityViaOwnerCapability: Capability = Capability(EditContentActivityViaOwner, "", assetClasses)


  val ViewActivityCapabilities: Set[String] = Set(ViewContentActivityViaNetwork, ViewContentActivityViaOwner,
    ViewContentActivityViaPurview, ViewContentActivityViaLocation, ViewContentActivityViaNetworkType, Admin)
  val EditActivityCapabilities: Set[String] = Set(Admin, EditContentActivityViaOwner, EditContentActivityViaNetworkType)

  val DetailedViewActivityCapabilities: Set[Capability] = Set(ViewContentActivityViaNetworkCapability, ViewContentActivityViaOwnerCapability,
    ViewContentActivityViaPurviewCapability, ViewContentActivityViaLocationCapability, ViewContentActivityViaNetworkTypeCapability, AdminCapability)
  val DetailedEditActivityCapabilities: Set[Capability] = Set(AdminCapability, EditContentActivityViaOwnerCapability, EditContentActivityViaNetworkTypeCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewActivityCapabilities ++ DetailedEditActivityCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewContentActivityViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
    EditContentActivityViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
    ViewContentActivityViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    ViewContentActivityViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    ViewContentActivityViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys),
    ViewContentActivityViaLocation -> AvailableKeyBuilder(buildLocationKeys),
    EditContentActivityViaOwner -> AvailableKeyBuilder(buildGuidKeys),
  )
}
