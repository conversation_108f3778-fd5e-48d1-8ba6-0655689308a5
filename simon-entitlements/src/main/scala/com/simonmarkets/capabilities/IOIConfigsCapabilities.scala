package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildNetworkKeys, buildUserPurviewKeys}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities}

object IOIConfigsCapabilities extends Capabilities with AvailableAccessKeysGenerator with HasDetailedViewCapabilities with HasDetailedEditCapabilities {
  override val DomainName: String = "IOIConfigs"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.StructuredInvestmentsAssetClass))

  val ViewIOIConfigViaNetworkCapability: Capability = Capability("viewIOIConfigViaNetwork", "able to view their network's ioi config", assetClasses)
  val ViewIOIConfigViaPurviewCapability: Capability = Capability("viewIOIConfigViaPurview", "able to view their purview network's ioi config", assetClasses)

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewIOIConfigViaNetworkCapability.name -> AvailableKeyBuilder(buildNetworkKeys),
    ViewIOIConfigViaPurviewCapability.name -> AvailableKeyBuilder(buildUserPurviewKeys)
  )

  override val DetailedViewCapabilities: Set[Capability] = Set(
    AdminCapability,
    ViewIOIConfigViaNetworkCapability,
    ViewIOIConfigViaPurviewCapability
  )

  override val DetailedEditCapabilities: Set[Capability] = Set(AdminCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities
}
