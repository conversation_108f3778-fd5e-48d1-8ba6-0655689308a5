package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.AdminCapability
import com.simonmarkets.capabilities.LearnV2QuizCapabilities.assetClasses
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.buildNetworkTypeKeys
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder}

object LearnV2AttestationCapabilities extends Capabilities with AvailableAccessKeysGenerator {
  override val DomainName: String = "LearnAttestationsCapabilities"

  val ViewAttestationCapabilitiesViaNetworkTypeCapability: Capability = Capability("viewAttestationCapabilitiesViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val EditAttestationCapabilitiesViaNetworkTypeCapability: Capability = Capability("editAttestationCapabilitiesViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)

  override val capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Capabilities.Admin -> availableKeyBuilder,
    ViewAttestationCapabilitiesViaNetworkTypeCapability.name -> AvailableKeyBuilder(buildNetworkTypeKeys),
    EditAttestationCapabilitiesViaNetworkTypeCapability.name -> AvailableKeyBuilder(buildNetworkTypeKeys),
  )

  val DetailedViewAttestationCapabilities: Set[Capability] = Set(AdminCapability, ViewAttestationCapabilitiesViaNetworkTypeCapability)
  val DetailedEditAttestationCapabilities: Set[Capability] = Set(AdminCapability, EditAttestationCapabilitiesViaNetworkTypeCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewAttestationCapabilities ++ DetailedEditAttestationCapabilities
}
