package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator._
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder}

object LearnAttestationCapabilities extends Capabilities with AvailableAccessKeysGenerator {
  override val DomainName: String = "LearnAttestation"

  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Platform))

  val ViewAttestationActivityViaNetworkTypeCapability: Capability = Capability("viewAttestationActivityViaNetworkType", "", assetClasses)
  val EditAttestationActivityViaNetworkTypeCapability: Capability = Capability("editAttestationActivityViaNetworkType", "", assetClasses)

  val ViewAttestationActivityViaNetwork: String = "viewAttestationActivityViaNetwork"
  val ViewAttestationActivityViaOwner: String = "viewAttestationActivityViaOwner"
  val ViewAttestationActivityViaPurview: String = "viewAttestationActivityViaPurview"
  val ViewAttestationActivityViaLocation: String = "viewAttestationActivityViaLocation"
  val EditAttestationActivityViaOwner: String = "editAttestationActivityViaOwner"
  val ViewAttestationActivityViaNetworkType: String = ViewAttestationActivityViaNetworkTypeCapability.name
  val EditAttestationActivityViaNetworkType: String = EditAttestationActivityViaNetworkTypeCapability.name

  val ViewAttestationActivityViaNetworkCapability: Capability = Capability(ViewAttestationActivityViaNetwork, "", assetClasses)
  val ViewAttestationActivityViaOwnerCapability: Capability = Capability(ViewAttestationActivityViaOwner, "", assetClasses)
  val ViewAttestationActivityViaPurviewCapability: Capability = Capability(ViewAttestationActivityViaPurview, "", assetClasses)
  val ViewAttestationActivityViaLocationCapability: Capability = Capability(ViewAttestationActivityViaLocation, "", assetClasses)
  val EditAttestationActivityViaOwnerCapability: Capability = Capability(EditAttestationActivityViaOwner, "", assetClasses)

  val ViewActivityCapabilities: Set[String] = Set(ViewAttestationActivityViaNetwork, ViewAttestationActivityViaOwner,
    ViewAttestationActivityViaPurview, ViewAttestationActivityViaLocation, ViewAttestationActivityViaNetworkType, Admin)
  val EditActivityCapabilities: Set[String] = Set(Admin, EditAttestationActivityViaOwner, EditAttestationActivityViaNetworkType)


  val DetailedViewActivityCapabilities: Set[Capability] = Set(ViewAttestationActivityViaNetworkCapability, ViewAttestationActivityViaOwnerCapability,
    ViewAttestationActivityViaPurviewCapability, ViewAttestationActivityViaLocationCapability, ViewAttestationActivityViaNetworkTypeCapability, AdminCapability)
  val DetailedEditActivityCapabilities: Set[Capability] = Set(AdminCapability, EditAttestationActivityViaOwnerCapability, EditAttestationActivityViaNetworkTypeCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewActivityCapabilities ++ DetailedEditActivityCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = {
    Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys),
      ViewAttestationActivityViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      EditAttestationActivityViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      ViewAttestationActivityViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      ViewAttestationActivityViaOwner -> AvailableKeyBuilder(buildGuidKeys),
      ViewAttestationActivityViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys),
      ViewAttestationActivityViaLocation -> AvailableKeyBuilder(buildLocationKeys),
      EditAttestationActivityViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    )
  }
}
