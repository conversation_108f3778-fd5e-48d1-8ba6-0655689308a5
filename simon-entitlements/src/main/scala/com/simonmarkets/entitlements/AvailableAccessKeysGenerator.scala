package com.simonmarkets.entitlements

import com.goldmansachs.marquee.pipg.{Network, NetworkType, PurviewedDomain, UserACL}
import com.gs.marquee.util.SetHelper._
import com.simonmarkets.networks.NetworkIds.AdminNetworkId
import com.simonmarkets.networks.{ExternalId, SmaStrategyAndUnderliers}
import simon.Id.NetworkId

trait AvailableAccessKeysGenerator {

  def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder]

  def getAvailableAccessKeys(user: UserACL): Set[String] = {
    capabilityToAvailableKeyBuilders.keySet.intersect(user.capabilities).flatMap(capability => {
      capabilityToAvailableKeyBuilders(capability)(capability, user)
    })
  }

  def getAvailableAccessKeysForCapability(capability: String, user: UserACL): Set[String] = {
    if (user.capabilities.contains(capability) && capabilityToAvailableKeyBuilders.contains(capability)) {
      capabilityToAvailableKeyBuilders(capability)(capability, user)
    } else {
      Set.empty[String]
    }
  }

  def getAvailableAccessKeysForCapabilities(capabilities: Set[String], user: UserACL): Set[String] = {
    capabilities.flatMap { capability =>
      getAvailableAccessKeysForCapability(capability, user)
    }
  }

  def availableKeyBuilder: AvailableKeyBuilder = AvailableKeyBuilder((capability, _) => Set(capability))

}

object AvailableAccessKeysGenerator {
  def buildLocationKeys(capability: String, userACL: UserACL): Set[String] = {
    Set(s"$capability:${userACL.networkId}").cartesianProduct(userACL.locationsTree).map {
      case (capabilityPlusNetwork, locationId) => capabilityPlusNetwork + ":" + locationId
    }
  }

  def buildNetworkTypeKeys(capability: String, user: UserACL): Set[String] =
    user.networkTypes.map(networkTypes => networkTypes.map(networkType => {
      //TODO: APPSERV-62935 temp hack... remove conditional, should only need $capability:${networkType.name}
      if (networkType == NetworkType.SMAManager) {
        s"$capability:${NetworkType.Admin.name}"
      } else {
        s"$capability:${networkType.name}"
      }
    }).toSet).getOrElse(Set.empty[String])

  def buildAllAccessibleLocationKeys(capability: String, userACL: UserACL): Set[String] = {
    Set(s"$capability:${userACL.networkId}").cartesianProduct(userACL.allAccessibleLocations).map {
      case (capabilityPlusNetwork, locationId) => capabilityPlusNetwork + ":" + locationId
    }
  }

  def buildAdminKeys(capability: String, user: UserACL): Set[String] = {
    if (user.networkId == AdminNetworkId) Set(capability)
    else Set.empty
  }

  // Default key builder for most capabilities. Does zero checks and appends to create the key set.
  def undecoratedKeyBuilder(capability: String, user: UserACL): Set[String] = Set(capability)

  def buildGuidKeys(capability: String, userACL: UserACL): Set[String] = Set(s"$capability:${userACL.userId}")

  def buildFANumberKeys(capability: String, userACL: UserACL): Set[String] =
    userACL.faNumbers.map(faNumber => s"$capability:${userACL.networkId}:$faNumber")

  def buildParentNetworkKeys(capability: String, userACL: UserACL): Set[String] =
    userACL.group.toSet[String].map(groupO => s"$capability:$groupO")

  def buildNetworkKeys(capability: String, userACL: UserACL): Set[String] =
    Set(s"$capability:${userACL.networkId}")

  def buildWhiteLabelPartnerKeys(capability: String, userACL: UserACL): Set[String] =
    userACL.whiteLabelPartnerId.map(wlpId => Set(s"$capability:$wlpId")).getOrElse(Set.empty[String])

  def buildFirmKeys(capability: String, userACL: UserACL): Set[String] =
    userACL.firmId.map(firmId => Set(s"$capability:$firmId")).getOrElse(Set.empty[String])

  def buildWhiteLabelPartnerAndFirmKeys(capability: String, userACL: UserACL): Set[String] =
    (userACL.whiteLabelPartnerId, userACL.firmId) match {
      case (Some(wlpId), Some(firmId)) => Set(s"$capability:$wlpId:$firmId")
      case _ => Set.empty[String]
    }

  def buildWhiteLabelPartnerWithIcnRoleKeys(capability: String, userACL: UserACL): Set[String] = {
    (userACL.whiteLabelPartnerId, userACL.icnRoles) match {
      case (Some(wlpId), Some(icnRoles)) =>
        icnRoles.map(icnRole => s"$capability:$wlpId:$icnRole")
      case _ => Set.empty[String]
    }
  }

  def buildWhiteLabelPartnerAndFirmWithIcnRoleKeys(capability: String, userACL: UserACL): Set[String] = {
    (userACL.whiteLabelPartnerId, userACL.firmId, userACL.icnRoles) match {
      case (Some(wlpId), Some(firmId), Some(icnRoles)) =>
        icnRoles.map(icnRole => s"$capability:$wlpId:$firmId:$icnRole")
      case _ => Set.empty[String]
    }
  }

  //Builds keys for networks whose users you have purview over
  def buildUserPurviewKeys(capability: String, userACL: UserACL): Set[String] =
    userACL.userPurviewIds.map(purviewNetworkId => s"$capability:$purviewNetworkId")

  def buildUserPurviewedDomainPurviewKeys(capability: String, userACL: UserACL): Set[String] =
    userACL.issuerPurviewIds.filter(purviewNetwork => purviewNetwork.purviewedDomainsUpdated.exists(_.contains(PurviewedDomain.Users)))
      .map(purviewNetwork => s"$capability:${NetworkId.unwrap(purviewNetwork.network)}")

  def buildCusipsKeys(capability: String, acl: UserACL): Set[String] = {
    acl.cusips.map(cusip => s"$capability:$cusip")
  }

  def buildGroupIdKeys(groupIdType: String)(capability: String, user: UserACL): Set[String] = {
    user.groups.getOrElse(Map.empty).getOrElse(groupIdType, Set.empty).map { groupValue =>
      s"$capability:${user.networkId}:$groupValue"
    }
  }

  def buildPayoffEntitlementKeys(capability: String, userACL: UserACL): Set[String] = {
    if (userACL.payoffEntitlementsV2.nonEmpty) {
      userACL.payoffEntitlementsV2.flatMap {
        case (issuerKey, payoffData) => payoffData.flatMap {
          case (payoffType, actions) => actions.map((
            action: Network.Action) => s"$capability:$issuerKey:$payoffType:${action.action}")
        }
      }.toSet
    } else {
      userACL.payoffEntitlements.flatMap {
        case (issuerKey, payoffData) => payoffData.flatMap {
          case (payoffType, actions) => actions.map((action: String) => s"$capability:$issuerKey:$payoffType:$action")
        }
      }.toSet
    }
  }

  def payoffEntitlementKeysForBuild(capability: String, userACL: UserACL): Set[String] =
    payoffEntitlementKeysForAction(capability, userACL)("build")

  def payoffEntitlementKeysForPerf(capability: String, userACL: UserACL): Set[String] =
    payoffEntitlementKeysForAction(capability, userACL)("performance")

  def payoffEntitlementKeysForIllustrate(capability: String, userACL: UserACL): Set[String] = {
    payoffEntitlementKeysForAction(capability, userACL)("illustrate")
  }

  def payoffEntitlementKeysForCanSell(capability: String, userACL: UserACL): Set[String] = {
    payoffEntitlementKeysForAction(capability, userACL)("canSell")
  }

  def payoffEntitlementKeysForView(capability: String, userACL: UserACL): Set[String] = {
    payoffEntitlementKeysForAction(capability, userACL)("view")
  }

  def payoffEntitlementKeysForTrade(capability: String, userACL: UserACL): Set[String] = {
    payoffEntitlementKeysForAction(capability, userACL)("trade")
  }

  def payoffEntitlementKeysForEdit(capability: String, userACL: UserACL): Set[String] = {
    payoffEntitlementKeysForAction(capability, userACL)("edit")
  }

  def payoffEntitlementKeysForAction(capability: String, userACL: UserACL)(action: String): Set[String] =
    if (userACL.payoffEntitlementsV2.nonEmpty) {
      userACL.payoffEntitlementsV2.flatMap {
        case (issuerKey, payoffData) => payoffData.flatMap {
          case (payoffType, actions) => actions.filter(_.action.contains(action)).map(
            _ => s"$capability:$issuerKey:$payoffType")
        }
      }.toSet
    } else {
      userACL.payoffEntitlements.flatMap {
        case (issuerKey, payoffData) => payoffData.flatMap {
          case (payoffType, actions) => actions.filter(_.contains(action)).map(
            _ => s"$capability:$issuerKey:$payoffType")
        }
      }.toSet
    }

  def payoffEntitlementKeysForPayoffTypeAndAction(capability: String, userACL: UserACL)(payoff: String, action: String): Set[String] =
    if (userACL.payoffEntitlementsV2.nonEmpty) {
      userACL.payoffEntitlementsV2.flatMap {
        case (issuerKey, payoffData) => payoffData.flatMap {
          case (payoffType, actions) if payoff == payoffType => actions.filter(_.action.contains(action)).map(
            _ => s"$capability:$issuerKey:$payoffType")
          case (_, _) => List.empty[String]
        }
      }.toSet
    } else {
      userACL.payoffEntitlements.flatMap {
        case (issuerKey, payoffData) => payoffData.flatMap {
          case (payoffType, actions) if payoff == payoffType => actions.filter(_.contains(action)).map(
            _ => s"$capability:$issuerKey:$payoffType")

          case (_, _) => List.empty[String]
        }
      }.toSet
    }

  def payoffEntitlementKeysForPurviewPayoffTypeAndAction(capability: String, userACL: UserACL)(payoff: String, action: String): Set[String] = {
    val networkToPurviewIssuerPurview = userACL.issuerPurviewIds.groupBy(_.network)
    userACL.userPurviewIds.flatMap { purviewNetworkId =>
      if (userACL.payoffEntitlementsV2.nonEmpty) {
        userACL.payoffEntitlementsV2.flatMap {
          case (_, payoffData) => payoffData.flatMap {
            case (payoffType, actions) if payoff == payoffType => actions.filter(_.action.contains(action)).flatMap(
              _ => {
                val networkToPurviewIssuerIds = networkToPurviewIssuerPurview.getOrElse(purviewNetworkId, Set.empty).map(_.issuers)
                networkToPurviewIssuerIds.flatMap { issuerIds =>
                  issuerIds.map(issuerId => s"$capability:${purviewNetworkId.toString}:$issuerId:$payoffType:$action")
                }
              }
            ).toList
            case (_, _) => List.empty[String]
          }
        }.toSet
      } else {
        userACL.payoffEntitlements.flatMap {
          case (_, payoffData) => payoffData.flatMap {
            case (payoffType, actions) if payoff == payoffType => actions.filter(_.contains(action)).flatMap(
              _ => {
                val networkToPurviewIssuerIds = networkToPurviewIssuerPurview.getOrElse(purviewNetworkId, Set.empty).map(_.issuers)
                networkToPurviewIssuerIds.flatMap { issuerIds =>
                  issuerIds.map(issuerId => s"$capability:${purviewNetworkId.toString}:$issuerId:$payoffType:$action")
                }
              }
            )
            case (_, _) => List.empty[String]
          }
        }.toSet
      }
    }
  }


  // create capability keys in a typesafe way
  // reason: s"" interpolator can call toString on everything, we don't want that because
  // it results in things like: "capability:Some(issuer):something"
  def mkKey(strParts: String*): String = strParts.mkString(":")

  //Builds keys for networks you have product purview over
  def buildNetworkIssuerPurviewKeys(capability: String, userACL: UserACL): Set[String] =
    for {
      issuerPurview <- userACL.issuerPurviewIds
      issuerSymbol <- issuerPurview.issuers
    } yield mkKey(capability, issuerPurview.network.toString, issuerSymbol)

  //Builds keys for networks you have specific product purviewed domain over
  def buildNetworkIssuerPurviewedDomainKeys(capability: String, userACL: UserACL, purviewedDomain: PurviewedDomain): Set[String] =
    for {
      issuerPurview <- userACL.issuerPurviewIds.filter(issuerPurview =>
        issuerPurview.purviewedDomainsUpdated.exists(purviewedDomainsSet => purviewedDomainsSet.contains(purviewedDomain))
      )
      issuerSymbol <- issuerPurview.issuers
    } yield mkKey(capability, issuerPurview.network.toString, issuerSymbol)

  def buildSystemUserKeys(capability: String, userACL: UserACL): Set[String] = {
    Set(capability)
  }

  def buildSmaStrategyKeys(capability: String, userACL: UserACL): Set[String] =
    for {
      strategiesAndUnderliers <- userACL.smaStrategiesAndUnderliers.toSet[Set[SmaStrategyAndUnderliers]]
      strategyAndUnderliers <- strategiesAndUnderliers
    } yield mkKey(capability, strategyAndUnderliers.strategyId)


  def buildSmaStrategyAndUnderlierKeys(capability: String, userACL: UserACL): Set[String] =
    for {
      strategiesAndUnderliers <- userACL.smaStrategiesAndUnderliers.toSet[Set[SmaStrategyAndUnderliers]]
      strategyAndUnderliers <- strategiesAndUnderliers
      underlier <- strategyAndUnderliers.underliers
    } yield mkKey(capability, strategyAndUnderliers.strategyId, underlier)

  def buildExternalIdKeys(capability: String, userACL: UserACL): Set[String] =
    for {
      externalIds <- userACL.externalIds.toSet[Seq[ExternalId]]
      externalId <- externalIds.toSet[ExternalId]
    } yield mkKey(capability, externalId.subject, externalId.id)
}

case class AvailableKeyBuilder(keyBuilder: (String, UserACL) => Set[String]) {
  def apply(capability: String, user: UserACL): Set[String] = keyBuilder(capability, user)

  /** Adds given section to keys */
  def add(section: UserACL => Any): AvailableKeyBuilder = {
    addList(user => section(user).toString :: Nil)
  }

  /** Adds given section to keys, only if present */
  def addOpt(section: UserACL => Option[String]): AvailableKeyBuilder = {
    addList(user => section(user).toList)
  }

  /** Adds all of given sections to keys, producing combinations of keys and sections */
  def addList(sections: UserACL => Traversable[String]): AvailableKeyBuilder = {
    AvailableKeyBuilder((c, u) => {
      val keys = keyBuilder(c, u)
      sections(u).flatMap(section => keys.map(key => s"$key:$section")).toSet
    })
  }
}