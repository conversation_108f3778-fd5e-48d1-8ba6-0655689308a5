package com.simonmarkets.entitlements

import com.goldmansachs.marquee.pipg.NetworkType

/* This trait should be mixed into any class that creates or updates database resources
*  and wants to control user access to it via the Access Keys mechanism. The trait helps
*  generate access keys based on properties of the resource being saved/updated.
*/
trait AcceptedAccessKeysGenerator[A] {

  def buildAdminKeys(capability: String, resource: Any): Set[String] = Set(capability)

  def buildAdminNetworkTypeKey(capability: String, resource: Any): Set[String] = {
    Set(s"$capability:${NetworkType.Admin.name}")
  }

  // Override this mapping variable to define all capabilities for the domain and the way to
  // generate access keys for each capability based on properties of the resource of type A
  def capabilityToAcceptedKeyBuilders: Map[String, AcceptedKeyBuilder[A]]

  def getAcceptedAccessKeys(resource: A): Set[String] = {
    getAcceptedAccessKeysWithBuilders(resource, capabilityToAcceptedKeyBuilders)
  }

  private def getAcceptedAccessKeysWithBuilders[B](
      resource: B,
      builders: Map[String, AcceptedKeyBuilder[B]]): Set[String] = {
    builders.flatMap { case (capability, builder) => builder(capability, resource) }.toSet
  }

  def getAcceptedAccessKeysForCapability(capability: String, resource: A): Set[String] =
    capabilityToAcceptedKeyBuilders.get(capability)
      .map(builder => builder(capability, resource))
      .getOrElse(Set.empty)

  def getAcceptedAccessKeysForCapabilities(capabilities: Set[String], resource: A): Set[String] =
    capabilities.flatMap(capability => getAcceptedAccessKeysForCapability(capability, resource))

  def acceptedKeyBuilder[B]: AcceptedKeyBuilder[B] = AcceptedKeyBuilder((capability, _) => Set(capability))
}

case class AcceptedKeyBuilder[A](keyBuilder: (String, A) => Set[String]) {
  def apply(capability: String, resource: A): Set[String] = keyBuilder(capability, resource)

  /** Adds given section to keys */
  def add(section: A => Any): AcceptedKeyBuilder[A] = {
    AcceptedKeyBuilder((c, r) => keyBuilder(c, r).map(key => s"$key:${section(r)}"))
  }

  /** Adds all of given sections to keys, producing combinations of keys and sections */
  def addList(sections: A => Traversable[String]): AcceptedKeyBuilder[A] = {
    AcceptedKeyBuilder((c, r) => {
      val keys = keyBuilder(c, r)
      sections(r).flatMap(section => keys.map(key => s"$key:$section")).toSet
    })
  }
}