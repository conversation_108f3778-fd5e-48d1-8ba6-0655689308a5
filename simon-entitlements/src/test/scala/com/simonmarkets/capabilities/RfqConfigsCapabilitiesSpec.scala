package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.IssuerPurview
import com.simonmarkets.capabilities.RfqConfigsCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class RfqConfigsCapabilitiesSpec extends WordSpec
  with Matchers
  with StandardCapabilitiesBehavior
  with MockitoSugar
  with BeforeAndAfter
  with CapabilitiesTestData {

  "RfqConfigsCapabilities" should {
    behave like standardCapabilities(RfqConfigsCapabilities)
    "Given a user which has Admin capability RfqConfigsCapabilities.availableAccessKeysGen" should {
      "ViewCapabilities returns one admin key" in {
        val keys = RfqConfigsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapabilities(ViewCapabilities, adminUser)
        keys.size shouldBe 1
        keys should contain("admin")
      }
    }
    "Given a user which does not have Admin capability then RfqConfigsCapabilities.availableAccessKeysGen" should {
      "ViewRfqConfigsViaNetwork capability returns user keys only" in {
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewRfqConfigsViaNetworkCapability.name))
        val keys = RfqConfigsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewRfqConfigsViaNetworkCapability.name, nonAdminUser)
        keys.size shouldBe 1
        keys should contain("viewRfqConfigsViaNetwork:network")
      }
      "ViewRfqConfigsViaNetworkIssuerPurviewCapability capability returns user keys only" in {
        when(nonAdminUser.issuerPurviewIds) thenReturn Set(
          IssuerPurview(purviewNetwork1, Set("issuer1")),
          IssuerPurview(purviewNetwork2, Set("issuer2"))
        )
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewRfqConfigsViaNetworkIssuerPurviewCapability.name))
        val keys = RfqConfigsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewRfqConfigsViaNetworkIssuerPurviewCapability.name, nonAdminUser)
        keys.size shouldBe 2
        keys should contain("viewRfqConfigsViaNetworkIssuerPurview:purviewNetwork1")
        keys should contain("viewRfqConfigsViaNetworkIssuerPurview:purviewNetwork2")
      }
    }
    "Given a user which has Admin capability RfqConfigsCapabilities.availableAccessKeysGen" should {
      "EditCapabilities returns one admin key" in {
        val keys = RfqConfigsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapabilities(EditCapabilities, adminUser)
        keys.size shouldBe 1
        keys should contain("admin")
      }
    }
  }
}
