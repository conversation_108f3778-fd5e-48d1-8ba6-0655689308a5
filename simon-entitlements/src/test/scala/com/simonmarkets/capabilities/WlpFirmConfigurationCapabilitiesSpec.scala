package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.UserRole
import com.simonmarkets.capabilities.WlpFirmConfigurationCapabilities._
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.{reset, when}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}


class WlpFirmConfigurationCapabilitiesSpec
  extends WordSpec
    with Matchers
    with StandardCapabilitiesBehavior
    with MockitoSugar
    with BeforeAndAfter
    with CapabilitiesTestData {

  before {
    reset(nonAdminUser)
    when(nonAdminUser.networkId).thenReturn(networkId)
    when(nonAdminUser.userId).thenReturn("nonAdminUser")
    when(nonAdminUser.capabilities).thenReturn(Set.empty[String])
    when(nonAdminUser.roles).thenReturn(Set.empty[UserRole])
    when(nonAdminUser.userPurviewIds).thenReturn(Set(purviewNetwork1, purviewNetwork2))
    when(nonAdminUser.locations).thenReturn(Set(location))
    when(nonAdminUser.externalIds).thenReturn(Some(Seq(ExternalId("subject", "id"))))
    when(nonAdminUser.whiteLabelPartnerId).thenReturn(Some(whiteLabelPartnerId))
    when(nonAdminUser.firmId).thenReturn(Some(firmId))
  }

  "WlpFirmConfigurationCapabilities" should {
    behave like standardCapabilities(WlpFirmConfigurationCapabilities)

    "ViewWlpFirmConfigurationViaWlp returns wlp keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewWlpFirmConfigurationViaWlp))
      WlpFirmConfigurationCapabilities.getAvailableAccessKeysForCapability(ViewWlpFirmConfigurationViaWlp, nonAdminUser) shouldBe
        Set(s"$ViewWlpFirmConfigurationViaWlp:${nonAdminUser.whiteLabelPartnerId.get}")
    }

    "ViewWlpFirmConfigurationViaWlp returns no keys when no wlp" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewWlpFirmConfigurationViaWlp))
      when(nonAdminUser.whiteLabelPartnerId).thenReturn(None)
      WlpFirmConfigurationCapabilities.getAvailableAccessKeysForCapability(ViewWlpFirmConfigurationViaWlp, nonAdminUser) shouldBe
        Set.empty[String]
    }

    "ViewWlpFirmConfigurationViaWlpAndFirm returns wlp + firm keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewWlpFirmConfigurationViaWlpAndFirm))
      WlpFirmConfigurationCapabilities.getAvailableAccessKeysForCapability(ViewWlpFirmConfigurationViaWlpAndFirm, nonAdminUser) shouldBe
        Set(s"$ViewWlpFirmConfigurationViaWlpAndFirm:${nonAdminUser.whiteLabelPartnerId.get}:${nonAdminUser.firmId.get}")
    }

    "ViewWlpFirmConfigurationViaWlpAndFirm returns no keys when no wlp" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewWlpFirmConfigurationViaWlpAndFirm))
      when(nonAdminUser.whiteLabelPartnerId).thenReturn(None)
      WlpFirmConfigurationCapabilities.getAvailableAccessKeysForCapability(ViewWlpFirmConfigurationViaWlpAndFirm, nonAdminUser) shouldBe
        Set.empty[String]
    }

    "ViewWlpFirmConfigurationViaWlpAndFirm returns no keys when no firm" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewWlpFirmConfigurationViaWlpAndFirm))
      when(nonAdminUser.firmId).thenReturn(None)
      WlpFirmConfigurationCapabilities.getAvailableAccessKeysForCapability(ViewWlpFirmConfigurationViaWlpAndFirm, nonAdminUser) shouldBe
        Set.empty[String]
    }

    "ViewWlpFirmConfigurationViaWlpAndFirm returns no keys when no wlp or firm" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewWlpFirmConfigurationViaWlpAndFirm))
      when(nonAdminUser.whiteLabelPartnerId).thenReturn(None)
      when(nonAdminUser.firmId).thenReturn(None)
      WlpFirmConfigurationCapabilities.getAvailableAccessKeysForCapability(ViewWlpFirmConfigurationViaWlpAndFirm, nonAdminUser) shouldBe
        Set.empty[String]
    }

    "ViewWlpFirmConfigurationViaFirm returns wlp keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewWlpFirmConfigurationViaFirm))
      WlpFirmConfigurationCapabilities.getAvailableAccessKeysForCapability(ViewWlpFirmConfigurationViaFirm, nonAdminUser) shouldBe
        Set(s"$ViewWlpFirmConfigurationViaFirm:${nonAdminUser.firmId.get}")
    }

    "ViewWlpFirmConfigurationViaFirm returns wlp no keys when no firm" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewWlpFirmConfigurationViaFirm))
      when(nonAdminUser.firmId).thenReturn(None)
      WlpFirmConfigurationCapabilities.getAvailableAccessKeysForCapability(ViewWlpFirmConfigurationViaFirm, nonAdminUser) shouldBe
        Set.empty[String]
    }
  }
}
