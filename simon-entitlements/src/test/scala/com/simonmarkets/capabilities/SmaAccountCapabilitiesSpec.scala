package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.{IssuerPurview, PurviewedDomain}
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import simon.Id.NetworkId

class SmaAccountCapabilitiesSpec
  extends WordSpec
    with Matchers
    with StandardCapabilitiesBehavior
    with MockitoSugar
    with BeforeAndAfter
    with CapabilitiesTestData {

  "SmaAccountCapabilities" should {
    import com.simonmarkets.capabilities.SmaAccountsCapabilities._

    behave like standardCapabilities(SmaAccountsCapabilities)

    val issuer1 = "issuer1"
    val issuer2 = "issuer2"
    val issuer3 = "issuer3"

    before {
      when(nonAdminUser.issuerPurviewIds) thenReturn Set(
        IssuerPurview(purviewNetwork1, Set(issuer1, issuer2), purviewedDomainsUpdated = Some(Set(PurviewedDomain.ViewSmaAccounts))),
        IssuerPurview(purviewNetwork2, Set(issuer1, issuer3), purviewedDomainsUpdated = Some(Set(PurviewedDomain.EditSmaAccounts)))
      )
    }

    "For Non Admin Users" should {
      "ViewSmaAccountViaOwner return owner keys" in {
        when(nonAdminUser.userId).thenReturn("nonAdminUser")
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewSmaAccountViaOwner))
        val keys = SmaAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(s"$ViewSmaAccountViaOwner:nonAdminUser")
      }

      "ViewSmaAccountViaFaNumber return fa number keys" in {
        when(nonAdminUser.faNumbers).thenReturn(Set("faNumber1", "faNumber2"))
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewSmaAccountViaFaNumber))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keys = SmaAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 2
        keys shouldBe Set(s"$ViewSmaAccountViaFaNumber:networkId:faNumber1", s"$ViewSmaAccountViaFaNumber:networkId:faNumber2")
      }

      "ViewSmaAccountViaLocation return location keys" in {
        when(nonAdminUser.locations).thenReturn(Set("location1", "location2"))
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewSmaAccountViaLocation))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keys = SmaAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 2
        keys shouldBe Set(s"$ViewSmaAccountViaLocation:networkId:location1", s"$ViewSmaAccountViaLocation:networkId:location2")
      }

      "ViewSmaAccountViaDistributorId return distributorId keys" in {
        when(nonAdminUser.distributorId).thenReturn(Some("distId1"))
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewSmaAccountViaDistributorId))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keys = SmaAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(s"$ViewSmaAccountViaDistributorId:networkId:distId1")

        when(nonAdminUser.distributorId).thenReturn(None)
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewSmaAccountViaDistributorId))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keysNone = SmaAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        keysNone.size shouldBe 0
        keysNone shouldBe Set.empty
      }

      "ViewSmaAccountViaExternalId return externalId keys" in {
        when(nonAdminUser.externalIds).thenReturn(Some(Seq(ExternalId("sub", "id"))))
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewSmaAccountViaExternalId))
        val keys = SmaAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(s"$ViewSmaAccountViaExternalId:sub:id")

        when(nonAdminUser.externalIds).thenReturn(None)
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewSmaAccountViaExternalId))
        val keysNone = SmaAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        keysNone.size shouldBe 0
        keysNone shouldBe Set.empty
      }

      "ViewSmaAccountViaNetwork return network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewSmaAccountViaNetwork))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keys = SmaAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(s"$ViewSmaAccountViaNetwork:networkId")
      }

      "ViewSmaAccountViaPurviewedDomain return network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewSmaAccountViaPurviewedDomain))
        val keys = SmaAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(
          s"$ViewSmaAccountViaPurviewedDomain:$purviewNetwork1",
        )
      }

      "EditSmaAccountViaOwner return owner keys" in {
        when(nonAdminUser.userId).thenReturn("nonAdminUser")
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditSmaAccountViaOwner))
        val keys = SmaAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(s"$EditSmaAccountViaOwner:nonAdminUser")
      }

      "EditSmaAccountViaFaNumber return fa number keys" in {
        when(nonAdminUser.faNumbers).thenReturn(Set("faNumber1", "faNumber2"))
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditSmaAccountViaFaNumber))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keys = SmaAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 2
        keys shouldBe Set(s"$EditSmaAccountViaFaNumber:networkId:faNumber1", s"$EditSmaAccountViaFaNumber:networkId:faNumber2")
      }

      "EditSmaAccountViaLocation return location keys" in {
        when(nonAdminUser.locations).thenReturn(Set("location1", "location2"))
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditSmaAccountViaLocation))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keys = SmaAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 2
        keys shouldBe Set(s"$EditSmaAccountViaLocation:networkId:location1", s"$EditSmaAccountViaLocation:networkId:location2")
      }

      "EditSmaAccountViaDistributorId return distributorId keys" in {
        when(nonAdminUser.distributorId).thenReturn(Some("distId1"))
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditSmaAccountViaDistributorId))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keys = SmaAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(s"$EditSmaAccountViaDistributorId:networkId:distId1")

        when(nonAdminUser.distributorId).thenReturn(None)
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditSmaAccountViaDistributorId))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keysNone = SmaAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        keysNone.size shouldBe 0
        keysNone shouldBe Set.empty
      }

      "EditSmaAccountViaExternalId return externalId keys" in {
        when(nonAdminUser.externalIds).thenReturn(Some(Seq(ExternalId("sub", "id"))))
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditSmaAccountViaExternalId))
        val keys = SmaAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(s"$EditSmaAccountViaExternalId:sub:id")

        when(nonAdminUser.externalIds).thenReturn(None)
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditSmaAccountViaExternalId))
        val keysNone = SmaAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        keysNone.size shouldBe 0
        keysNone shouldBe Set.empty
      }

      "EditSmaAccountViaNetwork return network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditSmaAccountViaNetwork))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keys = SmaAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(s"$EditSmaAccountViaNetwork:networkId")
      }

      "EditSmaAccountViaPurviewedDomain return network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditSmaAccountViaPurviewedDomain))
        val keys = SmaAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(
          s"$EditSmaAccountViaPurviewedDomain:$purviewNetwork2",
        )
      }
    }

    "For Admin Users" in {
      val availableKeys = SmaAccountsCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "have 15 capabilities" in {
      SmaAccountsCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 15
    }

    "have 8 view capabilities" in {
      SmaAccountsCapabilities.ViewCapabilities shouldBe Set(Admin, ViewSmaAccountViaOwner, ViewSmaAccountViaFaNumber, ViewSmaAccountViaLocation, ViewSmaAccountViaDistributorId, ViewSmaAccountViaExternalId, ViewSmaAccountViaNetwork, ViewSmaAccountViaPurviewedDomain)
    }

    "have 7 edit capabilities" in {
      SmaAccountsCapabilities.EditCapabilities shouldBe Set(Admin, EditSmaAccountViaOwner, EditSmaAccountViaFaNumber, EditSmaAccountViaLocation, EditSmaAccountViaDistributorId, EditSmaAccountViaExternalId, EditSmaAccountViaNetwork, EditSmaAccountViaPurviewedDomain)
    }
  }

}
