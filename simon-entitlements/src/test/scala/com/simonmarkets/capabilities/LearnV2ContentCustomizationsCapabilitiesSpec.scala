package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.LearnV2ContentCustomizationsCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar

class LearnV2ContentCustomizationsCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {
  private val locations = Seq("location1", "location2")
  private val icnRoles = Seq("role1", "role2")
  private val faNumbers = Seq("123", "456")

  before {
    when(nonAdminUser.locations).thenReturn(locations.toSet)
    when(nonAdminUser.icnRoles).thenReturn(Some(icnRoles.toSet))
    when(nonAdminUser.faNumbers).thenReturn(faNumbers.toSet)
  }

  "LearnV2ContentCustomizationCapabilities" should {
    "have 20 capabilities" in {
      LearnV2ContentCustomizationsCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 20
    }

    "have 14 view capabilities + 1 admin" in {
      LearnV2ContentCustomizationsCapabilities.ViewCapabilities.size shouldBe 15
    }

    "have 4 edit capabilities + 2 admin" in {
      LearnV2ContentCustomizationsCapabilities.EditCapabilities.size shouldBe 6
    }

    "For Admin user should return admin key" in {
      val availableKeys = LearnV2ContentCustomizationsCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "For Non Admin Users" should {

      "ViewContentCustomizationViaNetworkCapability returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewContentCustomizationViaNetworkCapability.name))
        val availableKeys = LearnV2ContentCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${ViewContentCustomizationViaNetworkCapability.name}:$networkId")
      }

      "ViewContentCustomizationViaNetworkAndLocationCapability returns network keys with locations" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewContentCustomizationViaNetworkAndLocationCapability.name))
        val availableKeys = LearnV2ContentCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(s"${ViewContentCustomizationViaNetworkAndLocationCapability.name}:$networkId:${locations.head}",
          s"${ViewContentCustomizationViaNetworkAndLocationCapability.name}:$networkId:${locations(1)}")
      }

      "ViewContentCustomizationViaWlpCapability returns wlp keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewContentCustomizationViaWlpCapability.name))
        val availableKeys = LearnV2ContentCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${ViewContentCustomizationViaWlpCapability.name}:$whiteLabelPartnerId")
      }

      "ViewContentCustomizationViaWlpAndFirmCapability returns wlp and firm keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewContentCustomizationViaWlpAndFirmCapability.name))
        val availableKeys = LearnV2ContentCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${ViewContentCustomizationViaWlpAndFirmCapability.name}:$whiteLabelPartnerId:$firmId")
      }

      "ViewApprovedContentCustomizationViaUserIdCapability returns user keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedContentCustomizationViaUserIdCapability.name))
        val availableKeys = LearnV2ContentCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${ViewApprovedContentCustomizationViaUserIdCapability.name}:${nonAdminUser.userId}")
      }

      "ViewApprovedContentCustomizationViaNetworkCapability returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedContentCustomizationViaNetworkCapability.name))
        val availableKeys = LearnV2ContentCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${ViewApprovedContentCustomizationViaNetworkCapability.name}:$networkId")
      }

      "ViewApprovedContentCustomizationViaWlpCapability returns wlp keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedContentCustomizationViaWlpCapability.name))
        val availableKeys = LearnV2ContentCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${ViewApprovedContentCustomizationViaWlpCapability.name}:$whiteLabelPartnerId")
      }

      "ViewApprovedContentCustomizationViaWlpAndFirmCapability returns wlp and firm keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedContentCustomizationViaWlpAndFirmCapability.name))
        val availableKeys = LearnV2ContentCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${ViewApprovedContentCustomizationViaWlpAndFirmCapability.name}:$whiteLabelPartnerId:$firmId")
      }

      "ViewApprovedContentCustomizationViaNetworkAndLocationCapability returns network keys with locations" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedContentCustomizationViaNetworkAndLocationCapability.name))
        val availableKeys = LearnV2ContentCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(s"${ViewApprovedContentCustomizationViaNetworkAndLocationCapability.name}:$networkId:${locations.head}",
          s"${ViewApprovedContentCustomizationViaNetworkAndLocationCapability.name}:$networkId:${locations(1)}")
      }

      "ViewApprovedContentCustomizationViaNetworkAndFaNumberCapability returns network keys with fa numbers" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedContentCustomizationViaNetworkAndFaNumberCapability.name))
        val availableKeys = LearnV2ContentCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(s"${ViewApprovedContentCustomizationViaNetworkAndFaNumberCapability.name}:$networkId:${faNumbers.head}",
          s"${ViewApprovedContentCustomizationViaNetworkAndFaNumberCapability.name}:$networkId:${faNumbers(1)}")
      }

      "ViewApprovedContentCustomizationViaWlpAndRoleCapability returns ICN roles keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedContentCustomizationViaWlpAndRoleCapability.name))
        val availableKeys = LearnV2ContentCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(s"${ViewApprovedContentCustomizationViaWlpAndRoleCapability.name}:$whiteLabelPartnerId:${icnRoles.head}",
          s"${ViewApprovedContentCustomizationViaWlpAndRoleCapability.name}:$whiteLabelPartnerId:${icnRoles(1)}")
      }

      "ViewApprovedContentCustomizationViaWlpAndFirmAndRoleCapability returns ICN roles keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedContentCustomizationViaWlpAndFirmAndRoleCapability.name))
        val availableKeys = LearnV2ContentCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(s"${ViewApprovedContentCustomizationViaWlpAndFirmAndRoleCapability.name}:$whiteLabelPartnerId:$firmId:${icnRoles.head}",
          s"${ViewApprovedContentCustomizationViaWlpAndFirmAndRoleCapability.name}:$whiteLabelPartnerId:$firmId:${icnRoles(1)}")
      }

      "ViewApprovedContentCustomizationViaWlpAndFirmAndFaNumberCapability returns location keys with fa numbers" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedContentCustomizationViaWlpAndFirmAndFaNumberCapability.name))
        val availableKeys = LearnV2ContentCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(s"${ViewApprovedContentCustomizationViaWlpAndFirmAndFaNumberCapability.name}:$whiteLabelPartnerId:$firmId:${faNumbers.head}",
          s"${ViewApprovedContentCustomizationViaWlpAndFirmAndFaNumberCapability.name}:$whiteLabelPartnerId:$firmId:${faNumbers(1)}")
      }

      "EditContentCustomizationViaNetworkCapability returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditContentCustomizationViaNetworkCapability.name))
        val availableKeys = LearnV2ContentCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${EditContentCustomizationViaNetworkCapability.name}:$networkId")
      }

      "EditContentCustomizationViaNetworkCapability returns network keys with locations" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditContentCustomizationViaNetworkAndLocationCapability.name))
        val availableKeys = LearnV2ContentCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(s"${EditContentCustomizationViaNetworkAndLocationCapability.name}:$networkId:${locations.head}",
          s"${EditContentCustomizationViaNetworkAndLocationCapability.name}:$networkId:${locations(1)}")
      }

      "EditContentCustomizationViaWlpCapability returns wlp keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditContentCustomizationViaWlpCapability.name))
        val availableKeys = LearnV2ContentCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${EditContentCustomizationViaWlpCapability.name}:$whiteLabelPartnerId")
      }

      "EditContentCustomizationViaWlpAndFirmCapability returns wlp and firm keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditContentCustomizationViaWlpAndFirmCapability.name))
        val availableKeys = LearnV2ContentCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${EditContentCustomizationViaWlpAndFirmCapability.name}:$whiteLabelPartnerId:$firmId")
      }
    }
  }
}
