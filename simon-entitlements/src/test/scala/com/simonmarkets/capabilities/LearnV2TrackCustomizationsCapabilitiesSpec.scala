package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.LearnV2TrackCustomizationsCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar

class LearnV2TrackCustomizationsCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {

  private val locations = Seq("location1", "location2")
  private val icnRoles = Seq("role1", "role2")
  private val faNumbers = Seq("123", "456")

  before {
    when(nonAdminUser.locations).thenReturn(locations.toSet)
    when(nonAdminUser.icnRoles).thenReturn(Some(icnRoles.toSet))
    when(nonAdminUser.faNumbers).thenReturn(faNumbers.toSet)
  }

  "LearnV2TrackCustomizationCapabilities" should {
    "have 20 capabilities" in {
      LearnV2TrackCustomizationsCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 20
    }

    "have 14 view capabilities + 1 admin" in {
      LearnV2TrackCustomizationsCapabilities.ViewCapabilities.size shouldBe 15
    }

    "have 4 edit capabilities + 2 admin" in {
      LearnV2TrackCustomizationsCapabilities.EditCapabilities.size shouldBe 6
    }

    "For Admin user should return admin key" in {
      val availableKeys = LearnV2TrackCustomizationsCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "For Non Admin Users" should {

      "ViewTrackCustomizationViaNetworkCapability returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewTrackCustomizationViaNetworkCapability.name))
        val availableKeys = LearnV2TrackCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${ViewTrackCustomizationViaNetworkCapability.name}:$networkId")
      }

      "ViewTrackCustomizationViaNetworkAndLocationCapability returns network keys with locations" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewTrackCustomizationViaNetworkAndLocationCapability.name))
        val availableKeys = LearnV2TrackCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(s"${ViewTrackCustomizationViaNetworkAndLocationCapability.name}:$networkId:${locations.head}",
          s"${ViewTrackCustomizationViaNetworkAndLocationCapability.name}:$networkId:${locations(1)}")
      }

      "ViewTrackCustomizationViaWlpCapability returns wlp keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewTrackCustomizationViaWlpCapability.name))
        val availableKeys = LearnV2TrackCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${ViewTrackCustomizationViaWlpCapability.name}:$whiteLabelPartnerId")
      }

      "ViewTrackCustomizationViaWlpAndFirmCapability returns wlp and firm keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewTrackCustomizationViaWlpAndFirmCapability.name))
        val availableKeys = LearnV2TrackCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${ViewTrackCustomizationViaWlpAndFirmCapability.name}:$whiteLabelPartnerId:$firmId")
      }

      "ViewApprovedTrackCustomizationViaUserIdCapability returns user keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedTrackCustomizationViaUserIdCapability.name))
        val availableKeys = LearnV2TrackCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${ViewApprovedTrackCustomizationViaUserIdCapability.name}:${nonAdminUser.userId}")
      }

      "ViewApprovedTrackCustomizationViaNetworkCapability returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedTrackCustomizationViaNetworkCapability.name))
        val availableKeys = LearnV2TrackCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${ViewApprovedTrackCustomizationViaNetworkCapability.name}:$networkId")
      }

      "ViewApprovedTrackCustomizationViaWlpCapability returns wlp keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedTrackCustomizationViaWlpCapability.name))
        val availableKeys = LearnV2TrackCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${ViewApprovedTrackCustomizationViaWlpCapability.name}:$whiteLabelPartnerId")
      }

      "ViewApprovedTrackCustomizationViaWlpAndFirmCapability returns wlp and firm keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedTrackCustomizationViaWlpAndFirmCapability.name))
        val availableKeys = LearnV2TrackCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${ViewApprovedTrackCustomizationViaWlpAndFirmCapability.name}:$whiteLabelPartnerId:$firmId")
      }

      "ViewApprovedTrackCustomizationViaNetworkAndLocationCapability returns network keys with locations" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedTrackCustomizationViaNetworkAndLocationCapability.name))
        val availableKeys = LearnV2TrackCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(s"${ViewApprovedTrackCustomizationViaNetworkAndLocationCapability.name}:$networkId:${locations.head}",
          s"${ViewApprovedTrackCustomizationViaNetworkAndLocationCapability.name}:$networkId:${locations(1)}")
      }

      "ViewApprovedTrackCustomizationViaNetworkAndFaNumberCapability returns network keys with fa numbers" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedTrackCustomizationViaNetworkAndFaNumberCapability.name))
        val availableKeys = LearnV2TrackCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(s"${ViewApprovedTrackCustomizationViaNetworkAndFaNumberCapability.name}:$networkId:${faNumbers.head}",
          s"${ViewApprovedTrackCustomizationViaNetworkAndFaNumberCapability.name}:$networkId:${faNumbers(1)}")
      }

      "ViewApprovedTrackCustomizationViaWlpAndRoleCapability returns ICN Roles keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedTrackCustomizationViaWlpAndRoleCapability.name))
        val availableKeys = LearnV2TrackCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(s"${ViewApprovedTrackCustomizationViaWlpAndRoleCapability.name}:$whiteLabelPartnerId:${icnRoles.head}",
          s"${ViewApprovedTrackCustomizationViaWlpAndRoleCapability.name}:$whiteLabelPartnerId:${icnRoles(1)}")
      }

      "ViewApprovedTrackCustomizationViaWlpAndFirmAndRoleCapability returns ICN Roles keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedTrackCustomizationViaWlpAndFirmAndRoleCapability.name))
        val availableKeys = LearnV2TrackCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(s"${ViewApprovedTrackCustomizationViaWlpAndFirmAndRoleCapability.name}:$whiteLabelPartnerId:$firmId:${icnRoles.head}",
          s"${ViewApprovedTrackCustomizationViaWlpAndFirmAndRoleCapability.name}:$whiteLabelPartnerId:$firmId:${icnRoles(1)}")
      }

      "ViewApprovedTrackCustomizationViaWlpAndFirmAndFaNumberCapability returns location keys with fa numbers" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedTrackCustomizationViaWlpAndFirmAndFaNumberCapability.name))
        val availableKeys = LearnV2TrackCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(s"${ViewApprovedTrackCustomizationViaWlpAndFirmAndFaNumberCapability.name}:$whiteLabelPartnerId:$firmId:${faNumbers.head}",
          s"${ViewApprovedTrackCustomizationViaWlpAndFirmAndFaNumberCapability.name}:$whiteLabelPartnerId:$firmId:${faNumbers(1)}")
      }

      "EditTrackCustomizationViaNetworkCapability returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditTrackCustomizationViaNetworkCapability.name))
        val availableKeys = LearnV2TrackCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${EditTrackCustomizationViaNetworkCapability.name}:$networkId")
      }

      "EditCustomizationViaNetworkAndLocationCapability returns network keys with locations" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditTrackCustomizationViaNetworkAndLocationCapability.name))
        val availableKeys = LearnV2TrackCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(s"${EditTrackCustomizationViaNetworkAndLocationCapability.name}:$networkId:${locations.head}",
          s"${EditTrackCustomizationViaNetworkAndLocationCapability.name}:$networkId:${locations(1)}")
      }

      "EditTrackCustomizationViaWlpCapability returns wlp keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditTrackCustomizationViaWlpCapability.name))
        val availableKeys = LearnV2TrackCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${EditTrackCustomizationViaWlpCapability.name}:$whiteLabelPartnerId")
      }

      "EditTrackCustomizationViaWlpAndFirmCapability returns wlp and firm keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditTrackCustomizationViaWlpAndFirmCapability.name))
        val availableKeys = LearnV2TrackCustomizationsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"${EditTrackCustomizationViaWlpAndFirmCapability.name}:$whiteLabelPartnerId:$firmId")
      }
    }
  }
}