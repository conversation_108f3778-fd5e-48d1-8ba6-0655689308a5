package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.OrderReconDefaultMappingsCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar

class OrderReconDefaultMappingsCapabilitiesSpec extends WordSpec
  with Matchers
  with StandardCapabilitiesBehavior
  with MockitoSugar
  with BeforeAndAfter
  with CapabilitiesTestData {
  "OrderReconDefaultMappingsCapabilities" can {
    "For Non Admin Users" should {
      "return network keys for ViewDefaultMappingsViaNetwork" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewDefaultMappingsViaNetwork))
        val availableKeys = OrderReconDefaultMappingsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewDefaultMappingsViaNetwork:network")
      }

      "return network keys for EditDefaultMappingsViaNetwork" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditDefaultMappingsViaNetwork))
        val availableKeys = OrderReconDefaultMappingsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$EditDefaultMappingsViaNetwork:network")
      }
    }

    "For Admin user" should {
      "return admin key" in {
        val availableKeys = OrderReconDefaultMappingsCapabilities.getAvailableAccessKeys(adminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(Admin)
      }
    }
    "have 5 capabilities" in {
      OrderReconDefaultMappingsCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 5
    }
  }
}
