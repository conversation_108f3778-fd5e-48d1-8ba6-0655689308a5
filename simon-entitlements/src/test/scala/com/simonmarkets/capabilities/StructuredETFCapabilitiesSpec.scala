package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.Network.Action
import com.goldmansachs.marquee.pipg.UserACL
import org.mockito.Mockito.{reset, when}
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar
import com.simonmarkets.capabilities.StructuredETFCapabilities._

class StructuredETFCapabilitiesSpec extends WordSpec with Matchers with StandardCapabilitiesBehavior with MockitoSugar with BeforeAndAfter {
  private val nonAdminUser = mock[UserACL]
  private val nonAdminUserWithoutV2PayoffEntitlements = mock[UserACL]

  val payoffEntitlementsV2 = Map(
    "TestIssuer1" -> Map(
      STRUCTURED_ETF_PAYOFF_TYPE -> Set(Action("view")),
      "TestPayoff2" -> Set(Action("trade")),
      "TestPayoff3" -> Set(Action("view"), Action("trade"))
    ),
    "TestIssuer2" -> Map(
      "TestPayoff1" -> Set(Action("view")),
      "TestPayoff2" -> Set(Action("trade"), Action("edit")),
      STRUCTURED_ETF_PAYOFF_TYPE -> Set(Action("view"), Action("trade"), Action("edit"))
    )
  )

  val payoffEntitlements = Map(
    "TestIssuer3" -> Map(
      STRUCTURED_ETF_PAYOFF_TYPE -> List("view"),
      "TestPayoff2" -> List("trade"),
      "TestPayoff3" -> List("view", "trade")
    ),
    "TestIssuer4" -> Map(
      "TestPayoff1" -> List("view"),
      "TestPayoff2" -> List("trade", "edit"),
      STRUCTURED_ETF_PAYOFF_TYPE -> List("view", "trade", "edit")
    )
  )

  before {
    reset(nonAdminUser)
    when(nonAdminUser.payoffEntitlementsV2).thenReturn(payoffEntitlementsV2)
    when(nonAdminUserWithoutV2PayoffEntitlements.payoffEntitlementsV2).thenReturn(Map.empty[String, Map[String, Set[Action]]])
    when(nonAdminUserWithoutV2PayoffEntitlements.payoffEntitlements).thenReturn(payoffEntitlements)
  }

  "StructuredETFCapabilities" should {
    behave like standardCapabilities(StructuredETFCapabilities)

    "ViewStructuredETFViaViewPayoffEntitlement returns payoffEntitlement keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewStructuredETFViaViewPayoffEntitlement))
      StructuredETFCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewStructuredETFViaViewPayoffEntitlement, nonAdminUser) shouldBe
        Set(
          s"$ViewStructuredETFViaViewPayoffEntitlement:TestIssuer1:$STRUCTURED_ETF_PAYOFF_TYPE",
          s"$ViewStructuredETFViaViewPayoffEntitlement:TestIssuer2:$STRUCTURED_ETF_PAYOFF_TYPE",
        )
    }

    "EditStructuredETFViaViewPayoffEntitlement returns payoffEntitlement keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(EditStructuredETFViaEditPayoffEntitlement))
      StructuredETFCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(EditStructuredETFViaEditPayoffEntitlement, nonAdminUser) shouldBe
        Set(s"$EditStructuredETFViaEditPayoffEntitlement:TestIssuer2:$STRUCTURED_ETF_PAYOFF_TYPE")
    }

    "ViewStructuredETFViaViewPayoffEntitlement returns payoffEntitlement keys using legacy payoff entitlements when v2 is not present" in {
      when(nonAdminUserWithoutV2PayoffEntitlements.capabilities).thenReturn(Set(ViewStructuredETFViaViewPayoffEntitlement))
      StructuredETFCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewStructuredETFViaViewPayoffEntitlement, nonAdminUserWithoutV2PayoffEntitlements) shouldBe
        Set(
          s"$ViewStructuredETFViaViewPayoffEntitlement:TestIssuer3:$STRUCTURED_ETF_PAYOFF_TYPE",
          s"$ViewStructuredETFViaViewPayoffEntitlement:TestIssuer4:$STRUCTURED_ETF_PAYOFF_TYPE",
        )
    }

    "EditStructuredETFViaViewPayoffEntitlement returns payoffEntitlement keys keys using legacy payoff entitlements when v2 is not present" in {
      when(nonAdminUserWithoutV2PayoffEntitlements.capabilities).thenReturn(Set(EditStructuredETFViaEditPayoffEntitlement))
      StructuredETFCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(EditStructuredETFViaEditPayoffEntitlement, nonAdminUserWithoutV2PayoffEntitlements) shouldBe
        Set(s"$EditStructuredETFViaEditPayoffEntitlement:TestIssuer4:$STRUCTURED_ETF_PAYOFF_TYPE")
    }
  }
}
