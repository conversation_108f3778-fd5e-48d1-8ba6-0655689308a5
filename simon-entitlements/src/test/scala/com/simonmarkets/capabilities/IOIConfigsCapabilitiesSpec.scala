package com.simonmarkets.capabilities

import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar

class IOIConfigsCapabilitiesSpec extends WordSpec
  with Matchers
  with StandardCapabilitiesBehavior
  with MockitoSugar
  with BeforeAndAfter
  with CapabilitiesTestData {

  "IOIConfigsCapabilities" should {
    behave like standardCapabilities(IOIConfigsCapabilities)

    "Given a user which has Admin capability, IOIConfigsCapabilities" should {
      "ViewCapabilities returns one admin key" in {
        val keys = IOIConfigsCapabilities.getAvailableAccessKeysForCapabilities(IOIConfigsCapabilities.ViewCapabilities, adminUser)
        keys.size shouldBe 1
        keys should contain("admin")
      }
      "EditCapabilities returns one admin key" in {
        val keys = IOIConfigsCapabilities.getAvailableAccessKeysForCapabilities(IOIConfigsCapabilities.EditCapabilities, adminUser)
        keys.size shouldBe 1
        keys should contain("admin")
      }
    }

    "Given a user which does not have Admin capability, then IOIConfigsCapabilities" should {
      when(nonAdminUser.capabilities).thenReturn(Set[String](IOIConfigsCapabilities.ViewIOIConfigViaNetworkCapability.name, IOIConfigsCapabilities.ViewIOIConfigViaPurviewCapability.name))

      "ViewCapabilities returns user keys" in {
        val keys = IOIConfigsCapabilities.getAvailableAccessKeysForCapabilities(IOIConfigsCapabilities.ViewCapabilities, nonAdminUser)
        keys.size shouldBe 3
        keys should contain("viewIOIConfigViaNetwork:network")
        keys should contain("viewIOIConfigViaPurview:purviewNetwork1")
        keys should contain("viewIOIConfigViaPurview:purviewNetwork2")
      }
      "EditCapabilities return nothing" in {
        val keys = IOIConfigsCapabilities.getAvailableAccessKeysForCapabilities(IOIConfigsCapabilities.EditCapabilities, nonAdminUser)
        keys.size shouldBe 0
      }
    }
  }
}
