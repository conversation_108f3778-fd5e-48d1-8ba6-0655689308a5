package com.simonmarkets.capabilities

import com.simonmarkets.entitlements.AvailableAccessKeysGenerator
import org.scalatest.{Matchers, WordSpec}

/**
 * Defines tests for a [[AvailableAccessKeysGenerator]] that should not generate available access keys that are
 * available to an admin-only user
 */
trait NonAdminAvailableAccessKeysGeneratorBehavior extends Matchers with CapabilitiesUsers {
  this: WordSpec =>

  def capabilities: Capabilities

  def nonAdminAccessKeysGenerator(availableAccessKeysGenerator: AvailableAccessKeysGenerator): Unit = {

    "generate no access keys for admin-only user" in {
      availableAccessKeysGenerator.getAvailableAccessKeys(adminOnlyUser) shouldBe Set.empty
    }

    "generate no access keys for set of capabilities" in {
      availableAccessKeysGenerator.getAvailableAccessKeysForCapabilities(capabilities.toSet, adminOnlyUser) shouldBe Set.empty
    }

  }
}
