package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.entitlements.{AcceptedAccessKeysGenerator, AvailableAccessKeysGenerator}
import org.scalatest.{Matchers, WordSpec}

/**
 * Defines tests for a [[AcceptedAccessKeysGenerator]] that should not generate accepted access keys for the Admin
 * capability
 */
trait NonAdminAcceptedAccessKeysGeneratorBehavior extends Matchers with CapabilitiesUsers {
  this: WordSpec =>

  def capabilities: Capabilities

  def nonAdminAcceptedKeysGenerator[A](acceptedAccessKeysGenerator: AcceptedAccessKeysGenerator[A], testResources: Seq[A]): Unit = {

    "generate no accepted keys for admin capability" in {
      acceptedAccessKeysGenerator.capabilityToAcceptedKeyBuilders.get(Admin) shouldBe None
    }

    "not generate admin access keys for any resource" in {
      testResources.foreach(acceptedAccessKeysGenerator.getAcceptedAccessKeys(_) should not contain Admin)
    }

  }
}
