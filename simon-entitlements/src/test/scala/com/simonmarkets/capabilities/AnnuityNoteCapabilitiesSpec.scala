package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.AnnuityNoteCapabilities._
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class AnnuityNoteCapabilitiesSpec extends WordSpec
  with Matchers
  with StandardCapabilitiesBehavior
  with MockitoSugar
  with BeforeAndAfter
  with CapabilitiesTestData {

  "AnnuityNoteCapabilities" when {

    behave like standardCapabilities(AnnuityNoteCapabilities)

    "is admin" should {

      "return admin key" in {
        val keys = AnnuityNoteCapabilities.getAvailableAccessKeys(adminUser)
        keys should contain only Admin
      }

    }

    "is not admin" should {

      "return userId for ViewAnnuityNoteViaUserCreated" in {
        when(nonAdminUser.capabilities) thenReturn Set(ViewAnnuityNoteViaUserCreated)
        val keys = AnnuityNoteCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys should contain only s"viewAnnuityNoteViaUserCreated:${nonAdminUser.userId}"
      }

      "return networkId for ViewAnnuityNoteViaNetwork" in {
        when(nonAdminUser.capabilities) thenReturn Set(ViewAnnuityNoteViaNetwork)
        val keys = AnnuityNoteCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys should contain only s"viewAnnuityNoteViaNetwork:${nonAdminUser.networkId}"
      }

      "return userId for EditAnnuityNoteViaUserCreated" in {
        when(nonAdminUser.capabilities) thenReturn Set(EditAnnuityNoteViaUserCreated)
        val keys = AnnuityNoteCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys should contain only s"editAnnuityNoteViaUserCreated:${nonAdminUser.userId}"
      }
    }

  }

}

