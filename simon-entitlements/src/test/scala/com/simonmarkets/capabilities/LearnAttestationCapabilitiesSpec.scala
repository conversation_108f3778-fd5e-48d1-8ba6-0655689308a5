package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.LearnAttestationCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class LearnAttestationCapabilitiesSpec
  extends WordSpec
    with Matchers
    with StandardCapabilitiesBehavior
    with MockitoSugar
    with BeforeAndAfter
    with CapabilitiesTestData {

  "LearnAttestationCapabilities" should {
    "For Non Admin Users" should {
      "ViewAttestationActivityViaPurview return purview keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewAttestationActivityViaPurview))
        val availableKeys = LearnAttestationCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(s"$ViewAttestationActivityViaPurview:$purviewNetwork1",
          s"$ViewAttestationActivityViaPurview:$purviewNetwork2")
      }

      "ViewAttestationActivityViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewAttestationActivityViaNetwork))
        val availableKeys = LearnAttestationCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewAttestationActivityViaNetwork:$networkId")
      }

      "ViewAttestationActivityViaNetwork returns location keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewAttestationActivityViaLocation))
        val availableKeys = LearnAttestationCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewAttestationActivityViaLocation:$networkId:$location")
      }

      "ViewAttestationActivityViaOwner returns guid keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewAttestationActivityViaOwner))
        val availableKeys = LearnAttestationCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewAttestationActivityViaOwner:nonAdminUser")
      }

      "EditAttestationActivityViaOwner returns guid keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditAttestationActivityViaOwner))
        val availableKeys = LearnAttestationCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$EditAttestationActivityViaOwner:nonAdminUser")
      }
    }
    "For Admin user should return admin key" in {
      val availableKeys = LearnAttestationCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "have 8 capabilities" in {
      LearnAttestationCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 8
    }

    "have 6 viewActivity capabilities" in {
      LearnAttestationCapabilities.ViewActivityCapabilities shouldBe Set(Admin, ViewAttestationActivityViaOwner,
        ViewAttestationActivityViaLocation, ViewAttestationActivityViaPurview, ViewAttestationActivityViaNetwork, ViewAttestationActivityViaNetworkTypeCapability.name)
    }

    "have 3 edit activity capability" in {
      LearnAttestationCapabilities.EditActivityCapabilities shouldBe Set(Admin, EditAttestationActivityViaOwner, EditAttestationActivityViaNetworkTypeCapability.name)
    }
  }

}
