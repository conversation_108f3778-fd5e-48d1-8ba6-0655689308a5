package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.RfqCrowdsourcingCapabilities._
import com.simonmarkets.util.CapabilitiesTestData

import org.mockito.Mockito.when
import org.scalatest.{Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar


class RfqCrowdsourcingCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with CapabilitiesTestData{

  "RfqCrowdsourcingCapabilities" should {
    "For Admin users should return admin key" in {
      val availableKeys = RfqCrowdsourcingCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "ViewRfqCrowdsourcingViaOwner returns guid keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewRfqCrowdsourcingViaOwner))
      val availableKeys = RfqCrowdsourcingCapabilities.getAvailableAccessKeys(nonAdminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(s"$ViewRfqCrowdsourcingViaOwner:${nonAdminUser.userId}")
    }

    "ViewRfqCrowdsourcingViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewRfqCrowdsourcingViaNetwork))
      val availableKeys = RfqCrowdsourcingCapabilities.getAvailableAccessKeys(nonAdminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(s"$ViewRfqCrowdsourcingViaNetwork:$networkId")
    }

    "EditRfqCrowdsourcingViaOwner returns guid keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(EditRfqCrowdsourcingViaOwner))
      val availableKeys = RfqCrowdsourcingCapabilities.getAvailableAccessKeys(nonAdminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(s"$EditRfqCrowdsourcingViaOwner:${nonAdminUser.userId}")
    }

    "EditRfqCrowdsourcingViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(EditRfqCrowdsourcingViaNetwork))
      val availableKeys = RfqCrowdsourcingCapabilities.getAvailableAccessKeys(nonAdminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(s"$EditRfqCrowdsourcingViaNetwork:$networkId")
    }

    "have 5 capabilities" in {
      RfqCrowdsourcingCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 5
    }
  }
}
