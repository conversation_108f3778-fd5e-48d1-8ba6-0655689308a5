package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.DocumentsCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import simon.Id.NetworkId

class DocumentsCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {
  val networkId2 = NetworkId("networkId2")

  when(nonAdminUser.userPurviewIds).thenReturn(Set(networkId2))

  "Given non-admin user with ViewInvestmentDocument capability, Document Available Key Generation should return correct access keys" in {
    when(nonAdminUser.capabilities).thenReturn(Set(ViewInvestmentDocument))
    DocumentAvailableAccessKeys.getAvailableAccessKeysForCapabilities(Set(ViewInvestmentDocument), nonAdminUser) shouldBe Set(ViewInvestmentDocument)
  }

  "Given non-admin user with EditInvestmentDocument capability, Document Available Key Generation should return correct access keys" in {
    when(nonAdminUser.capabilities).thenReturn(Set(EditInvestmentDocument))
    DocumentAvailableAccessKeys.getAvailableAccessKeysForCapabilities(Set(EditInvestmentDocument), nonAdminUser) shouldBe Set(EditInvestmentDocument)
  }

  "Given non-admin user with ViewNetworkDocumentViaNetwork capability, Document Available Key Generation should return correct access keys" in {
    when(nonAdminUser.capabilities).thenReturn(Set(ViewNetworkDocumentViaNetwork))
    DocumentAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewNetworkDocumentViaNetwork, nonAdminUser) shouldBe Set(s"$ViewNetworkDocumentViaNetwork:$networkId")
  }

  "Given non-admin user with ViewNetworkDocumentViaPurview capability, Document Available Key Generation should return correct access keys" in {
    when(nonAdminUser.capabilities).thenReturn(Set(ViewNetworkDocumentViaPurview))
    DocumentAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewNetworkDocumentViaPurview, nonAdminUser) shouldBe Set(s"$ViewNetworkDocumentViaPurview:$networkId2")
  }

  "Given non-admin user with EditNetworkDocumentViaNetwork capability, Document Available Key Generation should return correct access keys" in {
    when(nonAdminUser.capabilities).thenReturn(Set(EditNetworkDocumentViaNetwork))
    DocumentAvailableAccessKeys.getAvailableAccessKeysForCapability(EditNetworkDocumentViaNetwork, nonAdminUser) shouldBe Set(s"$EditNetworkDocumentViaNetwork:$networkId")
  }

  "Given non-admin user with EditNetworkDocumentViaPurview capability, Document Available Key Generation should return correct access keys" in {
    when(nonAdminUser.capabilities).thenReturn(Set(EditNetworkDocumentViaPurview))
    DocumentAvailableAccessKeys.getAvailableAccessKeysForCapability(EditNetworkDocumentViaPurview, nonAdminUser) shouldBe Set(s"$EditNetworkDocumentViaPurview:$networkId2")
  }

  "Given user with admin capability, View Document Available Key Generation should return " in {
    DocumentAvailableAccessKeys.getAvailableAccessKeysForCapabilities(ViewInvestmentDocumentCapabilities, adminUser) shouldBe Set(Admin)
  }

  "Given user with admin capability, Edit Document Available Key Generation should return " in {
    DocumentAvailableAccessKeys.getAvailableAccessKeysForCapabilities(EditInvestmentDocumentCapabilities, adminUser) shouldBe Set(Admin)
  }

  "Given user with admin capability, View Network Available Key Generation should return " in {
    DocumentAvailableAccessKeys.getAvailableAccessKeysForCapabilities(ViewNetworkDocumentCapabilities, adminUser) shouldBe Set(Admin)
  }

  "Given user with admin capability, Edit Network Available Key Generation should return " in {
    DocumentAvailableAccessKeys.getAvailableAccessKeysForCapabilities(EditNetworkDocumentCapabilities, adminUser) shouldBe Set(Admin)
  }
}