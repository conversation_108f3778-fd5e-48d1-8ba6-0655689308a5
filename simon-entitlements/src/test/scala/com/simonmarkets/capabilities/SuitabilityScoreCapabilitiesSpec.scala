package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import org.scalatest.{Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar


class SuitabilityScoreCapabilitiesSpec
  extends WordSpec
    with MockitoSugar
    with Matchers {

  "ViewCapabilities" should {
    "be ViewSuitabilityScoreViaNetwork and Admin" in {
      SuitabilityScoreCapabilities.ViewCapabilities shouldBe Set(
        Admin,
        "ViewSuitabilityScoreViaNetwork"
      )
    }
  }
  "EditCapabilities" should {
    "be EditOverrideSuitabilityScoreViaNetwork and Admin" in {
      SuitabilityScoreCapabilities.EditCapabilities shouldBe Set(
        Admin,
        "EditOverrideSuitabilityScoreViaNetwork"
      )
    }
  }
  "toSet" should {
    "be view and edit" in {
      SuitabilityScoreCapabilities.toSet shouldBe SuitabilityScoreCapabilities.EditCapabilities ++ SuitabilityScoreCapabilities.ViewCapabilities ++ SuitabilityScoreCapabilities.DeleteCapabilities
    }
  }
}