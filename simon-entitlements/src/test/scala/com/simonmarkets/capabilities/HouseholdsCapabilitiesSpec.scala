package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.Custodian.Cetera
import com.goldmansachs.marquee.pipg.{CustodianFaNumber, IssuerPurview, UserACL}
import com.simonmarkets.capabilities.HouseholdsCapabilities._
import com.simonmarkets.networks.ExternalId
import org.mockito.Mockito.{reset, when}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import simon.Id.NetworkId

class HouseholdsCapabilitiesSpec extends WordSpec
  with Matchers
  with MockitoSugar
  with BeforeAndAfter
  with StandardCapabilitiesBehavior
  with NonAdminAvailableAccessKeysGeneratorBehavior
  with NonAdminAcceptedAccessKeysGeneratorBehavior
  with NonAdminCapabilitiesBehavior {

  override implicit def capabilities: Capabilities = HouseholdsCapabilities

  val nonAdminUser = mock[UserACL]

  val purviewNetwork1 = NetworkId("purviewNetwork1")
  val purviewNetwork2 = NetworkId("purviewNetwork2")

  before {
    reset(nonAdminUser)

    when(nonAdminUser.userId).thenReturn("nonAdminUser")
    when(nonAdminUser.networkId).thenReturn(NetworkId("network"))
    when(nonAdminUser.capabilities).thenReturn(Set.empty[String])
    when(nonAdminUser.issuerPurviewIds).thenReturn(Set.empty[IssuerPurview])
    when(nonAdminUser.userPurviewIds).thenReturn(Set(purviewNetwork1, purviewNetwork2))
    when(nonAdminUser.locations).thenReturn(Set("location1", "location2"))
    when(nonAdminUser.allAccessibleLocations).thenReturn(Set("location1", "location3"))
    when(nonAdminUser.faNumbers).thenReturn(Set("faNumber1", "faNumber2"))
    when(nonAdminUser.custodianFaNumbers).thenReturn(Set(CustodianFaNumber(Cetera, "123"), CustodianFaNumber(Cetera, "456")))
    when(nonAdminUser.distributorId).thenReturn(Some("<EMAIL>"))
    when(nonAdminUser.externalIds).thenReturn(Some(Seq(ExternalId("subject", "id"))))
  }

  "HouseholdsAvailableAccessKeys" should {
    behave like nonAdminAccessKeysGenerator(HouseholdsCapabilities)
  }

  "HouseholdsCapabilities" should {
    behave like standardCapabilities
    behave like nonAdminCapabilities

    "ViewHouseholdViaPurview returns purview keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewHouseholdViaPurview))
      HouseholdsCapabilities.getAvailableAccessKeysForCapability(ViewHouseholdViaPurview, nonAdminUser) shouldBe
        Set(s"$ViewHouseholdViaPurview:purviewNetwork1", s"$ViewHouseholdViaPurview:purviewNetwork2")
    }

    "ViewHouseholdViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewHouseholdViaNetwork))
      HouseholdsCapabilities.getAvailableAccessKeysForCapability(ViewHouseholdViaNetwork, nonAdminUser) shouldBe
        Set(s"$ViewHouseholdViaNetwork:network")
    }

    "EditHouseholdViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(EditHouseholdViaNetwork))
      HouseholdsCapabilities.getAvailableAccessKeysForCapability(EditHouseholdViaNetwork, nonAdminUser) shouldBe
        Set(s"$EditHouseholdViaNetwork:network")
    }

    "ViewHouseholdViaLocation returns location keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewHouseholdViaLocation))
      HouseholdsCapabilities.getAvailableAccessKeysForCapability(ViewHouseholdViaLocation, nonAdminUser) shouldBe
        Set(s"$ViewHouseholdViaLocation:network:location1", s"$ViewHouseholdViaLocation:network:location2")
    }

    "ViewHouseholdViaFaNumber returns fa number keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewHouseholdViaFaNumber))
      HouseholdsCapabilities.getAvailableAccessKeysForCapability(ViewHouseholdViaFaNumber, nonAdminUser) shouldBe
        Set(s"$ViewHouseholdViaFaNumber:network:faNumber1", s"$ViewHouseholdViaFaNumber:network:faNumber2")
    }

    "ViewHouseholdViaCustodianFaNumber returns custodian fa number keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewHouseholdViaCustodianFaNumberCapability.name))
      HouseholdsCapabilities.getAvailableAccessKeysForCapability(ViewHouseholdViaCustodianFaNumberCapability.name, nonAdminUser) shouldBe
        Set(s"${ViewHouseholdViaCustodianFaNumberCapability.name}:network:Cetera:123", s"${ViewHouseholdViaCustodianFaNumberCapability.name}:network:Cetera:456")
    }

    "ViewHouseholdViaLocationHierarchy returns fa number keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewHouseholdViaLocationHierarchy))
      HouseholdsCapabilities.getAvailableAccessKeysForCapability(ViewHouseholdViaLocationHierarchy, nonAdminUser) shouldBe
        Set(s"$ViewHouseholdViaLocationHierarchy:network:location1", s"$ViewHouseholdViaLocationHierarchy:network:location3")
    }

    "ViewHouseholdViaDistributorId returns advisor keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewHouseholdViaDistributorId))
      HouseholdsCapabilities.getAvailableAccessKeysForCapability(ViewHouseholdViaDistributorId, nonAdminUser) shouldBe
        Set(s"$ViewHouseholdViaDistributorId:network:<EMAIL>")
    }

    "ViewHouseholdViaUserExternalId returns external id keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewHouseholdViaUserExternalId))
      HouseholdsCapabilities.getAvailableAccessKeysForCapability(ViewHouseholdViaUserExternalId, nonAdminUser) shouldBe
        Set(s"$ViewHouseholdViaUserExternalId:subject:id")
    }
  }

}
