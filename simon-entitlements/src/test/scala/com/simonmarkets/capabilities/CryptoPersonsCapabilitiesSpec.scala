package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.CryptoPersonsCapabilities.{EditCryptoPersonViaNetwork, ViewCryptoPersonViaNetwork}
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}

class CryptoPersonsCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with CapabilitiesTestData{

  "CryptoPersonsCapabilities" should {
    "For Non Admin Users" should {
      "ViewCryptoPersonViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewCryptoPersonViaNetwork))
        val availableKeys = CryptoPersonsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewCryptoPersonViaNetwork:$networkId")
      }

      "EditCryptoPersonViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditCryptoPersonViaNetwork))
        val availableKeys = CryptoPersonsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$EditCryptoPersonViaNetwork:$networkId")
      }
    }

    "For Admin users should return admin key" in {
      val availableKeys = CryptoPersonsCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "have 3 capabilities" in {
      CryptoPersonsCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 3
    }
  }
}
