package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.CrmAuthorizationCapabilities.{EditCrmAuthorizationViaOwner, ViewCrmAuthorizationViaOwner}
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar

class CrmAuthorizationCapabilitiesSpec extends WordSpec
  with Matchers
  with StandardCapabilitiesBehavior
  with MockitoSugar
  with BeforeAndAfter
  with CapabilitiesTestData {

  before( when(nonAdminUser.capabilities).thenReturn(Set[String](ViewCrmAuthorizationViaOwner, EditCrmAuthorizationViaOwner)) )

  "CrmAuthorizationCapabilities" should {

    behave like standardCapabilities(CrmAuthorizationCapabilities)

    "ViewCapabilities returns one admin key" in {
      val keys = CrmAuthorizationCapabilities.availableAccessKeysGenerator.getAvailableAccessKeysForCapabilities(CrmAuthorizationCapabilities.ViewCapabilities, adminUser)
      keys.size shouldBe 1
      keys should contain("admin")
    }

    "EditCapabilities returns one admin key" in {
      val keys = CrmAuthorizationCapabilities.availableAccessKeysGenerator.getAvailableAccessKeysForCapabilities(CrmAuthorizationCapabilities.EditCapabilities, adminUser)
      keys.size shouldBe 1
      keys should contain("admin")
    }

    "ViewCrmAuthorizationViaOwner returns user keys only" in {
      val keys = CrmAuthorizationCapabilities.availableAccessKeysGenerator.getAvailableAccessKeysForCapabilities(CrmAuthorizationCapabilities.ViewCapabilities, nonAdminUser)
      keys.size shouldBe 1
      keys should contain("viewCrmAuthorizationViaOwner:nonAdminUser")
    }

    "EditCrmAuthorizationViaOwner returns user keys only" in {
      val keys = CrmAuthorizationCapabilities.availableAccessKeysGenerator.getAvailableAccessKeysForCapabilities(CrmAuthorizationCapabilities.EditCapabilities, nonAdminUser)
      keys.size shouldBe 1
      keys should contain("editCrmAuthorizationViaOwner:nonAdminUser")
    }
  }
}