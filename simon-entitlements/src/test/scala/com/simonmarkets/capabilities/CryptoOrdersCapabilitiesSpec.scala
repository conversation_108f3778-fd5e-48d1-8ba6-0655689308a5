package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.CryptoOrdersCapabilities.{EditCryptoOrderViaNetwork, ViewCryptoOrderViaNetwork}
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}

class CryptoOrdersCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with CapabilitiesTestData{

  "CyptoOrdersCapabilities" should {
    "For Non Admin Users" should {
      "ViewCryptoOrderViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewCryptoOrderViaNetwork))
        val availableKeys = CryptoOrdersCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewCryptoOrderViaNetwork:$networkId")
      }

      "EditCryptoOrderViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditCryptoOrderViaNetwork))
        val availableKeys = CryptoOrdersCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$EditCryptoOrderViaNetwork:$networkId")
      }
    }

    "For Admin users should return admin key" in {
      val availableKeys = CryptoOrdersCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "have 3 capabilities" in {
      CryptoOrdersCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 3
    }
  }
}
