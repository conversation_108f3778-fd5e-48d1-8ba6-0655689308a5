package com.simonmarkets.capabilities

import com.simonmarkets.entitlements.{HasEditCapabilities, HasViewCapabilities}
import org.scalatest.{Matchers, WordSpec}

trait StandardCapabilitiesBehavior extends Matchers {
  this: WordSpec =>

  def standardCapabilities(implicit capabilities: Capabilities): Unit = {

    "return all view capabilities in toSet" in {
      capabilities match {
        case hasView: HasViewCapabilities => hasView.ViewCapabilities.subsetOf(capabilities.toSet) shouldBe true
        case _ =>
      }
    }

    "return all edit capabilities in toSet" in {
      capabilities match {
        case hasEdit: HasEditCapabilities => hasEdit.EditCapabilities.subsetOf(capabilities.toSet) shouldBe true
        case _ =>
      }
    }

  }
}
