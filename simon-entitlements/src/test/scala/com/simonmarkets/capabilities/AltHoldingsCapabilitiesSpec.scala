package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.{IssuerPurview, UserACL}
import com.simonmarkets.capabilities.AltHoldingsCapabilities._
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator
import com.simonmarkets.networks.ExternalId
import org.mockito.Mockito.{reset, when}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import simon.Id
import simon.Id.NetworkId

class AltHoldingsCapabilitiesSpec extends WordSpec
  with Matchers
  with MockitoSugar
  with BeforeAndAfter
  with StandardCapabilitiesBehavior
  with NonAdminAvailableAccessKeysGeneratorBehavior
  with NonAdminAcceptedAccessKeysGeneratorBehavior
  with AdminAcceptedAccessKeysGeneratorBehavior
  with AdminAvailableAccessKeysGeneratorBehavior {

  implicit def capabilities: Capabilities = AltHoldingsCapabilities

  implicit val availableAccessKeys: AvailableAccessKeysGenerator = AltHoldingsAvailableAccessKeys

  val nonAdminUser: UserACL = mock[UserACL]

  val purviewNetwork1: Id.NetworkId.Type = NetworkId("purviewNetwork1")
  val purviewNetwork2: Id.NetworkId.Type = NetworkId("purviewNetwork2")

  before {
    reset(nonAdminUser)

    when(nonAdminUser.userId).thenReturn("nonAdminUser")
    when(nonAdminUser.networkId).thenReturn(NetworkId("network"))
    when(nonAdminUser.capabilities).thenReturn(Set.empty[String])
    when(nonAdminUser.issuerPurviewIds).thenReturn(Set.empty[IssuerPurview])
    when(nonAdminUser.userPurviewIds).thenReturn(Set(purviewNetwork1, purviewNetwork2))
    when(nonAdminUser.locations).thenReturn(Set("location1", "location2"))
    when(nonAdminUser.allAccessibleLocations).thenReturn(Set("location1", "location3"))
    when(nonAdminUser.faNumbers).thenReturn(Set("faNumber1", "faNumber2"))
    when(nonAdminUser.distributorId).thenReturn(Some("<EMAIL>"))
    when(nonAdminUser.externalIds).thenReturn(Some(Seq(ExternalId("subject_1", "id_1"))))
  }

    "AltHoldingsAvailableAccessKeys" should {
      behave like nonAdminAccessKeysGenerator(AltHoldingsAvailableAccessKeys)
    }

    "AltSnapshotAvailableAccessKeys" should {
      behave like adminAccessKeysGenerator(AltSnapshotAvailableAccessKeys)
    }

    "AltSnapshotAcceptedAccessKeys" should {
      behave like adminAcceptedKeysGenerator(AltSnapshotAcceptedAccessKeys, Seq(NetworkId("network1"), NetworkId("network2")))
    }

    "AltHoldingsCapabilities" should {
      behave like standardCapabilities

      "Given a user which does not have Admin capability then Alt Holdings Available Key Generation" should {
        "ViewAltHoldingViaPurview returns purview keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHoldingViaPurviewCapability.name))
          AltHoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHoldingViaPurviewCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltHoldingViaPurviewCapability.name}:purviewNetwork1", s"${ViewAltHoldingViaPurviewCapability.name}:purviewNetwork2")
        }

        "ViewAltHoldingViaNetwork returns network keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHoldingViaNetworkCapability.name))
          AltHoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHoldingViaNetworkCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltHoldingViaNetworkCapability.name}:network")
        }

        "ViewAltHoldingViaFaNumber returns fa number keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHoldingViaFaNumberCapability.name))
          AltHoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHoldingViaFaNumberCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltHoldingViaFaNumberCapability.name}:network:faNumber1", s"${ViewAltHoldingViaFaNumberCapability.name}:network:faNumber2")
        }

        "ViewAltHoldingViaLocation returns location keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHoldingViaLocationCapability.name))
          AltHoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHoldingViaLocationCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltHoldingViaLocationCapability.name}:network:location1", s"${ViewAltHoldingViaLocationCapability.name}:network:location2")
        }

        "ViewAltHoldingViaLocationHierarchy returns location keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHoldingViaLocationHierarchyCapability.name))
          AltHoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHoldingViaLocationHierarchyCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltHoldingViaLocationHierarchyCapability.name}:network:location1", s"${ViewAltHoldingViaLocationHierarchyCapability.name}:network:location3")
        }

        "ViewAltHoldingViaDistributorId returns advisor keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHoldingViaDistributorIdCapability.name))
          AltHoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHoldingViaDistributorIdCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltHoldingViaDistributorIdCapability.name}:network:<EMAIL>")
        }

        "ViewAltHoldingViaExternalId returns external id keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHoldingViaUserExternalIdCapability.name))
          AltHoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHoldingViaUserExternalIdCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltHoldingViaUserExternalIdCapability.name}:subject_1:id_1")
        }
      }

      "Given a user which only has Admin capability then Holdings Available Key Generation" should {

        "ViewAltHoldingViaNetwork returns network keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHoldingViaNetworkCapability.name))
          AltHoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHoldingViaNetworkCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltHoldingViaNetworkCapability.name}:network")
        }

        "ViewAltHoldingViaFaNumber returns fa number keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHoldingViaFaNumberCapability.name))
          AltHoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHoldingViaFaNumberCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltHoldingViaFaNumberCapability.name}:network:faNumber1", s"${ViewAltHoldingViaFaNumberCapability.name}:network:faNumber2")
        }

        "ViewAltHoldingViaLocation returns location keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHoldingViaLocationCapability.name))
          AltHoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHoldingViaLocationCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltHoldingViaLocationCapability.name}:network:location1", s"${ViewAltHoldingViaLocationCapability.name}:network:location2")
        }

        "ViewAltHoldingViaLocationHierarchy returns location keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHoldingViaLocationHierarchyCapability.name))
          AltHoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHoldingViaLocationHierarchyCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltHoldingViaLocationHierarchyCapability.name}:network:location1", s"${ViewAltHoldingViaLocationHierarchyCapability.name}:network:location3")
        }

        "ViewAltHoldingViaDistributorId returns advisor keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHoldingViaDistributorIdCapability.name))
          AltHoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHoldingViaDistributorIdCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltHoldingViaDistributorIdCapability.name}:network:<EMAIL>")
        }

        "ViewAltHoldingViaExternalId returns external id keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHoldingViaUserExternalIdCapability.name))
          AltHoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHoldingViaUserExternalIdCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltHoldingViaUserExternalIdCapability.name}:subject_1:id_1")
        }
      }

    }
}
