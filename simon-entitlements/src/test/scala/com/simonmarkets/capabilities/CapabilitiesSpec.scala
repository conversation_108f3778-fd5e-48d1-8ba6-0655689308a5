package com.simonmarkets.capabilities

import org.scalatest.{Matchers, WordSpec}


class CapabilitiesSpec extends WordSpec with Matchers with StandardCapabilitiesBehavior {

  "HouseholdsCapabilities" should {
    behave like standardCapabilities(HouseholdsCapabilities)
  }

  "AccountsCapabilities" should {
    behave like standardCapabilities(AccountsCapabilities)
  }

  "CustomRoleCapabilities" should {
    behave like standardCapabilities(CustomRoleCapabilities)
  }
}
