package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.CrmIntegrationCapabilities.{EditCrmIntegrationViaOwner, ViewCrmIntegrationViaOwner}
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar

class CrmIntegrationCapabilitiesSpec extends WordSpec
  with Matchers
  with StandardCapabilitiesBehavior
  with MockitoSugar
  with BeforeAndAfter
  with CapabilitiesTestData {

  before( when(nonAdminUser.capabilities).thenReturn(Set[String](ViewCrmIntegrationViaOwner, EditCrmIntegrationViaOwner)) )

  "CrmIntegrationCapabilities" should {

    behave like standardCapabilities(CrmIntegrationCapabilities)

    "ViewCapabilities returns one admin key" in {
      val keys = CrmIntegrationCapabilities.availableAccessKeysGenerator.getAvailableAccessKeysForCapabilities(CrmIntegrationCapabilities.ViewCapabilities, adminUser)
      keys.size shouldBe 1
      keys should contain("admin")
    }

    "EditCapabilities returns one admin key" in {
      val keys = CrmIntegrationCapabilities.availableAccessKeysGenerator.getAvailableAccessKeysForCapabilities(CrmIntegrationCapabilities.EditCapabilities, adminUser)
      keys.size shouldBe 1
      keys should contain("admin")
    }

    "ViewCrmIntegrationViaOwner returns user keys only" in {
      val keys = CrmIntegrationCapabilities.availableAccessKeysGenerator.getAvailableAccessKeysForCapabilities(CrmIntegrationCapabilities.ViewCapabilities, nonAdminUser)
      keys.size shouldBe 1
      keys should contain("viewCrmIntegrationViaOwner:nonAdminUser")
    }

    "EditCrmIntegrationViaOwner returns user keys only" in {
      val keys = CrmIntegrationCapabilities.availableAccessKeysGenerator.getAvailableAccessKeysForCapabilities(CrmIntegrationCapabilities.EditCapabilities, nonAdminUser)
      keys.size shouldBe 1
      keys should contain("editCrmIntegrationViaOwner:nonAdminUser")
    }
  }
}
