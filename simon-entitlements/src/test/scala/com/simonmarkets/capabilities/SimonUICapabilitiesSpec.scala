package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.AssetClasses._
import org.scalatest.{Matchers, WordSpec}

class SimonUICapabilitiesSpec extends WordSpec with Matchers {
  "return asset class mapping" in {
    val assetClassMap = SimonUICapabilities.toAssetClassUserCapabilityMap
    val capabilitySet = SimonUICapabilities.toCapabilitySet

    val annuityUserCapabilitySet = capabilitySet.getCapabilitiesByAssetClass(AnnuitiesAssetClass).flatMap(_.linkedToCapability)

    val alternativeInvestmentUserCapabilitySet = capabilitySet.getCapabilitiesByAssetClass(AlternativesAssetClass).flatMap(_.linkedToCapability)

    val structuredInvestmentUserCapabilitySet = capabilitySet.getCapabilitiesByAssetClass(StructuredInvestmentsAssetClass).flatMap(_.linkedToCapability)

    val insuranceUserCapabilitySet = capabilitySet.getCapabilitiesByAssetClass(InsuranceAssetClass).flatMap(_.linkedToCapability)

    val smaUserCapabilitySet = capabilitySet.getCapabilitiesByAssetClass(SMAsAssetClass).flatMap(_.linkedToCapability)

    val definedOutcomeETFUserCapabilitySet = capabilitySet.getCapabilitiesByAssetClass(DefinedOutcomeETFsAssetClass).flatMap(_.linkedToCapability)

    val digitalUserCapabilitySet = capabilitySet.getCapabilitiesByAssetClass(DigitalAssetClass).flatMap(_.linkedToCapability)

    val architectUserCapabilitySet = capabilitySet.getCapabilitiesByAssetClass(Architect).flatMap(_.linkedToCapability)

    assetClassMap.annuities shouldBe annuityUserCapabilitySet
    assetClassMap.alternativeInvestments shouldBe alternativeInvestmentUserCapabilitySet
    assetClassMap.structuredInvestments shouldBe structuredInvestmentUserCapabilitySet
    assetClassMap.insurance shouldBe insuranceUserCapabilitySet
    assetClassMap.smas shouldBe smaUserCapabilitySet
    assetClassMap.definedOutcomeETFs shouldBe definedOutcomeETFUserCapabilitySet
    assetClassMap.digitalAssets shouldBe digitalUserCapabilitySet
    assetClassMap.architect shouldBe architectUserCapabilitySet
  }

}
