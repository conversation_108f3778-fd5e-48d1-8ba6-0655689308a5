package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.{IssuerPurview, PurviewedDomain}
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import simon.Id.NetworkId

class SmaAccountMaintenanceTaskCapabilitiesSpec
  extends WordSpec
    with Matchers
    with StandardCapabilitiesBehavior
    with MockitoSugar
    with BeforeAndAfter
    with CapabilitiesTestData {

  "SmaAccountMaintenanceTaskCapabilities" should {
    import com.simonmarkets.capabilities.SmaAccountMaintenanceTasksCapabilities._

    behave like standardCapabilities(SmaAccountMaintenanceTasksCapabilities)

    val issuer1 = "issuer1"
    val issuer2 = "issuer2"
    val issuer3 = "issuer3"

    before {
      when(nonAdminUser.issuerPurviewIds) thenReturn Set(
        IssuerPurview(purviewNetwork1, Set(issuer1, issuer2), purviewedDomainsUpdated = Some(Set(PurviewedDomain.ViewSmaAccountMaintenanceTasks))),
        IssuerPurview(purviewNetwork2, Set(issuer1, issuer3), purviewedDomainsUpdated = Some(Set(PurviewedDomain.EditSmaAccountMaintenanceTasks)))
      )
    }

    "For Non Admin Users" should {
      "ViewSmaAccountMaintenanceTaskViaOwner return owner keys" in {
        when(nonAdminUser.userId).thenReturn("nonAdminUser")
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewSmaAccountMaintenanceTaskViaOwner))
        val keys = SmaAccountMaintenanceTasksCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(s"$ViewSmaAccountMaintenanceTaskViaOwner:nonAdminUser")
      }

      "ViewSmaAccountMaintenanceTaskViaFaNumber return fa number keys" in {
        when(nonAdminUser.faNumbers).thenReturn(Set("faNumber1", "faNumber2"))
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewSmaAccountMaintenanceTaskViaFaNumber))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keys = SmaAccountMaintenanceTasksCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 2
        keys shouldBe Set(s"$ViewSmaAccountMaintenanceTaskViaFaNumber:networkId:faNumber1", s"$ViewSmaAccountMaintenanceTaskViaFaNumber:networkId:faNumber2")
      }

      "ViewSmaAccountMaintenanceTaskViaLocation return location keys" in {
        when(nonAdminUser.locations).thenReturn(Set("location1", "location2"))
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewSmaAccountMaintenanceTaskViaLocation))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keys = SmaAccountMaintenanceTasksCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 2
        keys shouldBe Set(s"$ViewSmaAccountMaintenanceTaskViaLocation:networkId:location1", s"$ViewSmaAccountMaintenanceTaskViaLocation:networkId:location2")
      }

      "ViewSmaAccountMaintenanceTaskViaDistributorId return distributorId keys" in {
        when(nonAdminUser.distributorId).thenReturn(Some("distId1"))
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewSmaAccountMaintenanceTaskViaDistributorId))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keys = SmaAccountMaintenanceTasksCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(s"$ViewSmaAccountMaintenanceTaskViaDistributorId:networkId:distId1")

        when(nonAdminUser.distributorId).thenReturn(None)
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewSmaAccountMaintenanceTaskViaDistributorId))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keysNone = SmaAccountMaintenanceTasksCapabilities.getAvailableAccessKeys(nonAdminUser)
        keysNone.size shouldBe 0
        keysNone shouldBe Set.empty
      }

      "ViewSmaAccountMaintenanceTaskViaExternalId return externalId keys" in {
        when(nonAdminUser.externalIds).thenReturn(Some(Seq(ExternalId("sub", "id"))))
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewSmaAccountMaintenanceTaskViaExternalId))
        val keys = SmaAccountMaintenanceTasksCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(s"$ViewSmaAccountMaintenanceTaskViaExternalId:sub:id")

        when(nonAdminUser.externalIds).thenReturn(None)
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewSmaAccountMaintenanceTaskViaExternalId))
        val keysNone = SmaAccountMaintenanceTasksCapabilities.getAvailableAccessKeys(nonAdminUser)
        keysNone.size shouldBe 0
        keysNone shouldBe Set.empty
      }

      "ViewSmaAccountMaintenanceTaskViaNetwork return network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewSmaAccountMaintenanceTaskViaNetwork))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keys = SmaAccountMaintenanceTasksCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(s"$ViewSmaAccountMaintenanceTaskViaNetwork:networkId")
      }

      "ViewSmaAccountMaintenanceTaskViaPurviewedDomain return network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewSmaAccountMaintenanceTaskViaPurviewedDomain))
        val keys = SmaAccountMaintenanceTasksCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(
          s"$ViewSmaAccountMaintenanceTaskViaPurviewedDomain:$purviewNetwork1",
        )
      }

      "EditSmaAccountMaintenanceTaskViaOwner return owner keys" in {
        when(nonAdminUser.userId).thenReturn("nonAdminUser")
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditSmaAccountMaintenanceTaskViaOwner))
        val keys = SmaAccountMaintenanceTasksCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(s"$EditSmaAccountMaintenanceTaskViaOwner:nonAdminUser")
      }

      "EditSmaAccountMaintenanceTaskViaFaNumber return fa number keys" in {
        when(nonAdminUser.faNumbers).thenReturn(Set("faNumber1", "faNumber2"))
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditSmaAccountMaintenanceTaskViaFaNumber))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keys = SmaAccountMaintenanceTasksCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 2
        keys shouldBe Set(s"$EditSmaAccountMaintenanceTaskViaFaNumber:networkId:faNumber1", s"$EditSmaAccountMaintenanceTaskViaFaNumber:networkId:faNumber2")
      }

      "EditSmaAccountMaintenanceTaskViaLocation return location keys" in {
        when(nonAdminUser.locations).thenReturn(Set("location1", "location2"))
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditSmaAccountMaintenanceTaskViaLocation))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keys = SmaAccountMaintenanceTasksCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 2
        keys shouldBe Set(s"$EditSmaAccountMaintenanceTaskViaLocation:networkId:location1", s"$EditSmaAccountMaintenanceTaskViaLocation:networkId:location2")
      }

      "EditSmaAccountMaintenanceTaskViaDistributorId return distributorId keys" in {
        when(nonAdminUser.distributorId).thenReturn(Some("distId1"))
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditSmaAccountMaintenanceTaskViaDistributorId))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keys = SmaAccountMaintenanceTasksCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(s"$EditSmaAccountMaintenanceTaskViaDistributorId:networkId:distId1")

        when(nonAdminUser.distributorId).thenReturn(None)
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditSmaAccountMaintenanceTaskViaDistributorId))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keysNone = SmaAccountMaintenanceTasksCapabilities.getAvailableAccessKeys(nonAdminUser)
        keysNone.size shouldBe 0
        keysNone shouldBe Set.empty
      }

      "EditSmaAccountMaintenanceTaskViaExternalId return externalId keys" in {
        when(nonAdminUser.externalIds).thenReturn(Some(Seq(ExternalId("sub", "id"))))
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditSmaAccountMaintenanceTaskViaExternalId))
        val keys = SmaAccountMaintenanceTasksCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(s"$EditSmaAccountMaintenanceTaskViaExternalId:sub:id")

        when(nonAdminUser.externalIds).thenReturn(None)
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditSmaAccountMaintenanceTaskViaExternalId))
        val keysNone = SmaAccountMaintenanceTasksCapabilities.getAvailableAccessKeys(nonAdminUser)
        keysNone.size shouldBe 0
        keysNone shouldBe Set.empty
      }

      "EditSmaAccountMaintenanceTaskViaNetwork return network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditSmaAccountMaintenanceTaskViaNetwork))
        when(nonAdminUser.networkId).thenReturn(NetworkId("networkId"))
        val keys = SmaAccountMaintenanceTasksCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(s"$EditSmaAccountMaintenanceTaskViaNetwork:networkId")
      }

      "EditSmaAccountMaintenanceTaskViaPurviewedDomain return network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditSmaAccountMaintenanceTaskViaPurviewedDomain))
        val keys = SmaAccountMaintenanceTasksCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys.size shouldBe 1
        keys shouldBe Set(
          s"$EditSmaAccountMaintenanceTaskViaPurviewedDomain:$purviewNetwork2",
        )
      }
    }

    "For Admin Users" in {
      val availableKeys = SmaAccountMaintenanceTasksCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "have 15 capabilities" in {
      SmaAccountMaintenanceTasksCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 15
    }

    "have 8 view capabilities" in {
      SmaAccountMaintenanceTasksCapabilities.ViewCapabilities shouldBe Set(Admin, ViewSmaAccountMaintenanceTaskViaOwner, ViewSmaAccountMaintenanceTaskViaFaNumber, ViewSmaAccountMaintenanceTaskViaLocation, ViewSmaAccountMaintenanceTaskViaDistributorId, ViewSmaAccountMaintenanceTaskViaExternalId, ViewSmaAccountMaintenanceTaskViaNetwork, ViewSmaAccountMaintenanceTaskViaPurviewedDomain)
    }

    "have 8 edit capabilities" in {
      SmaAccountMaintenanceTasksCapabilities.EditCapabilities shouldBe Set(Admin, EditSmaAccountMaintenanceTaskViaOwner, EditSmaAccountMaintenanceTaskViaFaNumber, EditSmaAccountMaintenanceTaskViaLocation, EditSmaAccountMaintenanceTaskViaDistributorId, EditSmaAccountMaintenanceTaskViaExternalId, EditSmaAccountMaintenanceTaskViaNetwork, EditSmaAccountMaintenanceTaskViaPurviewedDomain)
    }
  }

}
