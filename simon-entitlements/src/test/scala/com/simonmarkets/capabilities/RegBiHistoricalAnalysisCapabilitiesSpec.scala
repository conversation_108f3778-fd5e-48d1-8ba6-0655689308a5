package com.simonmarkets.capabilities

import org.scalatest.{Matchers, WordSpec}
import org.scalatest.prop.TableDrivenPropertyChecks
import com.simonmarkets.capabilities.RegBiHistoricalAnalysisCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when

class RegBiHistoricalAnalysisCapabilitiesSpec extends WordSpec with Matchers with TableDrivenPropertyChecks
  with CapabilitiesTestData {

  when(nonAdminUser.capabilities).thenReturn(Set[String](
    ViewRationaleViaNetwork,
    ViewEvaluationViaUserId,
    EditEvaluationViaUserId
  ))

  "a user which does not have Admin capability then HistoricalAnalysis" should {
    "have a capability returning it's expected keys" in {
      forAll(Table(
        ("capability", "expected key Set"),
        (ViewRationaleViaNetwork, Set("viewRationaleViaNetwork:network")),
        (ViewEvaluationViaUserId, Set("viewEvaluationViaUserId:nonAdminUser")),
        (EditEvaluationViaUserId, Set("editEvaluationViaUserId:nonAdminUser")),
      )) { case (capability, expectedKeySet) =>
        val keys = RegBiHistoricalAnalysisCapabilities.getAvailableAccessKeysForCapability(capability, nonAdminUser)
        keys shouldBe expectedKeySet
      }
    }
  }

  "an admin user" should {
    "have view access" in {
      RegBiHistoricalAnalysisCapabilities.getAvailableAccessKeysForCapabilities(
        RegBiHistoricalAnalysisCapabilities.ViewCapabilities,
        adminUser
      ) shouldBe Set("admin")
    }

    "have edit access" in {
      RegBiHistoricalAnalysisCapabilities.getAvailableAccessKeysForCapabilities(
        RegBiHistoricalAnalysisCapabilities.ViewCapabilities, adminUser
      ) shouldBe Set("admin")
    }
  }
}
