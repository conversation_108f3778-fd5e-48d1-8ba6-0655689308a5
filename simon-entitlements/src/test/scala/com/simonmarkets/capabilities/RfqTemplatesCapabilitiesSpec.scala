package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.RfqTemplatesCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class RfqTemplatesCapabilitiesSpec extends WordSpec with Matchers with StandardCapabilitiesBehavior with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {

  before {
    when(nonAdminUser.capabilities).thenReturn(Set[String](ViewRfqTemplateViaUserCreatedCapability.name, ViewRfqTemplateViaNetworkCapability.name, EditRfqTemplateViaUserCreatedCapability.name, EditRfqTemplateViaNetworkCapability.name))
  }

  "RfqTemplatesCapabilities" should {
    behave like standardCapabilities(RfqTemplatesCapabilities)
    "Given a user has Admin capability RfqTemplatesCapabilities.availableAccessKeysGen" should {
      "ViewCapabilities returns one admin key" in {
        val keys = RfqTemplatesCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapabilities(ViewCapabilities, adminUser)
        keys.size shouldBe 1
        keys should contain("admin")
      }
      "EditCapabilities returns one admin key" in {
        val keys = RfqTemplatesCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapabilities(EditCapabilities, adminUser)
        keys.size shouldBe 1
        keys should contain("admin")
      }
    }
    "Given a user does not have Admin capability then RfqTemplatesCapabilities.availableAccessKeysGen" should {
      "viewRfqTemplateViaUserCreated capability returns user keys only" in {
        val keys = RfqTemplatesCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewRfqTemplateViaUserCreatedCapability.name, nonAdminUser)
        keys.size shouldBe 1
        keys should contain("viewRfqTemplateViaUserCreated:nonAdminUser")
      }
      "viewRfqTemplateViaNetwork capability returns user keys only" in {
        val keys = RfqTemplatesCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewRfqTemplateViaNetworkCapability.name, nonAdminUser)
        keys.size shouldBe 1
        keys should contain("viewRfqTemplateViaNetwork:network")
      }
      "editRfqTemplateViaUserCreated capability returns user keys only" in {
        val keys = RfqTemplatesCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(EditRfqTemplateViaUserCreatedCapability.name, nonAdminUser)
        keys.size shouldBe 1
        keys should contain("editRfqTemplateViaUserCreated:nonAdminUser")
      }
      "editRfqTemplateViaNetwork capability returns user keys only" in {
        val keys = RfqTemplatesCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(EditRfqTemplateViaNetworkCapability.name, nonAdminUser)
        keys.size shouldBe 1
        keys should contain("editRfqTemplateViaNetwork:network")
      }
    }
  }
}
