package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.{IssuerPurview, PurviewedDomain}
import com.simonmarkets.capabilities.OwnersCapabilities._
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class OwnersCapabilitiesSpec extends WordSpec
  with CapabilitiesTestData
  with Matchers
  with MockitoSugar
  with BeforeAndAfter
  with StandardCapabilitiesBehavior
  with AdminAvailableAccessKeysGeneratorBehavior
  with AdminAcceptedAccessKeysGeneratorBehavior {

  override implicit def capabilities: Capabilities = OwnersCapabilities

  val issuerPurview1 = IssuerPurview(network = purviewNetwork1, issuers = Set("A", "B"), purviewedDomainsUpdated = Some(Set(PurviewedDomain.Accounts)))
  val issuerPurview2 = IssuerPurview(network = purviewNetwork2, issuers = Set("A", "B"))

  before {
    when(nonAdminUser.issuerPurviewIds).thenReturn(Set(issuerPurview1, issuerPurview2))
    when(nonAdminUser.locations).thenReturn(Set("location1", "location2"))
    when(nonAdminUser.allAccessibleLocations).thenReturn(Set("location1", "location3"))
    when(nonAdminUser.faNumbers).thenReturn(Set("faNumber1", "faNumber2"))
    when(nonAdminUser.distributorId).thenReturn(Some("<EMAIL>"))
  }

  "OwnersAvailableAccessKeys" should {
    behave like adminAccessKeysGenerator(OwnersAvailableAccessKeys)
  }

  "OwnersCapabilities" should {
    behave like standardCapabilities

    "ViewOwnerViaPurviewedDomain returns purview keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewOwnerViaPurviewedDomain))
      OwnersCapabilities.OwnersAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewOwnerViaPurviewedDomain, nonAdminUser) shouldBe
        Set(s"$ViewOwnerViaPurviewedDomain:purviewNetwork1")
    }

    "ViewOwnerViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewOwnerViaNetwork))
      OwnersCapabilities.OwnersAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewOwnerViaNetwork, nonAdminUser) shouldBe
        Set(s"$ViewOwnerViaNetwork:network")
    }

    "EditOwnerViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(EditOwnerViaNetwork))
      OwnersCapabilities.OwnersAvailableAccessKeys.getAvailableAccessKeysForCapability(EditOwnerViaNetwork, nonAdminUser) shouldBe
        Set(s"$EditOwnerViaNetwork:network")
    }

    "ViewOwnerViaLocation returns location keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewOwnerViaLocation))
      OwnersCapabilities.OwnersAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewOwnerViaLocation, nonAdminUser) shouldBe
        Set(s"$ViewOwnerViaLocation:network:location1", s"$ViewOwnerViaLocation:network:location2")
    }

    "ViewOwnerViaFaNumber returns fa number keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewOwnerViaFaNumber))
      OwnersCapabilities.OwnersAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewOwnerViaFaNumber, nonAdminUser) shouldBe
        Set(s"$ViewOwnerViaFaNumber:network:faNumber1", s"$ViewOwnerViaFaNumber:network:faNumber2")
    }

    "ViewOwnerViaLocationHierarchy returns fa number keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewOwnerViaLocationHierarchy))
      OwnersCapabilities.OwnersAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewOwnerViaLocationHierarchy, nonAdminUser) shouldBe
        Set(s"$ViewOwnerViaLocationHierarchy:network:location1", s"$ViewOwnerViaLocationHierarchy:network:location3")
    }

    "ViewOwnerViaDistributorId returns advisor keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewOwnerViaDistributorId))
      OwnersCapabilities.OwnersAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewOwnerViaDistributorId, nonAdminUser) shouldBe
        Set(s"$ViewOwnerViaDistributorId:network:<EMAIL>")
    }

    "Admin returns false for nonAdmin" in {
      OwnersCapabilities.OwnersAvailableAccessKeys.getAvailableAccessKeysForCapability(Admin, nonAdminUser) shouldBe
        Set.empty
    }

    "Admin returns true for admin view capabilities" in {
      OwnersCapabilities.OwnersAvailableAccessKeys.getAvailableAccessKeysForCapabilities(ViewCapabilities, adminUser) shouldBe
        Set(Admin)
    }

    "Admin returns true for admin edit capabilities" in {
      OwnersCapabilities.OwnersAvailableAccessKeys.getAvailableAccessKeysForCapabilities(EditCapabilities, adminUser) shouldBe
        Set(Admin)
    }
  }

}
