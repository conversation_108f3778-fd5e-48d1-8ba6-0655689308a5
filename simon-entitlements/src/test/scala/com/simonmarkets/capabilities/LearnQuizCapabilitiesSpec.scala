package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.LearnQuizCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class LearnQuizCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {

  "LearnQuizCapabilities" should {
    "For Non Admin Users" should {
      "ViewQuizActivityViaPurview return purview keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewQuizActivityViaPurview))
        val availableKeys = LearnQuizCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(s"$ViewQuizActivityViaPurview:$purviewNetwork1",
          s"$ViewQuizActivityViaPurview:$purviewNetwork2")
      }

      "ViewQuizActivityViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewQuizActivityViaNetwork))
        val availableKeys = LearnQuizCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewQuizActivityViaNetwork:$networkId")
      }

      "ViewQuizActivityViaNetwork returns location keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewQuizActivityViaLocation))
        val availableKeys = LearnQuizCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewQuizActivityViaLocation:$networkId:$location")
      }

      "ViewQuizActivityViaOwner returns guid keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewQuizActivityViaOwner))
        val availableKeys = LearnQuizCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewQuizActivityViaOwner:nonAdminUser")
      }

      "EditQuizActivityViaOwner returns guid keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditQuizActivityViaOwner))
        val availableKeys = LearnQuizCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$EditQuizActivityViaOwner:nonAdminUser")
      }
    }
    "For Admin user should return admin key" in {
      val availableKeys = LearnQuizCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "have 8 capabilities" in {
      LearnQuizCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 8
    }

    "have 6 viewActivity capabilities" in {
      LearnQuizCapabilities.ViewActivityCapabilities shouldBe Set(Admin, ViewQuizActivityViaOwner,
        ViewQuizActivityViaLocation, ViewQuizActivityViaPurview, ViewQuizActivityViaNetwork, ViewQuizActivityViaNetworkTypeCapability.name)
    }

    "have 3 edit activity capability" in {
      LearnQuizCapabilities.EditActivityCapabilities shouldBe Set(Admin, EditQuizActivityViaOwner, EditQuizActivityViaNetworkTypeCapability.name)
    }
  }

}
