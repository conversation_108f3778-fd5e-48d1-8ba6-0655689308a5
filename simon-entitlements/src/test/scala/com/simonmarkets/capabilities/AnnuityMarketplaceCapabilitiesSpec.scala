package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.AnnuityMarketplaceCapabilities.{ViewImmediateAnnuityViaNetwork, ViewStagedPolicyViaNetwork}
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.{Matchers, WordSpec}

class AnnuityMarketplaceCapabilitiesSpec extends WordSpec
  with Matchers
  with StandardCapabilitiesBehavior
  with CapabilitiesTestData {

  "ViewCapabilities" should {

    behave like standardCapabilities(AnnuityMarketplaceCapabilities)

    "For Admin user" should {
      "return admin key" in {
        val availableKeys = AnnuityCannexProductCapabilities.getAvailableAccessKeys(adminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(Admin)
      }
    }

    "For Non Admin Users" should {
      "return networkId ViewStagedPolicyViaNetwork" in {
        when(nonAdminUser.capabilities) thenReturn Set(ViewStagedPolicyViaNetwork)
        val availableKeys = AnnuityMarketplaceCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys should contain only s"viewStagedPolicyViaNetwork:${nonAdminUser.networkId}"
      }

      "return networkId ViewImmediateAnnuityViaNetwork" in {
        when(nonAdminUser.capabilities) thenReturn Set(ViewImmediateAnnuityViaNetwork)
        val availableKeys = AnnuityMarketplaceCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys should contain only s"viewImmediateAnnuityViaNetwork:${nonAdminUser.networkId}"
      }
    }

    "have 4 capabilities" in {
      AnnuityMarketplaceCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 4
    }

    "toSet" in {
      AnnuityMarketplaceCapabilities.toSet shouldBe AnnuityMarketplaceCapabilities.ViewCapabilities
    }
  }
}
