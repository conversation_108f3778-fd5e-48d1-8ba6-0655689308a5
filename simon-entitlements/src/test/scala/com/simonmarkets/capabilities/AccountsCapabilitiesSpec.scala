package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.Custodian.Cetera
import com.goldmansachs.marquee.pipg.{CustodianFaNumber, IssuerPurview, PurviewedDomain}
import com.simonmarkets.capabilities.AccountsCapabilities._
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class AccountsCapabilitiesSpec extends WordSpec
  with CapabilitiesTestData
  with Matchers
  with MockitoSugar
  with BeforeAndAfter
  with StandardCapabilitiesBehavior
  with AdminAvailableAccessKeysGeneratorBehavior
  with AdminAcceptedAccessKeysGeneratorBehavior {

  override implicit def capabilities: Capabilities = AccountsCapabilities

  val issuerPurview1 = IssuerPurview(network = purviewNetwork1, issuers = Set("A", "B"), purviewedDomainsUpdated = Some(Set(PurviewedDomain.Accounts)))
  val issuerPurview2 = IssuerPurview(network = purviewNetwork2, issuers = Set("A", "B"))

  before {
    when(nonAdminUser.issuerPurviewIds).thenReturn(Set(issuerPurview1, issuerPurview2))
    when(nonAdminUser.locations).thenReturn(Set("location1", "location2"))
    when(nonAdminUser.allAccessibleLocations).thenReturn(Set("location1", "location3"))
    when(nonAdminUser.faNumbers).thenReturn(Set("faNumber1", "faNumber2"))
    when(nonAdminUser.custodianFaNumbers).thenReturn(Set(CustodianFaNumber(Cetera, "123"), CustodianFaNumber(Cetera, "456")))
    when(nonAdminUser.distributorId).thenReturn(Some("<EMAIL>"))
  }

  "AccountsAvailableAccessKeys" should {
    behave like adminAccessKeysGenerator(AccountsAvailableAccessKeys)
  }

  "AccountsCapabilities" should {
    behave like standardCapabilities

    "ViewAccountViaPurviewedDomain returns purview keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewAccountViaPurviewedDomain))
      AccountsCapabilities.AccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAccountViaPurviewedDomain, nonAdminUser) shouldBe
        Set(s"$ViewAccountViaPurviewedDomain:purviewNetwork1")
    }

    "ViewAccountViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewAccountViaNetwork))
      AccountsCapabilities.AccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAccountViaNetwork, nonAdminUser) shouldBe
        Set(s"$ViewAccountViaNetwork:network")
    }

    "EditAccountViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(EditAccountViaNetwork))
      AccountsCapabilities.AccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(EditAccountViaNetwork, nonAdminUser) shouldBe
        Set(s"$EditAccountViaNetwork:network")
    }

    "ViewAccountViaLocation returns location keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewAccountViaLocation))
      AccountsCapabilities.AccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAccountViaLocation, nonAdminUser) shouldBe
        Set(s"$ViewAccountViaLocation:network:location1", s"$ViewAccountViaLocation:network:location2")
    }

    "ViewAccountViaFaNumber returns fa number keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewAccountViaFaNumber))
      AccountsCapabilities.AccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAccountViaFaNumber, nonAdminUser) shouldBe
        Set(s"$ViewAccountViaFaNumber:network:faNumber1", s"$ViewAccountViaFaNumber:network:faNumber2")
    }

    "ViewAccountViaCustodianFaNumber returns custodian fa number keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewAccountViaCustodianFaNumberCapability.name))
      AccountsCapabilities.AccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAccountViaCustodianFaNumberCapability.name, nonAdminUser) shouldBe
        Set(s"${ViewAccountViaCustodianFaNumberCapability.name}:network:Cetera:123", s"${ViewAccountViaCustodianFaNumberCapability.name}:network:Cetera:456")
    }

    "ViewAccountViaLocationHierarchy returns fa number keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewAccountViaLocationHierarchy))
      AccountsCapabilities.AccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAccountViaLocationHierarchy, nonAdminUser) shouldBe
        Set(s"$ViewAccountViaLocationHierarchy:network:location1", s"$ViewAccountViaLocationHierarchy:network:location3")
    }

    "ViewAccountViaDistributorId returns advisor keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewAccountViaDistributorId))
      AccountsCapabilities.AccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAccountViaDistributorId, nonAdminUser) shouldBe
        Set(s"$ViewAccountViaDistributorId:network:<EMAIL>")
    }

    "ViewAccountViaOwner returns userId" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewAccountViaOwner))
      AccountsCapabilities.AccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAccountViaOwner, nonAdminUser) shouldBe
        Set(s"$ViewAccountViaOwner:nonAdminUser")
    }

    "ViewAccountViaUserExternalId returns external id keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewAccountViaUserExternalId))
      AccountsCapabilities.AccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAccountViaUserExternalId, nonAdminUser) shouldBe
        Set(s"$ViewAccountViaUserExternalId:subject:id")
    }

    "Admin returns false for nonAdmin" in {
      AccountsCapabilities.AccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(Admin, nonAdminUser) shouldBe
        Set.empty
    }

    "Admin returns true for admin view capabilities" in {
      AccountsCapabilities.AccountsAvailableAccessKeys.getAvailableAccessKeysForCapabilities(ViewCapabilities, adminUser) shouldBe
        Set(Admin)
    }

    "Admin returns true for admin edit capabilities" in {
      AccountsCapabilities.AccountsAvailableAccessKeys.getAvailableAccessKeysForCapabilities(EditCapabilities, adminUser) shouldBe
        Set(Admin)
    }
  }

}
