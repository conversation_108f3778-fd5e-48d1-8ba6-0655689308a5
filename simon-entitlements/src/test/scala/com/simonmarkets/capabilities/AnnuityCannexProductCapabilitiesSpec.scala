package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.Network.Action
import com.simonmarkets.capabilities.AnnuityCannexProductCapabilities.{EditCannexProductViaPayoffEntitlement, ViewCannexProductViaPayoffEntitlement}
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterAll, Matchers, WordSpec}

class AnnuityCannexProductCapabilitiesSpec extends WordSpec
  with Matchers
  with MockitoSugar
  with BeforeAndAfterAll
  with CapabilitiesTestData {

  override def beforeAll(): Unit = {
    val payoffEntitlementsV2 = Map(
      "TestIssuer1" -> Map(
        "FixedIndexAnnuity" -> Set(Action("view")),
        "FixedAnnuity" -> Set(Action("edit")),
      )
    )
    when(nonAdminUser.payoffEntitlementsV2).thenReturn(payoffEntitlementsV2)
    super.beforeAll()
  }

  "AnnuityCannexProductCapabilities" can {
    "For Non Admin Users" should {
      "return network keys for ViewCannexProductViaPayoffEntitlement" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewCannexProductViaPayoffEntitlement))
        val availableKeys = AnnuityCannexProductCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewCannexProductViaPayoffEntitlement:TestIssuer1:FixedIndexAnnuity")
      }

      "return network keys for EditCannexProductViaPayoffEntitlement" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditCannexProductViaPayoffEntitlement))
        val availableKeys = AnnuityCannexProductCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$EditCannexProductViaPayoffEntitlement:TestIssuer1:FixedAnnuity")
      }
    }

    "For Admin user" should {
      "return admin key" in {
        val availableKeys = AnnuityCannexProductCapabilities.getAvailableAccessKeys(adminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(Admin)
      }
    }

    "have 3 capabilities" in {
      AnnuityCannexProductCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 3
    }
  }
}
