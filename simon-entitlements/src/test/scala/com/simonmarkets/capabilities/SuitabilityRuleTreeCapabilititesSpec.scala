package com.simonmarkets.capabilities

import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.prop.TableDrivenPropertyChecks
import org.scalatest.{Matchers, WordSpec}
import com.simonmarkets.capabilities.SuitabilityRuleTreeCapabilitites._

class SuitabilityRuleTreeCapabilititesSpec extends WordSpec with Matchers with TableDrivenPropertyChecks
  with CapabilitiesTestData{

  when(nonAdminUser.capabilities).thenReturn(Set[String](
    ViewSuitabilityRuleTreeViaNetwork,
    EditSuitabilityRuleTreeViaNetwork
  ))

  "a user who does not have Admin capabilities the SuitabilityRuleTree" should {
    "have a capability returning it's expected keys" in {
      forAll(Table(
        ("capability", "expected keyset"),
        (ViewSuitabilityRuleTreeViaNetwork, Set("viewSuitabilityRuleTreeViaNetwork:network")),
        (EditSuitabilityRuleTreeViaNetwork, Set("editSuitabilityRuleTreeViaNetwork:network")),
      )) { case (capability, expectedKeySet) =>
        val keys = SuitabilityRuleTreeCapabilitites.getAvailableAccessKeysForCapability(capability, nonAdminUser)
        keys shouldBe expectedKeySet
      }
    }
  }

  "an admin user" should {
    "have view access" in {
      SuitabilityRuleTreeCapabilitites.getAvailableAccessKeysForCapabilities(
        SuitabilityRuleTreeCapabilitites.ViewCapabilities,
        adminUser
      ) shouldBe Set("admin")
    }

    "have edit access" in {
      SuitabilityRuleTreeCapabilitites.getAvailableAccessKeysForCapabilities(
        SuitabilityRuleTreeCapabilitites.ViewCapabilities, adminUser
      ) shouldBe Set("admin")
    }
  }
}
