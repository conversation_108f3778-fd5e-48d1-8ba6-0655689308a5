package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.{NetworkType, UserACL}
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.UsersCapabilities._
import com.simonmarkets.networks.NetworkIds.AdminNetworkId
import com.simonmarkets.capabilities.CanImpersonateStatus.{CanImpersonate, CanImpersonateWithApproval, CannotImpersonate}
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}
import simon.Id.NetworkId

class UserACLCapabilitiesHelperSpec extends WordSpec with Matchers with MockitoSugar{
  private val impersonated = mock[UserACL]
  private val impersonator = mock[UserACL]

  private val location1 = "location1"
  private val location2 = "location2"
  private val faNumber1 = "faNumber1"
  private val faNumber2 = "faNumber2"
  private val networkIdStr1 = "network1"
  private val networkIdStr2 = "network2"

  when(impersonated.userId).thenReturn("impersonatedUser")
  when(impersonated.networkId).thenReturn(NetworkId(networkIdStr1))
  when(impersonated.locations).thenReturn(Set(location1))
  when(impersonated.faNumbers).thenReturn(Set(faNumber1))
  when(impersonated.networkTypes).thenReturn(Some(List(NetworkType.Admin, NetworkType.Issuer)))
  when(impersonator.networkId).thenReturn(AdminNetworkId)


  "UserACLCapabilities" should {
    "Allow impersonation via Admin" in {
      when(impersonator.capabilities).thenReturn(Set(Admin))
      val res = UserACLCapabilitiesHelper.canImpersonate(impersonator, impersonated)
      res shouldBe CanImpersonate
    }

    "Disallow impersonation via non-Admin" in {
      when(impersonator.capabilities).thenReturn(Set.empty[String])
      val res = UserACLCapabilitiesHelper.canImpersonate(impersonator, impersonated)
      res shouldBe CannotImpersonate
    }

    "Allow impersonation via Network" in {
      when(impersonator.capabilities).thenReturn(Set(ImpersonateUserViaNetwork))
      when(impersonator.networkId).thenReturn(NetworkId(networkIdStr1))
      val res = UserACLCapabilitiesHelper.canImpersonate(impersonator, impersonated)
      res shouldBe CanImpersonate
    }

    "Disallow impersonation via Network" in {
      when(impersonator.capabilities).thenReturn(Set(ImpersonateUserViaNetwork))
      when(impersonator.networkId).thenReturn(NetworkId(networkIdStr2))
      val res = UserACLCapabilitiesHelper.canImpersonate(impersonator, impersonated)
      res shouldBe CannotImpersonate
    }

    "Allow impersonation via Location" in {
      when(impersonator.capabilities).thenReturn(Set(ImpersonateUserViaLocation))
      when(impersonator.networkId).thenReturn(NetworkId(networkIdStr1))
      when(impersonator.locations).thenReturn(Set(location1))
      val res = UserACLCapabilitiesHelper.canImpersonate(impersonator, impersonated)
      res shouldBe CanImpersonate
    }

    "Disallow impersonation via Location" in {
      when(impersonator.capabilities).thenReturn(Set(ImpersonateUserViaLocation))
      when(impersonator.networkId).thenReturn(NetworkId(networkIdStr1))
      when(impersonator.locations).thenReturn(Set(location2))
      val res = UserACLCapabilitiesHelper.canImpersonate(impersonator, impersonated)
      res shouldBe CannotImpersonate
    }

    "Allow impersonation via FA Number" in {
      when(impersonator.capabilities).thenReturn(Set(ImpersonateUserViaFANumber))
      when(impersonator.networkId).thenReturn(NetworkId(networkIdStr1))
      when(impersonator.faNumbers).thenReturn(Set(faNumber1))
      val res = UserACLCapabilitiesHelper.canImpersonate(impersonator, impersonated)
      res shouldBe CanImpersonate
    }

    "Disallow impersonation via FA Number" in {
      when(impersonator.capabilities).thenReturn(Set(ImpersonateUserViaFANumber))
      when(impersonator.networkId).thenReturn(NetworkId(networkIdStr1))
      when(impersonator.faNumbers).thenReturn(Set(faNumber2))
      val res = UserACLCapabilitiesHelper.canImpersonate(impersonator, impersonated)
      res shouldBe CannotImpersonate
    }

    "Allow impersonation via Network With Approval" in {
      when(impersonator.capabilities).thenReturn(Set(ImpersonateUserViaNetworkWithApproval))
      when(impersonator.networkId).thenReturn(NetworkId(networkIdStr1))
      val res = UserACLCapabilitiesHelper.canImpersonate(impersonator, impersonated)
      res shouldBe CanImpersonateWithApproval
    }

    "Allow impersonation via Location With Approval" in {
      when(impersonator.capabilities).thenReturn(Set(ImpersonateUserViaLocationWithApproval))
      when(impersonator.networkId).thenReturn(NetworkId(networkIdStr1))
      when(impersonator.locations).thenReturn(Set(location1))
      val res = UserACLCapabilitiesHelper.canImpersonate(impersonator, impersonated)
      res shouldBe CanImpersonateWithApproval
    }

    "Allow impersonation via FA Number With Approval" in {
      when(impersonator.capabilities).thenReturn(Set(ImpersonateUserViaFANumberWithApproval))
      when(impersonator.networkId).thenReturn(NetworkId(networkIdStr1))
      when(impersonator.faNumbers).thenReturn(Set(faNumber1))
      val res = UserACLCapabilitiesHelper.canImpersonate(impersonator, impersonated)
      res shouldBe CanImpersonateWithApproval
    }

    "Disallow impersonation via Network With Approval" in {
      when(impersonator.capabilities).thenReturn(Set(ImpersonateUserViaNetworkWithApproval))
      when(impersonator.networkId).thenReturn(NetworkId(networkIdStr2))
      val res = UserACLCapabilitiesHelper.canImpersonate(impersonator, impersonated)
      res shouldBe CannotImpersonate
    }

    "Disallow impersonation via Location With Approval" in {
      when(impersonator.capabilities).thenReturn(Set(ImpersonateUserViaLocationWithApproval))
      when(impersonator.networkId).thenReturn(NetworkId(networkIdStr1))
      when(impersonator.locations).thenReturn(Set(location2))
      val res = UserACLCapabilitiesHelper.canImpersonate(impersonator, impersonated)
      res shouldBe CannotImpersonate
    }

    "Disallaw impersonation via FA Number With Approval" in {
      when(impersonator.capabilities).thenReturn(Set(ImpersonateUserViaFANumberWithApproval))
      when(impersonator.networkId).thenReturn(NetworkId(networkIdStr1))
      when(impersonator.faNumbers).thenReturn(Set(faNumber2))
      val res = UserACLCapabilitiesHelper.canImpersonate(impersonator, impersonated)
      res shouldBe CannotImpersonate
    }
  }
}
