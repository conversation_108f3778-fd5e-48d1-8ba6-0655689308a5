package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.LearnContentCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class LearnContentCapabilitiesSpec
  extends WordSpec
    with Matchers
    with MockitoSugar
    with CapabilitiesTestData
    with BeforeAndAfter {

  before {
    when(nonAdminUser.learnContent).thenReturn(Seq("content1", "content2", "content3"))
    when(nonAdminUser.endUserShareableContent).thenReturn(Seq("content1", "content3"))
  }

  "LearnContentCapabilities" should {
    "have 10 capabilities" in {
      LearnContentCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 10
    }

    "For Admin user should return admin key" in {
      val availableKeys = LearnContentCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "For Non Admin Users" should {
      "ViewContentViaContentId return learnContent keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewContentViaContentId))
        val availableKeys = LearnContentCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 3
        availableKeys shouldBe Set(s"$ViewContentViaContentId:content1",
          s"$ViewContentViaContentId:content2", s"$ViewContentViaContentId:content3")
      }

      "ViewContentViaNetwork return networkId" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewContentViaNetwork))
        val availableKeys = LearnContentCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewContentViaNetwork:${nonAdminUser.networkId}")
      }

      "ViewContentViaPurview return networkId" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewContentViaPurview))
        val availableKeys = LearnContentCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewContentViaPurview:${nonAdminUser.networkId}")
      }

      "EditContentViaNetwork return networkId" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditContentViaNetwork))
        val availableKeys = LearnContentCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$EditContentViaNetwork:${nonAdminUser.networkId}")
      }

      "EditContentViaPurview return networkId" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditContentViaPurview))
        val availableKeys = LearnContentCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$EditContentViaPurview:${nonAdminUser.networkId}")
      }

      "ExternalShareViaEndClientShareableId return endUserShareableContent keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ExternalShareViaEndClientShareableId))
        val availableKeys = LearnContentCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(s"$ExternalShareViaEndClientShareableId:content1",
          s"$ExternalShareViaEndClientShareableId:content3")
      }

      "InternalShareViaContentId return learnContent keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(InternalShareViaContentId))
        val availableKeys = LearnContentCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 3
        availableKeys shouldBe Set(s"$InternalShareViaContentId:content1",
          s"$InternalShareViaContentId:content2", s"$InternalShareViaContentId:content3")
      }
    }
  }
}
