package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.CrmContactCapabilities.ViewCrmContactViaOwner
import com.simonmarkets.entitlements.HasViewCapabilities
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito._
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar

class CrmContactCapabilitiesSpec extends WordSpec
  with Matchers
  with StandardCapabilitiesBehavior
  with MockitoSugar
  with BeforeAndAfter
  with CapabilitiesTestData {

  before ( when(nonAdminUser.capabilities).thenReturn(Set[String](ViewCrmContactViaOwner)) )

  "CrmContactCapabilities" should {
    "return all view capabilities in toSet" in {
      CrmContactCapabilities.ViewCapabilities.subsetOf(CrmContactCapabilities.toSet) shouldBe true
    }

    "ViewCapabilities returns one admin key" in {
      val keys = CrmContactCapabilities.availableAccessKeysGenerator.getAvailableAccessKeysForCapabilities(CrmContactCapabilities.ViewCapabilities, adminUser)
      keys.size shouldBe 1
      keys should contain("admin")
    }

    "ViewCrmContactViaOwner returns user keys only" in {
      val keys = CrmContactCapabilities.availableAccessKeysGenerator.getAvailableAccessKeysForCapabilities(CrmContactCapabilities.ViewCapabilities, nonAdminUser)
      keys.size shouldBe 1
      keys should contain("viewCrmContactViaOwner:nonAdminUser")
    }
  }
}
