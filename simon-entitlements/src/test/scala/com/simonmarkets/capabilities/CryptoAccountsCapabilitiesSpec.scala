package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.CryptoAccountsCapabilities.{ViewCryptoAccountViaNetwork, EditCryptoAccountViaNetwork}
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.{Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar

class CryptoAccountsCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with CapabilitiesTestData{

  "CryptoAccountsCapabilities" should {
    "For Non Admin Users" should {
      "ViewCryptoAccountViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewCryptoAccountViaNetwork))
        val availableKeys = CryptoAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewCryptoAccountViaNetwork:$networkId")
      }

      "EditCryptoAccountViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditCryptoAccountViaNetwork))
        val availableKeys = CryptoAccountsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$EditCryptoAccountViaNetwork:$networkId")
      }
    }

    "For Admin users should return admin key" in {
      val availableKeys = CryptoAccountsCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "have 3 capabilities" in {
      CryptoAccountsCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 3
    }
  }
}
