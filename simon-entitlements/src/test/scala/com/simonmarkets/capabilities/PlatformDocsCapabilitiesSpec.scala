package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.PlatformDocsCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.prop.TableDrivenPropertyChecks
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class PlatformDocsCapabilitiesSpec
  extends WordSpec
    with Matchers
    with StandardCapabilitiesBehavior
    with MockitoSugar
    with BeforeAndAfter
    with TableDrivenPropertyChecks
    with CapabilitiesTestData {

  "PlatformDocsCapabilities" should {
    behave like standardCapabilities(PlatformDocsCapabilities)

    "for non admin users" should {
      "allow viewing documents by referral codes (advisor)" in {
        when(nonAdminUser.whiteLabelPartnerId).thenReturn(Some("wlp-01"))
        when(nonAdminUser.firmId).thenReturn(Some("firm-01"))
        // TODO: use ref codes (once added) instead of fa numbers
        when(nonAdminUser.faNumbers).thenReturn(Set("fa-number-01", "fa-number-02"))
        when(nonAdminUser.capabilities).thenReturn(Set(ViewDocumentsViaWlpFirmRefCode))
        PlatformDocsCapabilities.getAvailableAccessKeys(nonAdminUser) shouldBe Set(
          "viewDocumentsViaWlpFirmRefCode:wlp-01:firm-01:fa-number-01",
          "viewDocumentsViaWlpFirmRefCode:wlp-01:firm-01:fa-number-02",
        )
      }

      "allow viewing documents by branch code (branch manager)" ignore {
        when(nonAdminUser.whiteLabelPartnerId).thenReturn(Some("wlp-02"))
        // TODO: add branch codes
        when(nonAdminUser.capabilities).thenReturn(Set(ViewDocumentsViaWlpBranchCode))
        PlatformDocsCapabilities.getAvailableAccessKeys(nonAdminUser) shouldBe Set(
          "viewDocumentsViaWlpBranchCode:wlp-02:branch-01"
        )
      }

      "allow viewing documents by interested party" ignore {
        when(nonAdminUser.whiteLabelPartnerId).thenReturn(Some("wlp-03"))
        // TODO: add interested party id
        when(nonAdminUser.capabilities).thenReturn(Set(ViewDocumentsViaWlpInterestedParty))
        PlatformDocsCapabilities.getAvailableAccessKeys(nonAdminUser) shouldBe Set(
          "viewDocumentsViaWlpBranchCode:wlp-03:interested-party-01"
        )
      }

      "allow viewing documents by account (investor)" ignore {
        when(nonAdminUser.whiteLabelPartnerId).thenReturn(Some("wlp-04"))
        // TODO: add account id
        when(nonAdminUser.capabilities).thenReturn(Set(ViewDocumentsViaWlpAccount))
        PlatformDocsCapabilities.getAvailableAccessKeys(nonAdminUser) shouldBe Set(
          "viewDocumentsViaWlpBranchCode:wlp-04:account-01"
        )
      }

      "allow viewing documents by whitelabel partner (home office manager)" in {
        when(nonAdminUser.whiteLabelPartnerId).thenReturn(Some("wlp-05"))
        when(nonAdminUser.capabilities).thenReturn(Set(ViewDocumentsViaWlp))
        PlatformDocsCapabilities.getAvailableAccessKeys(nonAdminUser) shouldBe Set(
          "viewDocumentsViaWlp:wlp-05"
        )
      }
    }

    "for admin users" should {
      "allow all access to documents" in {
        PlatformDocsCapabilities.getAvailableAccessKeysForCapabilities(ViewCapabilities, adminUser) shouldBe Set(Admin)
        PlatformDocsCapabilities.getAvailableAccessKeysForCapabilities(EditCapabilities, adminUser) shouldBe Set(Admin)
        // admin that doesn't belong to any entities for documents
        when(adminUser.whiteLabelPartnerId).thenReturn(None)
        when(adminUser.firmId).thenReturn(None)
        // still has access to all of the documents
        PlatformDocsCapabilities.getAvailableAccessKeys(adminUser) shouldBe Set(Admin)
      }
    }
  }
}
