package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.SmaCompositeDataCapabilities.ViewSmaCompositeDataViaStrategyAndUnderlier
import com.simonmarkets.capabilities.SmaStrategyCapabilities.ViewSmaStrategyViaStrategyId
import com.simonmarkets.networks.SmaStrategyAndUnderliers
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class SmaStrategyCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {

  before {
    when(nonAdminUser.smaStrategiesAndUnderliers).thenReturn(Some(Set(
      SmaStrategyAndUnderliers("123", Set("SPX", "M1EA")),
      SmaStrategyAndUnderliers("456", Set("SPX"))
    )))
  }

  "SmaStrategyCapabilities" should {
    "have 3 capabilities" in {
      SmaStrategyCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 3
    }

    "For Admin user should return admin key" in {
      val availableKeys = SmaStrategyCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "For Non Admin Users" should {
      "ViewSmaStrategyViaStrategyId return product of strategy and underlier IDs when user has the capability" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewSmaStrategyViaStrategyId))
        val availableKeys = SmaStrategyCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewSmaStrategyViaStrategyId:123", s"$ViewSmaStrategyViaStrategyId:456")
      }

      "ViewSmaStrategyViaStrategyId return no keys when user does not have ViewSmaStrategyViaStrategyId capability" in {
        when(nonAdminUser.capabilities).thenReturn(Set.empty[String])
        val availableKeys = SmaStrategyCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0
      }

      "ViewSmaStrategyViaStrategyId return no keys when network has no strategies associated" in {
        when(nonAdminUser.smaStrategiesAndUnderliers).thenReturn(None)
        val availableKeys = SmaStrategyCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0
      }
    }
  }
}
