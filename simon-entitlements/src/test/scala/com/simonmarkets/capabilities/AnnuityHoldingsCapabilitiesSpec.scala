package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.License.{CRD, NPN}
import com.goldmansachs.marquee.pipg.UserRole.{EqPIPGFAManager, Issuer}
import com.goldmansachs.marquee.pipg.{License, UserRole}
import com.simonmarkets.capabilities.AnnuityHoldingsCapabilities._
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import simon.Id.NetworkId


class AnnuityHoldingsCapabilitiesSpec
  extends WordSpec
    with Matchers
    with MockitoSugar
    with CapabilitiesTestData
    with BeforeAndAfter {

  val holdingsResource: AnnuityHoldingsResource = mock[AnnuityHoldingsResource]

  before {
    when(holdingsResource.distributorNetworkId).thenReturn(networkId)
  }

  "AnnuityHoldingsAvailableAccessKeys" should {
    "have 13 capabilities" in {
      AnnuityHoldingsAvailableAccessKeys.capabilityToAvailableKeyBuilders.size shouldBe 13
    }

    "For Admin user should return admin key" in {
      val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }
  }

  "For Non Admin Users" that {
    "have viewAnnuityHoldingsViaNetwork" should {
      "return the key when user has the capability" in {
        when(nonAdminUser.capabilities).thenReturn(Set(viewAnnuityHoldingsViaNetwork))

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$viewAnnuityHoldingsViaNetwork:$networkId")
      }
    }

    "have viewAnnuityHoldingsViaLicense" should {
      "return the key when the user has a single license" in {
        when(nonAdminUser.capabilities).thenReturn(Set(viewAnnuityHoldingsViaLicense))
        when(nonAdminUser.licenses).thenReturn(Set(CRD("testCRD")))

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$viewAnnuityHoldingsViaLicense:$networkId:CRD:testCRD")
      }

      "return the key list when the user has multiple licenses" in {
        when(nonAdminUser.capabilities).thenReturn(Set(viewAnnuityHoldingsViaLicense))
        when(nonAdminUser.licenses).thenReturn(Set(CRD("testCRD"), NPN("testNPN")))

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(
          s"$viewAnnuityHoldingsViaLicense:$networkId:CRD:testCRD",
          s"$viewAnnuityHoldingsViaLicense:$networkId:NPN:testNPN"
        )
      }

      "not return the key when the user has no licenses" in {
        when(nonAdminUser.capabilities).thenReturn(Set(viewAnnuityHoldingsViaLicense))
        when(nonAdminUser.licenses).thenReturn(Set.empty[License])

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0
      }
    }

    "have viewAnnuityHoldingsViaLicenseWithPurview" should {
      "return the key list when the user has licenses and purview" in {
        when(nonAdminUser.capabilities).thenReturn(Set(viewAnnuityHoldingsViaLicenseWithPurview))
        when(nonAdminUser.userPurviewIds).thenReturn(Set(networkId))
        when(nonAdminUser.licenses).thenReturn(Set(CRD("testCRD"), NPN("testNPN")))

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(
          s"$viewAnnuityHoldingsViaLicenseWithPurview:$networkId:CRD:testCRD",
          s"$viewAnnuityHoldingsViaLicenseWithPurview:$networkId:NPN:testNPN"
        )
      }

      "not return the key when the user has purview but no licenses" in {
        when(nonAdminUser.capabilities).thenReturn(Set(viewAnnuityHoldingsViaLicenseWithPurview))
        when(nonAdminUser.userPurviewIds).thenReturn(Set(networkId))
        when(nonAdminUser.licenses).thenReturn(Set.empty[License])

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0
      }

      "not return the key when user has the capability and licenses but not purview" in {
        when(nonAdminUser.capabilities).thenReturn(Set(viewAnnuityHoldingsViaLicenseWithPurview))
        when(nonAdminUser.userPurviewIds).thenReturn(Set.empty[NetworkId])
        when(nonAdminUser.licenses).thenReturn(Set(CRD("testCRD"), NPN("testNPN")))

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0
      }
    }

    "have viewAnnuityHoldingsViaPurview" should {
      "return the key when user has the capability and purview over the network" in {
        when(nonAdminUser.capabilities).thenReturn(Set(viewAnnuityHoldingsViaPurview))
        when(nonAdminUser.userPurviewIds).thenReturn(Set(networkId))

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$viewAnnuityHoldingsViaPurview:$networkId")
      }

      "not return the key when user has the capability but no purview over the network" in {
        when(nonAdminUser.capabilities).thenReturn(Set(viewAnnuityHoldingsViaPurview))
        when(nonAdminUser.userPurviewIds).thenReturn(Set.empty[NetworkId])

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0
      }
    }

    "have viewAnnuityHoldingsViaFaNumber" should {
      "return the key when user has the capability and fa number" in {
        when(nonAdminUser.capabilities).thenReturn(Set(viewAnnuityHoldingsViaFaNumber))
        when(nonAdminUser.faNumbers).thenReturn(Set("faNum1", "faNum2"))

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(
          s"$viewAnnuityHoldingsViaFaNumber:$networkId:faNum1",
          s"$viewAnnuityHoldingsViaFaNumber:$networkId:faNum2"
        )
      }

      "not return the key when user has the capability but not fa number" in {
        when(nonAdminUser.capabilities).thenReturn(Set(viewAnnuityHoldingsViaPurview))
        when(nonAdminUser.faNumbers).thenReturn(Set.empty[String])

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0
      }
    }

    "have viewAnnuityHoldingsViaLocation" should {
      "return the key when user has the capability and location" in {
        when(nonAdminUser.capabilities).thenReturn(Set(viewAnnuityHoldingsViaLocation))
        when(nonAdminUser.locations).thenReturn(Set("location1", "location2"))

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(
          s"$viewAnnuityHoldingsViaLocation:$networkId:location1",
          s"$viewAnnuityHoldingsViaLocation:$networkId:location2"
        )
      }

      "not return the key when user has the capability but not location" in {
        when(nonAdminUser.capabilities).thenReturn(Set(viewAnnuityHoldingsViaLocation))
        when(nonAdminUser.locations).thenReturn(Set.empty[String])

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0
      }
    }

    "have viewAnnuityHoldingsViaFaNumberWithPurview" should {
      "return the key when user has the capability and fa number and purview" in {
        when(nonAdminUser.capabilities).thenReturn(Set(viewAnnuityHoldingsViaFaNumberWithPurview))
        when(nonAdminUser.userPurviewIds).thenReturn(Set(networkId))
        when(nonAdminUser.faNumbers).thenReturn(Set("faNum1", "faNum2"))

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(
          s"$viewAnnuityHoldingsViaFaNumberWithPurview:$networkId:faNum1",
          s"$viewAnnuityHoldingsViaFaNumberWithPurview:$networkId:faNum2"
        )
      }

      "not return the key when user has the capability and faNumbers but not purview" in {
        when(nonAdminUser.capabilities).thenReturn(Set(viewAnnuityHoldingsViaFaNumberWithPurview))
        when(nonAdminUser.userPurviewIds).thenReturn(Set.empty[NetworkId])
        when(nonAdminUser.faNumbers).thenReturn(Set("faNum1", "faNum2"))

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0
      }
    }

    "have viewAnnuityHoldingsViaIssuer" should {
      "return the key when user has the capability and Issuer role" in {
        val userRoles: Set[UserRole] = Set(Issuer)

        when(nonAdminUser.capabilities).thenReturn(Set(viewAnnuityHoldingsViaIssuer))
        when(nonAdminUser.roles).thenReturn(userRoles)

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$viewAnnuityHoldingsViaIssuer:$networkId")
      }

      "NOT return the key when user has the capability but no Issuer role" in {
        val userRoles: Set[UserRole] = Set(EqPIPGFAManager)

        when(nonAdminUser.capabilities).thenReturn(Set(viewAnnuityHoldingsViaIssuer))
        when(nonAdminUser.roles).thenReturn(userRoles)

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0

      }
    }

    "have viewAnnuityHoldingsViaPurviewLicenses" should {
      "return the key list when the user has matching purviewLicenses" in {
        when(nonAdminUser.capabilities).thenReturn(Set(viewAnnuityHoldingsViaPurviewLicensesCapability.name))
        when(nonAdminUser.purviewLicenses).thenReturn(Some(Set(CRD("testCRD"), NPN("testNPN"))))

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(
          s"${viewAnnuityHoldingsViaPurviewLicensesCapability.name}:$networkId:CRD:testCRD",
          s"${viewAnnuityHoldingsViaPurviewLicensesCapability.name}:$networkId:NPN:testNPN"
        )
      }

      "not return the key when the user has no licenses" in {
        when(nonAdminUser.capabilities).thenReturn(Set(viewAnnuityHoldingsViaPurviewLicensesCapability.name))
        when(nonAdminUser.purviewLicenses).thenReturn(Some(Set.empty[License]))

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0
      }
    }

    "have viewHoldingsViaDistributorNSCCC" should {
      "return the key list when the user has matching purviewNsccCodes" in {
        when(nonAdminUser.capabilities).thenReturn(Set(viewHoldingsViaDistributorNSCCCapability.name))
        when(nonAdminUser.purviewNsccCodes).thenReturn(Some(Set("nscc1", "nscc2")))

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(
          s"${viewHoldingsViaDistributorNSCCCapability.name}:$networkId:nscc1",
          s"${viewHoldingsViaDistributorNSCCCapability.name}:$networkId:nscc2"
        )
      }

      "not return the key when the user has no matching purviewNsccCodes" in {
        when(nonAdminUser.capabilities).thenReturn(Set(viewHoldingsViaDistributorNSCCCapability.name))
        when(nonAdminUser.purviewNsccCodes).thenReturn(Some(Set.empty[String]))

        val availableKeys = AnnuityHoldingsAvailableAccessKeys.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0
      }
    }

  }

  "Accepted access key generator" should {

    def buildAcceptedAccessKeys(capability: String): Set[String] = {
      QueryAnnuityHoldingsAcceptedAccessKeysGenerator.getAcceptedAccessKeysForCapability(capability, holdingsResource)
    }

    "generate admin key" in {
      buildAcceptedAccessKeys(Admin) should contain("admin")
    }

    "generate viewAnnuityHoldingsViaNetwork key" in {
      buildAcceptedAccessKeys(viewAnnuityHoldingsViaNetwork) shouldBe Set(s"$viewAnnuityHoldingsViaNetwork:$networkId")
    }

    "generate viewAnnuityHoldingsViaIssuer key" in {
      when(holdingsResource.carrierNetworkId).thenReturn(Some(NetworkId("carrierId")))
      buildAcceptedAccessKeys(viewAnnuityHoldingsViaIssuer) shouldBe Set(s"$viewAnnuityHoldingsViaIssuer:carrierId")
    }

    "generate viewAnnuityHoldingsViaLicense key" in {
      when(holdingsResource.licenses).thenReturn(Set(CRD("testCRD"), NPN("testNPN")))
      buildAcceptedAccessKeys(viewAnnuityHoldingsViaLicense) shouldBe Set(
        s"$viewAnnuityHoldingsViaLicense:$networkId:NPN:testNPN",
        s"$viewAnnuityHoldingsViaLicense:$networkId:CRD:testCRD"
      )
    }

    "generate viewAnnuityHoldingsViaLicenseWithPurview key" in {
      when(holdingsResource.licenses).thenReturn(Set(CRD("testCRD"), NPN("testNPN")))
      buildAcceptedAccessKeys(viewAnnuityHoldingsViaLicenseWithPurview) shouldBe Set(
        s"$viewAnnuityHoldingsViaLicenseWithPurview:$networkId:NPN:testNPN",
        s"$viewAnnuityHoldingsViaLicenseWithPurview:$networkId:CRD:testCRD"
      )
    }

    "generate viewAnnuityHoldingsViaPurview key" in {
      buildAcceptedAccessKeys(viewAnnuityHoldingsViaPurview) shouldBe Set(s"$viewAnnuityHoldingsViaPurview:$networkId")
    }

    "generate viewAnnuityHoldingsViaFaNumber key" in {
      when(holdingsResource.faNumbers).thenReturn(Set("faNum1", "faNum2"))
      buildAcceptedAccessKeys(viewAnnuityHoldingsViaFaNumber) shouldBe Set(
        s"$viewAnnuityHoldingsViaFaNumber:$networkId:faNum1",
        s"$viewAnnuityHoldingsViaFaNumber:$networkId:faNum2"
      )
    }

    "generate viewAnnuityHoldingsViaLocation key" in {
      when(holdingsResource.locations).thenReturn(Set("location1", "location2"))
      buildAcceptedAccessKeys(viewAnnuityHoldingsViaLocation) shouldBe Set(
        s"$viewAnnuityHoldingsViaLocation:$networkId:location1",
        s"$viewAnnuityHoldingsViaLocation:$networkId:location2"
      )
    }

    "generate viewAnnuityHoldingsViaFaNumberWithPurview key" in {
      when(holdingsResource.faNumbers).thenReturn(Set("faNum1", "faNum2"))
      buildAcceptedAccessKeys(viewAnnuityHoldingsViaFaNumberWithPurview) shouldBe Set(
        s"$viewAnnuityHoldingsViaFaNumberWithPurview:$networkId:faNum1",
        s"$viewAnnuityHoldingsViaFaNumberWithPurview:$networkId:faNum2"
      )
    }

    "generate viewAnnuityHoldingsViaPurviewLicensesCapability key" in {
      when(holdingsResource.licenses).thenReturn(Set(CRD("testCRD"), NPN("testNPN")))
      buildAcceptedAccessKeys(viewAnnuityHoldingsViaPurviewLicensesCapability.name) shouldBe Set(
        s"${viewAnnuityHoldingsViaPurviewLicensesCapability.name}:$networkId:NPN:testNPN",
        s"${viewAnnuityHoldingsViaPurviewLicensesCapability.name}:$networkId:CRD:testCRD"
      )
    }

    "generate viewGroup key" in {
      when(holdingsResource.distributorAssignedIds).thenReturn(Set("hey", "how", "are", "you"))
      buildAcceptedAccessKeys(viewAnnuityHoldingsViaDistributorIdGroup) shouldBe Set(
        "viewAnnuityHoldingsViaDistributorIdGroup:network:hey",
        "viewAnnuityHoldingsViaDistributorIdGroup:network:how",
        "viewAnnuityHoldingsViaDistributorIdGroup:network:are",
        "viewAnnuityHoldingsViaDistributorIdGroup:network:you"
      )
    }
  }
}
