package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.UserACL
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}

class SalesFeeCapabilitiesSpec extends WordSpec with Matchers with StandardCapabilitiesBehavior with MockitoSugar {

  private val userWithSalesFeeCapabilities = mock[UserACL]
  private val userWOSalesFeeCapabilities = mock[UserACL]

  "SalesFeeCapabilities" should {
    behave like standardCapabilities(SalesFeeCapabilities)

    "check SalesFeeCapabilities when it defined" in {
      when(userWithSalesFeeCapabilities.capabilities).thenReturn(SalesFeeCapabilities.toSet)

      SalesFeeCapabilities.toSet.map { capability =>
        SalesFeeCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(capability, userWithSalesFeeCapabilities) shouldBe Set(capability)
      }
    }

    "check SalesFeeCapabilities when it undefined" in {
      when(userWOSalesFeeCapabilities.capabilities).thenReturn(Set.empty[String])

      SalesFeeCapabilities.toSet.map { capability =>
        SalesFeeCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(capability, userWOSalesFeeCapabilities) shouldBe Set.empty[String]
      }
    }
  }
}
