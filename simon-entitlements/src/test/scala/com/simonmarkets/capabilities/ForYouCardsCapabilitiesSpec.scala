package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.ForYouCardsCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}

class ForYouCardsCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with CapabilitiesTestData{
  "ForYouCardsCapabilities" should {
    "For Non Admin Users" should {
      "ViewForYouCardsViaUser returns guid keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewForYouCardsViaUser))
        val availableKeys = ForYouCardsCapabilities.availableAccessKeyGen.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewForYouCardsViaUser:${nonAdminUser.userId}")
      }

      "ViewForYouCardsViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewForYouCardsViaNetwork))
        val availableKeys = ForYouCardsCapabilities.availableAccessKeyGen.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewForYouCardsViaNetwork:$networkId")
      }

      "ViewForYouCardsViaPurview returns purview network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewForYouCardsViaPurview))
        val availableKeys = ForYouCardsCapabilities.availableAccessKeyGen.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(
          s"$ViewForYouCardsViaPurview:$purviewNetwork1",
          s"$ViewForYouCardsViaPurview:$purviewNetwork2"
        )
      }
    }

    "For Admin user should return admin key" in {
      val availableKeys = ForYouCardsCapabilities.availableAccessKeyGen.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "have 4 capabilities" in {
      ForYouCardsCapabilities.availableAccessKeyGen.capabilityToAvailableKeyBuilders.size shouldBe 4
    }
  }
}
