package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.UserImpersonationCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class UserImpersonationCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {

  "UserImpersonationCapabilities" should {
    "have only 5 capabilities" in {
      UserImpersonationCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 5
    }

    "For Admin Users" should {
      "Return admin key" in {
        val availableKeys = UserImpersonationCapabilities.getAvailableAccessKeys(adminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(Admin)
      }
    }

    "For Non Admin Users" should {
      "ViewUserImpersonationViaImpersonatorUserId return impersonatorUserId keys when user has the capability" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewUserImpersonationViaImpersonatorUserId))
        val availableKeys = UserImpersonationCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewUserImpersonationViaImpersonatorUserId:${nonAdminUser.userId}")
      }

      "ViewUserImpersonationViaImpersonatorUserId return no keys when user does not have ViewUserImpersonationViaImpersonatorUserId capability" in {
        when(nonAdminUser.capabilities).thenReturn(Set.empty[String])
        val availableKeys = UserImpersonationCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0
      }

      "ApproveUserImpersonationViaApproverUserId return approverUserId keys when user has the capability" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ApproveUserImpersonationViaApproverUserId))
        val availableKeys = UserImpersonationCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ApproveUserImpersonationViaApproverUserId:${nonAdminUser.userId}")
      }

      "ApproveUserImpersonationViaApproverUserId return no keys when user does not have ApproveUserImpersonationViaApproverUserId capability" in {
        when(nonAdminUser.capabilities).thenReturn(Set.empty[String])
        val availableKeys = UserImpersonationCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0
      }

    }

  }
}
