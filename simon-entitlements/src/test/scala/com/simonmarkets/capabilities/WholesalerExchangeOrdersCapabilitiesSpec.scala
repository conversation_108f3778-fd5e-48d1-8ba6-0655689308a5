package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.WholesalerExchangeOrdersCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar

class WholesalerExchangeOrdersCapabilitiesSpec extends WordSpec
  with Matchers
  with StandardCapabilitiesBehavior
  with MockitoSugar
  with BeforeAndAfter
  with CapabilitiesTestData {

  "WholesalerExchangeOrdersCapabilities" should {

    behave like standardCapabilities(WholesalerExchangeOrdersCapabilities)

    "For admin user, WholesalerExchangeOrdersCapabilities.availableAccessKeysGen" should {
      "ViewCapabilities - returns one admin key" in {
        val keys = WholesalerExchangeOrdersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapabilities(ViewCapabilities, adminUser)

        keys shouldBe Set("admin")
      }

      "EditCapabilities - returns one admin key" in {
        val keys = WholesalerExchangeOrdersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapabilities(EditCapabilities, adminUser)

        keys shouldBe Set("admin")
      }
    }

    "For non-admin user, WholesalerExchangeOrdersCapabilities.availableAccessKeysGen" should {
      "return viewWholesalerExchangeOrdersViaNetwork capability correctly" in {
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewWholesalerExchangeOrdersViaNetworkCapability.name))
        val keys = WholesalerExchangeOrdersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewWholesalerExchangeOrdersViaNetworkCapability.name, nonAdminUser)

        keys shouldBe Set("viewWholesalerExchangeOrdersViaNetwork:network")
      }
      "return editWholesalerExchangeOrdersViaOwner capability correctly" in {
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditWholesalerExchangeOrdersViaOwnerCapability.name))
        val keys = WholesalerExchangeOrdersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(EditWholesalerExchangeOrdersViaOwnerCapability.name, nonAdminUser)

        keys shouldBe Set("editWholesalerExchangeOrdersViaOwner:nonAdminUser")
      }
      "return editWholesalerExchangeOrdersViaNetwork capability correctly" in {
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditWholesalerExchangeOrdersViaNetworkCapability.name))
        val keys = WholesalerExchangeOrdersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(EditWholesalerExchangeOrdersViaNetworkCapability.name, nonAdminUser)

        keys shouldBe Set("editWholesalerExchangeOrdersViaNetwork:network")
      }
    }
  }
}
