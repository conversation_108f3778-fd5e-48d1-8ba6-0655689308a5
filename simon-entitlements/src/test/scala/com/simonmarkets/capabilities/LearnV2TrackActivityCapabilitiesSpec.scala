package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.LearnTrack
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.LearnV2TrackActivityCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class LearnV2TrackActivityCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {
  val trackId1 = "TRK1"
  val trackId2 = "TRK2"

  before {
    when(nonAdminUser.learnTracksV2).thenReturn(Seq(
      LearnTrack(trackId1, isActive = true),
      LearnTrack(trackId2, isActive = false)
    ))
  }


  "TrackActivityCapabilities" should {
    "For Non Admin Users" should {
      "ViewTrackActivityViaPurview return purview keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewTrackActivityViaPurview))
        val availableKeys = LearnV2TrackActivityCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 4
        availableKeys shouldBe Set(s"$ViewTrackActivityViaPurview:$trackId1:$purviewNetwork1", s"$ViewTrackActivityViaPurview:$trackId2:$purviewNetwork1",
          s"$ViewTrackActivityViaPurview:$trackId1:$purviewNetwork2", s"$ViewTrackActivityViaPurview:$trackId2:$purviewNetwork2")
      }

      "ViewTrackActivityViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewTrackActivityViaNetwork))
        val availableKeys = LearnV2TrackActivityCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewTrackActivityViaNetwork:$networkId")
      }

      "ViewTrackActivityViaLocation returns location keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewTrackActivityViaLocation))
        val availableKeys = LearnV2TrackActivityCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewTrackActivityViaLocation:$networkId:$location")
      }

      "ViewTrackActivityViaOwner returns guid keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewTrackActivityViaOwner))
        val availableKeys = LearnV2TrackActivityCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewTrackActivityViaOwner:nonAdminUser")
      }

      "EditTrackActivityViaOwner returns guid keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditTrackActivityViaOwner))
        val availableKeys = LearnV2TrackActivityCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$EditTrackActivityViaOwner:nonAdminUser")
      }
    }
    "For Admin user should return admin key" in {
      val availableKeys = LearnV2TrackActivityCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "have 10 capabilities" in {
      LearnV2TrackActivityCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 10
    }

    "have 8 viewActivity capabilities" in {
      LearnV2TrackActivityCapabilities.ViewTrackActivityCapabilities shouldBe Set(Admin, ViewTrackActivityViaOwner,
        ViewTrackActivityViaNetwork, ViewTrackActivityViaPurview, ViewTrackActivityViaLocation,
        ViewTrackActivityViaNetworkType,
        ViewTrackActivityViaWhiteLabelPartnerCapability.name,
        ViewTrackActivityViaWhiteLabelPartnerAndFirmCapability.name,
      )
    }

    "have 3 edit activity capability" in {
      LearnV2TrackActivityCapabilities.EditTrackActivityCapabilities shouldBe Set(Admin, EditTrackActivityViaOwner, EditTrackActivityViaNetworkType)
    }
  }

}
