package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.OrdersBulkUploadCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class OrdersBulkUploadCapabilitiesSpec
  extends WordSpec
    with Matchers
    with StandardCapabilitiesBehavior
    with MockitoSugar
    with BeforeAndAfter
    with CapabilitiesTestData {

  before {
    when(nonAdminUser.capabilities).thenReturn(Set[String](
      ViewOrderBulkUploadViaNetwork,
      ViewOrderBulkUploadViaOwner,
      ViewOrderBulkUploadViaUserCreated,
      ViewOrderBulkUploadViaPurviewCapability.name
    ))
  }

  "OrdersCapabilities" should {
    import com.simonmarkets.capabilities.OrdersBulkUploadCapabilities._

    behave like standardCapabilities(OrdersBulkUploadCapabilities)
    "Given a user which has Admin capability OrdersCapabilities.availableAccessKeysGen" should {
      "ViewCapabilities returns one admin key" in {
        val keys = OrdersBulkUploadCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapabilities(ViewCapabilities, adminUser)
        keys.size shouldBe 1
        keys should contain("admin")
      }
    }
    "Given a user which does not have Admin capability then OrdersCapabilities.availableAccessKeysGen" should {
      "ViewOrderViaOwner capability returns user keys only" in {
        val keys = OrdersBulkUploadCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewOrderBulkUploadViaOwner, nonAdminUser)
        keys.size shouldBe 1
        keys should contain("viewOrderBulkUploadViaOwner:nonAdminUser")
      }
      "ViewOrderViaNetwork capability returns network based keys only" in {
        val keys = OrdersBulkUploadCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewOrderBulkUploadViaNetwork, nonAdminUser)
        keys.size shouldBe 1
        keys should contain("viewOrderBulkUploadViaNetwork:network")
      }
      "ViewOrderViaUserCreated capability returns user based keys only" in {
        val keys = OrdersBulkUploadCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewOrderBulkUploadViaUserCreated, nonAdminUser)
        keys.size shouldBe 1
        keys should contain("viewOrderBulkUploadViaUserCreated:nonAdminUser")
      }
      "ViewOrderBulkUploadViaPurview capability returns purview keys only" in {
        val keys = OrdersBulkUploadCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewOrderBulkUploadViaPurviewCapability.name, nonAdminUser)
        keys.size shouldBe 2
        keys should contain theSameElementsAs Set(
          "viewOrderBulkUploadViaPurview:purviewNetwork1",
          "viewOrderBulkUploadViaPurview:purviewNetwork2"
        )
      }
    }

  }

}
