package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.{IssuerPurview, UserACL}
import com.simonmarkets.capabilities.AltHouseholdsCapabilities._
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator
import com.simonmarkets.networks.ExternalId
import org.mockito.Mockito.{reset, when}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import simon.Id
import simon.Id.NetworkId

class AltHouseholdsCapabilitiesSpec extends WordSpec
  with Matchers
  with MockitoSugar
  with BeforeAndAfter
  with StandardCapabilitiesBehavior
  with NonAdminAvailableAccessKeysGeneratorBehavior
  with NonAdminAcceptedAccessKeysGeneratorBehavior
  with AdminAcceptedAccessKeysGeneratorBehavior
  with AdminAvailableAccessKeysGeneratorBehavior {

  implicit def capabilities: Capabilities = AltHouseholdsCapabilities

  implicit val availableAccessKeys: AvailableAccessKeysGenerator = AltHouseholdsAvailableAccessKeys

  val nonAdminUser: UserACL = mock[UserACL]

  val purviewNetwork1: Id.NetworkId.Type = NetworkId("purviewNetwork1")
  val purviewNetwork2: Id.NetworkId.Type = NetworkId("purviewNetwork2")

  before {
    reset(nonAdminUser)

    when(nonAdminUser.userId).thenReturn("nonAdminUser")
    when(nonAdminUser.networkId).thenReturn(NetworkId("network"))
    when(nonAdminUser.capabilities).thenReturn(Set.empty[String])
    when(nonAdminUser.issuerPurviewIds).thenReturn(Set.empty[IssuerPurview])
    when(nonAdminUser.userPurviewIds).thenReturn(Set(purviewNetwork1, purviewNetwork2))
    when(nonAdminUser.locations).thenReturn(Set("location1", "location2"))
    when(nonAdminUser.allAccessibleLocations).thenReturn(Set("location1", "location3"))
    when(nonAdminUser.faNumbers).thenReturn(Set("faNumber1", "faNumber2"))
    when(nonAdminUser.distributorId).thenReturn(Some("<EMAIL>"))
    when(nonAdminUser.externalIds).thenReturn(Some(Seq(ExternalId("subject_1", "id_1"))))
  }

  "AltHouseholdsAvailableAccessKeys" should {
    behave like nonAdminAccessKeysGenerator(AltHouseholdsAvailableAccessKeys)
  }

  "AltHouseholdsCapabilities" should {
    behave like standardCapabilities

    "Given a user which does not have Admin capability then Alt Households Available Key Generation" should {
      "ViewAltHouseholdViaPurview returns purview keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHouseholdViaPurviewCapability.name))
        AltHouseholdsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHouseholdViaPurviewCapability.name, nonAdminUser) shouldBe
          Set(s"${ViewAltHouseholdViaPurviewCapability.name}:purviewNetwork1", s"${ViewAltHouseholdViaPurviewCapability.name}:purviewNetwork2")
      }

      "ViewAltHouseholdViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHouseholdViaNetworkCapability.name))
        AltHouseholdsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHouseholdViaNetworkCapability.name, nonAdminUser) shouldBe
          Set(s"${ViewAltHouseholdViaNetworkCapability.name}:network")
      }

      "ViewAltHouseholdViaFaNumber returns fa number keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHouseholdViaFaNumberCapability.name))
        AltHouseholdsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHouseholdViaFaNumberCapability.name, nonAdminUser) shouldBe
          Set(s"${ViewAltHouseholdViaFaNumberCapability.name}:network:faNumber1", s"${ViewAltHouseholdViaFaNumberCapability.name}:network:faNumber2")
      }

      "ViewAltHouseholdViaLocation returns location keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHouseholdViaLocationCapability.name))
        AltHouseholdsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHouseholdViaLocationCapability.name, nonAdminUser) shouldBe
          Set(s"${ViewAltHouseholdViaLocationCapability.name}:network:location1", s"${ViewAltHouseholdViaLocationCapability.name}:network:location2")
      }

      "ViewAltHouseholdViaLocationHierarchy returns location keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHouseholdViaLocationHierarchyCapability.name))
        AltHouseholdsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHouseholdViaLocationHierarchyCapability.name, nonAdminUser) shouldBe
          Set(s"${ViewAltHouseholdViaLocationHierarchyCapability.name}:network:location1", s"${ViewAltHouseholdViaLocationHierarchyCapability.name}:network:location3")
      }

      "ViewAltHouseholdViaDistributorId returns advisor keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHouseholdViaDistributorIdCapability.name))
        AltHouseholdsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHouseholdViaDistributorIdCapability.name, nonAdminUser) shouldBe
          Set(s"${ViewAltHouseholdViaDistributorIdCapability.name}:network:<EMAIL>")
      }

      "ViewAltHouseholdViaExternalId returns external id keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHouseholdViaUserExternalIdCapability.name))
        AltHouseholdsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHouseholdViaUserExternalIdCapability.name, nonAdminUser) shouldBe
          Set(s"${ViewAltHouseholdViaUserExternalIdCapability.name}:subject_1:id_1")
      }
    }

    "Given a user which only has Admin capability then Households Available Key Generation" should {

      "ViewAltHouseholdViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHouseholdViaNetworkCapability.name))
        AltHouseholdsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHouseholdViaNetworkCapability.name, nonAdminUser) shouldBe
          Set(s"${ViewAltHouseholdViaNetworkCapability.name}:network")
      }

      "ViewAltHouseholdViaFaNumber returns fa number keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHouseholdViaFaNumberCapability.name))
        AltHouseholdsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHouseholdViaFaNumberCapability.name, nonAdminUser) shouldBe
          Set(s"${ViewAltHouseholdViaFaNumberCapability.name}:network:faNumber1", s"${ViewAltHouseholdViaFaNumberCapability.name}:network:faNumber2")
      }

      "ViewAltHouseholdViaLocation returns location keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHouseholdViaLocationCapability.name))
        AltHouseholdsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHouseholdViaLocationCapability.name, nonAdminUser) shouldBe
          Set(s"${ViewAltHouseholdViaLocationCapability.name}:network:location1", s"${ViewAltHouseholdViaLocationCapability.name}:network:location2")
      }

      "ViewAltHouseholdViaLocationHierarchy returns location keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHouseholdViaLocationHierarchyCapability.name))
        AltHouseholdsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHouseholdViaLocationHierarchyCapability.name, nonAdminUser) shouldBe
          Set(s"${ViewAltHouseholdViaLocationHierarchyCapability.name}:network:location1", s"${ViewAltHouseholdViaLocationHierarchyCapability.name}:network:location3")
      }

      "ViewAltHouseholdViaDistributorId returns advisor keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHouseholdViaDistributorIdCapability.name))
        AltHouseholdsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHouseholdViaDistributorIdCapability.name, nonAdminUser) shouldBe
          Set(s"${ViewAltHouseholdViaDistributorIdCapability.name}:network:<EMAIL>")
      }

      "ViewAltHouseholdViaExternalId returns external id keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewAltHouseholdViaUserExternalIdCapability.name))
        AltHouseholdsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltHouseholdViaUserExternalIdCapability.name, nonAdminUser) shouldBe
          Set(s"${ViewAltHouseholdViaUserExternalIdCapability.name}:subject_1:id_1")
      }
    }

  }
}