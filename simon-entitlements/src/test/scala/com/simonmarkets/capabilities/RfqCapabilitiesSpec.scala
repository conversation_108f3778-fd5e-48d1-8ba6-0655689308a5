package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.{IssuerPurview, PurviewedDomain}
import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.capabilities.RfqCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class RfqCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {

  val issuer1 = "issuer1"
  val issuer2 = "issuer2"
  val issuer3 = "issuer3"

  before {
    when(nonAdminUser.issuerPurviewIds) thenReturn Set(
      IssuerPurview(purviewNetwork1, Set(issuer1, issuer2), purviewedDomainsUpdated = Some(Set(PurviewedDomain.ViewRfqs))),
      IssuerPurview(purviewNetwork2, Set(issuer1, issuer3), purviewedDomainsUpdated = Some(Set(PurviewedDomain.EditRfqs))),
      IssuerPurview(purviewNetwork3, Set(issuer2, issuer3), purviewedDomainsUpdated = Some(Set(PurviewedDomain.AwardRfqs)))
    )
  }

  "RfqCapabilities" should {
    "For Non Admin Users" should {
      "ViewRfqViaNetworkIssuerPurview return purview keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewRfqViaNetworkIssuerPurview))
        val availableKeys = RfqCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(
          s"$ViewRfqViaNetworkIssuerPurview:$purviewNetwork1:$issuer1",
          s"$ViewRfqViaNetworkIssuerPurview:$purviewNetwork1:$issuer2"
        )
      }

      "ViewRfqViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewRfqViaNetwork))
        val availableKeys = RfqCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewRfqViaNetwork:$networkId")
      }

      "ViewRfqViaOwner returns guid keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewRfqViaOwner))
        val availableKeys = RfqCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewRfqViaOwner:nonAdminUser")
      }

      "ViewRfqAuditNotesFieldViaNetwork returns guid keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewRfqAuditNotesFieldViaNetwork))
        val availableKeys = RfqCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewRfqAuditNotesFieldViaNetwork:$networkId")
      }

      "viewRfqInternalApprovalTagsViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewTagsInternalApprovalCapability.name))
        val availableKeys = RfqCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"viewTagsInternalApproval")
      }

      "EditRfqAuditNotesFieldViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditRfqAuditNotesFieldViaNetwork))
        val availableKeys = RfqCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$EditRfqAuditNotesFieldViaNetwork:$networkId")
      }

      "editRfqInternalApprovalTagsViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditTagsInternalApprovalCapability.name))
        val availableKeys = RfqCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"editTagsInternalApproval")
      }

      "AwardOnBehalfOfViaNetworkIssuerPurview return purview keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(AwardRfqOnBehalfOfViaNetworkIssuerPurview))
        val availableKeys = RfqCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(
          s"$AwardRfqOnBehalfOfViaNetworkIssuerPurview:$purviewNetwork3:$issuer2",
          s"$AwardRfqOnBehalfOfViaNetworkIssuerPurview:$purviewNetwork3:$issuer3",
        )
      }

      "AwardOnBehalfOfViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(AwardRfqOnBehalfOfViaNetwork))
        val availableKeys = RfqCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$AwardRfqOnBehalfOfViaNetwork:$networkId")
      }
    }
    "For Admin user should return admin key" in {
      val availableKeys = RfqCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "have 46 capabilities" in {
      RfqCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 46
      RfqCapabilities.capabilityToAvailableKeyBuilders.size shouldBe RfqCapabilities.toDetailedCapabilitySet.size
    }

    "have 5 view capabilities" in {
      RfqCapabilities.DetailedViewCapabilities.map(_.name) should contain allElementsOf Set(Admin, ViewRfqViaOwner, ViewRfqViaUserCreatedCapability.name, ViewRfqViaNetwork,
        ViewRfqViaNetworkIssuerPurview)
    }

    "have 1 edit activity capability" in {
      RfqCapabilities.DetailedEditCapabilities.map(_.name) should contain allElementsOf Set(Admin)
    }

    "have 5 submit activity capability" in {
      RfqCapabilities.DetailedSubmitCapabilities.map(_.name) should contain allElementsOf Set(Admin, AwardRfqOnBehalfOfViaNetwork,
        AwardRfqOnBehalfOfViaNetworkIssuerPurview, AwardRfqViaOwner, AwardRfqViaUserCreatedCapability.name)
    }

    "have 5 request activity capability" in {
      RfqCapabilities.DetailedRequestCapabilities.map(_.name) should contain allElementsOf Set(Admin, RequestRfqViaOwner,
        RequestRfqViaUserCreatedCapability.name, RequestRfqViaNetwork, RequestRfqViaNetworkIssuerPurview)
    }

    "have 6 decline activity capability" in {
      RfqCapabilities.DetailedDeclineCapabilities.map(_.name) should contain allElementsOf Set(Admin, DeclineRfqViaOwner, DeclineRfqViaUserCreatedCapability.name,
        DeclineRfqViaNetwork, DeclineRfqViaNetworkIssuerPurview, DeclineRfqViaNetworkTypeIssuerPurviewCapability.name)
    }

    "have 3 home office approve activity capability" in {
      RfqCapabilities.DetailedHomeOfficeApproveCapabilities should contain allElementsOf Set(AdminCapability, HomeOfficeApproveRfqViaNetworkCapability,
        HomeOfficeApproveRfqViaNetworkIssuerPurviewCapability)
    }
  }

}
