package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.CryptoOfferingsCapabilities.{EditCryptoOfferingViaNetwork, ViewCryptoOfferingViaNetwork}
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.{Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar

class CryptoOfferingsCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with CapabilitiesTestData{

  "CyptoOfferingsCapabilities" should {
    "For Non Admin Users" should {
      "ViewCryptoOfferingViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewCryptoOfferingViaNetwork))
        val availableKeys = CryptoOfferingsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewCryptoOfferingViaNetwork:$networkId")
      }

      "EditCryptoOfferingViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditCryptoOfferingViaNetwork))
        val availableKeys = CryptoOfferingsCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$EditCryptoOfferingViaNetwork:$networkId")
      }
    }

    "For Admin users should return admin key" in {
      val availableKeys = CryptoOfferingsCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "have 3 capabilities" in {
      CryptoOfferingsCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 3
    }
  }
}