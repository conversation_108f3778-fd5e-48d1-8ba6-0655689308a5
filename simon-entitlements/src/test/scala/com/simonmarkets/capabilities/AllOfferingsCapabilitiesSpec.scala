package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.{IssuerPurview, Network, Purview, UserACL}
import com.simonmarkets.capabilities.AllOfferingsCapabilities.availableAccessKeysGen
import com.simonmarkets.capabilities.RatesSPOfferingsCapabilities.{OpenCloseRatesSPOfferingViaNetworkCapability, RejectRatesSPOfferingViaNetworkCapability, RejectRatesSPOfferingViaNetworkIssuerPurviewCapability, ViewNonRegSRatesSPPastOfferingsViaViewPayoffEntitlementsCapability}
import com.simonmarkets.capabilities.SPOfferingsCapabilities.{EditSPOfferingViaNetwork, OpenCloseSPOfferingViaNetwork, OverrideTierSPOfferingViaNetworkCapability, OverrideTierSPOfferingViaPurviewCapability, RejectSPOfferingViaNetwork, RejectSPOfferingViaNetworkIssuerPurview, StageSPOfferingViaNetwork, StageSPOfferingViaNetworkIssuerPurview, ViewNonRegSSPPastOfferingsViaViewPayoffEntitlements}
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}
import simon.Id.NetworkId

class AllOfferingsCapabilitiesSpec extends WordSpec with MockitoSugar with Matchers {
  private val networkId = NetworkId("networkId")
  private val nonAdminUser = mock[UserACL]
  when(nonAdminUser.networkId).thenReturn(networkId)

  "AllOfferingsCapabilities" should {

    "user can edit offerings assigned to networks from issuerPurview " in {
      val networkId1 = NetworkId("networkId1")
      val networkId2 = NetworkId("networkId2")
      val issuer1 = "issuer1"
      val issuer2 = "issuer2"
      when(nonAdminUser.issuerPurviewIds).thenReturn(
        Set(
          IssuerPurview(networkId1, Set(issuer1)),
          IssuerPurview(networkId2, Set(issuer2))
        )
      )
      when(nonAdminUser.capabilities).thenReturn(Set(EditSPOfferingViaNetwork))
      availableAccessKeysGen.getAvailableAccessKeysForCapability(EditSPOfferingViaNetwork, nonAdminUser) shouldBe Set(s"$EditSPOfferingViaNetwork:$networkId")
    }

    "user can override tier for offerings assigned to networks and from purview" in {
      val overrideTierViaNetworkName = OverrideTierSPOfferingViaNetworkCapability.name
      val overrideTierViaPurviewName = OverrideTierSPOfferingViaPurviewCapability.name
      when(nonAdminUser.capabilities).thenReturn(Set(overrideTierViaNetworkName, overrideTierViaPurviewName))
      when(nonAdminUser.userPurviewIds).thenReturn(Set(NetworkId("networkId1"), NetworkId("networkId2")))

      availableAccessKeysGen.getAvailableAccessKeysForCapability(overrideTierViaNetworkName, nonAdminUser) shouldBe Set(s"$overrideTierViaNetworkName:$networkId")
      availableAccessKeysGen.getAvailableAccessKeysForCapability(overrideTierViaPurviewName, nonAdminUser) shouldBe Set(s"$overrideTierViaPurviewName:networkId1", s"$overrideTierViaPurviewName:networkId2")
    }

    "user can reject and stage offerings assigned to networks" in {
      when(nonAdminUser.capabilities).thenReturn(Set(
        RejectSPOfferingViaNetwork,
        RejectRatesSPOfferingViaNetworkCapability.name,
        StageSPOfferingViaNetwork
      ))
      availableAccessKeysGen.getAvailableAccessKeysForCapability(RejectSPOfferingViaNetwork, nonAdminUser) shouldBe Set(s"$RejectSPOfferingViaNetwork:$networkId")
      availableAccessKeysGen.getAvailableAccessKeysForCapability(RejectRatesSPOfferingViaNetworkCapability.name, nonAdminUser) shouldBe Set(s"${RejectRatesSPOfferingViaNetworkCapability.name}:$networkId")
      availableAccessKeysGen.getAvailableAccessKeysForCapability(StageSPOfferingViaNetwork, nonAdminUser) shouldBe Set(s"$StageSPOfferingViaNetwork:$networkId")
    }

    "user can reject and stage offerings assigned to networks from issuerPurview" in {
      val networkId1 = NetworkId("networkId1")
      val networkId2 = NetworkId("networkId2")
      val issuer1 = "issuer1"
      val issuer2 = "issuer2"
      when(nonAdminUser.issuerPurviewIds).thenReturn(
        Set(
          IssuerPurview(networkId1, Set(issuer1)),
          IssuerPurview(networkId2, Set(issuer2))
        )
      )
      when(nonAdminUser.capabilities).thenReturn(Set(
        RejectSPOfferingViaNetworkIssuerPurview,
        RejectRatesSPOfferingViaNetworkIssuerPurviewCapability.name,
        StageSPOfferingViaNetworkIssuerPurview
      ))
      availableAccessKeysGen.getAvailableAccessKeysForCapability(RejectSPOfferingViaNetworkIssuerPurview, nonAdminUser) shouldBe
        Set(s"$RejectSPOfferingViaNetworkIssuerPurview:$networkId1:$issuer1", s"$RejectSPOfferingViaNetworkIssuerPurview:$networkId2:$issuer2")
      availableAccessKeysGen.getAvailableAccessKeysForCapability(RejectRatesSPOfferingViaNetworkIssuerPurviewCapability.name, nonAdminUser) shouldBe
        Set(s"${RejectRatesSPOfferingViaNetworkIssuerPurviewCapability.name}:$networkId1:$issuer1", s"${RejectRatesSPOfferingViaNetworkIssuerPurviewCapability.name}:$networkId2:$issuer2")
      availableAccessKeysGen.getAvailableAccessKeysForCapability(StageSPOfferingViaNetworkIssuerPurview, nonAdminUser) shouldBe
        Set(s"$StageSPOfferingViaNetworkIssuerPurview:$networkId1:$issuer1", s"$StageSPOfferingViaNetworkIssuerPurview:$networkId2:$issuer2")
    }
  }

  "user can open/close offerings" in {
    when(nonAdminUser.capabilities).thenReturn(Set(OpenCloseSPOfferingViaNetwork, OpenCloseRatesSPOfferingViaNetworkCapability.name))
    availableAccessKeysGen.getAvailableAccessKeysForCapability(OpenCloseSPOfferingViaNetwork, nonAdminUser) shouldBe Set(s"$OpenCloseSPOfferingViaNetwork:$networkId")
    availableAccessKeysGen.getAvailableAccessKeysForCapability(OpenCloseRatesSPOfferingViaNetworkCapability.name, nonAdminUser) shouldBe Set(s"${OpenCloseRatesSPOfferingViaNetworkCapability.name}:$networkId")
  }

  "payoff entitlements v2 used for past offerings" in {
    val payoffEntitlements = Map(
      "PRU" -> Map("PointToPoint" -> Set(Network.Action("trade"), Network.Action("edit"), Network.Action("view"),
        Network.Action("build"), Network.Action("illustrate"), Network.Action("performance"))),
      "CITI" -> Map("BufferedLeveredNote" -> Set(Network.Action("view"))),
      "BCS" -> Map("Autocallable" -> Set(Network.Action("trade")))
    )

    when(nonAdminUser.payoffEntitlementsV2).thenReturn(payoffEntitlements)
    when(nonAdminUser.capabilities).thenReturn(Set(ViewNonRegSSPPastOfferingsViaViewPayoffEntitlements,
      ViewNonRegSRatesSPPastOfferingsViaViewPayoffEntitlementsCapability.name))

    availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewNonRegSSPPastOfferingsViaViewPayoffEntitlements,
      nonAdminUser) shouldBe Set(s"$ViewNonRegSSPPastOfferingsViaViewPayoffEntitlements:PRU:PointToPoint",
      s"$ViewNonRegSSPPastOfferingsViaViewPayoffEntitlements:CITI:BufferedLeveredNote")
    availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewNonRegSRatesSPPastOfferingsViaViewPayoffEntitlementsCapability.name,
      nonAdminUser) shouldBe Set(s"${ViewNonRegSRatesSPPastOfferingsViaViewPayoffEntitlementsCapability.name}:PRU:PointToPoint",
      s"${ViewNonRegSRatesSPPastOfferingsViaViewPayoffEntitlementsCapability.name}:CITI:BufferedLeveredNote")
  }

  //This is only needed for backward compatibility. Will be removed in Jan 06 release.
  "payoff entitlements will be used used for past offerings when payoff entitlements v2 is not set" in {
    val payoffEntitlements = Map(
      "PRU" -> Map("PointToPoint" -> List("trade", "edit", "view", "build", "illustrate", "performance")),
      "CITI" -> Map("BufferedLeveredNote" -> List("view")),
      "BCS" -> Map("Autocallable" -> List("trade"))
    )

    when(nonAdminUser.payoffEntitlementsV2).thenReturn(Map.empty[String, Map[String, Set[Network.Action]]])
    when(nonAdminUser.payoffEntitlements).thenReturn(payoffEntitlements)
    when(nonAdminUser.capabilities).thenReturn(Set(ViewNonRegSSPPastOfferingsViaViewPayoffEntitlements))

    availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewNonRegSSPPastOfferingsViaViewPayoffEntitlements,
      nonAdminUser) shouldBe Set(s"$ViewNonRegSSPPastOfferingsViaViewPayoffEntitlements:PRU:PointToPoint",
      s"$ViewNonRegSSPPastOfferingsViaViewPayoffEntitlements:CITI:BufferedLeveredNote")
  }
}
