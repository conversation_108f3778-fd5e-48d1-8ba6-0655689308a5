package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.Network.Action
import com.goldmansachs.marquee.pipg.NetworkType
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.SPOrdersCloseoutCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class SPOrdersCloseoutCapabilitiesSpec
  extends WordSpec
    with Matchers
    with MockitoSugar
    with CapabilitiesTestData
    with BeforeAndAfter {

  "SPOrdersCloseoutCapabilitiesSpec" should {
    "have 8 capabilities" in {
      SPOrdersCloseoutCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 9
    }

    "For Admin user should return admin key" in {
      val availableKeys = SPOrdersCloseoutCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "For Non Admin Users" should {
      "ViewSPOrdersCloseoutViaNetwork return neteworkId" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewSPOrdersCloseoutViaNetwork))
        val availableKeys = SPOrdersCloseoutCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewSPOrdersCloseoutViaNetwork:${nonAdminUser.networkId}")
      }

      "ViewSPOrdersCloseoutViaPurview return networkId" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewSPOrdersCloseoutViaPurview))
        val availableKeys = SPOrdersCloseoutCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewSPOrdersCloseoutViaPurview:${nonAdminUser.networkId}")
      }

      "ViewSPOrdersCloseoutViaSystemUser return networkId" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewSPOrdersCloseoutViaSystemUser))
        val availableKeys = SPOrdersCloseoutCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewSPOrdersCloseoutViaSystemUser")
      }

      "ViewSPOrdersCloseoutViaIssuerNetworkTypeAndEditPayoffEntitlements return all entitlements" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewSPOrdersCloseoutViaIssuerNetworkTypeAndEditPayoffEntitlements))
        when(nonAdminUser.networkTypes).thenReturn(Some(List(NetworkType.Issuer, NetworkType.Carrier)))
        when(nonAdminUser.payoffEntitlementsV2).thenReturn(
          Map(
            "bank" -> Map("a" -> Set(Action(action = "edit", contractParams = Map("a" -> Set("x", "y"))),
              Action(action = "b", contractParams = Map("c" -> Set("1", "2"))))),
            "bank2" -> Map("b" -> Set(Action(action = "edit", contractParams = Map("a" -> Set("x", "y")))))
          )
        )
        val availableKeys = SPOrdersCloseoutCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set("viewSPOrdersCloseoutViaIssuerNetworkTypeAndEditPayoffEntitlements:ISSUER:bank:a",
          "viewSPOrdersCloseoutViaIssuerNetworkTypeAndEditPayoffEntitlements:ISSUER:bank2:b")
      }

      "ViewSPOrdersCloseoutViaIssuerNetworkTypeAndEditPayoffEntitlements return no entitlements when networkTypes not defined" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewSPOrdersCloseoutViaIssuerNetworkTypeAndEditPayoffEntitlements))
        when(nonAdminUser.networkTypes).thenReturn(None)
        when(nonAdminUser.payoffEntitlementsV2).thenReturn(
          Map(
            "bank" -> Map("a" -> Set(Action(action = "edit", contractParams = Map("a" -> Set("x", "y"))),
              Action(action = "b", contractParams = Map("c" -> Set("1", "2"))))),
            "bank2" -> Map("b" -> Set(Action(action = "edit", contractParams = Map("a" -> Set("x", "y")))))
          )
        )
        val availableKeys = SPOrdersCloseoutCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set()
      }

      "ViewSPOrdersCloseoutViaIssuerNetworkTypeAndEditPayoffEntitlements return no entitlements when no edit payoff entitlements are defined" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewSPOrdersCloseoutViaIssuerNetworkTypeAndEditPayoffEntitlements))
        when(nonAdminUser.networkTypes).thenReturn(None)
        val availableKeys = SPOrdersCloseoutCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set()
      }

      "ViewSPOrdersCloseoutViaIssuerNetworkTypeAndEditPayoffEntitlements return no entitlements when no Issuer networkType is defined" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewSPOrdersCloseoutViaIssuerNetworkTypeAndEditPayoffEntitlements))
        when(nonAdminUser.networkTypes).thenReturn(Some(List(NetworkType.Wholesaler)))
        val availableKeys = SPOrdersCloseoutCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set()
      }

      "ApproveSPOrdersCloseoutViaNetwork return networkId" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ApproveSPOrdersCloseoutViaNetwork))
        val availableKeys = SPOrdersCloseoutCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ApproveSPOrdersCloseoutViaNetwork:${nonAdminUser.networkId}")
      }

      "ApproveSPOrdersCloseoutViaPurview return networkId" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ApproveSPOrdersCloseoutViaPurview))
        val availableKeys = SPOrdersCloseoutCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ApproveSPOrdersCloseoutViaPurview:${nonAdminUser.networkId}")
      }

      "ApproveSPOrdersCloseoutOnBehalfOfViaSystemUser return networkId" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ApproveSPOrdersCloseoutViaSystemUser))
        val availableKeys = SPOrdersCloseoutCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ApproveSPOrdersCloseoutViaSystemUser")
      }

      "ApproveSPOrdersCloseoutViaIssuerNetworkTypeAndEditPayoffEntitlements return all entitlements" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ApproveSPOrdersCloseoutViaIssuerNetworkTypeAndEditPayoffEntitlements))
        when(nonAdminUser.networkTypes).thenReturn(Some(List(NetworkType.Issuer, NetworkType.Carrier)))
        when(nonAdminUser.payoffEntitlementsV2).thenReturn(
          Map(
            "bank" -> Map("a" -> Set(Action(action = "edit", contractParams = Map("a" -> Set("x", "y"))),
              Action(action = "b", contractParams = Map("c" -> Set("1", "2"))))),
            "bank2" -> Map("b" -> Set(Action(action = "edit", contractParams = Map("a" -> Set("x", "y")))))
          )
        )
        val availableKeys = SPOrdersCloseoutCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set("approveSPOrdersCloseoutViaIssuerNetworkTypeAndEditPayoffEntitlements:ISSUER:bank:a",
          "approveSPOrdersCloseoutViaIssuerNetworkTypeAndEditPayoffEntitlements:ISSUER:bank2:b")
      }

    }
  }
}
