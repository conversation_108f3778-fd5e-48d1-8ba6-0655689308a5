package com.simonmarkets.capabilities

import org.scalatest.{Matchers, WordSpec}


class CapabilitySetSpec extends WordSpec with Matchers {


  val AnnuityCapability: Capability = Capability("annuityCapability", "", Some(Seq("Annuities")))
  val SICapability: Capability = Capability("siCapability", "", Some(Seq("SIs")))
  val InsuranceCapability: Capability = Capability("insuranceCapability", "", Some(Seq("Insurance")))
  val MultiAssetCapability: Capability = Capability("multiAssetCapability", "", Some(Seq("SIs","Annuities", "Insurance")))
  val SetOfCapabilities: CapabilitySet = CapabilitySet(Set(AnnuityCapability, SICapability, InsuranceCapability, MultiAssetCapability))
  val EmptyCapabilitySet: CapabilitySet = CapabilitySet.empty


  "SetOfCapabilities" when {
    "Getting si capabilities from capability set" should {
      "return a set containing SICapability and MultiAssetCapability" in {
        val siCapabilities = SetOfCapabilities.getCapabilitiesByAssetClass("SIs")
        siCapabilities shouldBe Set(SICapability, MultiAssetCapability)
      }
    }

    "Getting annuities capabilities from capability set" should {
      "return a set containing AnnuityCapability and MultiAssetCapability" in {
        val annuitiesCapabilities = SetOfCapabilities.getCapabilitiesByAssetClass("Annuities")
        annuitiesCapabilities shouldBe Set(AnnuityCapability, MultiAssetCapability)
      }
    }

    "Getting insurance capabilities from capability set" should {
      "return a set containing InsuranceCapability and MultiAssetCapability" in {
        val insuranceCapabilities = SetOfCapabilities.getCapabilitiesByAssetClass("Insurance")
        insuranceCapabilities shouldBe Set(InsuranceCapability, MultiAssetCapability)
      }
    }

    "Get capabilities from capability set without matching asset class" should {
      "return an empty Set" in {
        val noCapabilities = SetOfCapabilities.getCapabilitiesByAssetClass("SMAs")
        noCapabilities shouldBe Set()
      }
    }

    "Get capabilities from empty capability set" should {
      "return an empty Set" in {
        val noCapabilities = EmptyCapabilitySet.getCapabilitiesByAssetClass("SIs")
        noCapabilities shouldBe Set()
      }
    }

    "Getting capability names from capability set" should {
      "return Set[String] containing capability names" in {
        val capabilityNames = SetOfCapabilities.toCapabilityNames
        capabilityNames shouldBe Set(AnnuityCapability.name, SICapability.name, InsuranceCapability.name, MultiAssetCapability.name)
      }
    }
  }
}
