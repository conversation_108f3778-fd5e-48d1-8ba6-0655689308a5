package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.entitlements.AvailableKeyBuilder
import org.mockito.Mockito.when
import org.scalatest.words
import org.scalatest.mockito.MockitoSugar
import org.scalatest._

import java.time.format.DateTimeFormatter

class AltsProductsAvailableAccessKeyGeneratorSpec
    extends WordSpec
    with MockitoSugar
    with Matchers {

  "getAvailableAccessKeysForCapability" should {

    val random = java.time.LocalDateTime
      .now()
      .format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
    val adminKeys = Set(s"admin_${random}")
    val editKeys = Set(s"edit_${random}")
    val viewKeys = Set(s"view_${random}")
    val adminKeyBuilder = AvailableKeyBuilder((capability, user) => {
      adminKeys
    })
    val viewKeyBuilder = AvailableKeyBuilder((capability, user) => {
      viewKeys
    })
    val editKeyBuilder = AvailableKeyBuilder((capability, user) => {
      editKeys
    })
    val underTest: AltsProductsAvailableAccessKeyGenerator =
      AltsProductsAvailableAccessKeyGenerator(
        adminKeyBuilder,
        viewKeyBuilder,
        editKeyBuilder
      )

    "capabilityToAvailableKeyBuilders" in {
      underTest.capabilityToAvailableKeyBuilders shouldBe Map(
        Admin -> adminKeyBuilder,
        AltsProductCapabilities.ViewAltProductViaViewPayoffEntitlement -> viewKeyBuilder,
        AltsProductCapabilities.EditAltProductViaEditPayoffEntitlement -> editKeyBuilder
      )
    }
  }

}
