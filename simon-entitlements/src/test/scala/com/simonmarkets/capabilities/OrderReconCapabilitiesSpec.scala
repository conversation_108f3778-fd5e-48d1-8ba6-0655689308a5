package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.OrderReconCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar

class OrderReconCapabilitiesSpec extends WordSpec
  with Matchers
  with StandardCapabilitiesBehavior
  with MockitoSugar
  with BeforeAndAfter
  with CapabilitiesTestData {
  "OrderReconCapabilities" can {
    "For Non Admin Users" should {
      "return network keys for ViewOrderReconViaNetwork" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewOrderReconViaNetwork))
        val availableKeys = OrderReconCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewOrderReconViaNetwork:network")
      }

      "return network keys for EditOrderReconViaNetwork" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditOrderReconViaNetwork))
        val availableKeys = OrderReconCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$EditOrderReconViaNetwork:network")
      }

      "return Owner keys for ViewOrderReconViaOwner" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewOrderReconViaOwner))
        val availableKeys = OrderReconCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewOrderReconViaOwner:nonAdminUser")
      }

      "return Owner keys for EditOrderReconViaOwner" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditOrderReconViaOwner))
        val availableKeys = OrderReconCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$EditOrderReconViaOwner:nonAdminUser")
      }
    }

    "For Admin user" should {
      "return admin key" in {
        val availableKeys = OrderReconCapabilities.getAvailableAccessKeys(adminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(Admin)
      }
    }
    "have 7 capabilities" in {
      OrderReconCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 7
    }
  }
}
