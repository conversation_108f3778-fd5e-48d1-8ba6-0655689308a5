package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.entitlements.AvailableKeyBuilder
import org.mockito.Mockito.when
import org.scalatest.words
import org.scalatest.mockito.MockitoSugar
import org.scalatest._

class AltsProductCapabilitiesSpec
    extends WordSpec
    with MockitoSugar
    with Matchers {

  "ViewCapabilities" should {
    "be viewAltProductViaViewPayoffEntitlement and admin" in {
      AltsProductCapabilities.ViewCapabilities shouldBe Set(
        Admin,
        "viewAltProductViaViewPayoffEntitlement"
      )
    }
  }
  "EditCapabilities" should {
    "be editAltProductViaEditPayoffEntitlement and admin" in {
      AltsProductCapabilities.EditCapabilities shouldBe Set(
        Admin,
        "editAltProductViaEditPayoffEntitlement"
      )
    }
  }
  "toSet" should {
    "be view and edit" in {
      AltsProductCapabilities.toSet shouldBe AltsProductCapabilities.EditCapabilities ++ AltsProductCapabilities.ViewCapabilities
    }
  }
}
