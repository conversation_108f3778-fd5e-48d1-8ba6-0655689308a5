package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.DeveloperHubCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class DeveloperHubCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {

  "DeveloperHubCapabilities" when {

    "have all capabilities" in {
      DeveloperHubCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 6
    }

    "Admin User" should {
      "return admin key" in {
        val availableKeys = DeveloperHubCapabilities.getAvailableAccessKeys(adminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(Admin)
      }
    }

    "Non Admin User" should {
      "ViewDeveloperHubServiceViaGuid returns guid keys when user has capability" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewDeveloperHubServiceViaGuid))
        val availableKeys = DeveloperHubCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewDeveloperHubServiceViaGuid:nonAdminUser")
      }
      "ViewDeveloperHubRouteViaGuid returns guid keys when user has capability" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewDeveloperHubRouteViaGuid))
        val availableKeys = DeveloperHubCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewDeveloperHubRouteViaGuid:nonAdminUser")
      }
      "ViewDeveloperHubUserPreferencesViaGuid returns guid keys when user has capability" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewDeveloperHubUserPreferencesViaGuid))
        val availableKeys = DeveloperHubCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewDeveloperHubUserPreferencesViaGuid:nonAdminUser")
      }
      "EditDeveloperHubUserPreferencesViaGuid returns guid keys when user has capability" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditDeveloperHubUserPreferencesViaGuid))
        val availableKeys = DeveloperHubCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$EditDeveloperHubUserPreferencesViaGuid:nonAdminUser")
      }
      "ViewDeveloperHubUserSettingsViaGuid returns guid keys when user has capability" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewDeveloperHubUserSettingsViaGuid))
        val availableKeys = DeveloperHubCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewDeveloperHubUserSettingsViaGuid:nonAdminUser")
      }
    }

  }
}
