package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.ClientOrderCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar

class ClientOrderCapabilitiesSpec extends WordSpec
  with Matchers
  with StandardCapabilitiesBehavior
  with MockitoSugar
  with BeforeAndAfter
  with CapabilitiesTestData {

  "ClientOrderCapabilities" can {
    "For Non Admin Users" should {
      "return network keys for ViewClientOrdersViaNetwork" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewClientOrdersViaNetwork))
        val availableKeys = ClientOrderCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewClientOrdersViaNetwork:network")
      }

      "return network keys for EditClientOrdersViaNetwork" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditClientOrdersViaNetwork))
        val availableKeys = ClientOrderCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$EditClientOrdersViaNetwork:network")
      }

      "return guid keys for ViewClientOrdersViaOwner" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewClientOrdersViaOwner))
        val availableKeys = ClientOrderCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewClientOrdersViaOwner:nonAdminUser")
      }

      "return guid keys for EditClientOrdersViaOwner" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditClientOrdersViaOwner))
        val availableKeys = ClientOrderCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$EditClientOrdersViaOwner:nonAdminUser")
      }
    }

    "For Admin user" should {
      "return admin key" in {
        val availableKeys = ClientOrderCapabilities.getAvailableAccessKeys(adminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(Admin)
      }
    }

    "have 5 capabilities" in {
      ClientOrderCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 5
    }
  }
}
