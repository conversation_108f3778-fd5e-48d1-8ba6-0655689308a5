package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.AnnuityIllustrationHistoryCapabilities._
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar

class AnnuityIllustrationHistoryCapabilitiesSpec extends WordSpec
  with Matchers with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {

  "AnnuityIllustrationCapabilities" when {

    "Given a Non Admin User" should {
      "return purview keys with ViewIllustrationHistoryViaNetwork" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewIllustrationHistoryViaNetwork))
        val availableKeys = AnnuityIllustrationHistoryCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewIllustrationHistoryViaNetwork:$networkId")
      }
      "return purview keys with ViewIllustrationViaPurview" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewIllustrationHistoryViaPurview))
        val availableKeys = AnnuityIllustrationHistoryCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewIllustrationHistoryViaPurview:$purviewNetwork1",
          s"$ViewIllustrationHistoryViaPurview:$purviewNetwork2")
      }
    }

    "Given an Admin User" should {
      "have admin access keys" in {
        val availableKeys = AnnuityIllustrationHistoryCapabilities.getAvailableAccessKeys(adminUser)
        availableKeys shouldBe Set(Admin)
      }
    }

    "asked for the following" should {
      "return all capabilities" in {
        AnnuityIllustrationHistoryCapabilities.toSet shouldBe Set(ViewIllustrationHistoryViaNetwork, ViewIllustrationHistoryViaPurview, Admin)
      }
      "return all view capabilities" in {
        AnnuityIllustrationHistoryCapabilities.ViewCapabilities shouldBe Set(ViewIllustrationHistoryViaNetwork, ViewIllustrationHistoryViaPurview, Admin)
      }
      "return the domain name" in {
        AnnuityIllustrationHistoryCapabilities.DomainName shouldBe "AnnuityIllustrationHistory"
      }
    }
  }
}
