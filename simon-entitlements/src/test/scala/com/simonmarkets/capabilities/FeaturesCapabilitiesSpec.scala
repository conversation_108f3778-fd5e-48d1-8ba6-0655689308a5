package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.FeatureCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.{Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar


class FeaturesCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with CapabilitiesTestData {

  "FeatureCapabilities" should {
    "For Non Admin Users" should {
      "ViewFeatureViaNetworkCapability returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewFeatureViaNetwork))
        val availableKeys = FeatureCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewFeatureViaNetwork:$networkId")
      }

      "EditFeatureViaNetworkCapability returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditFeatureViaNetwork))
        val availableKeys = FeatureCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$EditFeatureViaNetwork:$networkId")
      }
    }

    "For Admin user should return admin key" in {
      val availableKeys = FeatureCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "have 3 capabilities" in {
      FeatureCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 3
    }
  }

}
