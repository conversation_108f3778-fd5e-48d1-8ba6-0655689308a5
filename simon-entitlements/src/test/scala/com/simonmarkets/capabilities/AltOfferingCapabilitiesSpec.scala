package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.NetworkType.{BrokerDealer, Issuer}
import com.goldmansachs.marquee.pipg.{IssuerPurview, Network, PurviewedDomain, UserACL}
import com.simonmarkets.capabilities.Capabilities.Admin
import org.mockito.Mockito.{reset, when}
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar
import simon.Id.NetworkId
import com.simonmarkets.capabilities.AltOfferingCapabilities._
import com.simonmarkets.capabilities.AllOfferingsCapabilities.availableAccessKeysGen


class AltOfferingCapabilitiesSpec extends WordSpec with Matchers with StandardCapabilitiesBehavior with MockitoSugar with BeforeAndAfter {

  private val adminUser = mock[UserACL]
  private val nonAdminUser = mock[UserACL]

  private val adminUserId = "adminUserId"
  private val nonAdminUserId = "nonAdminUserId"
  private val networkId = "SIMON Admin"
  private val network = NetworkId(networkId)

  private val purviewNetworkId1 = "purviewNetworkId1"
  private val purviewNetwork1 = NetworkId(purviewNetworkId1)
  private val purviewNetworkId2 = "purviewNetworkId2"
  private val purviewNetwork2 = NetworkId(purviewNetworkId2)

  private val issuerKey1 = "GS"
  private val issuerKey2 = "CS"

  before {
    reset(adminUser)
    reset(nonAdminUser)

    when(adminUser.userId).thenReturn(adminUserId)
    when(adminUser.networkId).thenReturn(network)
    when(adminUser.capabilities).thenReturn(Set(Admin))

    when(nonAdminUser.userId).thenReturn(nonAdminUserId)
    when(nonAdminUser.networkId).thenReturn(network)
    when(nonAdminUser.capabilities).thenReturn(Set.empty[String])
    when(nonAdminUser.userPurviewIds).thenReturn(Set(purviewNetwork1, purviewNetwork2))
    when(nonAdminUser.issuerPurviewIds).thenReturn(Set(IssuerPurview(purviewNetwork1, Set(issuerKey1, issuerKey2), None, Some(Set(PurviewedDomain.Users))),
      IssuerPurview(purviewNetwork2, Set(issuerKey1, issuerKey2))))
    when(nonAdminUser.payoffEntitlementsV2).thenReturn(Map(
      issuerKey1 -> Map(
        ALT_OFFERING_PAYOFF_TYPE -> Set(
          Network.Action("view"),
          Network.Action("trade")
        )
      )
    ))
  }

  "AltOfferingCapabilities" should {
    behave like standardCapabilities(AltOfferingCapabilities)

    
    "ViewApprovedAltOfferingViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedAltOfferingViaNetwork))
      availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewApprovedAltOfferingViaNetwork, nonAdminUser) shouldBe
        Set(s"$ViewApprovedAltOfferingViaNetwork:$networkId")
    }

    "ViewApprovedAltOfferingViaNetworkAndViewPayoffEntitlement returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedAltOfferingViaNetworkAndViewPayoffEntitlement))
      availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewApprovedAltOfferingViaNetworkAndViewPayoffEntitlement, nonAdminUser) shouldBe
        Set(s"$ViewApprovedAltOfferingViaNetworkAndViewPayoffEntitlement:$networkId:$issuerKey1:$ALT_OFFERING_PAYOFF_TYPE")
    }

    "ViewApprovedAltOfferingViaPurviewAndViewPayoffEntitlement returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedAltOfferingViaPurviewAndViewPayoffEntitlement))
      availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewApprovedAltOfferingViaPurviewAndViewPayoffEntitlement, nonAdminUser) shouldBe
        Set(s"$ViewApprovedAltOfferingViaPurviewAndViewPayoffEntitlement:$purviewNetwork1:$issuerKey1:$ALT_OFFERING_PAYOFF_TYPE",
          s"$ViewApprovedAltOfferingViaPurviewAndViewPayoffEntitlement:$purviewNetwork2:$issuerKey1:$ALT_OFFERING_PAYOFF_TYPE")
    }

    "TradeAltOfferingViaNetworkAndTradePayoffEntitlement returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(TradeAltOfferingViaNetworkAndTradePayoffEntitlement))
      availableAccessKeysGen.getAvailableAccessKeysForCapability(TradeAltOfferingViaNetworkAndTradePayoffEntitlement, nonAdminUser) shouldBe
        Set(s"$TradeAltOfferingViaNetworkAndTradePayoffEntitlement:$networkId:$issuerKey1:$ALT_OFFERING_PAYOFF_TYPE")
    }

    "EditAltOfferingViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(EditAltOfferingViaNetwork))
      availableAccessKeysGen.getAvailableAccessKeysForCapability(EditAltOfferingViaNetwork, nonAdminUser) shouldBe
        Set(s"$EditAltOfferingViaNetwork:$networkId")
    }

    "ViewApprovedAltOfferingViaPurview returns purview keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewApprovedAltOfferingViaPurview))
      availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewApprovedAltOfferingViaPurview, nonAdminUser) shouldBe
        Set(s"$ViewApprovedAltOfferingViaPurview:$purviewNetwork1",
          s"$ViewApprovedAltOfferingViaPurview:$purviewNetwork2")
    }

    "EditAltOfferingViaPurview returns purview keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(EditAltOfferingViaPurview))
      availableAccessKeysGen.getAvailableAccessKeysForCapability(EditAltOfferingViaPurview, nonAdminUser) shouldBe
        Set(s"$EditAltOfferingViaPurview:$purviewNetwork1",
          s"$EditAltOfferingViaPurview:$purviewNetwork2")
    }

    "ViewAltOfferingViaProductIssuerNetworkType returns product issuer network type keys for product issuer" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewAltOfferingViaProductIssuerNetworkType))
      when(nonAdminUser.networkTypes).thenReturn(Some(List(Issuer)))
      availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewAltOfferingViaProductIssuerNetworkType, nonAdminUser) shouldBe
        Set(s"$ViewAltOfferingViaProductIssuerNetworkType")
    }

    "ViewAltOfferingViaProductIssuerNetworkType returns no keys for non product issuer" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewAltOfferingViaProductIssuerNetworkType))
      when(nonAdminUser.networkTypes).thenReturn(Some(List(BrokerDealer)))
      availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewAltOfferingViaProductIssuerNetworkType, nonAdminUser) shouldBe
        Set.empty[String]
    }
  }
}
