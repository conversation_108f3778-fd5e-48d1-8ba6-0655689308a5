package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.{IssuerPurview, UserACL}
import com.simonmarkets.capabilities.AltAccountsCapabilities._
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator
import com.simonmarkets.networks.ExternalId
import org.mockito.Mockito.{reset, when}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import simon.Id
import simon.Id.NetworkId

class AltAccountsCapabilitiesSpec extends WordSpec
  with Matchers
  with MockitoSugar
  with BeforeAndAfter
  with StandardCapabilitiesBehavior
  with NonAdminAvailableAccessKeysGeneratorBehavior
  with NonAdminAcceptedAccessKeysGeneratorBehavior
  with AdminAcceptedAccessKeysGeneratorBehavior
  with AdminAvailableAccessKeysGeneratorBehavior {

  implicit def capabilities: Capabilities = AltAccountsCapabilities

  implicit val availableAccessKeys: AvailableAccessKeysGenerator = AltAccountsAvailableAccessKeys

  val nonAdminUser: UserACL = mock[UserACL]

  val purviewNetwork1: Id.NetworkId.Type = NetworkId("purviewNetwork1")
  val purviewNetwork2: Id.NetworkId.Type = NetworkId("purviewNetwork2")

  before {
    reset(nonAdminUser)

    when(nonAdminUser.userId).thenReturn("nonAdminUser")
    when(nonAdminUser.networkId).thenReturn(NetworkId("network"))
    when(nonAdminUser.capabilities).thenReturn(Set.empty[String])
    when(nonAdminUser.issuerPurviewIds).thenReturn(Set.empty[IssuerPurview])
    when(nonAdminUser.userPurviewIds).thenReturn(Set(purviewNetwork1, purviewNetwork2))
    when(nonAdminUser.locations).thenReturn(Set("location1", "location2"))
    when(nonAdminUser.allAccessibleLocations).thenReturn(Set("location1", "location3"))
    when(nonAdminUser.faNumbers).thenReturn(Set("faNumber1", "faNumber2"))
    when(nonAdminUser.distributorId).thenReturn(Some("<EMAIL>"))
    when(nonAdminUser.externalIds).thenReturn(Some(Seq(ExternalId("subject_1", "id_1"))))
  }

    "AltAccountsAvailableAccessKeys" should {
      behave like nonAdminAccessKeysGenerator(AltAccountsAvailableAccessKeys)
    }

    "AltAccountsCapabilities" should {
      behave like standardCapabilities

      "Given a user which does not have Admin capability then Alt Accounts Available Key Generation" should {
        "ViewAltAccountViaPurview returns purview keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltAccountViaPurviewCapability.name))
          AltAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltAccountViaPurviewCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltAccountViaPurviewCapability.name}:purviewNetwork1", s"${ViewAltAccountViaPurviewCapability.name}:purviewNetwork2")
        }

        "ViewAltAccountViaNetwork returns network keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltAccountViaNetworkCapability.name))
          AltAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltAccountViaNetworkCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltAccountViaNetworkCapability.name}:network")
        }

        "ViewAltAccountViaFaNumber returns fa number keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltAccountViaFaNumberCapability.name))
          AltAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltAccountViaFaNumberCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltAccountViaFaNumberCapability.name}:network:faNumber1", s"${ViewAltAccountViaFaNumberCapability.name}:network:faNumber2")
        }

        "ViewAltAccountViaLocation returns location keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltAccountViaLocationCapability.name))
          AltAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltAccountViaLocationCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltAccountViaLocationCapability.name}:network:location1", s"${ViewAltAccountViaLocationCapability.name}:network:location2")
        }

        "ViewAltAccountViaLocationHierarchy returns location keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltAccountViaLocationHierarchyCapability.name))
          AltAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltAccountViaLocationHierarchyCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltAccountViaLocationHierarchyCapability.name}:network:location1", s"${ViewAltAccountViaLocationHierarchyCapability.name}:network:location3")
        }

        "ViewAltAccountViaDistributorId returns advisor keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltAccountViaDistributorIdCapability.name))
          AltAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltAccountViaDistributorIdCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltAccountViaDistributorIdCapability.name}:network:<EMAIL>")
        }

        "ViewAltAccountViaExternalId returns external id keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltAccountViaUserExternalIdCapability.name))
          AltAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltAccountViaUserExternalIdCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltAccountViaUserExternalIdCapability.name}:subject_1:id_1")
        }
      }

      "Given a user which only has Admin capability then Accounts Available Key Generation" should {

        "ViewAltAccountViaNetwork returns network keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltAccountViaNetworkCapability.name))
          AltAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltAccountViaNetworkCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltAccountViaNetworkCapability.name}:network")
        }

        "ViewAltAccountViaFaNumber returns fa number keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltAccountViaFaNumberCapability.name))
          AltAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltAccountViaFaNumberCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltAccountViaFaNumberCapability.name}:network:faNumber1", s"${ViewAltAccountViaFaNumberCapability.name}:network:faNumber2")
        }

        "ViewAltAccountViaLocation returns location keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltAccountViaLocationCapability.name))
          AltAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltAccountViaLocationCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltAccountViaLocationCapability.name}:network:location1", s"${ViewAltAccountViaLocationCapability.name}:network:location2")
        }

        "ViewAltAccountViaLocationHierarchy returns location keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltAccountViaLocationHierarchyCapability.name))
          AltAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltAccountViaLocationHierarchyCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltAccountViaLocationHierarchyCapability.name}:network:location1", s"${ViewAltAccountViaLocationHierarchyCapability.name}:network:location3")
        }

        "ViewAltAccountViaDistributorId returns advisor keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltAccountViaDistributorIdCapability.name))
          AltAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltAccountViaDistributorIdCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltAccountViaDistributorIdCapability.name}:network:<EMAIL>")
        }

        "ViewAltAccountViaExternalId returns external id keys" in {
          when(nonAdminUser.capabilities).thenReturn(Set(ViewAltAccountViaUserExternalIdCapability.name))
          AltAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewAltAccountViaUserExternalIdCapability.name, nonAdminUser) shouldBe
            Set(s"${ViewAltAccountViaUserExternalIdCapability.name}:subject_1:id_1")
        }
      }

    }
}
