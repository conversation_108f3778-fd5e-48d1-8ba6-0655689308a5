package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.{Network, UserACL}
import com.simonmarkets.capabilities.LifecycleEventsCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}

class LifecycleEventsCapabilitiesSpec extends WordSpec with MockitoSugar with Matchers with CapabilitiesTestData {
  override val nonAdminUser: UserACL = mock[UserACL]
  when(nonAdminUser.networkId).thenReturn(networkId)

  "payoff entitlements v2 used for lifecycle events" in {
    val payoffEntitlements = Map(
      "PRU" -> Map("PointToPoint" -> Set(Network.Action("trade"), Network.Action("edit"), Network.Action("view"),
        Network.Action("build"), Network.Action("illustrate"), Network.Action("performance"))),
      "CITI" -> Map("BufferedLeveredNote" -> Set(Network.Action("view"))),
      "BCS" -> Map("Autocallable" -> Set(Network.Action("trade")))
    )

    when(nonAdminUser.payoffEntitlementsV2).thenReturn(payoffEntitlements)
    when(nonAdminUser.capabilities).thenReturn(Set(ViewLifecycleEventViaPayOffCapability.name,
      EditLifecycleEventViaPayOffCapability.name))

    lifecycleEventsAvailableAccessKeysGen.getAvailableAccessKeysForCapability(ViewLifecycleEventViaPayOff,
      nonAdminUser) shouldBe Set(s"$ViewLifecycleEventViaPayOff:PRU:PointToPoint",
      s"$ViewLifecycleEventViaPayOff:CITI:BufferedLeveredNote")

    lifecycleEventsAvailableAccessKeysGen.getAvailableAccessKeysForCapability(EditLifecycleEventViaPayOffCapability.name,
      nonAdminUser) shouldBe Set(s"$EditLifecycleEventViaPayOff:PRU:PointToPoint")
  }

  "payoff entitlements v2 should be ignored if user has admin capability" in {
    lifecycleEventsAvailableAccessKeysGen.getAvailableAccessKeys(adminUser) shouldBe Set(Capabilities.Admin)
  }

  "payoff entitlements will be used used for lifecycle events when payoff entitlements v2 is not set" in {
    val payoffEntitlements = Map(
      "PRU" -> Map("PointToPoint" -> List("trade", "edit", "view", "build", "illustrate", "performance")),
      "CITI" -> Map("BufferedLeveredNote" -> List("view")),
      "BCS" -> Map("Autocallable" -> List("trade"))
    )

    when(nonAdminUser.payoffEntitlementsV2).thenReturn(Map.empty[String, Map[String, Set[Network.Action]]])
    when(nonAdminUser.payoffEntitlements).thenReturn(payoffEntitlements)
    when(nonAdminUser.capabilities).thenReturn(Set(ViewLifecycleEventViaPayOff, EditLifecycleEventViaPayOff))

    lifecycleEventsAvailableAccessKeysGen.getAvailableAccessKeysForCapability(ViewLifecycleEventViaPayOff,
      nonAdminUser) shouldBe Set(s"$ViewLifecycleEventViaPayOff:PRU:PointToPoint",
      s"$ViewLifecycleEventViaPayOff:CITI:BufferedLeveredNote")

    lifecycleEventsAvailableAccessKeysGen.getAvailableAccessKeysForCapability(EditLifecycleEventViaPayOffCapability.name,
      nonAdminUser) shouldBe Set(s"$EditLifecycleEventViaPayOff:PRU:PointToPoint")
  }
}
