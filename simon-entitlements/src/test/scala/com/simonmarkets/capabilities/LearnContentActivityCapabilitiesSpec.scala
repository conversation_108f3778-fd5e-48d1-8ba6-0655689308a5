package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.LearnContentActivityCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class LearnContentActivityCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {

  "LearnAttestationCapabilities" should {
    "For Non Admin Users" should {
      "ViewContentActivityViaPurview return purview keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewContentActivityViaPurview))
        val availableKeys = LearnContentActivityCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(s"$ViewContentActivityViaPurview:$purviewNetwork1",
          s"$ViewContentActivityViaPurview:$purviewNetwork2")
      }

      "ViewContentActivityViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewContentActivityViaNetwork))
        val availableKeys = LearnContentActivityCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewContentActivityViaNetwork:$networkId")
      }

      "ViewContentActivityViaNetwork returns location keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewContentActivityViaLocation))
        val availableKeys = LearnContentActivityCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewContentActivityViaLocation:$networkId:$location")
      }

      "ViewContentActivityViaOwner returns guid keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewContentActivityViaOwner))
        val availableKeys = LearnContentActivityCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewContentActivityViaOwner:nonAdminUser")
      }

      "EditContentActivityViaOwner returns guid keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditContentActivityViaOwner))
        val availableKeys = LearnContentActivityCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$EditContentActivityViaOwner:nonAdminUser")
      }
    }
    "For Admin user should return admin key" in {
      val availableKeys = LearnContentActivityCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "have 8 capabilities" in {
      LearnContentActivityCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 8
    }

    "have 6 viewActivity capabilities" in {
      LearnContentActivityCapabilities.ViewActivityCapabilities shouldBe Set(Admin, ViewContentActivityViaOwner,
        ViewContentActivityViaLocation, ViewContentActivityViaPurview, ViewContentActivityViaNetwork, ViewContentActivityViaNetworkTypeCapability.name)
    }

    "have 3 edit activity capability" in {
      LearnContentActivityCapabilities.EditActivityCapabilities shouldBe Set(Admin, EditContentActivityViaOwner, EditContentActivityViaNetworkTypeCapability.name)
    }
  }

}
