package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.UnifiedHoldingsCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class UnifiedHoldingsCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {

  "UnifiedHoldingsCapabilities" should {

    "produce network-based keys for ViewUnifiedHoldingViaNetwork" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewUnifiedHoldingViaNetwork))
      val availableKeys = UnifiedHoldingsCapabilities.getAvailableAccessKeys(nonAdminUser)
      availableKeys shouldBe Set(s"$ViewUnifiedHoldingViaNetwork:$networkId")
    }

    "have the right capabilities defined" in {
      UnifiedHoldingsCapabilities.ViewCapabilities shouldBe Set(ViewUnifiedHoldingViaNetwork)
      UnifiedHoldingsCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 1
    }
  }

}
