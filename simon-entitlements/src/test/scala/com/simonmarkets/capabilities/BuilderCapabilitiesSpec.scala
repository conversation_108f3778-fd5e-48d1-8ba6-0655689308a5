package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.{reset, when}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class BuilderCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {
  val nonAdminUserNoAccess: UserACL = mock[UserACL]
  val nonAdminUserWithAccess: UserACL = mock[UserACL]
  val fakeCapability1: String = "FakeCapability1"
  val fakeCapability2: String = "FakeCapability2"

  before {
    reset(nonAdminUserNoAccess)
    when(nonAdminUserNoAccess.userId).thenReturn("nonAdminUserNoAccess")
    when(nonAdminUserNoAccess.networkId).thenReturn(networkId)
    when(nonAdminUserNoAccess.capabilities).thenReturn(Set(fakeCapability1, fakeCapability2))

    reset(nonAdminUserWithAccess)
    when(nonAdminUserWithAccess.userId).thenReturn("nonAdminUserWithAccess")
    when(nonAdminUserWithAccess.networkId).thenReturn(networkId)
    when(nonAdminUserWithAccess.capabilities).thenReturn(Set(fakeCapability2, BuilderCapabilities.BuildExperimentalProducts))

  }

  "BuilderCapabilities" should {
    "Allow Admin to have Admin Key" in {
      val availableKeys = BuilderCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys shouldBe Set(Admin)
    }

    "Allow Non-Admin with Access" in {
      val availableKeys = BuilderCapabilities.getAvailableAccessKeys(nonAdminUserWithAccess)
      availableKeys shouldBe Set(BuilderCapabilities.BuildExperimentalProducts)
    }

    "Not Allow Non-Admin with No Access" in {
      val availableKeys = BuilderCapabilities.getAvailableAccessKeys(nonAdminUserNoAccess)
      availableKeys.isEmpty shouldBe true
    }

    "have 2 capabilities" in {
      BuilderCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 2
    }

  }

}
