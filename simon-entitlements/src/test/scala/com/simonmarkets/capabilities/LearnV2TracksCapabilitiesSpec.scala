package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.LearnTrack
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.LearnV2TracksCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class LearnV2TracksCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {

  before {
    when(nonAdminUser.learnTracksV2).thenReturn(Seq(
      LearnTrack("track1", isActive = true),
      LearnTrack("track2", isActive = false),
      LearnTrack("track3", isActive = true)
    ))
  }

  "TracksCapabilities" should {
    "have 5 capabilities" in {
      LearnV2TracksCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 5
    }

    "For Admin user should return admin key" in {
      val availableKeys = LearnV2TracksCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "For Non Admin Users" should {
      "ViewActiveTracksViaTrackId return track keys when user has the capability" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewActiveTracksViaTrackId))
        val availableKeys = LearnV2TracksCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewActiveTracksViaTrackId:track1", s"$ViewActiveTracksViaTrackId:track3")
      }

      "ViewActiveTracksViaTrackId return no keys when user does not have ViewActiveTracksViaTrackId capability" in {
        when(nonAdminUser.capabilities).thenReturn(Set.empty[String])
        val availableKeys = LearnV2TracksCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0
      }

      "ViewActiveTracksViaTrackId return no keys when network has no tracks associated" in {
        when(nonAdminUser.learnTracksV2).thenReturn(Seq.empty[LearnTrack])
        val availableKeys = LearnV2TracksCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0
      }

      "ViewAllTracksViaTrackId return track keys when user has the capability" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewAllTracksViaTrackId))
        val availableKeys = LearnV2TracksCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewAllTracksViaTrackId:track1", s"$ViewAllTracksViaTrackId:track2", s"$ViewAllTracksViaTrackId:track3")
      }
    }
  }
}
