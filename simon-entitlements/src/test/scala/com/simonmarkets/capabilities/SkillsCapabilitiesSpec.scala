package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, DefaultCapability}
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, WordSpec}


class SkillsCapabilitiesSpec extends WordSpec with StandardCapabilitiesBehavior with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {

  "SkillsCapabilities" should {
    behave like standardCapabilities(SkillsCapabilities)

    "DefaultCapability returns default key" in {
      when(nonAdminUser.capabilities).thenReturn(Set(DefaultCapability))
      SkillsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(DefaultCapability, nonAdminUser) shouldBe
        Set(DefaultCapability)
    }

    "Admin returns default key" in {
      SkillsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(Admin, adminUser) shouldBe
        Set(Admin)
    }
  }
}
