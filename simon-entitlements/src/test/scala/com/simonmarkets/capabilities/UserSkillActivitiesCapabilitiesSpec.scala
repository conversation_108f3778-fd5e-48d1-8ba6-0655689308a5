package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.UserSkillActivitiesCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}


class UserSkillActivitiesCapabilitiesSpec
  extends WordSpec
    with Matchers
    with StandardCapabilitiesBehavior
    with MockitoSugar
    with BeforeAndAfter
    with CapabilitiesTestData {

  "UserSkillActivitiesCapabilities" should {
    behave like standardCapabilities(UserSkillActivitiesCapabilities)

    "ViewUserSkillActivityViaOwner returns fa number keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewUserSkillActivityViaOwner))
      UserSkillActivitiesCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewUserSkillActivityViaOwner, nonAdminUser) shouldBe
        Set(s"$ViewUserSkillActivityViaOwner:${nonAdminUser.userId}")
    }

    "ViewUserSkillActivityViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewUserSkillActivityViaNetwork))
      UserSkillActivitiesCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewUserSkillActivityViaNetwork, nonAdminUser) shouldBe
        Set(s"$ViewUserSkillActivityViaNetwork:$networkId")
    }

    "ViewUserSkillActivityViaPurview returns purview keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewUserSkillActivityViaPurview))
      UserSkillActivitiesCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewUserSkillActivityViaPurview, nonAdminUser) shouldBe
        Set(s"$ViewUserSkillActivityViaPurview:$purviewNetwork1",
          s"$ViewUserSkillActivityViaPurview:$purviewNetwork2")
    }

    "EditUserSkillActivityViaOwner returns owner keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(EditUserSkillActivityViaOwner))
      UserSkillActivitiesCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(EditUserSkillActivityViaOwner, nonAdminUser) shouldBe
        Set(s"$EditUserSkillActivityViaOwner:${nonAdminUser.userId}")
    }

    "EditUserSkillActivityViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(EditUserSkillActivityViaNetwork))
      UserSkillActivitiesCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(EditUserSkillActivityViaNetwork, nonAdminUser) shouldBe
        Set(s"$EditUserSkillActivityViaNetwork:$networkId")
    }

    "EditUserSkillActivityViaPurview returns purview keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(EditUserSkillActivityViaPurview))
      UserSkillActivitiesCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(EditUserSkillActivityViaPurview, nonAdminUser) shouldBe
        Set(s"$EditUserSkillActivityViaPurview:$purviewNetwork1",
          s"$EditUserSkillActivityViaPurview:$purviewNetwork2")
    }
  }
}
