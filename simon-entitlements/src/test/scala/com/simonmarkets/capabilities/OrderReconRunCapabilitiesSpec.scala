package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.OrderReconRunCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class OrderReconRunCapabilitiesSpec extends WordSpec
  with Matchers
  with StandardCapabilitiesBehavior
  with MockitoSugar
  with BeforeAndAfter
  with CapabilitiesTestData {
  "OrderReconRunCapabilities" can {
    "For Non Admin Users" should {
      "return network keys for ViewReconRunViaNetwork" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewReconRunViaNetwork))
        val availableKeys = OrderReconRunCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewReconRunViaNetwork:network")
      }

      "return network keys for EditReconRunViaNetwork" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditReconRunViaNetwork))
        val availableKeys = OrderReconRunCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$EditReconRunViaNetwork:network")
      }

      "return network keys for ViewReconRunViaOwner" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewReconRunViaOwner))
        val availableKeys = OrderReconRunCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewReconRunViaOwner:nonAdminUser")
      }

      "return network keys for EditReconRunViaOwner" in {
        when(nonAdminUser.capabilities).thenReturn(Set(EditReconRunViaOwner))
        val availableKeys = OrderReconRunCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$EditReconRunViaOwner:nonAdminUser")
      }
    }

    "For Admin user" should {
      "return admin key" in {
        val availableKeys = OrderReconRunCapabilities.getAvailableAccessKeys(adminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(Admin)
      }
    }

    "have 7 capabilities" in {
      OrderReconRunCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 7
    }
  }
}
