package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.Custodian.{Cetera, NFS}
import com.goldmansachs.marquee.pipg.{CustodianFaNumber, NetworkCategory, Purview, PurviewEntity, PurviewedDomain}
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.OrderAccountsCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import simon.Id.NetworkId

class OrderAccountsCapabilitiesSpec extends WordSpec
  with CapabilitiesTestData
  with Matchers
  with MockitoSugar
  with BeforeAndAfter
  with StandardCapabilitiesBehavior
  with AdminAvailableAccessKeysGeneratorBehavior
  with AdminAcceptedAccessKeysGeneratorBehavior {

  override implicit def capabilities: Capabilities = OrderAccountsCapabilities

  val purviewEntity1 = PurviewEntity(key = "A", purviewType = Some(NetworkCategory.Issuer), domains = Some(Set(PurviewedDomain.Accounts, PurviewedDomain.Users)))
  val purviewEntity2 = PurviewEntity(key = "B", purviewType = Some(NetworkCategory.Issuer), domains = Some(Set(PurviewedDomain.OrderAccounts)))

  val purview1 = Purview(networkId = "network1", purviewEntities = Set(purviewEntity1))
  val purview2 = Purview(networkId = "network2", purviewEntities = Set(purviewEntity2))
  val purview3 = Purview(networkId = "network3", purviewEntities = Set(purviewEntity2, purviewEntity1))

  before {
    when(nonAdminUser.purviews).thenReturn(Some(Set(purview1, purview2, purview3)))
    when(nonAdminUser.locations).thenReturn(Set("location1", "location2"))
    when(nonAdminUser.allAccessibleLocations).thenReturn(Set("location1", "location3"))
    when(nonAdminUser.faNumbers).thenReturn(Set("faNumber1", "faNumber2"))
    when(nonAdminUser.custodianFaNumbers).thenReturn(Set(CustodianFaNumber(Cetera, "CET123"), CustodianFaNumber(NFS, "NFS234")))
    when(nonAdminUser.distributorId).thenReturn(Some("<EMAIL>"))
  }

  "OrderAccountsAvailableAccessKeys" should {
    behave like adminAccessKeysGenerator(OrderAccountsAvailableAccessKeys)
  }

  "OrderAccountsCapabilities" should {
    behave like standardCapabilities

    "ViewOrderAccountViaPurviewedDomain returns purview keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewOrderAccountViaPurviewedDomain))
      OrderAccountsCapabilities.OrderAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewOrderAccountViaPurviewedDomain, nonAdminUser) shouldBe
        Set(s"$ViewOrderAccountViaPurviewedDomain:network2", s"$ViewOrderAccountViaPurviewedDomain:network3")
    }

    "ViewOrderAccountViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewOrderAccountViaNetwork))
      OrderAccountsCapabilities.OrderAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewOrderAccountViaNetwork, nonAdminUser) shouldBe
        Set(s"$ViewOrderAccountViaNetwork:network")
    }

    "EditOrderAccountViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(EditOrderAccountViaNetwork))
      OrderAccountsCapabilities.OrderAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(EditOrderAccountViaNetwork, nonAdminUser) shouldBe
        Set(s"$EditOrderAccountViaNetwork:network")
    }

    "ViewOrderAccountViaLocation returns location keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewOrderAccountViaLocation))
      OrderAccountsCapabilities.OrderAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewOrderAccountViaLocation, nonAdminUser) shouldBe
        Set(s"$ViewOrderAccountViaLocation:network:location1", s"$ViewOrderAccountViaLocation:network:location2")
    }

    "ViewOrderAccountViaFaNumber returns fa number keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewOrderAccountViaFaNumber))
      OrderAccountsCapabilities.OrderAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewOrderAccountViaFaNumber, nonAdminUser) shouldBe
        Set(s"$ViewOrderAccountViaFaNumber:network:faNumber1", s"$ViewOrderAccountViaFaNumber:network:faNumber2")
    }

    "ViewOrderAccountViaCustodianFaNumber returns custodian fa number keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewOrderAccountViaCustodianFaNumberCapability.name))
      OrderAccountsCapabilities.OrderAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewOrderAccountViaCustodianFaNumberCapability.name, nonAdminUser) shouldBe
        Set("viewOrderAccountViaCustodianFaNumber:network:Cetera:CET123", "viewOrderAccountViaCustodianFaNumber:network:NFS:NFS234")
    }

    "ViewOrderAccountViaLocationHierarchy returns fa number keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewOrderAccountViaLocationHierarchy))
      OrderAccountsCapabilities.OrderAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewOrderAccountViaLocationHierarchy, nonAdminUser) shouldBe
        Set(s"$ViewOrderAccountViaLocationHierarchy:network:location1", s"$ViewOrderAccountViaLocationHierarchy:network:location3")
    }

    "ViewOrderAccountViaDistributorId returns advisor keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewOrderAccountViaDistributorId))
      OrderAccountsCapabilities.OrderAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewOrderAccountViaDistributorId, nonAdminUser) shouldBe
        Set(s"$ViewOrderAccountViaDistributorId:network:<EMAIL>")
    }

    "ViewOrderAccountViaOwner returns userId" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewOrderAccountViaOwner))
      OrderAccountsCapabilities.OrderAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewOrderAccountViaOwner, nonAdminUser) shouldBe
        Set(s"$ViewOrderAccountViaOwner:nonAdminUser")
    }


    "Admin returns false for nonAdmin" in {
      OrderAccountsCapabilities.OrderAccountsAvailableAccessKeys.getAvailableAccessKeysForCapability(Admin, nonAdminUser) shouldBe
        Set.empty
    }

    "Admin returns true for admin view capabilities" in {
      OrderAccountsCapabilities.OrderAccountsAvailableAccessKeys.getAvailableAccessKeysForCapabilities(ViewCapabilities, adminUser) shouldBe
        Set(Admin)
    }

    "Admin returns true for admin edit capabilities" in {
      OrderAccountsCapabilities.OrderAccountsAvailableAccessKeys.getAvailableAccessKeysForCapabilities(EditCapabilities, adminUser) shouldBe
        Set(Admin)
    }

    "SnapshotAvailableAccessKeys" should {
      behave like adminAccessKeysGenerator(SnapshotAvailableAccessKeys)
    }

    "SnapshotAcceptedAccessKeys" should {
      behave like adminAcceptedKeysGenerator(SnapshotAcceptedAccessKeys, Seq(NetworkId("network1"), NetworkId("network2")))
    }
  }

}
