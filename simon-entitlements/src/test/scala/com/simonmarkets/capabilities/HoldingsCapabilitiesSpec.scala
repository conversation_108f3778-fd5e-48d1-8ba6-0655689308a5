package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.Custodian.Cetera
import com.goldmansachs.marquee.pipg.{CustodianFaNumber, IssuerPurview, UserACL}
import com.simonmarkets.capabilities.HoldingsCapabilities._
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator
import com.simonmarkets.networks.ExternalId
import org.mockito.Mockito.{reset, when}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import simon.Id.NetworkId

class HoldingsCapabilitiesSpec extends WordSpec
  with Matchers
  with MockitoSugar
  with BeforeAndAfter
  with StandardCapabilitiesBehavior
  with NonAdminAvailableAccessKeysGeneratorBehavior
  with NonAdminAcceptedAccessKeysGeneratorBehavior
  with AdminAcceptedAccessKeysGeneratorBehavior
  with AdminAvailableAccessKeysGeneratorBehavior {

  implicit def capabilities: Capabilities = HoldingsCapabilities
  implicit val availableAccessKeys: AvailableAccessKeysGenerator = HoldingsAvailableAccessKeys

  val nonAdminUser = mock[UserACL]

  val purviewNetwork1 = NetworkId("purviewNetwork1")
  val purviewNetwork2 = NetworkId("purviewNetwork2")

  before {
    reset(nonAdminUser)

    when(nonAdminUser.userId).thenReturn("nonAdminUser")
    when(nonAdminUser.networkId).thenReturn(NetworkId("network"))
    when(nonAdminUser.capabilities).thenReturn(Set.empty[String])
    when(nonAdminUser.issuerPurviewIds).thenReturn(Set.empty[IssuerPurview])
    when(nonAdminUser.userPurviewIds).thenReturn(Set(purviewNetwork1, purviewNetwork2))
    when(nonAdminUser.locations).thenReturn(Set("location1", "location2"))
    when(nonAdminUser.allAccessibleLocations).thenReturn(Set("location1", "location3"))
    when(nonAdminUser.faNumbers).thenReturn(Set("faNumber1", "faNumber2"))
    when(nonAdminUser.custodianFaNumbers).thenReturn(Set(CustodianFaNumber(Cetera, "123"), CustodianFaNumber(Cetera, "456")))
    when(nonAdminUser.distributorId).thenReturn(Some("<EMAIL>"))
    when(nonAdminUser.externalIds).thenReturn(Some(Seq(ExternalId("subject_1", "id_1"))))

  }

  "HoldingsAvailableAccessKeys" should {
    behave like nonAdminAccessKeysGenerator(HoldingsAvailableAccessKeys)
  }

  "SnapshotAvailableAccessKeys" should {
    behave like adminAccessKeysGenerator(SnapshotAvailableAccessKeys)
  }

  "SnapshotAcceptedAccessKeys" should {
    behave like adminAcceptedKeysGenerator(SnapshotAcceptedAccessKeys, Seq(NetworkId("network1"), NetworkId("network2")))
  }

  "HoldingsCapabilities" should {
    behave like standardCapabilities

    "Given a user which does not have Admin capability then Holdings Available Key Generation" should {
      "ViewHoldingViaPurview returns purview keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewHoldingViaPurview))
        HoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewHoldingViaPurview, nonAdminUser) shouldBe
          Set(s"$ViewHoldingViaPurview:purviewNetwork1", s"$ViewHoldingViaPurview:purviewNetwork2")
      }

      "ViewHoldingViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewHoldingViaNetwork))
        HoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewHoldingViaNetwork, nonAdminUser) shouldBe
          Set(s"$ViewHoldingViaNetwork:network")
      }

      "ViewHoldingViaFaNumber returns fa number keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewHoldingViaFaNumber))
        HoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewHoldingViaFaNumber, nonAdminUser) shouldBe
          Set(s"$ViewHoldingViaFaNumber:network:faNumber1", s"$ViewHoldingViaFaNumber:network:faNumber2")
      }

      "ViewHoldingViaCustodianFaNumber returns custodian fa number keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewHoldingViaCustodianFaNumberCapability.name))
        HoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewHoldingViaCustodianFaNumberCapability.name, nonAdminUser) shouldBe
          Set(s"${ViewHoldingViaCustodianFaNumberCapability.name}:network:Cetera:123", s"${ViewHoldingViaCustodianFaNumberCapability.name}:network:Cetera:456")
      }

      "ViewHoldingViaLocation returns location keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewHoldingViaLocation))
        HoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewHoldingViaLocation, nonAdminUser) shouldBe
          Set(s"$ViewHoldingViaLocation:network:location1", s"$ViewHoldingViaLocation:network:location2")
      }

      "ViewHoldingViaLocationHierarchy returns location keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewHoldingViaLocationHierarchy))
        HoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewHoldingViaLocationHierarchy, nonAdminUser) shouldBe
          Set(s"$ViewHoldingViaLocationHierarchy:network:location1", s"$ViewHoldingViaLocationHierarchy:network:location3")
      }

      "ViewHoldingViaDistributorId returns advisor keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewHoldingViaDistributorId))
        HoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewHoldingViaDistributorId, nonAdminUser) shouldBe
          Set(s"$ViewHoldingViaDistributorId:network:<EMAIL>")
      }

      "ViewHoldingViaExternalId returns external id keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewHoldingViaUserExternalId))
        HoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewHoldingViaUserExternalId, nonAdminUser) shouldBe
          Set(s"$ViewHoldingViaUserExternalId:subject_1:id_1")
      }
    }

    "Given a user which only has Admin capability then Holdings Available Key Generation" should {

      "ViewHoldingViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewHoldingViaNetwork))
        HoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewHoldingViaNetwork, nonAdminUser) shouldBe
          Set(s"$ViewHoldingViaNetwork:network")
      }

      "ViewHoldingViaFaNumber returns fa number keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewHoldingViaFaNumber))
        HoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewHoldingViaFaNumber, nonAdminUser) shouldBe
          Set(s"$ViewHoldingViaFaNumber:network:faNumber1", s"$ViewHoldingViaFaNumber:network:faNumber2")
      }

      "ViewHoldingViaLocation returns location keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewHoldingViaLocation))
        HoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewHoldingViaLocation, nonAdminUser) shouldBe
          Set(s"$ViewHoldingViaLocation:network:location1", s"$ViewHoldingViaLocation:network:location2")
      }

      "ViewHoldingViaLocationHierarchy returns location keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewHoldingViaLocationHierarchy))
        HoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewHoldingViaLocationHierarchy, nonAdminUser) shouldBe
          Set(s"$ViewHoldingViaLocationHierarchy:network:location1", s"$ViewHoldingViaLocationHierarchy:network:location3")
      }

      "ViewHoldingViaDistributorId returns advisor keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewHoldingViaDistributorId))
        HoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewHoldingViaDistributorId, nonAdminUser) shouldBe
          Set(s"$ViewHoldingViaDistributorId:network:<EMAIL>")
      }

      "ViewHoldingViaExternalId returns external id keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewHoldingViaUserExternalId))
        HoldingsAvailableAccessKeys.getAvailableAccessKeysForCapability(ViewHoldingViaUserExternalId, nonAdminUser) shouldBe
          Set(s"$ViewHoldingViaUserExternalId:subject_1:id_1")
      }
    }


  }

}
