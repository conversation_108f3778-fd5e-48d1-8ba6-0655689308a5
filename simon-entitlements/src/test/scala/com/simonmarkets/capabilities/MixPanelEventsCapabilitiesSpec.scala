package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}
import com.simonmarkets.capabilities.MixPanelEventsCapabilities._

class MixPanelEventsCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with CapabilitiesTestData{


  "MixPanelEventsCapabilities" should {
    "For Non Admin Users" should {
      "ViewMixPanelUserEventsViaUser returns guid keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewMixPanelUserEventsViaUser))
        val availableKeys = MixPanelEventsCapabilities.availableAccessKeyGen.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewMixPanelUserEventsViaUser:${nonAdminUser.userId}")
      }

      "ViewMixPanelUserEventsViaNetwork returns network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewMixPanelUserEventsViaNetwork))
        val availableKeys = MixPanelEventsCapabilities.availableAccessKeyGen.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 1
        availableKeys shouldBe Set(s"$ViewMixPanelUserEventsViaNetwork:$networkId")
      }

      "ViewMixPanelUserEventsViaPurview returns purview network keys" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewMixPanelUserEventsViaPurview))
        val availableKeys = MixPanelEventsCapabilities.availableAccessKeyGen.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 2
        availableKeys shouldBe Set(
          s"$ViewMixPanelUserEventsViaPurview:$purviewNetwork1",
          s"$ViewMixPanelUserEventsViaPurview:$purviewNetwork2"
        )
      }
    }

    "For Admin user should return admin key" in {
      val availableKeys = MixPanelEventsCapabilities.availableAccessKeyGen.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "have 4 capabilities" in {
      MixPanelEventsCapabilities.availableAccessKeyGen.capabilityToAvailableKeyBuilders.size shouldBe 4
    }
  }
}
