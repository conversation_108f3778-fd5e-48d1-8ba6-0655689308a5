package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, DefaultCapability}
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, WordSpec}


class LevelsCapabilitiesSpec extends WordSpec with StandardCapabilitiesBehavior with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {

  "LevelsCapabilities" should {
    behave like standardCapabilities(LevelsCapabilities)

    "DefaultCapability returns default key" in {
      when(nonAdminUser.capabilities).thenReturn(Set(DefaultCapability))
      LevelsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(DefaultCapability, nonAdminUser) shouldBe
        Set(DefaultCapability)
    }

    "Admin returns default key" in {
      LevelsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(Admin, adminUser) shouldBe
        Set(Admin)
    }
  }
}
