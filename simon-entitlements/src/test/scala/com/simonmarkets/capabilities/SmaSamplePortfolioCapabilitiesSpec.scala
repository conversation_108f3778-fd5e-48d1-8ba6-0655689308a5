package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.SmaSamplePortfolioCapabilities.ViewSmaSamplePortfolioViaStrategyAndUnderlier
import com.simonmarkets.networks.SmaStrategyAndUnderliers
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class SmaSamplePortfolioCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {

  before {
    when(nonAdminUser.smaStrategiesAndUnderliers).thenReturn(Some(Set(
      SmaStrategyAndUnderliers("123", Set("SPX", "M1EA")),
      SmaStrategyAndUnderliers("456", Set("SPX"))
    )))
  }

  "SmaSamplePortfolioCapabilities" should {
    "have 3 capabilities" in {
      SmaSamplePortfolioCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 3
    }

    "For Admin user should return admin key" in {
      val availableKeys = SmaSamplePortfolioCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "For Non Admin Users" should {
      "ViewSmaSamplePortfolioViaStrategyAndUnderlier return product of strategy and underlier IDs when user has the capability" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewSmaSamplePortfolioViaStrategyAndUnderlier))
        val availableKeys = SmaSamplePortfolioCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewSmaSamplePortfolioViaStrategyAndUnderlier:123:SPX", s"$ViewSmaSamplePortfolioViaStrategyAndUnderlier:123:M1EA", s"$ViewSmaSamplePortfolioViaStrategyAndUnderlier:456:SPX")
      }

      "ViewSmaSamplePortfolioViaStrategyAndUnderlier return no keys when user does not have ViewSmaSamplePortfolioViaStrategyAndUnderlier capability" in {
        when(nonAdminUser.capabilities).thenReturn(Set.empty[String])
        val availableKeys = SmaSamplePortfolioCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0
      }

      "ViewSmaSamplePortfolioViaStrategyAndUnderlier return no keys when network has no strategies associated" in {
        when(nonAdminUser.smaStrategiesAndUnderliers).thenReturn(None)
        val availableKeys = SmaSamplePortfolioCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0
      }
    }
  }
}
