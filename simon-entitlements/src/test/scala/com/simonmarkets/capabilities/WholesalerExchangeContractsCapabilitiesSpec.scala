package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.WholesalerExchangeContractsCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar

class WholesalerExchangeContractsCapabilitiesSpec extends WordSpec
  with Matchers
  with StandardCapabilitiesBehavior
  with MockitoSugar
  with BeforeAndAfter
  with CapabilitiesTestData {

  "WholesalerExchangeContractsCapabilities" should {

    behave like standardCapabilities(WholesalerExchangeContractsCapabilities)

    "For admin user, WholesalerExchangeContractsCapabilities.availableAccessKeysGen" should {
      "ViewCapabilities - returns one admin key" in {
        val keys = WholesalerExchangeContractsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapabilities(ViewCapabilities, adminUser)

        keys shouldBe Set("admin")
      }

      "EditCapabilities - returns one admin key" in {
        val keys = WholesalerExchangeContractsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapabilities(EditCapabilities, adminUser)

        keys shouldBe Set("admin")
      }
    }

    "For non-admin user, WholesalerExchangeContractsCapabilities.availableAccessKeysGen" should {
      "return viewWholesalerExchangeContractsViaNetwork capability correctly" in {
        when(nonAdminUser.capabilities).thenReturn(Set[String](ViewWholesalerExchangeContractsViaNetworkCapability.name))
        val keys = WholesalerExchangeContractsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewWholesalerExchangeContractsViaNetworkCapability.name, nonAdminUser)

        keys shouldBe Set("viewWholesalerExchangeContractsViaNetwork:network")
      }
      "return editWholesalerExchangeContractsViaNetwork capability correctly" in {
        when(nonAdminUser.capabilities).thenReturn(Set[String](EditWholesalerExchangeContractsViaNetworkCapability.name))
        val keys = WholesalerExchangeContractsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(EditWholesalerExchangeContractsViaNetworkCapability.name, nonAdminUser)

        keys shouldBe Set("editWholesalerExchangeContractsViaNetwork:network")
      }
    }
  }
}
