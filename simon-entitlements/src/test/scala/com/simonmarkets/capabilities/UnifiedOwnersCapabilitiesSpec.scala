package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.UnifiedOwnersCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class UnifiedOwnersCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {

  "UnifiedOwnersCapabilities" should {

    "produce network-based keys for ViewUnifiedOwnerViaNetwork" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewUnifiedOwnerViaNetwork))
      val availableKeys = UnifiedOwnersCapabilities.getAvailableAccessKeys(nonAdminUser)
      availableKeys shouldBe Set(s"$ViewUnifiedOwnerViaNetwork:$networkId")
    }

    "produce network-based keys for EditUnifiedOwnerViaNetwork" in {
      when(nonAdminUser.capabilities).thenReturn(Set(EditUnifiedOwnerViaNetwork))
      val availableKeys = UnifiedOwnersCapabilities.getAvailableAccessKeys(nonAdminUser)
      availableKeys shouldBe Set(s"$EditUnifiedOwnerViaNetwork:$networkId")
    }

    "have the right capabilities defined" in {
      UnifiedOwnersCapabilities.ViewCapabilities shouldBe Set(ViewUnifiedOwnerViaNetwork)
      UnifiedOwnersCapabilities.EditCapabilities shouldBe Set(EditUnifiedOwnerViaNetwork)
      UnifiedOwnersCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 2
    }

    "have the right detailed capabilities defined" in {
      UnifiedOwnersCapabilities.toDetailedCapabilitySet.map(_.name) shouldBe Set(
        ViewUnifiedOwnerViaNetwork, EditUnifiedOwnerViaNetwork
      )
    }

  }

}
