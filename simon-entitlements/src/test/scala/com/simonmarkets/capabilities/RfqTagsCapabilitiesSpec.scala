package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.RfqTagsCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import org.scalatest.mockito.MockitoSugar

class RfqTagsCapabilitiesSpec extends WordSpec
  with Matchers
  with StandardCapabilitiesBehavior
  with MockitoSugar
  with BeforeAndAfter
  with CapabilitiesTestData {

  before {
    when(nonAdminUser.capabilities).thenReturn(Set[String](ViewRfqTagsViaNetworkCapability.name, EditRfqTagsViaNetworkCapability.name))
  }

  "RfqTagsCapabilities" should {
    behave like standardCapabilities(RfqTagsCapabilities)
    "Given a user which has Admin capability RfqTagsCapabilities.availableAccessKeysGen" should {
      "ViewCapabilities returns one admin key" in {
        val keys = RfqTagsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapabilities(ViewCapabilities, adminUser)
        keys.size shouldBe 1
        keys should contain("admin")
      }
    }
    "Given a user which does not have Admin capability then RfqTagsCapabilities.availableAccessKeysGen" should {
      "ViewRfqTagsViaNetwork capability returns user keys only" in {
        val keys = RfqTagsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewRfqTagsViaNetworkCapability.name, nonAdminUser)
        keys.size shouldBe 1
        keys should contain("viewRfqTagsViaNetwork:network")
      }
      "EditRfqTagsViaNetwork capability returns user keys only" in {
        val keys = RfqTagsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(EditRfqTagsViaNetworkCapability.name, nonAdminUser)
        keys.size shouldBe 1
        keys should contain("editRfqTagsViaNetwork:network")
      }
    }
    "Given a user which has Admin capability RfqTagsCapabilities.availableAccessKeysGen" should {
      "EditCapabilities returns one admin key" in {
        val keys = RfqTagsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapabilities(EditCapabilities, adminUser)
        keys.size shouldBe 1
        keys should contain("admin")
      }
    }
  }
}
