package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.{IssuerPurview, PurviewedDomain, UserACL}
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.UsersCapabilities._
import org.mockito.Mockito.{reset, when}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import simon.Id.NetworkId

class UsersCapabilitiesSpec extends WordSpec with Matchers with StandardCapabilitiesBehavior with MockitoSugar with BeforeAndAfter {

  val nonAdminUser = mock[UserACL]

  val purviewNetwork1 = NetworkId("purviewNetwork1")
  val purviewNetwork2 = NetworkId("purviewNetwork2")

  val userId = "nonAdminUser"

  before {
    reset(nonAdminUser)

    when(nonAdminUser.userId).thenReturn(userId)
    when(nonAdminUser.networkId).thenReturn(NetworkId("network"))
    when(nonAdminUser.capabilities).thenReturn(Set.empty[String])
    when(nonAdminUser.userPurviewIds).thenReturn(Set(purviewNetwork1, purviewNetwork2))
    when(nonAdminUser.locations).thenReturn(Set("location1", "location2"))
    when(nonAdminUser.faNumbers).thenReturn(Set("faNumber1", "faNumber2"))
    when(nonAdminUser.issuerPurviewIds).thenReturn(Set(IssuerPurview(NetworkId("userPurviewNetwork"), Set("GS", "CS"), None, Some(Set(PurviewedDomain.Users))),
      IssuerPurview(NetworkId("normalNetwork"), Set("GS", "CS"))))
  }


  "UsersCapabilities" should {
    behave like standardCapabilities(UsersCapabilities)

    "ViewUserViaParentNetwork returns parent network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewUserViaParentNetwork))
      when(nonAdminUser.group).thenReturn(Some("Parent Network"))
      UsersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewUserViaParentNetwork, nonAdminUser) shouldBe
        Set(s"$ViewUserViaParentNetwork:Parent Network")
    }

    "ViewUserViaPurview returns purview keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewUserViaPurview))
      UsersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewUserViaPurview, nonAdminUser) shouldBe
        Set(s"$ViewUserViaPurview:userPurviewNetwork")
    }

    "ViewUserViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewUserViaNetwork))
      UsersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewUserViaNetwork, nonAdminUser) shouldBe
        Set(s"$ViewUserViaNetwork:network")
    }

    "ViewUserViaLocation returns location keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewUserViaLocation))
      UsersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewUserViaLocation, nonAdminUser) shouldBe
        Set(s"$ViewUserViaLocation:network:location1", s"$ViewUserViaLocation:network:location2")
    }

    "ViewUserViaFaNumber returns fa number keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewUserViaFANumber))
      UsersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewUserViaFANumber, nonAdminUser) shouldBe
        Set(s"$ViewUserViaFANumber:network:faNumber1", s"$ViewUserViaFANumber:network:faNumber2")
    }

    "ViewUserViaGuid returns fa number keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewUserViaGuid))
      UsersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewUserViaGuid, nonAdminUser) shouldBe
        Set(s"$ViewUserViaGuid:$userId")
    }

    "ImpersonateUserViaPurview returns purview keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ImpersonateUserViaPurview))
      UsersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ImpersonateUserViaPurview, nonAdminUser) shouldBe
        Set(s"$ImpersonateUserViaPurview:purviewNetwork1", s"$ImpersonateUserViaPurview:purviewNetwork2")
    }

    "ImpersonateUserViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ImpersonateUserViaNetwork))
      UsersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ImpersonateUserViaNetwork, nonAdminUser) shouldBe
        Set(s"$ImpersonateUserViaNetwork:network")
    }

    "ImpersonateUserViaLocation returns location keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ImpersonateUserViaLocation))
      UsersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ImpersonateUserViaLocation, nonAdminUser) shouldBe
        Set(s"$ImpersonateUserViaLocation:network:location1", s"$ImpersonateUserViaLocation:network:location2")
    }

    "ImpersonateUserViaFaNumber returns fa number keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ImpersonateUserViaFANumber))
      UsersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ImpersonateUserViaFANumber, nonAdminUser) shouldBe
        Set(s"$ImpersonateUserViaFANumber:network:faNumber1", s"$ImpersonateUserViaFANumber:network:faNumber2")
    }


    "UploadUserViaPurview returns purview keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(UploadUserViaPurview))
      UsersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(UploadUserViaPurview, nonAdminUser) shouldBe
        Set(s"$UploadUserViaPurview:purviewNetwork1", s"$UploadUserViaPurview:purviewNetwork2")
    }

    "UploadUserViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(UploadUserViaNetwork))
      UsersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(UploadUserViaNetwork, nonAdminUser) shouldBe
        Set(s"$UploadUserViaNetwork:network")
    }

    "ImpersonateCapabilities will include admin, ImpersonateViaNetwork, ImpersonateViaLocation, ImpersonateViaFaNumber, & ImpersonateViaPurview" in {
      UsersCapabilities.ImpersonateCapabilities shouldBe Set(Admin, ImpersonateUserViaPurview,
        ImpersonateUserViaNetwork, ImpersonateUserViaLocation, ImpersonateUserViaFANumber)
    }

    "ImpersonateWithApprovalCapabilities will include ImpersonateUserViaPurviewWithApproval, ImpersonateUserViaNetworkWithApproval, ImpersonateUserViaLocationWithApproval, & ImpersonateUserViaFANumberWithApproval" in {
      UsersCapabilities.ImpersonateWithApprovalCapabilities shouldBe
        Set(ImpersonateUserViaPurviewWithApproval, ImpersonateUserViaNetworkWithApproval, ImpersonateUserViaLocationWithApproval, ImpersonateUserViaFANumberWithApproval)
    }

    "EditClientCredentialsViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(EditClientCredentialsViaNetwork))
      UsersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(EditClientCredentialsViaNetwork, nonAdminUser) shouldBe
        Set(s"$EditClientCredentialsViaNetwork:network")
    }

  }

}
