package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities.Admin
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import simon.Id.NetworkId

trait CapabilitiesUsers extends MockitoSugar {

  protected val adminOnlyUser: UserACL = {
    val user = mock[UserACL]
    when(user.userId).thenReturn("adminUser")
    when(user.networkId).thenReturn(NetworkId("SIMON Admin"))
    when(user.capabilities).thenReturn(Set(Admin))
    user
  }

}
