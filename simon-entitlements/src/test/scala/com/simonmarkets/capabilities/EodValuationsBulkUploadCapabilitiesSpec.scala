package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.EodValuationsBulkUploadCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class EodValuationsBulkUploadCapabilitiesSpec extends WordSpec
  with Matchers
  with StandardCapabilitiesBehavior
  with MockitoSugar
  with BeforeAndAfter
  with CapabilitiesTestData {

  before {
    when(nonAdminUser.capabilities).thenReturn(Set[String](ViewEodValuationsBulkUploadViaUserUploadedByCapability.name, ViewEodValuationsBulkUploadViaNetworkCapability.name))
  }

  "EodValuationsBulkUploadCapabilities" should {
    import com.simonmarkets.capabilities.EodValuationsBulkUploadCapabilities._
    behave like standardCapabilities(EodValuationsBulkUploadCapabilities)
    "Given a user which has Admin capability EodValuationsBulkUploadCapabilities.availableAccessKeysGen" should {
      "ViewCapabilities returns one admin key" in {
        val keys = EodValuationsBulkUploadCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapabilities(ViewCapabilities, adminUser)
        keys.size shouldBe 1
        keys should contain("admin")
      }
    }
    "Given a user which does not have Admin capability then EodValuationsBulkUploadCapabilities.availableAccessKeysGen" should {
      "ViewEodValuationsBulkUploadViaUserUploadedBy capability returns user keys only" in {
        val keys = EodValuationsBulkUploadCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewEodValuationsBulkUploadViaUserUploadedByCapability.name, nonAdminUser)
        keys.size shouldBe 1
        keys should contain("viewEodValuationsBulkUploadViaUserUploadedBy:nonAdminUser")
      }
      "ViewEodValuationsBulkUploadViaNetwork capability returns network based keys only" in {
        val keys = EodValuationsBulkUploadCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewEodValuationsBulkUploadViaNetworkCapability.name, nonAdminUser)
        keys.size shouldBe 1
        keys should contain("viewEodValuationsBulkUploadViaNetwork:network")
      }
    }
  }

}
