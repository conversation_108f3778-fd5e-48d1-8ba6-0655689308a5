package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.{NetworkCategory, Purview, PurviewEntity, PurviewedDomain}
import com.simonmarkets.capabilities.OrdersCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.prop.TableDrivenPropertyChecks
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class OrdersCapabilitiesSpec
  extends WordSpec
    with Matchers
    with StandardCapabilitiesBehavior
    with MockitoSugar
    with BeforeAndAfter
    with TableDrivenPropertyChecks
    with CapabilitiesTestData {

  val purviewEntity1 = PurviewEntity(key = "Pershing", purviewType = Some(NetworkCategory.Custodian), domains = Some(Set(PurviewedDomain.SIRatesOrders, PurviewedDomain.Users)))
  val purviewEntity2 = PurviewEntity(key = "NFS", purviewType = Some(NetworkCategory.Custodian), domains = Some(Set(PurviewedDomain.SIEquitiesOrders, PurviewedDomain.Users)))
  val purviewEntity3 = PurviewEntity(key = "BARCLAYS", purviewType = Some(NetworkCategory.Issuer), domains = Some(Set(PurviewedDomain.OrderAccounts)))

  val purview1 = Purview(networkId = "network1", purviewEntities = Set(purviewEntity1))
  val purview2 = Purview(networkId = "network2", purviewEntities = Set(purviewEntity2))
  val purview3 = Purview(networkId = "network3", purviewEntities = Set(purviewEntity2, purviewEntity1))
  val purview4 = Purview(networkId = "network4", purviewEntities = Set(purviewEntity3))

  before {

    when(adminUser.purviews).thenReturn(Some(Set(purview1, purview2, purview3, purview4)))
    when(adminUser.locations).thenReturn(Set("location1"))
    when(adminUser.faNumbers).thenReturn(Set("faNumber1", "faNumber2"))

    when(nonAdminUser.capabilities).thenReturn(Set[String](ViewOrderViaNetwork, EditOrderViaNetwork, CancelOrderViaNetwork,
      ViewOrderViaLocation, EditOrderViaLocation, CancelOrderViaLocation,
      ViewOrderViaPurview,
      EditOrderViaPurview,
      CancelOrderViaPurview,
      ViewRatesOrderViaPurview,
      ViewOrderViaPurviewNetworkCustodianCapability.name,
      ViewRatesOrderViaPurviewNetworkCustodianCapability.name,
      EditOrderViaPurviewNetworkCustodianCapability.name,
      CancelOrderViaPurviewNetworkCustodianCapability.name,
      ViewOrderViaOwner, CancelOrderViaOwner,
      ApproveOrderViaApprovers,
      ViewOrderViaFaNumber,
      ViewOrderViaUserCreated, EditOrderViaUserCreated,
      BlockSingleSIFeeOrderProduct,
      ViewOrdersAuditTrailViaPurviewCapability.name,
      ViewOrdersAuditTrailViaPurviewNetworkCustodianCapability.name
    ))
    when(nonAdminUser.purviews).thenReturn(Some(Set(purview1, purview2, purview4)))
    when(nonAdminUser.locations).thenReturn(Set.empty[String])
    when(nonAdminUser.faNumbers).thenReturn(Set("faNumber1", "faNumber2"))
  }

  "OrdersCapabilities" should {
    import com.simonmarkets.capabilities.OrdersCapabilities._

    behave like standardCapabilities(OrdersCapabilities)
    "Given a user which has Admin capability OrdersCapabilities.availableAccessKeysGen" should {
      "each capability group returns or not the admin key" in {
        forAll(Table(
          ("capability", "should return admin key"),
          // as a requirement, Admins are not allowed to approve for other networks.
          (ApproveCapabilities, false),
          (ViewCapabilities, true),
          (CancelCapabilities, true),
          (EditCapabilities, true),
          (SubmitOnBehalfOfCapabilities, true),

        )) { case (capability, shouldReturnAdminKey) =>
          val keys = OrdersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapabilities(capability, adminUser)
          if (shouldReturnAdminKey) {
            keys shouldBe Set("admin")
          }
          else {
            keys shouldBe Set()
          }
        }
      }
    }
    "Given a user which does not have Admin capability then OrdersCapabilities.availableAccessKeysGen" should {
      "each capability returns it's expected keys" in {
        forAll(Table(
          ("capability", "expected key Set"),
          (ApproveOrderViaApprovers, Set("approveOrderViaApprovers:nonAdminUser")),
          (ViewOrderViaOwner, Set("viewOrderViaOwner:nonAdminUser")),
          (CancelOrderViaOwner, Set("cancelOrderViaOwner:nonAdminUser")),
          (CancelOrderViaNetwork, Set("cancelOrderViaNetwork:network")),
          (EditOrderViaNetwork, Set("editOrderViaNetwork:network")),
          (ViewOrderViaNetwork, Set("viewOrderViaNetwork:network")),
          (ViewOrderViaUserCreated, Set("viewOrderViaUserCreated:nonAdminUser")),
          (EditOrderViaUserCreated, Set("editOrderViaUserCreated:nonAdminUser")),
          (EditOrderViaLocation, Set()),
          (CancelOrderViaLocation, Set()),
          (ViewOrderViaLocation, Set()),
          (ViewOrderViaPurview, Set("viewOrderViaPurview:network4:BARCLAYS")),
          (ViewOrderViaPurviewNetworkCustodianCapability.name, Set("viewOrderViaPurviewNetworkCustodian:network2:NFS")),
          (EditOrderViaPurview, Set("editOrderViaPurview:network4:BARCLAYS")),
          (EditOrderViaPurviewNetworkCustodianCapability.name, Set("editOrderViaPurviewNetworkCustodian:network2:NFS")),
          (CancelOrderViaPurview, Set("cancelOrderViaPurview:network4:BARCLAYS")),
          (CancelOrderViaPurviewNetworkCustodianCapability.name, Set("cancelOrderViaPurviewNetworkCustodian:network2:NFS")),
          (ViewOrderViaFaNumber, Set(s"$ViewOrderViaFaNumber:network:faNumber1", s"$ViewOrderViaFaNumber:network:faNumber2")),
          (BlockSingleSIFeeOrderProduct, Set("blockSingleSIFeeOrderProduct")),
          (ViewOrdersAuditTrailViaPurviewCapability.name, Set("viewOrdersAuditTrailViaPurview")),
          (ViewOrdersAuditTrailViaPurviewNetworkCustodianCapability.name, Set("viewOrdersAuditTrailViaPurviewNetworkCustodian")),
          (ViewRatesOrderViaPurview, Set("viewRatesOrderViaPurview:network4:BARCLAYS")),
          (ViewRatesOrderViaPurviewNetworkCustodianCapability.name, Set("viewRatesOrderViaPurviewNetworkCustodian:network1:Pershing"))
        )) { case (capability, expectedKeySet) =>
          val keys = OrdersCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(capability, nonAdminUser)
          keys shouldBe expectedKeySet
        }
      }
    }
  }
}
