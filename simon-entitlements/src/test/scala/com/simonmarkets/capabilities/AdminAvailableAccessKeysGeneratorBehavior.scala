package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator
import org.scalatest.{Matchers, WordSpec}

/**
 * Defines tests for a [[AvailableAccessKeysGenerator]] that should generate available access keys that are
 * available an admin-only user
 */
trait AdminAvailableAccessKeysGeneratorBehavior extends Matchers with CapabilitiesUsers {
  this: WordSpec =>

  def capabilities: Capabilities

  def adminAccessKeysGenerator(availableAccessKeysGenerator: AvailableAccessKeysGenerator): Unit = {

    "generate access keys for admin-only user" in {
      availableAccessKeysGenerator.getAvailableAccessKeys(adminOnlyUser) shouldBe Set(Admin)
    }

    "generate access keys for set of capabilities" in {
      availableAccessKeysGenerator.getAvailableAccessKeysForCapabilities(capabilities.toSet, adminOnlyUser) shouldBe Set(Admin)
    }

  }
}
