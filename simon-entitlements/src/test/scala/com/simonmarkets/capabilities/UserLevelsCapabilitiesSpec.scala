package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.UserLevelsCapabilities._
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}


class UserLevelsCapabilitiesSpec extends WordSpec with Matchers with StandardCapabilitiesBehavior with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {

  "UserLevelsCapabilities" should {
    behave like standardCapabilities(UserLevelsCapabilities)

    "ViewUserLevelViaOwner returns fa number keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewUserLevelViaOwner))
      UserLevelsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewUserLevelViaOwner, nonAdminUser) shouldBe
        Set(s"$ViewUserLevelViaOwner:${nonAdminUser.userId}")
    }

    "ViewUserLevelViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewUserLevelViaNetwork))
      UserLevelsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewUserLevelViaNetwork, nonAdminUser) shouldBe
        Set(s"$ViewUserLevelViaNetwork:$networkId")
    }

    "ViewUserLevelViaPurview returns purview keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(ViewUserLevelViaPurview))
      UserLevelsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(ViewUserLevelViaPurview, nonAdminUser) shouldBe
        Set(s"$ViewUserLevelViaPurview:$purviewNetwork1",
          s"$ViewUserLevelViaPurview:$purviewNetwork2")
    }

    "EditUserLevelViaOwner returns owner keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(EditUserLevelViaOwner))
      UserLevelsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(EditUserLevelViaOwner, nonAdminUser) shouldBe
        Set(s"$EditUserLevelViaOwner:${nonAdminUser.userId}")
    }

    "EditUserLevelViaNetwork returns network keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(EditUserLevelViaNetwork))
      UserLevelsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(EditUserLevelViaNetwork, nonAdminUser) shouldBe
        Set(s"$EditUserLevelViaNetwork:$networkId")
    }

    "EditUserLevelViaPurview returns purview keys" in {
      when(nonAdminUser.capabilities).thenReturn(Set(EditUserLevelViaPurview))
      UserLevelsCapabilities.availableAccessKeysGen.getAvailableAccessKeysForCapability(EditUserLevelViaPurview, nonAdminUser) shouldBe
        Set(s"$EditUserLevelViaPurview:$purviewNetwork1",
          s"$EditUserLevelViaPurview:$purviewNetwork2")
    }
  }
}
