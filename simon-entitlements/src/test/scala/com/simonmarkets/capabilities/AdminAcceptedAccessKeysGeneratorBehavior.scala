package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.entitlements.AcceptedAccessKeysGenerator
import org.scalatest.{Matchers, WordSpec}

/**
 * Defines tests for a [[AcceptedAccessKeysGenerator]] that should generate accepted access keys for the Admin
 * capability
 */
trait AdminAcceptedAccessKeysGeneratorBehavior extends Matchers with CapabilitiesUsers {
  this: WordSpec =>

  def capabilities: Capabilities

  def adminAcceptedKeysGenerator[A](acceptedAccessKeysGenerator: AcceptedAccessKeysGenerator[A], testResources: Seq[A]): Unit = {

    "generate keys for admin capability" in {
      acceptedAccessKeysGenerator.capabilityToAcceptedKeyBuilders.get(Admin) should not be None
    }

    "generate admin access keys for any resource" in {
      testResources.foreach(acceptedAccessKeysGenerator.getAcceptedAccessKeys(_) should contain(Admin))
    }

  }
}
