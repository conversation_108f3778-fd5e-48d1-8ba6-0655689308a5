package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.SmaCompositeDataCapabilities.ViewSmaCompositeDataViaStrategyAndUnderlier
import com.simonmarkets.networks.SmaStrategyAndUnderliers
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class SmaCompositeDataCapabilitiesSpec extends WordSpec with Matchers with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {

  before {
    when(nonAdminUser.smaStrategiesAndUnderliers).thenReturn(Some(Set(
      SmaStrategyAndUnderliers("123", Set("SPX", "M1EA")),
      SmaStrategyAndUnderliers("456", Set("SPX"))
    )))
  }

  "SmaCompositeDataCapabilities" should {
    "have 3 capabilities" in {
      SmaCompositeDataCapabilities.capabilityToAvailableKeyBuilders.size shouldBe 3
    }

    "For Admin user should return admin key" in {
      val availableKeys = SmaCompositeDataCapabilities.getAvailableAccessKeys(adminUser)
      availableKeys.size shouldBe 1
      availableKeys shouldBe Set(Admin)
    }

    "For Non Admin Users" should {
      "ViewSmaCompositeDataViaStrategyAndUnderlier return product of strategy and underlier IDs when user has the capability" in {
        when(nonAdminUser.capabilities).thenReturn(Set(ViewSmaCompositeDataViaStrategyAndUnderlier))
        val availableKeys = SmaCompositeDataCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys shouldBe Set(s"$ViewSmaCompositeDataViaStrategyAndUnderlier:123:SPX", s"$ViewSmaCompositeDataViaStrategyAndUnderlier:123:M1EA", s"$ViewSmaCompositeDataViaStrategyAndUnderlier:456:SPX")
      }

      "ViewSmaCompositeDataViaStrategyAndUnderlier return no keys when user does not have ViewSmaCompositeDataViaStrategyAndUnderlier capability" in {
        when(nonAdminUser.capabilities).thenReturn(Set.empty[String])
        val availableKeys = SmaCompositeDataCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0
      }

      "ViewSmaCompositeDataViaStrategyAndUnderlier return no keys when network has no strategies associated" in {
        when(nonAdminUser.smaStrategiesAndUnderliers).thenReturn(None)
        val availableKeys = SmaCompositeDataCapabilities.getAvailableAccessKeys(nonAdminUser)
        availableKeys.size shouldBe 0
      }
    }
  }
}
