package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.Admin
import org.scalatest.{Matchers, WordSpec}

/**
 * Defines tests for a [[Capabilities]] for which no defined set of capabilities should include [[Admin]]
 */
trait NonAdminCapabilitiesBehavior extends Matchers with CapabilitiesUsers {
  this: WordSpec =>

  def nonAdminCapabilities(implicit capabilities: Capabilities): Unit = {

    "not include admin" in {
      capabilities.toSet should not contain Admin
    }

  }

}
