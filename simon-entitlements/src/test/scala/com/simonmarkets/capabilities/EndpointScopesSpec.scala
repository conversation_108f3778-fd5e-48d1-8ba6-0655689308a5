package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.{reset, when}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class EndpointScopesSpec extends WordSpec with Matchers with MockitoSugar with BeforeAndAfter with CapabilitiesTestData {

  val userWithEndpointScopes = mock[UserACL]

  val userWithoutEndpointScopes = mock[UserACL]
  val endpointScopesWithoutAdmin = EndpointScopes.toSet -- Set(Capabilities.Admin)


  before {
    reset(userWithEndpointScopes, userWithoutEndpointScopes)

    when(userWithEndpointScopes.capabilities).thenReturn(endpointScopesWithoutAdmin)
    when(userWithoutEndpointScopes.capabilities).thenReturn(Set.empty[String])
  }


  "EndpointScopes" should {
    "For Non Admin Users" should {
      "Only have the scopes that are assigned to the user" in {

        val availableKeysForUserWithoutEndpointScopes = EndpointScopes.getAvailableAccessKeys(userWithoutEndpointScopes)
        val availableKeysForUserWithEndpointScopes = EndpointScopes.getAvailableAccessKeys(userWithEndpointScopes)


        availableKeysForUserWithoutEndpointScopes shouldBe Set.empty[String]
        availableKeysForUserWithEndpointScopes shouldBe endpointScopesWithoutAdmin
      }
    }
    "For Admin Users" should {
      "Return all the endpoint scopes including admin scope" in {
        val adminEndpointScopes = EndpointScopes.getAvailableAccessKeys(adminUser)
        adminEndpointScopes shouldBe Set(Capabilities.Admin)
      }
    }

    "have 11 capabilities" in {
      EndpointScopes.capabilityToAvailableKeyBuilders.size shouldBe 13
    }
  }
}