package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.AnnuityActivityCapabilities._
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.util.CapabilitiesTestData
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

class AnnuityActivityCapabilitiesSpec
    extends WordSpec
    with Matchers
    with StandardCapabilitiesBehavior
    with MockitoSugar
    with BeforeAndAfter
    with CapabilitiesTestData {

  "AnnuityActivityCapabilities" when {

    behave like standardCapabilities(AnnuityActivityCapabilities)

    "is admin" should {

      "return admin key" in {
        val keys = AnnuityActivityCapabilities.getAvailableAccessKeys(adminUser)
        keys should contain only Admin
      }

    }

    "is not admin" should {

      "return userId for ViewRegisteredAnnuityActivityViaUserCreated" in {
        when(nonAdminUser.capabilities) thenReturn Set(ViewRegisteredAnnuityActivityViaUserCreated)
        val keys = AnnuityActivityCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys should contain only s"viewRegisteredAnnuityActivityViaUserCreated:${nonAdminUser.userId}"
      }

      "return networkId for ViewRegisteredAnnuityActivityViaNetwork" in {
        when(nonAdminUser.capabilities) thenReturn Set(ViewRegisteredAnnuityActivityViaNetwork)
        val keys = AnnuityActivityCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys should contain only s"viewRegisteredAnnuityActivityViaNetwork:${nonAdminUser.networkId}"
      }

      "return userId for EditRegisteredAnnuityActivityViaUserCreated" in {
        when(nonAdminUser.capabilities) thenReturn Set(EditRegisteredAnnuityActivityViaUserCreated)
        val keys = AnnuityActivityCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys should contain only s"editRegisteredAnnuityActivityViaUserCreated:${nonAdminUser.userId}"
      }

      "return networkId for EditRegisteredAnnuityActivityViaNetwork" in {
        when(nonAdminUser.capabilities) thenReturn Set(EditRegisteredAnnuityActivityViaNetwork)
        val keys = AnnuityActivityCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys should contain only s"editRegisteredAnnuityActivityViaNetwork:${nonAdminUser.networkId}"
      }

      "return userId for ViewNonRegisteredAnnuityActivityViaUserCreated" in {
        when(nonAdminUser.capabilities) thenReturn Set(ViewNonRegisteredAnnuityActivityViaUserCreated)
        val keys = AnnuityActivityCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys should contain only s"viewNonRegisteredAnnuityActivityViaUserCreated:${nonAdminUser.userId}"
      }

      "return networkId for ViewNonRegisteredAnnuityActivityViaNetwork" in {
        when(nonAdminUser.capabilities) thenReturn Set(ViewNonRegisteredAnnuityActivityViaNetwork)
        val keys = AnnuityActivityCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys should contain only s"viewNonRegisteredAnnuityActivityViaNetwork:${nonAdminUser.networkId}"
      }

      "return userId for EditNonRegisteredAnnuityActivityViaUserCreated" in {
        when(nonAdminUser.capabilities) thenReturn Set(EditNonRegisteredAnnuityActivityViaUserCreated)
        val keys = AnnuityActivityCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys should contain only s"editNonRegisteredAnnuityActivityViaUserCreated:${nonAdminUser.userId}"
      }

      "return networkId for EditNonRegisteredAnnuityActivityViaNetwork" in {
        when(nonAdminUser.capabilities) thenReturn Set(EditNonRegisteredAnnuityActivityViaNetwork)
        val keys = AnnuityActivityCapabilities.getAvailableAccessKeys(nonAdminUser)
        keys should contain only s"editNonRegisteredAnnuityActivityViaNetwork:${nonAdminUser.networkId}"
      }

    }

  }

}
