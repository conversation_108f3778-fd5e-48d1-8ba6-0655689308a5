package com.simonmarkets.util

import com.goldmansachs.marquee.pipg.{UserACL, UserRole}
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.NetworkIds.AdminNetworkId
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar.mock
import simon.Id
import simon.Id.NetworkId

trait CapabilitiesTestData {

  val networkId: Id.NetworkId.Type = NetworkId("network")
  val whiteLabelPartnerId: String = "whiteLabelPartnerId1"
  val firmId: String = "firmId1"
  val purviewNetwork1: Id.NetworkId.Type = NetworkId("purviewNetwork1")
  val purviewNetwork2: Id.NetworkId.Type = NetworkId("purviewNetwork2")
  val purviewNetwork3: Id.NetworkId.Type = NetworkId("purviewNetwork3")
  val location = "Location"

  val adminUser: UserACL = {
    val user = mock[UserACL]
    when(user.networkId).thenReturn(AdminNetworkId)
    when(user.capabilities).thenReturn(Set(Admin))
    when(user.roles).thenReturn(Set(UserRole.unapply("EqPIPGGSAdmin").get))
    user
  }

  val nonAdminUser: UserACL = {
    val user = mock[UserACL]
    when(user.networkId).thenReturn(networkId)
    when(user.userId).thenReturn("nonAdminUser")
    when(user.capabilities).thenReturn(Set.empty[String])
    when(user.roles).thenReturn(Set.empty[UserRole])
    when(user.userPurviewIds).thenReturn(Set(purviewNetwork1, purviewNetwork2))
    when(user.locations).thenReturn(Set(location))
    when(user.externalIds).thenReturn(Some(Seq(ExternalId("subject", "id"))))
    when(user.whiteLabelPartnerId).thenReturn(Some(whiteLabelPartnerId))
    when(user.firmId).thenReturn(Some(firmId))
    user
  }
}
