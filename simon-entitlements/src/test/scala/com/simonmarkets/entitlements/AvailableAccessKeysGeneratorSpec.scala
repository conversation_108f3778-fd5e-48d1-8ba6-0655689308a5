package com.simonmarkets.entitlements

import com.goldmansachs.marquee.pipg.UserRole._
import com.goldmansachs.marquee.pipg._
import com.gs.marquee.util.SetHelper._
import com.simonmarkets.capabilities.ApprovalsCapabilities._
import com.simonmarkets.capabilities.Capabilities._
import com.simonmarkets.capabilities.HoldingsCapabilities._
import com.simonmarkets.capabilities.OfferingsV1Capabilities._
import com.simonmarkets.capabilities.OrdersCapabilities._
import com.simonmarkets.capabilities.SPOfferingsCapabilities._
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator._
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{FunSpec, Matchers}
import simon.Id.NetworkId

class AvailableAccessKeysGeneratorSpec extends FunSpec
  with Matchers
  with MockitoSugar {

  val userId = "userId"
  val networkId: NetworkId = NetworkId("SIMON Admin")
  val name = "name"
  val firstName = "firstName"
  val lastName = "lastName"
  val email = "email"
  val managerId = "managerId"

  val fa: String = "EqPIPGFA"
  val faManager: String = "EqPIPGFAManager"
  val admin: String = "EqPIPGGSAdmin"

  val location1 = "location1"
  val location2 = "location2"
  val location3 = "location3"

  val faNumber1 = "faNumber1"
  val faNumber2 = "faNumber2"
  val faNumber3 = "faNumber3"

  val cusip1 = "cusip1"
  val cusip2 = "cusip2"

  val FaFamHybridRole: String = "FaFamHybridRole"
  val ViewPayoffEntitlementRole: String = "ViewPayoffEntitlementRole"
  val EditPayoffEntitlementRole: String = "EditPayoffEntitlementRole"
  val TradePayoffEntitlementRole: String = "TradePayoffEntitlementRole"
  val faRoles: Set[String] = Set(fa)
  val managerRoles: Set[String] = Set(faManager)
  val adminRoles: Set[String] = Set(admin)
  val allRoles: Set[String] = Set(fa, faManager, admin, ViewPayoffEntitlementRole, EditPayoffEntitlementRole, TradePayoffEntitlementRole)

  val locations = Set(location1, location2, location3)
  val faNumbers = Set(faNumber1, faNumber2, faNumber3)
  val cusips = Set(cusip1, cusip2)
  val mockService = new TestService

  val payoffType = "PointToPoint"
  val payoffEntitlements = Map("PRU" -> Map(payoffType -> Set(Network.Action("trade"), Network.Action("edit"), Network.Action("view"),
    Network.Action("build"), Network.Action("illustrate"), Network.Action("performance"))))
  val issuerSymbol = "PRU"

  val TestViewPayoffEntitlementCapability = "testViewPayoffEntitlementCapability"
  val TestEditPayoffEntitlementCapability = "testEditPayoffEntitlementCapability"
  val TestTradePayoffEntitlementCapability = "testTradePayoffEntitlementCapability"

  val groups = Map(GroupIdType.DistributorId.productPrefix -> Set("hey", "how", "are", "you"))

  describe(s"getAvailableAccessKeys tests") {

    it(s"Given a user with custom roles: $faRoles, locations: $locations, and faNumbers: $faNumbers") {
      val user = getUser(faRoles, locations, faNumbers)
      val expectedKeys = Set(s"$ViewHoldingViaFaNumber:$networkId:$faNumber1", s"$ViewHoldingViaFaNumber:$networkId:$faNumber2", s"$ViewHoldingViaFaNumber:$networkId:$faNumber3")
      val actualKeys = mockService.getAvailableAccessKeys(user)
      actualKeys shouldEqual expectedKeys
    }

    it(s"Given a user with custom roles: $managerRoles, locations: $locations, and faNumbers: $faNumbers") {
      val user = getUser(managerRoles, locations, faNumbers)
      val expectedKeys = Set(s"$ViewHoldingViaLocation:$networkId:$location1", s"$ViewHoldingViaLocation:$networkId:$location2", s"$ViewHoldingViaLocation:$networkId:$location3")
      val actualKeys = mockService.getAvailableAccessKeys(user)
      actualKeys shouldEqual expectedKeys
    }

    it(s"Given a user with custom roles: $allRoles, locations: $locations, and faNumbers: $faNumbers") {
      val user = getUser(allRoles, locations, faNumbers, cusips)
      val expectedKeys = Set(s"admin",
        s"$ViewHoldingViaLocation:$networkId:$location1", s"$ViewHoldingViaLocation:$networkId:$location2", s"$ViewHoldingViaLocation:$networkId:$location3",
        s"$ViewHoldingViaFaNumber:$networkId:$faNumber1", s"$ViewHoldingViaFaNumber:$networkId:$faNumber2", s"$ViewHoldingViaFaNumber:$networkId:$faNumber3",
        s"${PrintPdfViaPerfPayoffEntitlementCapability.name}",
        s"$TradeOfferingViaProductIdEntitlement:$cusip1",
        s"$TradeOfferingViaProductIdEntitlement:$cusip2",
        s"$TestViewPayoffEntitlementCapability:$issuerSymbol:$payoffType",
        s"$TestEditPayoffEntitlementCapability:$issuerSymbol:$payoffType",
        s"$TestTradePayoffEntitlementCapability:$issuerSymbol:$payoffType")
      val actualKeys = mockService.getAvailableAccessKeys(user)
      actualKeys shouldEqual expectedKeys
    }

    it(s"Given a user with custom roles: $adminRoles") {
      val user = getUser(adminRoles, Set.empty, Set.empty)
      val actualKeys = mockService.getAvailableAccessKeysForCapability(PrintPdfViaPerfPayoffEntitlementCapability.name, user)
      actualKeys shouldEqual Set(PrintPdfViaPerfPayoffEntitlementCapability.name)
    }
  }

  describe(s"getAvailableAccessKeysForCapability tests") {
    it(s"Given a user with custom roles: $managerRoles, locations: $locations, and faNumbers: $faNumbers, it should have keys for $ViewHoldingViaLocation") {
      val user = getUser(managerRoles, locations, faNumbers)
      val expectedKeys = Set(s"$ViewHoldingViaLocation:$networkId:$location1", s"$ViewHoldingViaLocation:$networkId:$location2", s"$ViewHoldingViaLocation:$networkId:$location3")
      val actualKeys = mockService.getAvailableAccessKeysForCapability(ViewHoldingViaLocation, user)
      actualKeys shouldEqual expectedKeys
    }

    it(s"Given a user with custom roles: $faRoles, locations: $locations, and faNumbers: $faNumbers, it should NOT have keys for $ViewHoldingViaLocation") {
      val user = getUser(faRoles, locations, faNumbers)
      val expectedKeys = Set()
      val actualKeys = mockService.getAvailableAccessKeysForCapability(ViewHoldingViaLocation, user)
      actualKeys shouldEqual expectedKeys
    }
  }

  describe(s"getAvailableAccessKeysForCapabilities tests") {
    it(s"Given a user with custom roles: $FaFamHybridRole, locations: $locations, and faNumbers: $faNumbers, it should have keys for $ViewHoldingViaLocation and $ViewHoldingViaFaNumber") {
      val user = getUser(Set(FaFamHybridRole), locations, faNumbers)
      val expectedKeys = Set(s"$ViewHoldingViaLocation:$networkId:$location1", s"$ViewHoldingViaLocation:$networkId:$location2", s"$ViewHoldingViaLocation:$networkId:$location3",
        s"$ViewHoldingViaFaNumber:$networkId:$faNumber1", s"$ViewHoldingViaFaNumber:$networkId:$faNumber2", s"$ViewHoldingViaFaNumber:$networkId:$faNumber3")
      val actualKeys = mockService.getAvailableAccessKeysForCapabilities(Set(ViewHoldingViaFaNumber, ViewHoldingViaLocation), user)
      actualKeys shouldEqual expectedKeys
    }

    it(s"Given a user with custom roles: $FaFamHybridRole, locations: $locations, and faNumbers: $faNumbers, it should NOT have keys for $ViewOrderViaLocation or $Admin") {
      val user = getUser(Set(FaFamHybridRole), locations, faNumbers)
      val expectedKeys = Set()
      val actualKeys = mockService.getAvailableAccessKeysForCapabilities(Set(ViewOrderViaLocation, Admin), user)
      actualKeys shouldEqual expectedKeys
    }
  }

  describe("buildAdminKeys tests") {
    it(s"Given a user in network: $networkId with custom roles: $adminRoles, it should have admin key") {
      val user = getUser(adminRoles, locations, faNumbers)
      val keys = mockService.getAvailableAccessKeys(user)
      keys.contains(Admin) shouldBe true
    }

    val anotherNetwork = NetworkId("not-admin-network")
    it(s"Given a user in network: $anotherNetwork with custom roles: $adminRoles, it should NOT have admin key") {
      val user = getUser(adminRoles, locations, faNumbers).copy(networkId = anotherNetwork)
      val keys = mockService.getAvailableAccessKeys(user)
      keys.contains(Admin) shouldBe false
    }
  }

  it("buildGroupIdKeys test with filled groups") {
    val networkId = NetworkId("hamsterNetwork")
    val user = getUser(adminRoles, locations, faNumbers).copy(networkId = networkId)
    buildGroupIdKeys(GroupIdType.DistributorId.productPrefix)("viewXViaGroup", user) shouldEqual
      Set(
        "viewXViaGroup:hamsterNetwork:hey",
        "viewXViaGroup:hamsterNetwork:how",
        "viewXViaGroup:hamsterNetwork:are",
        "viewXViaGroup:hamsterNetwork:you"
      )
  }

  it("buildGroupIdKeys test with empty groups") {
    val networkId = NetworkId("hamsterNetwork")
    val user = getUser(adminRoles, locations, faNumbers).copy(networkId = networkId).copy(groups=None)
    buildGroupIdKeys(GroupIdType.DistributorId.productPrefix)("ViewXViaGroup", user) shouldEqual
      Set()
  }

  private def getUser(customRoles: Set[String], locations: Set[String], faNumbers: Set[String],
      cusips: Set[String] = Set.empty): UserACL = {

    val defaultRolesToCapabilities: Map[String, Set[String]] = Map(
      EqPIPGFA.name -> Set(ViewOrderViaOwner, ViewApprovedSPOfferingViaNetwork, TradeSPOfferingViaNetwork, ViewApprovalViaOwner, ViewHoldingViaFaNumber),
      EqPIPGFAManager.name -> Set(ViewOrderViaLocation, EditOrderViaLocation, ViewApprovalViaLocation, ViewUnapprovedSPOfferingViaNetwork, ViewApprovedSPOfferingViaNetwork, TradeSPOfferingViaNetwork, ApproveSPOfferingViaNetwork, ViewHoldingViaLocation),
      EqPIPGGSAdmin.name -> Set(Admin, PrintPdfViaPerfPayoffEntitlementCapability.name, TradeOfferingViaProductIdEntitlement),
      FaFamHybridRole -> Set(ViewHoldingViaLocation, ViewHoldingViaFaNumber),
      ViewPayoffEntitlementRole -> Set(TestViewPayoffEntitlementCapability),
      EditPayoffEntitlementRole -> Set(TestEditPayoffEntitlementCapability),
      TradePayoffEntitlementRole -> Set(TestTradePayoffEntitlementCapability)

    )

    def capabilitiesGenerator(customRoles: Set[String]): Set[String] = {
      customRoles.flatMap(customRole => { defaultRolesToCapabilities.getOrElse(customRole, Set.empty) })
    }

    UserACL(
      userId,
      networkId,
      customRoles = customRoles,
      lastVisitedAt = None,
      distributorId = None,
      omsId = None,
      firstName = "firstName",
      lastName = "lastName",
      email = "email",
      locations = locations,
      faNumbers = faNumbers,
      capabilities = capabilitiesGenerator(customRoles),
      payoffEntitlementsV2 = payoffEntitlements,
      tradewebEligible = false,
      regSEligible = false,
      isActive = Some(true),
      cusips = cusips,
      groups = Some(groups)
    )
  }


  case class TestResource(faNumber: Option[String], networkId: NetworkId, locations: List[String])

  class TestService extends AvailableAccessKeysGenerator {

    override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys),
      ViewHoldingViaLocation -> AvailableKeyBuilder(buildViewHoldingManagerKeys),
      ViewHoldingViaFaNumber -> AvailableKeyBuilder(buildViewHoldingKeys),
      TradeOfferingViaProductIdEntitlement -> AvailableKeyBuilder(buildCusipsKeys),
      PrintPdfViaPerfPayoffEntitlementCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
      TestViewPayoffEntitlementCapability -> AvailableKeyBuilder(payoffEntitlementKeysForView),
      TestEditPayoffEntitlementCapability -> AvailableKeyBuilder(payoffEntitlementKeysForEdit),
      TestTradePayoffEntitlementCapability -> AvailableKeyBuilder(payoffEntitlementKeysForTrade),
    )

    def buildViewHoldingManagerKeys(capability: String, user: UserACL): Set[String] = {
      val networkIdKeys = keyBuilderAppendNetworkId(Set(capability), user)
      keyBuilderAppendLocations(networkIdKeys, user)
    }

    def buildViewHoldingKeys(capability: String, user: UserACL): Set[String] = {
      // fa number keys
      val networkIdKeys = keyBuilderAppendNetworkId(Set(capability), user)
      val faNumberKeys = keyBuilderAppendFANumber(networkIdKeys, user)

      faNumberKeys
    }

    def keyBuilderAppendNetworkId(currentKeys: Set[String], user: UserACL): Set[String] = {
      currentKeys.map(currentKey => s"$currentKey:${user.networkId}")
    }

    def keyBuilderAppendFANumber(currentKeys: Set[String], user: UserACL): Set[String] = {
      currentKeys.cartesianProduct(user.faNumbers).map {
        case (currentKey, faNumber) => s"$currentKey:$faNumber"
      }
    }

    def keyBuilderAppendLocations(currentKeys: Set[String], user: UserACL): Set[String] = {
      currentKeys.cartesianProduct(user.locationsTree).map {
        case (currentKey, locationId) => currentKey + ":" + locationId
      }
    }
  }
}