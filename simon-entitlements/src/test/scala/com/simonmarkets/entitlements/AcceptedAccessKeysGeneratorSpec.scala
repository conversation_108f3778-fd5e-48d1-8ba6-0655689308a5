package com.simonmarkets.entitlements

import com.gs.marquee.util.SetHelper._
import com.simonmarkets.capabilities.Capabilities._
import com.simonmarkets.capabilities.HoldingsCapabilities._
import org.scalatest.{FunSpec, Matchers}
import org.scalatest.mockito.MockitoSugar
import simon.Id.NetworkId

class AcceptedAccessKeysGeneratorSpec extends FunSpec with Matchers with MockitoSugar {

  val userId = "userId"
  val networkId = NetworkId("network")
  val name = "name"
  val firstName = "firstName"
  val lastName = "lastName"
  val email = "email"
  val managerId = "managerId"

  val location1 = "location1"
  val location2 = "location2"
  val location3 = "location3"

  val faNumber1 = "faNumber1"
  val faNumber2 = "faNumber2"
  val faNumber3 = "faNumber3"

  val setOfCapabilities1 = Set("admin", ViewHoldingViaLocation)
  val setOfCapabilities2 = Set(ViewHoldingViaLocation, ViewHoldingViaFaNumber)

  describe(s"Accepted Access Keys Spec") {
    val mockService = new TestService
    val locations = List(location1, location2, location3)

    it(s"Given a resource with faNumber: $faNumber1, networkId: $networkId, and locations: $locations") {
      val resource = TestResource(Some(faNumber1), networkId, locations)
      val expectedKeys = Set(s"admin",
        s"$ViewHoldingViaLocation:$networkId:$location1", s"$ViewHoldingViaLocation:$networkId:$location2", s"$ViewHoldingViaLocation:$networkId:$location3",
        s"$ViewHoldingViaFaNumber:$networkId:$faNumber1")
      val actualKeys = mockService.getAcceptedAccessKeys(resource)

      actualKeys shouldEqual expectedKeys
    }

    it("returns accessKeys for admin and ViewHoldingViaLocation capabilities") {
      val caps = Set("admin", ViewHoldingViaLocation)
      val resource = TestResource(Some(faNumber1), networkId, locations)
      val expectedKeys = Set(s"admin",
        s"$ViewHoldingViaLocation:$networkId:$location1", s"$ViewHoldingViaLocation:$networkId:$location2", s"$ViewHoldingViaLocation:$networkId:$location3")
      val actualKeys = mockService.getAcceptedAccessKeysForCapabilities(caps, resource)
      actualKeys shouldEqual expectedKeys
    }

    it("returns accessKeys for ViewHoldingViaLocation and ViewHoldingViaFaNumber capabilities") {
      val caps = Set(ViewHoldingViaLocation, ViewHoldingViaFaNumber)
      val resource = TestResource(Some(faNumber2), networkId, locations)
      val expectedKeys = Set(
        s"$ViewHoldingViaLocation:$networkId:$location1", s"$ViewHoldingViaLocation:$networkId:$location2", s"$ViewHoldingViaLocation:$networkId:$location3", s"$ViewHoldingViaFaNumber:$networkId:$faNumber2")
      val actualKeys = mockService.getAcceptedAccessKeysForCapabilities(caps, resource)
      actualKeys shouldEqual expectedKeys
      }
    }

  }

  case class TestResource(faNumber: Option[String], networkId: NetworkId, locations: List[String])

  class TestService extends AcceptedAccessKeysGenerator[TestResource] {

    override def capabilityToAcceptedKeyBuilders: Map[String, AcceptedKeyBuilder[TestResource]] = Map(
      Admin -> AcceptedKeyBuilder(buildAdminKeys),
      ViewHoldingViaLocation -> AcceptedKeyBuilder(buildViewHoldingManagerKeys),
      ViewHoldingViaFaNumber -> AcceptedKeyBuilder(buildViewHoldingKeys)
    )

    def buildViewHoldingManagerKeys(capability: String, holdingRecordInput: TestResource): Set[String] = {
      val networkIdKeys = keyBuilderAppendNetworkId(Set(capability), holdingRecordInput)
      keyBuilderAppendLocations(networkIdKeys, holdingRecordInput)
    }

    def buildViewHoldingKeys(capability: String, holdingRecordInput: TestResource): Set[String] = {
      val networkIdKeys = keyBuilderAppendNetworkId(Set(capability), holdingRecordInput)
      val faNumberKeys = keyBuilderAppendFANumber(networkIdKeys, holdingRecordInput)

      faNumberKeys
    }

    def keyBuilderAppendLocations(currentKeys: Set[String], holdingRecordInput: TestResource): Set[String] = {
      val locations = holdingRecordInput.locations.toSet
      currentKeys.cartesianProduct(locations).map {
        case (currentKey, locationId) => s"$currentKey:$locationId"
      }
    }

    def keyBuilderAppendNetworkId(currentKeys: Set[String], holdingRecordInput: TestResource): Set[String] = {
      currentKeys.map(currentKey => s"$currentKey:${holdingRecordInput.networkId}")
    }

    def keyBuilderAppendFANumber(currentKeys: Set[String], holdingRecordInput: TestResource): Set[String] = {
      holdingRecordInput.faNumber map {
        faNumber => currentKeys map { currentKey => s"$currentKey:$faNumber" }
      } getOrElse Set.empty[String]
    }
}