# Product Feature Sets Service


You can run the service using [Simon CLI](https://simonmarkets.atlassian.net/wiki/spaces/ENG/pages/504201554/Simon+CLI+Documentation) by following the next steps:

1. [Update Simon CLI](https://simonmarkets.atlassian.net/wiki/spaces/ENG/pages/504201554/Simon+CLI+Documentation#Updating) to the latest version.

2. Run `mongodb` container:

```
docker run -d -p 27017:27017 --name mongodb --network simon-network mongo:latest
```
> Note that `mongodb` container is starting with the name `mongodb` and in the network `simon-network` so that the local services can connect to it using `mongodb:27017`

3. Run `kong` container (select `kong` from the menu):

```
simon start
> Search: kong
```
> If your `kong` container has been running for a few days - you may experience request delays, so we suggest restarting it here.

> If you are running `kong` for the first time - you would want to sync with `alpha` using `simon openapi sync`

4. Authenticate to access `Secret Manager`:
> We recommend using [okta-aws](https://gitlab.com/simonmarkets/infrastructure/okta-aws) for this step.
```
okta-aws dump -p alpha
```

5. Run your service container (select your service from the menu):

```
simon start
> Search: * your service *
```

6. Confirm that the service `openapi` was deployed to `kong`:

```
simon kong find
> Select "local"
> Search: * your service *
```

7. Done!

> Now your service should be reachable with the auth token from `alpha`. Try hitting `http://localhost:8000/*service-path*/info` with Postman.