openapi: 3.0.0
info:
  title: Product Feature Sets Service
  version: 1.0.0
x-basepath: /simon/api/v1
x-kong-service-defaults:
  read_timeout: 300000
tags:
  - name: service-product-feature-sets
    description: Product Feature Sets Service
x-kong-plugin-aws-lambda:
  config:
    function_name: service-product-feature-sets-${STAGE}-api
    aws_region: us-east-1
    invocation_type: RequestResponse
    awsgateway_compatible: true
    forward_request_body: true
    forward_request_headers: true
    forward_request_method: true
    forward_request_uri: true
    is_proxy_integration: true
    skip_large_bodies: false
    proxy_url: ${HTTP_PROXY}
    proxy_scheme: https
    log_type: None
paths:
  /feature-sets/warmup:
    post:
      x-scopes: [ ]
      summary: Trigger service warmup logic
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.resteasy.awslambda.events.WarmupEvent}
      responses:
        200:
          description: Success
  /feature-sets/info:
    get:
      x-scopes:
        - admin
        - simon-system-user
      summary: Retrieve service info
      responses:
        200:
          description: Success
  /feature-sets/healthcheck:
    get:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      summary: Retrieve service healthcheck
      responses:
        200:
          description: Success
  /feature-sets/uptime:
    get:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      summary: Retrieve service uptime
      responses:
        200:
          description: Success
  /feature-sets/{id}:
    get:
      x-scopes:
        - admin
        - simon-system-user
      summary: Retrieve Feature by id
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.EntityID}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.productfeaturesets.model.view.FeatureView}
    delete:
      x-scopes:
        - admin
        - simon-system-user
      summary: Delete Feature by id
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.EntityID}
      responses:
        200:
          description: Successful operation
    put:
      x-scopes:
        - admin
        - simon-system-user
      summary: Update an existing feature
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.productfeaturesets.model.requests.UpsertFeatureRequest}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.productfeaturesets.model.view.FeatureView}
  /feature-sets:
    post:
      x-scopes:
        - admin
        - simon-system-user
      summary: Create new Feature
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.productfeaturesets.model.requests.UpsertFeatureRequest}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.productfeaturesets.model.view.FeatureView}
    get:
      x-scopes:
        - admin
        - simon-system-user
      summary: Returns all valid feature sets
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.productfeaturesets.model.view.FeatureView}
