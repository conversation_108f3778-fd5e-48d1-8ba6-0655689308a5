include classpath("common.conf")

run-mode {
  type = server-mode
  http-server-config {
    port = 1984
    interface = "0.0.0.0"
    ssl {
      enabled = false
      keystore {
        type = "JKS"
        file = ""
        password = "test123"
      }
    }
  }
}

system-routes {
  service-up {
    path = "simon/api/v1/feature-sets/uptime"
  }
  service-info {
    path = "simon/api/v1/feature-sets/info"
  }
  health-check {
    path = "simon/api/v1/feature-sets/healthcheck"
  }
  warm-up {
    path = "simon/api/v1/feature-sets/warmup"
  }
}

blocking-io-dispatcher {
  type = Dispatcher
  executor = "thread-pool-executor"
  thread-pool-executor {
    fixed-pool-size = 32
  }
  throughput = 1
}

config.resolvers {
  sm {
    region = "us-east-1"
  }
  file {
    root = "app-config/dev"
  }
}

system-user-id = "0oaevl54gplnFvyx70h7"

acl-client-config {
  http-client {
    auth {
      type = OAuth
      client-id = "0oaevl54gplnFvyx70h7"
      client-secret-file = ""
      site = "sm:applicationconfig-issuer-uri"
      authorization-path = ""
      token-path = "/v1/token"
      scope = "read_product_data"
      client-secret = "sm:Okta-secret"
      token-type {
        type = Cookie
        name = SimSSO
      }
    }
  }
  base-url = "https://dev.simonmarkets.com/simon/api"
  cache-config {
      enabled = true
      config {
          service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
          signin-region = "us-east-1"
      }
  }
}

authentication {
  type = "jwt"
  jwt {
    source {
      type = "bearer"
      name = ""
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      keys-url = "sm:applicationconfig-okta-validator"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clock-offset-in-seconds = 0
    }
  }

  user-info {
    source {
      type = "header"
      name = "x-userinfo"
    }
  }
}