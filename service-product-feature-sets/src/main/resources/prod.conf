include classpath("common.conf")

max-registration-record-count = 500

run-mode {
  type = lambda-mode
}

info {
  name = "Feature Set Service"
  description = "A service to save and load platform features"
  repository = "networks"
  module = "service-product-feature-sets"
  version = "${project.version}"
  revision = "${git.commit.id.abbrev}"
  build-timestamp = "${build.timestamp.millis}"
}

system-routes {
  service-up {
    path = "simon/api/v1/feature-sets/uptime"
  }
  service-info {
    path = "simon/api/v1/feature-sets/info"
  }
  health-check {
    path = "simon/api/v1/feature-sets/healthcheck"
  }
  warm-up {
    path = "simon/api/v1/feature-sets/warmup"
  }
}

blocking-io-dispatcher {
  type = Dispatcher
  executor = "thread-pool-executor"
  thread-pool-executor {
    fixed-pool-size = 32
  }
  throughput = 1
}

config.resolvers {
  sm {
    region = "us-east-1"
  }
  file {
    root = "app-config/dev"
  }
}

system-user-id = "c388df581a28404a94bbc9885c1650fd"

acl-client-config {
  http-client {
    auth {
      type = OAuth
      client-id = "0oa2h4re4ym0Fh3s82p7"
      client-secret = ""
      site = "sm:applicationconfig-issuer-uri"
      authorization-path = ""
      token-path = "/v1/token"
      scope = "read_product_data"
      client-secret = "sm:applicationconfig-oktaclientsecret"
      token-type: {
        type = Cookie
        name = SimSSO
      }
    }
    proxy {
      address = "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
      port = 3128
    }
  }
  base-url = "https://origin-dc1.api.simonmarkets.com/simon/api"
  cache-config {
    enabled = true
    config {
      service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
      signin-region = "us-east-1"
    }
  }
}

authentication {
  type = "jwt"
  jwt {
    source {
      type = "cookieOrHeader" # header / bearer
      name = "SimSSO:X-Simon-AccessToken"
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      keys-url = "sm:applicationconfig-okta-validator"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clock-offset-in-seconds = 0
      proxy-host = "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
      proxy-port = 3128
    }
  }

  user-info {
    source {
      type = header-source
      name = "x-simon-accesstoken"
    }
  }
}