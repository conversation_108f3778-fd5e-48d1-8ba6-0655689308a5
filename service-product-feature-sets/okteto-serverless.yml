service: service-product-feature-sets

provider:
  name: aws
  runtime: java11
  region: us-east-1
  profile: ${self:custom.file.provider.profile}
  stage: ${self:custom.file.provider.environment}
  versionFunctions: false
  deploymentBucket:
    name: simon-dev-local-serverless

custom:
  localstack:
    stages:
      - dev-local
    host: http://localstack-svc.${env:OKTETO_NAMESPACE}
    edgePort: 4566
    debug: true
  file: ${file(../config/okteto.yml)}
  warmup:
    default:
      enabled: true
      concurrency: 1
      prewarm: true
      timeout: 300
      events:
        - schedule: 'cron(0/5 12-22 ? * MON-FRI *)'  # every 5 min Mon-Fri 8 to 5 (UTC 12-22, EST 7am-5pm, EDT 8am-6pm)
      vpc: false
  newRelic:
    javaNewRelicHandler: handleStreamsRequest

plugins:
  - serverless-localstack
  - serverless-deployment-bucket
  - serverless-plugin-warmup


package:
  artifact: target/service-product-feature-sets-uber.jar

functions:
  api:
    handler: com.simonmarkets.productfeaturesets.ProductFeatureSetsApp$ProductFeatureSetsLambda$
    reservedConcurrency: 1
    # memorySize: 1024
    timeout: 300
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=okteto.conf
