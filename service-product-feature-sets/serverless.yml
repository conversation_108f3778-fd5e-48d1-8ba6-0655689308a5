service: service-product-feature-sets

provider:
  name: aws
  runtime: java11
  region: us-east-1
  profile: ${self:custom.file.provider.profile}
  iam:
    role: ${self:custom.file.provider.iam.role}
  stage: ${opt:stage, 'alpha'}
  versionFunctions: false
  tracing:
    lambda: true
  vpc:
    securityGroupIds: ${self:custom.file.provider.securityGroupIds}
    subnetIds: ${self:custom.file.provider.subnetIds}
  deploymentBucket:
    name: simon-${self:provider.profile}-serverless
  logRetentionInDays: ${self:custom.file.logRetentionInDays}

custom:
  file: ${file(../config/${opt:stage}.yml)}
  warmup:
    default:
      enabled: true
      concurrency: 10
      prewarm: true
      timeout: 60
      events:
        - schedule: 'cron(0/5 12-22 ? * MON-FRI *)'  # every 5 min Mon-Fri 8 to 5 (UTC 12-22, EST 7am-5pm, EDT 8am-6pm)
      vpc: false
      role: ${self:provider.iam.role}
  logSubscription: ${self:custom.file.logSubscription}
  newRelic:
    javaNewRelicHandler: handleStreamsRequest

plugins:
  - serverless-plugin-warmup
  - serverless-plugin-log-subscription

package:
  artifact: target/service-product-feature-sets-uber.jar

functions:
  api:
    handler: com.simonmarkets.productfeaturesets.ProductFeatureSetsApp$ProductFeatureSetsLambda$
    reservedConcurrency: 20
    memorySize: 1024
    timeout: 60
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=${opt:stage}.conf ${self:custom.file.proxySystemProperty, ''}