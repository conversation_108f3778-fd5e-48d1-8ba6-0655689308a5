name: networks

build:
  project:
    dockerfile: okteto-files/okteto.Dockerfile
    args:
      MVN_EXTRA_ARGS: ${OKTETO_MVN_EXTRA_ARGS}
      ARTIFACTORY_URL: ${ARTIFACTORY_URL}
      OKTETO_PROJECT_BASE_IMAGE: ${OKTETO_PROJECT_BASE_IMAGE:-master-nightly/networks:latest}
  user-acl:
    dockerfile: okteto-files/eks-services.Dockerfile
    target: service-user-acl
    depends_on:
      - project
    args:
      ARTIFACTORY_URL: ${ARTIFACTORY_URL}
      OKTETO_BUILD_PROJECT_IMAGE: ${OKTETO_BUILD_PROJECT_IMAGE}
  users:
    dockerfile: okteto-files/eks-services.Dockerfile
    target: service-users
    depends_on:
      - project
    args:
      ARTIFACTORY_URL: ${ARTIFACTORY_URL}
      OKTETO_BUILD_PROJECT_IMAGE: ${OKTETO_BUILD_PROJECT_IMAGE}

deploy:
  image: ${OKTETO_BUILD_PROJECT_IMAGE}
  commands:
    # Create aws lambdas
    # - name: Creating service-product-feature-sets lambda
    #   command: cd /okteto/src/service-product-feature-sets && sls deploy --stage dev-local -c okteto-serverless.yml --verbose

    - name: Creating serverless-global-rejections-event-handler lambda
      command: cd /okteto/src/serverless-global-rejections-event-handler && sls deploy --stage dev-local -c okteto-serverless.yml --verbose

    - name: Creating service-okta-sync lambda
      command: cd /okteto/src/service-okta-sync && sls deploy --stage dev-local -c okteto-serverless.yml --verbose

    - name: Creating service-tasks lambda
      command: cd /okteto/src/service-tasks && sls deploy --stage dev-local -c okteto-serverless.yml --verbose

    - name: Creating service-user-sync-mapping-validator lambda
      command: cd /okteto/src/service-user-sync-mapping-validator && sls deploy --stage dev-local -c okteto-serverless.yml --verbose

    - name: Creating service-networks lambda
      command: cd /okteto/src/service-networks && sls deploy --stage dev-local -c okteto-serverless.yml --verbose

    - name: Creating service-users lambda
      command: cd /okteto/src/service-users && sls deploy --stage dev-local -c okteto-serverless.yml --verbose

    - name: Creating serverless-sales-fee-rules lambda
      command: cd /okteto/src/serverless-sales-fee-rules && sls deploy --stage dev-local -c okteto-serverless.yml --verbose

    - name: Creating serverless-upload-network-hierarchy lambda
      command: cd /okteto/src/serverless-upload-network-hierarchy && sls deploy --stage dev-local -c okteto-serverless.yml --verbose

    # Upload openapi spec for eks apps
    - name: Upload OpenApi spec for service-user-acl eks app
      command: simon pipeline run --input /okteto/src/service-user-acl/src/main/resources/documentation/openapi_okteto.yaml  --upstream http://user-acl:8086 --service service-user-acl --repo networks --admin-url  https://kong-admin-${OKTETO_NAMESPACE}.${OKTETO_DOMAIN} --token xyz --output service-content_kong.json --backup-file backup_service-content_kong.json --env dev-local --tags service-content --type eks --validate-online

    - name: Upload OpenApi spec for service-users eks app
      command: simon pipeline run --input /okteto/src/service-users/src/main/resources/documentation/openapi_okteto.yaml  --upstream http://users:8086 --service service-users --repo networks --admin-url  https://kong-admin-${OKTETO_NAMESPACE}.${OKTETO_DOMAIN} --token xyz --output service-content_kong.json --backup-file backup_service-content_kong.json --env dev-local --tags service-content --type eks --validate-online

    # Upload openapi spec for lambdas
    # - name: Upload OpenApi spec for service-product-feature-sets lambda
    #   command: simon pipeline run -i /okteto/src/service-product-feature-sets/src/main/resources/documentation/openapi_okteto.yaml --service service-product-feature-sets --repo networks -a https://kong-admin-${OKTETO_NAMESPACE}.${OKTETO_DOMAIN} --upstream https://no-host --type lambda -o kong-output.json --backup-file backup.json -e dev-local --lambda-export-file lambda_export.json --lambda-merged-file lambda_merged.json

    - name: Upload OpenApi spec for service-tasks lambda
      command: simon pipeline run -i /okteto/src/service-tasks/src/main/resources/documentation/openapi_okteto.yaml --service service-tasks --repo networks -a https://kong-admin-${OKTETO_NAMESPACE}.${OKTETO_DOMAIN} --upstream https://no-host --type lambda -o kong-output.json --backup-file backup.json -e dev-local --lambda-export-file lambda_export.json --lambda-merged-file lambda_merged.json

    # - name: Upload OpenApi spec for service-user-sync-mapping-validator lambda
    #   command: simon pipeline run -i /okteto/src/service-user-sync-mapping-validator/src/main/resources/documentation/openapi_okteto.yaml --service service-user-sync-mapping-validator --repo networks -a https://kong-admin-${OKTETO_NAMESPACE}.${OKTETO_DOMAIN} --upstream https://no-host --type lambda -o kong-output.json --backup-file backup.json -e dev-local --lambda-export-file lambda_export.json --lambda-merged-file lambda_merged.json

    - name: Upload OpenApi spec for service-networks lambda
      command: simon pipeline run -i /okteto/src/service-networks/src/main/resources/documentation/openapi_okteto.yaml --service service-networks --repo networks -a https://kong-admin-${OKTETO_NAMESPACE}.${OKTETO_DOMAIN} --upstream https://no-host --type lambda -o kong-output.json --backup-file backup.json -e dev-local --lambda-export-file lambda_export.json --lambda-merged-file lambda_merged.json

    # - name: Upload OpenApi spec for service-users lambda
    #   command: simon pipeline run -i /okteto/src/service-users/src/main/resources/documentation/openapi_okteto.yaml --service service-users --repo networks -a https://kong-admin-${OKTETO_NAMESPACE}.${OKTETO_DOMAIN} --upstream https://no-host --type lambda -o kong-output.json --backup-file backup.json -e dev-local --lambda-export-file lambda_export.json --lambda-merged-file lambda_merged.json

    - name: Upload OpenApi spec for serverless-sales-fee-rules lambda
      command: simon pipeline run -i /okteto/src/serverless-sales-fee-rules/src/main/resources/documentation/openapi_okteto.yaml --service serverless-sales-fee-rules --repo networks -a https://kong-admin-${OKTETO_NAMESPACE}.${OKTETO_DOMAIN} --upstream https://no-host --type lambda -o kong-output.json --backup-file backup.json -e dev-local --lambda-export-file lambda_export.json --lambda-merged-file lambda_merged.json

    - name: Update openapi timeouts for lambda service
      command: |
        rm -f okteto_lambda_service.json && \
        simon pipeline dump -a https://kong-admin-${OKTETO_NAMESPACE}.${OKTETO_DOMAIN} --select-tags "type=lambda" -o okteto_lambda_service.json && \
        jq '(try .services[].routes[].plugins[] | select(.name=="aws-lambda")).config.timeout |= 300000' okteto_lambda_service.json | sponge okteto_lambda_service.json && \
        jq '(try .services[].routes[].plugins[] | select(.name=="aws-lambda")).config.keepalive |= 300000' okteto_lambda_service.json | sponge okteto_lambda_service.json && \
        simon pipeline sync -a https://kong-admin-${OKTETO_NAMESPACE}.${OKTETO_DOMAIN} -i okteto_lambda_service.json --select-tags "type=lambda"

  compose: okteto-files/docker-compose.yml

dependencies:
  localstack:
    repository: **************:simonmarkets/infrastructure/okteto-catalog.git
    manifest: localstack/okteto.yml
    branch: master
    wait: true
    timeout: 15m
    variables:
      LOCALSTACK_SYNC_SECRETS: "true"
      LOCALSTACK_CREATE_S3_BUCKET: |
        - simon-dev-local-serverless
        - serverless-org-upload-okteto
        - automation-test-bucket-okteto
      LOCALSTACK_CREATE_DYNAMODB_TABLE: |
        - user-acl-cache
      LOCALSTACK_CREATE_EVENTBUS: |
        - mongoEventBusNetworks
        - mongoEventBusUsers
        - mongoEventBusGeneralRejectionReasons
        - mongoEventBusExternalIdType

  kong-api-gw:
    repository: **************:simonmarkets/infrastructure/okteto-catalog.git
    manifest: kong-api-gw/okteto.yml
    branch: master
    wait: true
    variables:
      okta_add_origin: "true"

  simonapp:
    repository: **************:simonmarkets/infrastructure/okteto-catalog.git
    manifest: simonapp/okteto.yml
    branch: master
    timeout: 15m

  mongodb:
    repository: **************:simonmarkets/infrastructure/okteto-catalog.git
    manifest: mongodb/okteto.yml
    branch: master
    wait: true
    timeout: 15m
    variables:
      MONGODB_VALUES_YAML: |
        databases:
          - name: pipg
