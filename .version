version=89_0_0
# Do not change the lines below without consulting the DevOps Team.
APP_VERSION=networks-$version

# the services parameter includes service name installed in kong and location of openapispec file for that service
services="service-networks,service-networks/target|service-users,service-users/target|service-user-acl,service-user-acl/target|serverless-sales-fee-rules,serverless-sales-fee-rules/target|service-tasks,service-tasks/target|service-product-feature-sets,service-product-feature-sets/target"
# the dockerimg parameter includes docker image name, top level module directory, and docker image tag
dockerimg="service-users,service-users,service-users|service-user-acl,service-user-acl,service-user-acl"
#deploy will have Image,Tag, EKS deploy most cases it will be Img, some cases EKS deploy name diff from Image name.
deploy="service-users,service-users,service-users,deployment|service-user-acl,service-user-acl,service-user-acl,deployment"
