package com.simonmarkets.sales.fee.rules.enginelib

import com.simonmarkets.quantcommon.contractparams.ContractParameters._
import com.simonmarkets.quantcommon.contractparams.NeoContractParams
import com.simonmarkets.quantcommon.datamodels.{Consts, ContractParams}
import com.simonmarkets.sales.fee.rules.common.{FeeSchedule, FeeType, SalesFeeRule}
import org.junit.runner.RunWith
import org.scalatest.{AsyncWordSpec, Matchers}
import org.scalatest.junit.JUnitRunner
import java.time.LocalDate

@RunWith(classOf[JUnitRunner])
class SalesFeeRulesSpec extends AsyncWordSpec with Matchers {

  val contract = new ContractParams(
    tradeDate = LocalDate.now,
    nonCallPeriods = Some(1.0),
    isCallable = true,
    hasSinglePayment = true,
    contractTypeWrapper = Some(Consts.ContractTypeWrapper.note),
    issuerKey = Some("simonNote"),
    determinationDate = LocalDate.now.minusDays(5),
    hasUpsideParticipation = true,
    hasUpsideJump = false,
    hasAbsoluteReturn = false,
    isFullyProtected = false,
    hasCallPremium = false
  )

  val neoContract = NeoContractParams(
    tradeDate -> LocalDate.now,
    nonCallPeriods -> 3.0,
    isCallable -> true,
    hasSinglePayment -> true,
    contractTypeWrapper -> Consts.ContractTypeWrapper.note,
    issuerKey -> "simonNote",
    determinationDate -> LocalDate.now.minusDays(5),
    hasUpsideParticipation -> true,
    hasUpsideJump -> false,
    hasAbsoluteReturn -> false,
    isFullyProtected -> false,
    hasCallPremium -> false
  )

  val feeScheduleFixedOr24Commission = FeeSchedule(
    12 -> 0.0125,
    18 -> 0.015,
    24 -> 0.02,
    30 -> 0.025

  )

  // For Wholesaler feeType we are in the upper bound.
  val salesFeeRuleWholeSaler: SalesFeeRule = SalesFeeRule(
    id = "1",
    version = 1,
    name = "Rule 1",
    feeType = FeeType.Wholesaler,
    deleted = false,
    issuerKeys = List("simonNote"),
    contractTypes = Nil,
    contractTypeWrappers = List(Consts.ContractTypeWrapper.note),
    fees = FeeSchedule(24 -> 2),
    callable = Some(true),
    fullyProtected = Some(false),
    hasSinglePayment = Some(true),
    nonCallPeriodInMonths = Some(2))

  val salesFeeRuleWholesaler2: SalesFeeRule = SalesFeeRule(
    id = "2",
    version = 1,
    name = "Rule 2",
    feeType = FeeType.Wholesaler,
    deleted = false,
    issuerKeys = List("simonNote"),
    contractTypes = Nil,
    contractTypeWrappers = List(Consts.ContractTypeWrapper.note),
    fees = FeeSchedule(24 -> 1),
    callable = Some(true),
    fullyProtected = Some(false),
    hasSinglePayment = Some(true),
    nonCallPeriodInMonths = Some(4))

  val salesFeeRuleWholeSaler3: SalesFeeRule = SalesFeeRule(
    id = "3",
    version = 1,
    name = "Rule 3",
    feeType = FeeType.Wholesaler,
    deleted = false,
    issuerKeys = List("simonNote"),
    contractTypes = Nil,
    contractTypeWrappers = List(Consts.ContractTypeWrapper.note),
    fees = FeeSchedule(36 -> 1.12),
    callable = Some(true),
    fullyProtected = Some(false),
    hasSinglePayment = Some(true),
    nonCallPeriodInMonths = None)


  // For HomeOffice fee, we have a duplicate check CRUD to see if this is possible.
  val salesFeeRuleHomeOffice = SalesFeeRule(
    id = "2",
    version = 1,
    name = "Rule 2",
    feeType = FeeType.HomeOffice,
    deleted = false,
    issuerKeys = List("simonNote"),
    contractTypes = Nil,
    contractTypeWrappers = List(Consts.ContractTypeWrapper.note),
    fees = FeeSchedule(12 -> 2),
    callable = Some(true),
    fullyProtected = Some(false),
    hasSinglePayment = Some(true),
    nonCallPeriodInMonths = Some(4))

  val salesFeeRuleHomeOffice2 = SalesFeeRule(
    id = "2",
    version = 1,
    name = "Rule 2",
    feeType = FeeType.HomeOffice,
    deleted = false,
    issuerKeys = List("simonNote"),
    contractTypes = Nil,
    contractTypeWrappers = List(Consts.ContractTypeWrapper.note),
    fees = FeeSchedule(24 -> 1),
    callable = Some(true),
    fullyProtected = Some(false),
    hasSinglePayment = Some(true),
    nonCallPeriodInMonths = Some(4))


  // For FA commission, we are below the minimum NC period
  val salesFeeRuleCommission2 = SalesFeeRule(
    id = "2",
    version = 1,
    name = "Rule 2",
    feeType = FeeType.FACommission,
    deleted = false,
    issuerKeys = List("simonNote"),
    contractTypes = Nil,
    contractTypeWrappers = List(Consts.ContractTypeWrapper.note),
    fees = feeScheduleFixedOr24Commission,
    callable = Some(true),
    fullyProtected = Some(false),
    hasSinglePayment = Some(true),
    nonCallPeriodInMonths = Some(6))

  val salesFeeRuleCommission = SalesFeeRule(
    id = "2",
    version = 1,
    name = "Rule 2",
    feeType = FeeType.FACommission,
    deleted = false,
    issuerKeys = List("simonNote"),
    contractTypes = Nil,
    contractTypeWrappers = List(Consts.ContractTypeWrapper.note),
    fees = FeeSchedule(2 -> 0.03),
    callable = Some(true),
    fullyProtected = Some(false),
    hasSinglePayment = Some(true),
    nonCallPeriodInMonths = Some(12))

  val wholeSalerRules = List(salesFeeRuleWholeSaler, salesFeeRuleWholesaler2)
  val homeOfficeRules = List(salesFeeRuleHomeOffice, salesFeeRuleHomeOffice2)
  val faRules = List(salesFeeRuleCommission, salesFeeRuleCommission2)

  "Evaluate binary rules" should {
    "correctly compute feeSchedule" in {
      val result = Rules.evaluateBinaryRules(neoContract, wholeSalerRules ++ homeOfficeRules ++ faRules)
      result.isRight shouldBe true
      result.right.get shouldBe Map(FeeType.Wholesaler -> FeeSchedule(24 -> 1.0), FeeType.FACommission -> FeeSchedule(12 -> 0.0125, 18 -> 0.015, 24 -> 0.02, 30 -> 0.025), FeeType.HomeOffice -> FeeSchedule(24 -> 1.0))
    }
  }
}
