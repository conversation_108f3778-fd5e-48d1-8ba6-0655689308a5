package com.simonmarkets.sales.fee.rules.enginelib

import com.simonmarkets.http.HttpError
import com.simonmarkets.quantcommon.contractparams.NeoContractParams
import com.simonmarkets.quantcommon.parammap.{Id, ParamMap}
import com.simonmarkets.rules.engine.v2.syntax._
import com.simonmarkets.rules.engine.v2.{AlwaysPass, ApprovedPath, Branch, Context, Parameter, Rule, RuleSelection, SingleParameter}
import com.simonmarkets.sales.fee.rules.common.{FeeScheduleTrait, FeeType, SalesFeeRule}

final case class SalesFeeMatchError(errorMsg: String) extends Exception {

  def toHttpError: HttpError = HttpError.internalServerError(errorMsg)

  override def toString: String = errorMsg
}

object Rules {

  private def toContext(params: ParamMap[Id]): Context =
    Context(params.toList.map(pair => SingleParameter(pair.param.id) -> pair.value): _*)

  /**
   * Method to go from a list of SalesFeeRules and a contract, to a Map[FeeType, FeeScheduleTrait],
   * listing the schedule of the matched rules for each feeType.
   *
   * @param contract Contract parameters
   * @param salesFeeRuleList List of sales fee rules
   * @return Either a SalesFeeMatchError indicating some failure during rule construction / matching or the map of
   *         fee types to fee schedules.
   */
  def evaluateBinaryRules(
      contract: NeoContractParams,
      salesFeeRuleList: List[SalesFeeRule]
  ): Either[SalesFeeMatchError, Map[FeeType, FeeScheduleTrait]] = {
    val contractContext = toContext(contract.params)
    val matchedSalesFeeRules = salesFeeRuleList
      // now, have a zip which finds the original sfr which led to the (ONLY) one approval
      .map(sfr => (sfr, salesFeeRuleToRuleSelection(sfr).rule.evaluate(contractContext).result))
      .collect { case (sfr, ApprovedPath(_, _)) => sfr }
    extractFeeSchedulePerType(evaluateRulesByFeeType(matchedSalesFeeRules, contract, contractContext))
  }

  /**
   * Helper method for collecting errors from downstream matching per feeTypes to a unified error here, or
   * collecting feeTypes to relevant feeSchedule grids if all matched correctly.
   *
   * @param result A List of FeeType to Either a salesFeeMatchError or a SalesFeeRule (the final matched rule to
   * use the grid from)
   * @return Either a (aggregated) SalesFeeMatchError or a Map from feeType to the relevant feeSchedule.
   */
  private def extractFeeSchedulePerType(
      result: Map[FeeType, Either[SalesFeeMatchError, SalesFeeRule]]
  ): Either[SalesFeeMatchError, Map[FeeType, FeeScheduleTrait]] =
    if (result.forall(_._2.isRight)) {
      Right(result.collect {
        case (feeType, Right(salesFeeRule)) => feeType -> salesFeeRule.fees
      })
    } else {
      Left(SalesFeeMatchError(
        result
          .collect { case (feeType, Left(err)) => s"${err.errorMsg} failed on feeType $feeType" }
          .mkString(", ")
      ))
    }

  /**
   * Method for extracting the relevant matched salesFeeRule per feeType, including searching over nonCallPeriods
   * if necessary.
   *
   * @param matchedRules List of sales fee rules that have matched on their binary params
   * @param contract Contract parameters
   * @param contractContext Context constructed from the contract parameters
   * @return An iterable (per feeType), of Either a SalesFeeMatchError or a salesFeeRule
   */
  private def evaluateRulesByFeeType(
      matchedRules: List[SalesFeeRule],
      contract: NeoContractParams,
      contractContext: Context
  ): Map[FeeType, Either[SalesFeeMatchError, SalesFeeRule]] =
    matchedRules.groupBy(_.feeType).map { // group by feeType (faCommission etc)
      case (feeType, feeRuleList) =>
        feeType -> (feeRuleList match {
          case Nil =>
            Left(SalesFeeMatchError(s"No rule matched for the supplied contract on feeType: $feeType")) // no rule matched
          case sfr :: Nil if contract.nonCallPeriods.isEmpty =>
            Right(sfr) // matched //TODO: if only one matched, no reason to search over NonCall
          case sfr :: Nil if contract.nonCallPeriods.isDefined =>
            Left(SalesFeeMatchError(s"Only one rule with nonCallPeriod has matched, check salesFeeRules: $sfr, for feeType: $feeType"))
          case sfrList if contract.nonCallPeriods.isDefined =>
            nonCallPeriodsRuleEvaluation(contractContext, sfrList)
          case sfrList =>
            Left(SalesFeeMatchError(s"Multiple rules matched for feeType: $feeType, without a nonCallPeriod on the contract. Check these rules: $sfrList"))
        })
    }

  /**
   * Helper method for searching over the relevant nonCallPeriods, only if multiple rules matched on the
   * binary params, meaning we need to find the relevant grid based on nonCallPeriods.
   *
   * @param contractContext contract context constructed from NeoContractParams
   * @param salesFeeRuleList a list of the matched salesFeeRules
   * @return Either a SalesFeeMatchError or a tuple of SalesFeeRule for the matched rule based on nonCall values.
   */
  private def nonCallPeriodsRuleEvaluation(
      contractContext: Context,
      salesFeeRuleList: List[SalesFeeRule]
  ): Either[SalesFeeMatchError, SalesFeeRule] =
    if (salesFeeRuleList.forall(_.nonCallPeriodInMonths.isDefined)) {
      val matchedRules = extractSortNonCall(salesFeeRuleList)
        .map { case (sfr, selection) => (sfr, selection.rule.evaluate(contractContext).result) }
        .collect { case (sfr, ApprovedPath(_, _)) => sfr }

      matchedRules match {
        case Nil => Left(SalesFeeMatchError("No nonCallPeriod rule match for the supplied contract"))
        case sfr :: Nil => Right(sfr)
        case x => Left(SalesFeeMatchError(s"More than one nonCallPeriodRules have matched: $x"))
      }
    } else {
      Left(SalesFeeMatchError("Found a nonCallPeriod contract with rules matched that have nonCallPeriods"))
    }

  /**
   * Helper to create a range rule based on a lower and upper limit to the range. By default, lower is inclusive
   * and upper is exclusive.
   *
   * @param lower lower bound of range for a range rule target (inclusive)
   * @param upper upper bound of range for a range rule target (exclusive)
   * @param param the parameter to create this range target for
   */
  private def createRangeTargetRule(lower: BigDecimal, upper: BigDecimal, param: Parameter[BigDecimal]): RuleSelection =
    RuleSelection(s"nonCallPeriod between: $lower and $upper", AlwaysPass, param >= lower && param < upper)

  private object HasNonCallPeriod {
    def unapply(rule: SalesFeeRule): Option[BigDecimal] = rule.nonCallPeriodInMonths
  }

  /**
   * Helper to go from a list of approved salesFeeRulesList with nonCallPeriods we'd like to search over,
   * to a sorted list of tuples (SalesFeeRule, RuleSelection), pairing the salesFeeRule with the
   * constructed range target rule which corresponds to the desired nonCallPeriods range for each salesFeeRule.
   *
   * @param salesFeeRuleList matched salesFeeRules
   * @return List of tuples of salesFeeRules and the applicable (range) rule.
   */
  private def extractSortNonCall(salesFeeRuleList: List[SalesFeeRule]): List[(SalesFeeRule, RuleSelection)] = {
    val sortedNonCallSfr = salesFeeRuleList.sortWith {
      case (HasNonCallPeriod(a), HasNonCallPeriod(b)) => a <= b
      case _ => false
    }
    val rules = sortedNonCallSfr.sliding(2).flatMap {
      case HasNonCallPeriod(a) :: (next@HasNonCallPeriod(b)) :: Nil =>
        List(next -> createRangeTargetRule(a, b, Parameters.nonCallPeriods))
      case _ =>
        List()
    }.toList
    padRules(sortedNonCallSfr, rules)
  }

  /**
   * Helper to create padding of ranges (extrapolation down to 0 and up to infinity of edges) for
   * boundary conditions of nonCallPeriods.
   *
   * @param sortedNonCallSfr list of all matched salesFeeRules sorted by nonCallPeriods
   * @param rules list of tuples of salesFeeRule and the corresponding range rules.
   * @return Extrapolated/padded list of tuples of salesFeeRules and the corresponding range rules
   */
  private def padRules(
      sortedNonCallSfr: List[SalesFeeRule],
      rules: List[(SalesFeeRule, RuleSelection)]
  ): List[(SalesFeeRule, RuleSelection)] = {
    // calling unsafe .get here because the salesFeeRules passed in here are already filtered for having a nonCallPeriodInMonths defined in the nonCallPeriodRuleEvaluation method which calls this
    val lowerPadding = (sortedNonCallSfr.head, createRangeTargetRule(0, sortedNonCallSfr.head.nonCallPeriodInMonths.get, Parameters.nonCallPeriods)) // for lower and higher padding, remove the edges of existing ones.
    //TODO: edit higher padding to have something like: ((higherPadding._1, rules.last._2.ruleProcess or higherPadding._2.ruleProcess)) - but make sure to edit the ruleSelection to have the correct name for the bounds. They should both point to one Rule only.
    val higherPadding = (sortedNonCallSfr.last, RuleSelection(s"nonCallPeriod greater than ${sortedNonCallSfr.init.last.nonCallPeriodInMonths}", AlwaysPass,
      Parameters.nonCallPeriods >= sortedNonCallSfr.init.last.nonCallPeriodInMonths.get
    ))
    (lowerPadding :: rules.dropRight(1)) :+ higherPadding
  }

  /**
   * Method for constructing a RuleSelection (rule-tree) for all the binary params that constitute a salesFeeRule.
   *
   * @param salesFeeRule Sales fee rule
   * @return Rule for all binary params of a sales fee rule
   */
  private def salesFeeRuleToRuleSelection(salesFeeRule: SalesFeeRule): RuleSelection = {
    val isCallableRule = salesFeeRule.callable.map[Rule](Parameters.isCallable.is(_)).toList
    val isFullyProtectedRule = salesFeeRule.fullyProtected.map(Parameters.isFullyProtected.is(_)).toList
    val hasSinglePaymentRule = salesFeeRule.hasSinglePayment.map(Parameters.hasSinglePayment.is(_)).toList
    val issuerRule = List(salesFeeRule.issuerKeys).filter(_.nonEmpty).map(Parameters.issuerKey.in(_))
    val contractTypeWrapperRule = List(salesFeeRule.contractTypeWrappers).filter(_.nonEmpty).map(Parameters.contractTypeWrapper.in(_))
    val rules = (isCallableRule ++ isFullyProtectedRule ++ hasSinglePaymentRule ++ issuerRule ++ contractTypeWrapperRule).map(Branch(_))
    RuleSelection("salesFeeRule", AlwaysPass, if (rules.nonEmpty) rules.reduce((a, b) => a && b) else AlwaysPass)
  }
}
