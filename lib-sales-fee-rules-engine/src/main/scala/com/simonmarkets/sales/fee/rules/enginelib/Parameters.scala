package com.simonmarkets.sales.fee.rules.enginelib

import com.simonmarkets.rules.engine.v2.{Parameter, SingleParameter}

object Parameters {

  // contract params
  val isCallable: Parameter[Boolean] = SingleParameter("isCallable")
  val isFullyProtected: Parameter[Boolean] = SingleParameter("isFullyProtected")
  val hasSinglePayment: Parameter[Boolean] = SingleParameter("hasSinglePayment")
  val nonCallPeriods: Parameter[BigDecimal] = SingleParameter("nonCallPeriods")
  val contractTypeWrapper: Parameter[String] = SingleParameter("contractTypeWrapper")
  val issuerKey: Parameter[String] = SingleParameter("issuerKey")
}
