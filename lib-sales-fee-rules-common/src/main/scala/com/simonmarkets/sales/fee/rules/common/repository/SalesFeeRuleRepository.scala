package com.simonmarkets.sales.fee.rules.common.repository

import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.ChangelogItem
import com.simonmarkets.sales.fee.rules.common.SalesFeeRule

import scala.concurrent.Future

trait SalesFeeRuleRepository {

  def findAll: Future[Iterable[SalesFeeRule]]

  def findAllById(id: Seq[String]): Future[List[SalesFeeRule]]

  def findById(id: String): Future[Option[SalesFeeRule]]

  def delete(id: String): Future[Boolean]

  def insertAndSnap(feeRule: SalesFeeRule, userId: String, comment: Option[String], retries: Int)(implicit traceId: TraceId): Future[SalesFeeRule]

  def updateAndSnap(feeRule: SalesFeeRule, userId: String, comment: Option[String], retries: Int)(implicit traceId: TraceId): Future[SalesFeeRule]

  def getChangelogs(networkId: String): Future[List[ChangelogItem]]

  def getSnapshotEntity(snapshotId: String): Future[Option[SalesFeeRule]]
}
