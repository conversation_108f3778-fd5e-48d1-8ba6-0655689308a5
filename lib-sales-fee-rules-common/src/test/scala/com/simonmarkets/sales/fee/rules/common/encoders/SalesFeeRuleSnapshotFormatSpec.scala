package com.simonmarkets.sales.fee.rules.common.encoders

import com.simonmarkets.networks.common.{ChangelogItem, EntitySnapshot}
import com.simonmarkets.sales.fee.rules.common.{FeeSchedule, FeeType, SalesFeeRule}
import org.scalatest.{Matchers, WordSpec}

import java.time.LocalDateTime

class SalesFeeRuleSnapshotFormatSpec extends WordSpec with Matchers {

  val salesFeeRule: SalesFeeRule = SalesFeeRule("1", 0, "testRuleId1", FeeType.HomeOffice,
    deleted = false, List("GSBank"), List("GS"), List("Note"), FeeSchedule(240 -> 2.0),
    None, None, None, None)

  val snapshotWithNoneComment: EntitySnapshot[SalesFeeRule] = EntitySnapshot(
    id = "123abc",
    userId = "userId",
    modificationDate = LocalDateTime.of(2020, 2, 24, 1, 1),
    comment = None,
    entity = salesFeeRule
  )

  val snapshotWithComment: EntitySnapshot[SalesFeeRule] = EntitySnapshot(
    id = "123abc",
    userId = "userId",
    modificationDate = LocalDateTime.of(2020, 2, 24, 1, 1),
    comment = Some("my comment"),
    entity = salesFeeRule
  )

  val changelogWithNoneComment: ChangelogItem = snapshotWithNoneComment.asChangelogItem

  val changelogWithComment: ChangelogItem = snapshotWithComment.asChangelogItem

  "SalesFeeRuleSnapshotFormat" can {
    "encode sales fee rule snapshot" should {
      "encode snapshot without comment" in {
        val encoded = SalesFeeRuleSnapshotFormat.write(snapshotWithNoneComment)
        val decoded = SalesFeeRuleSnapshotFormat.read(encoded)

        decoded shouldBe snapshotWithNoneComment
      }

      "encode snapshot with comment" in {
        val encoded = SalesFeeRuleSnapshotFormat.write(snapshotWithComment)
        val decoded = SalesFeeRuleSnapshotFormat.read(encoded)

        decoded shouldBe snapshotWithComment
      }
    }

    "decode changelog item" should {
      "decode changelog item without comment" in {
        val encoded = SalesFeeRuleSnapshotFormat.write(snapshotWithNoneComment)
        val decoded = SalesFeeRuleSnapshotFormat.readChangelogItem(encoded)

        decoded shouldBe changelogWithNoneComment
      }

      "decode changelog item with comment" in {
        val encoded = SalesFeeRuleSnapshotFormat.write(snapshotWithComment)
        val decoded = SalesFeeRuleSnapshotFormat.readChangelogItem(encoded)

        decoded shouldBe changelogWithComment
      }
    }
  }

}
