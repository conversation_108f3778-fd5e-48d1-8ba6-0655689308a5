package com.simonmarkets.sales.fee.rules.common.encoders

import com.simonmarkets.sales.fee.rules.common.{FeeSchedule, FeeType, SalesFeeRule}
import org.scalatest.{Matchers, WordSpec}

class SalesFeeRuleFormatSpec extends WordSpec with Matchers {

  val salesFeeRuleWithNoneFields: SalesFeeRule = SalesFeeRule(
    "feeRuleId", 0, "feeRuleName", FeeType.HomeOffice,
    deleted = false, List("GSBank"), List("GS"), List("Note"),
    FeeSchedule(240 -> 2.0),
    callable = None,
    fullyProtected = None,
    hasSinglePayment = None,
    nonCallPeriodInMonths = None
  )

  val salesFeeRule: SalesFeeRule = SalesFeeRule(
    "feeRuleId", 0, "feeRuleName", FeeType.HomeOffice,
    deleted = false, List("GSBank"), List("GS"), List("Note"),
    FeeSchedule(240 -> 2.1),
    callable = Some(true),
    fullyProtected = Some(false),
    hasSinglePayment = Some(true),
    nonCallPeriodInMonths = Some(0.0)
  )

  "SalesFeeRuleFormat" can {
    "encode sales fee rule" should {
      "encode sales fee rule with None fields" in {
        val encoded = SalesFeeRuleFormat.write(salesFeeRule)
        val decoded = SalesFeeRuleFormat.read(encoded)

        decoded shouldBe salesFeeRule
      }

      "encode sales fee rule with Some fields" in {
        val encoded = SalesFeeRuleFormat.write(salesFeeRule)
        val decoded = SalesFeeRuleFormat.read(encoded)

        decoded shouldBe salesFeeRule
      }
    }
  }

}
