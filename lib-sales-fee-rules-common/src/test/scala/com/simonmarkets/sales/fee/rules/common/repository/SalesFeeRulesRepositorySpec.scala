package com.simonmarkets.sales.fee.rules.common.repository

import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.EntitySnapshot
import com.simonmarkets.sales.fee.rules.common.encoders.{SalesFeeRuleFormat, SalesFeeRuleSnapshotFormat}
import com.simonmarkets.sales.fee.rules.common.{FeeSchedule, FeeType, SalesFeeRule}
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.Document
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, Matchers}

import java.time.LocalDateTime

import scala.concurrent.Await
import scala.concurrent.duration._

class SalesFeeRulesRepositorySpec extends AsyncWordSpec with EmbeddedMongoLike with BeforeAndAfterEach with Matchers {
  implicit val traceId: TraceId = TraceId("mongo-sales-fee-rules-repo-spec")

  private lazy val collection = db.getCollection[Document]("sales_fee_rules_test")
  private lazy val snapshotCollection = db.getCollection[Document]("sales_fee_rules_test.snapshots")
  private lazy val salesFeeRulesRepository = new MongoSalesFeeRuleRepository(collection, snapshotCollection, client)

  private val duration = 5.seconds

  private val salesFeeRule1: SalesFeeRule = SalesFeeRule("1", 0, "testRuleId1", FeeType.HomeOffice,
    deleted = false, List("GSBank"), List("GS"), List("Note"), FeeSchedule(240 -> 2.0), None, None, None, None)

  private val salesFeeRule2: SalesFeeRule = SalesFeeRule("2", 1, "testRuleId2", FeeType.HomeOffice,
    deleted = true, List("GSBank"), List("GS"), List("Note"), FeeSchedule(240 -> 2.0), None, None, None, None)

  private val salesFeeRule3: SalesFeeRule = SalesFeeRule("3", 0, "testRuleId3", FeeType.HomeOffice,
    deleted = false, List("GSBank"), List("GS"), List("Note"), FeeSchedule(240 -> 2.0), None, None, None, None)

  private val salesFeeRule4: SalesFeeRule = SalesFeeRule("4", 4, "testRuleId4", FeeType.HomeOffice,
    deleted = true, List("GSBank"), List("GS"), List("Note"), FeeSchedule(240 -> 2.0), None, None, None, None)

  private val salesFeeRules = List(salesFeeRule1, salesFeeRule2, salesFeeRule3, salesFeeRule4)
  private val testSnaps: List[EntitySnapshot[SalesFeeRule]] = getTestSnapshots(8, salesFeeRules.slice(0,5))
  private val salesFeeRulesDocs: Seq[Document] = salesFeeRules.map(SalesFeeRuleFormat.write)
  private val snapDocs: Seq[Document] = testSnaps.map(SalesFeeRuleSnapshotFormat.write)

  override def beforeEach(): Unit = {
    Await.result(collection.drop().toFuture(), duration)
    Await.result(snapshotCollection.drop().toFuture(), duration)
    Await.result(collection.insertMany(salesFeeRulesDocs).toFuture(), duration)
    Await.result(snapshotCollection.insertMany(snapDocs).toFuture(), duration)
  }

  "MongoSalesFeeRulesRepository" can {
    "findById" should {
      "return sales fee rule matching provided id" in {
        salesFeeRulesRepository.findById(salesFeeRule1.id).map { feeRule =>
          feeRule should be(Some(salesFeeRule1))
        }
      }

      "return nothing if no matching sales fee rule found" in {
        salesFeeRulesRepository.findById("nonExistingId").map { feeRule =>
          feeRule should be(None)
        }
      }
    }

    "findAll" should {
      "list available sales fee rules" in {
        salesFeeRulesRepository.findAll.map { list =>
          list should be(List(salesFeeRule1, salesFeeRule3))
        }
      }
    }

    "findByIds" should {
      "list available sales fee rules by provided ids" in {
        salesFeeRulesRepository.findAllById(Seq(salesFeeRule1.id, salesFeeRule2.id)).map { list =>
          list should contain only (List(salesFeeRule1): _*)
        }
      }

      "return empty list if sales fee rules doesn't exist" in {
        salesFeeRulesRepository.findAllById(Seq("nonExistingId")).map { list =>
          list should be(empty)
        }
      }

      "findByIds" should {
        "list only existing sales fee rules" in {
          salesFeeRulesRepository.findAllById(Seq(salesFeeRule1.id, "nonExistingId")).map { list =>
            list should contain only (List(salesFeeRule1): _*)
          }
        }
      }
    }

    "delete" should {
      "update sales fee rule deleted field to true" in {
        salesFeeRulesRepository.delete(salesFeeRule1.id).map { result =>
          result should be(true)
        }
      }

      "update nothing if sales fee rule deleted field set to true" in {
        salesFeeRulesRepository.delete(salesFeeRule2.id).map { result =>
          result should be(false)
        }
      }
    }

    "getChangeLogs" should {
      "return changelogs matching on entity id" in {
        val expected = List(testSnaps.head.asChangelogItem, testSnaps(4).asChangelogItem)
          salesFeeRulesRepository.getChangelogs(salesFeeRule1.id).map { result =>
          result should be(expected)
        }
      }

      "return nothing if no snapshot matched entity id" in {
        salesFeeRulesRepository.getChangelogs("nonExistingId").map { result =>
          result should be(Nil)
        }
      }
    }

    "getSnapshotEntity" should {
      "return entity in matching snapshot" in {
        salesFeeRulesRepository.getSnapshotEntity("snap_1").map { result =>
          result should be(Some(testSnaps(1).entity))
        }
      }

      "return nothing if no snapshot matched id" in {
        salesFeeRulesRepository.getSnapshotEntity("notfound").map { result =>
          result should be(None)
        }
      }
    }
  }

  def getTestSnapshots( count: Int, salesFeeRules: List[SalesFeeRule]): List[EntitySnapshot[SalesFeeRule]] = {
    (0 until count).toList.map(i =>
      EntitySnapshot(
        id = "snap_"+i.toString,
        userId = "userId_" + i.toString,
        modificationDate = LocalDateTime.of(1993, 7, 24, 1, 1),
        comment = Some("comment_" + i.toString),
        entity = salesFeeRules( i % salesFeeRules.size)
      )
    )
  }
}
