service: user-networks-db-migration

provider:
  name: aws
  runtime: java11
  memorySize: 1024
  region: us-east-1
  profile: ${self:custom.file.provider.profile}
  iam:
    role: ${self:custom.file.provider.iam.role}
  stage: ${opt:stage, 'alpha'}
  versionFunctions: false
  tracing:
    lambda: true
  vpc:
    securityGroupIds: ${self:custom.file.provider.securityGroupIds}
    subnetIds: ${self:custom.file.provider.subnetIds}
  deploymentBucket:
    name: simon-${self:provider.profile}-serverless
  logRetentionInDays: ${self:custom.file.logRetentionInDays}

custom:
  file: ${file(../config/${opt:stage}.yml)}
  logSubscription: ${self:custom.file.logSubscription}
  newRelic:
    javaNewRelicHandler: handleStreamsRequest

plugins:
  - serverless-plugin-log-subscription

package:
  artifact: target/db-migration-uber.jar

functions:
  api:
    handler: com.simonmarkets.db.migration.MigrationController
    memorySize: 2048
    timeout: 900
    environment:
      JAVA_TOOL_OPTIONS: -Dsimon.env=${opt:stage} -Dconfig.file=${opt:stage}.conf ${self:custom.file.proxySystemProperty, ''}
      ENV_KEY: ${self:custom.file.provider.profile}
