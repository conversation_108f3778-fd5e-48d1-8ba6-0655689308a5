package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.SimonUICapabilities
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0110_rollout_unified_annuity_marketplaceSpec
  extends WordSpec
    with Matchers
    with EmbeddedMongoLike
    with BeforeAndAfterEach {

  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  lazy val newCapabilities: Set[String] = Set(SimonUICapabilities.ViewUIUnifiedAnnuitiesMarketplaceCapability.name)

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0110_rollout_unified_annuity_marketplace" should {

    "add unified marketplace view capability" in {
      val base = Network(
        id = simon.Id.NetworkId("network1"),
        name = "name",
        networkCode = "XYZ",
        eventInfo = EventInfo.Default,
        loginMode = LoginMode.UsernamePassword,
        idHubOrganization = IdHubOrganization(1, "idHubOrg"),
      )

      val testFaFIA: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("viewUIFIAPortal"))
      val testFaSVA: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("viewUISVAPortal"))
      val testFaFA: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("viewUIFAPortal"))
      val testFaVA: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("viewUIVAPortal"))
      val testFaOff: CustomRoleDefinition = CustomRoleDefinition(UserRole.Issuer.productPrefix, Set("viewUIOffPlatformAnnuities"))

      val fiaNetwork = base.copy(id = simon.Id.NetworkId("net1"), customRolesConfig = Set(testFaFIA))
      val faNetwork = base.copy(id = simon.Id.NetworkId("net2"), customRolesConfig = Set(testFaFA))
      val svaNetwork = base.copy(id = simon.Id.NetworkId("net3"), customRolesConfig = Set(testFaSVA))
      val vaNetwork = base.copy(id = simon.Id.NetworkId("net4"), customRolesConfig = Set(testFaVA))
      val offNetwork = base.copy(id = simon.Id.NetworkId("net5"), customRolesConfig = Set(testFaOff))

      val impactedNetworks = List(fiaNetwork, faNetwork, svaNetwork, vaNetwork, offNetwork)

      helper.verifyCapabilityAbsent(helper.allNetworks, newCapabilities)

      impactedNetworks.map { n =>
        val doc = NetworkFormat.write(n)
        helper.networksCol.insertOne(doc).toFuture().await()
      }

      helper.verifyCapabilityPresent(Seq((fiaNetwork, UserRole.EqPIPGFA.productPrefix)), Set("viewUIFIAPortal"))
      helper.verifyCapabilityPresent(Seq((svaNetwork, UserRole.EqPIPGFA.productPrefix)), Set("viewUISVAPortal"))
      helper.verifyCapabilityPresent(Seq((faNetwork, UserRole.EqPIPGFA.productPrefix)), Set("viewUIFAPortal"))
      helper.verifyCapabilityPresent(Seq((vaNetwork, UserRole.EqPIPGFA.productPrefix)), Set("viewUIVAPortal"))
      helper.verifyCapabilityPresent(Seq((offNetwork, UserRole.Issuer.productPrefix)), Set("viewUIOffPlatformAnnuities"))

      val migration = new _0110_rollout_unified_annuity_marketplace(db)
      migration.apply.await

      helper.verifyCapabilityPresent(Seq((fiaNetwork, UserRole.EqPIPGFA.productPrefix)), Set("viewUIFIAPortal") ++ newCapabilities)
      helper.verifyCapabilityPresent(Seq((svaNetwork, UserRole.EqPIPGFA.productPrefix)), Set("viewUISVAPortal") ++ newCapabilities)
      helper.verifyCapabilityPresent(Seq((faNetwork, UserRole.EqPIPGFA.productPrefix)), Set("viewUIFAPortal") ++ newCapabilities)
      helper.verifyCapabilityPresent(Seq((vaNetwork, UserRole.EqPIPGFA.productPrefix)), Set("viewUIVAPortal") ++ newCapabilities)
      helper.verifyCapabilityPresent(Seq((offNetwork, UserRole.Issuer.productPrefix)), Set("viewUIOffPlatformAnnuities") ++ newCapabilities)
      helper.verifyCapabilityAbsent(helper.allNetworks, newCapabilities)
    }

  }

}

