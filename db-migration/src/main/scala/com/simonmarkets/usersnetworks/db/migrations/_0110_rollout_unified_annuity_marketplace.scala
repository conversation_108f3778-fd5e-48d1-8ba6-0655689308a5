package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.SimonUICapabilities
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0110_rollout_unified_annuity_marketplace(val db: MongoDatabase)
  extends ChangeSet(db) with UpdateNetworkCapabilities {
  override implicit val traceId: TraceId = TraceId("_0110_rollout_unified_annuity_marketplace")

  lazy private val relevantCapabilities: Set[Role] = Set(
    SimonUICapabilities.ViewUIFIAPortalCapability.name,
    SimonUICapabilities.ViewUISVAPortalCapability.name,
    SimonUICapabilities.ViewUIFAPortalCapability.name,
    SimonUICapabilities.ViewUIVAPortalCapability.name,
    SimonUICapabilities.ViewUIOffPlatformAnnuitiesCapability.name,
  )

  /**
   * Capabilities to add or remove
   */
  override def capabilities: Set[Capability] = Set(
    SimonUICapabilities.ViewUIUnifiedAnnuitiesMarketplaceCapability.name
  )

  /**
   * Whether to add or remove the specified capabilities
   *
   * @return true if this migration will add capabilities. false if it will remove them
   */
  override def shouldAdd: Boolean = true

  /**
   * Whether the given network / custom role should be updated
   *
   * @return true if this migration will update capabilities for given network / role. false if it will remove them
   */
  override def shouldUpdate(network: Network, customRole: Role): Boolean = network.customRolesConfig
    .exists { rolesConfig =>
      customRole == rolesConfig.role &&
        rolesConfig.capabilities.exists(relevantCapabilities.contains)
    }
}
