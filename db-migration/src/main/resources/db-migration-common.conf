change-set-package = "com.simonmarkets.usersnetworks.db.migrations"

config.resolvers {
  sm {
    region = "us-east-1"
  }
  ssm {
    region = "us-east-1"
  }
  file = {
    root = ""
  }
}

mongo-db {
  database-name = "pipg"
  change-set-collection-name = "users_networks.migrationsv2"
  connection {
    app-name = "service-users"
    connection = "sm:config:applicationconfig-mongo-auth-pipg"
  }
}
