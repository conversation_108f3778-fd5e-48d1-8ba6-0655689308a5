<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>users-networks</artifactId>
        <groupId>com.simonmarkets</groupId>
        <version>sandbox-SNAPSHOT</version>
    </parent>

    <artifactId>db-migration</artifactId>

    <dependencies>

        <!-- Scala Language Dependency -->
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
        </dependency>

        <!-- Simon Dependencies -->
        <dependency>
            <groupId>com.simonmarkets.dbmigrations</groupId>
            <artifactId>lib-db-migration-mongo</artifactId>
        </dependency>

        <!--local-->
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>om-networks</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-networks-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>service-users</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.goldmansachs.marquee</groupId>
                    <artifactId>lib-encryption</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--test-->
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_${scala.version.short}</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets.utils</groupId>
            <artifactId>scala-mongo-embedded-test</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>

        <plugins>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>${maven-shade-plugin.version}</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <minimizeJar>true</minimizeJar>
                            <shadedArtifactAttached>true</shadedArtifactAttached>
                            <shadedClassifierName>uber</shadedClassifierName>
                            <transformers>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                    <resource>reference.conf</resource>
                                </transformer>
                            </transformers>
                            <filters combine.children="append">
                                <filter>
                                    <artifact>org.scala-lang:scala-library</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>ch.qos.logback:*</artifact>
                                    <includes>**</includes>
                                </filter>
                                <filter>
                                    <artifact>ch.qos.logback.contrib:*</artifact>
                                    <includes>**</includes>
                                </filter>
                                <filter>
                                    <artifact>com.simonmarkets:lib-logging-core</artifact>
                                    <includes>**</includes>
                                </filter>
                                <filter>
                                    <artifact>com.simonmarkets.logging:*</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>commons-logging:commons-logging</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>com.typesafe.akka:akka-stream_${scala.version.short}</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>com.typesafe.akka:akka-actor_${scala.version.short}</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.coderplus.maven.plugins</groupId>
                <artifactId>copy-rename-maven-plugin</artifactId>
            </plugin>

        </plugins>
    </build>
</project>