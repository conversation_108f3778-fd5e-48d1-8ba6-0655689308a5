package com.simonmarkets.networks.common.repository

import com.simonmarkets.circe.{CirceDecoders, CirceEncoders}
import io.circe.Decoder
import io.circe.generic.extras.{AutoDerivation, Configuration}

trait NetworkMongoJsonCodecs extends CirceEncoders with CirceDecoders with AutoDerivation {
  implicit val circeConfig: Configuration = Configuration.default.withDefaults

  implicit def decodeParam: Decoder[Map[String, Any]] = Decoder.decodeJsonObject.map {
    x => x.toMap
  }
}
