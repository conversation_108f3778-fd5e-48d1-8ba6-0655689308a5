package com.simonmarkets.networks.common.domain

import io.circe.Decoder
import io.circe.generic.extras.Configuration
import io.circe.generic.extras.semiauto.deriveConfiguredDecoder

case class PassportUserCandidate(
    icnId: Int,
    email: String,
    domain: String,
    wlpName: String
)

object PassportUserCandidate {
  implicit val config: Configuration = Configuration.default.withSnakeCaseMemberNames
  implicit val decoder: Decoder[PassportUserCandidate] = deriveConfiguredDecoder[PassportUserCandidate]
}