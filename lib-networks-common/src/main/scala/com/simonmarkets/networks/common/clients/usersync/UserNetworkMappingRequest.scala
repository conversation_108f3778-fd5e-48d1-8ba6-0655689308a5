package com.simonmarkets.networks.common.clients.usersync

case class UserNetworkMappingRequest(
  sourceSystem: SourceDestinationSystem,
  destinationSystem: SourceDestinationSystem,
  sourcePrimaryId: String,
  sourcePrimaryIdKind: SourceDestinationPrimaryIdKind,
  sourceSecondaryId: Option[String],
  sourceSecondaryIdKind: Option[SourceDestinationSecondaryIdKind] = Some(SourceDestinationSecondaryIdKind.`ICN_FIRM`)
)
