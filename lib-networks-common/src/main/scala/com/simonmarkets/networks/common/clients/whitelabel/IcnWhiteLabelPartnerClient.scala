package com.simonmarkets.networks.common.clients.whitelabel

import akka.http.scaladsl.model.headers.RawHeader
import akka.stream.Materializer
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.logging.{TraceId, TraceLogging}

import scala.concurrent.{ExecutionContext, Future}

trait IcnWhiteLabelPartnerClient {

  /**
   * Get all white label partners
   *
   * @param traceId trace
   * @return set of all white label partners
   */
  def getAll(implicit traceId: TraceId): Future[Set[WhiteLabelPartnerResponse]]

  /**
   * Get all of the firms that are under a white label partner, i.e all of the distinct firms that users in a white label partner are assigned to
   * @param whiteLabelPartnerId white label partner's id
   * @return set of firms under white label partner
   */
  def getAllFirmsForWhiteLabelPartner(whiteLabelPartnerId: String)(implicit traceId: TraceId): Future[Set[FirmResponse]]

}

class HttpIcnWhiteLabelPartnerClient(client: FutureHttpClient, basePath: String, apiToken: String)
  (implicit ec: ExecutionContext,
      mat: Materializer) extends IcnWhiteLabelPartnerClient with JsonCodecs with TraceLogging {

  private implicit val tag: FutureHttpClient.Tag = FutureHttpClient.Tag("white-label-firm")

  override def getAll(implicit traceId: TraceId): Future[Set[WhiteLabelPartnerResponse]] = {
    client.get[Set[WhiteLabelPartnerResponse]](s"$basePath/v1/white_label_partners", List(RawHeader("Authorization", apiToken)))
  }


  override def getAllFirmsForWhiteLabelPartner(whiteLabelPartnerId: String)
    (implicit traceId: TraceId): Future[Set[FirmResponse]] = {
    client.get[Set[FirmResponse]](s"$basePath/v1/white_label_partners/$whiteLabelPartnerId/firms", List(RawHeader("Authorization", apiToken)))
  }
}
