package com.simonmarkets.networks.common.service.networkservice

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.capabilities.NetworksCapabilities._
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.common.api.{EncryptionClientKeyResponse, NetworkEncryptionClientKey, Page, UpdateLocationsRequest, UpsertNetworkRequestValidator}
import com.simonmarkets.networks.common.config.NetworkServiceConfig
import com.simonmarkets.networks.common.repository.NetworkRepository
import com.simonmarkets.networks.common.service.networkservice.BasicNetworkService.generateNetworkCode
import com.simonmarkets.networks.common.service.NetworkAcceptedAccessKeysGenerator
import com.simonmarkets.networks.common.{ChangelogItem, Network, NetworkNamesLocationsView, NetworkView, VisibleNetworkView}
import com.simonmarkets.resteasy.utils.FutureOps.FutureOptionOps
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.User
import simon.Id.NetworkId

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Random

class BasicNetworkService(
    networkRepository: NetworkRepository,
    transactionRetryLimit: Int = NetworkServiceConfig.defaultTransactionRetryLimit
)(implicit ec: ExecutionContext) extends TraceLogging with NetworkAcceptedAccessKeysGenerator {

  def getNetworkById(requesterAcl: UserACL, id: NetworkId)(implicit traceId: TraceId): Future[Network] = {
    log.info("Getting network by id", id)
    networkRepository.getById(id)(generateEntitlements(requesterAcl, ViewCapabilities)).flatMap {
      case None => HttpError.notFound(NetworkId unwrap id).future
      case Some(n) => n.successFuture
    }
  }

  def unentitledGetNetworkById(id: NetworkId)(implicit traceId: TraceId): Future[Network] = {
    log.info("Getting network by id without entitlements", id)
    networkRepository.getById(id)(Set(Capabilities.Admin)).flatMap {
      case None => HttpError.notFound(NetworkId unwrap id).future
      case Some(n) => n.successFuture
    }
  }

  def getNetworkByName(requesterAcl: UserACL, name: String)(implicit traceId: TraceId): Future[Network] = {
    log.trace(s"Retrieving network by name: ${name}")

    networkRepository.getByName(name)(generateEntitlements(requesterAcl, ViewCapabilities)).flatMap {
      case None => HttpError.notFound(s"Network with name: ${name} not found").future
      case Some(n) => n.successFuture
    }
  }

  def getNetworks(requesterAcl: UserACL)(implicit traceId: TraceId): Future[List[Network]] = {
    log.info("Getting all networks")
    networkRepository.getNetworks(generateEntitlements(requesterAcl, ViewCapabilities))
  }

  def getNetworksWithPurview(requesterAcl: UserACL)(implicit traceId: TraceId): Future[Seq[VisibleNetworkView]] = {
    log.info("Getting visible networks")
    val networkIdToIssuerPurview = requesterAcl.issuerPurviewIds.map(issuerPurview => issuerPurview.network -> issuerPurview).toMap
    val entitlements = generateEntitlements(requesterAcl, ViewCapabilities)
    val networkIds = requesterAcl.userPurviewIds + requesterAcl.networkId
    val networks = if (requesterAcl.capabilities.contains(Capabilities.Admin)) networkRepository.getNetworks(entitlements) else networkRepository.getByIdsWithoutEntitlements(networkIds)
    for {
      networkList <- networks
    }
    yield {
      networkList.map { network =>
        VisibleNetworkView.fromNetwork(network, networkIdToIssuerPurview)
      }
    }
  }

  def getNetworkViewById(requesterAcl: UserACL, id: NetworkId)
    (implicit traceId: TraceId): Future[NetworkView] = {
    log.info("Getting networkview by id", id)
    getNetworkById(requesterAcl, id).map(NetworkView.fromNetwork)
  }

  /**
   * Called by `sp-contracts-offerings` service for admin users
   */
  def getNetworkIds(requesterAcl: UserACL)
    (implicit traceId: TraceId): Future[Set[NetworkId]] = {
    log.info("Getting all network IDs")

    networkRepository
      .getNetworkIds(generateEntitlements(requesterAcl, ViewCapabilities))
  }

  def getNetworkNames(requesterAcl: UserACL, ids: Set[NetworkId])
    (implicit traceId: TraceId): Future[Map[NetworkId, String]] = {
    log.info("Getting network names")
    networkRepository.getNetworkNames(ids)(generateEntitlements(requesterAcl, ViewCapabilities))
  }

  def getNetworksNamesLocations(requesterAcl: UserACL)
    (implicit traceId: TraceId): Future[Seq[NetworkNamesLocationsView]] = {
    log.info("Getting network names with locations")
    networkRepository.getAllNetworkNamesLocations()(generateEntitlements(requesterAcl, ViewCapabilities))
  }

  def getNetworkByExternalId(requesterAcl: UserACL, externalId: ExternalId)
    (implicit traceId: TraceId): Future[Network] = {
    log.info("Getting network by external id", externalId.id, "for subject", externalId.subject)
    networkRepository.getByExternalId(externalId)(generateEntitlements(requesterAcl, ViewCapabilities)).flatMap {
      case None => HttpError.notFound(s"Id: ${externalId.id}, Subject: ${externalId.subject}").future
      case Some(n) => n.successFuture
    }
  }

  def getNetworkViewByExternalId(requesterAcl: UserACL, externalId: ExternalId)
    (implicit traceId: TraceId): Future[NetworkView] = {
    log.info("Getting network by external id", externalId.id, "for subject", externalId.subject)
    getNetworkByExternalId(requesterAcl, externalId).map(NetworkView.fromNetwork)
  }


  def createNetworkCode: Future[String] =
    networkRepository.existingNetworkCodes().map { existingCodes =>
      var newCode = generateNetworkCode()
      while (existingCodes.contains(newCode)) newCode = generateNetworkCode()
      newCode
    }

  def getNetworkViewPage(requesterAcl: UserACL, limit: Option[Int], from: Option[String])
    (implicit traceId: TraceId): Future[Page[NetworkView]] = {
    log.info("Getting networkview page from repository", "requester" -> requesterAcl.userId, limit, from)
    val page = networkRepository.getNetworksPage(limit.getOrElse(1000), from)(generateEntitlements(requesterAcl, ViewCapabilities))
    page.map(pg =>
      Page[NetworkView](
        total = pg.total,
        count = pg.count,
        next = pg.next,
        result = pg.result.map(network => NetworkView.fromNetwork(network))
      )
    )
  }

  def getNetworkViewPageByIds(requesterAcl: UserACL, ids: Set[String])
    (implicit traceId: TraceId): Future[Page[NetworkView]] = {
    log.info("Getting networks page from repository by ids", ids)
    val networkIds = ids.map(NetworkId.apply) // TODO check for max size?
    for {
      networks <- networkRepository.getByIds(networkIds)(generateEntitlements(requesterAcl, ViewCapabilities))
      page = Page[NetworkView](
        total = networks.size,
        count = networks.size,
        next = None,
        result = networks.map(network => NetworkView.fromNetwork(network))
      )
    } yield page
  }

  def getNetworkViewPageBySubject(requesterAcl: UserACL, subject: String)
    (implicit traceId: TraceId): Future[Page[NetworkView]] = {
    log.info("Getting networks page from repository by subject", subject)
    for {
      networks <- networkRepository.getBySubject(subject)(generateEntitlements(requesterAcl, ViewCapabilities))
      page = Page[NetworkView](
        total = networks.size,
        count = networks.size,
        next = None,
        result = networks.map(network => NetworkView.fromNetwork(network))
      )
    } yield page
  }

  def updateNetworkLocations(requesterAcl: UserACL, id: NetworkId, request: UpdateLocationsRequest)
    (implicit traceId: TraceId): Future[Option[NetworkView]] = {
    log.info("Updating locations for network of id", id)
    UpsertNetworkRequestValidator.validateLocations(request.locations) match {
      case Left(error) => Future.failed(HttpError.badRequest(error))
      case Right(_) =>
        for {
          currNetwork <- getNetworkById(requesterAcl, id)
          result <- networkRepository.updateAndSnap(
              currNetwork.copy(locations = request.locations),
              requesterAcl.userId,
              request.comment,
              transactionRetryLimit
            )(generateEntitlements(requesterAcl, EditCapabilities))
            .map(n => Some(NetworkView.fromNetwork(n)))
        } yield result
    }
  }

  def removeUserFromApprovers(requesterAcl: UserACL, user: User)(implicit traceId: TraceId): Future[Network] = {
    log.info("Removing user from network's approvers", "userId" -> user.id, "networkId" -> user.networkId)
    val network = networkRepository.getById(user.networkId)(generateEntitlements(requesterAcl: UserACL, ViewCapabilities)).flattenOption() //TODO do we need to check the entitlements here, if it is called by other service
    network.flatMap { network =>
      val updatedApprovers = network.approverSet.map { case (key, approverListList) =>
        (key, approverListList.map(approverList => approverList.filter(_ != user.id)))
      }

      networkRepository.updateAndSnap(
        network.copy(approverSet = updatedApprovers),
        requesterAcl.userId,
        None,
        transactionRetryLimit
      )(generateEntitlements(requesterAcl, EditCapabilities)
      )
    }
  }

  def getChangelogs(id: NetworkId)(implicit traceId: TraceId): Future[List[ChangelogItem]] = {
    log.info("Getting change logs for network ", id)
    networkRepository.getChangelogs(id)
  }

  def getSnapshot(snapshotId: String)(implicit traceId: TraceId): Future[Option[NetworkView]] = {
    log.info("Getting a network snapshot", snapshotId)
    networkRepository.getSnapshotEntity(snapshotId)
      .map(_.map(NetworkView.fromNetwork))
  }

  /** Retrieves the encryption client keys for a given set of network ids */
  def getEncryptionClientKeys(networkIds: Set[NetworkId], user: UserACL)
    (implicit traceId: TraceId): Future[EncryptionClientKeyResponse] = {
    for {
      _ <- Future.unit
      _ = log.info("Reading client keys for networks", networkIds)
      networks <- networkRepository.getByIds(networkIds)(generateEntitlements(user, ViewCapabilities))
      (presentKeys, missingKeys) = networks.partition(n => n.encryptionClientKey.isDefined)
      _ = log.info("Missing client keys for networks", "ids" -> missingKeys.map(network => network.id))
      list = presentKeys.map(network => NetworkEncryptionClientKey(network.id, network.encryptionClientKey.get))
      response = EncryptionClientKeyResponse(list)
    } yield response
  }

  protected def generateEntitlements(userACL: UserACL, capabilities: Set[String]): Set[String] = {
    getAvailableAccessKeysForCapabilities(capabilities, userACL)
  }

}

object BasicNetworkService {
  def generateNetworkCode(): String = {
    val charPossiblities = ('a' to 'z') ++ ('A' to 'Z') ++ ('0' to '9')
    val nrChars = 4
    // This gives us 14_776_336 possibilities on 4 characters
    // 26 + 26 + 10 = 62 different characters
    // 62 ^ 4 = 14_776_336
    (0 until nrChars).map { _ =>
      val randomIndex = Random.nextInt(charPossiblities.size)
      charPossiblities(randomIndex)
    }.mkString
  }
}
