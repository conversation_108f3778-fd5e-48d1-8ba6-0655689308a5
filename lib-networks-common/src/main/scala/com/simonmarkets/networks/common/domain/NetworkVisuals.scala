package com.simonmarkets.networks.common.domain

case class NetworkVisuals(
    networkId: String,
    visuals: Visuals
)

case class Visuals(
    accentColor: Option[String] = None,
    secondaryColor: Option[String] = None,
    defaultLogo: Option[String] = None,
    inverseLogo: Option[String] = None,
    defaultLogoPDF: Option[String] = None,
    fillColorPDF: Option[String] = None,
    colorPDF: Option[String] = None,
    backgroundPDF: Option[String] = None,
    showPoweredByMarketplacePDF: Option[Boolean] = None,
    frontLogoMarketplacePDF: Option[String] = None,
    showPoweredByPerformancePDF: Option[Boolean] = None,
    supplementLogoPerformancePDF: Option[String] = None,
    headerLogoPerformancePDF: Option[String] = None,
    hideAdvisorNamePerformancePDF: Option[Boolean] = None,
    hideNotionalDetailsPerformancePDF: Option[Boolean] = None,
    showPoweredBySpectrumPDF: Option[Boolean] = None,
    supplementLogoSpectrumPDF: Option[String] = None,
    headerLogoSpectrumPDF: Option[String] = None,
)

