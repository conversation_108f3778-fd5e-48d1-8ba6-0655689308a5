package com.simonmarkets.networks.common.service

sealed trait ExternalIdTypeError extends Exception {
  def reason: String
  def name: String
  def pretty: String = s"$reason: $name"
}

object ExternalIdTypeError {

  final case class NotFound(name: String, reason: String) extends ExternalIdTypeError

  final case class Internal(name: String, reason: String, cause: Option[Throwable]) extends ExternalIdTypeError

  object Internal {
    def apply(name: String, internalReason: String, err: Throwable): Internal =
      Internal(name, s"$internalReason. Reason: [${err.getMessage}]", Option(err.getCause))
  }

}