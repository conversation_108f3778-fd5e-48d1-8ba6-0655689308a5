package com.simonmarkets.networks.common.service

import akka.http.javadsl.model.headers.HttpCredentials
import akka.http.scaladsl.model.headers.Authorization
import akka.stream.Materializer
import com.simonmarkets.circe.{CirceNullAsEmptyDecoders, JsonCodecs}
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.service.IcnKmsService.AccessToken
import io.circe.Codec
import io.circe.generic.semiauto.deriveCodec

import scala.concurrent.{ExecutionContext, Future}

trait IcnKmsService {
  /**
   * Creates a new encryption client using the given provider (default if missing) with the given client id
   *
   * @param clientId             the id of the new client
   * @param encryptionProviderId the id of the encryption provider, if not default for the environment
   * @return the same clientId as requested
   */
  def createClientKey(clientId: String, encryptionProviderId: Option[String])(implicit tid: TraceId, token: AccessToken): Future[String]
}

object IcnKmsService {
  class IcnKmsServiceImpl(
      endpoint: String,
      environment: String,
      httpClient: FutureHttpClient
  )(implicit ec: ExecutionContext, mat: Materializer) extends IcnKmsService with IcnKmsApiCodecs {
    override def createClientKey(clientId: String, encryptionProviderId: Option[String])(implicit tid: TraceId, token: AccessToken): Future[String] = {
      val request = CreateClientRequest(environment, clientId, encryptionProviderId)
      val authTokenHeader = Authorization(HttpCredentials.createOAuth2BearerToken(token.token))
      val response = httpClient.post[CreateClientRequest, CreateClientResponse](s"$endpoint/client-keys/create", request, authTokenHeader :: Nil)
      response.map(r => r.clientId)
    }
  }

  /** Request to ICN KMS node to create a new encryption client
   *
   * @param env                   the name of the environment in which the client will be created
   * @param clientId              the id of the client being created
   * @param encryptionKeyProvider the id of the encryption provider, if default is not used
   */
  case class CreateClientRequest(env: String, clientId: String, encryptionKeyProvider: Option[String])

  /** Response from ICN KMS node for a newly created encryption client */
  case class CreateClientResponse(clientId: String)

  trait IcnKmsApiCodecs extends JsonCodecs with CirceNullAsEmptyDecoders {
    implicit lazy val createClientRequestCodec: Codec[CreateClientRequest] = deriveCodec
    implicit lazy val createClientResponseCodec: Codec[CreateClientResponse] = deriveCodec
  }

  /** Access token that is passed through the service call to inner services */
  case class AccessToken(token: String)
}