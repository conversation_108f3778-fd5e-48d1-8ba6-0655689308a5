package com.simonmarkets.networks.common.encoders

import com.goldmansachs.marquee.pipg.Network.Action
import com.goldmansachs.marquee.pipg._
import com.simonmarkets.asset.Region
import com.simonmarkets.logging.TraceId
import com.simonmarkets.mongodb.bson.BsonValueDecoder.instances._
import com.simonmarkets.mongodb.bson.ScalaBsonDocumentsOps.Ops
import com.simonmarkets.mongodb.bson.{BsonValueDecoder, NoImplicitToBsonDocumentConversion}
import com.simonmarkets.networks.common.enums.NetworkDomainEvent
import com.simonmarkets.networks.common.{DeadlineConfig, EventInfo, Network, ProspectusDeadlineConfig}
import com.simonmarkets.networks.enums.{DomicileCode, CrmProvider => CrmProviderEnum}
import com.simonmarkets.networks.{ExternalId, SmaStrategyAndUnderliers}
import com.simonmarkets.users.common.{LandingPage, LoginMode}
import com.simonmarkets.shared.{EAppProvider, MaskedId, ProductType}
import org.mongodb.scala.bson.collection.Document
import org.mongodb.scala.bson.collection.mutable.{Document => MutableDoc}
import simon.Id.ExternalNetworkId

import scala.collection.JavaConverters._

object NetworkFormat extends BsonFormat[Network] with NoImplicitToBsonDocumentConversion {

  object Fields {
    val ObjectId = "_id"
    val NetworkId = "id"
    val NetworkName = "name"
    val IdHubOrg = "idHubOrganization"
    val Purviews = "purviews"
    val To = "to"
    val Cc = "cc"
    val Approvers = "approverSet"
    val IoiApprovers = "ioiApproverSet"
    val AccountMappings = "accountMappings"
    val NetTypes = "networkTypes"
    val PurviewNetworks = "purviewNetworks"
    val SalesFeeRuleIds = "salesFeeRuleIds"
    val NetworkCapabilities = "capabilities"
    val PayoffEntitlements = "payoffEntitlements"
    val PayoffEntitlementsV2 = "payoffEntitlementsV2"
    val DynamicRoles = "dynamicRoles"
    val DistributorAlias = "distributorAlias"
    val Version = "version"
    val CustomRolesConfig = "customRolesConfig"
    val MaskedIds = "maskedIds"
    val BooksConfig = "booksCloseConfig"
    val BooksCustomConfig = "booksCloseCustomConfig"
    val BooksSendConfig = "booksSendConfig"
    val Locations = "locations"
    val Entitlements = "entitlements"
    val LearnTracksV2 = "learnTracksV2"
    val LearnContent = "learnContent"
    val EndUserShareableContent = "endUserShareableContent"
    val IdpId = "idpId"
    val SICertificationRequirements = "siCertificationRequirements"
    val AnnuityCertificationRequirements = "annuityCertificationRequirements"
    val DefinedOutcomeETFCertificationRequirements = "definedOutcomeETFCertificationRequirements"
    val AltCertificationRequirements = "altCertificationRequirements"
    val CertificationProducts = "certificationProducts"
    val OmsAlias = "omsAlias"
    val OfferingCloseBehaviour = "offeringCloseBehaviour"
    val ProspectusConfig = "prospectusDeadlineConfig"
    val DtccId = "dtccId"
    val AnnuityProvider = "annuityEAppProvider"
    val SsoPrefix = "ssoPrefix"
    val ContactInformation = "contactInfo"
    val Group = "group"
    val ExternalId = "externalId"
    val ExternalIds = "externalIds"
    val PartnerUrls = "partnerUrls"
    val NetworkCode = "networkCode"
    val CrmProviders = "crmProviders"
    val LoginModeField = "loginMode"
    val EmbeddingInfo = "embeddingInfo"
    val EventInfoField = "eventInfo"
    val SmaStrategiesAndUnderliersField = "smaStrategiesAndUnderliers"
    val SmaRestrictedIssuersField = "smaRestrictedIssuers"
    val ContactInfo2 = "contactInfo2"
    val ContactInfo2Name = "contactInfo2Name"
    val UiViewCardOverrides = "uiViewCardOverrides"
    val UiViewCardOverridesExpiryDate = "uiViewCardOverridesExpiryDate"
    val HistoricHoldingsStartFrom = "historicHoldingsStartFrom"
    val CobrandingCustomDisclosure = "cobrandingCustomDisclosure"
    val EncryptionClientKey = "encryptionClientKey"
    val SessionInactivityTimeout = "sessionInactivityTimeout"
    val LandingPageField = "landingPage"
    val DomicilesField = "domiciles"
    val ProductTypeCertificationRequirements = "productTypeCertificationRequirements"
    val WlpUrl = "wlpUrl"
  }

  override def write(network: Network): Document = {
    import Fields._

    val networkDocument: MutableDoc = MutableDoc(
      NetworkId -> simon.Id.NetworkId.unwrap(network.id),
      NetworkName -> network.name,
      IdHubOrg -> IdHubOrganizationFormat.write(network.idHubOrganization),
      Approvers -> network.approverSet.toList,
      IoiApprovers -> network.ioiApproverSet.toList,
      SalesFeeRuleIds -> network.salesFeeRuleIds,
      NetworkCapabilities -> network.capabilities.toList,
      PayoffEntitlements -> network.payoffEntitlements.mapValues(x => x.toList).toList,
      PayoffEntitlementsV2 -> network.payoffEntitlementsV2.mapValues(x => x.mapValues(x => x.map(ActionFormat.write).toList).toList).toList,
      DynamicRoles -> network.dynamicRoles.toList,
      Version -> network.version,
      CustomRolesConfig -> network.customRolesConfig.map(definition => CustomRoleDefinitionFormat.write(definition)).toList,
      MaskedIds -> network.maskedIds.map(maskedId => MaskedIdFormat.write(maskedId)).toList,
      BooksConfig -> network.booksCloseConfig.map(config => BooksCloseConfigFormat.write(config)),
      BooksCustomConfig -> network.booksCloseCustomConfig.map(config => BooksCloseConfigFormat.write(config)),
      Locations -> network.locations.map(loc => NetworkLocationFormat.write(loc)).toList,
      Entitlements -> network.entitlements.toList,
      LearnTracksV2 -> network.learnTracksV2.map(LearnTrackFormat.write).toList,
      LearnContent -> network.learnContent.toList,
      EndUserShareableContent -> network.endUserShareableContent.toList,
      SICertificationRequirements -> network.siCertificationRequirements
        .map(CertificationAlternativesForProductFormat.write).toList,
      AnnuityCertificationRequirements -> network.annuityCertificationRequirements
        .map(reqs => CertificationAlternativesForProductFormat.write(reqs)).toList,
      DefinedOutcomeETFCertificationRequirements -> network.definedOutcomeETFCertificationRequirements
        .map(reqs => CertificationAlternativesForProductFormat.write(reqs)).toList,
      ExternalIds -> network.externalIds.map(ExternalIdFormat.write).toList,
      PartnerUrls -> network.partnerUrls.map(PartnerUrlFormat.write).toList,
      CrmProviders -> network.crmProviders.map(_.productPrefix),
      NetworkCode -> network.networkCode,
      LoginModeField -> network.loginMode.productPrefix,
      EventInfoField -> EventInfoFormat.write(network.eventInfo),
      SmaStrategiesAndUnderliersField -> network.smaStrategiesAndUnderliers.map(SmaStrategyAndUnderliersFormat.write).toList,
      SmaRestrictedIssuersField -> network.smaRestrictedIssuers.toList,
      AltCertificationRequirements -> network.altCertificationRequirements
        .map(altCertificationRequirements =>
          altCertificationRequirements.map(reqs => CertificationAlternativesForProductFormat.write(reqs)).toList
        ),
      UiViewCardOverrides -> network.uiViewCardOverrides.toList,
      UiViewCardOverridesExpiryDate -> network.uiViewCardOverridesExpiryDate.toList,
      HistoricHoldingsStartFrom -> network.historicHoldingsStartFrom,
      CobrandingCustomDisclosure -> network.cobrandingCustomDisclosure,
      EncryptionClientKey -> network.encryptionClientKey,
      SessionInactivityTimeout -> network.sessionInactivityTimeout,
      ProductTypeCertificationRequirements ->
        network.productTypeCertificationRequirements
          .map { seqReq =>
            seqReq.map(ProductTypeCertificationRequirementsFormat.write)
          }
    )
    network.networkTypes.foreach(types => networkDocument.update(NetTypes, types.map(_.name)))
    network.to.foreach(to => networkDocument.update(To, to))
    network.cc.foreach(cc => networkDocument.update(Cc, cc))
    network.accountMappings.foreach(acc => networkDocument.update(AccountMappings, acc))
    network.purviews.foreach { purviews =>
      val configs = purviews.map(PurviewFormat.write)
      networkDocument.update(Purviews, configs.toList)
    }
    network.purviewNetworks.foreach { purviewNetworks =>
      val configs = purviewNetworks.map(IssuerPurviewFormat.write)
      networkDocument.update(PurviewNetworks, configs.toList)
    }
    network.distributorAlias.foreach(v => networkDocument.update(DistributorAlias, ExternalAliasFormat.write(v)))
    network.annuityEAppProvider.foreach(provider => networkDocument.update(AnnuityProvider, AnnuityEAppProviderFormat.write(provider)))
    network.ssoPrefix.foreach(sso => networkDocument.update(SsoPrefix, SSOPrefixFormat.write(sso)))
    network.contactInfo.foreach(info => networkDocument.update(ContactInformation, ContactInfoFormat.write(info)))
    network.omsAlias.foreach(oms => networkDocument.update(OmsAlias, oms))
    network.dtccId.foreach(dtcc => networkDocument.update(DtccId, dtcc))
    network.idpId.foreach(idp => networkDocument.update(IdpId, idp))
    network.prospectusDeadlineConfig.foreach { prospectusDeadlineConfigs =>
      val configs = prospectusDeadlineConfigs.map(ProspectusDeadlineConfigFormat.write)
      networkDocument.update(ProspectusConfig, configs)
    }
    network.externalId.foreach(id => networkDocument.update(Fields.ExternalId, ExternalNetworkId.unwrap(id)))
    network.group.foreach(group => networkDocument.update(Group, group))
    network.certificationProducts.foreach(ids => networkDocument.update(CertificationProducts, ids.toList))
    network.embeddingInfo.foreach(info => networkDocument.update(EmbeddingInfo, EmbeddingInfoFormat.write(info)))
    network.booksSendConfig.foreach(configs => networkDocument.update(BooksSendConfig, configs.map(config => BooksCloseConfigFormat.write(config))))
    network.contactInfo2.foreach { contactInfo =>
      val contact = contactInfo.map(ContactInfo2Format.write)
      networkDocument.update(ContactInfo2, contact)
    }
    network.contactInfo2Name.foreach(name => networkDocument.update(ContactInfo2Name, name))
    network.landingPage.foreach(lp => networkDocument.update(LandingPageField, lp.toString))
    network.domiciles.foreach(dom => networkDocument.update(DomicilesField, dom.map(_.toString).toList))
    network.wlpUrl.foreach(wlpUrl => networkDocument.update(Fields.WlpUrl, wlpUrl))
    Document(networkDocument.toBsonDocument)
  }

  import networkBsonValueDecoderInstances._

  override def read(d: Document): Network = {
    import Fields._

    Network(
      id = simon.Id.NetworkId(d.getString(NetworkId)),
      name = d.getStringOpt(NetworkName).getOrElse(d.getString(NetworkId)),
      idHubOrganization = d.getAsScala[IdHubOrganization](IdHubOrg),
      purviews = d.getSetOpt[Purview](Purviews),
      approverSet = d.getAsScalaOpt[Map[String, List[List[String]]]](Approvers).getOrElse(Map.empty),
      ioiApproverSet = d.getAsScalaOpt[Map[String, List[List[String]]]](IoiApprovers).getOrElse(Map.empty),
      accountMappings = d.getStringListOpt(AccountMappings).filter(_.nonEmpty),
      networkTypes = d.getStringListOpt(NetTypes).filter(_.nonEmpty).map(_.map(networkType => {
        //TODO: APPSERV-62935 remove conversion. Temp hack that converts SMAManager networkType to Admin networkType
        if (networkType == NetworkType.SMAManager.name) NetworkType.Admin else NetworkType(networkType)
      })),
      to = d.getStringListOpt(To).filter(_.nonEmpty),
      cc = d.getStringListOpt(Cc).filter(_.nonEmpty),
      purviewNetworks = d.get(PurviewNetworks).map(bsonVal => {
        if (!bsonVal.isNull) {
          bsonVal.asArray().asScala.map(v => IssuerPurviewFormat.read(v.asDocument())).toSet
        } else {
          Set.empty
        }
      }),
      salesFeeRuleIds = d.getStringListOpt(SalesFeeRuleIds).getOrElse(List.empty),
      capabilities = d.getAsScalaOpt[Map[String, List[String]]](NetworkCapabilities).getOrElse(Map.empty),
      payoffEntitlements = d.getAsScalaOpt[Map[String, Map[String, List[String]]]](PayoffEntitlements).getOrElse(Map.empty),
      payoffEntitlementsV2 = d.getAsScalaOpt[Map[String, Map[String, Set[Action]]]](PayoffEntitlementsV2).getOrElse(Map.empty),
      dynamicRoles = d.getStringSetOpt(DynamicRoles).getOrElse(Set.empty),
      distributorAlias = d.getAsScalaOpt[ExternalAlias](DistributorAlias),
      omsAlias = d.getStringOpt(OmsAlias),
      version = d.getInt(Version),
      customRolesConfig = d.getSetOpt[CustomRoleDefinition](CustomRolesConfig).getOrElse(Set.empty),
      maskedIds = d.getSetOpt[MaskedId](MaskedIds).getOrElse(Set.empty),
      booksCloseConfig = d.getListOpt[BooksCloseConfig](BooksConfig).getOrElse(List.empty),
      booksCloseCustomConfig = d.getListOpt[BooksCloseConfig](BooksCustomConfig).getOrElse(List.empty),
      booksSendConfig = d.getListOpt[BooksCloseConfig](BooksSendConfig),
      prospectusDeadlineConfig = d.getListOpt[ProspectusDeadlineConfig](ProspectusConfig),
      dtccId = d.getStringOpt(DtccId),
      locations = d.getSetOpt[NetworkLocation](Locations).getOrElse(Set.empty),
      entitlements = d.getStringSetOpt(Entitlements).getOrElse(Set.empty),
      annuityEAppProvider = d.getAsScalaOpt[AnnuityEAppProvider](AnnuityProvider),
      ssoPrefix = d.getAsScalaOpt[SSOPrefix](SsoPrefix),
      contactInfo = d.getAsScalaOpt[ContactInfo](ContactInformation),
      learnTracksV2 = d.getListOpt[LearnTrack](LearnTracksV2).getOrElse(List.empty),
      learnContent = d.getStringListOpt(LearnContent).getOrElse(List.empty),
      endUserShareableContent = d.getStringListOpt(EndUserShareableContent).getOrElse(List.empty),
      idpId = d.getStringOpt(IdpId),
      siCertificationRequirements =
        d.getListOpt[CertificationAlternativesForProduct](SICertificationRequirements).getOrElse(List.empty),
      annuityCertificationRequirements =
        d.getListOpt[CertificationAlternativesForProduct](AnnuityCertificationRequirements).getOrElse(List.empty),
      definedOutcomeETFCertificationRequirements =
        d.getListOpt[CertificationAlternativesForProduct](DefinedOutcomeETFCertificationRequirements).getOrElse(List.empty),
      certificationProducts = d.getStringListOpt(CertificationProducts).filter(_.nonEmpty),
      group = d.getStringOpt(Group),
      externalId = d.getStringOpt(Fields.ExternalId).map(ExternalNetworkId.apply),
      externalIds = d.getSetOpt[ExternalId](Fields.ExternalIds).getOrElse(Set.empty),
      partnerUrls = d.getSetOpt[PartnerUrl](PartnerUrls).getOrElse(Set.empty),
      networkCode = d.getString(NetworkCode),
      crmProviders = d.getStringListOpt(CrmProviders).filterNot(_.isEmpty).map(_.map(CrmProviderEnum(_))).getOrElse(List.empty),
      embeddingInfo = d.getAsScalaOpt[EmbeddingInfo](EmbeddingInfo),
      loginMode = d.getStringOpt(LoginModeField).map(LoginMode(_)).getOrElse(LoginMode.EnumNotFound),
      eventInfo = d.getAsScalaOpt[EventInfo](EventInfoField).getOrElse(EventInfo.Default),
      smaStrategiesAndUnderliers = d.getSetOpt[SmaStrategyAndUnderliers](SmaStrategiesAndUnderliersField).getOrElse(Set.empty),
      smaRestrictedIssuers = d.getStringSetOpt(SmaRestrictedIssuersField).getOrElse(Set.empty),
      contactInfo2 = d.getListOpt[ContactInfo2](ContactInfo2),
      contactInfo2Name = d.getStringOpt(ContactInfo2Name),
      altCertificationRequirements = d.getListOpt[CertificationAlternativesForProduct](AltCertificationRequirements),
      uiViewCardOverrides = d.getAsScalaOpt[Map[String, List[String]]](UiViewCardOverrides).getOrElse(Map.empty),
      uiViewCardOverridesExpiryDate = d.getAsScalaOpt[Map[String, String]](UiViewCardOverridesExpiryDate).getOrElse(Map.empty),
      historicHoldingsStartFrom = d.getStringOpt(HistoricHoldingsStartFrom),
      cobrandingCustomDisclosure = d.getStringOpt(CobrandingCustomDisclosure),
      encryptionClientKey = d.getStringOpt(EncryptionClientKey),
      sessionInactivityTimeout = d.getIntOpt(SessionInactivityTimeout),
      landingPage = d.getStringOpt(LandingPageField).map(LandingPage(_)),
      domiciles = d.getSetOpt[String](DomicilesField).map(_.map(DomicileCode.apply)),
      productTypeCertificationRequirements =
        d.getListOpt[ProductCertificationRequirements](ProductTypeCertificationRequirements),
      wlpUrl = d.getStringOpt(WlpUrl)
    )
  }

  private object IdHubOrganizationFormat extends BsonFormat[IdHubOrganization] {

    private object Fields {
      val Id = "id"
      val Name = "name"
    }

    override def write(v: IdHubOrganization): Document = {
      import Fields._
      Document(Id -> v.id, Name -> v.name)
    }

    override def read(d: Document): IdHubOrganization = {
      import Fields._
      IdHubOrganization(id = d.getInt(Id), name = d.getString(Name))
    }
  }


  object EventInfoFormat extends BsonFormat[EventInfo] {

    object Fields {
      val CorrelationId = "correlationId"
      val EventTypeField = "eventType"
      val Duplicated = "duplicated"
      val TriggeredBy = "triggeredBy"
    }

    override def write(
        eventInfo: EventInfo): Document = {
      import Fields._

      val eventInfoDocument: MutableDoc = MutableDoc(
        CorrelationId -> eventInfo.correlationId.toString,
        TriggeredBy -> eventInfo.triggeredBy,
        Duplicated -> eventInfo.duplicated,
        EventTypeField -> eventInfo.eventType.productPrefix
      )
      Document(eventInfoDocument.toBsonDocument)
    }

    override def read(
        d: Document): EventInfo = {

      import Fields._

      EventInfo(
        duplicated = d.getBoolean(Duplicated),
        triggeredBy = d.getString(TriggeredBy),
        correlationId = TraceId(d.getString(CorrelationId)),
        eventType = NetworkDomainEvent(d.getString(EventTypeField))
      )
    }

  }

  private object ActionFormat extends BsonFormat[Action] {

    override def write(v: Action): Document = {
      Document("action" -> v.action,
        "contractParams" -> v.contractParams.mapValues(s => s.toList).toList
      )
    }

    override def read(d: Document): Action = {
      Action(
        action = d.getString("action"),
        contractParams = d.getAsScalaOpt[Map[String, Set[String]]]("contractParams").getOrElse(Map.empty)
      )
    }
  }

  object CustomRoleDefinitionFormat extends BsonFormat[CustomRoleDefinition] {
    override def write(v: CustomRoleDefinition): Document = Document(
      "role" -> v.role,
      "capabilities" -> v.capabilities.toList
    )

    override def read(d: Document): CustomRoleDefinition = {
      CustomRoleDefinition(
        d.getString("role"),
        d.getStringSetOpt("capabilities").getOrElse(Set.empty)
      )
    }
  }

  object MaskedIdFormat extends BsonFormat[MaskedId] {

    object Fields {
      val Id = "id"
      val Target = "target"
    }

    override def write(v: MaskedId): Document = {
      import Fields._
      Document(Target -> v.target, Id -> v.id)
    }

    override def read(d: Document): MaskedId = {
      import Fields._
      MaskedId(target = d.getString(Target), id = d.getString(Id))
    }
  }

  private object BooksCloseConfigFormat extends BsonFormat[BooksCloseConfig] {
    override def write(v: BooksCloseConfig): Document = Document(
      "region" -> v.region.toString,
      "tradeDateOffset" -> v.tradeDateOffset,
      "hourOfDay" -> v.hourOfDay,
      "minuteOfHour" -> v.minuteOfHour
    )

    override def read(d: Document): BooksCloseConfig = BooksCloseConfig(
      region = Region(d.getString("region")),
      tradeDateOffset = d.getInt("tradeDateOffset"),
      hourOfDay = d.getInt("hourOfDay"),
      minuteOfHour = d.getInt("minuteOfHour")
    )
  }

  private object IssuerPurviewFormat extends BsonFormat[IssuerPurview] {

    private object Fields {
      val Net = "network"
      val Issuers = "issuers"
      val Wholesaler = "wholesaler"
      val PurviewedDomains = "purviewedDomains"
    }

    override def write(v: IssuerPurview): Document = {
      import Fields._
      Document(Net -> simon.Id.NetworkId.unwrap(v.network),
        Issuers -> v.issuers.toList,
        Wholesaler -> v.wholesaler.map(i => simon.Id.NetworkId.unwrap(i)),
        PurviewedDomains -> v.purviewedDomainsUpdated.map(_.map(_.productPrefix).toList)
      )
    }

    override def read(d: Document): IssuerPurview = {
      import Fields._
      IssuerPurview(
        network = simon.Id.NetworkId(d.getString(Net)),
        issuers = d.getStringSetOpt(Issuers).getOrElse(Set.empty),
        wholesaler = d.getStringOpt(Wholesaler).map(simon.Id.NetworkId(_)),
        purviewedDomainsUpdated = d.getStringSetOpt(PurviewedDomains).map(_.map(PurviewedDomain(_)))
      )
    }
  }

  private object PurviewFormat extends BsonFormat[Purview] {

    private object Fields {
      val NetworkId = "networkId"
      val PurviewEntities = "purviewEntities"
    }

    override def write(v: Purview): Document = {
      import Fields._
      Document(
        NetworkId -> v.networkId,
        PurviewEntities -> v.purviewEntities.map(PurviewEntityFormat.write).toList
      )
    }

    override def read(d: Document): Purview = {
      import Fields._
      Purview(
        networkId = d.getString(NetworkId),
        purviewEntities = d.getAsScala[Set[PurviewEntity]](PurviewEntities)
      )
    }
  }

  private object PurviewEntityFormat extends BsonFormat[PurviewEntity] {

    private object Fields {
      val Key = "key"
      val PurviewType = "purviewType"
      val Domains = "domains"
    }

    override def write(v: PurviewEntity): Document = {
      import Fields._
      Document(
        Key -> v.key,
        PurviewType -> v.purviewType.map(_.productPrefix),
        Domains -> v.domains.map(_.map(_.productPrefix).toList)
      )
    }

    override def read(d: Document): PurviewEntity = {
      import Fields._
      PurviewEntity(
        key = d.getString(Key),
        purviewType = d.getStringOpt(PurviewType).map(NetworkCategory(_)),
        domains =  d.getStringSetOpt(Domains).map(_.map(PurviewedDomain(_)))
      )
    }
  }

  private object DeadlineConfigFormat extends BsonFormat[DeadlineConfig] {

    private object Fields {
      val DayOffset = "dayOffset"
      val HourOffset = "hourOffset"
      val MinuteOffset = "minuteOffset"
    }

    override def write(v: DeadlineConfig): Document = {
      import Fields._
      Document(DayOffset -> v.dayOffset, HourOffset -> v.hourOffset, MinuteOffset -> v.minuteOffset)
    }

    override def read(d: Document): DeadlineConfig = {
      import Fields._
      DeadlineConfig(
        dayOffset = d.getInt(DayOffset),
        hourOffset = d.getInt(HourOffset),
        minuteOffset = d.getInt(MinuteOffset)
      )
    }
  }

  private object ProspectusDeadlineConfigFormat extends BsonFormat[ProspectusDeadlineConfig] {

    override def write(v: ProspectusDeadlineConfig): Document = Document(
      "tier" -> v.tier,
      "deadlineConfig" -> DeadlineConfigFormat.write(v.deadlineConfig)
    )

    override def read(d: Document): ProspectusDeadlineConfig = ProspectusDeadlineConfig(
      tier = d.getInt("tier"),
      deadlineConfig = d.getAsScala[DeadlineConfig]("deadlineConfig")
    )
  }

  private object ExternalAliasFormat extends BsonFormat[ExternalAlias] {

    override def read(x: Document): ExternalAlias = ExternalAlias(x.getString("name"), x.getString("pattern"))

    override def write(x: ExternalAlias): Document = Document("name" -> x.name, "pattern" -> x.pattern)
  }

  private object AnnuityEAppProviderFormat extends BsonFormat[AnnuityEAppProvider] {

    override def write(x: AnnuityEAppProvider): Document =
      Document(
        "providerName" -> x.providerName.productPrefix,
        "params" -> x.params.mapValues(v => v).toList
      )

    override def read(x: Document): AnnuityEAppProvider =
      AnnuityEAppProvider(
        EAppProvider(x.getString("providerName")),
        x.getMapOpt[String]("params").getOrElse(Map.empty)
      )
  }

  object NetworkLocationFormat extends BsonFormat[NetworkLocation] {
    override def write(v: NetworkLocation): Document = {
      val doc = MutableDoc(
        "name" -> v.name,
        "children" -> v.children.toList
      )
      v.parent.foreach(parent => doc.update("parent", parent))
      Document(doc.toBsonDocument)
    }

    override def read(d: Document): NetworkLocation = {
      NetworkLocation(
        name = d.getString("name"),
        parent = d.getStringOpt("parent"),
        children = d.getStringSetOpt("children").getOrElse(Set.empty)
      )
    }
  }

  private object SSOPrefixFormat extends BsonFormat[SSOPrefix] {
    override def read(x: Document): SSOPrefix = {
      SSOPrefix(
        baseUrl = x.getString("baseUrl"),
        redirectionKey = x.getString("redirectionKey"),
        simonBase = x.getStringOpt("simonBase"),
        isRedirectionKeyEncoded = x.getBooleanOpt("isRedirectionKeyEncoded"),
        ssoSystemName = x.getStringOpt("ssoSystemName")
      )
    }

    override def write(x: SSOPrefix): Document = {
      val doc = MutableDoc("baseUrl" -> x.baseUrl, "redirectionKey" -> x.redirectionKey)
      x.simonBase.foreach(base => doc.update("simonBase", base))
      x.isRedirectionKeyEncoded.foreach(value => doc.update("isRedirectionKeyEncoded", value))
      x.ssoSystemName.foreach(name => doc.update("ssoSystemName", name))
      Document(doc.toBsonDocument)
    }
  }

  private object ContactInfoFormat extends BsonFormat[ContactInfo] {

    override def write(v: ContactInfo): Document = {
      Document("email" -> v.email, "phone" -> v.phone, "distributionLists" -> v.distributionLists.map(DistributionListFormat.write))
    }

    override def read(v: Document): ContactInfo = ContactInfo(
      email = v.getStringOpt("email"),
      phone = v.getStringOpt("phone"),
      distributionLists = v.getAsScalaOpt[DistributionList]("distributionLists")
    )
  }

  private object ContactInfo2Format extends BsonFormat[ContactInfo2] {
    override def write(v: ContactInfo2): Document = {
      val doc = MutableDoc(
        "name" -> v.name,
        "url" -> v.url,
        "urlDisplayText" -> v.urlDisplayText,
        "email" -> v.email,
        "phone" -> v.phone
      )

      v.contactType.foreach(ct => doc.update("contactType", ct.productPrefix))

      Document(doc.toBsonDocument)
    }

    override def read(v: Document): ContactInfo2 = ContactInfo2(
      name = v.getStringOpt("name"),
      url = v.getStringOpt(("url")),
      urlDisplayText = v.getStringOpt("urlDisplayText"),
      email = v.getStringOpt("email"),
      phone = v.getStringOpt("phone"),
      contactType = v.getStringOpt("contactType").map(ContactType(_))
    )
  }

  private object EmbeddingInfoFormat extends BsonFormat[EmbeddingInfo] {
    override def write(v: EmbeddingInfo): Document = {
      val document = MutableDoc(
        "hostApplicationUrl" -> v.hostApplicationUrl
      )
      v.hostApplicationName.foreach {
        app =>
          document.update("hostApplicationName", app)
      }
      Document(document.toBsonDocument)
    }

    override def read(d: Document): EmbeddingInfo = {
      EmbeddingInfo(
        hostApplicationName = d.getStringOpt("hostApplicationName"),
        hostApplicationUrl = d.getString("hostApplicationUrl")
      )
    }
  }

  private object LearnTrackFormat extends BsonFormat[LearnTrack] {
    override def write(v: LearnTrack): Document =
      Document("trackId" -> v.trackId, "isActive" -> v.isActive)

    override def read(d: Document): LearnTrack =
      LearnTrack(d.getString("trackId"), d.getBoolean("isActive"))
  }

  private object CertificationAlternativesForProductFormat extends BsonFormat[CertificationAlternativesForProduct] {
    override def write(v: CertificationAlternativesForProduct): Document = {
      val doc = MutableDoc(
        "productId" -> v.productId,
        "productName" -> v.productName,
        "certificationAlternatives" -> v.certificationAlternatives.map(alt => alt.toList).toList
      )

      v.compensationType.foreach(ct => doc.update("compensationType", ct.productPrefix))
      v.underliers.foreach(underliers => doc.update("underliers", underliers.toList))

      Document(doc.toBsonDocument)
    }

    override def read(d: Document): CertificationAlternativesForProduct = {
      CertificationAlternativesForProduct(
        productId = d.getString("productId"),
        productName = d.getString("productName"),
        certificationAlternatives = d.getAsScalaOpt[List[List[String]]]("certificationAlternatives").getOrElse(List.empty),
        compensationType = d.getStringOpt("compensationType").map(CompensationType(_)),
        underliers = d.getAsScalaOpt[List[String]]("underliers")
      )
    }
  }

  object ExternalIdFormat extends BsonFormat[ExternalId] {

    val Id = "id"
    val Subject = "subject"

    override def write(v: ExternalId): Document = {
      Document(Id -> v.id, Subject -> v.subject)
    }

    override def read(d: Document): ExternalId = {
      ExternalId(id = d.getString(Id), subject = d.getString(Subject))
    }
  }

  object PartnerUrlFormat extends BsonFormat[PartnerUrl] {

    val PartnerName = "partnerName"
    val Urls = "urls"

    override def write(v: PartnerUrl): Document = {
      Document(PartnerName -> v.partnerName, Urls -> v.urls.map(URLFormat.write).toList)
    }

    override def read(d: Document): PartnerUrl = {
      PartnerUrl(partnerName = d.getString(PartnerName), urls = d.getAsScala[Set[URL]](Urls))
    }
  }

  object URLFormat extends BsonFormat[URL] {
    val UrlType = "urlType"
    val Url = "url"

    override def write(v: URL): Document = {
      Document(UrlType -> v.urlType, Url -> v.url)
    }

    override def read(d: Document): URL = {
      URL(urlType = d.getString(UrlType), url = d.getString(Url))
    }
  }

  object DistributionListFormat extends BsonFormat[DistributionList] {
    val etfs = "etfs"
    val etfSponsor = "etfSponsor"
    val etfHomeOffice = "etfHomeOffice"

    override def write(v: DistributionList): Document = {
      Document(etfs -> v.etfs, etfSponsor -> v.etfSponsor, etfHomeOffice -> v.etfHomeOffice)
    }

    override def read(d: Document): DistributionList = {
      DistributionList(etfs = d.getStringListOpt(etfs).getOrElse(Nil), etfSponsor = d.getStringListOpt(etfSponsor).getOrElse(Nil),
        etfHomeOffice = d.getStringListOpt(etfHomeOffice).getOrElse(Nil))
    }
  }

  object SmaStrategyAndUnderliersFormat extends BsonFormat[SmaStrategyAndUnderliers] {
    override def write(v: SmaStrategyAndUnderliers): Document = {
      Document("strategyId" -> v.strategyId, "underliers" -> v.underliers.toList)
    }

    override def read(d: Document): SmaStrategyAndUnderliers = {
      SmaStrategyAndUnderliers(
        d.getString("strategyId"),
        d.getStringSetOpt("underliers").getOrElse(Set.empty)
      )
    }
  }

  private object ProductTypeCertificationRequirementsFormat extends BsonFormat[ProductCertificationRequirements] {
    override def write(v: ProductCertificationRequirements): Document = {
      val doc = MutableDoc(
        "productType" -> v.productType.toString,
        "certificationRequirements" -> v.certificationRequirements.map { req =>
          CertificationAlternativesForProductFormat.write(req)
        }.toList,
      )

      v.certificationProducts.foreach { certificationProducts =>
        doc.update("certificationProducts", certificationProducts.toList)
      }
      v.attributeBasedCertificationRequirements.foreach { attributeBasedCertificationRequirements =>
        val abcrs = attributeBasedCertificationRequirements.map(AttributeBasedCertificationRequirementsFormat.write).toList
        doc.update("attributeBasedCertificationRequirements", abcrs)
      }
      Document(doc.toBsonDocument)
    }

    override def read(d: Document): ProductCertificationRequirements = {

      ProductCertificationRequirements(
        productType = d.getAsScala[ProductType]("productType"),
        certificationRequirements =
          d.getAsScalaOpt[List[CertificationAlternativesForProduct]]("certificationRequirements")
            .getOrElse(List.empty),
        certificationProducts = d.getStringListOpt("certificationProducts"),
        attributeBasedCertificationRequirements = d.getListOpt[AttributeBasedCertificationRequirement]("attributeBasedCertificationRequirements")
      )
    }
  }

  private object AttributeBasedCertificationRequirementsFormat extends BsonFormat[AttributeBasedCertificationRequirement] {
    override def write(v: AttributeBasedCertificationRequirement): Document = {

      val doc = MutableDoc(
        "attributeType" -> v.attributeType.toString,
        "certificationAlternatives" -> v.certificationAlternatives.map(alt => alt.toList).toList
      )

      v.attributeValue.foreach(attributeValue => doc.update("attributeValue", attributeValue))
      Document(doc.toBsonDocument)
    }

    override def read(d: Document): AttributeBasedCertificationRequirement = {
      AttributeBasedCertificationRequirement(
        attributeValue = d.getStringOpt("attributeValue"),
        attributeType = ProductAttribute(d.getString("attributeType")),
        certificationAlternatives = d.getAsScalaOpt[List[List[String]]]("certificationAlternatives").getOrElse(List.empty),
      )
    }
  }

  private object networkBsonValueDecoderInstances {
    implicit val actionBsonValueDecoder: BsonValueDecoder[Action] =
      fromFormat(ActionFormat)

    implicit val idHubOrganizationBsonValueDecoder: BsonValueDecoder[IdHubOrganization] =
      fromFormat(IdHubOrganizationFormat)

    implicit val customRoleDefinitionBsonValueDecoder: BsonValueDecoder[CustomRoleDefinition] =
      fromFormat(CustomRoleDefinitionFormat)

    implicit val maskedIdBsonValueDecoder: BsonValueDecoder[MaskedId] =
      fromFormat(MaskedIdFormat)

    implicit val booksCloseConfigBsonValueDecoder: BsonValueDecoder[BooksCloseConfig] =
      fromFormat(BooksCloseConfigFormat)

    implicit val issuerPurviewBsonValueDecoder: BsonValueDecoder[IssuerPurview] =
      fromFormat(IssuerPurviewFormat)

    implicit val purviewEntityBsonValueDecoder: BsonValueDecoder[PurviewEntity] =
      fromFormat(PurviewEntityFormat)

    implicit val purviewBsonValueDecoder: BsonValueDecoder[Purview] =
      fromFormat(PurviewFormat)

    implicit val deadlineConfigBsonValueDecoder: BsonValueDecoder[DeadlineConfig] =
      fromFormat(DeadlineConfigFormat)

    implicit val prospectusDeadlineConfigBsonValueDecoder: BsonValueDecoder[ProspectusDeadlineConfig] =
      fromFormat(ProspectusDeadlineConfigFormat)

    implicit val externalAliasBsonValueDecoder: BsonValueDecoder[ExternalAlias] =
      fromFormat(ExternalAliasFormat)

    implicit val annuityEAppProviderBsonValueDecoder: BsonValueDecoder[AnnuityEAppProvider] =
      fromFormat(AnnuityEAppProviderFormat)

    implicit val networkLocationBsonValueDecoder: BsonValueDecoder[NetworkLocation] =
      fromFormat(NetworkLocationFormat)

    implicit val ssoPrefixBsonValueDecoder: BsonValueDecoder[SSOPrefix] =
      fromFormat(SSOPrefixFormat)

    implicit val contactInfoBsonValueDecoder: BsonValueDecoder[ContactInfo] =
      fromFormat(ContactInfoFormat)

    implicit val learnTrackBsonValueDecoder: BsonValueDecoder[LearnTrack] =
      fromFormat(LearnTrackFormat)

    implicit val certificationAlternativesForProductBsonValueDecoder: BsonValueDecoder[CertificationAlternativesForProduct] =
      fromFormat(CertificationAlternativesForProductFormat)

    implicit val externalIdV2BsonValueDecoder: BsonValueDecoder[ExternalId] =
      fromFormat(ExternalIdFormat)

    implicit val partnerUrlValueDecoder: BsonValueDecoder[PartnerUrl] =
      fromFormat(PartnerUrlFormat)

    implicit val urlValueDecoder: BsonValueDecoder[URL] =
      fromFormat(URLFormat)

    implicit val distributionListValueDecoder: BsonValueDecoder[DistributionList] =
      fromFormat(DistributionListFormat)

    implicit val embeddingInfoValueDecoder: BsonValueDecoder[EmbeddingInfo] = fromFormat(EmbeddingInfoFormat)

    implicit val eventInfoValueDecoder: BsonValueDecoder[EventInfo] = fromFormat(EventInfoFormat)

    implicit val smaStrategyAndUnderliersDecoder: BsonValueDecoder[SmaStrategyAndUnderliers] = fromFormat(SmaStrategyAndUnderliersFormat)

    implicit val contactInfo2BsonValueDecoder: BsonValueDecoder[ContactInfo2] =
      fromFormat(ContactInfo2Format)

    implicit val productTypeCertificationRequirementsBsonValueDecoder: BsonValueDecoder[ProductCertificationRequirements] =
      fromFormat(ProductTypeCertificationRequirementsFormat)

    implicit val attributeBasedCertificationRequirementBsonValueDecoder: BsonValueDecoder[AttributeBasedCertificationRequirement] =
      fromFormat(AttributeBasedCertificationRequirementsFormat)

  }
}
