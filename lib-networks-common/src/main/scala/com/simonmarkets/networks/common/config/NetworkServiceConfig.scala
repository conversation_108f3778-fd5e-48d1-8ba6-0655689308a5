package com.simonmarkets.networks.common.config

import com.goldmansachs.marquee.pipg.CustomRoleDefinition
import com.simonmarkets.networks.common.config.NetworkServiceConfig.defaultTransactionRetryLimit

case class NetworkServiceConfig(
    externalTargets: Set[String],
    paging: PagingConfig,
    activation: ActivationConfig,
    omsAlias: String,
    defaultCustomRolesConfig: Set[CustomRoleDefinition],
    transactionRetryLimit: Int = defaultTransactionRetryLimit,
    encryptionClientPrefix: String,
    adminNetworkTypeConfig: AdminNetworkTypeConfig,
    defaultDomiciles: Set[String]
)
object NetworkServiceConfig {
  val defaultTransactionRetryLimit = 3
}

case class PagingConfig(
    defaultLimit: Int
)

case class ActivationConfig(
    skipEmails: Set[String]
)

case class AdminNetworkTypeConfig(
    whitelist:  Set[String]
)
