package com.simonmarkets.networks.common.service

import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.ExternalIdType
import com.simonmarkets.networks.common.repository.ExternalIdTypeRepository
import com.simonmarkets.networks.common.service.ExternalIdTypeError._
import simon.Id.NetworkId

import scala.concurrent.{ExecutionContext, Future}

trait ExternalIdTypeService {

  def get(name: String)(implicit traceId: TraceId): Future[ExternalIdType]

  def list(implicit traceId: TraceId): Future[Seq[ExternalIdType]]

  def upsert(request: ExternalIdType)(implicit traceId: TraceId): Future[ExternalIdType]

  def delete(name: String)(implicit traceId: TraceId): Future[Unit]

  def getByNetwork(networkId: NetworkId)(implicit traceId: TraceId): Future[Seq[ExternalIdType]]

}

object ExternalIdTypeService {

  class V1(repository: ExternalIdTypeRepository)
    (implicit ec: ExecutionContext) extends ExternalIdTypeService with TraceLogging {

    def get(name: String)(implicit traceId: TraceId): Future[ExternalIdType] = {
      log.info(s"Get external id type name=$name traceId=$traceId")
      repository
        .get(name)
        .flatMap {
          case Some(value) => Future.successful(value)
          case None => Future.failed(NotFound(name, "External id type not found"))
        }
    }

    def list(implicit traceId: TraceId): Future[Seq[ExternalIdType]] = {
      log.info("List all external id types")
      repository.list
    }

    def upsert(request: ExternalIdType)
      (implicit traceId: TraceId): Future[ExternalIdType] = {
      log.info(s"Upsert external id type", request.name)
      repository.upsert(request)
    }

    def delete(name: String)(implicit traceId: TraceId): Future[Unit] = {
      log.info(s"Delete external id type", name)
      repository.delete(name)
    }

    def getByNetwork(networkId: NetworkId)(implicit traceId: TraceId): Future[Seq[ExternalIdType]] = {
      log.debug("Get by network", networkId)
      repository.getByNetwork(networkId)
    }

  }

}