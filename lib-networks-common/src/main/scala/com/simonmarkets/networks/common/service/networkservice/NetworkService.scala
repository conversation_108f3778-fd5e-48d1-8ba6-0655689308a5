package com.simonmarkets.networks.common.service.networkservice

import com.goldmansachs.marquee.pipg._
import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.capabilities.NetworksCapabilities._
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.common.api.{EncryptionClientKeyResponse, EnsureEncryptionRequest, NetworkEncryptionClientKey, UpsertNetworkRequest}
import com.simonmarkets.networks.common.config.NetworkServiceConfig
import com.simonmarkets.networks.common.enums.NetworkDomainEvent
import com.simonmarkets.networks.common.repository.NetworkRepository
import com.simonmarkets.networks.common.service.IcnKmsService
import com.simonmarkets.networks.common.service.IcnKmsService.AccessToken
import com.simonmarkets.networks.common.service.networkservice.NetworkService.FutureListOps
import com.simonmarkets.networks.common.{EventInfo, Network, NetworkView}
import com.simonmarkets.networks.enums.DomicileCode
import com.simonmarkets.okta.service.{OktaGroupRules, OktaService}
import com.simonmarkets.resteasy.utils.FutureOps.FutureOptionOps
import com.simonmarkets.users.common.Utils._
import simon.Id.NetworkId

import java.util.UUID

import scala.concurrent.{ExecutionContext, Future}

class NetworkService(
    networkRepository: NetworkRepository,
    kmsService: IcnKmsService,
    oktaService: OktaService,
    config: NetworkServiceConfig
)(implicit val ec: ExecutionContext) extends BasicNetworkService(
  networkRepository,
  transactionRetryLimit = config.transactionRetryLimit
) with TraceLogging {

  def handleUpdateRequest(requesterAcl: UserACL, networkId: NetworkId, request: UpsertNetworkRequest)
    (implicit traceId: TraceId): Future[NetworkView] = {
    log.info("Handling Network Update Request", request.networkName)
    val updateEvent = EventInfo(NetworkDomainEvent.NetworkUpdated, triggeredBy = requesterAcl.userId, correlationId = traceId)
    val firstUpdateRequest = convertAdminNetworkType(request) //TODO:APPSERV-62935 remove

    for {
      updatedRequest <- validateAndUpdatePurviews(firstUpdateRequest)
      oktaId <- updateOktaGroup(updatedRequest, networkId.toString)
      entitlements = generateEntitlements(requesterAcl, EditCapabilities)
      currNetwork <- networkRepository.getById(networkId)(entitlements).flattenOption(s"Network not found id=$networkId")
      _ <- validateNetworkType(requesterAcl, updatedRequest)
      network <- updatedRequest.copy(id = Some(NetworkId.unwrap(networkId)),
        domiciles = validateDomicileCodes(updatedRequest.domiciles)
        ).asNetwork(config.defaultCustomRolesConfig, currNetwork.networkCode, updateEvent)
      updatedNetwork <- networkRepository.updateAndSnap(
        network.withEntitlements(getAcceptedAccessKeys(network)).withIdpId(oktaId).withDynamicRoles,
        requesterAcl.userId,
        updatedRequest.comment,
        config.transactionRetryLimit
      )(entitlements)
    } yield NetworkView.fromNetwork(updatedNetwork)
  }

  def handleInsertRequest(requesterAcl: UserACL, request: UpsertNetworkRequest)
    (implicit traceId: TraceId): Future[NetworkView] = {
    log.info("Handling Network Insert Request", request.networkName)
    val firstUpdateRequest = convertAdminNetworkType(request) //TODO:APPSERV-62935 remove
    val insertEvent = EventInfo(NetworkDomainEvent.NetworkCreated, triggeredBy = requesterAcl.userId, correlationId = traceId)

    for {
      updatedRequest <- validateAndUpdatePurviews(firstUpdateRequest)
      reqWithDefaults = updatedRequest.copy(
        booksCloseConfig = if (updatedRequest.booksCloseConfig.isEmpty) BooksCloseConfig.defaults else updatedRequest.booksCloseConfig,
        booksCloseCustomConfig = if (updatedRequest.booksCloseCustomConfig.isEmpty) BooksCloseConfig.defaults else updatedRequest.booksCloseCustomConfig,
        externalIds = addGeneratedIds(updatedRequest.externalIds),
        domiciles = validateDomicileCodes(updatedRequest.domiciles)
      )
      _ <- validateNetworkType(requesterAcl, updatedRequest)
      networkCode <- createNetworkCode
      network <- reqWithDefaults.asNetwork(config.defaultCustomRolesConfig, networkCode, insertEvent)

      _ <- networkRepository.exists(network.id, network.name)
        .flatMap(exists =>
          if (exists) Future.failed(HttpError.badRequest("Network of same ID or Name already exists"))
          else Future.unit
        )

      oktaId <- createOktaGroup(reqWithDefaults, network.id.toString)

      insertedNetwork <- networkRepository.insertAndSnap(
        network.withEntitlements(getAcceptedAccessKeys(network)).withIdpId(oktaId).withDynamicRoles,
        requesterAcl.userId,
        reqWithDefaults.comment,
        config.transactionRetryLimit
      )(generateEntitlements(requesterAcl, EditCapabilities))

    } yield NetworkView.fromNetwork(insertedNetwork)
  }

  def validateAndUpdatePurviews(updatedRequest: UpsertNetworkRequest): Future[UpsertNetworkRequest] = {
    if (updatedRequest.purviewNetworks.nonEmpty) {
      Future.successful(updatePurviews(updatedRequest))
    } else if (updatedRequest.purviews.nonEmpty && updatedRequest.purviewNetworks.isEmpty) {
      Future.successful(updatePurviewNetworks(updatedRequest))
    } else {
      Future.successful(updatedRequest)
    }
  }

  def updatePurviewNetworks(request: UpsertNetworkRequest): UpsertNetworkRequest = {
    request.purviews match {
      case Some(pvs) =>
        val updatedPurviewNetworks = pvs.map(purview => mapPurviewToIssuerPurview(purview))
        request.copy(purviewNetworks = Some(updatedPurviewNetworks))
      case None => request
    }
  }

  def updatePurviews(request: UpsertNetworkRequest): UpsertNetworkRequest = {
    request.purviewNetworks match {
      case Some(pns) =>
        val updatedPurviews = pns.map(purviewNetwork => mapIssuerPurviewToPurview(purviewNetwork))
        request.copy(purviews = Some(updatedPurviews))
      case None => request
    }
  }

  def mapIssuerPurviewToPurview(issuerPurview: IssuerPurview): Purview = {
    val purviewEntities = issuerPurview.issuers.map { issuer =>
      PurviewEntity(
        key = issuer,
        purviewType = Some(NetworkCategory.Issuer),
        domains = issuerPurview.purviewedDomainsUpdated
      )
    } ++ issuerPurview.wholesaler.map { wholesaler =>
      PurviewEntity(
        key = wholesaler.toString,
        purviewType = Some(NetworkCategory.Wholesaler),
        domains = issuerPurview.purviewedDomainsUpdated
      )
    }

    Purview(
      networkId = issuerPurview.network.toString,
      purviewEntities = purviewEntities
    )
  }

  def mapPurviewToIssuerPurview(purview: Purview): IssuerPurview = {
    val networkId = purview.networkId
    val issuers = purview.purviewEntities.collect {
      case entity if entity.purviewType.contains(NetworkCategory.Issuer) => entity.key
    }
    val wholesaler = purview.purviewEntities.collectFirst {
      case entity if entity.purviewType.contains(NetworkCategory.Wholesaler) => NetworkId(entity.key)
    }
    val purviewedDomainsUpdated = if (purview.purviewEntities.exists(_.domains.isDefined)) {
      Some(purview.purviewEntities.flatMap(_.domains.get))
    } else {
      None
    }
    IssuerPurview(
      network = NetworkId(networkId),
      issuers = issuers,
      wholesaler = wholesaler,
      purviewedDomainsUpdated = purviewedDomainsUpdated
    )
  }

  private def validateDomicileCodes(domiciles: Option[Set[DomicileCode]]): Option[Set[DomicileCode]] = {
    domiciles match {
      case None => Some(config.defaultDomiciles.map(DomicileCode.apply))
      case Some(set) if set.isEmpty => Some(config.defaultDomiciles.map(DomicileCode.apply))
      case Some(set) =>
        set.find(_ == DomicileCode.EnumNotFound) match {
          case Some(_) =>
            throw HttpError.badRequest(s"The request contains unsupported domicile code(s)")
          case None => Some(set)
        }
    }
  }

  private def convertAdminNetworkType(request: UpsertNetworkRequest): UpsertNetworkRequest = {
    //TODO: APPSERV-62935 remove function. Temp hack that converts ADMIN networkType to SMAManger networkType
    val networkTypesOpt = request.networkTypes.map(_.map(s => if (s == NetworkType.Admin.name) NetworkType.SMAManager.name else s))
    request.copy(networkTypes = networkTypesOpt)
  }

  private def validateNetworkType(requesterAcl: UserACL, request: UpsertNetworkRequest)
    (implicit traceId: TraceId): Future[Unit] = {
    //TODO: APPSERV-62935 Switch NetworkType.SMAManager to NetworkType.Admin
    if (request.networkTypes.getOrElse(List.empty).contains(NetworkType.SMAManager.name)) {
      val adminNetworkIdCanBeAdded = request.id.exists(config.adminNetworkTypeConfig.whitelist.contains)
      for {
        _ <- when(request.id.isEmpty).failWith(HttpError.badRequest("Admin NetworkType can only be added on existing Networks, try again after the Network is created")) //When Network is created, need to grab the NetworkId and add it to the SM whitelist
        _ <- when(!requesterAcl.capabilities.contains(Capabilities.Admin)).failWith(HttpError.badRequest("Only Admin Users are allowed to upsert Admin Network Types"))
        _ <- when(!adminNetworkIdCanBeAdded).failWith(HttpError.badRequest("Cannot add Admin NetworkType to this Network, Network is not in whitelist of Networks"))
      } yield ()
    } else {
      Future.unit
    }
  }

  private def updateOktaGroup(req: UpsertNetworkRequest, id: String)
    (implicit traceId: TraceId): Future[Option[String]] = {
    val groupName = networkOktaGroupName(id)
    oktaService.upsertGroup(groupName, groupName, req.networkName).flatMap(group => {
      oktaService.upsertGroupRule(OktaGroupRules.networkGroupRule(id, group.getId, oktaService.getNetworkFieldName))
        .map(_ => Some(group.getId))
    }).recover { case ex =>
      log.error(s"Couldn't create network in Okta", ex)
      None
    }
  }

  private def createOktaGroup(req: UpsertNetworkRequest, id: String)
    (implicit traceId: TraceId): Future[Option[String]] = {
    oktaService.createGroup(networkOktaGroupName(id), networkOktaGroupName(req.networkName)).flatMap(group => {
      oktaService.createGroupRule(OktaGroupRules.networkGroupRule(id, group.getId, oktaService.getNetworkFieldName))
        .map(_ => Some(group.getId))
    }).recover { case ex =>
      log.error(s"Couldn't create network in Okta", ex)
      None
    }
  }

  private def networkOktaGroupName(id: String): String = {
    s"$id ${oktaService.getNetworkFieldName}"
  }

  /** Ensures that the given networks have encryption client keys. Returns the result state of the networks */
  def ensureEncryption(user: UserACL, request: EnsureEncryptionRequest)(implicit traceId: TraceId, token: AccessToken): Future[EncryptionClientKeyResponse] = {
    for {
      _ <- Future.unit
      _ = log.info("Searching for all networks with ids", "userId" -> user.userId, "ids" -> request.networkIds)
      networkIds = request.networkIds.map(NetworkId.apply).toSet
      allFoundNetworks <- networkRepository.getByIds(networkIds)(generateEntitlements(user, EditCapabilities))
      missingKeyNetworks = allFoundNetworks.filter(network => network.encryptionClientKey.isEmpty)
      _ = log.info(s"Found networks, ensuring the encryption client keys", "total" -> allFoundNetworks.size, "without key" -> missingKeyNetworks.size)
      networkKeys <- allFoundNetworks.futureSequence { network => ensureEncryptionClientKey(network, request.encryptionProviderId, user) }
      response = EncryptionClientKeyResponse(networkKeys)
    } yield response
  }

  /** Creates encryption key for the given network (if missing) and updates it in the repository */
  private def ensureEncryptionClientKey(network: Network, encryptionProviderId: Option[String], user: UserACL)(implicit tid: TraceId, token: AccessToken): Future[NetworkEncryptionClientKey] = {
    network.encryptionClientKey match {
      case Some(key) =>
        Future.successful(NetworkEncryptionClientKey(network.id, key))
      case None =>
        for {
          _ <- Future.unit
          requestedClientId = config.encryptionClientPrefix + UUID.randomUUID.toString
          _ = log.info("Creating a new encryption client key for network", network.id, network.name, requestedClientId)
          resultClientId <- kmsService.createClientKey(requestedClientId, encryptionProviderId)
          _ = log.info("Updating network with a new client key", network.id, network.name, resultClientId)
          _ <- networkRepository.updateAndSnap(
            network.copy(encryptionClientKey = Some(resultClientId)),
            userId = user.userId,
            comment = None,
            retries = config.transactionRetryLimit
          )(generateEntitlements(user, EditCapabilities))
        } yield NetworkEncryptionClientKey(network.id, resultClientId)
    }
  }


  /**
   * Masked ids are required in some cases to communicate with external parties,
   * this ensures networks have all requisite ids
   *
   * @param existingIds provided ids from request
   * @return provided ids ++ required ids
   */
  private def addGeneratedIds(existingIds: Set[ExternalId]): Set[ExternalId] = {
    val requiredIds = config.externalTargets
    val idsToAdd = (requiredIds -- existingIds.map(_.subject)).map(subject => ExternalId(subject, UUID.randomUUID.toString))
    existingIds ++ idsToAdd
  }
}

object NetworkService {
  implicit class FutureListOps[A](val list: List[A]) extends AnyVal {
    /** Processes given list of values as a sequence of futures one by one, then merges the results */
    def futureSequence[B](code: A => Future[B])(implicit ec: ExecutionContext): Future[List[B]] = {
      list.foldLeft(Future.successful[List[B]](Nil)) { case (future, element) =>
        future.flatMap { list => code.apply(element).map(next => list :+ next) }
      }
    }
  }
}
