package com.simonmarkets.networks.common.clients.usersync

import com.simonmarkets.circe.{CirceNullAsEmptyDecoders, JsonCodecs}
import io.circe.Decoder
import io.circe.generic.semiauto.deriveDecoder

case class UserNetworkMappingResponse(
    destination_system: SourceDestinationSystem,
    destination_primary: SourceDestinationPrimary,
    destination_secondary: Option[SourceDestinationSecondary] = None,
    destination_property_matcher: SourceDestinationMatchByProperty,
    source_property_matcher: Option[SourceDestinationMatchByProperty] = None,
    source_system: SourceDestinationSystem,
    source_primary: SourceDestinationPrimary,
    source_secondary: Option[SourceDestinationSecondary] = None,
    entitlements: Set[EntitlementMap],
    fields: Set[SyncField]
)

object UserNetworkMappingResponse extends CirceNullAsEmptyDecoders with JsonCodecs {
  implicit lazy val decoder: Decoder[UserNetworkMappingResponse] = deriveDecoder[UserNetworkMappingResponse]
}

case class SourceDestinationSystem(
    name: SourceDestinationSystemName
)

object SourceDestinationSystem extends JsonCodecs {

  val ICN = SourceDestinationSystem(SourceDestinationSystemName.ICN)

  val SIMON = SourceDestinationSystem(SourceDestinationSystemName.SIMON)

  implicit lazy val decoder: Decoder[SourceDestinationSystem] = deriveDecoder[SourceDestinationSystem]

}

case class SourceDestinationPrimary(
    external_id: String,
    kind: SourceDestinationPrimaryIdKind
)

object SourceDestinationPrimary extends JsonCodecs {
  implicit lazy val decoder: Decoder[SourceDestinationPrimary] = deriveDecoder[SourceDestinationPrimary]
}

case class SourceDestinationSecondary(
    external_id: String,
    kind: SourceDestinationSecondaryIdKind
)

object SourceDestinationSecondary extends JsonCodecs {
  implicit lazy val decoder: Decoder[SourceDestinationSecondary] = deriveDecoder[SourceDestinationSecondary]
}

case class SourceDestinationMatchByProperty(
    primary: MatchByPropertyName,
    secondary: Option[String] = None
)

object SourceDestinationMatchByProperty extends JsonCodecs {
  implicit lazy val decoder: Decoder[SourceDestinationMatchByProperty] = deriveDecoder[SourceDestinationMatchByProperty]
}


case class MappingPage(
    total: Int,
    page: Int,
    per_page: Int,
    partner_mappings: Set[UserNetworkMappingResponse]
)

case class EntitlementMap(
    id: Int,
    source_entitlement: String,
    destination_entitlement: String
)
