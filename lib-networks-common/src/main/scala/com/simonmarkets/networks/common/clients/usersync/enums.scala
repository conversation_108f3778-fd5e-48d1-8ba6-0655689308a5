package com.simonmarkets.networks.common.clients.usersync

import com.simonmarkets.util.{EnumEntry, ProductEnums, SafeEnums}
import io.simon.openapi.annotation.Field.EnumValues

@EnumValues("ICN", "SIMON")
sealed trait SourceDestinationSystemName extends EnumEntry

object SourceDestinationSystemName extends ProductEnums[SourceDestinationSystemName] {
  case object ICN extends SourceDestinationSystemName

  case object SIMON extends SourceDestinationSystemName

  override def Values: Seq[SourceDestinationSystemName] = ICN :: SIMON :: Nil
}

@EnumValues("ICN_WHITE_LABEL", "SIMON_NETWORK")
sealed trait SourceDestinationPrimaryIdKind extends EnumEntry

object SourceDestinationPrimaryIdKind extends ProductEnums[SourceDestinationPrimaryIdKind] {
  case object `ICN_WHITE_LABEL` extends SourceDestinationPrimaryIdKind

  case object `SIMON_NETWORK` extends SourceDestinationPrimaryIdKind

  override def Values: Seq[SourceDestinationPrimaryIdKind] = `ICN_WHITE_LABEL` :: `SIMON_NETWORK` :: Nil
}

@EnumValues("ICN_FIRM", "SIMON_LOCATION")
sealed trait SourceDestinationSecondaryIdKind extends EnumEntry

object SourceDestinationSecondaryIdKind extends ProductEnums[SourceDestinationSecondaryIdKind] {
  case object `ICN_FIRM` extends SourceDestinationSecondaryIdKind

  case object `SIMON_LOCATION` extends SourceDestinationSecondaryIdKind

  override def Values: Seq[SourceDestinationSecondaryIdKind] = `ICN_FIRM` :: `SIMON_LOCATION` :: Nil
}

@EnumValues("email", "external_id", "distributor_id")
sealed trait MatchByPropertyName extends EnumEntry

object MatchByPropertyName extends ProductEnums[MatchByPropertyName] {
  case object email extends MatchByPropertyName

  case object external_id extends MatchByPropertyName

  case object distributor_id extends MatchByPropertyName

  override def Values: Seq[MatchByPropertyName] = email :: external_id :: distributor_id :: Nil
}

@EnumValues(
  "icn_groups",
  "external_ids",
  "first_name",
  "last_name",
  "email",
  "icn_roles",
  "password",
  "EnumNotFound",
  "custom_roles",
  "phone"
)
sealed trait SyncField extends EnumEntry

object SyncField extends SafeEnums[SyncField] {

  case object icn_groups extends SyncField

  case object external_ids extends SyncField

  case object first_name extends SyncField

  case object last_name extends SyncField

  case object email extends SyncField

  case object icn_roles extends SyncField

  case object phone extends SyncField

  case object password extends SyncField

  case object EnumNotFound extends SyncField

  val Values: Seq[SyncField] = Seq(
    icn_groups,
    external_ids,
    first_name,
    last_name,
    email,
    phone,
    icn_roles,
    password,
    EnumNotFound
  )

}