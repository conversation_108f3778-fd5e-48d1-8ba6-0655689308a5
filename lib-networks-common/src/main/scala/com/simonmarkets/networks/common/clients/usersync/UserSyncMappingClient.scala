package com.simonmarkets.networks.common.clients.usersync

import akka.NotUsed
import akka.http.scaladsl.model.Uri
import akka.http.scaladsl.model.Uri.Query
import akka.http.scaladsl.model.headers.RawHeader
import akka.stream.Materializer
import akka.stream.scaladsl.Source
import com.github.benmanes.caffeine.cache.AsyncCache
import com.simonmarkets.circe.{CirceNullAsEmptyDecoders, JsonCodecs}
import com.simonmarkets.http.{FutureHttpClient, HttpError}
import com.simonmarkets.http.FutureHttpClient.Tag
import com.simonmarkets.logging.{TraceId, TraceLogging}

import java.util.concurrent.{CompletableFuture, Executor}

import scala.annotation.unused
import scala.compat.java8.FunctionConverters.enrichAsJavaBiFunction
import scala.compat.java8.FutureConverters.{CompletionStageOps, FutureOps}
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

trait UserSyncMappingClient {

  /**
   * Get a mapping of a user's network between two systems i.e. ICN and SIMON
   *
   * @param request data to find the mapping in the destination system for the source system's primary and secondary id's
   * @param traceId trace
   * @return network mapping
   */
  def get(request: UserNetworkMappingRequest)
    (implicit traceId: TraceId): Future[Option[UserNetworkMappingResponse]]

  def getMany(page: Option[Int], per_page: Option[Int])(implicit traceId: TraceId): Future[MappingPage]

  def streamAll(implicit traceId: TraceId): Source[Set[UserNetworkMappingResponse], NotUsed]

  def invalidateCache: Option[Unit]

}

class HttpUserSyncMappingClient(
    client: FutureHttpClient,
    basePath: String,
    apiToken: String,
    hostOpt: Option[String] = None,
    cacheOpt: Option[AsyncCache[UserNetworkMappingRequest, Option[UserNetworkMappingResponse]]] = None,
)(implicit ec: ExecutionContext,
    mat: Materializer) extends UserSyncMappingClient with CirceNullAsEmptyDecoders with JsonCodecs with TraceLogging {

  implicit val requestTag: Tag = Tag("network-mapping-client")
  val baseHeaders: List[RawHeader] = List(RawHeader("Authorization", apiToken))
  val headers: List[RawHeader] = hostOpt.map(host => baseHeaders ++ List(RawHeader("Host", host))).getOrElse(baseHeaders)

  def getMappingFromServer(request: UserNetworkMappingRequest)
    (implicit traceId: TraceId): Future[Option[UserNetworkMappingResponse]] = {
    log.info("retrieving mapping from server")

    for {
      query <- getQueryParams(request)
      optMapping <- client.get[Option[UserNetworkMappingResponse]](Uri(s"$basePath/v1/user_mapping/mappings/map").withQuery(query), headers)
        .transformWith {
          case Failure(exception) =>
            log.error(exception.getMessage)
            Future(None)
          case Success(foundMapping) => Future(foundMapping)
        }
    } yield (optMapping)

  }

  def getMappingFromServerWrapper(traceId: TraceId)(
      request: UserNetworkMappingRequest,
      @unused executor: Executor
  ): CompletableFuture[Option[UserNetworkMappingResponse]] =
    getMappingFromServer(request)(traceId).toJava.toCompletableFuture

  override def get(request: UserNetworkMappingRequest)
    (implicit traceId: TraceId): Future[Option[UserNetworkMappingResponse]] = {

    cacheOpt match {
      case Some(cache) =>
        val cacheFallBackFn: (UserNetworkMappingRequest, Executor) => CompletableFuture[Option[UserNetworkMappingResponse]] = {
          getMappingFromServerWrapper(traceId)
        }

        cache.get(request, cacheFallBackFn.asJava)
          .toScala
      case None => getMappingFromServer(request)
    }
  }

  def getQueryParams(request: UserNetworkMappingRequest): Future[Query] = {
    val baseQuery = Query(
      "source_system" -> request.sourceSystem.name.toString,
      "destination_system" -> request.destinationSystem.name.toString,
      "source_primary_id" -> request.sourcePrimaryId,
      "source_primary_id_kind" -> request.sourcePrimaryIdKind.toString,
    )

    request.sourceSecondaryId.fold(Future.successful(baseQuery)) { secondaryId =>
      request.sourceSecondaryIdKind match {
        case Some(kind) =>
          val updatedQueryMap = baseQuery.toMap ++ Map(
            "source_secondary_id" -> secondaryId,
            "source_secondary_id_kind" -> kind.toString,
          )
          Future.successful(Query(updatedQueryMap))
        case None => Future.failed(HttpError.badRequest("did not supply kind for source secondary"))
      }

    }.map(_.sorted)

  }

  override def getMany(page: Option[Int], per_page: Option[Int])(implicit traceId: TraceId): Future[MappingPage] = {
    val query = Query(
      "page" -> page.fold("")(_.toString),
      "per_page" -> per_page.fold("")(_.toString)
    )
    client.get[MappingPage](Uri(s"$basePath/v1/user_mapping/mappings").withQuery(query), headers)
  }

  def streamAll(implicit traceId: TraceId): Source[Set[UserNetworkMappingResponse], NotUsed] = {
    Source.unfoldAsync(0) { page =>
      client
        .get[MappingPage](Uri(s"$basePath/v1/user_mapping/mappings").withQuery(Query("page" -> page.toString)), headers)
        .map { response =>
          if (response.partner_mappings.isEmpty) None
          else Some((page + 1, response.partner_mappings))
        }
    }
  }

  def invalidateCache: Option[Unit] = {
    cacheOpt.map(_.synchronous().invalidateAll())
  }
}
