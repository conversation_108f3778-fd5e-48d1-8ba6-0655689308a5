package com.simonmarkets.networks.common.repository

import com.mongodb.client.model.ReturnDocument
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.ExternalIdType
import com.simonmarkets.networks.common.service.ExternalIdTypeError._
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.bson.conversions.Bson
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.FindOneAndReplaceOptions
import simon.Id.NetworkId

import scala.concurrent.{ExecutionContext, Future}

trait ExternalIdTypeRepository {

  def get(name: String)(implicit traceId: TraceId): Future[Option[ExternalIdType]]

  def list(implicit traceId: TraceId): Future[Seq[ExternalIdType]]

  def upsert(request: ExternalIdType)(implicit traceId: TraceId): Future[ExternalIdType]

  def delete(name: String)(implicit traceId: TraceId): Future[Unit]

  def getByNetwork(networkId: NetworkId)(implicit traceId: TraceId): Future[Seq[ExternalIdType]]

}

object ExternalIdTypeRepository {

  class V1(collection: MongoCollection[ExternalIdType])
    (implicit ec: ExecutionContext) extends ExternalIdTypeRepository with TraceLogging {

    def get(name: String)(implicit traceId: TraceId): Future[Option[ExternalIdType]] = {
      log.debug(s"Repository get", name)
      collection
        .find(nameFilter(name))
        .toFuture
        .map(_.headOption)
    }

    def list(implicit traceId: TraceId): Future[Seq[ExternalIdType]] = {
      log.debug("Repository list all")
      collection
        .find()
        .toFuture
    }

    def upsert(request: ExternalIdType)(implicit traceId: TraceId): Future[ExternalIdType] = {
      log.debug("Repository upsert", request)
      collection
        .findOneAndReplace(
          filter = nameFilter(request.name),
          replacement = request,
          options = FindOneAndReplaceOptions.apply.upsert(true).returnDocument(ReturnDocument.AFTER)
        )
        .toFutureOption
        .flatMap {
          case Some(value) => Future.successful(value)
          case None => Future.failed(NotFound(request.name, "No document matched on upsert"))
        }
    }

    def delete(name: String)(implicit traceId: TraceId): Future[Unit] = {
      log.debug("Repository delete", name)
      collection
        .deleteOne(nameFilter(name))
        .toFuture
        .map { deleteResult =>
          if (deleteResult.getDeletedCount != 1) NotFound(name, "No document found to delete")
          else ()
        }
    }

    def getByNetwork(networkId: NetworkId)(implicit traceId: TraceId): Future[Seq[ExternalIdType]] = {
      log.debug("Repository get by network", networkId)
      collection
        .find(networkFilter(networkId))
        .toFuture
    }

    private def nameFilter(name: String): Bson = equal("name", name)

    private def networkFilter(networkId: NetworkId): Bson = equal("matchableNetworks", networkId)

  }

}