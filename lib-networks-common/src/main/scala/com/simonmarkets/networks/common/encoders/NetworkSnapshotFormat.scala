package com.simonmarkets.networks.common.encoders

import com.simonmarkets.mongodb.bson.ScalaBsonDocumentsOps.Ops
import com.simonmarkets.mongodb.bson.{Format, NoImplicitToBsonDocumentConversion}
import com.simonmarkets.networks.common.{ChangelogItem, EntitySnapshot, Network}
import org.mongodb.scala.bson.collection.Document

import java.time.ZoneId
import java.util.Date

object NetworkSnapshotFormat extends BsonFormat[EntitySnapshot[Network]] with NoImplicitToBsonDocumentConversion {

  object Fields {
    val SnapshotId = "id"
    val UserId = "userId"
    val ModificationDate = "modificationDate"
    val Comment = "comment"
    val Entity = "entity"
  }

  override def write(v: EntitySnapshot[Network]): Document = {
    import Fields._
    Document(
      SnapshotId -> v.id,
      UserId -> v.userId,
      ModificationDate -> Date.from(v.modificationDate.atZone(ZoneId.systemDefault()).toInstant),
      Comment -> v.comment,
      Entity -> NetworkFormat.write(v.entity)
    )
  }

  override def read(d: Document): EntitySnapshot[Network] = {
    import Fields._
    val modificationDate = documentToUntypedDocument(d).getDate(ModificationDate)
    EntitySnapshot(
      id = d.getString(SnapshotId),
      userId = d.getString(UserId),
      modificationDate = Format.toLocalDateTime(modificationDate),
      comment = d.getStringOpt(Comment),
      entity = d.getAsScala(Entity)(d => NetworkFormat.read(d.asDocument()))
    )
  }

  def readChangelogItem(d: Document): ChangelogItem = {
    import Fields._
    val modificationDate = documentToUntypedDocument(d).getDate(ModificationDate)
    ChangelogItem(
      id = d.getString(SnapshotId),
      userId = d.getString(UserId),
      modificationDate = Format.toLocalDateTime(modificationDate),
      comment = d.getStringOpt(Comment)
    )
  }

}
