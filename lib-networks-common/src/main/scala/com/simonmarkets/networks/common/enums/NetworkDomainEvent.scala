package com.simonmarkets.networks.common.enums

import com.simonmarkets.util.{EnumEntry, SafeEnums}
import io.simon.openapi.annotation.Field.EnumValues

@EnumValues("NetworkCreated", "NetworkUpdated", "NetworkLocationsUpdated")
sealed trait NetworkDomainEvent extends EnumEntry

object NetworkDomainEvent extends SafeEnums[NetworkDomainEvent] {

  case object NetworkCreated extends NetworkDomainEvent
  case object NetworkUpdated extends NetworkDomainEvent
  case object NetworkLocationsUpdated extends NetworkDomainEvent



  override def Values: Seq[NetworkDomainEvent] = NetworkCreated :: NetworkLocationsUpdated :: NetworkUpdated :: Nil

  override val EnumNotFound: NetworkDomainEvent = NetworkUpdated
}

