package com.simonmarkets.networks.common.clients.emailsender

import akka.stream.Materializer
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.http.FutureHttpClient.Tag
import com.simonmarkets.logging.{TraceId, TraceLogging}

import scala.concurrent.Future

trait EmailSenderClient {

  def sendEmail(request: EmailRequest)(implicit traceId: TraceId): Future[Unit]

}

class EmailSenderHttpClient(
    client: FutureHttpClient,
    basePath: String
)(implicit mat: Materializer) extends EmailSenderClient with JsonCodecs with TraceLogging {

  implicit val requestTag: Tag = Tag("email-sender")

  override def sendEmail(request: EmailRequest)(implicit traceId: TraceId): Future[Unit] = {
    log.debug(s"Sending email request ${request.eventId}")
    client.post[EmailRequest, Unit](s"$basePath/v1/email-sender", request)
  }

}
