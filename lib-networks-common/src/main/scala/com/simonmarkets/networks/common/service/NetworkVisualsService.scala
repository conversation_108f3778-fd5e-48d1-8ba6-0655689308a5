package com.simonmarkets.networks.common.service

import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.domain.NetworkVisuals
import com.simonmarkets.networks.common.repository.mongo.MongoNetworkVisualsRepository
import com.simonmarkets.resteasy.utils.FutureOps.FutureOptionOps
import com.simonmarkets.ui.assets.utils.PathHandler
import simon.Id.NetworkId

import scala.concurrent.{ExecutionContext, Future}

class NetworkVisualsService(networkVisualsRepository: MongoNetworkVisualsRepository, pathHandler: PathHandler)
  (implicit ec: ExecutionContext) extends TraceLogging {

  def get(networkId: NetworkId)
    (implicit traceId: TraceId): Future[NetworkVisuals] = {
    log.info(s"Getting network visuals", networkId)
    networkVisualsRepository.get(networkId).flattenOption(s"Network visual not found for network=$networkId").map(
      visuals => visuals.copy(
        visuals = visuals.visuals.copy(
          defaultLogo = visuals.visuals.defaultLogo.map(pathHandler.signURLIfNeeded(_)),
          inverseLogo = visuals.visuals.inverseLogo.map(pathHandler.signURLIfNeeded(_)),
          defaultLogoPDF = visuals.visuals.defaultLogoPDF.map(pathHandler.signURLIfNeeded(_)),
          frontLogoMarketplacePDF = visuals.visuals.frontLogoMarketplacePDF.map(pathHandler.signURLIfNeeded(_)),
          supplementLogoPerformancePDF = visuals.visuals.supplementLogoPerformancePDF.map(pathHandler.signURLIfNeeded(_)),
          headerLogoPerformancePDF = visuals.visuals.headerLogoPerformancePDF.map(pathHandler.signURLIfNeeded(_)),
          supplementLogoSpectrumPDF = visuals.visuals.supplementLogoSpectrumPDF.map(pathHandler.signURLIfNeeded(_)),
          headerLogoSpectrumPDF = visuals.visuals.headerLogoSpectrumPDF.map(pathHandler.signURLIfNeeded(_)),
        )
      )
    )
  }

  def upsert(request: NetworkVisuals)
    (implicit traceId: TraceId): Future[NetworkVisuals] = {
    log.info(s"Upserting network visuals", request.networkId)
    networkVisualsRepository.upsert(request)
  }

  def delete(networkId: NetworkId)(implicit traceId: TraceId): Future[Boolean] = {
    log.info(s"Deleting network visuals", networkId)
    networkVisualsRepository.delete(networkId)
  }
}
