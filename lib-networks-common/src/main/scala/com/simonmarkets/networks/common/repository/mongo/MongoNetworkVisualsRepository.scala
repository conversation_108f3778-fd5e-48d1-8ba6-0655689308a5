package com.simonmarkets.networks.common.repository.mongo

import com.simonmarkets.networks.common.domain.{NetworkVisuals, Visuals}
import com.simonmarkets.networks.common.repository.NetworkVisualsRepository
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.ReplaceOptions
import simon.Id.NetworkId

import scala.concurrent.{ExecutionContext, Future}

class MongoNetworkVisualsRepository(collection: MongoCollection[NetworkVisuals])
  (implicit ec: ExecutionContext) extends NetworkVisualsRepository {

  private final val coll = collection.withCodecRegistry(MongoNetworkVisualsRepository.registry)

  object Fields {
    final val NetworkId = "networkId"
  }

  override def get(networkId: NetworkId): Future[Option[NetworkVisuals]] = {
    coll.find(equal(Fields.NetworkId, NetworkId.unwrap(networkId))).headOption()
  }

  override def upsert(networkVisuals: NetworkVisuals): Future[NetworkVisuals] = {

    coll.replaceOne(
      equal(Fields.NetworkId, networkVisuals.networkId),
      networkVisuals,
      new ReplaceOptions().upsert(true)
    ).toFuture().map(_ => networkVisuals)
  }

  override def delete(networkId: NetworkId): Future[Boolean] = {
    coll.deleteOne(equal(Fields.NetworkId, NetworkId.unwrap(networkId)))
      .toFuture().map(_.getDeletedCount > 0)
  }

}

object MongoNetworkVisualsRepository {
  final val registry = fromRegistries(fromProviders(classOf[NetworkVisuals], classOf[Visuals]),
    DEFAULT_CODEC_REGISTRY)
}
