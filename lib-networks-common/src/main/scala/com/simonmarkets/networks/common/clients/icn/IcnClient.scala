package com.simonmarkets.networks.common.clients.icn

import akka.http.scaladsl.model.headers._
import akka.stream.Materializer
import com.simonmarkets.http.{FutureHttpClient, HttpError}
import com.simonmarkets.http.FutureHttpClient.{FullResponse, Tag}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.api.{CanImpersonateResponse, CandidatesRequest, CandidatesResponse}
import com.simonmarkets.networks.common.clients.icn.IcnClient.IcnCookieName

import scala.concurrent.{ExecutionContext, Future}


/**
 * Client for ICN interactions against main application
 * Should always use no auth client and pass user auth token
 */
trait IcnClient {

  def candidates(request: CandidatesRequest, authToken: String)
    (implicit traceId: TraceId): Future[(CandidatesResponse, Option[HttpCookie])]

  def canImpersonate(icnId: Int, authToken: String)
    (implicit traceId: TraceId): Future[CanImpersonateResponse]

}

class HttpIcnClient(noAuthClient: FutureHttpClient, basePath: String, hostOpt: Option[String])
  (implicit ec: ExecutionContext, mat: Materializer) extends IcnClient with TraceLogging {

  private implicit val tag: Tag = Tag("icn-client")

  override def candidates(request: CandidatesRequest, authToken: String)
    (implicit traceId: TraceId): Future[(CandidatesResponse, Option[HttpCookie])] = {
    noAuthClient
      .post[CandidatesRequest, FullResponse[CandidatesResponse]](
        s"$basePath/v2/passport/candidates",
        request,
        List(Authorization(OAuth2BearerToken(authToken))) ++ hostOpt.map(Host.apply)
      )
      .map { r =>
        val cookieOpt = r
          .headers
          .collectFirst {
            case c: `Set-Cookie` if c.cookie.name == IcnCookieName => c.cookie
          }
        (r.body, cookieOpt)
      }
  }

  override def canImpersonate(icnId: Int, authToken: String)
    (implicit traceId: TraceId): Future[CanImpersonateResponse] = {
    noAuthClient
      .get[CanImpersonateResponse](
        s"$basePath/v1/users/$icnId/can_impersonate",
        List(Authorization(OAuth2BearerToken(authToken))) ++ hostOpt.map(Host.apply))
      .recoverWith {
        case HttpError(httpResponse, _) =>
          log.error(s"HTTP Error occurred during the GET /can_impersonate request for icnId: $icnId. HTTP Response: ${httpResponse.status}")
          Future.successful(CanImpersonateResponse(false))
      }
  }

}

object IcnClient {

  val IcnCookieName = "_icn_session"

}
