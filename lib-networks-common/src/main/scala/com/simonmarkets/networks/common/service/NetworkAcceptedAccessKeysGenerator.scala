package com.simonmarkets.networks.common.service

import com.simonmarkets.capabilities.Capabilities.{Admin, ReadOnlyAdmin}
import com.simonmarkets.capabilities.NetworksCapabilities.{EditNetworkViaNetwork, EditNetworkViaNetworkType, EditNetworkViaPurview, ViewNetworkViaNetwork, ViewNetworkViaNetworkType, ViewNetworkViaPurview}
import com.simonmarkets.entitlements.{AcceptedAccessKeysGenerator, AcceptedKeyBuilder}
import com.simonmarkets.networks.common.Network

trait NetworkAcceptedAccessKeysGenerator extends AcceptedAccessKeysGenerator[Network]{

  override def capabilityToAcceptedKeyBuilders: Map[String, AcceptedKeyBuilder[Network]] = Map(
    Admin -> AcceptedKeyBuilder(buildAdminKeys),
    ReadOnlyAdmin -> AcceptedKeyBuilder(buildAdminKeys),
    ViewNetworkViaNetworkType -> AcceptedKeyBuilder(buildAdminNetworkTypeKey),
    EditNetworkViaNetworkType -> AcceptedKeyBuilder(buildAdminNetworkTypeKey),
    ViewNetworkViaNetwork -> AcceptedKeyBuilder(buildNetworkKeys),
    ViewNetworkViaPurview -> AcceptedKeyBuilder(buildNetworkKeys),
    EditNetworkViaNetwork -> AcceptedKeyBuilder(buildNetworkKeys),
    EditNetworkViaPurview -> AcceptedKeyBuilder(buildNetworkKeys)
  )
  def buildNetworkKeys(capability: String, network: Network): Set[String] = Set(s"$capability:${network.id}")
}
