package com.simonmarkets.networks.common.repository

import com.mongodb.client.model.{CountOptions, FindOneAndReplaceOptions, ReturnDocument}
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.common.api.Page
import com.simonmarkets.networks.common.encoders.NetworkFormat.ExternalIdFormat
import com.simonmarkets.networks.common.encoders.{NetworkFormat, NetworkSnapshotFormat}
import com.simonmarkets.networks.common.repository.NetworkRepository.Keys
import com.simonmarkets.networks.common.{ChangelogItem, EntitySnapshot, Network, NetworkNamesLocationsView}
import org.bson.types.ObjectId
import org.mongodb.scala.bson.BsonArray
import org.mongodb.scala.bson.collection.immutable.Document
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.Sorts.{ascending, descending}
import org.mongodb.scala.{ClientSession, MongoClient, MongoCollection, ReadConcern, ReadPreference, TransactionOptions, WriteConcern, _}
import simon.Id.{ExternalNetworkId, NetworkId}

import java.time.LocalDateTime
import java.util.UUID

import scala.collection.JavaConverters._
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Try}

trait NetworkRepository {

  def getById(id: NetworkId)(entitlements: Set[String]): Future[Option[Network]]

  def getByName(name: String)(entitlements: Set[String]): Future[Option[Network]]

  @deprecated("Use externalIdV2 instead", "3.15.2021")
  def getByExternalId(id: ExternalNetworkId)(entitlements: Set[String]): Future[Option[Network]]

  def getByExternalId(externalId: ExternalId)(entitlements: Set[String]): Future[Option[Network]]

  def getByIds(id: Set[NetworkId])(entitlements: Set[String]): Future[List[Network]]

  def getByIdsWithoutEntitlements(id: Set[NetworkId]): Future[Seq[Network]]

  def getBySubject(subject: String)(entitlements: Set[String]): Future[List[Network]]

  def getNetworks(entitlements: Set[String])(implicit traceId: TraceId): Future[List[Network]]

  def getNetworksPage(limit: Int, from: Option[String])(entitlements: Set[String])
    (implicit traceId: TraceId): Future[Page[Network]]

  /**
   * Called by `sp-contracts-offerings` service for admin users
   */
  def getNetworkIds(entitlements: Set[String]): Future[Set[NetworkId]]

  def getNetworkNames(ids: Set[NetworkId])(entitlements: Set[String]): Future[Map[NetworkId, String]]

  def getAllNetworkNames()(implicit entitlements: Set[String]): Future[Map[NetworkId, String]]

  def getAllNetworkNamesLocations()(implicit entitlements: Set[String]): Future[Seq[NetworkNamesLocationsView]]

  def insertAndSnap(network: Network, userId: String, comment: Option[String], retries: Int)(entitlements: Set[String])
    (implicit traceId: TraceId): Future[Network]

  def updateAndSnap(network: Network, userId: String, comment: Option[String], retries: Int)(entitlements: Set[String])
    (implicit traceId: TraceId): Future[Network]

  def getChangelogs(networkId: NetworkId): Future[List[ChangelogItem]]

  def getSnapshotEntity(snapshotId: String): Future[Option[Network]]

  def getPriorSnapshotByEntityId(networkId: NetworkId): Future[Option[Network]]

  def networkWithSalesFeeRuleExists(id: String): Future[Boolean]

  def exists(id: NetworkId, name: String): Future[Boolean]

  def existingNetworkCodes(): Future[Set[String]]
}


class MongoNetworkRepository(collection: MongoCollection[Document], snapCollection: MongoCollection[Document],
    mongoClient: MongoClient)(implicit ec: ExecutionContext)
  extends NetworkRepository with NetworkMongoJsonCodecs with TraceLogging {

  override def exists(id: NetworkId, name: String): Future[Boolean] = {
    collection.countDocuments(
      or(equal(Keys.NetworkId, id.toString), equal(NetworkFormat.Fields.NetworkName, name)),
      new CountOptions().limit(1)
    ).toFuture().map(count => count != 0)
  }

  override def getById(id: NetworkId)(entitlements: Set[String]): Future[Option[Network]] = {
    collection.find(and(equal(Keys.NetworkId, id.toString), withEntitlements(entitlements))).limit(1).headOption.map(_.map(documentToNetwork))
  }

  override def getByName(name: String)(entitlements: Set[String]): Future[Option[Network]] = {
    collection.find(and(equal(NetworkFormat.Fields.NetworkName, name), withEntitlements(entitlements)))
      .limit(1).headOption.map(_.map(documentToNetwork))
  }

  override def getByExternalId(id: ExternalNetworkId)(entitlements: Set[String]): Future[Option[Network]] = {
    collection.find(and(equal(Keys.ExternalId, id.toString), withEntitlements(entitlements))).limit(1).headOption.map(_.map(documentToNetwork))
  }

  override def getByIds(ids: Set[NetworkId])(entitlements: Set[String]): Future[List[Network]] = {
    if (ids.nonEmpty)
      collection.find(and(or(ids.map(id => equal(Keys.NetworkId, id)).toSeq: _*), withEntitlements(entitlements))).toFuture
        .map(_.map(documentToNetwork).toList)
    else
      Future.successful(List())
  }

  override def getByIdsWithoutEntitlements(ids: Set[NetworkId]): Future[Seq[Network]] = {
    if (ids.nonEmpty) {
      collection.find(in(Keys.NetworkId, ids.toSeq: _*)).toFuture
        .map(_.map(documentToNetwork))
    } else
      Future.successful(List())
  }

  override def getBySubject(subject: String)(entitlements: Set[String]): Future[List[Network]] = {
    collection.find(and(equal(Keys.ExternalId_Subject, subject), withEntitlements(entitlements))).toFuture
      .map(_.map(documentToNetwork).toList)
  }

  override def existingNetworkCodes(): Future[Set[String]] = {
    collection
      .find()
      .projection(Document(Keys.NetworkCode -> 1, "_id" -> 0))
      .toFuture
      .map(_.map(doc => doc.getString(Keys.NetworkCode)).toSet)
  }

  def getNetworkIds(entitlements: Set[String]): Future[Set[NetworkId]] = {
    collection
      .distinct[String](Keys.NetworkId, withEntitlements(entitlements))
      .toFuture
      .map(_.map(NetworkId(_)).toSet)
  }

  override def getNetworkNames(ids: Set[NetworkId])(entitlements: Set[String]): Future[Map[NetworkId, String]] = {
    collection
      .find(and(in(Keys.NetworkId, ids.toSeq: _*), withEntitlements(entitlements)))
      .projection(Document(Keys.NetworkId -> 1, Keys.NetworkName -> 1))
      .toFuture
      .map(_.map { d: Document =>
        val id = d.getString(Keys.NetworkId)
        val name = Option(d.getString(Keys.NetworkName)).getOrElse(id)
        simon.Id.NetworkId(id) -> name
      }.toMap)
  }

  override def getAllNetworkNamesLocations()
    (implicit entitlements: Set[String]): Future[Seq[NetworkNamesLocationsView]] = {
    collection
      .find(withEntitlements(entitlements))
      .projection(Document(Keys.NetworkId -> 1, Keys.NetworkName -> 1, Keys.NetworkLocations -> 1))
      .toFuture
      .map(_.map { d: Document =>
        val id = d.getString(Keys.NetworkId)
        val name = Option(d.getString(Keys.NetworkName)).getOrElse(id)
        val locationsOption: Option[BsonArray] = d.get(Keys.NetworkLocations).map(_.asArray())
        val locations: Set[String] = locationsOption match {
          case Some(bsonArray) =>
            bsonArray.getValues.asScala.map { bsVal =>
              val doc = bsVal.asDocument
              doc.getString("name").getValue
            }.toSet
          case None => Set.empty[String]
        }
        NetworkNamesLocationsView(id = simon.Id.NetworkId(id), name = name, locations = locations)
      })
  }

  override def getAllNetworkNames()(implicit entitlements: Set[String]): Future[Map[NetworkId, String]] = {
    collection
      .find(withEntitlements(entitlements))
      .projection(Document(Keys.NetworkId -> 1, Keys.NetworkName -> 1))
      .toFuture
      .map(_.map { d: Document =>
        val id = d.getString(Keys.NetworkId)
        val name = Option(d.getString(Keys.NetworkName)).getOrElse(id)
        simon.Id.NetworkId(id) -> name
      }.toMap)
  }

  override def getByExternalId(externalId: ExternalId)(entitlements: Set[String]): Future[Option[Network]] = {
    val query = Seq(equal(Keys.ExternalId_Id, externalId.id), equal(Keys.ExternalId_Subject, externalId.subject))
    collection
      .find(and(query :+ withEntitlements(entitlements): _*))
      .limit(1)
      .headOption
      .map(_.map(documentToNetwork))
  }

  override def insertAndSnap(network: Network, userId: String, comment: Option[String], retries: Int)
    (entitlements: Set[String])(implicit traceId: TraceId): Future[Network] = {

    for {
      clientSession <- mongoClient.startSession().toFuture
      _ = clientSession.startTransaction(transactionOptions)
      entity <- collection.insertOne(clientSession, networkToDocument(network)).toFuture.map(_ => network)
      _ = log.info("...finished inserting entity", entity.id)
      snap <- insertSnapshot(clientSession, userId, comment, network)
      _ = log.info("...finished inserting snapshot", snap.id)
      committed <- clientSession.commitTransaction().toSingle.toFuture
      _ = log.info(s"...$committed")
    } yield entity
  }

  override def updateAndSnap(network: Network, userId: String, comment: Option[String], retries: Int)
    (entitlements: Set[String])(implicit traceId: TraceId): Future[Network] = {
    val netToUpdate = network.increaseVersion()

    for {
      clientSession <- mongoClient.startSession().toFuture
      _ = clientSession.startTransaction(transactionOptions)

      entity <- collection.findOneAndReplace(
        clientSession,
        and(equal(Keys.NetworkId, network.id), equal(NetworkFormat.Fields.Version, network.version), withEntitlements(entitlements)),
        networkToDocument(netToUpdate),
        new FindOneAndReplaceOptions().returnDocument(ReturnDocument.AFTER)
      ).toFuture.map(doc => Option(doc) match {
        case None => throw HttpError.badRequest("Network does not exist, or version is out of date")
        case Some(doc) => documentToNetwork(doc)
      })
      _ = log.info("...finished updating entity", entity.id)
      snap <- insertSnapshot(clientSession, userId, comment, netToUpdate)
      _ = log.info("...finished inserting snapshot", snap.id)

      committed <- clientSession.commitTransaction().toSingle.toFuture
      _ = log.info(s"...$committed")
    } yield entity
  }

  override def getNetworksPage(limit: Int, from: Option[String])(entitlements: Set[String])
    (implicit traceId: TraceId): Future[Page[Network]] = {

    val result = from match {
      case Some(id) =>
        Try {
          val objectId = new ObjectId(id)
          val expressions = and(gt("_id", objectId), withEntitlements(entitlements))
          collection.find(and(expressions))
            .sort(ascending("_id"))
            .limit(limit)
            .toFuture()
        }.getOrElse(Future(List()))
      case None =>
        collection.find(and(withEntitlements(entitlements)))
          .sort(ascending("_id"))
          .limit(limit)
          .toFuture()
    }

    collection.countDocuments(and(withEntitlements(entitlements))).toFuture().flatMap { totalCount =>
      result.map {
        documents =>
          log.info(s"Got ${documents.size} users with params", from, limit)
          if (documents.isEmpty) {
            Page(totalCount, 0, None, List())
          } else {
            val lastId = documents.last.get("_id").get.asObjectId().getValue.toString
            val networks = documents.map(documentToNetwork).toList
            val next = if (documents.size < limit) None else Some(lastId)
            Page(totalCount, networks.size, next, networks)
          }
      }
    }
  }

  override def getNetworks(entitlements: Set[String])(implicit traceId: TraceId): Future[List[Network]] = {
    collection.find().toFuture().map(_.map(document =>
      Try(documentToNetwork(document)).recoverWith {
        case ex =>
          log.error(s"Error processing document", ex)
          Failure(ex)
      }
    ).filter(_.isSuccess).map(_.get).toList)
  }

  override def getChangelogs(networkId: NetworkId): Future[List[ChangelogItem]] = {
    snapCollection.find(and(equal("entity.id", NetworkId unwrap networkId)))
      .sort(descending(NetworkSnapshotFormat.Fields.ModificationDate))
      .toFuture
      .map(_.map(NetworkSnapshotFormat.readChangelogItem).toList)
  }

  override def getSnapshotEntity(snapshotId: String): Future[Option[Network]] = {
    snapCollection.find(and(equal(NetworkSnapshotFormat.Fields.SnapshotId, snapshotId)))
      .limit(1)
      .headOption()
      .map(_.map(s => NetworkSnapshotFormat.read(s).entity))
  }

  override def getPriorSnapshotByEntityId(networkId: NetworkId): Future[Option[Network]] = {
    snapCollection.find(equal("entity.id", NetworkId unwrap networkId))
      .sort(descending(NetworkSnapshotFormat.Fields.ModificationDate))
      .skip(1)
      .limit(1)
      .headOption()
      .map(_.map(s => NetworkSnapshotFormat.read(s).entity))
  }

  def networkWithSalesFeeRuleExists(id: String): Future[Boolean] = {
    collection.countDocuments(all("salesFeeRuleIds", id)).toFuture.map(_ > 0)
  }

  private def transactionOptions = TransactionOptions.builder()
    .readPreference(ReadPreference.primary())
    .readConcern(ReadConcern.SNAPSHOT)
    .writeConcern(WriteConcern.MAJORITY)
    .build()

  private def networkToDocument(network: Network) = NetworkFormat.write(network)

  private def documentToNetwork(doc: Document) = NetworkFormat.read(doc)

  private def withEntitlements(entitlements: Set[String]) = in(Keys.Entitlements, entitlements.toSeq: _*)

  private def insertSnapshot(clientSession: ClientSession, userId: String, comment: Option[String],
      network: Network): Future[EntitySnapshot[Network]] = {
    val snapshot = EntitySnapshot(
      id = UUID.randomUUID.toString,
      userId = userId,
      modificationDate = LocalDateTime.now(),
      comment = comment,
      entity = network
    )
    snapCollection.insertOne(clientSession, NetworkSnapshotFormat.write(snapshot)).toFuture().map(_ => snapshot)
  }
}

object NetworkRepository {
  object Keys {
    val NetworkId: String = NetworkFormat.Fields.NetworkId
    val NetworkName: String = NetworkFormat.Fields.NetworkName
    val NetworkCode: String = NetworkFormat.Fields.NetworkCode
    val ExternalId: String = NetworkFormat.Fields.ExternalId
    val Entitlements: String = NetworkFormat.Fields.Entitlements
    val ExternalId_Subject: String = s"${NetworkFormat.Fields.ExternalIds}.${ExternalIdFormat.Subject}"
    val ExternalId_Id: String = s"${NetworkFormat.Fields.ExternalIds}.${ExternalIdFormat.Id}"
    val NetworkLocations: String = NetworkFormat.Fields.Locations
  }

}
