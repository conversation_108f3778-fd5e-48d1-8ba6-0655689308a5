package com.simonmarkets.networks.common.clients.emailsender

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.circe.Json

case class EmailRequest(
    eventId: String,
    eventType: String,
    recipients: Recipients,
    templateId: TemplateId,
    content: Json,
    subject: String,
    urls: Map[String, String] = Map.empty,
    from: Option[String] = None,
    cc: Option[String] = None,
    ccList: Set[String] = Set.empty,
    bcc: Set[String] = Set.empty,
    enrichUrls: Option[Boolean] = None,
    attachments: Set[EmailAttachment] = Set.empty
)


case class EmailAttachment(
    fileName: String,
    fileType: String,
    data: String,
)

case class Recipients(
    userIds: Set[String],
    emails: Set[Recipient]
)

case class Recipient(
    email: String,
    firstName: Option[String],
    lastName: Option[String]
)

sealed trait TemplateId extends EnumEntry

object TemplateId extends ProductEnums[TemplateId] {

  case object `user-sync-mapping` extends TemplateId

  case object `user-passport-verification` extends TemplateId

  case object `user-impersonation-approval-request` extends TemplateId

  case object `user-impersonation-status-update` extends TemplateId

  val Values: Seq[TemplateId] = List(`user-sync-mapping`, `user-passport-verification`)

}
