package com.simonmarkets.networks.common

import com.goldmansachs.marquee.pipg.User.DistributorInfo
import com.goldmansachs.marquee.pipg.{CustodianFaNumber, License, UserRole}
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.shared.MaskedId
import com.simonmarkets.users.common.{Context, LandingPage, LoginMode, User, UserType}
import simon.Id.NetworkId

import java.time.LocalDateTime

object TestUser {

  def apply(
      id: String,
      networkId: NetworkId = TestNetwork().id,
      createdAt: LocalDateTime = LocalDateTime.of(2020, 8, 31, 1, 1),
      createdBy: String = "1234",
      updatedAt: LocalDateTime = LocalDateTime.of(2020, 8, 31, 1, 1),
      updatedBy: String = "12345",
      emailSentAt: Option[LocalDateTime] = None,
      emailSentBy: Option[String] = None,
      lastVisitedAt: Option[LocalDateTime] = None,
      email: String = "<EMAIL>",
      firstName: String = "Test",
      lastName: String = "User",
      distributorId: Option[String] = None,
      omsId: Option[String] = None,
      tradewebEligible: Boolean = false,
      regSEligible: Boolean = false,
      isActive: Boolean = true,
      roles: Set[UserRole] = Set.empty,
      entitlements: Set[String] = Set.empty,
      dynamicRoles: Set[String] = Set.empty,
      locations: Set[String] = Set.empty,
      faNumbers: Set[String] = Set.empty,
      custodianFaNumbers: Set[CustodianFaNumber] = Set.empty,
      customRoles: Set[String] = Set.empty,
      maskedIds: Set[MaskedId] = Set.empty,
      licenses: Set[License] = Set.empty,
      idpId: Option[String] = None,
      distributorInfo: Option[DistributorInfo] = None,
      accountInContext: Option[String] = None,
      context: Option[Context] = None,
      cusips: Set[String] = Set.empty,
      idpLoginId: String = "<EMAIL>",
      externalIds: Seq[ExternalId] = Seq.empty,
      version: Int = 0,
      landingPage: Option[LandingPage] = None) =
    new User(
      id,
      networkId,
      createdAt,
      createdBy,
      updatedAt,
      updatedBy,
      emailSentAt,
      emailSentBy,
      lastVisitedAt,
      email,
      firstName,
      lastName,
      distributorId,
      omsId,
      tradewebEligible,
      regSEligible,
      isActive,
      roles,
      entitlements,
      dynamicRoles,
      locations,
      faNumbers,
      custodianFaNumbers,
      customRoles,
      maskedIds,
      licenses,
      idpId,
      distributorInfo,
      accountInContext,
      context,
      cusips,
      idpLoginId,
      version,
      LoginMode.SSOAndUsernamePassword,
      UserType.Human,
      com.simonmarkets.users.common.EventInfo.Default,
      externalIds = externalIds,
      landingPage = landingPage,
    )

  def create(count: Int, entitlements: Set[String] = Set("admin")): List[User] = {
    (0 until count).toList.map(i => TestUser(i.toString, entitlements = entitlements))
  }

}
