package com.simonmarkets.networks.common.encoders

import com.goldmansachs.marquee.pipg.IdHubOrganization
import com.simonmarkets.networks.common.{ChangelogItem, EntitySnapshot, EventInfo, Network}
import com.simonmarkets.users.common.{LandingPage, LoginMode}
import org.scalatest.{Matchers, WordSpec}
import simon.Id.NetworkId

import java.time.LocalDateTime

class NetworkSnapshotFormatSpec extends WordSpec with Matchers {

  val sansCommentSnapshot: EntitySnapshot[Network] = EntitySnapshot(
    id = "123abc",
    userId = "erik",
    modificationDate = LocalDateTime.of(1993, 7, 24, 1, 1),
    comment = None,
    entity = Network(NetworkId("id"), "name", IdHubOrganization(123,"456"), networkCode = "nc", loginMode = LoginMode.SSOAndUsernamePassword, eventInfo = EventInfo.Default, landingPage = Some(LandingPage.SIMON))
  )

  val withCommentSnapshot: EntitySnapshot[Network] = EntitySnapshot(
    id = "123abc",
    userId = "erik",
    modificationDate = LocalDateTime.of(1993, 7, 24, 1, 1),
    comment = Some("my comment"),
    entity = Network(NetworkId("id"), "name", IdHubOrganization(123,"456"), networkCode = "nc", loginMode = LoginMode.SSOAndUsernamePassword, eventInfo = EventInfo.Default, landingPage = Some(LandingPage.SIMON))
  )

  val sansCommentItem: ChangelogItem = sansCommentSnapshot.asChangelogItem

  val withCommentItem: ChangelogItem = withCommentSnapshot.asChangelogItem

  "NetworkSnapshotFormat" can {
    "encode network snapshot" should {
      "encode snapshot without comment" in {
        val encoded = NetworkSnapshotFormat.write(sansCommentSnapshot)
        val decoded = NetworkSnapshotFormat.read(encoded)

        decoded shouldBe sansCommentSnapshot
      }

      "encode snapshot with comment" in {
        val encoded = NetworkSnapshotFormat.write(withCommentSnapshot)
        val decoded = NetworkSnapshotFormat.read(encoded)

        decoded shouldBe withCommentSnapshot
      }
    }

    "decode changelog item" should {
      "decode changelog item without comment" in {
        val encoded = NetworkSnapshotFormat.write(sansCommentSnapshot)
        val decoded = NetworkSnapshotFormat.readChangelogItem(encoded)

        decoded shouldBe sansCommentItem
      }

      "decode changelog item with comment" in {
        val encoded = NetworkSnapshotFormat.write(withCommentSnapshot)
        val decoded = NetworkSnapshotFormat.readChangelogItem(encoded)

        decoded shouldBe withCommentItem
      }
    }
  }
}
