package com.simonmarkets.networks.common.clients.icn

import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.api.CanImpersonateResponse
import com.simonmarkets.syntax.futureOpsConversion
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}

import scala.concurrent.Future

class IcnClientSpec extends WordSpec with Matchers with MockitoSugar {

  private implicit val traceId: TraceId = TraceId("icn-client-spec-test")

  val mockHttpIcnClient: HttpIcnClient = mock[HttpIcnClient]

  "Icn Client" can {

    "canImpersonate" should {
      "return true" in {
        when(mockHttpIcnClient.canImpersonate(any[Int], any[String])(any[TraceId])).thenReturn(
          Future.successful(CanImpersonateResponse(true)))
        val shouldImpersonate: CanImpersonateResponse = mockHttpIcnClient.canImpersonate(1, "authToken").await
        val desiredResult: CanImpersonateResponse = CanImpersonateResponse(true)
        shouldImpersonate shouldEqual desiredResult
      }

      "return false" in {
        when(mockHttpIcnClient.canImpersonate(any[Int], any[String])(any[TraceId])).thenReturn(
          Future.successful(CanImpersonateResponse(false)))
        val shouldImpersonate: CanImpersonateResponse = mockHttpIcnClient.canImpersonate(1, "authToken").await
        val desiredResult: CanImpersonateResponse = CanImpersonateResponse(false)
        shouldImpersonate shouldEqual desiredResult
      }
    }

  }
}
