package com.simonmarkets.networks.common.service

import akka.actor.ActorSystem
import akka.testkit.TestKit
import com.goldmansachs.marquee.pipg.ProductAttribute.{Category, Default}
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.goldmansachs.marquee.pipg._
import com.mongodb.client.model.FindOneAndReplaceOptions
import com.okta.sdk.resource.model.{Group, GroupRule}
import com.simonmarkets.capabilities.{Capabilities, CustomRoleCapabilities}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.NetworkIds.AdminNetworkId
import com.simonmarkets.networks.common.TestUtils.FutureOps
import com.simonmarkets.networks.common.ChangelogItem
import com.simonmarkets.networks.common._
import com.simonmarkets.networks.common.api._
import com.simonmarkets.networks.common.config.{ActivationConfig, AdminNetworkTypeConfig, NetworkServiceConfig, PagingConfig}
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.enums.NetworkDomainEvent
import com.simonmarkets.networks.common.repository.{MongoNetworkRepository, NetworkRepository}
import com.simonmarkets.networks.common.service.IcnKmsService.AccessToken
import com.simonmarkets.networks.common.service.NetworkServiceSpec._
import com.simonmarkets.networks.common.service.networkservice.NetworkService
import com.simonmarkets.networks.common.{EventInfo, Network, NetworkView}
import com.simonmarkets.networks.enums.DomicileCode
import com.simonmarkets.okta.service.{OktaGroupRule, OktaService}
import com.simonmarkets.shared.ProductType
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.{LoginMode, User}
import org.mockito.ArgumentMatcher
import org.mockito.ArgumentMatchers.{any, argThat, eq => meq}
import org.mockito.Mockito.when
import org.mongodb.scala.bson.conversions
import org.mongodb.scala.{ClientSession, Document, FindObservable, MongoClient, MongoCollection, SingleObservable}
import org.scalatest.Matchers._
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterAll, WordSpecLike}
import simon.Id.NetworkId

import java.time.LocalDateTime

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}
import scala.reflect.ClassTag

class NetworkServiceSpec extends TestKit(ActorSystem("NetworkServiceSpec"))
  with WordSpecLike with BeforeAndAfterAll with MockitoSugar {

  import com.simonmarkets.http.HttpError._

  override def afterAll(): Unit = TestKit.shutdownActorSystem(system)

  "NetworkService" should {
    "getNetworkIds" should {
      "return a user's network ids" in new Scope {
        val expectedNetworkIds =
          Set(network, network2)
            .map(_.id)

        when(networkRepository.getNetworkIds(entitlements)) thenReturn Future.successful(expectedNetworkIds)

        networkService
          .getNetworkIds(adminRequesterAcl) assertResult expectedNetworkIds
      }
    }

    "getNetworksWithPurview" should {
      "return all networks converted to VisibleNetworkView" in new Scope {
        val networkPurview = VisibleNetworkView(id = AdminNetworkId, name = "UnitTestNetworkName", category = None, networkTypes = None, ssoPrefix = None, purviewedDomains = None, domiciles = None)
        when(networkRepository.getNetworks(entitlements)) thenReturn Future.successful(List(network))
        when(networkRepository.getByIdsWithoutEntitlements(Set(AdminNetworkId))) thenReturn Future.successful(List(network))
        networkService.getNetworksWithPurview(nonAdminRequesterAcl) assertResult List(networkPurview)
      }

      "return all networks if user has admin role" in new Scope {
        val networkPurview = VisibleNetworkView(id = AdminNetworkId, name = "UnitTestNetworkName", category = None, networkTypes = None, ssoPrefix = None, purviewedDomains = None, domiciles = None)
        val networkPurview2 = VisibleNetworkView(id = simon.Id.NetworkId("testNetworkId"), name = "UnitTestNetworkName", category = None, networkTypes = None, ssoPrefix = None, purviewedDomains = None, domiciles = None)
        when(networkRepository.getNetworks(Set("admin"))) thenReturn Future.successful(List(network, network2))
        networkService.getNetworksWithPurview(adminRequesterAcl) assertResult List(networkPurview, networkPurview2)
      }

      "return none if no networks found" in new Scope {
        when(networkRepository.getByIdsWithoutEntitlements(Set(AdminNetworkId))) thenReturn Future.successful(List())
        networkService.getNetworksWithPurview(nonAdminRequesterAcl) assertResult List()
      }
    }

    "getNetworkById" should {
      "return network by id if it exists in the repository" in new Scope {
        when(networkRepository.getById(network.id)(entitlements)) thenReturn Future.successful(Some(network))
        networkService.getNetworkById(adminRequesterAcl, network.id) assertResult network
      }
      "fail if network does not exist" in new Scope {
        when(networkRepository.getById(network.id)(entitlements)) thenReturn Future.successful(None)
        networkService.getNetworkById(adminRequesterAcl, network.id) assertFailureMessage notFound(network.id.toString).getMessage()
      }
    }

    "unentitledGetNetworkById" should {
      "return network by id if it exists in the repository" in new Scope {
        when(networkRepository.getById(network.id)(entitlements)) thenReturn Future.successful(Some(network))
        networkService.unentitledGetNetworkById(network.id) assertResult network
      }
      "fail if network does not exist" in new Scope {
        when(networkRepository.getById(network.id)(entitlements)) thenReturn Future.successful(None)
        networkService.unentitledGetNetworkById(network.id) assertFailureMessage notFound(network.id.toString).getMessage()
      }
    }

    "getNetworkViewById" should {
      "return networkview by id if it exists in the repository" in new Scope {
        when(networkRepository.getById(network.id)(entitlements)) thenReturn Future.successful(Some(network))
        networkService.getNetworkViewById(adminRequesterAcl, network.id) assertResult netView
      }
      "fail if network does not exist" in new Scope {
        when(networkRepository.getById(network.id)(entitlements)) thenReturn Future.successful(None)
        val error = intercept[HttpError](
          networkService.getNetworkViewById(adminRequesterAcl, network.id).await
        )
        error.getMessage() shouldBe notFound(network.id.toString).getMessage()
      }
    }

    "getNetworkNamesLocations" should {
      "return id, name and locations" in new Scope {
        when(networkRepository.getAllNetworkNamesLocations()(entitlements)) thenReturn Future.successful(Seq(networkNamesLocations))
        networkService.getNetworksNamesLocations(adminRequesterAcl) assertResult Seq(networkNamesLocations)
      }
    }

    "getNetworkViewByExternalId" should {
      "return networkview by external id and subject if in the repository" in new Scope {
        when(networkRepository.getByExternalId(ExternalId("subject", "externalId"))(entitlements)) thenReturn Future.successful(Some(network))
        networkService.getNetworkViewByExternalId(adminRequesterAcl, ExternalId("subject", "externalId"))
          .assertResult(netView)
      }
      "fail if no network matched" in new Scope {
        when(networkRepository.getByExternalId(ExternalId("subject", "notFound"))(entitlements)) thenReturn Future.successful(None)
        val error = intercept[HttpError](
          networkService.getNetworkViewByExternalId(adminRequesterAcl, ExternalId("subject", "notFound")).await
        )
        error.getMessage() shouldBe notFound("Id: notFound, Subject: subject").getMessage()
      }
    }

    "getNetworks" should {
      "return all networks" in new Scope {
        when(networkRepository.getNetworks(entitlements)) thenReturn Future.successful(List(network))
        networkService.getNetworks(adminRequesterAcl) assertResult List(network)
      }

      "return none if no networks found" in new Scope {
        when(networkRepository.getNetworks(Set("admin"))) thenReturn Future.successful(List())
        networkService.getNetworks(adminRequesterAcl) assertResult List()
      }
    }

    "getNetworkViewPage" should {
      "return a networkview page" in new Scope {
        val page: Page[Network] = Page(total = 100, count = 10, next = None, result = List(network))
        val netViewPage: Page[NetworkView] =
          Page(total = 100, count = 10, next = None, result = List(NetworkView.fromNetwork(network)))

        when(networkRepository.getNetworksPage(100, None)(entitlements)) thenReturn Future.successful(page)
        networkService.getNetworkViewPage(adminRequesterAcl, limit = Option(100), from = None) assertResult netViewPage
      }

      "return a network page using default limit" in new Scope {
        val defaultLimit = 1000
        val page: Page[Network] = Page(total = 100, count = 10, next = None, result = List(network))
        val netViewPage: Page[NetworkView] =
          Page(total = 100, count = 10, next = None, result = List(NetworkView.fromNetwork(network)))

        when(networkRepository.getNetworksPage(defaultLimit, None)(entitlements)) thenReturn Future.successful(page)
        networkService.getNetworkViewPage(adminRequesterAcl, limit = None, from = None) assertResult netViewPage
      }

      "return an empty page if no networks returned" in new Scope {
        val page: Page[Network] = Page(total = 0, count = 0, next = None, result = List())
        val netViewPage: Page[NetworkView] = Page(total = 0, count = 0, next = None, result = List())

        when(networkRepository.getNetworksPage(100, None)(entitlements)) thenReturn Future.successful(page)
        networkService.getNetworkViewPage(adminRequesterAcl, limit = Option(100), from = None) assertResult netViewPage
      }
    }

    "getNetworkViewPageByIds" should {
      "return a networkview page" in new Scope {
        val nets = List(TestNetwork(id = "testId1"), TestNetwork(id = "testId2"))
        val ids: Set[NetworkId] = nets.map(n => n.id).toSet
        val netViewPage: Page[NetworkView] = Page(total = 2, count = 2, next = None, result = nets.map(NetworkView.fromNetwork))
        when(networkRepository.getByIds(ids)(entitlements)) thenReturn Future.successful(nets)

        networkService.getNetworkViewPageByIds(adminRequesterAcl, ids.map(NetworkId.unwrap)) assertResult netViewPage
      }
      "return an empty page if no ids provided" in new Scope {
        val netViewPage: Page[NetworkView] = Page(total = 0, count = 0, next = None, result = List.empty)
        when(networkRepository.getByIds(Set.empty)(entitlements)) thenReturn Future.successful(List.empty)

        networkService.getNetworkViewPageByIds(adminRequesterAcl, Set.empty) assertResult netViewPage
      }
    }

    "getNetworkViewBySubject" should {
      "return networkview by id if it exists in the repository" in new Scope {
        val nets = List(TestNetwork(id = "testId1", externalIds = Set(ExternalId("fidelity", "foo"))), TestNetwork(id = "testId2", externalIds = Set(ExternalId("fidelity", "foo"))), TestNetwork(id = "testId3", externalIds = Set(ExternalId("fidelity2", "foo"))))
        val netViewPage: Page[NetworkView] = Page(total = 2, count = 2, next = None, result = nets.filter(_.id != "testId3").map(NetworkView.fromNetwork))
        when(networkRepository.getBySubject("fidelity")(entitlements)) thenReturn Future.successful(nets.filter(_.id != "testId3"))
        networkService.getNetworkViewPageBySubject(adminRequesterAcl, "fidelity") assertResult netViewPage
      }
      "fail if network does not exist" in new Scope {
        when(networkRepository.getBySubject("fidelity")(entitlements)) thenReturn Future(List.empty)
        networkService.getNetworkViewPageBySubject(adminRequesterAcl, "fidelity").await shouldBe Page(0, 0, None, List())
      }
    }

    "getChangeLogs" should {
      "return change logs if found by network id" in new Scope {
        when(networkRepository.getChangelogs(network.id)) thenReturn Future.successful(List(changelog))
        networkService.getChangelogs(network.id) assertResult List(changelog)
      }
      "return empty list if no change logs found" in new Scope {
        when(networkRepository.getChangelogs(network.id)) thenReturn Future.successful(List.empty)
        networkService.getChangelogs(network.id) assertResult List.empty
      }
    }

    "getSnapshot" should {
      "return networkview of matching snapshot entity" in new Scope {
        when(networkRepository.getSnapshotEntity(snapId)) thenReturn Future.successful(Some(network))
        networkService.getSnapshot(snapId) assertResult netView.some
      }

      "return none if no matching snapshot found" in new Scope {
        when(networkRepository.getSnapshotEntity(snapId)) thenReturn Future.successful(None)
        networkService.getSnapshot(snapId) assertResult None
      }
    }

    "handle insert" should {
      "create a network" in new Scope {

        val req: UpsertNetworkRequest = upsertRequest(network, Some("my-test"))
        val insertEvent = EventInfo(eventType = NetworkDomainEvent.NetworkCreated, triggeredBy = adminRequesterAcl.userId, correlationId = tid)
        when(oktaService.createGroup(any[String], any[String])(any[TraceId], any[ExecutionContext]))
          .thenReturn(oktaSdkGroup.successFuture)
        when(oktaService.createGroupRule(any[OktaGroupRule], any[Boolean])(any[TraceId], any[ExecutionContext]))
          .thenReturn(Future.successful(mock[GroupRule]))

        when(networkRepository.exists(meq(network.id), meq(network.name))) thenReturn Future.successful(false)
        when(networkRepository.insertAndSnap(
          matchWithFields(network.copy(booksCloseConfig = BooksCloseConfig.defaults, booksCloseCustomConfig = BooksCloseConfig.defaults,
            eventInfo = insertEvent, domiciles = Some(Set(DomicileCode.US)))),
          meq(adminRequesterAcl.userId), meq(req.comment), meq(3)
        )(meq(entitlements))(any[TraceId])) thenReturn network.successFuture

        networkService.handleInsertRequest(adminRequesterAcl, req).await shouldBe NetworkView.fromNetwork(network)
      }

      "fail when inserting a Network with an ADMIN NetworkType and without a networkId" in new Scope {
        val req: UpsertNetworkRequest = upsertRequest(network, Some("my-test")).copy(networkTypes = Some(List(NetworkType.Admin.name)), id = None)
        networkService.handleInsertRequest(adminRequesterAcl, req) assertFailureMessage
          badRequest("Admin NetworkType can only be added on existing Networks, try again after the Network is created").getMessage()
      }

      "fail when ADMIN NetworkType is not on whitelist and networkId exists" in new Scope {
        val req: UpsertNetworkRequest = upsertRequest(network, Some("my-test")).copy(networkTypes = Some(List(NetworkType.Admin.name)))
        val superAdminAcl = adminRequesterAcl.copy(customRoles = Set(Capabilities.Admin))
        networkService.handleInsertRequest(superAdminAcl, req) assertFailureMessage
          badRequest("Cannot add Admin NetworkType to this Network, Network is not in whitelist of Networks").getMessage()
      }

      "fail when UserAcl doesn't have admin role and is attempting to add Admin NetworkType and networkId exists" in new Scope {
        val req: UpsertNetworkRequest = upsertRequest(network, Some("my-test")).copy(networkTypes = Some(List(NetworkType.Admin.name)))
        networkService.handleInsertRequest(editViaNetworkAcl, req) assertFailureMessage
          badRequest("Only Admin Users are allowed to upsert Admin Network Types").getMessage()
      }

      "fail if endUserShareableContent is not a subset of learnContent" in new Scope {
        val learnContent = Seq("c1", "c2")
        val endUserShareableContent = Seq("c1", "c2", "c3")
        val req: UpsertNetworkRequest = upsertRequest(network).copy(learnContent = learnContent, endUserShareableContent = endUserShareableContent)

        when(networkRepository.exists(meq(network.id), meq(network.name))) thenReturn Future.successful(false)

        networkService.handleInsertRequest(adminRequesterAcl, req) assertFailureMessage
          badRequest("EndUserShareableContent should be subset of learnContent").getMessage()
      }

      "fail if network already exists in collection" in new Scope {
        val req: UpsertNetworkRequest = upsertRequest(network)

        when(networkRepository.exists(meq(network.id), meq(network.name))) thenReturn Future.successful(true)

        networkService.handleInsertRequest(adminRequesterAcl, req) assertFailureMessage
          badRequest("Network of same ID or Name already exists").getMessage()
      }
    }

    "handle update" when {
      "network already in mongo" in new Scope {
        val req: UpsertNetworkRequest = upsertRequest(network, comment = Some("my-comment"))
        val updateEvent = EventInfo(NetworkDomainEvent.NetworkUpdated, triggeredBy = adminRequesterAcl.userId,
          correlationId = tid)
        when(networkRepository.getById(meq(network.id))(meq(entitlements))) thenReturn Future.successful(Some(network))
        when(networkRepository.updateAndSnap(
          matchWithFields(network.copy(eventInfo = updateEvent, domiciles = Some(Set(DomicileCode.US)))),
          meq(adminRequesterAcl.userId), meq(req.comment), meq(3)
        )(meq(entitlements))(any[TraceId])) thenReturn network.successFuture

        networkService.handleUpdateRequest(adminRequesterAcl, network.id, req) assertResult NetworkView.fromNetwork(network)
      }

      "update a network with ADMIN NetworkType" in new Scope {
        val testNetwork = network.copy(id = NetworkId("AdminNetworkTypeTest"))
        val req: UpsertNetworkRequest = upsertRequest(testNetwork, comment = Some("my-comment")).copy(networkTypes = Some(List(NetworkType.Admin.name)))
        val superAdminAcl = adminRequesterAcl.copy(customRoles = Set(Capabilities.Admin))

        when(networkRepository.getById(meq(testNetwork.id))(meq(entitlements))) thenReturn Future.successful(Some(testNetwork))
        when(networkRepository.updateAndSnap(any[Network], any[String], any[Option[String]], any[Int])(any[Set[String]])(any[TraceId])) thenReturn testNetwork.successFuture
        networkService.handleUpdateRequest(superAdminAcl, testNetwork.id, req) assertResult NetworkView.fromNetwork(testNetwork)
      }

      "fail when ADMIN NetworkType is not on whitelist" in new Scope {
        val req: UpsertNetworkRequest = upsertRequest(network, comment = Some("my-comment")).copy(networkTypes = Some(List(NetworkType.Admin.name)))

        when(networkRepository.getById(meq(network.id))(meq(entitlements))) thenReturn Future.successful(Some(network))
        val superAdminAcl = adminRequesterAcl.copy(customRoles = Set(Capabilities.Admin))
        networkService.handleUpdateRequest(superAdminAcl, network.id, req) assertFailureMessage
          badRequest("Cannot add Admin NetworkType to this Network, Network is not in whitelist of Networks").getMessage()
      }

      "fail when UserAcl doesn't have admin role and is attempting to add Admin NetworkType" in new Scope {
        val req: UpsertNetworkRequest = upsertRequest(network, comment = Some("my-comment")).copy(networkTypes = Some(List(NetworkType.Admin.name)))
        when(networkRepository.getById(meq(network.id))(meq(editViaNetworkEntitlements))) thenReturn Future.successful(Some(network))
        networkService.handleUpdateRequest(editViaNetworkAcl, network.id, req) assertFailureMessage
          badRequest("Only Admin Users are allowed to upsert Admin Network Types").getMessage()
      }

      "network version in mongo and network version in request match" in new Scope {
        val expectedVersion = 42
        val updateEvent = EventInfo(NetworkDomainEvent.NetworkUpdated, triggeredBy = adminRequesterAcl.userId,
          correlationId = tid)
        val testNetwork = network.copy(version = expectedVersion)
        val req: UpsertNetworkRequest = upsertRequest(network, version = expectedVersion)

        when(networkRepository.getById(meq(testNetwork.id))(meq(entitlements))) thenReturn Future.successful(Some(testNetwork))
        when(networkRepository.updateAndSnap(
          matchWithFields(testNetwork.copy(eventInfo = updateEvent, domiciles = Some(Set(DomicileCode.US)))), meq(adminRequesterAcl.userId), meq(req.comment), meq(3)
        )(meq(entitlements))(any[TraceId])) thenReturn testNetwork.successFuture

        networkService.handleUpdateRequest(adminRequesterAcl, testNetwork.id, req) assertResult NetworkView.fromNetwork(testNetwork)
      }

      "fails if attempting to update an out-of-date version of network" in new Scope {
        val expectedVersion = 42
        val actualVersion = 41
        val requestNetwork = network.copy(version = expectedVersion)
        val req: UpsertNetworkRequest = upsertRequest(requestNetwork, version = actualVersion)

        // Mocks for repository method
        val mongoClient: MongoClient = mock[MongoClient]
        val clientSession: ClientSession = mock[ClientSession]
        val clientSessionObservable: SingleObservable[ClientSession] = mock[SingleObservable[ClientSession]]
        val collection: MongoCollection[Document] = mock[MongoCollection[Document]]
        val observable: SingleObservable[Document] = mock[SingleObservable[Document]]
        val findObservable: FindObservable[Document] = mock[FindObservable[Document]]
        val nullDoc: Document = null
        val accessKeysGenerator: AvailableAccessKeysGenerator = mock[AvailableAccessKeysGenerator]

        when(mongoClient.startSession()) thenReturn clientSessionObservable
        when(clientSessionObservable.toFuture) thenReturn clientSession.successFuture
        when(observable.toFuture) thenReturn Future.successful(nullDoc)
        when(collection.find(any[conversions.Bson])(any, any[ClassTag[Document]])) thenReturn findObservable
        when(findObservable.headOption) thenReturn Future.successful(Some(NetworkFormat.write(requestNetwork)))
        when(findObservable.limit(meq(1))) thenReturn findObservable
        when(collection.findOneAndReplace(
          any[ClientSession],
          any[conversions.Bson],
          any[Document],
          any[FindOneAndReplaceOptions]
        )) thenReturn observable
        when(accessKeysGenerator.getAvailableAccessKeysForCapabilities(any[Set[String]], any[UserACL])) thenReturn Set("")

        val actualNetworkRepository = new MongoNetworkRepository(collection, collection, mongoClient)
        val networkServiceWithRepo = new NetworkService(actualNetworkRepository, kmsService, oktaService, config)
        networkServiceWithRepo.handleUpdateRequest(adminRequesterAcl, requestNetwork.id, req) assertFailureMessage
          badRequest("Network does not exist, or version is out of date").getMessage()
      }

      "enrich network with default custom roles" in new Scope {
        val createEvent = EventInfo(NetworkDomainEvent.NetworkCreated, triggeredBy = adminRequesterAcl.userId,
          correlationId = tid)
        val additionalRoleDef = CustomRoleDefinition("otherRole", CustomRoleCapabilities.toSet.tail)
        val networkWithCode = network.copy(customRolesConfig = Set(additionalRoleDef), networkCode = "nc")
        val req: UpsertNetworkRequest = upsertRequest(networkWithCode, Some("my-test"))

        when(oktaService.createGroup(any[String], any[String])(any[TraceId], any[ExecutionContext]))
          .thenReturn(oktaSdkGroup.successFuture)
        when(oktaService.createGroupRule(any[OktaGroupRule], any[Boolean])(any[TraceId], any[ExecutionContext]))
          .thenReturn(Future.successful(mock[GroupRule]))
        when(networkRepository.exists(meq(network.id), meq(network.name))) thenReturn Future.successful(false)
        when(networkRepository.insertAndSnap(
          matchWithFields(networkWithCode.copy(eventInfo = createEvent, booksCloseConfig = BooksCloseConfig.defaults,
            booksCloseCustomConfig = BooksCloseConfig.defaults,
            customRolesConfig = network.customRolesConfig + additionalRoleDef,
            domiciles = Some(Set(DomicileCode.US))
          )),
          meq(adminRequesterAcl.userId), meq(req.comment), meq(3)
        )(meq(entitlements))(any[TraceId])) thenReturn network.successFuture

        networkService.handleInsertRequest(adminRequesterAcl, req).await shouldBe NetworkView.fromNetwork(network)
      }

      "fails if locations are not valid" in new Scope {
        when(networkRepository.getById(meq(network.id))(meq(entitlements))) thenReturn Future.successful(Some(network))

        val learnContent = Seq("c1", "c2")
        val badLocations = Set(
          NetworkLocation("location1", None, Set("location2", "location3")),
          NetworkLocation("location2", None, Set.empty),
          NetworkLocation("location3", Some("location1"), Set.empty))
        val req: UpsertNetworkRequest = upsertRequest(network).copy(learnContent = learnContent, locations = badLocations)

        networkService.handleUpdateRequest(adminRequesterAcl, network.id, req) assertFailureMessage
          badRequest("Exactly one root location is required").getMessage()
      }

      "fail if endUserShareableContent not a subset of learnContent" in new Scope {
        when(networkRepository.getById(meq(network.id))(meq(entitlements))) thenReturn Future.successful(Some(network))

        val invalidRequest: UpsertNetworkRequest = upsertRequest(network).copy(
          endUserShareableContent = Seq("id1", "id2"),
          learnContent = Seq("id3", "id4"))
        networkService.handleUpdateRequest(adminRequesterAcl, network.id, invalidRequest) assertFailureMessage
          badRequest("EndUserShareableContent should be subset of learnContent").getMessage()
      }

      "updates to the domiciles field" in new Scope {

        val upsertNetworkRequest = UpsertNetworkRequest(
          networkName = network.name,
          idHubOrganization = network.idHubOrganization,
          domiciles = Some(Set(DomicileCode.US, DomicileCode.CA)),
          comment = Some("test"),
        )
        val updatedNetwork = network.copy(domiciles = Some(Set(DomicileCode.US, DomicileCode.CA)))
        val updateEvent = EventInfo(NetworkDomainEvent.NetworkUpdated, triggeredBy = adminRequesterAcl.userId, correlationId = tid)

        when(networkRepository.getById(meq(network.id))(meq(entitlements))) thenReturn Future.successful(Some(network))
        when(networkRepository.updateAndSnap(matchWithFields(network.copy(domiciles = Some(Set(DomicileCode.US, DomicileCode.CA)), eventInfo = updateEvent, loginMode = LoginMode("UsernamePassword"))), meq(adminRequesterAcl.userId), meq(upsertNetworkRequest.comment), meq(3))(meq(entitlements))(any[TraceId])) thenReturn Future.successful(updatedNetwork)

        networkService.handleUpdateRequest(adminRequesterAcl, network.id, upsertNetworkRequest)(tid) assertResult NetworkView.fromNetwork(updatedNetwork)
      }

      "network gets updated with default domiciles when upsert with empty domiciles field" in new Scope {
        val upsertNetworkRequest = UpsertNetworkRequest(networkName = network.name,
          idHubOrganization = network.idHubOrganization, domiciles = Some(Set.empty))

        val updatedNetwork = network.copy(domiciles = Some(Set(DomicileCode.US)))

        when(networkRepository.exists(any[NetworkId], any[String])) thenReturn Future.successful(false)
        when(networkRepository.insertAndSnap(any[Network], any[String], any[Option[String]], any[Int])(any[Set[String]])(any[TraceId])) thenReturn Future.successful(updatedNetwork)
        when(oktaService.createGroup(any[String], any[String])(any[TraceId], any[ExecutionContext])) thenReturn Future.successful(mock[Group])
        when(oktaService.createGroupRule(any[OktaGroupRule], any[Boolean])(any[TraceId], any[ExecutionContext])) thenReturn Future.successful(mock[GroupRule])

        val result = networkService.handleInsertRequest(adminRequesterAcl, upsertNetworkRequest).await
        result shouldBe NetworkView.fromNetwork(updatedNetwork)
      }

      "throws error when upsertNetworkRequest contains EnumNotFound domiciles" in new Scope {
        val upsertNetworkRequest = UpsertNetworkRequest(
          networkName = network.name,
          idHubOrganization = network.idHubOrganization,
          domiciles = Some(Set(DomicileCode("XYZ"))),
          comment = Some("test"),
        )

        val thrown = intercept[HttpError] {
          networkService.handleInsertRequest(adminRequesterAcl, upsertNetworkRequest).await
        }
        thrown.getMessage shouldBe "400 Bad Request {\"messages\":[\"The request contains unsupported domicile code(s)\"]}"
      }
    }

    "updates to the productTypeCertificationRequirements field" in new Scope {
      val productTypeCertificationRequirements = Seq(
        ProductCertificationRequirements(
          productType = ProductType.StructuredProduct,
          certificationRequirements = Seq(
            CertificationAlternativesForProduct(productId = "321", productName = "S1", certificationAlternatives = Seq(Seq("abc"), Seq("ghi"))),
            CertificationAlternativesForProduct(productId = "432", productName = "S2", certificationAlternatives = Seq(Seq("ghi")),
              compensationType = Some(CompensationType.Commission)),
            CertificationAlternativesForProduct(productId = "789", productName = "S3", certificationAlternatives = Seq(Seq("bla")),
              compensationType = Some(CompensationType.Fee), underliers = Some(Seq("und1", "und2")))
          )
        ),
        ProductCertificationRequirements(
          productType = ProductType.Annuities,
          certificationRequirements = Seq(
            CertificationAlternativesForProduct(productId = "123", productName = "A1", certificationAlternatives = Seq(Seq("abc"), Seq("ghi")))
          )
        ),
        ProductCertificationRequirements(
          productType = ProductType.TargetReturnETF,
          certificationRequirements = Seq(
            CertificationAlternativesForProduct(productId = "456", productName = "B1", certificationAlternatives = Seq(Seq("def"), Seq("ghi"))))
        ),
        ProductCertificationRequirements(
          productType = ProductType.AlternativeInvestment,
          certificationRequirements = Seq(
            CertificationAlternativesForProduct(productId = "543", productName = "Alt1", certificationAlternatives = Seq(Seq("abc"), Seq("ghi")))
          ),
          certificationProducts = Some(Seq("p1", "p2")),
          attributeBasedCertificationRequirements = Some(
            Seq(
              AttributeBasedCertificationRequirement(
                attributeType = Default,
                attributeValue = None,
                certificationAlternatives = Seq(
                  Seq("track1", "track2", "track3"),
                  Seq("track4", "track5", "track6")
                )
              ),
              AttributeBasedCertificationRequirement(
                attributeType = Category,
                attributeValue = Some("category1"),
                certificationAlternatives = Seq(
                  Seq("track1", "track2", "track3"),
                  Seq("track4", "track5", "track6")
                )
              )
            )
          )
        ),
      )

      val upsertNetworkRequest = UpsertNetworkRequest(
        networkName = network.name,
        idHubOrganization = network.idHubOrganization,
        comment = Some("test"),
        productTypeCertificationRequirements = Some(productTypeCertificationRequirements)
      )
      val updatedNetwork = network.copy(productTypeCertificationRequirements = Some(productTypeCertificationRequirements))
      val updateEvent = EventInfo(NetworkDomainEvent.NetworkUpdated, triggeredBy = adminRequesterAcl.userId, correlationId = tid)

      when(networkRepository.getById(meq(network.id))(meq(entitlements))) thenReturn Future.successful(Some(network))
      when(networkRepository.updateAndSnap(
        matchWithFields(network.copy(
          productTypeCertificationRequirements = Some(productTypeCertificationRequirements),
          eventInfo = updateEvent,
          loginMode = LoginMode("UsernamePassword"),
          domiciles = Some(Set(DomicileCode.US))
        )),
        meq(adminRequesterAcl.userId),
        meq(upsertNetworkRequest.comment),
        meq(3)
      )(meq(entitlements))(any[TraceId])) thenReturn Future.successful(updatedNetwork)


      networkService.handleUpdateRequest(adminRequesterAcl, network.id, upsertNetworkRequest)(tid) assertResult NetworkView.fromNetwork(updatedNetwork)
    }

    "updateNetworkLocations" should {
      "return a networkview with updated locations" in new Scope {
        val request: UpdateLocationsRequest = UpdateLocationsRequest(
          Set(NetworkLocation("loc1", Some("loc2"), Set()), NetworkLocation("loc2", None, Set("loc1"))),
          Some("mycomment")
        )
        val updatedNetwork: Network = network.copy(locations = request.locations)
        val expectedView: NetworkView = NetworkView.fromNetwork(updatedNetwork)

        when(networkRepository.getById(network.id)(entitlements)) thenReturn Future.successful(Some(network))
        when(networkRepository.updateAndSnap(matchWithFields(network.copy(locations = request.locations, idpId = Some(oktaId))),
          meq(adminRequesterAcl.userId), meq(request.comment), meq(3))(meq(entitlements))(any[TraceId])) thenReturn Future.successful(updatedNetwork)

        networkService.updateNetworkLocations(adminRequesterAcl, network.id, request) assertResult Some(expectedView)
      }

      "return 404 when updating non-existent network" in new Scope {
        val request: UpdateLocationsRequest = UpdateLocationsRequest(Set(
          NetworkLocation("loc1", Some("loc2"), Set()),
          NetworkLocation("loc2", None, Set("loc1"))), Some("mycomment"))

        when(networkRepository.getById(network.id)(entitlements)) thenReturn Future.successful(None)

        networkService.updateNetworkLocations(adminRequesterAcl, network.id, request) assertFailureMessage
          notFound(s"${network.id}").getMessage()
      }

      "fail when the locations are invalid" in new Scope {
        val request: UpdateLocationsRequest = UpdateLocationsRequest(Set(NetworkLocation("loc1", Some("parent"), Set("child"))), Some("mycomment"))

        networkService.updateNetworkLocations(adminRequesterAcl, network.id, request) assertFailureMessage
          badRequest("Exactly one root location is required").getMessage()
      }

    }

    "removeUserFromApprovers" should {
      "remove user id from approver list" in new Scope {

        val approverSet: Map[String, List[List[String]]] = Map(
          "key1" -> List(List(userId)),
          "key2" -> List(List("differentUserId"))
        )
        val expectedApproverSet: Map[String, List[List[String]]] = Map(
          "key1" -> List(List()),
          "key2" -> List(List("differentUserId"))
        )
        val origNetwork: Network = network.copy(approverSet = approverSet)
        val expectedNetwork: Network = network.copy(approverSet = expectedApproverSet)

        val netMatcher: ArgumentMatcher[Network] = (n: Network) => {
          n.approverSet == expectedApproverSet
        }

        when(networkRepository.getById(network.id)(entitlements)) thenReturn Future.successful(Some(origNetwork))
        when(networkRepository.updateAndSnap(argThat(netMatcher), meq(adminRequesterAcl.userId), meq(None), meq(3))(argThat(entitlementMatcher))(argThat(traceIdMatcher))) thenReturn Future.successful(expectedNetwork)

        networkService.removeUserFromApprovers(adminRequesterAcl, user) assertResult expectedNetwork
      }
    }

    "ensureEncryption" should {
      implicit val token: AccessToken = AccessToken("test-token")

      "create keys for multiple networks" in new Scope {
        val ids = List("lorem", "ipsum", "dolor")
        val originalNetworks = ids.map { id =>
          network.copy(id = NetworkId(id), name = id, encryptionClientKey = None)
        }
        val expectedNetworks = originalNetworks.map { network =>
          network.copy(encryptionClientKey = Some("test-client-key"))
        }
        val expectedResponse = EncryptionClientKeyResponse(expectedNetworks.map(network => NetworkEncryptionClientKey(network.id, network.encryptionClientKey.get)))

        when(kmsService.createClientKey(any[String], meq(Some("provider-id")))(any[TraceId], any[AccessToken]))
          .thenReturn(Future.successful("test-client-key"))
        when(networkRepository.getByIds(meq(ids.map(NetworkId.apply).toSet))(any[Set[String]]))
          .thenReturn(Future.successful(originalNetworks))
        expectedNetworks.foreach { network =>
          when(networkRepository.updateAndSnap(meq(network), meq(adminRequesterAcl.userId), meq(None), any[Int])(any[Set[String]])(any[TraceId]))
            .thenReturn(Future.successful(network))
        }

        val request = EnsureEncryptionRequest(networkIds = ids, encryptionProviderId = Some("provider-id"))
        networkService.ensureEncryption(adminRequesterAcl, request) assertResult expectedResponse
      }

      "ignore creation of the keys for network that already has them" in new Scope {
        val originalNetwork = network.copy(id = NetworkId("lorem"), name = "lorem", encryptionClientKey = Some("client-key"))
        val expectedResponse = EncryptionClientKeyResponse(NetworkEncryptionClientKey(originalNetwork.id, "client-key") :: Nil)

        when(networkRepository.getByIds(meq(Set(NetworkId("lorem"))))(any[Set[String]]))
          .thenReturn(Future.successful(List(originalNetwork)))

        val request = EnsureEncryptionRequest(networkIds = List("lorem"), encryptionProviderId = Some("provider-id"))
        networkService.ensureEncryption(adminRequesterAcl, request) assertResult expectedResponse
      }
    }

    "getEncryptionClientKeys" should {
      "get client keys for multiple networks" in new Scope {
        val rawIds = List("lorem", "ipsum", "dolor")
        val networkIds = rawIds.map(NetworkId.apply).toSet
        val storedNetworks = rawIds.map { id =>
          val key = if (id == "lorem") None else Some(s"$id-key")
          network.copy(id = NetworkId(id), name = id, encryptionClientKey = key)
        }
        when(networkRepository.getByIds(meq(networkIds))(any[Set[String]]))
          .thenReturn(Future.successful(storedNetworks))
        val expectedResponse = EncryptionClientKeyResponse(List(
          NetworkEncryptionClientKey(NetworkId("ipsum"), "ipsum-key"),
          NetworkEncryptionClientKey(NetworkId("dolor"), "dolor-key"),
        ))
        networkService.getEncryptionClientKeys(networkIds, adminRequesterAcl) assertResult expectedResponse
      }
    }
    "Keeping purviews and purviewNetworks fields in sync" should {
      "update purviewNetworks when purviews are provided but purviewNetworks are missing" in new Scope {
        val purviews = Some(Set(
          Purview("network1", Set(PurviewEntity("issuer1", Some(NetworkCategory.Issuer), Some(Set(PurviewedDomain("domain1"))))))
        ))

        val request = UpsertNetworkRequest(networkName = network.name,
          idHubOrganization = network.idHubOrganization, purviews = purviews)

        val updatedNetwork = network.copy(purviewNetworks = Some(Set(
          networkService.mapPurviewToIssuerPurview(purviews.get.head)
        )))

        when(networkRepository.getById(network.id)(entitlements)) thenReturn Future.successful(Some(network))
        when(networkRepository.updateAndSnap(any[Network], any[String], any[Option[String]], any[Int])(any[Set[String]])(any[TraceId])) thenReturn Future.successful(updatedNetwork)

        val result = networkService.handleUpdateRequest(adminRequesterAcl, network.id, request).await

        result.purviewNetworks shouldBe Some(Set(
          networkService.mapPurviewToIssuerPurview(purviews.get.head)
        ))
      }

      "update purviews when purviewNetworks are provided but purviews are missing" in new Scope {
        val purviewNetworks = Some(Set(
          IssuerPurview(NetworkId("network1"), Set("issuer1"), wholesaler = None, purviewedDomainsUpdated = None)
        ))

        val request = UpsertNetworkRequest(networkName = network.name,
          idHubOrganization = network.idHubOrganization, purviewNetworks = purviewNetworks)

        val updatedNetwork = network.copy(purviews = Some(Set(
          networkService.mapIssuerPurviewToPurview(purviewNetworks.get.head)
        )))

        when(networkRepository.getById(network.id)(entitlements)) thenReturn Future.successful(Some(network))
        when(networkRepository.updateAndSnap(any[Network], any[String], any[Option[String]], any[Int])(any[Set[String]])(any[TraceId])) thenReturn Future.successful(updatedNetwork)

        val result = networkService.handleUpdateRequest(adminRequesterAcl, network.id, request).await

        result.purviews shouldBe Some(Set(
          networkService.mapIssuerPurviewToPurview(purviewNetworks.get.head)
        ))
      }

      "use the purviewNetworks to override purviews info when both are defined (update)" in new Scope {
        val purviews = Some(Set(
          Purview("network1", Set(PurviewEntity("issuer1", Some(NetworkCategory.Issuer), Some(Set(PurviewedDomain.Users)))))
        ))
        val purviewNetworks = Some(Set(
          IssuerPurview(NetworkId("network1"), Set("issuer2"), wholesaler = None, purviewedDomainsUpdated = Some(Set(PurviewedDomain.Users)))
        ))

        val request = UpsertNetworkRequest(networkName = network.name,
          idHubOrganization = network.idHubOrganization, purviews = purviews, purviewNetworks = purviewNetworks)

        val updatedNetwork = network.copy(purviews = Some(Set(
          networkService.mapIssuerPurviewToPurview(purviewNetworks.get.head)
        )))

        when(networkRepository.getById(network.id)(entitlements)) thenReturn Future.successful(Some(network))
        when(networkRepository.updateAndSnap(any[Network], any[String], any[Option[String]], any[Int])(any[Set[String]])(any[TraceId])) thenReturn Future.successful(updatedNetwork)

        val result = networkService.handleUpdateRequest(adminRequesterAcl, network.id, request).await

        result.purviews shouldBe Some(Set(
          networkService.mapIssuerPurviewToPurview(purviewNetworks.get.head)
        ))
      }

      "insert purviewNetworks when purviews are provided but purviewNetworks are missing" in new Scope {
        val purviews = Some(Set(
          Purview("network1", Set(PurviewEntity("issuer1", Some(NetworkCategory.Issuer), Some(Set(PurviewedDomain("domain1"))))))
        ))
        val request = UpsertNetworkRequest(networkName = network.name,
          idHubOrganization = network.idHubOrganization, purviews = purviews)

        val insertedNetwork = network.copy(purviewNetworks = Some(Set(
          networkService.mapPurviewToIssuerPurview(purviews.get.head)
        )))

        when(networkRepository.exists(any[NetworkId], any[String])) thenReturn Future.successful(false)
        when(networkRepository.insertAndSnap(any[Network], any[String], any[Option[String]], any[Int])(any[Set[String]])(any[TraceId])) thenReturn Future.successful(insertedNetwork)
        when(oktaService.createGroup(any[String], any[String])(any[TraceId], any[ExecutionContext])) thenReturn Future.successful(mock[Group])
        when(oktaService.createGroupRule(any[OktaGroupRule], any[Boolean])(any[TraceId], any[ExecutionContext])) thenReturn Future.successful(mock[GroupRule])

        val result = networkService.handleInsertRequest(adminRequesterAcl, request).await

        result.purviewNetworks shouldBe Some(Set(
          networkService.mapPurviewToIssuerPurview(purviews.get.head)
        ))
      }

      "insert purviews when purviewNetworks are provided but purviews are missing" in new Scope {
        val purviewNetworks = Some(Set(
          IssuerPurview(NetworkId("network1"), Set("issuer1"), wholesaler = None, purviewedDomainsUpdated = None)
        ))

        val request = UpsertNetworkRequest(networkName = network.name,
          idHubOrganization = network.idHubOrganization, purviewNetworks = purviewNetworks)

        val insertedNetwork = network.copy(purviews = Some(Set(
          networkService.mapIssuerPurviewToPurview(purviewNetworks.get.head)
        )))

        when(networkRepository.exists(any[NetworkId], any[String])) thenReturn Future.successful(false)
        when(networkRepository.insertAndSnap(any[Network], any[String], any[Option[String]], any[Int])(any[Set[String]])(any[TraceId])) thenReturn Future.successful(insertedNetwork)
        when(oktaService.createGroup(any[String], any[String])(any[TraceId], any[ExecutionContext])) thenReturn Future.successful(mock[Group])
        when(oktaService.createGroupRule(any[OktaGroupRule], any[Boolean])(any[TraceId], any[ExecutionContext])) thenReturn Future.successful(mock[GroupRule])

        val result = networkService.handleInsertRequest(adminRequesterAcl, request).await

        result.purviews shouldBe Some(Set(
          networkService.mapIssuerPurviewToPurview(purviewNetworks.get.head)
        ))
      }

      "use the purviewNetworks to override purviews info when both are defined (insert)" in new Scope {
        val purviews = Some(Set(
          Purview("network1", Set(PurviewEntity("issuer1", Some(NetworkCategory.Issuer), Some(Set(PurviewedDomain.Users, PurviewedDomain.SIRatesOrders)))))
        ))
        val purviewNetworks = Some(Set(
          IssuerPurview(NetworkId("network1"), Set("issuer2"), wholesaler = None, purviewedDomainsUpdated = Some(Set(PurviewedDomain.Users)))
        ))

        val request = UpsertNetworkRequest(networkName = network.name,
          idHubOrganization = network.idHubOrganization, purviews = purviews, purviewNetworks = purviewNetworks)

        val updatedNetwork = network.copy(purviews = Some(Set(
          networkService.mapIssuerPurviewToPurview(purviewNetworks.get.head)
        )))

        when(networkRepository.exists(any[NetworkId], any[String])) thenReturn Future.successful(false)
        when(networkRepository.insertAndSnap(any[Network], any[String], any[Option[String]], any[Int])(any[Set[String]])(any[TraceId])) thenReturn Future.successful(updatedNetwork)
        when(oktaService.createGroup(any[String], any[String])(any[TraceId], any[ExecutionContext])) thenReturn Future.successful(mock[Group])
        when(oktaService.createGroupRule(any[OktaGroupRule], any[Boolean])(any[TraceId], any[ExecutionContext])) thenReturn Future.successful(mock[GroupRule])

        val result = networkService.handleInsertRequest(adminRequesterAcl, request).await

        result.purviews shouldBe Some(Set(
          networkService.mapIssuerPurviewToPurview(purviewNetworks.get.head)
        ))
      }
    }

    "Correct mapping" should {
      "correctly map IssuerPurview to Purview" in new Scope {
        val issuerPurview = IssuerPurview(
          network = NetworkId("network-id"),
          issuers = Set("issuer1"),
          wholesaler = Some(NetworkId("wholesaler1")),
          purviewedDomainsUpdated = Some(Set(PurviewedDomain.Accounts, PurviewedDomain.Users))
        )
        val purview = networkService.mapIssuerPurviewToPurview(issuerPurview)
        purview.networkId shouldBe "network-id"
        purview.purviewEntities should have size 2
        purview.purviewEntities.head.key shouldBe "issuer1"
        purview.purviewEntities.head.purviewType shouldBe Some(NetworkCategory.Issuer)
        purview.purviewEntities.head.domains shouldBe Some(Set(PurviewedDomain.Accounts, PurviewedDomain.Users))
        purview.purviewEntities.last.key shouldBe "wholesaler1"
        purview.purviewEntities.last.purviewType shouldBe Some(NetworkCategory.Wholesaler)
        purview.purviewEntities.last.domains shouldBe Some(Set(PurviewedDomain.Accounts, PurviewedDomain.Users))
      }

      "correctly map Purview to IssuerPurview" in new Scope {
        val purview = Purview(
          networkId = "network-id",
          purviewEntities = Set(
            PurviewEntity(
              key = "issuer1",
              purviewType = Some(NetworkCategory.Issuer),
              domains = Some(Set(PurviewedDomain.Accounts, PurviewedDomain.Users))
            ),
            PurviewEntity(
              key = "wholesaler1",
              purviewType = Some(NetworkCategory.Wholesaler),
              domains = Some(Set(PurviewedDomain.Accounts, PurviewedDomain.Users))
            )
          )
        )
        val issuerPurview = networkService.mapPurviewToIssuerPurview(purview)
        issuerPurview.network shouldBe NetworkId("network-id")
        issuerPurview.issuers should contain("issuer1")
        issuerPurview.wholesaler shouldBe Some(NetworkId("wholesaler1"))
        issuerPurview.purviewedDomainsUpdated shouldBe Some(Set(PurviewedDomain.Accounts, PurviewedDomain.Users))
      }
    }
  }
}

trait Scope extends MockitoSugar with NetworkAcceptedAccessKeysGenerator {
  implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  implicit val tid: TraceId = TraceId("NetworkServiceSpec")

  val userId = "userTestId"
  val oktaId = "oktaId"
  val snapId = "snapId"
  val entitlements = Set("admin")
  val editViaNetworkEntitlements = Set("editNetworkViaNetwork:SIMON Admin")
  val customRolesConfig = Set(CustomRoleDefinition("testRole", capabilities = Set(CustomRoleCapabilities.toSet.head)))
  val networkCode = "nc12"


  val network: Network = TestNetwork(id = AdminNetworkId.toString, customRolesConfig = customRolesConfig)
  val network2: Network = TestNetwork(id = "testNetworkId", customRolesConfig = customRolesConfig)
  val networkNamesLocations = NetworkNamesLocationsView(id = simon.Id.NetworkId(AdminNetworkId.toString), name = "SomeName", locations = Set("loc1", "loc2"))
  val netView: NetworkView = NetworkView.fromNetwork(network)
  val user: User = TestUser(id = userId, networkId = network.id)

  val changelog: ChangelogItem = ChangelogItem(snapId, "userId", LocalDateTime.of(1993, 7, 24, 1, 1), Some("comment"))

  val entitlementMatcher: ArgumentMatcher[Set[String]] = (e: Set[String]) => {
    e == entitlements
  }
  val traceIdMatcher: ArgumentMatcher[TraceId] = (t: TraceId) => {
    t == tid
  }

  val adminRequesterAcl: UserACL = TestUserACL(userId, network.id, capabilities = Set("admin"))
  val nonAdminRequesterAcl: UserACL = TestUserACL(userId, network.id, capabilities = Set())
  val editViaNetworkAcl: UserACL = TestUserACL(userId, network.id, capabilities = Set("editNetworkViaNetwork"))

  val networkRepository: NetworkRepository = mock[NetworkRepository]
  val kmsService: IcnKmsService = mock[IcnKmsService]
  val oktaService: OktaService = mock[OktaService]
  val oktaSdkGroup: Group = mock[Group]
  val config: NetworkServiceConfig = NetworkServiceConfig(
    externalTargets = Set("testExternalTarget"),
    paging = PagingConfig(1000),
    activation = ActivationConfig(Set("@excludedemail.com")),
    omsAlias = "twd",
    defaultCustomRolesConfig = customRolesConfig,
    encryptionClientPrefix = "applicationkey-kms-",
    adminNetworkTypeConfig = AdminNetworkTypeConfig(Set("AdminNetworkTypeTest")),
    defaultDomiciles = Set("US")
  )

  val networkService = new NetworkService(networkRepository, kmsService, oktaService, config)

  when(networkRepository.existingNetworkCodes()).thenReturn(Set.empty[String].successFuture)
  when(oktaService.upsertGroup(any[String], any[String], any[String])(any[TraceId], any[ExecutionContext]))
    .thenReturn(oktaSdkGroup.successFuture)
  when(oktaService.upsertGroupRule(any[OktaGroupRule], any[Boolean])(any[TraceId], any[ExecutionContext]))
    .thenReturn(Future.successful(mock[GroupRule]))
  when(oktaSdkGroup.getId) thenReturn oktaId


  def matchWithFields(network: Network): Network = {
    argThat((arg: Network) => {
      if (arg == null) {
        true
      } else {
        if (network.entitlements.forall(arg.entitlements.contains)) {
          val net1 = network.copy(externalIds = Set.empty, idpId = Some(oktaId), networkCode = networkCode, entitlements = getAcceptedAccessKeys(network)).withDynamicRoles
          val net2 = arg.copy(externalIds = Set.empty, idpId = Some(oktaId), networkCode = networkCode, entitlements = getAcceptedAccessKeys(arg)).withDynamicRoles
          net1 == net2
        } else {
          println(s"[Test] Missing expected entitlements: ${network.entitlements.diff(arg.entitlements)}")
          false
        }
      }
    })
  }
}

object NetworkServiceSpec {
  def upsertRequest(network: Network, comment: Option[String] = None,
      version: Int = 0): UpsertNetworkRequest = UpsertNetworkRequest(
    id = Some(network.id.toString),
    networkName = network.name,
    idHubOrganization = network.idHubOrganization,
    comment = comment,
    version = Some(version),
    customRolesConfig = network.customRolesConfig,
    loginMode = Some(network.loginMode),
    embeddingInfo = network.embeddingInfo,
    landingPage = network.landingPage,
  )
}
