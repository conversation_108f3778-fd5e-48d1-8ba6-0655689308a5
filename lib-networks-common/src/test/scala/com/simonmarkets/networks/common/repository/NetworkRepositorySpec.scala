package com.simonmarkets.networks.common.repository

import com.goldmansachs.marquee.pipg.NetworkLocation
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.networks.common.TestUtils._
import com.simonmarkets.networks.common.api.Page
import com.simonmarkets.networks.common.encoders.{NetworkFormat, NetworkSnapshotFormat}
import com.simonmarkets.networks.common.{EntitySnapshot, Network, NetworkNamesLocationsView, TestNetwork}
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.Document
import org.scalatest.Matchers._
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfter, WordSpec}
import simon.Id.{ExternalNetworkId, NetworkId}

import java.time.LocalDateTime

import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext, Future}

class NetworkRepositorySpec extends WordSpec with MockitoSugar with EmbeddedMongoLike
  with BeforeAndAfter with NetworkMongoJsonCodecs {

  "MongoNetworkRepository" can {
    implicit val ec: ExecutionContext = ExecutionContext.global
    implicit val tid: TraceId = TraceId("test")

    lazy val collection = db.getCollection[Document]("networks_test")
    lazy val snapshotCollection = db.getCollection[Document]("networks_test.snapshots")
    lazy val networkRepository = new MongoNetworkRepository(collection, snapshotCollection, client)

    val entitlements = Set("admin")
    val duration: Duration = 20.seconds

    val testNetworks = TestNetwork.create(10, entitlements)
    val testNetworkWithRuleId = testNetworks(1).copy(id = NetworkId("11"), salesFeeRuleIds = List("ruleId1", "ruleId2"))
    val testNetworkWithLocation = testNetworks(1).copy(id = NetworkId("12"), locations = Set(NetworkLocation(name = "loc1", None, Set.empty)))
    val testNetworkWithExternalId = testNetworks(1).copy(id = NetworkId("11"), externalIds = Set(ExternalId("subject", "id")))
    val testSnaps: List[EntitySnapshot[Network]] = getTestSnapshots(10, testNetworks.slice(0, 5))
    val networkWithRuleIdDoc: Document = NetworkFormat.write(testNetworkWithRuleId)
    val networkWithLocations: Document = NetworkFormat.write(testNetworkWithLocation)
    val networkDocs: Seq[Document] = testNetworks.map(NetworkFormat.write) :+ networkWithRuleIdDoc :+ networkWithLocations
    val snapDocs: Seq[Document] = testSnaps.map(NetworkSnapshotFormat.write)


    before {
      Await.result(collection.drop().toFuture(), duration)
      Await.result(snapshotCollection.drop().toFuture(), duration)
      Await.result(collection.insertMany(networkDocs).toFuture(), duration)
      Await.result(snapshotCollection.insertMany(snapDocs).toFuture(), duration)
    }

    "getById" should {
      "return network matching provided id" in {
        networkRepository.getById(NetworkId("1"))(entitlements) assertResult Some(testNetworks(1))
        networkRepository.getById(NetworkId("3"))(entitlements) assertResult Some(testNetworks(3))
      }
      "return nothing if no matching network found" in {
        networkRepository.getById(NetworkId("not_found"))(entitlements) assertResult None
      }
    }

    "getByExternalId" should {
      "return network matching provided external id" in {
        networkRepository.getByExternalId(ExternalNetworkId("externalId1"))(entitlements) assertResult Some(testNetworks(1))
        networkRepository.getByExternalId(ExternalNetworkId("externalId3"))(entitlements) assertResult Some(testNetworks(3))
      }
      "return nothing if no matching network is found" in {
        networkRepository.getByExternalId(ExternalNetworkId("not_found"))(entitlements) assertResult None
      }
    }

    "getByIds" should {
      "return networks matching provided ids" in {
        networkRepository.getByIds(Set(NetworkId("1"), NetworkId("3")))(entitlements) assertResult List(testNetworks(1), testNetworks(3))
      }
      "return empty list when no matching networks found" in {
        networkRepository.getByIds(Set(NetworkId("not_found"), NetworkId("not_found2")))(entitlements) assertResult List()
      }
      "return empty list when no network ids provided" in {
        networkRepository.getByIds(Set())(entitlements) assertResult List()
      }
    }

    "getByExternalId" should {
      "return network matching using id and subject" in {
        networkRepository.getByExternalId(ExternalId("test", "externalId_2"))(entitlements) assertResult Some(testNetworks(2))
      }
      "return none if mismatch on target" in {
        networkRepository.getByExternalId(ExternalId("notFound", "externalId_3"))(entitlements) assertResult None
      }
    }

    "getBySubject" should {

      "return network matching using id and subject" in {
        val networkWithExternalIds: Document = NetworkFormat.write(testNetworkWithExternalId)
        Await.result(collection.insertOne(networkWithExternalIds).toFuture(), duration)
        networkRepository.getBySubject("subject")(entitlements) assertResult List(testNetworkWithExternalId)
      }
      "return none if mismatch on target" in {
        val networkWithExternalIds: Document = NetworkFormat.write(testNetworkWithExternalId)
        Await.result(collection.insertOne(networkWithExternalIds).toFuture(), duration)
        networkRepository.getBySubject("subject1")(entitlements) assertResult List.empty
      }
    }

    "getNetworks" should {
      "return all networks" in {
        networkRepository.getNetworks(entitlements) assertResult testNetworks :+ testNetworkWithRuleId :+ testNetworkWithLocation
      }
    }

    "getNetworksPage" should {
      "return network page with the first n networks" in {
        val sortedObjectIds = Await.result(collection.find().toFuture(), 4.seconds).map(_.get("_id").get.asObjectId().getValue.toString)
        val limit = 5
        val result = networkRepository.getNetworksPage(limit, None)(entitlements)

        val assertions = (p: Page[Network]) => {
          p.result.size shouldBe limit
          p.result.map(_.id) shouldBe (0 until limit).map(_.toString).toList
          p.next shouldBe Some(sortedObjectIds(limit - 1))
          p.total shouldBe networkDocs.size
        }
        result assert assertions
      }

      "return network page with the next n networks" in {
        val sortedObjectIds = Await.result(collection.find().toFuture(), 4.seconds).map(_.get("_id").get.asObjectId().getValue.toString)
        val limit = 3
        val result = networkRepository.getNetworksPage(limit, Some(sortedObjectIds(limit)))(entitlements)

        val assertions = (p: Page[Network]) => {
          p.result.size shouldBe limit
          p.result.map(_.id) shouldBe (limit + 1 until limit + limit + 1).map(_.toString).toList
          p.next shouldBe Some(sortedObjectIds(limit + limit))
          p.total shouldBe networkDocs.size
        }
        result assert assertions
      }

      "return 0 networks if invalid from specified" in {
        val limit = 5
        val result = networkRepository.getNetworksPage(limit, Some("100"))(entitlements)

        result assertResult Page(networkDocs.size, 0, None, List())
      }

      "return every network from collection if every page is requested" in {
        val limit = 3

        def getPage(from: Option[String], limit: Int, previousPages: List[Page[Network]]): Future[List[Page[Network]]] = {
          val nextPage = networkRepository.getNetworksPage(limit, from)(entitlements)
          nextPage.flatMap { page =>
            if (page.next.isEmpty) {
              Future(page :: previousPages)
            } else {
              getPage(page.next, limit, page :: previousPages)
            }
          }
        }

        val result = getPage(None, limit, List())

        val assertions = (p: List[Page[Network]]) => {
          p.size shouldBe networkDocs.size / limit + 1
          p.flatMap(_.result).size shouldBe networkDocs.size
          p.flatMap(_.result).sortBy(_.id.toString.toInt) shouldBe testNetworks :+ testNetworkWithRuleId :+ testNetworkWithLocation
          p.head.next shouldBe None
        }

        result assert assertions
      }

      "return only networks user is entitled to" in {
        val networksWithDifferentEntitlements = (1 to 5).map(_ => TestNetwork(entitlements = Set("testing")))

        val documents = networksWithDifferentEntitlements.map(net => NetworkFormat.write(net))
        val previousSize = networkDocs.size

        Await.result(collection.insertMany(documents).toFuture(), duration)

        var results = networkRepository.getNetworksPage(100, None)(entitlements)
        results assert ((p: Page[Network]) => {
          p.result.size shouldBe previousSize
        })

        results = networkRepository.getNetworksPage(100, None)(Set("testing"))
        results assert ((p: Page[Network]) => {
          p.result.size shouldBe networksWithDifferentEntitlements.size
        })
      }
    }

    "getNetworkNames" should {
      "return network names for matching ids" in {
        val expected = Map(
          NetworkId("2") -> "name_2",
          NetworkId("5") -> "name_5"
        )
        networkRepository.getNetworkNames(expected.keySet)(entitlements) assertResult expected
      }
    }

    "getAllNetworkNamesLocations" should {
      "return network names, ids, locations" in {
        val expected = List(
          NetworkNamesLocationsView(NetworkId("0"), "name_0", Set.empty),
          NetworkNamesLocationsView(NetworkId("1"), "name_1", Set.empty),
          NetworkNamesLocationsView(NetworkId("2"), "name_2", Set.empty),
          NetworkNamesLocationsView(NetworkId("3"), "name_3", Set.empty),
          NetworkNamesLocationsView(NetworkId("4"), "name_4", Set.empty),
          NetworkNamesLocationsView(NetworkId("5"), "name_5", Set.empty),
          NetworkNamesLocationsView(NetworkId("6"), "name_6", Set.empty),
          NetworkNamesLocationsView(NetworkId("7"), "name_7", Set.empty),
          NetworkNamesLocationsView(NetworkId("8"), "name_8", Set.empty),
          NetworkNamesLocationsView(NetworkId("9"), "name_9", Set.empty),
          NetworkNamesLocationsView(NetworkId("11"), "name_1", Set.empty),
          NetworkNamesLocationsView(NetworkId("12"), "name_1", Set("loc1")),
        )
        networkRepository.getAllNetworkNamesLocations()(entitlements) assertResult expected
      }
    }

    "getAllNetworkNames" should {
      "return network names for matching ids" in {
        val expected = Map(
          NetworkId("0") -> "name_0",
          NetworkId("1") -> "name_1",
          NetworkId("2") -> "name_2",
          NetworkId("3") -> "name_3",
          NetworkId("4") -> "name_4",
          NetworkId("5") -> "name_5",
          NetworkId("6") -> "name_6",
          NetworkId("7") -> "name_7",
          NetworkId("8") -> "name_8",
          NetworkId("9") -> "name_9",
          NetworkId("11") -> "name_1",
          NetworkId("12") -> "name_1",
        )
        networkRepository.getAllNetworkNames()(entitlements) assertResult expected
      }
    }

    "getChangeLogs" should {

      "return changelogs matching on entity id" in {
        val expected = List(testSnaps(1).asChangelogItem, testSnaps(6).asChangelogItem)
        networkRepository.getChangelogs(NetworkId("1")) assertResult expected
      }

      "return nothing if no snapshot matched entity id" in {
        networkRepository.getChangelogs(NetworkId("notfound")) assertResult List.empty
      }

    }

    "getSnapshotEntity" should {

      "return entity in matching snapshot" in {
        networkRepository.getSnapshotEntity("snap_1") assertResult Some(testSnaps(1).entity)
      }

      "return nothing if no snapshot matched id" in {
        networkRepository.getSnapshotEntity("notfound") assertResult None
      }

    }

    "getPriorSnapshotByEntityId" should {

      "return entity in matching entity id" in {
        networkRepository.getPriorSnapshotByEntityId(NetworkId("1")) assertResult Some(testNetworks(1))
      }

      "return nothing if no entity matched id" in {
        networkRepository.getPriorSnapshotByEntityId(NetworkId("not_found")) assertResult None
      }

    }

    "insertAndSnap" should {
      "add new network and snapshot to collections" in {
        //TODO
      }
      "fail to insert already existing network and new snapshot" in {
        //TODO
      }
    }

    "updateAndSnap" should {
      "find and update given network" in {
        //TODO
      }
      "fail to update non-existent network nor create snapshot" in {
        //TODO
      }
    }

    "exists" should {
      "return true if network found by id or name" in {
        networkRepository.exists(NetworkId("1"), "name_1") assertResult true
        networkRepository.exists(NetworkId("not_in_collection"), "name_1") assertResult true
        networkRepository.exists(NetworkId("1"), "not_in_collection") assertResult true
      }
      "return false if network id and name not in collection" in {
        networkRepository.exists(NetworkId("not_in_collection"), "not_in_collection") assertResult false
      }
    }

    "containNetworkWithRuleId" should {
      "return false if sales fee rule id doesn't exist in network" in {
        networkRepository.networkWithSalesFeeRuleExists("nonExistingId") assertResult false
      }

      "return true if sales fee rule id exists in network" in {
        networkRepository.networkWithSalesFeeRuleExists("ruleId1") assertResult true
      }
    }
  }

  def getTestSnapshots(count: Int, networks: List[Network]): List[EntitySnapshot[Network]] = {
    (0 until count).toList.map(i =>
      EntitySnapshot(
        id = "snap_" + i.toString,
        userId = "userId_" + i.toString,
        modificationDate = LocalDateTime.of(1993, 7, 24, 1, 1),
        comment = Some("comment_" + i.toString),
        entity = networks(i % networks.size)
      )
    )
  }
}
