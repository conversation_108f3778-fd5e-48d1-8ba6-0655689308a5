package com.simonmarkets.networks.common.api

import com.goldmansachs.marquee.pipg.IdHubOrganization
import com.goldmansachs.marquee.pipg.Network.Action
import org.scalatest.{Matchers, WordSpec}

class UpsertNetworkRequestSpec extends WordSpec with Matchers {

  val basicRequest: UpsertNetworkRequest = UpsertNetworkRequest(
    networkName = "basicReq",
    idHubOrganization = IdHubOrganization(123,"456")
  )

  "UpsertNetworkRequest" can {

    "getPayoffEntitlementsV1" should {

      "return payoff entitlements from v2 to v1 format" in {
        val payoffsV2: Map[String, Map[String, Set[Action]]] = Map(
          "test" -> Map(
            "e1" -> Set(Action("action1"), Action("action2")),
            "e2" -> Set(Action("action3"))
          ),
          "test2" -> Map.empty
        )
        val payoffsV1: Map[String, Map[String, List[String]]] = Map(
          "test" -> Map(
            "e1" -> List("action1", "action2"),
            "e2" -> List("action3")
          ),
          "test2" -> Map.empty
        )

        val testRequest = basicRequest.copy(payoffEntitlements = Option(payoffsV2))
        testRequest.getPayoffEntitlementsV1 shouldBe payoffsV1
      }

      "return empty map if no payoff entitlements given" in {
        val testRequest = basicRequest.copy(payoffEntitlements = None)
        testRequest.getPayoffEntitlementsV1 shouldBe Map.empty
      }
    }
  }
}
