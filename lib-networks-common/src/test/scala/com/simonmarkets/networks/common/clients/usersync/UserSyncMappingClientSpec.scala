package com.simonmarkets.networks.common.clients.usersync

import akka.actor.ActorSystem
import akka.http.scaladsl.model.Uri.Query
import akka.http.scaladsl.model.{HttpHeader, StatusCode, Uri}
import akka.stream.Materializer
import com.github.benmanes.caffeine.cache.{AsyncCache, Caffeine}
import com.simonmarkets.circe.{CirceNullAsEmptyDecoders, JsonCodecs}
import com.simonmarkets.http.retry.RetryStrategy
import com.simonmarkets.http.{FutureHttpClient, HttpDecoder, HttpError}
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.clients.usersync.SourceDestinationPrimaryIdKind.{`ICN_WHITE_LABEL`, `SIMON_NETWORK`}
import com.simonmarkets.networks.common.clients.usersync.SourceDestinationSecondaryIdKind.`SIMON_LOCATION`
import com.simonmarkets.networks.common.clients.usersync.SourceDestinationSystemName.{ICN, SIMON}
import com.simonmarkets.syntax.futureOpsConversion
import org.mockito.ArgumentMatchers.{any, eq => meq}
import org.mockito.Mockito.{spy, times, verify, when}
import org.scalatest.RecoverMethods.recoverToExceptionIf
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext.global
import scala.concurrent.Future

class UserSyncMappingClientSpec extends WordSpec with Matchers with MockitoSugar with BeforeAndAfterEach with JsonCodecs with CirceNullAsEmptyDecoders {

  val mockCacheReqKey = Caffeine
    .newBuilder()
    .buildAsync[UserNetworkMappingRequest, Option[UserNetworkMappingResponse]]

  val mockCacheStringKey = mock[AsyncCache[String, UserNetworkMappingResponse]]

  val client = mock[FutureHttpClient]

  implicit val ec = global
  implicit lazy val system: ActorSystem = ActorSystem("service")
  implicit lazy val mat: Materializer = Materializer(system)

  val mappingClient = spy(new HttpUserSyncMappingClient(client, "mappings.com", "", None, Some(mockCacheReqKey)))

  implicit val traceId = TraceId.randomize

  val request = UserNetworkMappingRequest(
    sourceSystem = SourceDestinationSystem(ICN),
    destinationSystem = SourceDestinationSystem(SIMON),
    sourcePrimaryId = "test1",
    sourcePrimaryIdKind = `ICN_WHITE_LABEL`,
    sourceSecondaryId = None,
    sourceSecondaryIdKind = None
  )

  val request2 = UserNetworkMappingRequest(
    sourceSystem = SourceDestinationSystem(SIMON),
    destinationSystem = SourceDestinationSystem(ICN),
    sourcePrimaryId = "TN 2",
    sourcePrimaryIdKind = `SIMON_NETWORK`,
    sourceSecondaryId = Some("tn2_b2"),
    sourceSecondaryIdKind = Some(`SIMON_LOCATION`)
  )

  val response = UserNetworkMappingResponse(
    destination_system = SourceDestinationSystem(SIMON),
    destination_primary = SourceDestinationPrimary("test1", `SIMON_NETWORK`),
    destination_secondary = None,
    destination_property_matcher = SourceDestinationMatchByProperty(MatchByPropertyName.email),
    source_property_matcher = None,
    source_system = SourceDestinationSystem(SIMON),
    source_primary = SourceDestinationPrimary("wlp1", `ICN_WHITE_LABEL`),
    source_secondary = None,
    entitlements = Set.empty,
    fields = Set.empty
  )

  "Client" should {

    "retrieve value from cache on subsequent request" in {
      val query = Query(
        "source_system" -> request.sourceSystem.name.toString,
        "destination_system" -> request.destinationSystem.name.toString,
        "source_primary_id" -> request.sourcePrimaryId,
        "source_primary_id_kind" -> request.sourcePrimaryIdKind.toString,
      ).sorted

      when(client.get[Option[UserNetworkMappingResponse]](meq(Uri(s"mappings.com/v1/user_mapping/mappings/map").withQuery(query)), any[List[HttpHeader]])
        (any[HttpDecoder[Option[UserNetworkMappingResponse]]], any[TraceId], any[RetryStrategy], any[FutureHttpClient.RequestTag]))
        .thenReturn(Future.successful(Some(response)))

      val responseFromClient = mappingClient.get(request).await
      responseFromClient shouldBe Some(response)
      verify(mappingClient, times(1)).getMappingFromServer(request)
      val responseFromClient2 = mappingClient.get(request).await
      responseFromClient2 shouldBe Some(response)
      //second call response retrieved from cache so function should still have one call
      verify(mappingClient, times(1)).getMappingFromServer(request)
    }

    "cache none when request failed" in {
      val query = Query(
        "source_system" -> request2.sourceSystem.name.toString,
        "destination_system" -> request2.destinationSystem.name.toString,
        "source_primary_id" -> request2.sourcePrimaryId,
        "source_primary_id_kind" -> request2.sourcePrimaryIdKind.toString,
        "source_secondary_id" -> request2.sourceSecondaryId.get,
        "source_secondary_id_kind" -> request2.sourceSecondaryIdKind.get.toString,
      ).sorted
      when(client.get[Option[UserNetworkMappingResponse]](meq(Uri(s"mappings.com/v1/user_mapping/mappings/map").withQuery(query)), any[List[HttpHeader]])
        (any[HttpDecoder[Option[UserNetworkMappingResponse]]], any[TraceId], any[RetryStrategy], any[FutureHttpClient.RequestTag]))
        .thenReturn(Future.failed(HttpError.notFound("")))

      val responseFromClient = mappingClient.get(request2).await
      responseFromClient shouldBe None
      verify(mappingClient, times(1)).getMappingFromServer(request2)
      val responseFromClient2 = mappingClient.get(request2).await
      responseFromClient2 shouldBe None
      //second call response retrieved from cache so function should still have one call
      verify(mappingClient, times(1)).getMappingFromServer(request2)
    }

    "throw error when source secondary id provided without kind" in {

      val missingKind = request2.copy(sourceSecondaryIdKind = None)
      val query = Query(
        "source_system" -> missingKind.sourceSystem.name.toString,
        "destination_system" -> missingKind.destinationSystem.name.toString,
        "source_primary_id" -> missingKind.sourcePrimaryId,
        "source_primary_id_kind" -> missingKind.sourcePrimaryIdKind.toString,
        "source_secondary_id" -> missingKind.sourceSecondaryId.get,
      ).sorted
      when(client.get[Option[UserNetworkMappingResponse]](meq(Uri(s"mappings.com/v1/user_mapping/mappings/map").withQuery(query)), any[List[HttpHeader]])
        (any[HttpDecoder[Option[UserNetworkMappingResponse]]], any[TraceId], any[RetryStrategy], any[FutureHttpClient.RequestTag]))
        .thenReturn(Future.failed(HttpError.notFound("")))

      recoverToExceptionIf[HttpError] {
        mappingClient.get(missingKind)
      }.map{ e => e.status shouldBe StatusCode.int2StatusCode(400)}
    }
  }

  "decode json" in {
    val mappingWithoutFields =
      """{
        |	"id": 2,
        |	"source_system": {
        |		"id": 1,
        |		"name": "ICN"
        |	},
        |	"destination_system": {
        |		"id": 2,
        |		"name": "SIMON"
        |	},
        |	"source_primary": {
        |		"id": 3,
        |		"external_id": "388",
        |		"kind": "ICN_WHITE_LABEL"
        |	},
        |	"destination_primary": {
        |		"id": 1,
        |		"external_id": "Test Network 2",
        |		"kind": "SIMON_NETWORK"
        |	},
        |	"source_secondary": {
        |		"id": 4,
        |		"external_id": "41265",
        |		"kind": "ICN_FIRM"
        |	},
        |	"destination_secondary": {
        |		"id": 2,
        |		"external_id": "branch2_TN2",
        |		"kind": "SIMON_LOCATION"
        |	},
        |	"source_property_matcher": {
        |		"id": 1,
        |		"primary": "external_id",
        |		"secondary": "test_external"
        |	},
        |	"destination_property_matcher": {
        |		"id": 1,
        |		"primary": "external_id",
        |		"secondary": "test_external"
        |	},
        |	"entitlements": [
        |		{
        |			"id": 2,
        |			"source_entitlement": "62704",
        |			"destination_entitlement": "SpectrumArchitect"
        |		}
        |	]
        |}
        |""".stripMargin

    decode[UserNetworkMappingResponse](mappingWithoutFields) match {
      case Left(e) => throw new Error(e)
      case Right(decodedMapping) =>
        decodedMapping.fields shouldBe Set.empty[String]
    }
  }
}
