package com.simonmarkets.networks.common.repository

import com.simonmarkets.logging.TraceId
import com.simonmarkets.mongodb.codec.IdTypeCodecProvider
import com.simonmarkets.networks.common.ExternalIdType
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.bson.codecs.Macros
import org.scalacheck.{Arbitrary, Gen}
import org.scalatest.Matchers.{contain, convertToAnyShouldWrapper}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{AsyncWordSpec, BeforeAndAfter}
import simon.Id.NetworkId

import scala.concurrent.{ExecutionContext, Future}

class ExternalIdTypeRepositorySpec extends AsyncWordSpec with MockitoSugar with EmbeddedMongoLike with BeforeAndAfter {

  implicit val traceId: TraceId = TraceId("test")
  implicit val ec: ExecutionContext = ExecutionContext.global
  private val registry = fromRegistries(fromProviders(IdTypeCodecProvider(NetworkId), Macros.createCodecProvider[ExternalIdType]), DEFAULT_CODEC_REGISTRY)
  private lazy val collection: MongoCollection[ExternalIdType] = db
    .getCollection[ExternalIdType]("networks_test")
    .withCodecRegistry(registry)
  private lazy val externalIdTypeRepo = new ExternalIdTypeRepository.V1(collection)

  private def genRandomIdTypes(count: Int): Seq[ExternalIdType] = {
    implicit val arbNetId: Arbitrary[NetworkId] = Arbitrary(Gen.identifier.map(NetworkId.apply))
    val arb = Arbitrary(Gen.resultOf(ExternalIdType)).arbitrary
    for (_ <- 1 to count) yield arb.sample.get
  }

  private def seedCol(seedData: Seq[ExternalIdType]): Future[Unit] = {
    for {
      _ <- collection.drop().toFuture
      _ <- collection.insertMany(seedData).toFuture
    } yield ()
  }

  "V1" can {

    "get an externalIdType" in {
      val testIdType = genRandomIdTypes(1).head
      for {
        _ <- seedCol(Seq(testIdType))
        foundIdType <- externalIdTypeRepo.get(testIdType.name)
      } yield foundIdType shouldBe Some(testIdType)
    }

    "delete an externalIdType" in {
      val testIdType = genRandomIdTypes(1).head
      for {
        _ <- seedCol(Seq(testIdType))
        _ <- externalIdTypeRepo.delete(testIdType.name)
        count <- collection.countDocuments().toFuture
      } yield count shouldBe 0
    }

    "list externalIdTypes" in {
      val testIdTypes = genRandomIdTypes(5)
      for {
        _ <- seedCol(testIdTypes)
        colList <- collection.find().toFuture
        list <- externalIdTypeRepo.list
      } yield colList should contain theSameElementsAs list
    }

    "insert an externalIdType" in {
      val newIdType = genRandomIdTypes(1).head
      for {
        _ <- collection.drop().toFuture
        _ <- externalIdTypeRepo.upsert(newIdType)
        fromCol <- collection.find().toFuture
      } yield fromCol.head shouldBe newIdType
    }

    "update an externalIdType" in {
      val newIdType = genRandomIdTypes(1).head
      val modified = newIdType.copy(idpFieldName = "modified")
      for {
        _ <- seedCol(Seq(newIdType))
        _ <- externalIdTypeRepo.upsert(modified)
        fromCol <- collection.find().toFuture
      } yield fromCol.head shouldBe modified
    }

    "getByNetwork" in {
      val newIdTypes = genRandomIdTypes(1).head
      val targetNetwork = NetworkId("network")
      val typeToReturn = ExternalIdType("name", Set(targetNetwork), "idp_name")
      for {
        _ <- seedCol(Seq(newIdTypes) :+ typeToReturn)
        result <- externalIdTypeRepo.getByNetwork(targetNetwork)
      } yield result shouldBe Seq(typeToReturn)
    }
  }
}