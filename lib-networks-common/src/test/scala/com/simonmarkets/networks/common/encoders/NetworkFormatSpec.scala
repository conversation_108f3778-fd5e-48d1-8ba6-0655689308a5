package com.simonmarkets.networks.common.encoders

import com.goldmansachs.marquee.pipg.Network.Action
import com.goldmansachs.marquee.pipg.ProductAttribute._
import com.goldmansachs.marquee.pipg._
import com.simonmarkets.asset.Region
import com.simonmarkets.networks.common.{DeadlineConfig, EventInfo, Network, ProspectusDeadlineConfig}
import com.simonmarkets.networks.enums.CrmProvider.{Redtail, Salesforce}
import com.simonmarkets.networks.enums.DomicileCode
import com.simonmarkets.networks.{ExternalId, SmaStrategyAndUnderliers}
import com.simonmarkets.shared.{EAppProvider, MaskedId, ProductType}
import com.simonmarkets.users.common.{LandingPage, LoginMode}
import org.scalatest.{Matchers, WordSpec}
import simon.Id.ExternalNetworkId

class NetworkFormatSpec extends WordSpec with Matchers {

  val emptyNet: Network = Network(
    id = simon.Id.NetworkId("some-id"),
    name = "emptynet",
    idHubOrganization = IdHubOrganization(123, "ABC"),
    networkCode = "networkCode",
    loginMode = LoginMode.EnumNotFound,
    eventInfo = EventInfo.Default,
    landingPage = None,
  )

  val nonEmptyNet: Network = Network(
    id = simon.Id.NetworkId("some-id"),
    name = "nonemptynet",
    idHubOrganization = IdHubOrganization(123, "ABC"),
    purviews = Some(Set(Purview(networkId = "id", purviewEntities = Set(PurviewEntity(key = "wholesaler", purviewType = Some(NetworkCategory.Wholesaler),
      domains = Some(Set(PurviewedDomain.Accounts))))))),
    approverSet = Map("PeriodicIncomeIncome:CD" -> List(List("123abc", "456cde")), "IncomeGenerator:CD" -> List(List("123abc", "456cde"))),
    ioiApproverSet = Map("IssuerNote" -> List(List("123")), "CallableNote" -> List(List("123"))),
    accountMappings = Option(List("123","234")),
    networkTypes = Option(List(NetworkType.Issuer, NetworkType.HedgeProvider)),
    to = Option(List("<EMAIL>", "<EMAIL>")),
    cc = Option(List("<EMAIL>", "<EMAIL>")),
    purviewNetworks = Option(Set(IssuerPurview(network = simon.Id.NetworkId("id"), issuers = Set("a", "b"),
      wholesaler = Option(simon.Id.NetworkId("wholesales")), purviewedDomainsUpdated = Option(Set(PurviewedDomain.Accounts))))),
    salesFeeRuleIds = List("rule1", "rule2"),
    capabilities = Map("fwpDisclaimer" -> List(), "canSeeQuizzes" -> List("view"), "addLocationKeysToOrder" -> List("view")),
    payoffEntitlements = Map(
      "bank" -> Map("a" -> List("1", "2")), "bank2" -> Map("b" -> List("1", "2")),
      "bank2" -> Map("a" -> List("1", "2")), "bank2" -> Map("b" -> List("1", "2"))
    ),
    payoffEntitlementsV2 = Map(
      "bank" -> Map("a" -> Set(Action(action = "a", contractParams = Map("a" -> Set("x", "y"))),
        Action(action = "b", contractParams = Map("c" -> Set("1", "2"))))),
      "bank2" -> Map("b" -> Set(Action(action = "a", contractParams = Map("a" -> Set("x", "y")))))
    ),
    dynamicRoles = Set("role1", "role2"),
    distributorAlias = Option(ExternalAlias(name = "alias1", pattern = "pattern")),
    omsAlias = Some("omsAlias"),
    version = 12,
    customRolesConfig = Set(CustomRoleDefinition(role = "admin", capabilities = Set("c1", "c2")), CustomRoleDefinition(role = "admin2", capabilities = Set("c1", "c2"))),
    maskedIds = Set(MaskedId(target = "MixPanel", id ="id1"),MaskedId(target = "MixPanel", id ="id2")),
    booksCloseConfig = List(BooksCloseConfig(region = Region.Americas, 1, 2, 3),BooksCloseConfig(region = Region.Asia, 2, 3, 4)),
    booksCloseCustomConfig = List(BooksCloseConfig(region = Region.Americas, 1, 2, 3),BooksCloseConfig(region = Region.Asia, 2, 3, 4)),
    booksSendConfig = Some(List(BooksCloseConfig(region = Region.Americas, 1, 2, 3),BooksCloseConfig(region = Region.Asia, 2, 3, 4))),
    prospectusDeadlineConfig = Option(List(
      ProspectusDeadlineConfig(tier = 1, deadlineConfig = DeadlineConfig(1, 2, 3)),
      ProspectusDeadlineConfig(tier = 2, deadlineConfig = DeadlineConfig(11, 22, 33)))),
    dtccId = Some("thing"),
    locations = Set(NetworkLocation(name = "Erik", parent = None, children = Set("thing1", "thing2")),
      NetworkLocation(name = "Erik", parent = Some("parent"), children = Set())),
    entitlements = Set("admin", "god"), annuityEAppProvider = Some(AnnuityEAppProvider(providerName = EAppProvider.Firelight, params = Map("A" -> "1", "B" -> "C"))),
    ssoPrefix = Option(SSOPrefix(baseUrl = "www.gs.com", redirectionKey = "key", simonBase = None)),
    contactInfo = Option(ContactInfo(email = Some("<EMAIL>"), phone = None, Some(DistributionList(List("<EMAIL>"), List("<EMAIL>"), List("<EMAIL>"))))),
    learnTracksV2 = Seq(
      LearnTrack("other", isActive = true),
      LearnTrack("another", isActive = false)
    ),
    learnContent = Seq("other", "another"),
    endUserShareableContent = Seq("other", "another"),
    idpId = Option("some-idpid"),
    siCertificationRequirements = Seq(
      CertificationAlternativesForProduct(productId = "321", productName = "S1", certificationAlternatives = Seq(Seq("abc"), Seq("ghi"))),
      CertificationAlternativesForProduct(productId = "432", productName = "S2", certificationAlternatives = Seq(Seq("ghi")), compensationType = Some(CompensationType.Commission)),
      CertificationAlternativesForProduct(productId = "789", productName = "S3", certificationAlternatives = Seq(Seq("bla")),
        compensationType = Some(CompensationType.Fee), underliers = Some(Seq("und1", "und2")))
    ),
    annuityCertificationRequirements = Seq(CertificationAlternativesForProduct(productId = "123", productName = "A1", certificationAlternatives = Seq(Seq("abc"), Seq("ghi")))),
    definedOutcomeETFCertificationRequirements = Seq(CertificationAlternativesForProduct(productId = "456", productName = "B1", certificationAlternatives = Seq(Seq("def"), Seq("ghi")))),
    certificationProducts = Some(Seq("74430A664", "74430A665")),
    group = Some("testGroup"),
    externalId = Some(ExternalNetworkId("externalId")),
    externalIds = Set(ExternalId(subject = "MixPanel", id ="id1"),ExternalId(subject = "MixPanel", id ="id2")),
    partnerUrls = Set(PartnerUrl("somePartnerName", Set(URL("someURLType", "someURL")))),
    networkCode = "nc",
    crmProviders = List(Redtail, Salesforce),
    loginMode = LoginMode.SSOAndUsernamePassword,
    embeddingInfo = Some(EmbeddingInfo("host url", Some("app name"))),
    eventInfo = EventInfo.Default,
    smaStrategiesAndUnderliers = Set(SmaStrategyAndUnderliers("123", Set("SPX")), SmaStrategyAndUnderliers("456", Set("SPX", "M1EA"))),
    smaRestrictedIssuers = Set("CITI", "JPM"),
    contactInfo2 = Option(List(
      ContactInfo2(name = Some("name1"), url = Some("www.fakeurl.com/userid/123"), urlDisplayText = Some("urlDisplayText1"), phone = Some("************"), email = Some("<EMAIL>"), contactType = Some(ContactType.HomeOffice)),
      ContactInfo2(name = Some("name2"), url = Some("www.fakeurl.com/userid/234"), urlDisplayText = Some("urlDisplayText2"), phone = Some("************"), email = Some("<EMAIL>"), contactType = Some(ContactType.SIMON)),
      ContactInfo2(name = Some("name3"), url = Some("www.fakeurl.com/userid/345"), urlDisplayText = Some("urlDisplayText3"), phone = Some("************"), email = Some("<EMAIL>"), contactType = Some(ContactType.HomeSupport))
    )),
    contactInfo2Name = Some("The name on contact"),
    altCertificationRequirements = Some(
      Seq(CertificationAlternativesForProduct(productId = "payoffType4", productName = "payoff4", certificationAlternatives = Seq(Seq("t5", "t6"), Seq("t7", "t8"))))
    ),
    uiViewCardOverrides = Map(
      "SomeView" -> List("SomePromo","SomeOtherPromo")
    ),
    uiViewCardOverridesExpiryDate = Map(
      "Structured Investments" -> "12/20/23"
    ),
    historicHoldingsStartFrom = Some("2022-05-20"),
    cobrandingCustomDisclosure = Some("Some Custom Disclosure"),
    encryptionClientKey = Some("7933b9d1-e948-4574-aded-1816d6cadb70"),
    sessionInactivityTimeout = Some(30),
    landingPage = Some(LandingPage.SIMON),
    domiciles = Some(Set(DomicileCode.US, DomicileCode.CA)),
    productTypeCertificationRequirements = Some(Seq(
      ProductCertificationRequirements(
        productType = ProductType.StructuredProduct,
        certificationRequirements = Seq(
          CertificationAlternativesForProduct(productId = "321", productName = "S1", certificationAlternatives = Seq(Seq("abc"), Seq("ghi"))),
          CertificationAlternativesForProduct(productId = "432", productName = "S2", certificationAlternatives = Seq(Seq("ghi")),
            compensationType = Some(CompensationType.Commission)),
          CertificationAlternativesForProduct(productId = "789", productName = "S3", certificationAlternatives = Seq(Seq("bla")),
            compensationType = Some(CompensationType.Fee), underliers = Some(Seq("und1", "und2")))
        )
      ),
      ProductCertificationRequirements(
        productType = ProductType.Annuities,
        certificationRequirements = Seq(
          CertificationAlternativesForProduct(productId = "123", productName = "A1", certificationAlternatives = Seq(Seq("abc"), Seq("ghi")))
        )
      ),
      ProductCertificationRequirements(
        productType = ProductType.TargetReturnETF,
        certificationRequirements = Seq(
          CertificationAlternativesForProduct(productId = "456", productName = "B1", certificationAlternatives = Seq(Seq("def"), Seq("ghi")))        )
      ),
      ProductCertificationRequirements(
        productType = ProductType.AlternativeInvestment,
        certificationRequirements = Seq(
          CertificationAlternativesForProduct(productId = "543", productName = "Alt1", certificationAlternatives = Seq(Seq("abc"), Seq("ghi")))
        ),
        certificationProducts = Some(Seq("p1", "p2")),
        attributeBasedCertificationRequirements = Some(
          Seq(
            AttributeBasedCertificationRequirement(
              attributeType = Default,
              attributeValue = None,
              certificationAlternatives = Seq(
                Seq("track1", "track2", "track3"),
                Seq("track4", "track5", "track6")
              )
            ),
            AttributeBasedCertificationRequirement(
              attributeType = Category,
              attributeValue = Some("category1"),
              certificationAlternatives = Seq(
                Seq("track1", "track2", "track3"),
                Seq("track4", "track5", "track6")
              )
            )
          )
        )
      ),
    )),
    wlpUrl = Some("WhiteLabelUrl")
  )

  "NetworkFormat" can {
    "encode network" should {
      "encode non empty network" in {
        val encoded = NetworkFormat.write(nonEmptyNet)
        val decoded = NetworkFormat.read(encoded)

        decoded shouldBe nonEmptyNet
      }

      "encode empty network" in {
        val encoded = NetworkFormat.write(emptyNet)
        val decoded = NetworkFormat.read(encoded)

        decoded shouldBe emptyNet
      }
    }
  }
}
