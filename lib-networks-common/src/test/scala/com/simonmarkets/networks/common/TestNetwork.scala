package com.simonmarkets.networks.common

import com.goldmansachs.marquee.pipg.Network.Action
import com.goldmansachs.marquee.pipg.{AnnuityEAppProvider, BooksCloseConfig, CertificationAlternativesForProduct, ContactInfo, ContactInfo2, CustomRoleDefinition, ExternalAlias, IdHubOrganization, IssuerPurview, LearnTrack, NetworkLocation, NetworkType, PartnerUrl, ProductCertificationRequirements, Purview, SSOPrefix}
import com.simonmarkets.networks.{ExternalId, SmaStrategyAndUnderliers}
import com.simonmarkets.networks.enums.CrmProvider
import com.simonmarkets.shared.{MaskedId, ProductType}
import com.simonmarkets.users.common.{LandingPage, LoginMode}
import simon.Id.{ExternalNetworkId, NetworkId}

object TestNetwork {
  def apply(
      id: String = "UnitTestNetwork",
      name: String = "UnitTestNetworkName",
      purviews: Option[Set[Purview]] = None,
      approverSet: Map[String, List[List[String]]] = Map.empty,
      ioiApproverSet: Map[String, List[List[String]]] = Map.empty,
      accountMappings: Option[List[String]] = None,
      networkTypes: Option[List[NetworkType]] = None,
      to: Option[List[String]] = None,
      cc: Option[List[String]] = None,
      purviewNetworks: Option[Set[IssuerPurview]] = None,
      salesFeeRuleIds: List[String] = Nil,
      capabilities: Map[String, List[String]] = Map.empty,
      payoffEntitlements: Map[String, Map[String, List[String]]] = Map.empty,
      payoffEntitlementsV2: Map[String, Map[String, Set[Action]]] = Map.empty,
      dynamicRoles: Set[String] = Set.empty,
      distributorAlias: Option[ExternalAlias] = None,
      omsAlias: Option[String] = None,
      version: Int = 0,
      customRolesConfig: Set[CustomRoleDefinition] = Set.empty,
      maskedIds: Set[MaskedId] = Set.empty,
      booksCloseConfig: List[BooksCloseConfig] = List.empty,
      booksCloseCustomConfig: List[BooksCloseConfig] = List.empty,
      booksSendConfig: Option[List[BooksCloseConfig]] = None,
      prospectusDeadlineConfig: Option[List[ProspectusDeadlineConfig]] = None,
      dtccId: Option[String] = None,
      locations: Set[NetworkLocation] = Set.empty,
      entitlements: Set[String] = Set.empty,
      annuityEAppProvider: Option[AnnuityEAppProvider] = None,
      ssoPrefix: Option[SSOPrefix] = None,
      contactInfo: Option[ContactInfo] = None,
      @deprecated("This field has been deprecated in favor of learnTracksV2", "86.0.0")
      learnTracks: Seq[String] = Seq.empty,
      learnTracksV2: Seq[LearnTrack] = Seq.empty,
      learnContent: Seq[String] = Seq.empty,
      endUserShareableContent: Seq[String] = Seq.empty,
      idpId: Option[String] = None,
      siCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
      annuityCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
      definedOutcomeETFCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
      certificationProducts: Option[Seq[String]] = None,
      externalId: Option[ExternalNetworkId] = None,
      externalIds: Set[ExternalId] = Set.empty,
      partnerUrls: Set[PartnerUrl] = Set.empty,
      crmProviders: List[CrmProvider] = List.empty,
      networkCode: String = "UnitTestNetworkCode",
      smaStrategiesAndUnderliers: Set[SmaStrategyAndUnderliers] = Set.empty,
      smaRestrictedIssuers: Set[String] = Set.empty,
      contactInfo2: Option[List[ContactInfo2]] = None,
      contactInfo2Name: Option[String] = None,
      altCertificationRequirements: Option[Seq[CertificationAlternativesForProduct]] = None,
      historicHoldingsStartFrom: Option[String] = None,
      cobrandingCustomDisclosure: Option[String] = None,
      encryptionClientKey: Option[String] = None,
      landingPage: Option[LandingPage] = None,
      productTypeCertificationRequirements: Option[Seq[ProductCertificationRequirements]] = None,
      wlpUrl: Option[String] = None
  ): Network = Network(
    NetworkId(id),
    name,
    IdHubOrganization(11, "some org"),
    purviews,
    approverSet,
    ioiApproverSet,
    accountMappings,
    networkTypes,
    to,
    cc,
    purviewNetworks,
    salesFeeRuleIds,
    capabilities,
    payoffEntitlements,
    payoffEntitlementsV2,
    dynamicRoles,
    distributorAlias,
    omsAlias,
    version,
    customRolesConfig,
    maskedIds,
    booksCloseConfig,
    booksCloseCustomConfig,
    booksSendConfig,
    prospectusDeadlineConfig,
    dtccId,
    locations,
    entitlements,
    annuityEAppProvider,
    ssoPrefix,
    contactInfo,
    learnTracksV2,
    learnContent,
    endUserShareableContent,
    idpId,
    siCertificationRequirements = siCertificationRequirements,
    annuityCertificationRequirements = annuityCertificationRequirements,
    definedOutcomeETFCertificationRequirements = definedOutcomeETFCertificationRequirements,
    certificationProducts = certificationProducts,
    group = None,
    externalId = externalId,
    externalIds = externalIds,
    partnerUrls = partnerUrls,
    crmProviders = crmProviders,
    networkCode = networkCode,
    loginMode = LoginMode.SSO,
    embeddingInfo = None,
    eventInfo = com.simonmarkets.networks.common.EventInfo.Default,
    smaStrategiesAndUnderliers = smaStrategiesAndUnderliers,
    smaRestrictedIssuers = smaRestrictedIssuers,
    contactInfo2 = contactInfo2,
    contactInfo2Name = contactInfo2Name,
    altCertificationRequirements = altCertificationRequirements,
    historicHoldingsStartFrom = historicHoldingsStartFrom,
    cobrandingCustomDisclosure = cobrandingCustomDisclosure,
    encryptionClientKey = encryptionClientKey,
    landingPage = landingPage,
    //TODO: legacy fields will be deprecated so this will be removed - Jimmy
    productTypeCertificationRequirements = productTypeCertificationRequirements match {
      case Some(productTypeCertificationRequirements) => Some(productTypeCertificationRequirements)
      case None => Some(Seq(
        ProductCertificationRequirements(ProductType.StructuredProduct, siCertificationRequirements),
        ProductCertificationRequirements(ProductType.Annuities, annuityCertificationRequirements),
        ProductCertificationRequirements(ProductType.TargetReturnETF, definedOutcomeETFCertificationRequirements),
        ProductCertificationRequirements(ProductType.AlternativeInvestment, altCertificationRequirements.getOrElse(Seq.empty))
      ))
    },
    wlpUrl = wlpUrl
  )

  def create(count: Int, entitlements: Set[String] = Set("admin")): List[Network] = {
    (0 until count).toList.map(i => TestNetwork(
      id = i.toString,
      name = s"name_$i",
      entitlements = entitlements,
      externalId = Some(ExternalNetworkId(s"externalId$i")),
      externalIds = Set(ExternalId(id = "externalId_"+i, subject="test"))
    ))
  }
}
