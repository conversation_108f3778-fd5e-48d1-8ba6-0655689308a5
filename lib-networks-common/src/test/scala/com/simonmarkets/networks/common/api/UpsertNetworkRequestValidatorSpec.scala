package com.simonmarkets.networks.common.api

import com.goldmansachs.marquee.pipg.{CertificationAlternativesForProduct, CompensationType, CustomRoleDefinition, IdHubOrganization, LocationNode, NetworkLocation}
import com.simonmarkets.capabilities.Capabilities._
import com.simonmarkets.capabilities.HoldingsCapabilities.{ViewHoldingViaLocation, ViewHoldingViaNetwork}
import com.simonmarkets.http.HttpError
import com.simonmarkets.networks.NetworkIds.AdminNetworkId
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{Matchers, WordSpec}
import simon.Id.NetworkId

class UpsertNetworkRequestValidatorSpec extends WordSpec with Matchers {

  val basicRequest: UpsertNetworkRequest = UpsertNetworkRequest(networkName = "basicReq",
    idHubOrganization = IdHubOrganization(123, "456")
  )

  "UpsertNetworkRequestValidator" can {

    "validate" should {

      "not throw an exception" in {
        UpsertNetworkRequestValidator.validate(request = basicRequest, Set.empty)
      }

      "fail with an HttpError when given invalid fields" in {
        val learnContent = Seq("c1", "c2")
        val endUserShareableContent = Seq("c1", "c2", "c3")

        val req = basicRequest.copy(learnContent = learnContent, endUserShareableContent = endUserShareableContent)

        val res = UpsertNetworkRequestValidator.validate(req, Set.empty)
        ScalaFutures.whenReady(res.failed) { e =>
          e shouldBe a[HttpError]
          e.asInstanceOf[HttpError].getMessage() shouldBe
            """400 Bad Request {"messages":["EndUserShareableContent should be subset of learnContent"]}"""
        }
      }

      "fail with an HttpError using the first validation failure as the message" in {
        val badCertReqs = Seq(
          CertificationAlternativesForProduct(productId = "pt", productName = "A1", certificationAlternatives = Seq.empty),
          CertificationAlternativesForProduct(productId = "pt", productName = "A1", certificationAlternatives = Seq.empty),
        )
        val badlocations = Set(
          NetworkLocation("location1", None, Set("location2", "location3")),
          NetworkLocation("location2", None, Set.empty),
          NetworkLocation("location3", Some("location1"), Set.empty)
        )

        val req = basicRequest.copy(
          siCertificationRequirements = badCertReqs,
          locations = badlocations
        )
        val res = UpsertNetworkRequestValidator.validate(req, Set.empty)
        ScalaFutures.whenReady(res.failed) { e =>
          e shouldBe a[HttpError]
          e.asInstanceOf[HttpError].getMessage() shouldBe
            ("""400 Bad Request {"messages":["Payoff certification requirements contains multiple definitions for a single productId. """ +
              """Duplicated productId_compensationType_underlier: pt"]}""").stripMargin
        }
      }

    }

    "validate network name" should {
      "succeed if network name is nonempty" in {
        UpsertNetworkRequestValidator.validateNetworkName("erikNetwork") shouldBe Right(())
      }
      "fail if empty string" in {
        UpsertNetworkRequestValidator.validateNetworkName("") shouldBe Left("Network Name can't be empty")
        UpsertNetworkRequestValidator.validateNetworkName("      ") shouldBe Left("Network Name can't be empty")
      }
      "fail if null" in {
        UpsertNetworkRequestValidator.validateNetworkName(null) shouldBe Left("Network Name can't be empty")
      }
    }

    "validateMultiPayoffCertificationRequirementsList" should {

      "succeed if distinct pairs of product Id" in {
        val reqs = Seq(
          CertificationAlternativesForProduct("p1", productName = "pn1", certificationAlternatives = Seq.empty),
          CertificationAlternativesForProduct("p2", productName = "pn1", certificationAlternatives = Seq.empty)
        )
        UpsertNetworkRequestValidator.validateMultiPayoffCertificationRequirementsList(reqs) shouldBe Right(())
      }

      "succeed if distinct pairs of product Id and one Compensation Type" in {
        val reqs = Seq(
          CertificationAlternativesForProduct("p1", productName = "pn1", certificationAlternatives = Seq.empty),
          CertificationAlternativesForProduct("p1", productName = "pn1", certificationAlternatives = Seq.empty, Some(CompensationType.Fee))
        )
        UpsertNetworkRequestValidator.validateMultiPayoffCertificationRequirementsList(reqs) shouldBe Right(())
      }

      "succeed if distinct pairs of product Id and Compensation Type" in {
        val reqs = Seq(
          CertificationAlternativesForProduct("p1", productName = "pn1", certificationAlternatives = Seq.empty, Some(CompensationType.Commission)),
          CertificationAlternativesForProduct("p1", productName = "pn1", certificationAlternatives = Seq.empty, Some(CompensationType.Fee))
        )
        UpsertNetworkRequestValidator.validateMultiPayoffCertificationRequirementsList(reqs) shouldBe Right(())
      }

      "succeed if distinct pair of product Id, Compensation Type and Underliers" in {
        val reqs = Seq(
          CertificationAlternativesForProduct("p1", productName = "pn1", certificationAlternatives = Seq.empty, Some(CompensationType.Fee)),
          CertificationAlternativesForProduct("p1", productName = "pn1", certificationAlternatives = Seq.empty, Some(CompensationType.Fee),
            underliers = Some(Seq("un1"))),
          CertificationAlternativesForProduct("p1", productName = "pn1", certificationAlternatives = Seq.empty, Some(CompensationType.Fee),
            underliers = Some(Seq("un2", "un3")))
        )

        UpsertNetworkRequestValidator.validateMultiPayoffCertificationRequirementsList(reqs) shouldBe Right(())
      }

      val errorMessage: String = "Payoff certification requirements contains multiple definitions for a single productId. Duplicated productId_compensationType_underlier:"

      "fail if non-unique pairs of product Id found" in {
        val reqs = Seq(
          CertificationAlternativesForProduct("p1", productName = "pn1", certificationAlternatives = Seq.empty),
          CertificationAlternativesForProduct("p1", productName = "pn2", certificationAlternatives = Seq.empty)
        )

        UpsertNetworkRequestValidator.validateMultiPayoffCertificationRequirementsList(reqs) shouldBe Left(
          s"$errorMessage p1"
        )
      }

      "fail if non-unique underliers for the same product found" in {
        val reqs = Seq(
          CertificationAlternativesForProduct("p1", productName = "pn1", certificationAlternatives = Seq.empty,
            underliers = Some(Seq("a", "b"))),
          CertificationAlternativesForProduct("p1", productName = "pn1", certificationAlternatives = Seq.empty,
            underliers = Some(Seq("b", "a")))
        )

        UpsertNetworkRequestValidator.validateMultiPayoffCertificationRequirementsList(reqs) shouldBe Left(
          s"$errorMessage p1_b,p1_a")
      }

      "not fail if non-unique underliers for the same product but different compensation found" in {
        val reqs = Seq(
          CertificationAlternativesForProduct("p1", productName = "pn1", certificationAlternatives = Seq.empty,
            compensationType = Some(CompensationType.Fee), underliers = Some(Seq("a", "b"))),
          CertificationAlternativesForProduct("p1", productName = "pn1", certificationAlternatives = Seq.empty,
            compensationType = Some(CompensationType.Commission), underliers = Some(Seq("b", "a")))
        )

        UpsertNetworkRequestValidator.validateMultiPayoffCertificationRequirementsList(reqs) shouldBe Right(())
      }

      "fail if the same underlier used for same product" in {
        val reqs = Seq(
          CertificationAlternativesForProduct("p1", productName = "pn1", certificationAlternatives = Seq.empty,
            underliers = Some(Seq("a", "b"))),
          CertificationAlternativesForProduct("p1", productName = "pn1", certificationAlternatives = Seq.empty,
            underliers = Some(Seq("b", "c")))
        )

        UpsertNetworkRequestValidator.validateMultiPayoffCertificationRequirementsList(reqs) shouldBe Left(
          s"$errorMessage p1_b")
      }

      "fail if the same underlier used for same product+compensation type" in {
        val reqs = Seq(
          CertificationAlternativesForProduct("p1", productName = "pn1", certificationAlternatives = Seq.empty,
            compensationType = Some(CompensationType.Fee), underliers = Some(Seq("a", "b"))),
          CertificationAlternativesForProduct("p1", productName = "pn1", certificationAlternatives = Seq.empty,
            compensationType = Some(CompensationType.Fee), underliers = Some(Seq("b", "c")))
        )

        UpsertNetworkRequestValidator.validateMultiPayoffCertificationRequirementsList(reqs) shouldBe Left(
          s"$errorMessage p1_Fee_b")
      }
    }

    "validateOmsAlias" should {
      "succeed if valid oms alias" in {
        UpsertNetworkRequestValidator.validateOmsAlias(Some("twd")) shouldBe Right(Some("twd"))
      }

      "succeed if no OMS Alias provided" in {
        UpsertNetworkRequestValidator.validateOmsAlias(None) shouldBe Right(None)
      }

      "fail if invalid OMS Alias" in {
        UpsertNetworkRequestValidator.validateOmsAlias(Some("invalid")) shouldBe Left("unsupported oms alias: invalid, valid aliases: twd, firelight")
      }

    }

    "validateLocations" should {

      val location1 = "parent"
      val location2 = "branch1"
      val location3 = "branch2"

      "succeed if valid hierarchy" in {
        val locations = Set(
          NetworkLocation(location1, None, Set(location2, location3)),
          NetworkLocation(location2, Some(location1), Set.empty),
          NetworkLocation(location3, Some(location1), Set.empty)
        )
        val res = LocationNode(location1, Set(LocationNode(location2, Set()), LocationNode(location3, Set())))
        UpsertNetworkRequestValidator.validateLocations(locations) shouldBe Right(Some(res))
      }

      "fail if more than one root" in {
        val locations = Set(
          NetworkLocation(location1, None, Set(location2, location3)),
          NetworkLocation(location2, None, Set.empty),
          NetworkLocation(location3, Some(location1), Set.empty))
        UpsertNetworkRequestValidator.validateLocations(locations) shouldBe Left("Exactly one root location is required")
      }

      "fail if multiple definitions for the same location name are found" in {
        val locations = Set(
          NetworkLocation(location1, None, Set(location2, location3)),
          NetworkLocation(location2, Some(location1), Set(location3)),
          NetworkLocation(location2, Some(location1), Set.empty))
        UpsertNetworkRequestValidator.validateLocations(locations) shouldBe Left("Conflicting location definitions")
      }

      "fail if multiple locations claim to be parent for same child" in {
        val locations = Set(
          NetworkLocation(location1, None, Set(location2, location3)),
          NetworkLocation(location2, Some(location1), Set(location3)),
          NetworkLocation(location3, Some(location1), Set.empty))
        UpsertNetworkRequestValidator.validateLocations(locations) shouldBe Left("Location branch2 was already visited")

      }

      "fail if a claimed child is not defined" in {
        val locations = Set(
          NetworkLocation(location1, None, Set(location2, location3, "fakeBranch")),
          NetworkLocation(location2, Some(location1), Set.empty),
          NetworkLocation(location3, Some(location1), Set.empty))
        UpsertNetworkRequestValidator.validateLocations(locations) shouldBe Left("No location data presented for parent's child: fakeBranch")

      }

      "fail if a location's parent is not defined" in {
        val locations = Set(
          NetworkLocation(location1, None, Set(location2, location3)),
          NetworkLocation(location2, Some("fakeParent"), Set.empty),
          NetworkLocation(location3, Some(location1), Set.empty))
        UpsertNetworkRequestValidator.validateLocations(locations) shouldBe Left("Location branch1's parent fakeParent does not exist")

      }

      "fail if location claims wrong parent (but is still a child of another location)" in {
        val locations = Set(
          NetworkLocation(location1, None, Set(location2, location3)),
          NetworkLocation(location2, Some(location3), Set.empty),
          NetworkLocation(location3, Some(location1), Set.empty))
        UpsertNetworkRequestValidator.validateLocations(locations) shouldBe Left("Location branch1's parent branch2 does not exist")

      }

      "fail if location claims wrong parent (and is not a child of any location)" in {
        val locations = Set(
          NetworkLocation(location1, None, Set(location3)),
          NetworkLocation(location2, Some(location1), Set.empty),
          NetworkLocation(location3, Some(location1), Set.empty))
        UpsertNetworkRequestValidator.validateLocations(locations) shouldBe Left("Number of locations processed: 2 does not match number of inputs: 3")

      }

      "fail if there are cycles in the tree" in {
        val locations = Set(
          NetworkLocation(location1, None, Set(location2)),
          NetworkLocation(location2, Some(location1), Set(location3)),
          NetworkLocation(location3, Some(location2), Set(location1)))
        UpsertNetworkRequestValidator.validateLocations(locations) shouldBe Left("Location parent was already visited")

      }

      "fail for very deep trees" in {
        val root = NetworkLocation("0", None, Set("1"))
        val locations = Set(root) ++ List.range(1, 1000000).map { i =>
          NetworkLocation(i.toString, Some((i - 1).toString), Set((i + 1).toString))
        }.toSet
        UpsertNetworkRequestValidator.validateLocations(locations) shouldBe Left("Stack overflow - most likely due to tree depth")
      }
    }

    "validateCustomRolesConfig" should {
      "fail on duplicate role definitions" in {
        val roles = Set(
          CustomRoleDefinition("customRole", Set(ViewHoldingViaLocation, ViewHoldingViaNetwork)),
          CustomRoleDefinition("customRole", Set(ViewHoldingViaLocation))
        )

        UpsertNetworkRequestValidator.validateCustomRolesConfig(roles) shouldBe Left("Duplicate definitions for a custom role")
      }

      "fail if unknown unknown capabilities found" in {
        val roles = Set(
          CustomRoleDefinition("customRole1", Set(ViewHoldingViaLocation, ViewHoldingViaNetwork)),
          CustomRoleDefinition("customRole2", Set("NOTREAL"))
        )

        UpsertNetworkRequestValidator.validateCustomRolesConfig(roles) shouldBe Left("Unrecognized capability specified in customRolesConfig: Set(NOTREAL)")
      }

      "fail if role contains a space" in {
        val role = Set(CustomRoleDefinition("customRole", Set(ViewHoldingViaLocation, ViewHoldingViaNetwork)))
        val role1 = Set(CustomRoleDefinition("custom  Role1", Set(ViewHoldingViaLocation, ViewHoldingViaNetwork)))
        val role2 = Set(CustomRoleDefinition("customRole2 ", Set(ViewHoldingViaLocation, ViewHoldingViaNetwork)))
        val role3 = Set(CustomRoleDefinition(" customRole3", Set(ViewHoldingViaLocation, ViewHoldingViaNetwork)))

        UpsertNetworkRequestValidator.validateCustomRolesConfig(role1 ++ role) shouldBe Left("Custom role must not contain spaces")
        UpsertNetworkRequestValidator.validateCustomRolesConfig(role2 ++ role) shouldBe Left("Custom role must not contain spaces")
        UpsertNetworkRequestValidator.validateCustomRolesConfig(role3 ++ role) shouldBe Left("Custom role must not contain spaces")
      }

      "succeed when no duplicate roles defined" in {
        val roles = Set(
          CustomRoleDefinition("customRole1", Set(ViewHoldingViaNetwork)),
          CustomRoleDefinition("customRole2", Set(ViewHoldingViaLocation))
        )

        UpsertNetworkRequestValidator.validateCustomRolesConfig(roles) shouldBe Right(())
      }
    }

    "validateLearnContent" should {
      "succeed when end user shareable content is a subset of learn content" in {
        val endUserShareableContent = Seq("c1", "c2")
        val learnContent = Seq("c1", "c2", "c3")
        UpsertNetworkRequestValidator.validateLearnContent(endUserShareableContent, learnContent) shouldBe Right(())
      }

      "fail if user sharable content is not a subset of learn content" in {
        val learnContent = Seq("c1", "c2")
        val endUserShareableContent = Seq("c1", "c2", "c3")
        UpsertNetworkRequestValidator.validateLearnContent(endUserShareableContent, learnContent) shouldBe Left("EndUserShareableContent should be subset of learnContent")
      }
    }

    "validateAdminCapability" should {
      "succeed for roles in the admin network" in {
        val adminNetworkIdStr = NetworkId.unwrap(AdminNetworkId)
        val customRolesWithAdminCapability = Set(
          CustomRoleDefinition("customRole1", Set(Admin)),
          CustomRoleDefinition("customRole2", Set(ViewHoldingViaLocation))
        )

        UpsertNetworkRequestValidator.validateAdminCapability(Some(adminNetworkIdStr), customRolesWithAdminCapability) shouldBe Right(())
      }

      "fail on attempt to assign role with admin capability in a non-admin network" in {
        val networkIdStr = "NON-ADMIN-NETWORKID"
        val customRolesWithAdminCapability = Set(
          CustomRoleDefinition("customRole1", Set(Admin)),
          CustomRoleDefinition("customRole2", Set(ViewHoldingViaLocation))
        )

        UpsertNetworkRequestValidator.validateAdminCapability(Some(networkIdStr), customRolesWithAdminCapability) shouldBe Left("This network can not be assigned roles with admin capabilities")
      }

      "succeed for non-admin networks if the roles don't contain admin capability" in {
        val networkIdStr = "NON-ADMIN-NETWORKID"
        val customRoles = Set(
          CustomRoleDefinition("customRole1", Set(ViewHoldingViaNetwork)),
          CustomRoleDefinition("customRole2", Set(ViewHoldingViaLocation))
        )

        UpsertNetworkRequestValidator.validateAdminCapability(Some(networkIdStr), customRoles) shouldBe Right(())
      }
    }
  }
}
