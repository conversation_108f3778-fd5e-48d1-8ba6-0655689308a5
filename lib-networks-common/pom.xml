<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.simonmarkets</groupId>
        <artifactId>users-networks</artifactId>
        <version>sandbox-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>lib-networks-common</artifactId>
    <version>sandbox-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
        </dependency>

        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-users-common</artifactId>
            <version>sandbox-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-users-test</artifactId>
            <version>sandbox-SNAPSHOT</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-okta</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets.ui</groupId>
            <artifactId>lib-ui-assets</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-caching</artifactId>
        </dependency>

        <!--external-->
        <dependency>
            <groupId>dev.zio</groupId>
            <artifactId>zio_${scala.version.short}</artifactId>
        </dependency>
        <dependency>
            <groupId>io.scalac</groupId>
            <artifactId>zio-akka-http-interop_${scala.version.short}</artifactId>
        </dependency>
        <dependency>
            <groupId>dev.zio</groupId>
            <artifactId>zio-interop-reactivestreams_${scala.version.short}</artifactId>
            <version>1.3.8</version>
        </dependency>
        <dependency>
            <groupId>dev.zio</groupId>
            <artifactId>zio-logging_${scala.version.short}</artifactId>
            <version>0.5.13</version>
        </dependency>
        <dependency>
            <groupId>org.mongodb.scala</groupId>
            <artifactId>mongo-scala-driver_${scala.version.short}</artifactId>
        </dependency>
        <dependency>
            <groupId>io.circe</groupId>
            <artifactId>circe-generic-extras_${scala.version.short}</artifactId>
        </dependency>

        <!--test-->
        <dependency>
            <groupId>com.simonmarkets.utils</groupId>
            <artifactId>scala-mongo-embedded-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>2.28.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_${scala.version.short}</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.typesafe.akka</groupId>
            <artifactId>akka-testkit_${scala.version.short}</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.scalacheck</groupId>
            <artifactId>scalacheck_${scala.version.short}</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
                <configuration>
                    <args>
                        <arg>-deprecation</arg>
                        <arg>-feature</arg>
                        <arg>-language:experimental.macros</arg>
                        <arg>-language:higherKinds</arg>
                        <arg>-unchecked</arg>
<!--                        <arg>-Xfatal-warnings</arg>-->
                        <arg>-Xlint:adapted-args</arg>
                        <arg>-Xlint:by-name-right-associative</arg>
                        <arg>-Xlint:constant</arg>
                        <arg>-Xlint:delayedinit-select</arg>
                        <arg>-Xlint:doc-detached</arg>
                        <arg>-Xlint:inaccessible</arg>
                        <arg>-Xlint:infer-any</arg>
                        <arg>-Xlint:missing-interpolator</arg>
                        <arg>-Xlint:nullary-override</arg>
                        <arg>-Xlint:nullary-unit</arg>
                        <arg>-Xlint:option-implicit</arg>
                        <arg>-Xlint:package-object-classes</arg>
                        <arg>-Xlint:poly-implicit-overload</arg>
                        <arg>-Xlint:private-shadow</arg>
                        <arg>-Xlint:stars-align</arg>
                        <arg>-Xlint:type-parameter-shadow</arg>
                        <arg>-Xlint:unsound-match</arg>
                        <arg>-Yno-adapted-args</arg>
                        <arg>-Ypartial-unification</arg>
                        <arg>-Ywarn-dead-code</arg>
                        <arg>-Ywarn-extra-implicit</arg>
                        <arg>-Ywarn-inaccessible</arg>
                        <arg>-Ywarn-infer-any</arg>
                        <arg>-Ywarn-nullary-override</arg>
                        <arg>-Ywarn-nullary-unit</arg>
                        <arg>-Ywarn-unused:implicits</arg>
                        <arg>-Ywarn-unused:imports</arg>
                        <arg>-Ywarn-unused:locals</arg>
                        <arg>-Ywarn-unused:params</arg>
                        <arg>-Ywarn-unused:patvars</arg>
                        <arg>-Ywarn-unused:privates</arg>
                    </args>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
                <configuration>
                    <parallel>false</parallel>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
