package com.simonmarkets.oktaevents.service

import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.oktaevents.entitlements.OktaEventsAcceptedAccessKeyGenerator._
import com.simonmarkets.oktaevents.model._
import com.simonmarkets.oktaevents.repository.{OktaEventsRepositoryMongo, codecRegistry}
import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.logging.TraceId
import com.simonmarkets.oktaevents.model.OktaEvent
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}
import simon.Id.NetworkId

import java.time.Instant

import scala.concurrent.Await
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration._

class OktaEventsServiceSpec extends WordSpec with EmbeddedMongoLike with MockitoSugar with Matchers {

  private val awaitDuration = 60.seconds
  private val traceId = TraceId("any")

  private lazy val collection = db.getCollection[OktaEvent]("oktaevents").withCodecRegistry(codecRegistry)
  private lazy val repo = new OktaEventsRepositoryMongo(collection)
  private lazy val service = new OktaEventsServiceImpl(repo)

  private val admin = TestUserACL("admin-user", NetworkId("SIMON Admin"), capabilities = Set(Capabilities.Admin))

  private val id1 = "id1"

  private def insertData(): OktaEvent = {
    val eventTime = Instant.now()
    val publishedTime = Instant.now()
    val oktaEventData = OktaEventData(Seq(
      OktaLogEvent(
        "123",
        publishedTime,
        "user.session.start",
        OktaActor("userId1", "User", Some("<EMAIL>")),
        Some(OktaLogEventOutcome("SUCCESS", None)),
        Some(
          OktaLogEventClient(
            Some(OktaLogEventUserAgent(Some("Chrome/100.0.4896.127 Safari/537.36"), Some("Mac OS X"), Some("CHROME"))),
            Some("null"),
            Some("Computer"),
            None,
            Some(OktaLogEventGeographicalContext(Some("New York"), Some("New York"), Some("United States"), Some("10018")))
          )
        )
      )
    ))
    val insertRequest = UpsertOktaEventRequest(eventId = id1, oktaEventData, eventTime)

    val template = insertRequest.toOktaEventWithoutAccessKeys
    val oktaEvent = template.copy(acceptedAccessKeys = getAcceptedAccessKeys(template))
    Await.result(repo.upsert(oktaEvent, Set("admin"))(traceId), awaitDuration)
  }

  "OktaEventService" can {
    "get" should {
      "return expected data for admin" in {
        insertData()
        val res = Await.result(service.get(Some("user.session.start"), None, None, None)(traceId, admin), awaitDuration)

        assert(res.length == 1)
        assert(res.head.id == id1)
      }
    }

    "upsert" should {
      "insert expected data for admin" in {
        val eventTime = Instant.now()
        val publishedTime = Instant.now()
        val oktaEventData = OktaEventData(Seq(
          OktaLogEvent(
            "123",
            publishedTime,
            "user.session.start",
            OktaActor("userId1", "User", Some("<EMAIL>")),
            Some(OktaLogEventOutcome("SUCCESS", None)),
            Some(
              OktaLogEventClient(
                Some(OktaLogEventUserAgent(Some("Chrome/100.0.4896.127 Safari/537.36"), Some("Mac OS X"), Some("CHROME"))),
                Some("null"),
                Some("Computer"),
                None,
                Some(OktaLogEventGeographicalContext(Some("New York"), Some("New York"), Some("United States"), Some("10018")))
              )
            )
          )
        ))
        val request = UpsertOktaEventRequest(id1, oktaEventData, eventTime)
        val res = Await.result(service.upsert(request)(traceId), awaitDuration)
        val expected = OktaEventView(request.toOktaEventWithoutAccessKeys)

        assert(res == expected)
      }
    }

    "delete" should {
      "remove expected data for admin" in {
        insertData()
        val request = DeleteOktaEventRequest(id1)
        val res = Await.result(service.delete(request)(traceId, admin), awaitDuration)

        assert(res)
      }
    }
  }

}
