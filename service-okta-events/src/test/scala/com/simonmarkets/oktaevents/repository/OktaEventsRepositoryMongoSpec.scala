package com.simonmarkets.oktaevents.repository

import com.simonmarkets.oktaevents.model.{OktaActor, OktaEvent, OktaEventData, OktaLogEvent, OktaLogEventClient, OktaLogEventGeographicalContext, OktaLogEventOutcome, OktaLogEventUserAgent}
import com.simonmarkets.logging.TraceId
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, Matchers}

import scala.concurrent.Await
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration._
import java.time.Instant
import java.time.temporal.ChronoUnit

class OktaEventsRepositoryMongoSpec extends AsyncWordSpec with EmbeddedMongoLike with Matchers with BeforeAndAfterEach {
  private val awaitDuration = 60.seconds

  private lazy val collection = db.getCollection[OktaEvent]("oktaEvents").withCodecRegistry(codecRegistry)
  private lazy val repo = new OktaEventsRepositoryMongo(collection)(global)

  private val traceID = TraceId("traceId")

  private val adminAvailableAccessKeys = Set("admin")

  private val eventTime = Instant.now().truncatedTo(ChronoUnit.MILLIS)
  private val publishedTime = Instant.now().truncatedTo(ChronoUnit.MILLIS)

  private val mockOktaEventSessionStart1 = OktaLogEvent(
    "123",
    publishedTime,
    "user.session.start",
    OktaActor("userId1", "User", Some("<EMAIL>")),
    Some(OktaLogEventOutcome("SUCCESS", None)),
    Some(
      OktaLogEventClient(
        Some(OktaLogEventUserAgent(Some("Chrome/100.0.4896.127 Safari/537.36"), Some("Mac OS X"), Some("CHROME"))),
        Some("null"),
        Some("Computer"),
        None,
        Some(OktaLogEventGeographicalContext(Some("New York"), Some("New York"), Some("United States"), Some("10018")))
      )
    )
  )

  private val mockOktaEventSessionStart2 = mockOktaEventSessionStart1.copy(uuid = "456", actor = OktaActor("userId2", "User", Some("<EMAIL>")))

  private val mockOktaEventSessionEnd = mockOktaEventSessionStart1.copy(uuid = "789", eventType = "user.session.end")
  private val mockOktaEventSeq1 = OktaEventData(Seq(mockOktaEventSessionStart1, mockOktaEventSessionStart2))
  private val mockOktaEventSeq2 = OktaEventData(Seq(mockOktaEventSessionEnd))


  private val id1 = "id1"
  private val oktaEvent1 = OktaEvent(id1, mockOktaEventSeq1, eventTime, adminAvailableAccessKeys)

  private val id2 = "id2"
  private val oktaEvent2 = OktaEvent(id2, mockOktaEventSeq2, eventTime, adminAvailableAccessKeys)

  private val id3 = "id3"

  private val sessionStartEventRes = Seq(
    OktaEvent(id1, OktaEventData(Seq(mockOktaEventSessionStart1)), eventTime, adminAvailableAccessKeys),
    OktaEvent(id1, OktaEventData(Seq(mockOktaEventSessionStart2)), eventTime, adminAvailableAccessKeys),
  )

  private val userId1EventsRes = Seq(
    OktaEvent(id1, OktaEventData(Seq(mockOktaEventSessionStart1)), eventTime, adminAvailableAccessKeys),
    OktaEvent(id2, OktaEventData(Seq(mockOktaEventSessionEnd)), eventTime, adminAvailableAccessKeys),
  )

  private val userId1SessionStartEventsRes = Seq(
    OktaEvent(id1, OktaEventData(Seq(mockOktaEventSessionStart1)), eventTime, adminAvailableAccessKeys),
  )



  override def beforeEach(): Unit = {
    super.beforeEach()
    Await.result(collection.drop().toFuture(), awaitDuration)
    Await.result(collection.insertMany(Seq(oktaEvent1, oktaEvent2)).toFuture, awaitDuration)
  }

  "OktaEventRepositoryMongo" can {
    "get" should {
      "find docs by eventType" in {
        val res = Await.result(repo.get(Some("user.session.start"),None, None, None, adminAvailableAccessKeys)(traceID), awaitDuration)
        res shouldBe sessionStartEventRes
      }

      "find docs by userId" in {
        val res = Await.result(repo.get(None,Some("userId1"), None, None, adminAvailableAccessKeys)(traceID), awaitDuration)
        res shouldBe userId1EventsRes
      }

      "find docs by userId and eventType" in {
        val res = Await.result(repo.get(Some("user.session.start"),Some("userId1"), None, None, adminAvailableAccessKeys)(traceID), awaitDuration)
        res shouldBe userId1SessionStartEventsRes
      }
    }

    "upsert" should {
      "insert docs" in {
        Await.result(repo.upsert(oktaEvent2, adminAvailableAccessKeys)(traceID), awaitDuration)
        val res = Await.result(repo.get(Some("user.session.end"),None, None, None, adminAvailableAccessKeys)(traceID), awaitDuration)
        res shouldBe Seq(oktaEvent2)
      }

      "delete" should {
        "remove doc by id if it exists" in {
          val result = Await.result(repo.delete(id1, adminAvailableAccessKeys)(traceID), awaitDuration)
          assert(result)
        }

        "not remove doc if it doesn't exists" in {
          val result = Await.result(repo.delete(id3, adminAvailableAccessKeys)(traceID), awaitDuration)
          assert(!result)
        }
      }
    }
  }
}
