package com.simonmarkets.oktaevents.api

import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.model.StatusCodes
import akka.http.scaladsl.server.Route
import akka.http.scaladsl.testkit.ScalatestRouteTest
import com.goldmansachs.marquee.pipg.UserACL
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.logging.TraceId
import com.simonmarkets.oktaevents.model._
import com.simonmarkets.oktaevents.service.OktaEventsService
import com.simonmarkets.utils.akkahttp.DirectivesWithCirce
import com.simonmarkets.utils.akkahttp.requests.LoggedAuthorizedDirectives
import org.mockito.ArgumentMatchers.{any, eq => exact}
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}
import simon.Id.NetworkId

import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.UUID

import scala.concurrent.Future

class OktaEventsRoutesSpec extends WordSpec with Matchers with ScalatestRouteTest with MockitoSugar with DirectivesWithCirce {

  private val userId = "userId"
  private val networkId = NetworkId("networkId")
  private val userAcl = TestUserACL(userId, networkId)

  private val authorizedDirectives = mock[LoggedAuthorizedDirectives[UserACL]]
  when(authorizedDirectives.authorized(any[String])).thenReturn(tprovide((TraceId.randomize, userAcl)))
  when(authorizedDirectives.unauthenticated).thenReturn(provide(TraceId.randomize))

  def oktaEventsRoutes(service: OktaEventsService): Route = new OktaEventsRoutes(service, authorizedDirectives).routes

  private val publishedTime = Instant.now().truncatedTo(ChronoUnit.MILLIS)
  private val mockOktaEventSessionStart1 = OktaLogEvent(
    "123",
    publishedTime,
    "user.session.start",
    OktaActor("userId1", "User", Some("<EMAIL>")),
    Some(OktaLogEventOutcome("SUCCESS", None)),
    Some(
      OktaLogEventClient(
        Some(OktaLogEventUserAgent(Some("Chrome/100.0.4896.127 Safari/537.36"), Some("Mac OS X"), Some("CHROME"))),
        Some("null"),
        Some("Computer"),
        None,
        Some(OktaLogEventGeographicalContext(Some("New York"), Some("New York"), Some("United States"), Some("10018")))
      )
    )
  )

  private val mockOktaEventSessionStart2 = mockOktaEventSessionStart1.copy(uuid = "456", actor = OktaActor("userId2", "User", Some("<EMAIL>")))

  private val mockOktaEventSessionEnd = mockOktaEventSessionStart1.copy(uuid = "789", eventType = "user.session.end")
  private val mockOktaEventSeq1 = OktaEventData(Seq(mockOktaEventSessionStart1, mockOktaEventSessionStart2))
  private val mockOktaEventSeq2 = OktaEventData(Seq(mockOktaEventSessionEnd))
  private val allEventsResponse = Seq(OktaEventView("id1", mockOktaEventSeq1), OktaEventView("id2", mockOktaEventSeq2))
  private val sessionStartEventResponse = Seq(OktaEventView("id1", mockOktaEventSeq1))
  private val sessionEndEventsResponse = Seq(OktaEventView("id1", mockOktaEventSeq2))
  private val userId1EventsResponse = Seq(OktaEventView("id1", OktaEventData(Seq(mockOktaEventSessionStart1))), OktaEventView("id2", OktaEventData(Seq(mockOktaEventSessionEnd))))
  private val userId1SessionStartEventsResponse = Seq(OktaEventView("id1", OktaEventData(Seq(mockOktaEventSessionStart1))))

  private val service: OktaEventsService = mock[OktaEventsService]


  private val routes = oktaEventsRoutes(service)
  "OktaEventRoutes" should {

    "handle GET request with no query params" in {

      when(service.get(exact(None), exact(None),exact(None), exact(None))(any[TraceId], exact(userAcl))).thenReturn(Future.successful(allEventsResponse))
      Get("/simon/api/v1/oktaevents") ~> routes ~> check {
        status shouldBe StatusCodes.OK
        entityAs[Seq[OktaEventView]] shouldBe allEventsResponse
      }
    }

    "handle GET request with query params" should {

      "eventType == user.session.start events" in {
        when(service.get(exact(Some("user.session.start")), exact(None),exact(None), exact(None))(any[TraceId], exact(userAcl))).thenReturn(Future.successful(sessionStartEventResponse))
        Get("/simon/api/v1/oktaevents?eventType=user.session.start") ~> routes ~> check {
          status shouldBe StatusCodes.OK
          entityAs[Seq[OktaEventView]] shouldBe sessionStartEventResponse
        }
      }

      "eventType == user.session.end events" in {
        when(service.get(exact(Some("user.session.end")), exact(None),exact(None), exact(None))(any[TraceId], exact(userAcl))).thenReturn(Future.successful(sessionEndEventsResponse))
        Get("/simon/api/v1/oktaevents?eventType=user.session.end") ~> routes ~> check {
          status shouldBe StatusCodes.OK
          entityAs[Seq[OktaEventView]] shouldBe sessionEndEventsResponse
        }
      }

      "userId == userId1" in {
        when(service.get(exact(None), exact(Some("userId1")),exact(None), exact(None))(any[TraceId], exact(userAcl))).thenReturn(Future.successful(userId1EventsResponse))
        Get("/simon/api/v1/oktaevents?userId=userId1") ~> routes ~> check {
          status shouldBe StatusCodes.OK
          entityAs[Seq[OktaEventView]] shouldBe userId1EventsResponse
        }
      }

      "userId == userId1 and eventType == user.session.start" in {
        when(service.get(exact(Some("user.session.start")), exact(Some("userId1")),exact(None), exact(None))(any[TraceId], exact(userAcl))).thenReturn(Future.successful(userId1SessionStartEventsResponse))
        Get("/simon/api/v1/oktaevents?userId=userId1&eventType=user.session.start") ~> routes ~> check {
          status shouldBe StatusCodes.OK
          entityAs[Seq[OktaEventView]] shouldBe userId1SessionStartEventsResponse
        }
      }
    }


    "Okta Event Verification and Event Hook" should {
      "handle one time verification" in {
        val verificationField = "test-verify"
        when(service.verifyWithOkta(exact(verificationField))(any[TraceId])).thenReturn(OktaEventEndpointVerification(verificationField))
        Get("/simon/api/v1/oktaevents/sign-in").addHeader(RawHeader("x-okta-verification-challenge", verificationField)) ~> routes ~> check {
          status shouldBe StatusCodes.OK
          entityAs[OktaEventEndpointVerification] shouldBe OktaEventEndpointVerification(verificationField)
        }
      }

      "handle event post" in {
        val upsertOktaEventRequest = UpsertOktaEventRequest(eventId = UUID.randomUUID().toString, data = mockOktaEventSeq1, eventTime = Instant.now())
        val upsertResponseView = OktaEventView(upsertOktaEventRequest.eventId, mockOktaEventSeq1)
        when(service.upsert(exact(upsertOktaEventRequest))(any[TraceId])).thenReturn(Future.successful(upsertResponseView))
        Post("/simon/api/v1/oktaevents/sign-in", upsertOktaEventRequest) ~> routes ~> check {
          status shouldBe StatusCodes.NoContent
        }
      }
    }
  }

}
