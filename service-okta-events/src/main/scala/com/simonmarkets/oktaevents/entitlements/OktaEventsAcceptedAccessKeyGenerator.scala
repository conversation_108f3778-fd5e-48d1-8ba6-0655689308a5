package com.simonmarkets.oktaevents.entitlements

import com.simonmarkets.oktaevents.model.OktaEvent
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.entitlements.{AcceptedAccessKeysGenerator, AcceptedKeyBuilder}

object OktaEventsAcceptedAccessKeyGenerator extends AcceptedAccessKeysGenerator[OktaEvent] {
  override def capabilityToAcceptedKeyBuilders = Map(
    Admin -> AcceptedKeyBuilder(buildAdminKeys)
  )
}
