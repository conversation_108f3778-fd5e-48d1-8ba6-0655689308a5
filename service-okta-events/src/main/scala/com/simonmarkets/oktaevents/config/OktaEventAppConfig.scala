package com.simonmarkets.oktaevents.config

import com.simonmarkets.api.users.config.AclClientConfig
import com.simonmarkets.http.HttpClientConfig
import com.simonmarkets.mongodb.config.MongoClientConfig
import com.simonmarkets.utils.akkahttp.AuthnSupplier.AuthenticationConfiguration
import com.simonmarkets.utils.akkahttp.awslambda.RunMode
import com.simonmarkets.utils.akkahttp.framework.config.{ServiceAppConfiguration, SystemRoutesConfig}

final case class OktaEventAppConfig(
    mongoDB: MongoDBConfig,
    aclServerPath: String,
    httpClientConfig: HttpClientConfig,
    authentication: Option[AuthenticationConfiguration],
    aclClientConfig: AclClientConfig,
    runMode: RunMode,
    systemRoutes: Option[SystemRoutesConfig]
) extends ServiceAppConfiguration

final case class MongoDBConfig(
    client: MongoClientConfig,
    oktaeventsCollectionName: String,
    databaseName: String)
