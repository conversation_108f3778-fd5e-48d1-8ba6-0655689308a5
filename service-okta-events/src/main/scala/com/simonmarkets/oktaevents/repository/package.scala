package com.simonmarkets.oktaevents

import com.simonmarkets.oktaevents.model._
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros._

package object repository {

  val codecRegistry: CodecRegistry =
    fromRegistries(
      fromProviders(
        classOf[OktaActor],
        classOf[OktaLogEventOutcome],
        classOf[OktaLogEventClient],
        classOf[OktaLogEventUserAgent],
        classOf[OktaLogEventGeographicalContext],
        classOf[OktaLogEvent],
        classOf[OktaEventData],
        classOf[OktaEventDataResponse],
        classOf[OktaEventResponse],
        classOf[OktaEvent]
      ),
      DEFAULT_CODEC_REGISTRY
    )

}
