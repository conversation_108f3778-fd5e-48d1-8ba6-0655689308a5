package com.simonmarkets.oktaevents.repository

import com.simonmarkets.logging.TraceId
import com.simonmarkets.oktaevents.model.{OktaEvent, OktaEventData, OktaEventResponse}
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.bson.conversions.Bson
import org.mongodb.scala.model.Aggregates.{`match`, unwind}
import org.mongodb.scala.model.Filters.{and, equal, in}
import org.mongodb.scala.model.{Aggregates, ReplaceOptions}

import scala.concurrent.{ExecutionContext, Future}

trait OktaEventsRepository {
  def get(eventType: Option[String], userId: Option[String], limit: Option[Int], offset: Option[Int], availableAccessKeys: Set[String])(implicit traceId: TraceId): Future[Seq[OktaEvent]]

  def upsert(oktaEvent: OktaEvent, availableAccessKeys: Set[String])(implicit traceId: TraceId): Future[OktaEvent]

  def delete(id: String, availableAccessKeys: Set[String])(implicit traceId: TraceId): Future[Boolean]
}

class OktaEventsRepositoryMongo(collection: MongoCollection[OktaEvent])
  (implicit executionContext: ExecutionContext) extends OktaEventsRepository {

  private def accessKeysCondition(availableAccessKeys: Set[String]): Bson =
    in("acceptedAccessKeys", availableAccessKeys.toSeq: _*)

  override def get(eventType: Option[String], userId: Option[String], limit: Option[Int], offset: Option[Int], availableAccessKeys: Set[String])
    (implicit traceId: TraceId): Future[Seq[OktaEvent]] = {
    
    val stagesWithoutUnwind =
        Seq(Aggregates.`match`(accessKeysCondition(availableAccessKeys))) ++
      limit.map(lim => Seq(Aggregates.limit(lim))).getOrElse(Seq(Aggregates.limit(50))) ++
        offset.map(off => Seq(Aggregates.skip(off))).getOrElse(Seq(Aggregates.skip(0)))

    //
    val nestedArrayFieldsMap:Map[String, String] =
      Map(
      "data.events.eventType" -> eventType,
      "data.events.actor.id" -> userId
      ).collect{ case (key, Some(value)) => key -> value}

    //unwind events array and match on it's fields
    val stagesWithEventsArrayUnwind = buildQueryWithMatchFields(Seq(unwind("$data.events")),nestedArrayFieldsMap,  stagesWithoutUnwind)

    if (nestedArrayFieldsMap.nonEmpty) {
      collection.aggregate[OktaEventResponse](stagesWithEventsArrayUnwind)
        .map(oktaEventRes => OktaEvent(oktaEventRes.eventId, OktaEventData(Seq(oktaEventRes.data.events)),oktaEventRes.eventTime, oktaEventRes.acceptedAccessKeys))
        .toFuture()
    } else {
      collection.aggregate(stagesWithoutUnwind).toFuture()
    }
  }

  override def upsert(oktaEvent: OktaEvent, availableAccessKeys: Set[String])
    (implicit traceId: TraceId): Future[OktaEvent] = {
    collection.replaceOne(
      and(
        equal("eventId", oktaEvent.eventId),
        accessKeysCondition(availableAccessKeys)
      ),
      oktaEvent,
      new ReplaceOptions().upsert(true)
    ).toFuture().map(_ => oktaEvent)
  }

  override def delete(id: String, availableAccessKeys: Set[String])
    (implicit traceId: TraceId): Future[Boolean] = {
    collection.deleteMany(
      and(
        equal("eventId", id),
        accessKeysCondition(availableAccessKeys)
      )).toFuture.map { result =>
      result.getDeletedCount > 0
    }
  }

  private def buildQueryWithMatchFields(baseBsonSeq: Seq[Bson], fieldNameAndValue: Map[String, String],additionalStages: Seq[Bson]): Seq[Bson] = {
    val newBsonSeq = baseBsonSeq ++ fieldNameAndValue.flatMap{ case (fieldName, fieldValue) => Seq(`match`(equal(fieldName, fieldValue)))}.toSeq ++ additionalStages
    newBsonSeq
  }
}
