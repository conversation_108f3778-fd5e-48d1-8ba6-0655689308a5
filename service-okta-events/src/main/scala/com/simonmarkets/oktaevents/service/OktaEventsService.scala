package com.simonmarkets.oktaevents.service

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.oktaevents.model._
import com.simonmarkets.oktaevents.repository.OktaEventsRepository
import com.simonmarkets.capabilities.OktaEventsCapabilities.{EditCapabilities, ViewCapabilities}
import com.simonmarkets.capabilities.OktaEventsCapabilities.availableAccessKeysGen
import com.simonmarkets.oktaevents.entitlements.OktaEventsAcceptedAccessKeyGenerator._
import com.simonmarkets.logging.{TraceId, TraceLogging}

import scala.concurrent.{ExecutionContext, Future}

trait OktaEventsService {
  def get(eventType: Option[String], userId: Option[String], limit: Option[Int], offset: Option[Int])(implicit traceId: TraceId, user: UserACL): Future[Seq[OktaEventView]]

  def upsert(req: UpsertOktaEventRequest)(implicit traceId: TraceId): Future[OktaEventView]

  def delete(req: DeleteOktaEventRequest)(implicit traceId: TraceId, user: UserACL): Future[Boolean]

  def verifyWithOkta(verificationChallengeVal:String)(implicit traceId: TraceId): OktaEventEndpointVerification
}

class OktaEventsServiceImpl(oktaEventsRepository: OktaEventsRepository)
  (implicit executionContext: ExecutionContext) extends OktaEventsService with TraceLogging {

  override def get(eventType: Option[String], userId: Option[String], limit: Option[Int], offset: Option[Int])
    (implicit traceId: TraceId,
        user: UserACL): Future[Seq[OktaEventView]] = {

    val availableAccessKeys = availableAccessKeysGen.getAvailableAccessKeysForCapabilities(ViewCapabilities, user)
    oktaEventsRepository.get(eventType, userId, limit, offset, availableAccessKeys)
      .map(oktaEvents => oktaEvents.map(OktaEventView(_)))
  }

  override def upsert(req: UpsertOktaEventRequest)
    (implicit traceId: TraceId): Future[OktaEventView] = {

    val oktaEventTemplate = req.toOktaEventWithoutAccessKeys
    val availableAccessKeys = Set(Admin)
    val acceptedAccessKeys = getAcceptedAccessKeys(oktaEventTemplate)
    val oktaEvent = oktaEventTemplate.copy(acceptedAccessKeys = acceptedAccessKeys)
    oktaEventsRepository.upsert(oktaEvent, availableAccessKeys).map(OktaEventView(_))
  }

  override def delete(req: DeleteOktaEventRequest)
    (implicit traceId: TraceId, user: UserACL): Future[Boolean] = {

    val availableAccessKeys = availableAccessKeysGen.getAvailableAccessKeysForCapabilities(EditCapabilities, user)
    oktaEventsRepository.delete(req.id, availableAccessKeys)
  }

  override def verifyWithOkta(verificationChallengeVal: String)
    (implicit traceId: TraceId): OktaEventEndpointVerification = {
    OktaEventEndpointVerification(verificationChallengeVal)
  }
}

