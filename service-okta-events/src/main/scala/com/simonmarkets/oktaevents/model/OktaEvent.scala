package com.simonmarkets.oktaevents.model

import io.simon.openapi.annotation.Field.{Nullable, Ref}
import io.simon.openapi.definitions.CommonDefinitions

import java.time.Instant

//Fundamental class of this service
final case class OktaEvent(
    eventId: String,
    data: OktaEventData,
    @Ref(CommonDefinitions.DateOrTimestamp)
    eventTime: Instant,
    acceptedAccessKeys: Set[String]
)

final case class OktaEventResponse(
    eventId: String,
    data: OktaEventDataResponse,
    @Ref(CommonDefinitions.DateOrTimestamp)
    eventTime: Instant,
    acceptedAccessKeys: Set[String]
)

final case class OktaEventData(events:Seq[OktaLogEvent])

final case class OktaEventDataResponse(events:OktaLogEvent)

final case class OktaLogEvent(
    uuid: String,
    @Ref(CommonDefinitions.DateOrTimestamp)
    published: Instant,
    eventType: String,
    actor: <PERSON>ta<PERSON><PERSON>,
    outcome: Option[OktaLogEventOutcome],
    client: Option[OktaLogEventClient]
)

final case class OktaLogEventOutcome(
    result: String,
    @Nullable
    reason: Option[String])

final case class OktaActor(
    id: String, //for user would be their user id
    `type`: String,
    alternateId: Option[String], //user email if present
)

final case class OktaLogEventClient(
    userAgent: Option[OktaLogEventUserAgent],
    zone: Option[String],
    device: Option[String],
    @Nullable
    id: Option[String],
    geographicalContext: Option[OktaLogEventGeographicalContext]
)
final case class OktaLogEventUserAgent(rawUserAgent: Option[String], os: Option[String], browser: Option[String])

final case class OktaLogEventGeographicalContext(
    city: Option[String],
    state: Option[String],
    country: Option[String],
    postalCode: Option[String]
)


//Fundamental external view of this service's data
final case class OktaEventView(id: String, data: OktaEventData)


//one-time verification for okta event endpoint
final case class OktaEventEndpointVerification(verification: String)

object OktaEventView {
  def apply(OktaEvent: OktaEvent): OktaEventView = OktaEventView(OktaEvent.eventId, OktaEvent.data)
}

