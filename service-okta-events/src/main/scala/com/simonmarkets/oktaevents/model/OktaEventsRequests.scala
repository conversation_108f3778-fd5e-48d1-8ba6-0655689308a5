package com.simonmarkets.oktaevents.model

import io.simon.openapi.annotation.Field.Ref
import io.simon.openapi.definitions.CommonDefinitions

import java.time.Instant

final case class UpsertOktaEventRequest(
    eventId: String,
    data: OktaEventData,
    @Ref(CommonDefinitions.DateOrTimestamp)
    eventTime: Instant
)
{

  def toOktaEventWithoutAccessKeys: OktaEvent =
    OktaEvent(eventId, data, eventTime, Set.empty[String])

}

final case class DeleteOktaEventRequest(id: String)
