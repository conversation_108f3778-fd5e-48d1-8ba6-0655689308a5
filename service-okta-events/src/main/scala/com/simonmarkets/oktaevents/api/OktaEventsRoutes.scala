package com.simonmarkets.oktaevents.api

import akka.http.scaladsl.model.StatusCodes
import akka.http.scaladsl.server.{RequestContext, Route, RouteResult}
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.oktaevents.model._
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.oktaevents.service.OktaEventsService
import com.simonmarkets.utils.akkahttp.DirectivesWithCirce
import com.simonmarkets.utils.akkahttp.requests.LoggedAuthorizedDirectives

import scala.concurrent.{ExecutionContext, Future}

class OktaEventsRoutes(
    oktaEventsService: OktaEventsService,
    authorizedDirectives: LoggedAuthorizedDirectives[UserACL])
  (implicit ec: ExecutionContext) extends DirectivesWithCirce with TraceLogging {

  import authorizedDirectives._

  def routes: Route = pathPrefix("simon" / "api" / "v1" / "oktaevents")(routesInner)

  private def routesInner: Route = {
    unauthenticated { tid =>
      implicit val traceId: TraceId = tid
      oktaVerificationAndEventHookPostRoutes("sign-in") ~
      oktaVerificationAndEventHookPostRoutes("sign-out")
    } ~ authorized() { (_traceId, _userACL) =>
      implicit val (traceId, userACL) = (_traceId, _userACL)
      pathEnd {
        get {
          parameters('eventType.as[String].?, 'userId.as[String].?,'limit.as[Int].?, 'offset.as[Int].?) {
            (eventType, userId, limit, offset)  =>
              complete(oktaEventsService.get(eventType, userId, limit, offset))
          }
        }
      } ~
        path(Segment) { id =>
            delete {
              complete {
                val request = DeleteOktaEventRequest(id)
                oktaEventsService.delete(request).map { result =>
                  if (result) StatusCodes.NoContent else StatusCodes.NotFound
                }
              }
            }
        }
    }
  }

  private def oktaVerificationAndEventHookPostRoutes(routeName: String)(implicit traceId: TraceId): RequestContext => Future[RouteResult]  = {
    (path(routeName) & pathEndOrSingleSlash) {
      get { //path for okta to use for one-time verification
        headerValueByName("x-okta-verification-challenge") { verificationVal =>
          complete(oktaEventsService.verifyWithOkta(verificationVal))
        }
      }
    } ~
    (path(routeName) & pathEndOrSingleSlash) {
      post {
        entity(as[UpsertOktaEventRequest]) {
          request => complete(StatusCodes.NoContent, oktaEventsService.upsert(request))
        }
      }
    }
  }
}

