package com.simonmarkets.oktaevents

import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.mongodb.Client
import com.simonmarkets.useracl.UserAclLoggedAuthorizedDirective
import com.simonmarkets.utils.akkahttp.awslambda.AkkaHttpLambdaHandler
import com.simonmarkets.utils.akkahttp.framework.{Environment, ServiceApp}
import com.simonmarkets.oktaevents.api.OktaEventsRoutes
import com.simonmarkets.oktaevents.config.OktaEventAppConfig
import com.simonmarkets.oktaevents.model.OktaEvent
import com.simonmarkets.oktaevents.repository.{OktaEventsRepositoryMongo, codecRegistry}
import com.simonmarkets.oktaevents.service.OktaEventsServiceImpl
import io.simon.openapi.generator.OpenApiGenerator
import org.mongodb.scala.{MongoCollection, MongoDatabase}
import pureconfig.generic.auto._

object OktaEventsApp extends ServiceApp[OktaEventAppConfig]("OktaEventApp") with App with TraceLogging {

  OpenApiGenerator.generateOpenApiDocumentation()

  startServer()

  class EntryPoint extends AkkaHttpLambdaHandler(constructRoutes)

  override def init(environment: Environment): Unit = {

    val authorizedDirectives = UserAclLoggedAuthorizedDirective(config.aclClientConfig, config.authentication)

    val mongoDB: MongoDatabase =
      Client.create(config.mongoDB.client)
        .getDatabase(config.mongoDB.databaseName)
        .withCodecRegistry(codecRegistry)
    val oktaEventCollection: MongoCollection[OktaEvent] = mongoDB.getCollection[OktaEvent](config.mongoDB.oktaeventsCollectionName)

    val oktaEventsRepository = new OktaEventsRepositoryMongo(oktaEventCollection)
    val oktaEventsService = new OktaEventsServiceImpl(oktaEventsRepository)
    val routes = new OktaEventsRoutes(oktaEventsService, authorizedDirectives).routes

    environment.addRoutes(routes)
  }
}
