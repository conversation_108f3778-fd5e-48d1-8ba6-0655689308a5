include "akka-http-version"

run-mode {
  type = server-mode
  http-server-config {
    port = 8086
    interface = "0.0.0.0"
    ssl {
      enabled = false # enable if you want to test woth https but don't forget to chanage keystore file
      keystore {
        type = "JKS"
        file = "D:/Users/<USER>/src/Projects/simon/apps/services/app-config/config/keystore.jks" #TODO: Set correct file path
        password = "test123"
      }
    }
  }
}

blocking-io-dispatcher {
  type = Dispatcher
  executor = "thread-pool-executor"
  thread-pool-executor {
    fixed-pool-size = 32
  }
  throughput = 1
}

config.resolvers {
  sm {
    region = "us-east-1"
  }
  file {
    root = "app-config/dev"
  }
}

mongo-db {
  database-name = "pipg"
  oktaevents-collection-name = "oktaevents"
  client {
    app-name = "oktaevents"
    connection {
      url = "mongodb://localhost:27017/?readPreference=primary&appname=MongoDB%20Compass%20Community&ssl=false"
      authentication {
        type = none
      }
    }
  }
}

acl-server-path = "https://origin-a.dev.simonmarkets.com/simon/api"

acl-client-config {
  http-client {
    auth {
      type: OAuth
      client-id: "sm:oktaevents-client-id"
      client-secret: "sm:oktaevents-client-secret"
      site = "sm:applicationconfig-issuer-uri"
      authorization-path: ""
      token-path = "/v1/token"
      scope: "read_product_data"
      token-type: {
        type: Cookie
        name: SimSSO
      }
    }
  }
  base-url = "https://origin-a.dev.simonmarkets.com/simon/api"
  cache-config {
    enabled = true
    config {
      service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
      signin-region = "us-east-1"
    }
  }
}

http-client-config {
  auth {
    type: OAuth
    client-id: "sm:oktaevents-client-id"
    client-secret: "sm:oktaevents-client-secret"
    site = "sm:applicationconfig-issuer-uri"
    authorization-path: ""
    token-path = "/v1/token"
    scope: "read_product_data"
    token-type = {
      type = BearerToken
    }
  }
}

authentication {
  type = "jwt"
  jwt {
    source {
      type = "cookieOrHeader" # header / bearer
      name = "SimSSO:X-Simon-AccessToken"
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      keys-url = "sm:applicationconfig-okta-validator"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clock-offset-in-seconds = 0
    }
  }

  user-info {
    source {
      type = header-source
      name = "x-simon-accesstoken"
    }
  }
}

system-routes {
  service-up {
    path = "simon/api/v1/oktaevents/uptime"
  }
  service-info {
    path = "simon/api/v1/oktaevents/info"
  }
  health-check {
    path = "simon/api/v1/oktaevents/healthcheck"
  }
}
