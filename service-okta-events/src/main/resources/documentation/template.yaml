openapi: 3.0.0
info:
  title: OktaEvent Service
  version: 1.0.0
x-basepath: /simon/api/v1/oktaevents
x-kong-service-defaults:
  read_timeout: 300000
x-kong-plugin-aws-lambda:
  config:
    function_name: service-okta-events-${STAGE}-api
    aws_region: us-east-1
    invocation_type: RequestResponse
    awsgateway_compatible: true
    forward_request_body: true
    forward_request_headers: true
    forward_request_method: true
    forward_request_uri: true
    is_proxy_integration: true
    skip_large_bodies: false
    proxy_url: ${HTTP_PROXY}
    proxy_scheme: https
    log_type: None
tags:
  - name: oktaEvents
    description: oktaEvents

paths:
  "":
    get:
      description: Get OktaEvent(s)
      parameters:
        - name: eventType
          in: query
          description: Type of Okta Event
          required: false
          schema:
            type: string
      responses:
        200:
          description: OktaEvent response
          content:
            application/json:
              schema:
                type: object
  /sign-in:
    x-kong-route-defaults:
      headers:
        'X-Simon-Okta-Event-${STAGE}-Token':
          - ${ENHANCE_ACCESS_TOKEN_INLINE_HOOK_SECRET}
    get:
      x-disabled-plugins:
        - openId
      description: One Time Verification for Okta
      responses:
        200:
          description: Verification
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.oktaevents.model.OktaEventEndpointVerification}
    post:
      x-disabled-plugins:
        - openId
        - request-validator
      description: Store Sign-In OktaEvent
      requestBody:
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.oktaevents.model.UpsertOktaEventRequest}
      responses:
        200:
          description: OktaEvent created
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.oktaevents.model.OktaEventView}
  /sign-out:
    x-kong-route-defaults:
      headers:
        'X-Simon-Okta-Event-${STAGE}-Token':
          - ${ENHANCE_ACCESS_TOKEN_INLINE_HOOK_SECRET}
    get:
      x-disabled-plugins:
        - openId
      description: One Time Verification for Okta
      responses:
        200:
          description: Verification
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.oktaevents.model.OktaEventEndpointVerification}
    post:
      x-disabled-plugins:
        - openId
        - request-validator
      description: Store Sign-Out OktaEvent
      requestBody:
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.oktaevents.model.UpsertOktaEventRequest}
      responses:
        200:
          description: OktaEvent created
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.oktaevents.model.OktaEventView}
  /{id}:
    delete:
      description: Deletes OktaEvent by id
      parameters:
        - name: id
          in: path
          description: Id of the OktaEvent to delete
          required: true
          schema:
            type: string
      responses:
        204:
          description: OktaEvent successfully deleted
        404:
          description: OktaEvent not found
