include "akka-http-version"
include classpath("common.conf")

run-mode {
  type = lambda-mode
}

blocking-io-dispatcher {
  type = Dispatcher
  executor = "thread-pool-executor"
  thread-pool-executor {
    fixed-pool-size = 32
  }
  throughput = 1
}

acl-server-path = "https://origin-a.dev.simonmarkets.com/simon/api"

acl-client-config {
  http-client {
    auth {
      type: OAuth
      client-id: "sm:oktaevents-client-id"
      client-secret: "sm:oktaevents-client-secret"
      site = "sm:applicationconfig-issuer-uri"
      authorization-path: ""
      token-path = "/v1/token"
      scope: "read_product_data"
      token-type: {
        type: Cookie
        name: SimSS<PERSON>
      }
    }
  }
  base-url = "https://origin-a.dev.simonmarkets.com/simon/api"
  cache-config {
    enabled = true
    config {
      service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
      signin-region = "us-east-1"
    }
  }
}

http-client-config {
  auth {
    type: OAuth
    client-id: "sm:oktaevents-client-id"
    client-secret: "sm:oktaevents-client-secret"
    site = "sm:applicationconfig-issuer-uri"
    authorization-path: ""
    token-path = "/v1/token"
    scope: "read_product_data"
    token-type = {
      type = BearerToken
    }
  }
}

authentication {
  type = "jwt"
  jwt {
    source {
      type = "cookieOrHeader" # header / bearer
      name = "SimSSO:X-Simon-AccessToken"
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      keys-url = "sm:applicationconfig-okta-validator"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clock-offset-in-seconds = 0
    }
  }

  user-info {
    source {
      type = header-source
      name = "x-simon-accesstoken"
    }
  }
}

system-routes {
  service-up {
    path = "simon/api/v1/oktaevents/uptime"
  }
  service-info {
    path = "simon/api/v1/oktaevents/info"
  }
  health-check {
    path = "simon/api/v1/oktaevents/healthcheck"
  }
}
