service: service-user-sync-mapping-validator

provider:
  name: aws
  runtime: java11
  region: us-east-1
  profile: ${self:custom.file.provider.profile}
  iam:
    role: ${self:custom.file.provider.iam.role}
  stage: ${opt:stage, 'alpha'}
  versionFunctions: false
  tracing:
    lambda: true
  vpc:
    securityGroupIds: ${self:custom.file.provider.securityGroupIds}
    subnetIds: ${self:custom.file.provider.subnetIds}
  deploymentBucket:
    name: simon-${self:provider.profile}-serverless
  logRetentionInDays: ${self:custom.file.logRetentionInDays}

custom:
  file: ${file(../config/${opt:stage}.yml)}
  logSubscription: ${self:custom.file.logSubscription}
  newRelic:
    javaNewRelicHandler: handleStreamsRequest

plugins:
  - serverless-plugin-log-subscription
  - serverless-plugin-datadog
  - serverless-prune-plugin
package:
  artifact: target/service-user-sync-mapping-validator-uber.jar

functions:
  api:
    handler: com.simonmarkets.usersyncmappingvalidator.UserSyncMappingValidatorApp::findAllMissingMappings
    reservedConcurrency: 1
    timeout: 900
    memorySize: 1024
#    currently disabled as not used
#    events:
#      - schedule: 'cron(0 23 ? * Mon-Fri *)' # Once a day, Mon-Fri (UTC 23, EST 7pm)
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=${opt:stage}.conf ${self:custom.file.proxySystemProperty, ''}
