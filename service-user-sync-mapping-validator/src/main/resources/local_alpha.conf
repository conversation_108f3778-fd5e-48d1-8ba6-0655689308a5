include classpath("common.conf")

simon-base-path = "https://origin-a.dev.simonmarkets.com/simon/api"

icn-base-path = "https://icn-int-1.stg.icapitalnetwork.com/api"

simon-http-client {
  auth {
    type = OAuth
    client-id = "0oaevl54gplnFvyx70h7"
    client-secret-file = ""
    client-secret = "sm:Okta-secret"
    site = "sm:applicationconfig-issuer-uri"
    authorization-path = ""
    token-path = "/v1/token"
    scope = "read_product_data"
    token-type {
      type = Cookie
      name = SimSSO
    }
  }
}

enabled = false
