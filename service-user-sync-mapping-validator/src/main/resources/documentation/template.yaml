openapi: 3.0.0
info:
  title: User Sync Mapping Validator Service
  version: 1.0.0
x-basepath: /simon/api/v1/mapping-validator/mapping-validators
x-kong-service-defaults:
  read_timeout: 300000
tags:
  - name: service-user-sync-mapping-validator
    description: User Sync Mapping Validator Service
x-kong-plugin-aws-lambda:
  config:
    function_name: service-user-sync-mapping-validator-${STAGE}-api
    aws_region: us-east-1
    invocation_type: RequestResponse
    awsgateway_compatible: true
    forward_request_body: true
    forward_request_headers: true
    forward_request_method: true
    forward_request_uri: true
    is_proxy_integration: true
    skip_large_bodies: false
    proxy_url: ${HTTP_PROXY}
    proxy_scheme: https
    log_type: None
paths:
  /warmup:
    post:
      x-scopes: [ ]
      summary: Trigger service warmup logic
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.resteasy.awslambda.events.WarmupEvent}
      responses:
        200:
          description: Success
  /info:
    get:
      x-scopes:
        - admin
        - simon-system-user
      summary: Retrieve service info
      responses:
        200:
          description: Success
  /healthcheck:
    get:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      summary: Retrieve service healthcheck
      responses:
        200:
          description: Success
  /uptime:
    get:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      summary: Retrieve service uptime
      responses:
        200:
          description: Success
