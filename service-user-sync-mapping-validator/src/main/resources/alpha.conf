include classpath("common.conf")

simon-base-path = "https://origin-a.dev.simonmarkets.com/simon/api"

icn-base-path = "https://icn-int-1.stg.icapitalnetwork.com/api"

icn-http-client-config.proxy {
  port = 3128
  address = "internal-alpha-squid-proxy-elb-1264059358.us-east-1.elb.amazonaws.com"
}

simon-http-client {
  auth {
    type = OAuth
    client-id = "0oaevl54gplnFvyx70h7"
    client-secret-file = ""
    client-secret = "sm:Okta-secret"
    site = "sm:applicationconfig-issuer-uri"
    authorization-path = ""
    token-path = "/v1/token"
    scope = "read_product_data"
    token-type {
      type = Cookie
      name = SimSSO
    }
  }
  proxy {
    port = 3128
    address = "internal-alpha-squid-proxy-elb-1264059358.us-east-1.elb.amazonaws.com"
  }
}

enabled = false
