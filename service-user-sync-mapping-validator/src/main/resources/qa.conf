include classpath("common.conf")

simon-base-path = "https://origin-dc1.api.qa.simonmarkets.com/simon/api"

icn-base-path = "https://s.staging.icapitalnetwork.com/api"

icn-http-client-config.proxy {
  address = "internal-qa-squid-elb-1135562419.us-east-1.elb.amazonaws.com"
  port = 3128
}

simon-http-client {
  auth {
    type = OAuth
    client-id = "0oaevl54gplnFvyx70h7"
    client-secret-file = ""
    client-secret = "sm:applicationconfig-oktaclientsecret"
    site = "sm:applicationconfig-issuer-uri"
    authorization-path = ""
    token-path = "/v1/token"
    scope = "read_product_data"
    token-type {
      type = Cookie
      name = SimSSO
    }
  },
  proxy {
    address = "internal-qa-squid-elb-1135562419.us-east-1.elb.amazonaws.com"
    port = 3128
  }
}
