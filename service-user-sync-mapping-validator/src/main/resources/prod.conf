include classpath("common.conf")

simon-base-path = "https://origin-dc1.api.simonmarkets.com/simon/api"

icn-base-path = "https://s.icapitalnetwork.com/api"

icn-http-client-config.proxy {
  address = "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
  port = 3128
}

simon-http-client {
  auth {
    type = OAuth
    client-id = "0oa2h4re4ym0Fh3s82p7"
    client-secret-file = ""
    client-secret = "sm:applicationconfig-oktaclientsecret"
    site = "sm:applicationconfig-issuer-uri"
    authorization-path = ""
    token-path = "/v1/token"
    scope = "read_product_data"
    token-type {
      type = Cookie
      name = SimSSO
    }
  }
  proxy {
    port: 3128
    address: "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
  }
}
