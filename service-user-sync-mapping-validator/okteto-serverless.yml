service: service-user-sync-mapping-validator

provider:
  name: aws
  runtime: java11
  region: us-east-1
  profile: ${self:custom.file.provider.profile}
  stage: ${self:custom.file.provider.environment}
  versionFunctions: false
  deploymentBucket:
    name: simon-dev-local-serverless

custom:
  localstack:
    stages:
      - dev-local
    host: http://localstack-svc.${env:OKTETO_NAMESPACE}
    edgePort: 4566
    debug: true
  file: ${file(../config/okteto.yml)}
  newRelic:
    javaNewRelicHandler: handleStreamsRequest

plugins:
  - serverless-localstack
  - serverless-deployment-bucket

package:
  artifact: target/service-user-sync-mapping-validator-uber.jar

functions:
  api:
    handler: com.simonmarkets.usersyncmappingvalidator.UserSyncMappingValidatorApp::findAllMissingMappings
    reservedConcurrency: 1
    timeout: 900
    # memorySize: 1024
#    currently disabled as not used
#    events:
#      - schedule: 'cron(0 23 ? * Mon-Fri *)' # Once a day, <PERSON>-<PERSON><PERSON> (UTC 23, EST 7pm)
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=okteto.conf
