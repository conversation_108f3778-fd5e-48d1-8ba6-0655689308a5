<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="ServiceUserAcl" type="Application" factoryName="Application">
    <envs>
      <env name="aws_profile" value="alpha" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.simonmarkets.useracl.UserAclApp" />
    <module name="service-user-acl" />
    <option name="VM_PARAMETERS" value="-Daws.profile=alpha-elevated -Dconfig.file=service-user-acl/src/main/resources/local.conf -Dconfig.trace=loads" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.simonmarkets.networks.common.clients.usersync.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <extension name="software.aws.toolkits.jetbrains.core.execution.JavaAwsConnectionExtension">
      <option name="credential" />
      <option name="region" />
      <option name="useCurrentConnection" value="false" />
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>