<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="UsersAggregateApp" type="Application" factoryName="Application">
    <option name="ALTERNATIVE_JRE_PATH" value="11" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <option name="MAIN_CLASS_NAME" value="com.simonmarkets.users.UsersAggregateApp" />
    <module name="service-users" />
    <option name="VM_PARAMETERS" value="-Daws.profile=alpha-elevated -Dconfig.file=service-users/src/main/resources/local.conf" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.simonmarkets.networks.common.clients.usersync.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <extension name="software.aws.toolkits.jetbrains.core.execution.JavaAwsConnectionExtension">
      <option name="credential" value="profile:alpha-elevated" />
      <option name="region" value="us-east-1" />
      <option name="useCurrentConnection" value="false" />
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>