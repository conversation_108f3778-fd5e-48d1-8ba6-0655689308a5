<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="RunLocal" type="Application" factoryName="Application">
    <option name="MAIN_CLASS_NAME" value="com.simonmarkets.usersnetworks.db.RunLocal" />
    <module name="db-migration" />
    <option name="VM_PARAMETERS" value="-Dconfig.file=db-migration/src/main/resources/local.conf" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.simonmarkets.usersnetworks.db.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>