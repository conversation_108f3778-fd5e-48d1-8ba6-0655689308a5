<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="RunUserSyncMappingValidatorLocal" type="Application" factoryName="Application">
    <option name="MAIN_CLASS_NAME" value="com.simonmarkets.usersyncmappingvalidator.RunUserSyncMappingValidatorLocal" />
    <module name="service-user-sync-mapping-validator" />
    <option name="VM_PARAMETERS" value="-Daws.profile=qa-read-only -Dconfig.file=service-user-sync-mapping-validator/src/main/resources/local_qa.conf" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.simonmarkets.usersyncmappingvalidator.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>