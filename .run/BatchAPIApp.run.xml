<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="BatchAPIApp" type="Application" factoryName="Application">
    <option name="MAIN_CLASS_NAME" value="com.simonmarkets.tasks.BatchAPIApp" />
    <module name="service-tasks" />
    <option name="VM_PARAMETERS" value="-Daws.profile=alpha -Dconfig.file=service-tasks/src/main/resources/local.conf" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.simonmarkets.tasks.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>