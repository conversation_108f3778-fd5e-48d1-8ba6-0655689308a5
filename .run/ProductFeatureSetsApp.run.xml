<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="ProductFeatureSetsApp" type="Application" factoryName="Application">
    <option name="MAIN_CLASS_NAME" value="com.simonmarkets.productfeaturesets.ProductFeatureSetsApp" />
    <module name="service-product-feature-sets" />
    <option name="VM_PARAMETERS" value="-Dconfig.file=service-product-feature-sets/src/main/resources/local.conf -Daws.profile=alpha-admin" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.simonmarkets.productfeaturesets.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>