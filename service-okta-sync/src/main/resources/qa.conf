config.resolvers {
  sm {
    region = "us-east-1"
  }
  file {
    root = ""
  }
}

client-config {
  okta-org-url = "https://auth.int.simonmarkets.com"
  okta-auth-token = "sm:applicationconfig-okta-api-token"
  cache-ttl = 30.seconds
  proxy {
    port = 3128
    host = "internal-qa-squid-elb-1135562419.us-east-1.elb.amazonaws.com"
  }
}

service-config {
  create {
    activate = true,
    setPassword = true,
    expirePassword = false
  }

  profile {
    network-name = "network"
    skip-secondary-emails = ["@simonmarkets.com"]
    secondary-email = "<EMAIL>"
  }
}