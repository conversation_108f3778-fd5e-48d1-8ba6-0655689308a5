config.resolvers {
  sm {
    region = "us-east-1"
  }
  file {
    root = ""
  }
}

client-config {
  okta-org-url = "https://auth.dev.simonmarkets.com"
  okta-auth-token = "sm:applicationconfig-okta-api-token"
  cache-ttl = 30.seconds
  proxy {
      port = 3128
      host = "internal-alpha-squid-proxy-elb-1264059358.us-east-1.elb.amazonaws.com"
  }
}

service-config {
  create {
    activate = true,
    setPassword = true,
    expirePassword = false
  }

  profile {
    network-name = "alphaNetwork"
    skip-secondary-emails = ["@simonmarkets.com"]
    secondary-email = "<EMAIL>"
  }
}