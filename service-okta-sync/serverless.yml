service: service-okta-sync

provider:
  stage: ${opt:stage, 'sandbox'}
  name: aws
  runtime: java11
  region: us-east-1
  profile: ${self:custom.file.provider.profile}
  iam:
    role: ${self:custom.file.provider.iam.role}
  versionFunctions: false
  tracing:
    lambda: true
  vpc:
    securityGroupIds: ${self:custom.file.provider.securityGroupIds}
    subnetIds: ${self:custom.file.provider.subnetIds}
  deploymentBucket:
    name: simon-${self:provider.profile}-serverless
  logRetentionInDays: ${self:custom.file.logRetentionInDays}

custom:
  file: ${file(../config/${opt:stage}.yml)}
  logSubscription: ${self:custom.file.logSubscription}
  warmup:
    default:
      enabled: true
      concurrency: 2
      prewarm: true
      timeout: 60
      events:
        - schedule: rate(5 minutes)
      vpc: false
      role: ${self:provider.iam.role}
  newRelic:
    javaNewRelicHandler: handleStreamsRequest

plugins:
    - serverless-plugin-log-subscription

package:
  artifact: target/service-okta-sync-uber.jar

functions:
  profile-sync:
    handler: com.simonmarkets.oktasync.HandlerImpl::handleRequest
    memorySize: 1024
    timeout: 900
    reservedConcurrency: 1
    environment:
      JAVA_TOOL_OPTIONS:
        -Dconfig.file=${opt:stage}.conf ${self:custom.file.proxySystemProperty, ''}
        -Dlogback.configurationFile=logback.xml
    events:
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusExternalIdType}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"