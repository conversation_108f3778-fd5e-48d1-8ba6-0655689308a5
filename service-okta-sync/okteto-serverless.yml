service: service-okta-sync

provider:
  stage: ${self:custom.file.provider.environment}
  name: aws
  runtime: java11
  region: us-east-1
  profile: ${self:custom.file.provider.profile}
  versionFunctions: false
  deploymentBucket:
    name: simon-dev-local-serverless

custom:
  localstack:
    stages:
      - dev-local
    host: http://localstack-svc.${env:OKTETO_NAMESPACE}
    edgePort: 4566
    debug: true
  file: ${file(../config/okteto.yml)}
  warmup:
    default:
      enabled: true
      concurrency: 1
      prewarm: true
      timeout: 300
      events:
        - schedule: rate(5 minutes)
      vpc: false
  newRelic:
    javaNewRelicHandler: handleStreamsRequest

plugins:
  - serverless-localstack
  - serverless-deployment-bucket


package:
  artifact: target/service-okta-sync-uber.jar

functions:
  profile-sync:
    handler: com.simonmarkets.oktasync.HandlerImpl::handleRequest
    # memorySize: 1024
    timeout: 900
    reservedConcurrency: 1
    environment:
      JAVA_TOOL_OPTIONS:
        -Dconfig.file=okteto.conf
        -Dlogback.configurationFile=logback.xml
    events:
      - eventBridge:
          eventBus: ${self:custom.file.mongoEventBusExternalIdType}
          pattern:
            account:
              - ${self:custom.file.provider.account}
            source:
              - prefix: "aws.partner/mongodb.com/stitch.trigger/"
