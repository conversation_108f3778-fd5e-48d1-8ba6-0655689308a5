proxySystemProperty: "-Dhttps.proxyHost=internal-uat-squid-elb-*********.us-east-1.elb.amazonaws.com -Dhttps.proxyPort=3128"

provider:
  profile: uat-us
  securityGroupIds:
    - "sg-0b2a25c207a92749a"
  subnetIds:
    - "subnet-0b18b1137bf303e7b"
    - "subnet-08712f05d5de769ca"
    - "subnet-003f49f1265c45a96"
  iam:
    role: arn:aws:iam::************:role/user-networks-service-uat-role
  account: "************"

mongoEventBusUsers: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/5f6c9a06a0e37ed987434fc9"
mongoEventBusNetworks: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/5f6c99b874ea39958ddd3da4"

mongoEventBusGeneralRejectionReasons: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/5f18b99a67efdca2a01ff5b1"

mongoEventBusExternalIdType: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/62194dc5fe67f0b369f8a091"

dbUsers: "pipg"
collectionUsers: "users"

dbNetworks: "pipg"
collectionNetworks: "networks_new"

dbGeneralRejectionReasons: "pipg"
collectionGeneralRejectionReasons: "generalRejectionReasons"

userAclConcurrency: 50

# Don't edit this, it's provided by DevOps team
logRetentionInDays: 90
logSubscription:
  - enabled: true
    destinationArn: "arn:aws:firehose:us-east-1:************:deliverystream/cwl-splunk-firehose-stream-uat"
    roleArn: "arn:aws:iam::************:role/cwl-cloudwatch_logs_role"

icnFeatures:
  datadog: false
