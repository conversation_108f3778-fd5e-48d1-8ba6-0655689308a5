provider:
  profile: default
  environment: dev-local
  account: "************"

mongoEventBusUsers: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/mongoEventBusUsers"
mongoEventBusNetworks: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/mongoEventBusNetworks"

mongoEventBusGeneralRejectionReasons: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/mongoEventBusGeneralRejectionReasons"

mongoEventBusExternalIdType: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/mongoEventBusExternalIdType"

dbUsers: "pipg"
collectionUsers: "users"

dbNetworks: "pipg"
collectionNetworks: "networks_new"

dbGeneralRejectionReasons: "pipg"
collectionGeneralRejectionReasons: "generalRejectionReasons"

userAclConcurrency: 5

icnFeatures:
  datadog: false
