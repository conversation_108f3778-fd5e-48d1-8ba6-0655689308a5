proxySystemProperty: "-Dhttps.proxyHost=internal-qa-squid-elb-**********.us-east-1.elb.amazonaws.com -Dhttps.proxyPort=3128"

provider:
  profile: qa
  securityGroupIds:
    - "sg-3549b641"
  subnetIds:
    - "subnet-e61597c9"
    - "subnet-8d9016d0"
    - "subnet-e240a3ed"
  iam:
    role: arn:aws:iam::************:role/user-networks-service-qa-role
  account: "************"

mongoEventBusUsers: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/5f6c9a06a0e37ed987434fc9"
mongoEventBusNetworks: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/5f6c99b874ea39958ddd3da4"

mongoEventBusGeneralRejectionReasons: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/5f18b99a67efdca2a01ff5b1"

mongoEventBusExternalIdType: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/62194dc5fe67f0b369f8a091"

dbUsers: "pipg"
collectionUsers: "users"

dbNetworks: "pipg"
collectionNetworks: "networks_new"

dbGeneralRejectionReasons: "pipg"
collectionGeneralRejectionReasons: "generalRejectionReasons"

userAclConcurrency: 50

# Don't edit this, it's provided by DevOps team
logRetentionInDays: 90
logSubscription:
  - enabled: true
    destinationArn: "arn:aws:firehose:us-east-1:************:deliverystream/cwl-splunk-firehose-stream-qa"
    roleArn: "arn:aws:iam::************:role/cwl-cloudwatch_logs_role"

icnFeatures:
  datadog: false
