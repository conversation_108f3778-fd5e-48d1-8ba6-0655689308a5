proxySystemProperty: "-Dhttps.proxyHost=internal-prod-squid-elb-*********.us-east-1.elb.amazonaws.com -Dhttps.proxyPort=3128"

provider:
  profile: prod
  securityGroupIds:
    - "sg-a56b1fd3"
  subnetIds:
    - "subnet-61ba8d4e"
    - "subnet-********"
    - "subnet-4066c14f"
  iam:
    role: arn:aws:iam::************:role/user-networks-service-prod-role
  account: "************"

mongoEventBusUsers: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/5f7468ce69f456bf3a19dd2b"
mongoEventBusNetworks: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/5f74682b89f714135f4016fb"

mongoEventBusGeneralRejectionReasons: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/5f222d441d090070d1ddd05d"

mongoEventBusExternalIdType: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/621d0ac22bc235cce9be3b81"

dbUsers: "pipg"
collectionUsers: "users"

dbNetworks: "pipg"
collectionNetworks: "networks_new"

dbGeneralRejectionReasons: "pipg"
collectionGeneralRejectionReasons: "generalRejectionReasons"

userAclConcurrency: 75

# Don't edit this, it's provided by DevOps team
logRetentionInDays: 365
logSubscription:
  - enabled: true
    destinationArn: "arn:aws:firehose:us-east-1:************:deliverystream/cwl-splunk-firehose-stream-prd"
    roleArn: "arn:aws:iam::************:role/cwl-cloudwatch_logs_role"

icnFeatures:
  datadog: false
