proxySystemProperty: "-Dhttps.proxyHost=internal-alpha-squid-proxy-elb-**********.us-east-1.elb.amazonaws.com -Dhttps.proxyPort=3128"

provider:
  profile: alpha
  securityGroupIds:
    - "sg-04a9f78088f2c60b3"
  subnetIds:
    - "subnet-092aa15246e226be2"
    - "subnet-0978a24297da4c96e"
    - "subnet-0c85b6c43be91a809"
  iam:
    role: arn:aws:iam::************:role/user-networks-service-alpha-role
  account: "************"

mongoEventBusUsers: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/5f64cceca0e37ed98708124d"
mongoEventBusNetworks: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/5f64cd6074ea39958da2715f"

mongoEventBusGeneralRejectionReasons: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/5efef951e65a9d40bafd1697"

mongoEventBusExternalIdType: "arn:aws:events:us-east-1:************:event-bus/aws.partner/mongodb.com/stitch.trigger/61f1d431a37cb8585dcb6ef5"

dbUsers: "pipg"
collectionUsers: "users"

dbNetworks: "pipg"
collectionNetworks: "networks_new"

dbGeneralRejectionReasons: "pipg"
collectionGeneralRejectionReasons: "generalRejectionReasons"

userAclConcurrency: 25

# Don't edit this, it's provided by DevOps team
logRetentionInDays: 60
logSubscription:
  - enabled: true
    destinationArn: "arn:aws:firehose:us-east-1:************:deliverystream/cwl-splunk-firehose-stream"
    roleArn: "arn:aws:iam::************:role/cwl-cloudwatch_logs_role"

icnFeatures:
  datadog: false
