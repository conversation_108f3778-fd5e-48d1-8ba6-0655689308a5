package com.simonmarkets.sales.fee.rules

import akka.http.scaladsl.model.StatusCodes
import com.goldmansachs.marquee.pipg.UserACL
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.ChangelogItem
import com.simonmarkets.networks.common.repository.MongoNetworkRepository
import com.simonmarkets.sales.fee.rules.common.api.{CreateFeeRuleRequest, UpdateFeeRuleRequest}
import com.simonmarkets.sales.fee.rules.common.repository.SalesFeeRuleRepository
import com.simonmarkets.sales.fee.rules.common.{FeeSchedule, FeeType, SalesFeeRule}
import com.simonmarkets.sales.fee.rules.service.SalesFeeRulesService
import org.mockito.{ArgumentMatchers => MM}
import org.mockito.Mockito.{reset, when}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{AsyncWordSpec, BeforeAndAfterAll, BeforeAndAfterEach, Matchers}
import simon.Id.NetworkId

import java.time.LocalDateTime

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}

class SalesFeeRulesServiceSpec extends AsyncWordSpec
  with BeforeAndAfterAll with MockitoSugar with Matchers with BeforeAndAfterEach{

  implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  implicit val traceId: TraceId = TraceId("SalesFeeRulesServiceSpec")

  val userId = "userTestId"
  val netId = "netTestId"
  val snapId = "snapId"

  val adminRequesterAcl: UserACL = TestUserACL(userId, NetworkId(netId), capabilities = Set("admin"))
  val salesFeeRule1: SalesFeeRule = SalesFeeRule("ruleId", 0, "testRuleId", FeeType.HomeOffice,
    deleted = false, issuerKeys = List("GSBank"),
    contractTypes = List("GS"),
    contractTypeWrappers = List("Note"),
    fees = FeeSchedule(240 -> 2.0),
    callable = None,
    fullyProtected = None,
    hasSinglePayment = None,
    nonCallPeriodInMonths = None)
  val salesFeeRule2: SalesFeeRule = SalesFeeRule("ruleId2", 1, "testRuleId", FeeType.HomeOffice,
    deleted = false,
    issuerKeys = List("GSBank"),
    contractTypes = List("GS"),
    contractTypeWrappers = List("Note"),
    fees = FeeSchedule(240 -> 2.0),
    callable = None,
    fullyProtected = None,
    hasSinglePayment = None,
    nonCallPeriodInMonths = None)
  val salesFeeRule3: SalesFeeRule = SalesFeeRule("ruleId3", 0, "testRuleId3", FeeType.HomeOffice,
    deleted = false,
    issuerKeys = List("GSBank"),
    contractTypes = List("GS"),
    contractTypeWrappers = List("Note"),
    fees = FeeSchedule(240 -> 2.0),
    callable = None,
    fullyProtected = None,
    hasSinglePayment = None,
    nonCallPeriodInMonths = None)

  val salesFeeRule4: SalesFeeRule = SalesFeeRule("ruleId4", 0, "testRuleId4", FeeType.HomeOffice,
    deleted = false, issuerKeys = List("GSBank"),
    contractTypes = List("GS"),
    contractTypeWrappers = List("Note"),
    fees = FeeSchedule(240 -> 2.0),
    callable = None,
    fullyProtected = None,
    hasSinglePayment = None,
    nonCallPeriodInMonths = None)

  val salesFeeRule5: SalesFeeRule = SalesFeeRule("ruleId5",
    0,
    name = "GSBank Note issuer callable with not callable first 3 months",
    feeType = FeeType.HomeOffice,
    deleted = false,
    issuerKeys = List("GSBank"),
    contractTypes = List("GS"),
    contractTypeWrappers = List("Note"),
    fees = FeeSchedule(240 -> 2.0),
    callable = Some(true),
    fullyProtected = None,
    hasSinglePayment = None,
    nonCallPeriodInMonths = Some(3.0))

  val changelog: ChangelogItem = ChangelogItem(snapId, "userId", LocalDateTime.of(2020, 7, 24, 1, 1), Some("comment"))

  val networkRepository: MongoNetworkRepository = mock[MongoNetworkRepository]
  val rulesRepository: SalesFeeRuleRepository = mock[SalesFeeRuleRepository]

  val salesFeeRulesService = new SalesFeeRulesService(networkRepository, rulesRepository, 3)

  override def beforeEach(): Unit = {
    reset(rulesRepository)
  }

  "SalesFeeRulesService" should {

    "getNetworkById" should {
      "return fee rule by id if it exists in the repository" in {
        when(rulesRepository.findById(salesFeeRule1.id)) thenReturn Future.successful(Some(salesFeeRule1))
        salesFeeRulesService.findById(salesFeeRule1.id, adminRequesterAcl).map { rule =>
          rule should be(salesFeeRule1)
        }
      }
      "fail if fee rule does not exist" in {
        when(rulesRepository.findById(salesFeeRule1.id)) thenReturn Future.successful(None)
        recoverToExceptionIf[HttpError] {
          salesFeeRulesService.findById(salesFeeRule1.id, adminRequesterAcl)
        } map { httpError =>
          httpError.status should be(StatusCodes.NotFound)
        }
      }
    }
      "findAll" should {
        "return all sales fee rules" in {
          when(rulesRepository.findAll) thenReturn Future.successful(List(salesFeeRule1))
          salesFeeRulesService.findAll(adminRequesterAcl).map { result =>
            result should be(List(salesFeeRule1))
          }
        }

        "return none if no sales fee rules found" in {
          when(rulesRepository.findAll) thenReturn Future.successful(List())
          salesFeeRulesService.findAll(adminRequesterAcl).map { result =>
            result should be(List())
          }
        }
      }

      "delete" should {
        "delete sales fee rule by id" in {
          when(rulesRepository.delete(salesFeeRule1.id)) thenReturn Future.successful(true)
          salesFeeRulesService.delete(salesFeeRule1.id) map { result =>
            result should be(true)
          }
        }
      }

      "getChangeLogs" should {
        "return change logs if found by sales fee rule id" in {
          when(rulesRepository.getChangelogs(salesFeeRule1.id)) thenReturn Future.successful(List(changelog))
          salesFeeRulesService.getChangelogs(salesFeeRule1.id).map { result =>
            result should be(List(changelog))
          }
        }

        "return empty list if no change logs found" in {
          when(rulesRepository.getChangelogs(salesFeeRule1.id)) thenReturn Future.successful(List.empty)
          salesFeeRulesService.getChangelogs(salesFeeRule1.id).map { result =>
            result should be(List.empty)
          }
        }
      }

      "getSnapshot" should {
        "return networkview of matching snapshot entity" in {
          when(rulesRepository.getSnapshotEntity(snapId)) thenReturn Future.successful(Some(salesFeeRule1))
          salesFeeRulesService.getSnapshot(snapId).map { result =>
            result should be(result)
          }
        }

        "return none if no matching snapshot found" in {
          when(rulesRepository.getSnapshotEntity(snapId)) thenReturn Future.successful(None)
          salesFeeRulesService.getSnapshot(snapId).map { result =>
            result should be(None)
          }
        }
      }

      "handle insert" should {
        "create a sales fee rule" in {
          val req: CreateFeeRuleRequest = CreateFeeRuleRequest("testRuleId",
            feeType = FeeType.HomeOffice,
            issuerKeys = Some(List("GSBank")),
            contractTypes = Some(List("GS")),
            contractTypeWrappers = Some(List("Note")),
            fees = FeeSchedule(240 -> 2.0),
            comment = Some("comment"),
            callable = None,
            fullyProtected = None,
            hasSinglePayment = None,
            nonCallPeriodInMonths = None
          )

          when(rulesRepository.insertAndSnap(MM.any[SalesFeeRule], MM.eq(adminRequesterAcl.userId), MM.eq(req.comment), MM.eq(3))(MM.any[TraceId])) thenReturn Future.successful(salesFeeRule1)
          salesFeeRulesService.handleInsertRequest(adminRequesterAcl, req).map { result =>
            result should be(salesFeeRule1)
          }
        }
      }

      "handle update" should {
        "fee rule exists in mongo" in {
          val req: UpdateFeeRuleRequest = UpdateFeeRuleRequest(salesFeeRule1.id, 1, "updatedName", salesFeeRule1.feeType, None,
            None, Some(List("CD")), salesFeeRule1.fees, Some("updatedOnce"), None, None, Some(true), None)

          when(rulesRepository.findById(salesFeeRule1.id)) thenReturn Future.successful(Some(salesFeeRule1))
          when(rulesRepository.updateAndSnap(MM.any[SalesFeeRule], MM.eq(adminRequesterAcl.userId), MM.eq(req.comment), MM.eq(3))(MM.any[TraceId])) thenReturn Future.successful(salesFeeRule2)

          salesFeeRulesService.handleUpdateRequest(adminRequesterAcl, req).map { result =>
            result should be(salesFeeRule2)
          }
        }

        "fails if sales fee rule's not in mongo" in {
          val req: UpdateFeeRuleRequest = UpdateFeeRuleRequest(salesFeeRule1.id, 1, "updatedName", salesFeeRule1.feeType, None,
            None, Some(List("CD")), salesFeeRule1.fees, Some("updatedOnce"), None, None, Some(true), None)

          when(rulesRepository.findById(salesFeeRule1.id)) thenReturn Future.successful(None)
          recoverToExceptionIf[HttpError] {
            salesFeeRulesService.handleUpdateRequest(adminRequesterAcl, req)
          } map { httpError =>
            httpError.status should be(StatusCodes.NotFound)
          }
        }
      }

      "findAllById" should {
        "return sales fee rules with ids if they exist in the repository" in {
          when(rulesRepository.findAllById(Seq(salesFeeRule1.id, salesFeeRule3.id, salesFeeRule4.id, salesFeeRule5.id))) thenReturn Future.successful(List(salesFeeRule1, salesFeeRule3, salesFeeRule4, salesFeeRule5))
          salesFeeRulesService.findAllById(Seq(salesFeeRule1.id, salesFeeRule3.id, salesFeeRule4.id, salesFeeRule5.id), adminRequesterAcl).map { rules =>
            rules.contains(salesFeeRule1) should be(true)
            rules.contains(salesFeeRule3) should be(true)
            rules.contains(salesFeeRule4) should be(true)
            rules.contains(salesFeeRule5) should be(true)
          }
        }

        "return  empty seq if sales fee rules with ids don't exist in the repository" in {
          when(rulesRepository.findAllById(Seq(salesFeeRule1.id, salesFeeRule3.id, salesFeeRule4.id))) thenReturn Future.successful(List.empty[SalesFeeRule])
          salesFeeRulesService.findAllById(Seq(salesFeeRule1.id, salesFeeRule3.id, salesFeeRule4.id), adminRequesterAcl).map { rules =>
            rules.isEmpty should be(true)
          }
        }
      }
    }
}
