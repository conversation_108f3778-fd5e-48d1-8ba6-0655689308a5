package com.simonmarkets.sales.fee.rules.models

import com.simonmarkets.sales.fee.rules.common.api.SalesFeeRulesOpenApiDefinitions.ContractTypeWrapper
import io.simon.openapi.annotation.Field.{Ref, Required, TypeArgRef}
import io.simon.openapi.definitions.CommonDefinitions

case class SalesFeeCalculatorDryRunRequest(
    @Required
    @TypeArgRef(CommonDefinitions.Symbol)
    rules: List[String],
    @Required
    @Ref(CommonDefinitions.Symbol)
    issuerKey: String,
    @Required
    @Ref(ContractTypeWrapper)
    contractTypeWrapper: String,
    @Ref(CommonDefinitions.Boolean)
    callable: Option[Boolean],
    @Ref(CommonDefinitions.Boolean)
    fullyProtected: Option[<PERSON>olean],
    @Ref(CommonDefinitions.Boolean)
    hasSinglePayment: Option[Boolean]
)
