package com.simonmarkets.sales.fee.rules.config

import com.simonmarkets.api.users.config.CacheConfig
import com.simonmarkets.http.HttpClientConfig
import com.simonmarkets.mongodb.config.MongoClientConfig
import com.simonmarkets.resteasy.framework.RunMode
import com.simonmarkets.resteasy.framework.config.RestEasyAppConfiguration
import com.simonmarkets.resteasy.framework.config.RestEasyAppConfiguration.SystemRoutesConfig
import com.simonmarkets.resteasy.framework.config.authn.AuthenticationConfiguration
import com.simonmarkets.resteasy.framework.info.InfoConfig

case class AppConfig(
    runMode: RunMode,
    httpClientConfig: HttpClientConfig,
    authentication: Option[AuthenticationConfiguration],
    systemRoutes: Option[SystemRoutesConfig],
    mongoDB: MongoDBConfig,
    aclServerPath: String,
    transactionRetryLimit: Int,
    aclCacheConfig: CacheConfig,
    info: Option[InfoConfig],
) extends RestEasyAppConfiguration

case class DbConfiguration(
    collection: String,
    database: String
)

case class MongoDBConfig(
    client: MongoClientConfig,
    networks: DbConfiguration,
    networksSnapshots: DbConfiguration,
    salesFeeRulesSnapshots: DbConfiguration,
    salesFeeRules: DbConfiguration
)
