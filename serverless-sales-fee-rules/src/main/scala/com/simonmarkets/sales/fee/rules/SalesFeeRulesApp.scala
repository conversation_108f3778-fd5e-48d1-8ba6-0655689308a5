package com.simonmarkets.sales.fee.rules

import akka.http.scaladsl.Http
import com.simonmarkets.api.users.HttpACLClient
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.mongodb.Client
import com.simonmarkets.networks.HttpNetworksClient
import com.simonmarkets.networks.common.repository.MongoNetworkRepository
import com.simonmarkets.sales.fee.rules.api.SalesFeeRulesRoutes
import com.simonmarkets.sales.fee.rules.common.repository.MongoSalesFeeRuleRepository
import com.simonmarkets.sales.fee.rules.config.AppConfig
import com.simonmarkets.sales.fee.rules.service.{SalesFeeCalculatorService, SalesFeeRulesService}
import io.simon.openapi.generator.OpenApiGenerator
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.{Document, MongoCollection}
import com.simonmarkets.resteasy.framework.RestEasyModule.RestEasyLambda
import com.simonmarkets.resteasy.framework.{Environment, RestEasyModule}
import com.simonmarkets.useracl.UserAclAuthorizedDirective
import pureconfig.generic.auto._

object SalesFeeRulesApp extends RestEasyModule[AppConfig] with App with TraceLogging {

  override def serviceName: String = "SalesFeeRules"

  override def servicePath: String = "simon/api/v1/sales-fee-rules"

  OpenApiGenerator.generateOpenApiDocumentation

  override def init(environment: Environment): Unit = {
    val aclCacheConfig = config.aclCacheConfig
    val httpClient = new FutureHttpClient(Http(), config.httpClientConfig)
    val httpACLClient = HttpACLClient(httpClient, config.aclServerPath, Some(aclCacheConfig))
    val userACLDirective = UserAclAuthorizedDirective(httpACLClient)
    val networksClient: HttpNetworksClient = new HttpNetworksClient(
      client = httpClient,
      path = config.aclServerPath
    )

    val transactionsLimit = config.transactionRetryLimit

    log.info("Creating mongo client")
    val mongoClient = Client.create(config.mongoDB.client)

    val salesFeeRulesCollection: MongoCollection[Document] = mongoClient
      .getDatabase(config.mongoDB.salesFeeRules.database)
      .withCodecRegistry(DEFAULT_CODEC_REGISTRY)
      .getCollection[Document](config.mongoDB.salesFeeRules.collection)

    val salesFeeRulesSnapshots: MongoCollection[Document] = mongoClient
      .getDatabase(config.mongoDB.salesFeeRulesSnapshots.database)
      .withCodecRegistry(DEFAULT_CODEC_REGISTRY)
      .getCollection[Document](config.mongoDB.salesFeeRulesSnapshots.collection)

    val networkCollection: MongoCollection[Document] = mongoClient
      .getDatabase(config.mongoDB.networks.database)
      .withCodecRegistry(DEFAULT_CODEC_REGISTRY)
      .getCollection[Document](config.mongoDB.networks.collection)

    val networkSnapshotsCollection: MongoCollection[Document] = mongoClient
      .getDatabase(config.mongoDB.networksSnapshots.database)
      .withCodecRegistry(DEFAULT_CODEC_REGISTRY)
      .getCollection[Document](config.mongoDB.networksSnapshots.collection)

    val networkRepository = new MongoNetworkRepository(networkCollection, networkSnapshotsCollection, mongoClient)
    val salesFeeRulesRepository = new MongoSalesFeeRuleRepository(salesFeeRulesCollection, salesFeeRulesSnapshots, mongoClient)

    val service = new SalesFeeRulesService(networkRepository, salesFeeRulesRepository, transactionsLimit)
    val calculatorService = new SalesFeeCalculatorService(networksClient, salesFeeRulesRepository)
    val routes = SalesFeeRulesRoutes(service, calculatorService, userACLDirective).routes
    environment.addRoutes(routes)
  }
  RestEasy.start()
  object EntryPoint extends RestEasyLambda
}
