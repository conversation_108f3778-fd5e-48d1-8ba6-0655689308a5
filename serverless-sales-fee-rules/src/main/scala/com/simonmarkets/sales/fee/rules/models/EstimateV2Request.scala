package com.simonmarkets.sales.fee.rules.models

import com.simonmarkets.sales.fee.rules.common.api.SalesFeeRulesOpenApiDefinitions.ContractTypeWrapper
import io.simon.openapi.annotation.Field.{Ref, Required}
import io.simon.openapi.definitions.CommonDefinitions
import io.simon.openapi.definitions.CommonDefinitions.NetworkName
import simon.Id.NetworkId

case class EstimateV2Request(
    requestId: Option[String],
    @Required
    @Ref(NetworkName)
    networkId: NetworkId,
    @Required
    @Ref(CommonDefinitions.Symbol)
    issuerKey: String,
    @Required
    @Ref(ContractTypeWrapper)
    contractTypeWrapper: String,
    callable: Option[<PERSON>olean],
    fullyProtected: Option[<PERSON><PERSON>an],
    hasSinglePayment: Option[Boolean]
)
