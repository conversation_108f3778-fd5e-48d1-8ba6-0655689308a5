package com.simonmarkets.sales.fee.rules.models

import com.simonmarkets.sales.fee.rules.common.{FeeSchedule, FeeType}

case class Calculation(
  wholesaler: FeeSchedule,
  homeOffice: FeeSchedule,
  simon: FeeSchedule,
  max: FeeSchedule,
  faCommission: FeeSchedule,
) {

  import Calculation.BigDecimalOptionOps

  def values: Seq[FeeSchedule] = wholesaler :: homeOffice :: simon :: Nil

  def get(feeType: FeeType): Option[FeeSchedule] = feeType match {
    case FeeType.Wholesaler   => Some(wholesaler)
    case FeeType.HomeOffice   => Some(homeOffice)
    case FeeType.Simon        => Some(simon)
    case FeeType.Max          => Some(max)
    case FeeType.FACommission => Some(faCommission)
    case FeeType.Structuring  => None
  }

  def fee(
    maturity: Int,
    distributorFee: Option[BigDecimal] = None,
  ): Option[BigDecimal] = {

    val maxFee  = max.fee(maturity)
    val fees    = values map { _.fee(maturity) }
    val sum     = fees.fold(None) { _ merge _ } // sum of some(x)

    // If distributor fee is defined, fee is the difference between distributor fee and sum of default fee for maturity.
    // Then, return the less of the computed fee above and maxFee in input.
    sum map { sum =>
      val fee = distributorFee.fold(sum) { distributorFee => (distributorFee - sum) max 0 }
      maxFee.fold(fee) { maxFee => fee min maxFee }
    }
  }

  override def toString: String = {
    val keys = { wholesaler.months ++ simon.months ++ homeOffice.months ++ max.months }.toSeq.sorted
    val matrix = keys.map { month =>
      Array(
        wholesaler.fee(month).map(_.toString).getOrElse(""),
        homeOffice.fee(month).map(_.toString).getOrElse(""),
        simon.fee(month).map(_.toString).getOrElse(""),
        max.fee(month).map(_.toString).getOrElse(""))
    }.toArray
    val columnWidth = matrix.map { _.map { _.length }.max }.max + 2

    val sb = new StringBuilder

    def addCell(text: String, len: Int = columnWidth): Unit = {
      val spaces = " " * len
      sb.append("| ")
      sb.append({spaces + text}.takeRight(len))
    }

    sb.append("| Calculation ")
    addCell("WS")
    addCell("HO")
    addCell("SI")
    addCell("MX")
    sb.append("\n")

    for { key <- keys } {
      addCell(key.toString, 12)
      addCell(wholesaler.fee(key).map(_.toString) getOrElse "")
      addCell(homeOffice.fee(key).map(_.toString) getOrElse "")
      addCell(simon.fee(key).map(_.toString) getOrElse "")
      addCell(max.fee(key).map(_.toString) getOrElse "")
      sb.append("\n")
    }

    sb.toString()
  }
}

object Calculation {

  def apply(
    wholesaler: FeeSchedule,
    homeoffice: FeeSchedule,
    simon: FeeSchedule,
    faComission:FeeSchedule,
  ): Calculation =
    Calculation(
      wholesaler = wholesaler,
      homeOffice = homeoffice,
      simon = simon,
      max = FeeSchedule.empty,
      faCommission = faComission,
    )

  def apply(schedule: FeeSchedule): Calculation = Calculation(
    wholesaler = schedule,
    homeOffice = schedule,
    simon      = schedule,
    max        = FeeSchedule(),
    faCommission = schedule,
  )

  def apply(xs: (FeeType, FeeSchedule)*): Either[String, Calculation] = apply(xs.toMap)

  def apply(feesMap: Map[FeeType, FeeSchedule]): Either[String, Calculation] = {

    def warn(): String = {
      val absent = (FeeType.Values.toSet -- feesMap.keySet - FeeType.Max - FeeType.FACommission) map { _.name } mkString " and "

      s"Fee schedule is not defined for $absent components. Please define it to proceed with calculations."
    }

    for {
      wholesaler  <- feesMap.get(FeeType.Wholesaler).toRight(warn())
      homeoffice  <- feesMap.get(FeeType.HomeOffice).toRight(warn())
      simon       <- feesMap.get(FeeType.Simon).toRight(warn())
    } yield Calculation(
      wholesaler  = wholesaler,
      homeOffice  = homeoffice,
      simon       = simon,
      max         = feesMap.getOrElse(FeeType.Max, FeeSchedule.empty),
      faCommission = feesMap.getOrElse(FeeType.FACommission, FeeSchedule.empty),
    )
  }

  def partial(feesMap: Map[FeeType, FeeSchedule]): Calculation = {
    Calculation(
      wholesaler  = feesMap.getOrElse(FeeType.Wholesaler, FeeSchedule.empty),
      homeOffice  = feesMap.getOrElse(FeeType.HomeOffice, FeeSchedule.empty),
      simon       = feesMap.getOrElse(FeeType.Simon, FeeSchedule.empty),
      max         = feesMap.getOrElse(FeeType.Max, FeeSchedule.empty),
      faCommission = feesMap.getOrElse(FeeType.FACommission, FeeSchedule.empty),
    )
  }

  private implicit class BigDecimalOptionOps(val x: Option[BigDecimal]) extends AnyVal {
    def merge(y: Option[BigDecimal]): Option[BigDecimal] = (x, y) match {
      case (Some(x), Some(y)) => Some(x + y)
      case (Some(x), None)    => Some(x)
      case (None, Some(y))    => Some(y)
      case (None, None)       => None
    }
  }
}
