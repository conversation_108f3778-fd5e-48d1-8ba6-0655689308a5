package com.simonmarkets.sales.fee.rules.service

import akka.stream.Materializer
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.{NetworkDTO, NetworksClient}
import com.simonmarkets.sales.fee.rules.common.repository.SalesFeeRuleRepository
import com.simonmarkets.sales.fee.rules.models.{Calculation, EstimateV2BatchResponse, EstimateV2Request, NetworkFees, SalesFeeRuleFilter}
import simon.Id.NetworkId

import scala.concurrent.{ExecutionContext, Future}


class SalesFeeCalculatorService(
  networksClient: NetworksClient,
  rulesRepository: SalesFeeRuleRepository,
)(implicit ec: ExecutionContext, mat: Materializer) extends TraceLogging {

  def estimate(
    networkId: NetworkId,
    issuerKey: String,
    contractTypeWrapper: String,
    callable: Option[Boolean],
    fullyProtected: Option[Boolean],
    hasSinglePayment: Option[Boolean],
  )(implicit traceId: TraceId): Future[Calculation] = for {
    networkFees <- networkFeesByNetworkId(networkId)
    calculation <- networkFees.calculate(
      SalesFeeRuleFilter(
        issuerKey,
        None,
        contractTypeWrapper,
        callable,
        fullyProtected,
        hasSinglePayment
      ),
      allowPartial = false,
    )
  } yield calculation

  def estimateBatch(
    estimateSeq: Seq[EstimateV2Request],
  )(implicit traceId: TraceId): Future[EstimateV2BatchResponse] = for {
    networkFees <- networkFeesByNetworkIds(estimateSeq.map(_.networkId).toSet)
  } yield {
    val batch = (estimateSeq.zipWithIndex map { case (estimate, index) =>
      (estimate.requestId.getOrElse(index.toString), networkFees.get(estimate.networkId).map(fees =>
        fees.apply(
          SalesFeeRuleFilter(
            estimate.issuerKey,
            None,
            estimate.contractTypeWrapper,
            estimate.callable,
            estimate.fullyProtected,
            estimate.hasSinglePayment
          ),
          allowPartial = false)
      ).getOrElse(Left(s"Cannot find network fees for networkId=${estimate.networkId}")))
    }).toMap

    EstimateV2BatchResponse.fromBatch(batch)
  }

  def dryRun(
    ruleIds: List[String],
    issuerKey: String,
    contractTypeWrapper: String,
    callable: Option[Boolean],
    fullyProtected: Option[Boolean],
    hasSinglePayment: Option[Boolean],
  ): Future[Calculation] = for {
    networkFees <- networkFeesByRuleIds(ruleIds)
    calculation <- networkFees.calculate(
      SalesFeeRuleFilter(
        issuerKey,
        None,
        contractTypeWrapper,
        callable,
        fullyProtected,
        hasSinglePayment
      ),
      allowPartial = true
    )
  } yield calculation

  private def networkFeesByNetworkId(
    networkId: NetworkId,
  )(implicit traceId: TraceId): Future[NetworkFees] = for {
    network     <- networksClient.getById(networkId)
    networkFees <- networkFeesByRuleIds(network.salesFeeRuleIds)
  } yield networkFees

  private def networkFeesByNetworkIds(
    networkIds: Set[NetworkId],
  )(implicit traceId: TraceId): Future[Map[NetworkId, NetworkFees]] = for {
    networks  <- networksClient.streamAll(ids = Some(networkIds.toSeq)).runFold(Seq.empty[NetworkDTO])(_ ++ _)
    rules     <- rulesRepository.findAllById(networks.flatMap(_.salesFeeRuleIds))
  } yield {
    val ruleMap = rules.map { rule => rule.id -> rule }.toMap
    networks.map { network =>
      (network.id, NetworkFees(network.salesFeeRuleIds.reverse flatMap ruleMap.get))
    }.toMap
  }

  private def networkFeesByRuleIds(ruleIds: List[String]): Future[NetworkFees] = for {
    rules <- rulesRepository.findAllById(ruleIds)
  } yield {
    val rulesById = rules.map(rule => rule.id -> rule).toMap
    NetworkFees(ruleIds.reverse.flatMap(rulesById.get))
  }
}
