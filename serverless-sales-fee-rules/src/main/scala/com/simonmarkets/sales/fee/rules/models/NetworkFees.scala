package com.simonmarkets.sales.fee.rules.models

import com.simonmarkets.sales.fee.rules.common.SalesFeeRule

import scala.concurrent.Future

/** NetworkFees is an intermediary representation of all sales fee rules applied to corresponding
 * network aiming to provide us with ability to calculate (filter and select) fee schedules for
 * all the fee types.
 *
 * @param rules
 */
case class NetworkFees(rules: List[SalesFeeRule]) {

  /** Here is where filtering is done.
   * So we are walking through defined sales fee rules and gathering only those
   * which match our filtering criterias which are (contractTypeWrapper, contactType, issuerKey).
   *
   * For filtered rules we are merging fee schedules.
   * For more details about merging please refer [[com.goldmansachs.marquee.pipg.FeeSchedule#updated]]
   *
   * @param filter       set of filtering criterias
   * @param allowPartial partially defined result means the result which contain fee schedules
   *                     for only some of possible fee types (not all)
   * @return Right if all good
   *         Left explanation of exception
   */
  def apply(filter: SalesFeeRuleFilter, allowPartial: Boolean): Either[String, Calculation] = {
    val effectiveFees = for {
      rule <- rules.toStream if filter.matched(rule)
    } yield (rule.feeType, rule.fees)

    val feeSchedules = {
      val effectiveFeeMap = effectiveFees groupBy { case (feeType, _) => feeType }

      effectiveFeeMap mapValues { stream =>
        stream.map { case (_, feeSchedule) => feeSchedule }.reduce {
          _.updated(_)
        }
      }
    }

    if (allowPartial) Right(Calculation.partial(feeSchedules)) else Calculation(feeSchedules)
  }

  def calculate(
    filter: SalesFeeRuleFilter,
    allowPartial: Boolean,
  ): Future[Calculation] = {
    apply(filter, allowPartial) match {
      case Left(msg) => Future.failed(new IllegalArgumentException(msg))
      case Right(calculation) => Future.successful(calculation)
    }
  }
}
