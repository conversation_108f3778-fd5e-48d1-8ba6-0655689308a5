package com.simonmarkets.sales.fee.rules.models

import akka.http.scaladsl.model.StatusCodes
import com.simonmarkets.sales.fee.rules.models.EstimateV2BatchResponse._

case class EstimateV2BatchResponse(
  successes: List[BatchResponseSuccess],
  failures: List[BatchResponseFailure]
)

object EstimateV2BatchResponse {
  case class BatchResponseSuccess(requestId: String, statusCode: Int, entity: Calculation)
  case class BatchResponseFailure(requestId: String, statusCode: Int, failureMessage: String)

  def fromBatch(batch: Map[String, Either[String, Calculation]]): EstimateV2BatchResponse = {
    val successes = batch.collect {
      case (key, Right(calc)) => BatchResponseSuccess(key, StatusCodes.OK.intValue, calc)
    }.toList
    val failures = batch.collect {
      case (key, Left(errorMessage)) => BatchResponseFailure(key, StatusCodes.BadRequest.intValue, errorMessage)
    }.toList
    EstimateV2BatchResponse(successes, failures)
  }
}
