package com.simonmarkets.sales.fee.rules.models

import com.simonmarkets.sales.fee.rules.common.SalesFeeRule

case class SalesFeeRuleFilter(
    issuerKey: String,
    contractType: Option[String],
    contractTypeWrapper: String,
    callable: Option[Boolean] = None,
    fullyProtected: Option[Boolean] = None,
    hasSinglePayment: Option[Boolean] = None) {

  def matched(feeRule: SalesFeeRule): Boolean = {
    def matchesCriteria(x: List[String], y: String): Boolean =
      // assuming that empty filter mean - accept any
      if(x.isEmpty) true else x contains y

    (contractType.isEmpty || contractType.exists(matchesCriteria(feeRule.contractTypes, _))) &&
      matchesCriteria(feeRule.contractTypeWrappers, contractTypeWrapper) &&
      matchesCriteria(feeRule.issuerKeys, issuerKey) &&
      matchCallable(feeRule) && matchFullyProtected(feeRule) &&
      matchHasSinglePayment(feeRule)
  }

  def matchCallable(feeRule: SalesFeeRule) : Boolean =
    callable.isEmpty || feeRule.callable.isEmpty || callable == feeRule.callable

  def matchFullyProtected(feeRule: SalesFeeRule) : Boolean = {
    fullyProtected.isEmpty || feeRule.fullyProtected.isEmpty || fullyProtected == feeRule.fullyProtected
  }

  def matchHasSinglePayment(feeRule: SalesFeeRule) : Boolean = {
    hasSinglePayment.isEmpty || feeRule.hasSinglePayment.isEmpty || hasSinglePayment == feeRule.hasSinglePayment
  }
}
