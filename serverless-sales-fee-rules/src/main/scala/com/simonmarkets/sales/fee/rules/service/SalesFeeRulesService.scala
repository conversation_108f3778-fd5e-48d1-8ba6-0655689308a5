package com.simonmarkets.sales.fee.rules.service

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.ChangelogItem
import com.simonmarkets.networks.common.repository.MongoNetworkRepository
import com.simonmarkets.sales.fee.rules.common.SalesFeeRule
import com.simonmarkets.sales.fee.rules.common.api.{CreateFeeRuleRequest, UpdateFeeRuleRequest}
import com.simonmarkets.sales.fee.rules.common.repository.SalesFeeRuleRepository

import scala.concurrent.{ExecutionContext, Future}

class SalesFeeRulesService(
    networksRepository: MongoNetworkRepository,
    rulesRepository: SalesFeeRuleRepository,
    transactionLimit: Int)
  (implicit ec: ExecutionContext) extends TraceLogging {

  def findAll(requesterAcl: UserACL)(implicit traceId: TraceId): Future[Iterable[SalesFeeRule]] = {
    log.info("Getting all sales fee rules by user", requesterAcl.userId)
    rulesRepository.findAll
  }

  def findById(id: String, requesterAcl: UserACL)(implicit traceId: TraceId): Future[SalesFeeRule] = {
    log.info(s"Getting sales fee rule by id: $id, by user: ${requesterAcl.userId}")
    rulesRepository.findById(id) map {
      case None => throw HttpError.notFound(s"Couldn't query fee rule with id=$id")
      case Some(feeRule) => feeRule
    }
  }

  def findAllById(ids: Seq[String], requesterAcl: UserACL)(implicit traceId: TraceId): Future[Seq[SalesFeeRule]] = {
    log.info(s"Getting sales fee rule by id: ${ids.mkString(", ")}, by user: ${requesterAcl.userId}")
    rulesRepository.findAllById(ids).map(_.toSeq)
  }

  def checkRuleId(id: String)(implicit traceId: TraceId): Future[Boolean] = {
    log.info("Looking for sales fee rule id:", id)
    networksRepository.networkWithSalesFeeRuleExists(id)
  }

  def delete(id: String)(implicit traceId: TraceId): Future[Boolean] = {
    log.info("Deleting sales fee rule with id: ", id)
    rulesRepository.delete(id)
  }

  def handleInsertRequest(requesterAcl: UserACL, request: CreateFeeRuleRequest)(implicit traceId: TraceId): Future[SalesFeeRule] = {
    log.info("Handling Fee rule Insert Request", request.name)

    for {
      insertedFeeRule <- rulesRepository.insertAndSnap(
        request.asEntity,
        requesterAcl.userId,
        request.comment,
        transactionLimit
      )
    } yield insertedFeeRule
  }

  def getChangelogs(id: String)(implicit traceId: TraceId): Future[List[ChangelogItem]] = {
    log.info("Getting change logs for fee rule ", id)
    rulesRepository.getChangelogs(id)
  }

  def getSnapshot(snapshotId: String)(implicit traceId: TraceId): Future[Option[SalesFeeRule]] = {
    log.info("Getting a sales fee rule snapshot", snapshotId)
    rulesRepository.getSnapshotEntity(snapshotId)
  }

  def handleUpdateRequest(requesterAcl: UserACL, request: UpdateFeeRuleRequest)(implicit traceId: TraceId): Future[SalesFeeRule] = {
    log.info("Handling Fee rule Update Request", request.name)

    for {
      currentRule <- findById(request.id, requesterAcl)
      updatedFeeRule <- rulesRepository.updateAndSnap(
        request.copy(id = currentRule.id, version = currentRule.version).asEntity,
        requesterAcl.userId,
        request.comment,
        transactionLimit
      )
    } yield updatedFeeRule
  }
}
