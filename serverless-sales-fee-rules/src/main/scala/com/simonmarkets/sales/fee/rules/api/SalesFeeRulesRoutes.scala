package com.simonmarkets.sales.fee.rules.api

import akka.http.scaladsl.server.{Directive, Directive0, Directive1, Route}
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.sales.fee.rules.common.api.{CreateFeeRuleRequest, ListFeeRulesRequest, UpdateFeeRuleRequest}
import com.simonmarkets.sales.fee.rules.models.{EstimateV2Request, SalesFeeCalculatorDryRunRequest}
import com.simonmarkets.sales.fee.rules.service.{SalesFeeCalculatorService, SalesFeeRulesService}
import com.simonmarkets.useracl.{UserAclAuthorizedDirective, UserAclAuthorizedDirectives}
import simon.Id.NetworkId

import scala.concurrent.{ExecutionContext, Future}

case class SalesFeeRulesRoutes(
    service: SalesFeeRulesService,
    calculatorService: SalesFeeCalculatorService,
    authorizationDirective: UserAclAuthorizedDirective
    )(implicit ec: ExecutionContext) extends TraceLogging with DirectivesWithCirce with UserAclAuthorizedDirectives with JsonCodecs {

  def routes: Route = admin { (trace, acl) => {
      implicit val (traceId@_, userACL) = (trace, acl)
      rejectEmptyResponse(
        V1_FEE_RULES {
            `GET /:id` { feeRuleId =>
              complete(service.findById(feeRuleId, userACL))
            } ~
            `GET /` {
              complete(service.findAll(userACL))
            } ~
            `GET /:id/snapshots` { feeRuleId =>
              complete(service.getChangelogs(feeRuleId).map(feeRule => feeRule.head))
            } ~
            `GET /:feeRuleId/snapshots/:id` { (_, snapId) =>
              complete(service.getSnapshot(snapId))
            } ~
            `POST /` {
              entity(as[CreateFeeRuleRequest]) { request: CreateFeeRuleRequest =>
                complete(service.handleInsertRequest(userACL, request))
              }
            } ~
            `POST /list` {
              entity(as[ListFeeRulesRequest]) { request: ListFeeRulesRequest =>
                complete(service.findAllById(request.ids, userACL))
              }
            } ~
            `PUT /` {
              entity(as[UpdateFeeRuleRequest]) { request: UpdateFeeRuleRequest =>
                complete(service.handleUpdateRequest(userACL, request))
              }
            } ~
            `DELETE /:id` { feeRuleId =>
              complete(service.checkRuleId(feeRuleId).map(bool =>
                if (!bool) {
                  service.delete(feeRuleId)
                }
                else {
                  Future.failed(HttpError.conflict("Rule id exists in network"))
                }
              )
              )
            }
        } ~
        V2_SALES_FEE_CALCULATOR (
          (get & pathEnd) {
            parameters(
              "networkId",
              "issuerKey",
              "contractTypeWrapper",
              "callable".as[Boolean].?,
              "fullyProtected".as[Boolean].?,
              "hasSinglePayment".as[Boolean].?
            )((
              networkId,
              issuerKey,
              contractTypeWrapper,
              callable,
              fullyProtected,
              hasSinglePayment
            ) => {
              complete(
                calculatorService.estimate(
                  NetworkId(networkId),
                  issuerKey,
                  contractTypeWrapper,
                  callable,
                  fullyProtected,
                  hasSinglePayment,
                )
              )
            })
          } ~
          (post & pathEnd) {
            entity(as[SalesFeeCalculatorDryRunRequest]) { request: SalesFeeCalculatorDryRunRequest =>
              complete(
                calculatorService.dryRun(
                  request.rules,
                  request.issuerKey,
                  request.contractTypeWrapper,
                  request.callable,
                  request.fullyProtected,
                  request.hasSinglePayment,
                )
              )
            }
          } ~
          (post & path("query")) {
            entity(as[List[EstimateV2Request]]) { request: List[EstimateV2Request] =>
              complete(
                calculatorService.estimateBatch(request)
              )
            }
          }
        )
      )
    }
  }

  def `GET /`: Directive0 = get & pathEnd

  def `GET /:id`: Directive1[String] = get & path(Segment) & pathEnd

  def `DELETE /:id`: Directive1[String] = delete & path(Segment) & pathEnd

  def `POST /`: Directive0 = post & pathEndOrSingleSlash

  def `POST /list`: Directive0 = post & path("list") & pathEndOrSingleSlash

  def `PUT /`: Directive0 = put & pathEndOrSingleSlash

  def `GET /:id/snapshots`: Directive1[String] = get & path(Segment / "snapshots") & pathEnd

  def `GET /:feeRuleId/snapshots/:id`: Directive[(String, String)] = get & path(Segment / "snapshots" / Segment) & pathEnd

  val V1_FEE_RULES: Directive0 = pathPrefix("simon" / "api" / "v1" / "sales-fee-rules")

  val V2_SALES_FEE_CALCULATOR: Directive0 = pathPrefix("simon" / "api" / "v2" / "sales-fee-calculator")
}
