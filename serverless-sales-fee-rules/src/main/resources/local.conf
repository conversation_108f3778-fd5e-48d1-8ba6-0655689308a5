include classpath("common.conf")

info {
  name = "Sales Fee Rules Service"
  description = "The service provides CRUD operations to for the sales fee rules used by distributor networks"
  repository = "networks"
  module = "serverless-sales-fee-rules"
  version = "${project.version}"
  revision = "${git.commit.id.abbrev}"
  build-timestamp = "${build.timestamp.millis}"
  product-line = "StructuredInvestment"
  used-by = ["StructuredInvestment"]
  owner = "Quant"
  support-distro = ["<EMAIL>"]
  metrics-url = "todo",
  documentation-url = "todo"
}

run-mode {
  type = "server-mode"
  http-server-config {
    port = 8086
    interface = "0.0.0.0"
    ssl {
      enabled = false
      keystore {
        type = "JKS"
        file = ""
        password = ""
      }
    }
  }
}

http-client-config {
  auth {
    type: OAuth
    client-id: "0oaevl54gplnFvyx70h7"
    client-secret-file: ""
    client-secret: "sm:Okta-secret"
    site = "sm:applicationconfig-issuer-uri"
    authorization-path: ""
    token-path = "/v1/token"
    scope: "read_product_data"
    token-type = {
      type = BearerToken
    }
  }
}

acl-server-path = "https://www.dev.simonmarkets.com/simon/api"
transaction-retry-limit = 3

authentication {
  type = "jwt"
  jwt {
    source {
      type = "cookieOrHeader"
      name = "SimSSO:X-Simon-AccessToken"
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      keys-url = "sm:applicationconfig-okta-validator"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clock-offset-in-seconds = 0
    }
  }

  user-info {
    source {
      type = "header"
      name = "x-userinfo"
    }
  }
}


acl-cache-config {
  enabled = false

}


