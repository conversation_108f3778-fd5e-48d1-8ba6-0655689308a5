
system-routes {
  service-up {
    path = "simon/api/v1/sales-fee-rules/uptime"
  }
  service-info {
    path = "simon/api/v1/sales-fee-rules/info"
  }
  health-check {
    path = "simon/api/v1/sales-fee-rules/healthcheck"
  }
  warm-up {
    path = "simon/v1/sales-fee-rules/warmup"
  }
}

mongo-db {
  sales-fee-rules-snapshots {
    collection = "SalesFeeRules.snapshots"
    database = "pipg"
  }
  sales-fee-rules {
    collection = "SalesFeeRules"
    database = "pipg"
  }
  networks {
    collection = "networks_new"
    database = "pipg"
  }
  networks-snapshots {
    collection = "networks_new.snapshots"
    database = "pipg"
  }
  client {
    app-name = "serverless-sales-fee-rules"
    connection = "sm:config:applicationconfig-mongo-auth-pipg"
  }
}

config.resolvers {
  sm {
    region = "us-east-1"
  }
  file {
    root = "app-config/dev"
  }
}