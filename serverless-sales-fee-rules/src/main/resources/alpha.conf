include classpath("common.conf")

info {
  name = "Sales Fee Rules Service"
  description = "The service provides CRUD operations to for the sales fee rules used by distributor networks"
  repository = "networks"
  module = "serverless-sales-fee-rules"
  version = "${project.version}"
  revision = "${git.commit.id.abbrev}"
  build-timestamp = "${build.timestamp.millis}"
  product-line = "StructuredInvestment"
  used-by = ["StructuredInvestment"]
  owner = "Quant"
  support-distro = ["<EMAIL>"]
  metrics-url = "todo",
  documentation-url = "todo"
}

run-mode {
  type = lambda-mode
}

http-client-config {
  auth {
    type: OAuth
    client-id: "0oaevl54gplnFvyx70h7"
    client-secret-file: ""
    client-secret: "sm:Okta-secret"
    site = "sm:applicationconfig-issuer-uri"
    authorization-path: ""
    token-path = "/v1/token"
    scope: "read_product_data"
    token-type = {
      type = BearerToken
    }
    proxy: {
        address: "internal-alpha-squid-proxy-elb-1264059358.us-east-1.elb.amazonaws.com"
        port: 3128
    }
  }
  proxy: {
    address: "internal-alpha-squid-proxy-elb-1264059358.us-east-1.elb.amazonaws.com"
    port: 3128
  }
}

acl-server-path = "https://origin-a.dev.simonmarkets.com/simon/api"
transaction-retry-limit = 3

authentication {
  type = "jwt"
  jwt {
    source {
      type = "cookieOrHeader"
      name = "SimSSO:X-Simon-AccessToken"
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      keys-url = "sm:applicationconfig-okta-validator"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clock-offset-in-seconds = 0
    }
  }

  user-info {
    source {
      type = header-source
      name = "x-simon-accesstoken"
    }
  }
}

acl-cache-config {
  enabled = true
  config {
    service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
    signin-region = "us-east-1"
  }
}


