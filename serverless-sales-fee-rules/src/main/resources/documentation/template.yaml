openapi: 3.0.0
info:
  title: Sales fee rules service
  version: 1.0.0
x-basepath: /simon
x-kong-service-defaults:
  read_timeout: 300000
x-kong-plugin-aws-lambda:
  config:
    function_name: serverless-sales-fee-rules-${STAGE}-api
    aws_region: us-east-1
    invocation_type: RequestResponse
    awsgateway_compatible: true
    forward_request_body: true
    forward_request_headers: true
    forward_request_method: true
    forward_request_uri: true
    is_proxy_integration: true
    skip_large_bodies: false
    proxy_url: ${HTTP_PROXY}
    proxy_scheme: https
    log_type: None
tags:
  - name: salesfee
    description: Sales fee rules service

paths:
  /api/v1/sales-fee-rules/info:
    x-kong-route-defaults:
      regex_priority: 999
    get:
      summary: Service Info
      responses:
        200:
          description: Service info

  /api/v1/sales-fee-rules/healthcheck:
    x-kong-route-defaults:
      regex_priority: 999
    get:
      summary: Service healthcheck
      responses:
        200:
          description: Service healthcheck

  /api/v1/sales-fee-rules/uptime:
    x-kong-route-defaults:
      regex_priority: 999
    get:
      summary: Service uptime
      responses:
        200:
          description: Time elapsed since the service was up and running

  /api/v1/sales-fee-rules:
    x-kong-route-defaults:
      regex_priority: 998
    x-scopes:
      - admin
      - fa-manager
      - internal
    get:
      summary: Get Sales Fee Rules
      tags:
        - salesfee
      parameters:
        - name: _
          in: query
          required: false
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.CacheBustParameter}
      responses:
        200:
          description: Sales fee rules
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ${com.simonmarkets.sales.fee.rules.common.SalesFeeRule}
    post:
      summary: Create new Sales Fee rule
      tags:
        - salesfee
      requestBody:
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.sales.fee.rules.common.api.CreateFeeRuleRequest}
    put:
      summary: Update Sales Fee rule
      tags:
        - salesfee
      requestBody:
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.sales.fee.rules.common.api.UpdateFeeRuleRequest}
  /api/v1/sales-fee-rules/{id}:
    x-kong-route-defaults:
      regex_priority: -20
    x-scopes: [admin]
    get:
      summary: Get a single Sales Fee Rule
      tags:
        - salesfee
      parameters:
        - name: _
          in: query
          required: false
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.CacheBustParameter}
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.Symbol}
      responses:
        200:
          description: Single snapshot of sales fee rule history
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.sales.fee.rules.common.SalesFeeRule}
    delete:
      summary: Remove a single Sales Fee Rule
      tags:
        - salesfee
      parameters:
        - name: _
          in: query
          required: false
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.CacheBustParameter}
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.Symbol}
  /v1/sales-fee-rules/{id}/snapshots:
    x-scopes: [admin]
    get:
      summary: Get history of sales fee rule updates
      tags:
        - salesfee
      parameters:
        - name: _
          in: query
          required: false
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.CacheBustParameter}
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.Symbol}
      responses:
        200:
          description: Changelog of sales fee rule changes
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.networks.common.ChangelogItem}
  /api/v1/sales-fee-rules/{ruleId}/snapshots/{id}:
    get:
      x-scopes: [admin]
      summary: Get a single snapshot in history of sales fee rule updates
      tags:
        - salesfee
      parameters:
        - name: _
          in: query
          required: false
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.CacheBustParameter}
        - name: ruleId
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.Symbol}
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.Symbol}
      responses:
        200:
          description: Single snapshot of sales fee rule history
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.sales.fee.rules.common.SalesFeeRule}

  /api/v1/sales-fee-rules/list:
    x-scopes: [ admin ]
    post:
      summary: List sales fee rules
      tags:
        - salesfee
      requestBody:
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.sales.fee.rules.common.api.ListFeeRulesRequest}
      responses:
        200:
          description: Sales fee rules
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ${com.simonmarkets.sales.fee.rules.common.SalesFeeRule}
  /api/v2/sales-fee-calculator:
    x-scopes:
      - admin
    get:
      summary: Get a Sales Fee Estimate
      x-kong-route-defaults:
        regex_priority: 201
      tags:
        - salesfee
      parameters:
        - name: networkId
          in: query
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
        - name: issuerKey
          in: query
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.Symbol}
        - name: contractTypeWrapper
          in: query
          required: true
          schema:
            $ref: ${com.simonmarkets.sales.fee.rules.common.api.SalesFeeRulesOpenApiDefinitions.ContractTypeWrapper}
        - name: callable
          in: query
          required: false
          schema:
            type: boolean
        - name: fullyProtected
          in: query
          required: false
          schema:
            type: boolean
        - name: hasSinglePayment
          in: query
          required: false
          schema:
            type: boolean
      responses:
        200:
          description: Sales fee estimate
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.sales.fee.rules.models.Calculation}
    post:
      summary: Perform a dry run of a sales fee calculation
      x-kong-route-defaults:
        regex_priority: 201
      tags:
        - salesfee
      requestBody:
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.sales.fee.rules.models.SalesFeeCalculatorDryRunRequest}
      responses:
        200:
          description: Sales fee estimate
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.sales.fee.rules.models.Calculation}
  /api/v2/sales-fee-calculator/query:
    x-scopes:
      - admin
    post:
      summary: Get a batch of Sales Fee Estimates
      x-kong-route-defaults:
        regex_priority: 201
      tags:
        - salesfee
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: ${com.simonmarkets.sales.fee.rules.models.EstimateV2Request}
      responses:
        200:
          description: Calculated sales fee estimates including successes and failures
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.sales.fee.rules.models.EstimateV2BatchResponse}
