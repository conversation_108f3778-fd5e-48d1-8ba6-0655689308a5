service: serverless-sales-fee-rules

provider:
  stage: ${self:custom.file.provider.environment}
  name: aws
  runtime: java11
  region: us-east-1
  profile: ${self:custom.file.provider.profile}
  versionFunctions: false
  deploymentBucket:
    name: simon-dev-local-serverless

custom:
  localstack:
    stages:
      - dev-local
    host: http://localstack-svc.${env:OKTETO_NAMESPACE}
    edgePort: 4566
    debug: true
  file: ${file(../config/okteto.yml)}
  warmup:
    default:
      enabled: true
      concurrency: 1
      prewarm: true
      events:
        - schedule: cron(0/2 8-23 ? * MON-FRI *)  # run the warmup every 2 min Mon- Fri 08:00 to 23:00 (EST)
      timeout: 300
      vpc: false
  newRelic:
    javaNewRelicHandler: handleStreamsRequest

plugins:
  - serverless-localstack
  - serverless-deployment-bucket
  - serverless-plugin-warmup

package:
  artifact: target/serverless-sales-fee-rules-uber.jar

functions:
  api:
    handler: com.simonmarkets.sales.fee.rules.SalesFeeRulesApp$EntryPoint$
    reservedConcurrency: 1
    # memorySize: 1024
    timeout: 300
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=okteto.conf
