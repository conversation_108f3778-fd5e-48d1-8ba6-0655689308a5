service: serverless-sales-fee-rules

provider:
  stage: ${opt:stage, 'sandbox'}
  name: aws
  runtime: java11
  region: us-east-1
  profile: ${self:custom.file.provider.profile}
  iam:
    role: ${self:custom.file.provider.iam.role}
  versionFunctions: false
  tracing:
    lambda: true
  vpc:
    securityGroupIds: ${self:custom.file.provider.securityGroupIds}
    subnetIds: ${self:custom.file.provider.subnetIds}
  deploymentBucket:
    name: simon-${self:provider.profile}-serverless
  logRetentionInDays: ${self:custom.file.logRetentionInDays}

custom:
  file: ${file(../config/${opt:stage}.yml)}
  logSubscription: ${self:custom.file.logSubscription}
  warmup:
    default:
      enabled: true
      concurrency: 15
      prewarm: true
      events:
        - schedule: cron(0/2 8-23 ? * MON-FRI *)  # run the warmup every 2 min Mon- Fri 08:00 to 23:00 (EST)
      timeout: 60
      vpc: false
      role: ${self:provider.iam.role}
  newRelic:
    javaNewRelicHandler: handleStreamsRequest

plugins:
  - serverless-plugin-log-subscription
  - serverless-plugin-warmup


package:
  artifact: target/serverless-sales-fee-rules-uber.jar

functions:
  api:
    handler: com.simonmarkets.sales.fee.rules.SalesFeeRulesApp$EntryPoint$
    reservedConcurrency: 10
    memorySize: 1024
    timeout: 60
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=${opt:stage}.conf ${self:custom.file.proxySystemProperty, ''}