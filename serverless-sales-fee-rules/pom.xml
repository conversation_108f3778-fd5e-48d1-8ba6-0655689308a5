<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>users-networks</artifactId>
        <groupId>com.simonmarkets</groupId>
        <version>sandbox-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>serverless-sales-fee-rules</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-useracl-directive-resteasy</artifactId>
            <version>sandbox-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-logging-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets.resteasy</groupId>
            <artifactId>lib-resteasy-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.typesafe.akka</groupId>
            <artifactId>akka-http_2.12</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-acl-client-resteasy</artifactId>
            <version>sandbox-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-sales-fee-rules-common</artifactId>
            <version>sandbox-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-networks-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.simonmarkets</groupId>
                    <artifactId>lib-users</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- TEST DEPENDENCIES-->
        <dependency>
            <groupId>com.typesafe.akka</groupId>
            <artifactId>akka-testkit_${scala.version.short}</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-users-test</artifactId>
            <version>sandbox-SNAPSHOT</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets.utils</groupId>
            <artifactId>lib-utils-testkit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
                <configuration>
                    <args>
                        <arg>-Xmacro-settings:resourcePath=${openapi.resourcePath},templatePath=${openapi.template},genOkteto=${openapi.genOkteto}</arg>
                    </args>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>${maven-shade-plugin.version}</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <shadedArtifactAttached>true</shadedArtifactAttached>
                            <shadedClassifierName>uber</shadedClassifierName>
                            <minimizeJar>true</minimizeJar>
                            <shadedArtifactAttached>true</shadedArtifactAttached>
                            <shadedClassifierName>uber</shadedClassifierName>
                            <filters combine.children="append">
                                <filter>
                                    <artifact>org.scala-lang:scala-library</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>com.typesafe.akka:akka-testkit_${scala.version.short}</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>com.typesafe.akka:akka-actor_${scala.version.short}</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>com.typesafe.akka:akka-stream_${scala.version.short}</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>ch.qos.logback:*</artifact>
                                    <includes>**</includes>
                                </filter>
                                <filter>
                                    <artifact>ch.qos.logback.contrib:*</artifact>
                                    <includes>**</includes>
                                </filter>
                                <filter>
                                    <artifact>com.simonmarkets:lib-logging-core</artifact>
                                    <includes>**</includes>
                                </filter>
                                <filter>
                                    <artifact>com.simonmarkets.logging:*</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>commons-logging:commons-logging</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>org.mongodb:mongo-java-driver</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                            </filters>
                            <!-- There are multiple reference.conf files in separate jars. The following should merge them -->
                            <!-- Source: https://doc.akka.io/docs/akka/current/additional/packaging.html#maven-jarjar-onejar-or-assembly -->
                            <artifactSet>
                                <includes>
                                    <include>*:*</include>
                                </includes>
                            </artifactSet>
                            <transformers>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                    <resource>reference.conf</resource>
                                </transformer>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <manifestEntries>
                                        <Main-Class>
                                            com.simonmarkets.sales.fee.rules.SalesFeeRulesApp$EntryPoint
                                        </Main-Class>
                                    </manifestEntries>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.coderplus.maven.plugins</groupId>
                <artifactId>copy-rename-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>de.smartics.maven.plugin</groupId>
                <artifactId>buildmetadata-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>io.github.git-commit-id</groupId>
                <artifactId>git-commit-id-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>assembly.xml</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>app-config/config</directory>
                <targetPath>app-config/config</targetPath>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

</project>
