package com.simonmarkets.mongodb.trigger.codec

import com.simonmarkets.circe.{CirceDecoders, CirceEncoders}
import io.circe.Decoder
import io.circe.generic.extras.{AutoDerivation, Configuration}

import java.time.{LocalDateTime, OffsetDateTime}

import scala.util.{Failure, Success, Try}

trait MongoTriggerJsonCodecs extends CirceEncoders with CirceDecoders with AutoDerivation {
  implicit val circeConfig: Configuration = Configuration.default.withDefaults
  //mongo sends the LocalDatetime fields as OffsetDateTime
  implicit def offsetDateTimeDecoder: Decoder[OffsetDateTime] = {
    Decoder.decodeString.map(OffsetDateTime.parse(_))
  }

  /**
   * Decodes json map into Map[String, Json]
   * The values can be decoded with another Decoder if the type of the value is known
   */
  implicit val decodeParam: Decoder[Map[String, Any]] = Decoder.decodeJsonObject.map { x => x.toMap }


  override implicit def localDateTimeDecoder[A <: LocalDateTime]: Decoder[LocalDateTime] = {
    offsetDateTimeDecoder.map(_.toLocalDateTime) or super.localDateTimeDecoder
  }

  def parseEvent[T](rawEvent: String)(implicit decoder: Decoder[T]): Try[T] = {
    decode[T](rawEvent) match {
      case Left(error) => Failure(new Exception(s"Unable to parse event: $rawEvent \n$error"))
      case Right(value) => Success(value)
    }
  }

}
