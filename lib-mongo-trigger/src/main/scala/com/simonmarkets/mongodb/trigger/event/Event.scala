package com.simonmarkets.mongodb.trigger.event

import com.simonmarkets.util.{EnumEntry, ProductEnums}

import java.time.OffsetDateTime

sealed trait Event

// see documentation here:  https://docs.mongodb.com/manual/reference/change-events/
case class MongoEvent[T](
    id: String,
    time: OffsetDateTime,
    detail: Payload[T]
)

case class UserSyncEvent(
    detail: SingleUser
) extends Event

case class SingleUser(
    userId: String
)

case class NetworkMfaSyncEvent(
    detail: NetworkMfaSyncEventDetail
) extends Event

case class NetworkMfaSyncEventDetail(
    userId: String,
    groupId: String,
    operation: MfaOperation
)

case class MongoEventWithoutDocument(
    id: String,
    time: OffsetDateTime,
    detail: PayloadWithoutDocument
) extends Event

case class PayloadWithoutDocument(
    operationType: String,
    ns: MongoNs
)

case class Payload[T](
    operationType: String,
    fullDocument: Option[T],
    updateDescription: Option[UpdateDescription],
    ns: MongoNs
)

case class MongoNs(
    db: String,
    coll: String
)

case class UpdateDescription(
    // In current use cases we don't care about values, we need only keys to understand what was changed
    updatedFields: Map[String, Any]
)

object OperationType {
  val INSERT = "insert"
  val REPLACE = "replace"
  val UPDATE = "update"
  val DELETE = "delete"
}

sealed trait MfaOperation extends EnumEntry

object MfaOperation extends ProductEnums[MfaOperation] {

  case object add extends MfaOperation

  case object remove extends MfaOperation

  override def Values: Seq[MfaOperation] = add :: remove :: Nil
}