<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.simonmarkets</groupId>
    <artifactId>users-networks</artifactId>
    <version>sandbox-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>SIMON User and Network Service (Parent)</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <simon.bom.version>bill-of-material-68_0_0-1670453353</simon.bom.version>
        <simon.db-migration.version>db-migrations-1_0_0-953654582</simon.db-migration.version>
        <simon.openapi-generator.version>lib-openapi-gen-86_0_0-1284684753</simon.openapi-generator.version>
        <simon.utils.version>utils-1_0_1-1782493932</simon.utils.version>
        <simon.resteasy.version>resteasy-2_0_0-1392589009</simon.resteasy.version>
        <simon.resteasy.experimental.version>resteasy-001_0_0-1425165204</simon.resteasy.experimental.version>
        <rules-engine.version>rules-engine-1_0_0-1520785364</rules-engine.version>
        <simon.ui-assets.version>ui-static-asset-management-1_0_0-951002042</simon.ui-assets.version>
        <version.zio>2.0.15</version.zio>
        <version.testcontainers>0.40.15</version.testcontainers>
        <scala.version.short>2.12</scala.version.short>
        <scala.version>2.12.13</scala.version>
        <scala-maven-plugin.version>4.9.2</scala-maven-plugin.version>
        <maven-shade-plugin.version>3.2.1</maven-shade-plugin.version>
        <scalatest-maven-plugin.version>2.0.0</scalatest-maven-plugin.version>
        <maven-surefire-plugin.version>2.20.1</maven-surefire-plugin.version>
        <maven-resources-plugin.version>3.0.2</maven-resources-plugin.version>
        <copy-rename-maven-plugin.version>1.0</copy-rename-maven-plugin.version>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <maven-jar-plugin.version>3.2.0</maven-jar-plugin.version>

        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>

        <openapi.resourcePath>${project.basedir}/src/main/resources/documentation/</openapi.resourcePath>
        <openapi.template>${openapi.resourcePath}template.yaml</openapi.template>
        <openapi.genOkteto>true</openapi.genOkteto>
        <openapi.output>${project.build.directory}/</openapi.output>

        <okta.version>13.0.0</okta.version>
        <okta-commons.version>1.3.5</okta-commons.version>
        <quant-common.version>quant-common-1_0_0-947855771</quant-common.version>
        <scala-maven-plugin.version>3.2.2</scala-maven-plugin.version>
        <scalatest-maven-plugin.version>2.0.0</scalatest-maven-plugin.version>
        <maven-surefire-plugin.version>2.20.1</maven-surefire-plugin.version>
        <timestamp>${maven.build.timestamp}</timestamp>
        <maven.build.timestamp.format>yyyy-MM-dd'T'HH:mm:ss.SSSZ</maven.build.timestamp.format>
    </properties>

    <modules>
        <module>db-migration</module>
        <module>lib-acl-client-resteasy</module>
        <module>lib-networks-client</module>
        <module>lib-okta</module>
        <module>lib-ssn-hash-client</module>
        <module>lib-users-resteasy</module>
        <module>lib-legacy-users-networks-repository</module>
        <module>lib-users-client</module>
        <module>lib-users-test</module>
        <module>lib-mongo-trigger</module>
        <module>simon-entitlements</module>
        <module>serverless-upload-network-hierarchy</module>
        <module>service-okta-sync</module>
        <module>serverless-global-rejections-event-handler</module>
        <module>lib-rejection-reasons</module>
        <module>service-users</module>
        <module>service-networks</module>
        <module>lib-users-common</module>
        <module>lib-networks-common</module>
        <module>service-user-acl</module>
        <module>serverless-sales-fee-rules</module>
        <module>lib-sales-fee-rules-common</module>
        <module>service-tasks</module>
        <module>om-networks</module>
        <module>om-sales-fee-rules</module>
        <module>lib-useracl-directive-resteasy</module>
        <!-- This is not live so safe to comment for now. To be uncommented after some refactor
                <module>service-okta-events</module>
        -->
        <module>service-product-feature-sets</module>
        <module>lib-sales-fee-rules-engine</module>
        <module>service-fee-rules-engine</module>
        <module>service-user-sync-mapping-validator</module>
        <module>lib-oauth-client</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>third-party-bill-of-material</artifactId>
                <version>${simon.bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.scalaland</groupId>
                <artifactId>chimney_${scala.version.short}</artifactId>
                <version>0.6.1</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets.resteasy</groupId>
                <artifactId>lib-resteasy-authn</artifactId>
                <version>${simon.resteasy.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-caching</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-logging-core</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-logging-lambda</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-config</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets.utils</groupId>
                <artifactId>lib-data-mongo</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.goldmansachs.marquee</groupId>
                <artifactId>lib-encryption</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-utils-akkahttp</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>io.simon</groupId>
                <artifactId>lib-openapi-generator</artifactId>
                <version>${simon.openapi-generator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-utils-scala</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-utils-entitlements-macros</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-mongodb</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-common</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets.quantcommon</groupId>
                <artifactId>om-contract-params</artifactId>
                <version>${quant-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-http-client</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-asset-models</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-utils-mongo-casbah</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <!-- FIXME get rid of this -->
            <dependency>
                <groupId>com.simonmarkets.quantcommon</groupId>
                <artifactId>lib-date</artifactId>
                <version>${quant-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-dynamodb</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-circe-extensions</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-task-processing</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-task-processing-mongo-scala</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-eventbridge</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets.dbmigrations</groupId>
                <artifactId>lib-db-migration-mongo</artifactId>
                <version>${simon.db-migration.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets.resteasy</groupId>
                <artifactId>lib-resteasy-core</artifactId>
                <version>${simon.resteasy.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mongodb</groupId>
                        <artifactId>mongo-java-driver</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets.ui</groupId>
                <artifactId>lib-ui-assets</artifactId>
                <version>${simon.ui-assets.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.zio</groupId>
                <artifactId>zio_${scala.version.short}</artifactId>
                <version>${version.zio}</version>
            </dependency>
            <dependency>
                <groupId>com.okta.sdk</groupId>
                <artifactId>okta-sdk-api</artifactId>
                <version>${okta.version}</version>
            </dependency>
            <dependency>
                <groupId>com.okta.sdk</groupId>
                <artifactId>okta-sdk-impl</artifactId>
                <version>${okta.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>com.okta.sdk</groupId>
                <artifactId>okta-http-httpclient</artifactId>
                <version>${okta-commons.version}</version>
                <scope>runtime</scope>
            </dependency>
            <!-- TEST DEPENDENCIES-->
            <dependency>
                <groupId>com.gs.marquee.pipg</groupId>
                <artifactId>test-support</artifactId>
                <version>${simon.utils.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.gs.marquee.pipg</groupId>
                <artifactId>test-support-common</artifactId>
                <version>${simon.utils.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets.utils</groupId>
                <artifactId>lib-utils-testkit</artifactId>
                <version>${simon.utils.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.5.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets.utils</groupId>
                <artifactId>scala-mongo-embedded-test</artifactId>
                <version>${simon.utils.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.dimafeng</groupId>
                <artifactId>testcontainers-scala-kafka_${scala.version.short}</artifactId>
                <version>${version.testcontainers}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.dimafeng</groupId>
                <artifactId>testcontainers-scala-scalatest_${scala.version.short}</artifactId>
                <version>${version.testcontainers}</version>
                <scope>test</scope>
            </dependency>

            <!--local dependencies-->
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-users-client</artifactId>
                <version>sandbox-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-networks-client</artifactId>
                <version>sandbox-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-okta</artifactId>
                <version>sandbox-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>om-networks</artifactId>
                <version>sandbox-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-users-common</artifactId>
                <version>sandbox-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-acl-client</artifactId>
                <version>sandbox-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-acl-client-resteasy</artifactId>
                <version>sandbox-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mongodb</groupId>
                        <artifactId>mongo-java-driver</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-ssn-hash-client</artifactId>
                <version>sandbox-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-authn-akkahttp</artifactId>
                <version>${simon.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-users</artifactId>
                <version>sandbox-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mongodb</groupId>
                        <artifactId>casbah_${scala.version.short}</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.hazelcast</groupId>
                        <artifactId>hazelcast-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.goldmansachs.marquee</groupId>
                <artifactId>simon-entitlements</artifactId>
                <version>sandbox-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-networks-common</artifactId>
                <version>sandbox-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>service-users</artifactId>
                <version>sandbox-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-mongo-trigger</artifactId>
                <version>sandbox-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.simonmarkets</groupId>
                <artifactId>lib-useracl-directive-resteasy</artifactId>
                <version>sandbox-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>net.alchim31.maven</groupId>
                    <artifactId>scala-maven-plugin</artifactId>
                    <version>${scala-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>add-source</goal>
                                <goal>compile</goal>
                                <goal>testCompile</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <jvmArgs>
                            <jvmArg>-Xss12m</jvmArg>
                        </jvmArgs>
                        <args>
                            <arg>-Xmacro-settings:resourcePath=${openapi.resourcePath},templatePath=${openapi.template},genOkteto=${openapi.genOkteto}</arg>
                            <arg>-deprecation</arg>
                            <arg>-feature</arg>
                            <arg>-language:experimental.macros</arg>
                            <arg>-language:higherKinds</arg>
                            <arg>-unchecked</arg>
                            <arg>-Xfatal-warnings</arg>
                            <arg>-Xlint:adapted-args</arg>
                            <arg>-Xlint:by-name-right-associative</arg>
                            <arg>-Xlint:constant</arg>
                            <arg>-Xlint:delayedinit-select</arg>
                            <arg>-Xlint:doc-detached</arg>
                            <arg>-Xlint:inaccessible</arg>
                            <arg>-Xlint:infer-any</arg>
                            <arg>-Xlint:missing-interpolator</arg>
                            <arg>-Xlint:nullary-override</arg>
                            <arg>-Xlint:nullary-unit</arg>
                            <arg>-Xlint:option-implicit</arg>
                            <arg>-Xlint:package-object-classes</arg>
                            <arg>-Xlint:poly-implicit-overload</arg>
                            <arg>-Xlint:private-shadow</arg>
                            <arg>-Xlint:stars-align</arg>
                            <arg>-Xlint:type-parameter-shadow</arg>
                            <arg>-Xlint:unsound-match</arg>
                            <arg>-Yno-adapted-args</arg>
                            <arg>-Ypartial-unification</arg>
                            <arg>-Ywarn-dead-code</arg>
                            <arg>-Ywarn-extra-implicit</arg>
                            <arg>-Ywarn-inaccessible</arg>
                            <arg>-Ywarn-infer-any</arg>
                            <arg>-Ywarn-nullary-override</arg>
                            <arg>-Ywarn-nullary-unit</arg>
                            <arg>-Ywarn-macros:after</arg>
                            <arg>-Ywarn-unused:implicits</arg>
                            <arg>-Ywarn-unused:imports</arg>
                            <arg>-Ywarn-unused:locals</arg>
                            <arg>-Ywarn-unused:params</arg>
                            <arg>-Ywarn-unused:patvars</arg>
                            <arg>-Ywarn-unused:privates</arg>
                        </args>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.coderplus.maven.plugins</groupId>
                    <artifactId>copy-rename-maven-plugin</artifactId>
                    <version>${copy-rename-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>copy-file</id>
                            <phase>package</phase>
                            <goals>
                                <goal>copy</goal>
                            </goals>
                            <configuration>
                                <sourceFile>target/${project.artifactId}-${project.version}-uber.${project.packaging}</sourceFile>
                                <destinationFile>target/${project.artifactId}-uber.${project.packaging}</destinationFile>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven-resources-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>copy-resources</id>
                            <phase>process-classes</phase>
                            <goals>
                                <goal>copy-resources</goal>
                            </goals>
                            <configuration>
                                <outputDirectory>${openapi.output}</outputDirectory>
                                <resources>
                                    <resource>
                                        <directory>${openapi.resourcePath}</directory>
                                        <filtering>false</filtering>
                                    </resource>
                                </resources>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.scalatest</groupId>
                    <artifactId>scalatest-maven-plugin</artifactId>
                    <version>${scalatest-maven-plugin.version}</version>
                    <configuration>
                        <reportsDirectory>${project.build.directory}/surefire-reports</reportsDirectory>
                        <junitxml>.</junitxml>
                        <stdout>G</stdout>
                        <tagsToExclude>simon.testsupport.tags.Manual,org.scalatest.tags.Slow</tagsToExclude>
                        <parallel>false</parallel>
                        <argLine>-Djava.util.logging.config.file=src/test/resources/logging.properties
                            -Dlogback.debug=false
                            -Dlogback.statusListenerClass=ch.qos.logback.core.status.NopStatusListener
                        </argLine>
                    </configuration>
                    <executions>
                        <execution>
                            <id>test</id>
                            <goals>
                                <goal>test</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <skipTests>true</skipTests>
                        <!-- DISABLE -->
                        <printSummary>true</printSummary>
                        <includes>
                            <include>**/Test*.class</include>
                            <include>**/*Test.class</include>
                            <include>**/*TestCase.class</include>
                            <include>**/*Spec.class</include>
                        </includes>
                        <systemProperties>
                            <property>
                                <name>java.util.logging.config.file</name>
                                <value>src/test/resources/logging.properties</value>
                            </property>
                            <property>
                                <name>logback.debug</name>
                                <value>false</value>
                            </property>
                            <property>
                                <name>logback.statusListenerClass</name>
                                <value>ch.qos.logback.core.status.NopStatusListener</value>
                            </property>
                        </systemProperties>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.1.0</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <phase>package</phase>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>de.smartics.maven.plugin</groupId>
                    <artifactId>buildmetadata-maven-plugin</artifactId>
                    <version>1.6.1</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>provide-buildmetadata</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>io.github.git-commit-id</groupId>
                    <artifactId>git-commit-id-maven-plugin</artifactId>
                    <version>5.0.0</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>revision</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
