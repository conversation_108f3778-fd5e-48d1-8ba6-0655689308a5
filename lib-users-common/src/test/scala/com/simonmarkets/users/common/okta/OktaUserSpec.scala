package com.simonmarkets.users.common.okta

import com.goldmansachs.marquee.pipg.{AcordState, License}
import org.scalatest.Matchers._
import org.scalatest.WordSpec
import org.scalatest.mockito.MockitoSugar

class OktaUserSpec extends WordSpec with MockitoSugar with OktaUserSpecData {
  "OktaUser" should {

    /* User -> OktaUser */
    "be created from a network and user" in {
      OktaUserConversions.toOktaUser(user, customRoleConfig) shouldBe expectedOktaUser
    }

    "be created from a network and admin user" in {
      OktaUserConversions.toOktaUser(adminUser, customRoleConfig) shouldBe expectedAdminOktaUser
    }

    "handle reading and writing licenses" in {
      val licenses = List(
        License.NPN("123"),
        License.StateProducer("123", AcordState.DE))


      val result = licenses.map(OktaUserConversions.writeLicense)

      result should contain theSameElementsAs List("NPN:123:Annuities", "StateProducer:123:Annuities:DE")
    }
  }
}
