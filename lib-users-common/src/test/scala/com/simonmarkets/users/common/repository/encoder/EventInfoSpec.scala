package com.simonmarkets.users.common.repository.encoder

import com.simonmarkets.logging.TraceId
import com.simonmarkets.users.common.EventInfo
import com.simonmarkets.users.common.UserDomainEvent.UserUpdated
import org.scalatest.{Matchers, WordSpec}
import io.circe.parser.decode


class EventInfoSpec extends WordSpec with Matchers {


  val eventInfoJson =
    """{
      |		"eventType": "UserUpdated",
      |		"triggeredBy": "System",
      |		"correlationId": "545w50-asedf943-a32424",
      |		"duplicated": false
      |}""".stripMargin

  "EventInfoDecoder" should {
    "decode event info" in {
      val eventInfo = decode[EventInfo](eventInfoJson)

      eventInfo match {
        case Left(_) => assert(false)
        case Right(value) => value shouldBe EventInfo(UserUpdated, "System", TraceId("545w50-asedf943-a32424"))
      }
    }
  }
}
