package com.simonmarkets.users.common.okta

import com.goldmansachs.marquee.pipg.UserRole.{EqPIPGDeveloper, EqPIPGSIMONSystemUser}
import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, License, UserRole}
import com.simonmarkets.capabilities.{Capabilities, EndpointScopes}
import com.simonmarkets.okta.domain.OktaUser
import com.simonmarkets.users.common.{EventInfo, LoginMode, User, UserType}
import simon.Id.NetworkId

import java.time.LocalDateTime

trait OktaUserSpecData {

  val simonId = "id"
  val networkId = "networkId"
  val firstName = "firstName"
  val lastName = "lastName"
  val email = "email"
  val clientId = Some("clientId")
  val omsId = Some("omsId")
  val roles: Set[UserRole] = Set(EqPIPGDeveloper)
  val rolesSerialized: Set[String] = roles.map(_.name)
  val locations = Set("loc1", "loc2")
  val tradewebEligible = true
  val regSEligible = false
  val isActive = true
  val faNumbers = Set("123", "456")
  val customRoles = Set("ADMIN", "customRole2")
  val networkCode = "networkCode"
  val idpLoginId = s"$networkCode-$email"
  val loginId = idpLoginId

  val adminCustomRoles = Set("EQPipgAdmin")
  val licenses: Set[License] = Set(License.NPN("123"))
  val licensesSerialized: Set[String] = licenses.map(OktaUserConversions.writeLicense)
  val idpId = Some("123")

  val expectedEndpointCapabilities = Set("fa-manager", "issuer")
  val expectedAdminEndpointCapabilities = EndpointScopes.toSet

  val ssoNetworkId = "ssoNetworkId"
  val inferredSsoNetwork = "ssoExternalNetworkId"
  val ssoClientId = Some("ssoClientId")
  val ssoOmsId = Some("ssoOmsId")
  val ssoRoles: Set[UserRole] = Set(EqPIPGSIMONSystemUser)
  val ssoRolesSerialized: Set[String] = ssoRoles.map(_.name)
  val ssoLocations = Set("ssoLoc1", "ssoLoc2")
  val ssoTradewebEligible = true
  val ssoRegSEligible = true
  val ssoIsActive = true
  val ssoFaNumbers = Set("sso123", "sso456")
  val ssoCustomRoles = Set("ssoADMIN", "ssoCustomRole2")
  val ssoNpn: Option[String] = Some("123")
  val ssoEndpointCapabilities = Set("EqPIPGSIMONSystemUser")
  val ssoFaNumbersCommaSeparated = ssoFaNumbers.mkString(",")
  val ssoLocationsCommaSeparated = ssoLocations.mkString(",")
  val ssoCustomRolesCommaSeparated = ssoCustomRoles.mkString(",")

  val customRoleConfig = Set(
    CustomRoleDefinition("EQPipgAdmin", Set(Capabilities.Admin)),
    CustomRoleDefinition("ADMIN", Set(EndpointScopes.FaManagerScope)),
    CustomRoleDefinition("customRole2", Set("customCapability1", EndpointScopes.IssuerScope)),
    CustomRoleDefinition("customRole3", Set("customCapability5", "customCapability4")))

  val expectedEnpointCapabilities = Set("fa-manager", "issuer")

  //Okta Users
  val expectedOktaUser = OktaUser(simonId, idpId, firstName, lastName, email, isActive, networkId, customRoles,
    faNumbers, locations, omsId, clientId, tradewebEligible,
    regSEligible, rolesSerialized, licensesSerialized, expectedEndpointCapabilities, idpLoginId)

  val expectedAdminOktaUser = OktaUser(simonId, idpId, firstName, lastName, email, isActive, networkId, adminCustomRoles,
    faNumbers, locations, omsId, clientId, tradewebEligible,
    regSEligible, rolesSerialized, licensesSerialized, expectedAdminEndpointCapabilities, loginId)

  // Users

  val user = User(
    id = simonId,
    networkId = NetworkId(networkId),
    firstName = firstName,
    lastName = lastName,
    email = email,
    createdAt = LocalDateTime.now,
    createdBy = "test",
    updatedAt = LocalDateTime.now,
    updatedBy = "test",
    emailSentAt = None,
    emailSentBy = None,
    lastVisitedAt = Some(LocalDateTime.now),
    distributorId = clientId,
    omsId = omsId,
    roles = roles,
    locations = locations,
    tradewebEligible = tradewebEligible,
    regSEligible = regSEligible,
    isActive = isActive,
    faNumbers = faNumbers,
    customRoles = customRoles,
    licenses = licenses,
    idpId = idpId,
    idpLoginId = idpLoginId,
    userType = UserType.Human,
    loginMode = LoginMode.SSOAndUsernamePassword,
    eventInfo = EventInfo.Default,
    externalIds = Seq.empty,
  )

  val adminUser = User(
    id = simonId,
    networkId = NetworkId(networkId),
    firstName = firstName,
    lastName = lastName,
    email = email,
    createdAt = LocalDateTime.now,
    createdBy = "test",
    updatedAt = LocalDateTime.now,
    updatedBy = "test",
    emailSentAt = None,
    emailSentBy = None,
    lastVisitedAt = None,
    distributorId = clientId,
    omsId = omsId,
    roles = roles,
    locations = locations,
    tradewebEligible = tradewebEligible,
    regSEligible = regSEligible,
    isActive = isActive,
    faNumbers = faNumbers,
    customRoles = adminCustomRoles,
    licenses = licenses,
    idpId = idpId,
    idpLoginId = loginId,
    userType = UserType.Human,
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.SSOAndUsernamePassword,
    externalIds = Seq.empty,
  )

}
