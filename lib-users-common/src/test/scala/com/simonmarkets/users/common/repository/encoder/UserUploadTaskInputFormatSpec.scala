package com.simonmarkets.users.common.repository.encoder

import com.goldmansachs.marquee.pipg.UserRole
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.users.common.batch.{BatchOperation, UserUploadTaskInput}
import org.scalatest.{Matchers, WordSpec}
import simon.Id.ProcessingTaskId

import scala.concurrent.ExecutionContext

class UserUploadTaskInputFormatSpec extends WordSpec with Matchers {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "UserUploadTaskInputFormat" should {
    "encode and decode input" in {
      val input = UserUploadTaskInput(
        taskId = ProcessingTaskId("task-id"),
        row = 100,
        operation = BatchOperation.Add,

        email = "<EMAIL>",
        firstName = "First Name",
        lastName = "Last Name",
        network = "Network id",
        externalIds = Map("abc"->"123"),
        roles = Set(UserRole.EqPIPGFA),
        locations = Set("loc-1"),
        faNumbers = Set("fa-1", "fa-2"),
        customRoles = Set("custom role"),
        tradewebEligible = true,
        regSEligible = true,
        npn = Some("npn1"),
        crd = Some("crd1"),
        newEmail = Some("newEmail-a"),
        loginMode = Some(LoginMode.SSOAndUsernamePassword)
      )

      val encoded = UserUploadTaskInputFormat.writeAsync(input)
      val decoded = UserUploadTaskInputFormat.readAsync(encoded.await)

      decoded.await shouldBe input
    }

    "encode and decode input with empty fields" in {
      val input = UserUploadTaskInput(
        taskId = ProcessingTaskId("task-id"),
        row = 100,
        operation = BatchOperation.Add,
        email = "<EMAIL>",
        firstName = "First Name",
        lastName = "Last Name",
        network = "Network id",
        externalIds = Map("abc"->"123"),
        roles = Set.empty,
        locations = Set.empty,
        faNumbers = Set.empty,
        customRoles = Set.empty,
        tradewebEligible = true,
        regSEligible = true,
        npn = None,
        crd = None,
        newEmail = None,
        loginMode = Some(LoginMode.SSOAndUsernamePassword)
      )

      val encoded = UserUploadTaskInputFormat.writeAsync(input)
      val decoded = UserUploadTaskInputFormat.readAsync(encoded.await)

      decoded.await shouldBe input
    }
  }
}
