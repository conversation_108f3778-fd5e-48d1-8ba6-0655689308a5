package com.simonmarkets.users.common.repository.encoder

import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.users.common.batch.{BatchOperation, UserUploadFailed, UserUploadSucceed}
import org.scalatest.{Matchers, WordSpec}
import simon.Id.ProcessingTaskId

import scala.concurrent.ExecutionContext.Implicits.global

class UserUploadTaskOutputFormatSpec extends WordSpec with Matchers {
  "UserUploadTaskOutputFormat" should {
    "encode and decode success output" in {
      val successOutput = UserUploadSucceed(
        taskId = ProcessingTaskId("task-id"),
        row = 100,
        warning = "Some warning",
        email = "email",
        operation = BatchOperation.Add
      )

      val encoded = UserUploadTaskOutputFormat.writeAsync(successOutput)
      val decoded = encoded.flatMap(UserUploadTaskOutputFormat.readAsync)

      decoded.await shouldBe successOutput
    }

    "encode and decode failed output" in {
      val failedOutput = UserUploadFailed(
        taskId = ProcessingTaskId("task-id"),
        row = 100,
        error = "Something went wrong",
        email = "email",
        operation = BatchOperation.Add
      )

      val encoded = UserUploadTaskOutputFormat.writeAsync(failedOutput)
      val decoded = encoded.flatMap(UserUploadTaskOutputFormat.readAsync)

      decoded.await shouldBe failedOutput
    }
  }
}
