package com.simonmarkets.users.common.batch

import com.simonmarkets.taskprocessing.TasksManager.TaskOutput
import com.simonmarkets.util.{EnumEntry, ProductEnums}
import simon.Id.ProcessingTaskId

sealed trait UserUploadTaskOutput extends TaskOutput {

  val email: String
  val operation: BatchOperation

}

sealed trait UserUploadTaskOutputType extends EnumEntry

object UserUploadTaskOutputType extends ProductEnums[UserUploadTaskOutputType] {
  case object Success extends UserUploadTaskOutputType

  case object Failure extends UserUploadTaskOutputType

  lazy val Values: Seq[UserUploadTaskOutputType] = List(Success, Failure)
}

case class UserUploadSucceed(
    taskId: ProcessingTaskId,
    row: Long,
    warning: String,
    `type`: UserUploadTaskOutputType = UserUploadTaskOutputType.Success,
    email: String,
    operation: BatchOperation
) extends UserUploadTaskOutput

case class UserUploadFailed(
    taskId: ProcessingTaskId,
    row: Long,
    error: String,
    `type`: UserUploadTaskOutputType = UserUploadTaskOutputType.Failure,
    email: String,
    operation: BatchOperation
) extends UserUploadTaskOutput
