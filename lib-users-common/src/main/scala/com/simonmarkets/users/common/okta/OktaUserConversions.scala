package com.simonmarkets.users.common.okta

import com.goldmansachs.marquee.pipg._
import com.simonmarkets.capabilities.{Capabilities, EndpointScopes}
import com.simonmarkets.okta.BcryptHash
import com.simonmarkets.okta.domain.OktaUser
import com.simonmarkets.users.common.User
import simon.Id.NetworkId

object OktaUserConversions {

  def toOktaUser(
      user: User,
      customRolesConfig: Set[CustomRoleDefinition],
      externalIds: Map[String, String] = Map.empty,
      password: Option[BcryptHash] = None
  ): OktaUser = OktaUser(
    user.id,
    user.idpId,
    user.firstName,
    user.lastName,
    user.email,
    user.isActive,
    NetworkId.unwrap(user.networkId),
    user.customRoles,
    user.faNumbers,
    user.locations,
    user.omsId,
    user.distributorId,
    user.tradewebEligible,
    user.regSEligible,
    user.roles.map(_.name),
    user.licenses.map(writeLicense),
    getEndpointCapabilities(user, customRolesConfig),
    loginId = user.idpLoginId,
    accountInContext = user.accountInContext,
    contextAccountId = user.context.flatMap(_.accountId),
    contextProposalId = user.context.flatMap(_.proposalId),
    contextTaskId = user.context.flatMap(_.taskId),
    contextServiceRequestId = user.context.flatMap(_.serviceRequestId),
    contextLastUpdatedAt = user.context.flatMap(_.lastUpdatedAt.map(_.toString())),
    externalIds = externalIds,
    whiteLabelPartnerId = user.whiteLabelPartnerId,
    firmId = user.firmId,
    iCapitalUserId = user.iCapitalUserId,
    secondaryEmail = user.secondaryEmail,
    password = password
  )

  //helpers

  private def getEndpointCapabilities(user: User, customRolesConfig: Set[CustomRoleDefinition]) = {
    val endpointScopes = customRolesConfig.flatMap { customRoleDefinition =>
      if (user.customRoles.contains(customRoleDefinition.role))
        customRoleDefinition.capabilities else Set.empty[String]
    }.intersect(EndpointScopes.toSet)
    if (endpointScopes.contains(Capabilities.Admin)) {
      endpointScopes ++ EndpointScopes.toSet
    } else {
      endpointScopes
    }
  }

  private[common] def writeLicense(license: License): String = {
    s"${license.name.name}:${license.number}:${license.`type`.name}" + license.state.map(s => s":${s.name}").getOrElse("")
  }

}
