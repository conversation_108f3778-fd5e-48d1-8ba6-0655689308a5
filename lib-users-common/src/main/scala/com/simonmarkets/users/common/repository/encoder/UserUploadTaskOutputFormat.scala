package com.simonmarkets.users.common.repository.encoder

import com.simonmarkets.mongodb.bson.ScalaBsonDocumentsOps._
import com.simonmarkets.mongodb.bson.{AsyncFormat, NoImplicitToBsonDocumentConversion}
import com.simonmarkets.users.common.batch._
import org.mongodb.scala.bson.collection.Document
import simon.Id.ProcessingTaskId

import scala.concurrent.{ExecutionContext, Future}

object UserUploadTaskOutputFormat extends AsyncFormat[UserUploadTaskOutput] with NoImplicitToBsonDocumentConversion {

  object Fields {
    val TaskId = "taskId"
    val Row = "row"
    val Type = "type"
    val Warning = "warning"
    val Error = "error"
    val Email = "email"
    val Operation = "operation"
  }

  import Fields._

  override def writeAsync(x: UserUploadTaskOutput)(implicit ec: ExecutionContext): Future[Document] = {
    val input = x match {
      case success: UserUploadSucceed =>
        Document(
          Type -> success.`type`.toString,
          TaskId -> ProcessingTaskId.unwrap(success.taskId),
          Row -> success.row,
          Warning -> success.warning,
          Email -> success.email,
          Operation -> success.operation.productPrefix
        )

      case error: UserUploadFailed =>
        Document(
          Type -> error.`type`.toString,
          TaskId -> ProcessingTaskId.unwrap(error.taskId),
          Row -> error.row,
          Error -> error.error,
          Email -> error.email,
          Operation -> error.operation.productPrefix
        )
    }
    Future.successful(input)
  }

  override def readAsync(d: Document)(implicit ec: ExecutionContext): Future[UserUploadTaskOutput] = {
    val output = d.getEnum[UserUploadTaskOutputType](Type) match {
      case UserUploadTaskOutputType.Success =>
        UserUploadSucceed(
          `type` = UserUploadTaskOutputType.Success,
          taskId = ProcessingTaskId(d.getString(TaskId)),
          row = d.getLong(Row),
          warning = d.getString(Warning),
          email = d.getString(Email),
          operation = d.getEnum[BatchOperation](Operation)
        )
      case UserUploadTaskOutputType.Failure =>
        UserUploadFailed(
          `type` = UserUploadTaskOutputType.Failure,
          taskId = ProcessingTaskId(d.getString(TaskId)),
          row = d.getLong(Row),
          error = d.getString(Error),
          email = d.getString(Email),
          operation = d.getEnum[BatchOperation](Operation)
        )
    }
    Future.successful(output)
  }
}
