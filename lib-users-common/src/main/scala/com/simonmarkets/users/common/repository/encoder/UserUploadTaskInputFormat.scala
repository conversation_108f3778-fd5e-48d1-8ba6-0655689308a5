package com.simonmarkets.users.common.repository.encoder

import com.goldmansachs.marquee.pipg.UserRole
import com.simonmarkets.mongodb.bson.BsonValueDecoder.instances._
import com.simonmarkets.mongodb.bson.{AsyncFormat, NoImplicitToBsonDocumentConversion}
import com.simonmarkets.mongodb.bson.ScalaBsonDocumentsOps._
import com.simonmarkets.taskprocessing.TasksManager.TaskInput
import com.simonmarkets.users.common
import com.simonmarkets.users.common.batch
import com.simonmarkets.users.common.batch.{BatchOperation, UserUploadTaskInput}
import org.mongodb.scala.bson.collection.Document
import simon.Id.ProcessingTaskId

import scala.concurrent.{ExecutionContext, Future}

object UserUploadTaskInputFormat extends AsyncFormat[TaskInput] with NoImplicitToBsonDocumentConversion {

  object Fields {
    val TaskId = "taskId"
    val Row = "row"
    val Email = "email"
    val NewEmail = "newEmail"
    val FirstName = "firstName"
    val LastName = "lastName"
    val Network = "network"
    val ExternalIds = "externalIds"
    val Roles = "roles"
    val Locations = "locations"
    val FaNumbers = "faNumbers"
    val CustomRoles = "customRoles"
    val TradewebEligible = "tradewebEligible"
    val RegSEligible = "regSEligible"
    val Id = "id"
    val Npn = "npn"
    val Crd = "crd"
    val Operation = "operation"
    val LoginMode = "loginMode"
    val WhiteLabelPartnerId = "whiteLabelPartnerId"
    val FirmId = "firmId"
    val ICapitalUserId = "iCapitalUserId"
    val SecondaryEmail = "secondaryEmail"
  }


  override def writeAsync(ti: TaskInput)(implicit ec: ExecutionContext): Future[Document] = ti match {
    case x: UserUploadTaskInput =>
      import Fields._
      val doc = org.mongodb.scala.bson.collection.mutable.Document(
        TaskId -> ProcessingTaskId.unwrap(x.taskId),
        Row -> x.row,
        Email -> x.email,
        FirstName -> x.firstName,
        LastName -> x.lastName,
        Network -> x.network,
        ExternalIds -> Document(x.externalIds),
        Roles -> x.roles.map(_.name).toList,
        Locations -> x.locations.toList,
        FaNumbers -> x.faNumbers.toList,
        CustomRoles -> x.customRoles.toList,
        TradewebEligible -> x.tradewebEligible,
        RegSEligible -> x.regSEligible,
        Operation -> x.operation.productPrefix
      )

      x.newEmail.foreach(newEmail => doc.update(NewEmail, newEmail))
      x.loginMode.foreach(loginMode => doc.update(LoginMode, loginMode.productPrefix))
      x.npn.foreach(npn => doc.update(Npn, npn))
      x.crd.foreach(crd => doc.update(Crd, crd))
      x.firmId.foreach(firmId => doc.update(FirmId, firmId))
      x.whiteLabelPartnerId.foreach(id => doc.update(WhiteLabelPartnerId, id))
      x.iCapitalUserId.foreach(id => doc.update(ICapitalUserId, id))
      x.secondaryEmail.foreach(email => doc.update(SecondaryEmail, email))
      Future.successful(Document(doc.toBsonDocument))
  }

  override def readAsync(x: Document)(implicit ec: ExecutionContext): Future[TaskInput] = {
    import Fields._
    val input = batch.UserUploadTaskInput(
      taskId = ProcessingTaskId(x.getString(TaskId)),
      row = x.getLong(Row),
      email = x.getString(Email),
      firstName = x.getString(FirstName),
      lastName = x.getString(LastName),
      network = x.getString(Network),
      externalIds = x.getMap[String](ExternalIds),
      roles = x.getList[String](Roles).flatMap(UserRole.unapply).toSet,
      locations = x.getList[String](Locations).toSet,
      faNumbers = x.getList[String](FaNumbers).toSet,
      customRoles = x.getList[String](CustomRoles).toSet,
      tradewebEligible = x.getBoolean(TradewebEligible),
      regSEligible = x.getBoolean(RegSEligible),
      newEmail = x.getStringOpt(NewEmail),
      npn = x.getStringOpt(Npn),
      crd = x.getStringOpt(Crd),
      operation = x.getEnum[BatchOperation](Operation),
      loginMode = x.getStringOpt(LoginMode).map(common.LoginMode.apply),
      firmId = x.getStringOpt(FirmId),
      whiteLabelPartnerId = x.getStringOpt(WhiteLabelPartnerId),
      iCapitalUserId = x.getStringOpt(ICapitalUserId),
      secondaryEmail = x.getStringOpt(SecondaryEmail),
    )
    Future.successful(input)
  }

}
