package com.simonmarkets.users.common.api.response

import com.goldmansachs.marquee.pipg.User.DistributorInfo
import com.goldmansachs.marquee.pipg.{CustodianFaNumber, License, UserRole}
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.okta.domain.Factor
import com.simonmarkets.shared.MaskedId
import com.simonmarkets.users.common.User.{Distributor<PERSON>ey, OmsKey}
import com.simonmarkets.users.common.{Context, LandingPage, LoginMode, User, UserType}
import io.scalaland.chimney.dsl.TransformerOps
import simon.Id.NetworkId

import java.time.LocalDateTime

final case class UserView(
    id: String,
    networkId: NetworkId,
    createdAt: LocalDateTime,
    createdBy: String,
    updatedAt: LocalDateTime,
    updatedBy: String,
    emailSentAt: Option[LocalDateTime],
    emailSentBy: Option[String],
    lastVisitedAt: Option[LocalDateTime],
    email: String,
    firstName: String,
    lastName: String,
    externalIds: Map[String, String],
    tradewebEligible: Boolean,
    regSEligible: Boolean,
    roles: Set[UserRole],
    isActive: Boolean,
    locations: Set[String],
    faNumbers: Set[String],
    custodianFaNumbers: Set[CustodianFaNumber],
    customRoles: Set[String],
    licenses: Set[License],
    maskedIds: Set[MaskedId],
    idpId: Option[String],
    distributorInfo: Option[DistributorInfo],
    accountInContext: Option[String],
    context: Option[Context],
    cusips: Set[String],
    idpLoginId: String,
    loginMode: LoginMode,
    userType: UserType,
    purviewLicenses: Set[License],
    purviewNsccCodes: Set[String],
    clientSecret: Option[String],
    firmId: Option[String],
    whiteLabelPartnerId: Option[String],
    secondaryEmail: Option[String],
    iCapitalUserId: Option[String],
    userSyncedAt: Option[LocalDateTime],
    landingPage: Option[LandingPage],
    groups: Map[String, Set[String]],
    icnGroups: Set[String],
    icnRoles: Set[String],
    passport: Map[String, Int],
    mfas: Map[String, Factor]
)

object UserView {

  def apply(user: User, clientSecret: Option[String] = None): UserView =
    user
      .into[UserView]
      .withFieldComputed(
        _.externalIds,
        u =>
          ExternalId
            .toStringMap(u.externalIds) ++ Seq(u.distributorId.map(DistributorKey -> _), u.omsId.map(OmsKey -> _))
            .flatten
            .toMap
      )
      .withFieldConst(_.clientSecret, clientSecret)
      .transform

}
