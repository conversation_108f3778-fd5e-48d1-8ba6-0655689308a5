package com.simonmarkets.users.common

import cats.syntax.functor._
import com.simonmarkets.circe.CirceDecoders
import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.circe.Decoder
import io.circe.generic.extras.Configuration
import io.circe.generic.extras.semiauto.deriveConfiguredDecoder
import io.simon.openapi.annotation.Field.{AnyOf, EnumValues, Ref}
import io.simon.openapi.annotation.{ClassReference, Reference}

import scala.annotation.nowarn

@AnyOf(
  UpsertField.UpsertFieldEnumValues,
  UpsertField.ExternalIdRef
)
sealed trait UpsertField extends EnumEntry

object UpsertField extends ProductEnums[UpsertField] {

  sealed trait Keyed extends UpsertField

  case object Distributor extends UpsertField

  case object Oms extends UpsertField

  case object Email extends UpsertField

  case object UserId extends UpsertField

  case class ExternalId(subject: String) extends Keyed

  override def Values: Seq[UpsertField] = Seq(Distri<PERSON><PERSON>, O<PERSON>, Email, UserId)

  implicit val configuration: Configuration = Configuration.default.withDiscriminator("type")

  @nowarn("cat=deprecation")
  implicit val decoder: Decoder[UpsertField] = List[Decoder[UpsertField]](
    deriveConfiguredDecoder[Keyed].widen,
    CirceDecoders.productEnumDecoder[UpsertField].widen
  ).reduce(_ or _)

  @EnumValues(
    "Distributor", "distributor",
    "Oms", "oms",
    "Email", "email",
    "UserId", "userId"
  )
  object UpsertFieldEnumValues extends Reference

  @Ref(ClassReference(classOf[ExternalId]))
  object ExternalIdRef extends Reference

}
