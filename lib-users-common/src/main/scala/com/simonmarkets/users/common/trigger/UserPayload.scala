package com.simonmarkets.users.common.trigger

import com.goldmansachs.marquee.pipg.UserRole
import io.simon.openapi.annotation.Field.Type
import io.simon.openapi.annotation.OpenApiType
import simon.Id.NetworkId

import java.time.LocalDateTime

/**
 * Subset of fields of user which are not filtered by mongo trigger projection
 */
case class UserPayload(
    id: String,
    @Type(OpenApiType.String)
    networkId: NetworkId,
    createdAt: LocalDateTime,
    createdBy: String,
    updatedAt: LocalDateTime,
    updatedBy: String,
    email: String,
    firstName: String,
    lastName: String,
    roles: Set[UserRole],
    version: Int = 0
)
