package com.simonmarkets.users.common

import com.simonmarkets.util.{EnumEntry, SafeEnums}
import io.simon.openapi.annotation.Field.EnumValues

@EnumValues("SIMON", "ICN", "Unified")
sealed trait LandingPage extends EnumEntry

object LandingPage extends SafeEnums[LandingPage] {
  case object SIMON extends LandingPage
  case object ICN extends  LandingPage
  case object Unified extends LandingPage

  override def Values: Seq[LandingPage] = SIMON :: ICN :: Unified :: Nil

  override val EnumNotFound: LandingPage = SIMON
}
