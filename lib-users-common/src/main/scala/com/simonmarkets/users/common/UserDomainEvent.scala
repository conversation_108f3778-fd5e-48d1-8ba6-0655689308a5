package com.simonmarkets.users.common

import com.simonmarkets.util.{EnumEntry, SafeEnums}
import io.simon.openapi.annotation.Field.EnumValues

@EnumValues(
  "UserCreated",
  "UserCreatedViaJit",
  "UserUpdated",
  "UserLastVisitedUpdated",
  "UserActivated",
  "UserActivatedDontSendEmail",
  "UserPasswordReset",
  "UserStateTransitioned",
  "UserDeactivated",
  "UserAccountInContextUpdated",
  "UserLocationsUpdated",
  "UserLicensesUpdated",
  "UserFaNumbersUpdated",
  "UserEmailUpdated",
  "UserIdpIdUpdated",
  "UserMasterUserIdUpdated",
  "UserCusipsUpdated",
  "UserSynced",
  "UserLoginModeUpdated"
)
sealed trait UserDomainEvent extends EnumEntry

object UserDomainEvent extends SafeEnums[UserDomainEvent] {
  case object UserCreated extends UserDomainEvent
  case object UserCreatedViaJit extends UserDomainEvent
  case object UserUpdated extends UserDomainEvent
  case object UserLastVisitedUpdated extends UserDomainEvent
  case object UserActivatedSendEmail extends UserDomainEvent
  case object UserActivatedDontSendEmail extends UserDomainEvent
  case object UserPasswordReset extends UserDomainEvent
  case object UserStateTransitioned extends UserDomainEvent
  case object UserDeactivated extends UserDomainEvent
  case object UserAccountInContextUpdated extends UserDomainEvent
  case object UserLocationsUpdated extends UserDomainEvent
  case object UserCusipsUpdated extends UserDomainEvent
  case object UserLicensesUpdated extends UserDomainEvent
  case object UserFaNumbersUpdated extends UserDomainEvent
  case object UserEmailUpdated extends UserDomainEvent
  case object UserIdpIdUpdated extends UserDomainEvent
  case object UserMasterUserIdUpdated extends UserDomainEvent
  case object UserExternalIdsUpdated extends UserDomainEvent
  case object UserSynced extends UserDomainEvent
  case object UserLoginModeUpdated extends UserDomainEvent

  override def Values: Seq[UserDomainEvent] = Seq(
    UserCreated,
    UserCreatedViaJit,
    UserUpdated,
    UserLastVisitedUpdated,
    UserActivatedSendEmail,
    UserActivatedDontSendEmail,
    UserPasswordReset,
    UserStateTransitioned,
    UserDeactivated,
    UserAccountInContextUpdated,
    UserLocationsUpdated,
    UserLicensesUpdated,
    UserFaNumbersUpdated,
    UserEmailUpdated,
    UserIdpIdUpdated,
    UserMasterUserIdUpdated,
    UserExternalIdsUpdated,
    UserCusipsUpdated,
    UserSynced,
    UserLoginModeUpdated
  )

  override val EnumNotFound: UserDomainEvent = UserUpdated
}


