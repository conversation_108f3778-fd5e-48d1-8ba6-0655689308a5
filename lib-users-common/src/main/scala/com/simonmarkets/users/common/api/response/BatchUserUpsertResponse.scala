package com.simonmarkets.users.common.api.response

import com.goldmansachs.marquee.pipg.License
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.users.common.User
import com.simonmarkets.users.common.api.request.{UpsertUserRequest}

case class BatchUserUpsertResponse(success: Seq[BatchUser], failure: Seq[UserInsertFailure])

case class BatchUser(
    id: String,
    email: String,
    externalIds: Map[String, String],
    licenses: Set[License],
    href: String
)

object BatchUser {
  def fromUser(u: User): BatchUser = BatchUser(
    id = u.id,
    email = u.email,
    externalIds = ExternalId.toStringMap(u.externalIds),
    licenses = u.licenses,
    href = s"/v2/users/${u.id}"
  )
}

case class UserInsertFailure(request: UpsertUserRequest, errorCode: String, description: String)
