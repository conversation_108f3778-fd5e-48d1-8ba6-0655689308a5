package com.simonmarkets.users.common

import com.simonmarkets.util.{EnumEntry, SafeEnums}
import io.simon.openapi.annotation.Field.EnumValues

@EnumValues("SSO", "UsernamePassword", "Embedded", "SSOAndUsernamePassword",
  "ClientCredentials", "ICNUsernamePassword", "SSOAndICNUsernamePassword", "UnifiedPassword")
sealed trait LoginMode extends EnumEntry

object LoginMode extends SafeEnums[LoginMode] {

  case object SSO extends LoginMode
  case object UsernamePassword extends LoginMode
  case object Embedded extends LoginMode
  case object SSOAndUsernamePassword extends LoginMode
  case object ClientCredentials extends LoginMode
  case object ICNUsernamePassword extends LoginMode
  case object SSOAndICNUsernamePassword extends LoginMode
  case object UnifiedPassword extends LoginMode

  override def Values: Seq[LoginMode] = SSO :: UsernamePassword :: Embedded :: SSOAndUsernamePassword ::
    ClientCredentials :: SSOAndICNUsernamePassword :: ICNUsernamePassword :: UnifiedPassword :: Nil

  override val EnumNotFound: LoginMode = UsernamePassword
}
