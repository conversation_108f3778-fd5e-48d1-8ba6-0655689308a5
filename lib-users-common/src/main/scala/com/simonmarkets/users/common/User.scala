package com.simonmarkets.users.common

import com.goldmansachs.marquee.pipg.User.DistributorInfo
import com.goldmansachs.marquee.pipg.{CustodianFaNumber, License, UserRole, User => LegacyUser}
import com.simonmarkets.mongodb.snapshots.Snapshotable
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.okta.domain.Factor
import com.simonmarkets.shared.MaskedId
import io.simon.openapi.annotation.Field.Type
import io.simon.openapi.annotation.OpenApiType
import simon.Id.NetworkId

import java.time.LocalDateTime

case class User(
    id: String,
    @Type(OpenApiType.String)
    networkId: NetworkId,
    createdAt: LocalDateTime,
    createdBy: String,
    updatedAt: LocalDateTime,
    updatedBy: String,
    emailSentAt: Option[LocalDateTime],
    emailSentBy: Option[String],
    lastVisitedAt: Option[LocalDateTime],
    email: String,
    firstName: String,
    lastName: String,
    distributorId: Option[String],
    omsId: Option[String],
    tradewebEligible: Boolean = false,
    regSEligible: Boolean = false,
    isActive: Boolean = true,
    roles: Set[UserRole],
    entitlements: Set[String] = Set.empty,
    dynamicRoles: Set[String] = Set.empty,
    locations: Set[String] = Set.empty,
    faNumbers: Set[String] = Set.empty,
    custodianFaNumbers: Set[CustodianFaNumber] = Set.empty,
    customRoles: Set[String] = Set.empty,
    maskedIds: Set[MaskedId] = Set.empty,
    licenses: Set[License] = Set.empty,
    idpId: Option[String] = None,
    distributorInfo: Option[DistributorInfo] = None,
    accountInContext: Option[String] = None,
    context: Option[Context] = None,
    cusips: Set[String] = Set.empty,
    idpLoginId: String,
    version: Int = 0,
    loginMode: LoginMode,
    userType: UserType,
    eventInfo: EventInfo,
    externalIds: Seq[ExternalId],
    purviewLicenses: Set[License] = Set.empty,
    purviewNsccCodes: Set[String] = Set.empty,
    landingPage: Option[LandingPage] = None,
    firmId: Option[String] = None,
    whiteLabelPartnerId: Option[String] = None,
    secondaryEmail: Option[String] = None,
    iCapitalUserId: Option[String] = None,
    userSyncedAt: Option[LocalDateTime] = None,
    groups: Map[String, Set[String]] = Map.empty,
    icnGroups: Set[String] = Set.empty,
    icnRoles: Set[String] = Set.empty,
    passport: Map[String, Int] = Map.empty,
    mfas: Map[String, Factor] = Map.empty
) extends Snapshotable


object User {
  val DistributorKey = "distributor"
  val OmsKey = "oms"

  implicit class UserOps(val user: User) extends AnyVal {

    def active: Boolean = user.isActive && user.roles.nonEmpty

    def fullName: String = s"${user.firstName} ${user.lastName}".trim

    def reversedFullName: String = s"${user.lastName} ${user.firstName}".trim

    def isAdmin: Boolean = user.roles.contains(UserRole.EqPIPGGSAdmin)

    def isManager: Boolean = user.roles.contains(UserRole.EqPIPGFAManager)

    def isIssuer: Boolean = user.roles.contains(UserRole.Issuer)

    def isWholesaler: Boolean = user.roles.contains(UserRole.Wholesaler)

    def resetEntitlements(f: User => Set[String]) = user.copy(entitlements = f(user))

    def updateEntitlements(f: User => Set[String]) = user.copy(entitlements = user.entitlements ++ f(user))

    def refreshUpdatedFields(updater: String) = user.copy(updatedBy = updater, updatedAt = LocalDateTime.now)

    def withIdpId(id: Option[String]) = user.copy(idpId = id)

    def withoutFaNumbers: User = user.copy(faNumbers = Set.empty)

    def withoutCustodianFaNumbers: User = user.copy(custodianFaNumbers = Set.empty)

    def incrementVersion: User = user.copy(version = user.version + 1)

    def isPreviewUser: Boolean = user.userType == UserType.Preview

    def asLegacyUser: LegacyUser = LegacyUser(
      user.id,
      user.networkId,
      user.createdAt,
      user.createdBy,
      user.updatedAt,
      user.updatedBy,
      user.emailSentAt,
      user.emailSentBy,
      user.lastVisitedAt,
      user.email,
      user.firstName,
      user.lastName,
      user.distributorId,
      user.omsId,
      user.tradewebEligible,
      user.regSEligible,
      user.isActive,
      user.roles,
      user.entitlements,
      user.dynamicRoles,
      user.locations,
      user.faNumbers,
      user.custodianFaNumbers,
      user.customRoles,
      user.maskedIds,
      user.licenses,
      user.idpId,
      user.distributorInfo,
      user.accountInContext,
      user.context,
      user.cusips,
      user.idpLoginId,
      user.version,
      externalIds = user.externalIds,
      purviewLicenses = user.purviewLicenses,
      purviewNsccCodes = user.purviewNsccCodes,
      firmId = user.firmId,
      whiteLabelPartnerId = user.whiteLabelPartnerId,
      secondaryEmail = user.secondaryEmail,
      iCapitalUserId = user.iCapitalUserId,
      userSyncedAt = user.userSyncedAt,
      groups = user.groups,
      icnRoles = user.icnRoles,
      icnGroups = user.icnGroups,
      passport = user.passport,
    )
  }

  def buildIdpLoginId(networkCode: String, email: String, multiNetworkEnabled: Boolean): String =
    if (multiNetworkEnabled) s"$networkCode-$email" else email

}