package com.simonmarkets.users.common.api.request

import com.goldmansachs.marquee.pipg.{CustodianFaNumber, GroupIdType, License, UserRole}
import com.simonmarkets.networks.{ExternalId, NetworksDefinitions}
import com.simonmarkets.okta.domain.{Factor, FactorType}
import com.simonmarkets.syntax.anyOpsConversion
import com.simonmarkets.users.common.User.{Distributor<PERSON>ey, OmsKey}
import com.simonmarkets.users.common.{Context, LandingPage, LoginMode, UpsertField, User, UserType}
import io.scalaland.chimney.dsl.TransformerOps
import io.simon.openapi.annotation.ClassReference
import io.simon.openapi.annotation.Field.{Description, Ref, Required, TypeArgRef}
import io.simon.openapi.definitions.CommonDefinitions
import io.simon.openapi.definitions.CommonDefinitions.{FaNumber, NetworkName, UserId}
import simon.Id.NetworkId

case class UpsertUserRequest(
    @Required
    @Ref(NetworkName)
    networkId: NetworkId,
    @Required
    @Ref(CommonDefinitions.EmailAddress)
    email: String,
    @Required
    @Ref(CommonDefinitions.HumanName)
    firstName: String,
    @Required
    @Ref(CommonDefinitions.HumanName)
    lastName: String,
    @Required
    @TypeArgRef(NetworksDefinitions.ExternalId)
    externalIds: Map[String, String],
    tradewebEligible: Option[Boolean],
    regSEligible: Option[Boolean],
    @Required
    roles: Set[UserRole],
    @Description("Array of FA numbers")
    @TypeArgRef(FaNumber)
    faNumbers: Option[Set[String]] = None,
    @Description("Array of Custodian-linked FA numbers")
    custodianFaNumbers: Option[Set[CustodianFaNumber]] = None,
    @Description("Array of an organization's location identifiers")
    @TypeArgRef(CommonDefinitions.Location)
    locations: Option[Set[String]] = None,
    @Description("Array of custom defined roles for the user")
    @TypeArgRef(CommonDefinitions.CustomRole)
    customRoles: Option[Set[String]] = None,
    @Description("Array of licenses")
    licenses: Option[Set[License]] = None,
    @Ref(UserId)
    id: Option[String] = None,
    @Ref(CommonDefinitions.EntityID)
    idpId: Option[String] = None,
    @Description("Array of product ids user is entitled to")
    @TypeArgRef(CommonDefinitions.EntityID)
    cusips: Option[Set[String]] = None,
    isActive: Option[Boolean] = None,
    @TypeArgRef(ClassReference(classOf[LoginMode]))
    loginMode: Option[LoginMode] = None,
    @TypeArgRef(ClassReference(classOf[UserType]))
    userType: Option[UserType] = None,
    @Ref(CommonDefinitions.EntityID)
    accountInContext: Option[String] = None,
    @TypeArgRef(ClassReference(classOf[Context]))
    context: Option[Context] = None,
    @TypeArgRef(ClassReference(classOf[License]))
    @Description("Array of licenses user is entitled to")
    purviewLicenses: Option[Set[License]] = None,
    @Description("Array of nscc codes user is entitled to")
    purviewNsccCodes: Option[Set[String]] = None,
    @Description("Id of the iCapital user that this user is linked to")
    iCapitalUserId: Option[String] = None,
    @Description("Firm id of the iCapital User that this user is linked to")
    firmId: Option[String] = None,
    @Description("White label partner of the iCapital User that this user is linked to")
    whiteLabelPartnerId: Option[String] = None,
    secondaryEmail: Option[String] = None,
    upsertField: Option[UpsertField] = None,
    @TypeArgRef(ClassReference(classOf[LandingPage]))
    landingPage: Option[LandingPage] = None,
    @TypeArgRef(CommonDefinitions.SymbolArray)
    @Description("Sets of values user is entitled to")
    groups: Map[GroupIdType, Set[String]] = Map.empty,
    @TypeArgRef(CommonDefinitions.AlphaNumeric)
    @Description("User groups for access control")
    icnGroups: Option[Set[String]] = None,
    @Description("Roles for access control")
    icnRoles: Option[Set[String]] = None,
    mfas: Map[FactorType, Factor] = Map.empty,
)

object UpsertUserRequest {

  private def wrap[T](in: Set[T]): Option[Set[T]] =
    if (in.nonEmpty) Some(in)
    else None

  def fromUser(user: User): UpsertUserRequest = {
    user
      .into[UpsertUserRequest]
      .withFieldComputed(_.externalIds, user => {
        val legacyIds = List(
          user.distributorId.map(DistributorKey -> _),
          user.omsId.map(OmsKey -> _)
        ).flatten.toMap
        ExternalId.toStringMap(user.externalIds) ++ legacyIds
      })
      .withFieldComputed(_.tradewebEligible, _.tradewebEligible.some)
      .withFieldComputed(_.regSEligible, _.regSEligible.some)
      .withFieldComputed(_.faNumbers, _.faNumbers |> wrap)
      .withFieldComputed(_.custodianFaNumbers, _.custodianFaNumbers |> wrap)
      .withFieldComputed(_.customRoles, _.customRoles |> wrap)
      .withFieldComputed(_.licenses, _.licenses |> wrap)
      .withFieldComputed(_.cusips, _.cusips |> wrap)
      .withFieldComputed(_.locations, _.locations |> wrap)
      .withFieldComputed(_.purviewNsccCodes, _.purviewNsccCodes |> wrap)
      .withFieldComputed(_.purviewLicenses, _.purviewLicenses |> wrap)
      //admin required fields
      .withFieldComputed(_.icnRoles, _.icnRoles |> wrap)
      .withFieldComputed(_.icnGroups, _.icnGroups |> wrap)
      .withFieldComputed(_.groups, _.groups.flatMap { case (k, v) => GroupIdType.unapply(k).map(_ -> v) })
      .withFieldComputed(_.mfas, _.mfas.map { case (k, v) => FactorType(k) -> v })
      .transform
  }
}

case class GetUniqueUserIdRequest(
    id: Option[String],
    email: String,
    networkId: NetworkId,
    externalIds: Map[String, String]
)

object GetUniqueUserIdRequest {
  def apply(request: UpsertUserRequest): GetUniqueUserIdRequest = {
    GetUniqueUserIdRequest(
      request.id,
      request.email,
      request.networkId,
      request.externalIds
    )
  }
}
