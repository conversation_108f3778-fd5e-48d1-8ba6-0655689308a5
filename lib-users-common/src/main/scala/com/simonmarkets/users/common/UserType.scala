package com.simonmarkets.users.common

import com.simonmarkets.util.{EnumEntry, SafeEnums}
import io.simon.openapi.annotation.Field.EnumValues

@EnumValues("Human", "System", "Preview")
sealed trait UserType extends EnumEntry

object UserType extends SafeEnums[UserType] {

  case object Human extends UserType

  case object System extends UserType

  case object Preview extends UserType

  override def Values: Seq[UserType] = Human :: System :: Preview :: Nil

  override val EnumNotFound: UserType = Human
}
