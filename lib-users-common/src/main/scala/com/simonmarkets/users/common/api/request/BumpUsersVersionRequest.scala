package com.simonmarkets.users.common.api.request

import io.simon.openapi.annotation.Field.{Description, TypeArgRef}
import io.simon.openapi.definitions.CommonDefinitions
import io.simon.openapi.definitions.CommonDefinitions.UserId
import simon.Id.NetworkId

case class BumpUsersVersionRequest(
    @TypeArgRef(UserId)
    @Description("Bump version of provided user ids")
    userIds: Option[Set[String]],
    @TypeArgRef(CommonDefinitions.NetworkName)
    @Description("Bump version of all users in networks")
    networkIds: Option[Set[NetworkId]],
) extends RequestWithOptUserIds with RequestWithOptNetworkIds
