package com.simonmarkets.users.common

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.logging.TraceId


trait Event[T] {
  val eventType: T
  val triggeredBy: String
  val correlationId: TraceId
  val duplicated: Boolean
}


case class EventInfo(
    eventType: UserDomainEvent,
    triggeredBy: String,
    correlationId: TraceId,
    duplicated: Boolean = false) extends Event[UserDomainEvent]


object EventInfo {

  import io.circe._

  val NoOne = "NoOne"
  val Default = EventInfo(UserDomainEvent.UserUpdated, triggeredBy = NoOne, correlationId = TraceId.randomize)

  def apply(eventType: UserDomainEvent, requester: UserACL)(implicit traceId: TraceId): EventInfo = {
    EventInfo(eventType, triggeredBy = requester.userId, correlationId = traceId)
  }

  implicit val decoder: Decoder[EventInfo] = (c: HCursor) => for {
    eventType <- c.downField("eventType").as[String]
    triggeredBy <- c.downField("triggeredBy").as[String]
    traceId <- c.downField("correlationId").as[String]
    duplicated <- c.downField("duplicated").as[Boolean]
  } yield EventInfo(UserDomainEvent(eventType), triggeredBy, TraceId(traceId), duplicated)
}
