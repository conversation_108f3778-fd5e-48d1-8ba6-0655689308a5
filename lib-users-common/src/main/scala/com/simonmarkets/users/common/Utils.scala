package com.simonmarkets.users.common

import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.shared.MaskedId

import java.util.UUID

import scala.concurrent.Future

object Utils extends TraceLogging {
  def generateMaskedIdForTarget(target: String): MaskedId = MaskedId(target, UUID.randomUUID.toString)

  // Small DSL for the if(something) Future.failed(error) else Future.unit pattern
  def when(condition: Boolean): FailWith = new FailWith(condition)

  def whenNot(condition: Boolean): FailWith = new FailWith(!condition)

  class FailWith(val condition: Boolean) extends AnyVal {
    def failWith(httpError: => HttpError)(implicit traceId: TraceId): Future[Unit] =
      if (condition) {
        log.error(httpError.payload)
        httpError.future
      } else Future.unit
  }

}
