package com.simonmarkets.users.common.api.request

import io.simon.openapi.annotation.Field.{Description, TypeArgRef}
import io.simon.openapi.definitions.CommonDefinitions
import io.simon.openapi.definitions.CommonDefinitions.UserId
import simon.Id.NetworkId


trait RequestWithOptUserIds {
    val userIds: Option[Set[String]]
}

trait RequestWithOptNetworkIds {
    val networkIds: Option[Set[NetworkId]]
}

case class OktaSyncRequest(
    @TypeArgRef(UserId)
    @Description("Sync provided user ids")
    userIds: Option[Set[String]],
    @TypeArgRef(CommonDefinitions.NetworkName)
    @Description("Sync all users in networks")
    networkIds: Option[Set[NetworkId]],
    @Description("Sync all users in system")
    syncAll: Option[Boolean]
) extends RequestWithOptUserIds with RequestWithOptNetworkIds
