package com.simonmarkets.users.common.batch

import com.goldmansachs.marquee.pipg.UserRole
import com.simonmarkets.taskprocessing.TasksManager.TaskInput
import com.simonmarkets.users.common.LoginMode
import simon.Id.ProcessingTaskId

case class UserUploadTaskInput(
    taskId: ProcessingTaskId,
    row: Long,
    operation: BatchOperation,

    email: String,
    firstName: String,
    lastName: String,
    network: String,  // network name
    externalIds: Map[String, String],
    roles: Set[UserRole],
    locations: Set[String],
    faNumbers: Set[String],
    customRoles: Set[String],
    tradewebEligible: Boolean,
    regSEligible: Boolean,
    npn: Option[String],
    crd: Option[String],
    newEmail: Option[String],
    loginMode: Option[LoginMode],
    firmId: Option[String] = None,
    whiteLabelPartnerId: Option[String] = None,
    iCapitalUserId: Option[String] = None,
    secondaryEmail: Option[String] = None,
) extends TaskInput
