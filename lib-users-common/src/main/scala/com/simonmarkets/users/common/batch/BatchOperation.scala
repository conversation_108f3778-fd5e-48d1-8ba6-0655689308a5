package com.simonmarkets.users.common.batch

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues
@EnumValues("Add", "Edit", "Deactivate", "Reset-Password")
sealed trait BatchOperation extends EnumEntry

object BatchOperation extends ProductEnums[BatchOperation] {
  case object Add extends BatchOperation

  case object Edit extends BatchOperation

  case object Deactivate extends BatchOperation

  case object `Reset-Password` extends BatchOperation

  override def Values: Seq[BatchOperation] = Seq(Add, Edit, Deactivate, `Reset-Password`)

}
