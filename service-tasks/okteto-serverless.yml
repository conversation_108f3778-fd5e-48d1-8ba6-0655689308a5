service: service-tasks

provider:
  stage: ${self:custom.file.provider.environment}
  name: aws
  runtime: java11
  region: us-east-1
  profile: ${self:custom.file.provider.profile}
  versionFunctions: false
  deploymentBucket:
    name: simon-dev-local-serverless

custom:
  localstack:
    stages:
      - dev-local
    host: http://localstack-svc.${env:OKTETO_NAMESPACE}
    edgePort: 4566
    debug: true
  file: ${file(../config/okteto.yml)}
  service-users: "service-users"
  warmup:
    default:
      enabled: true
      concurrency: 1
      prewarm: true
      timeout: 300
      events:
        - schedule: rate(5 minutes)
      vpc: false
  newRelic:
    javaNewRelicHandler: handleStreamsRequest

plugins:
  - serverless-localstack
  - serverless-deployment-bucket
  - serverless-step-functions
  - serverless-plugin-warmup

package:
  artifact: target/service-tasks-uber.jar

functions:
  api:
    handler: com.simonmarkets.tasks.BatchAPIApp$EntryPoint$
    # memorySize: 2048
    timeout: 300
    warmup:
      default:
        enabled: true
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=okteto.conf

  start-task-processing:
    handler: com.simonmarkets.tasks.StartTaskProcessingApp::handleRequest
    # memorySize: 2048
    timeout: 300
    warmup:
      default:
        enabled: false
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=okteto.conf

  finish-task-processing:
    handler: com.simonmarkets.tasks.FinishTaskProcessingApp::handleRequest
    # memorySize: 2048
    timeout: 300
    warmup:
      default:
        enabled: false
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=okteto.conf

stepFunctions:
  stateMachines:
    users-tasks-processing:
      name: UsersTasksProcessing
      definition:
        Comment: Step function to processing generic tasks
        StartAt: StartTaskProcessingState
        States:
          # function which invokes state machine should pass `{"taskId":"<task id value>", "submitterId":"<id of user who submitted task>"}`
          # this stage should return `{"taskId": "<task id value>", "nextStage":"<mandatory task type>", "batches":[{"from": 0, "to": 10}]}
          # StartTaskProcessingState and FinishTaskProcessingState can be reused for other task-processing step functions
          StartTaskProcessingState:
            Type: Task
            Resource: arn:aws:lambda:${self:provider.region}:${self:custom.file.provider.account}:function:${self:service}-${opt:stage}-start-task-processing
            Next: ChooseProcessorState
            Catch:
              - ErrorEquals: [ "States.ALL" ]
                Next: FinishTaskProcessingState
                ResultPath: "$.error" # store error as `error` field along with input fields. That allows us to pass `taskId` to the failure handler
          ChooseProcessorState:
            Type: Choice
            # this choice can be extended with other state for different processors
            Choices:
              - Variable: $.nextStage
                StringEquals: com.simonmarkets.users.common.batch.UsersTasksStages$UsersBatchUpload$
                Next: UsersBatchUploadState
            Default: CannotFindProcessorState
          UsersBatchUploadState:
            Type: Map
            ItemsPath: "$.batches"
            MaxConcurrency: 1
            Parameters:
              "id.$": "$.id"
              "initiator.$": "$.initiator"
              "networkId.$": "$.networkId"
              "batch.$": "$$.Map.Item.Value"
            Iterator:
              StartAt: UsersBatchProcessingState
              States:
                UsersBatchProcessingState:
                  Type: Task
                  # calling a function defined in service-users
                  Resource: arn:aws:lambda:${self:provider.region}:${self:custom.file.provider.account}:function:${self:custom.service-users}-${opt:stage}-process-users-upload
                  End: true
            Catch: # if lambda throws in exception this considered as fatal error and will mark task as failed
              - ErrorEquals: [ "States.ALL" ]
                Next: FinishTaskProcessingState
                # store error as `error` field along with input fields. That allows us to pass `taskId` to the failure handler
                ResultPath: "$.error"
            ResultPath: "$.results"
            Next: FinishTaskProcessingState
          # handles errors and success. If `error` field is present with value that means that task will be marked as failed
          FinishTaskProcessingState:
            Type: Task
            Resource: arn:aws:lambda:${self:provider.region}:${self:custom.file.provider.account}:function:${self:service}-${opt:stage}-finish-task-processing
            End: true
          CannotFindProcessorState:
            Type: Pass
            Result:
              Error: "Cannot find processor state"
              Cause: "Cannot find processor state"
            ResultPath: "$.error"
            Next: FinishTaskProcessingState
