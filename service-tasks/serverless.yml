service: service-tasks

provider:
  stage: ${opt:stage, 'sandbox'}
  name: aws
  runtime: java11
  region: us-east-1
  profile: ${self:custom.file.provider.profile}
  iam:
    role: ${self:custom.file.provider.iam.role}
  versionFunctions: false
  tracing:
    lambda: true
  vpc:
    securityGroupIds: ${self:custom.file.provider.securityGroupIds}
    subnetIds: ${self:custom.file.provider.subnetIds}
  deploymentBucket:
    name: simon-${self:provider.profile}-serverless
  logRetentionInDays: ${self:custom.file.logRetentionInDays}

custom:
  file: ${file(../config/${opt:stage}.yml)}
  logSubscription: ${self:custom.file.logSubscription}
  service-users: "service-users"
  warmup:
    default:
      enabled: true
      concurrency: 2
      prewarm: true
      timeout: 60
      events:
        - schedule: rate(5 minutes)
      vpc: false
      role: ${self:provider.iam.role}
  newRelic:
    javaNewRelicHandler: handleStreamsRequest

plugins:
  - serverless-step-functions
  - serverless-plugin-warmup
  - serverless-plugin-log-subscription

package:
  artifact: target/service-tasks-uber.jar

functions:
  api:
    handler: com.simonmarkets.tasks.BatchAPIApp$EntryPoint$
    memorySize: 2048
    timeout: 60
    warmup:
      default:
        enabled: true
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=${opt:stage}.conf ${self:custom.file.proxySystemProperty, ''}

  start-task-processing:
    handler: com.simonmarkets.tasks.StartTaskProcessingApp::handleRequest
    memorySize: 2048
    timeout: 60
    warmup:
      default:
        enabled: false
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=${opt:stage}.conf ${self:custom.file.proxySystemProperty, ''}

  finish-task-processing:
    handler: com.simonmarkets.tasks.FinishTaskProcessingApp::handleRequest
    memorySize: 2048
    timeout: 60
    warmup:
      default:
        enabled: false
    environment:
      JAVA_TOOL_OPTIONS: -Dconfig.file=${opt:stage}.conf ${self:custom.file.proxySystemProperty, ''}

stepFunctions:
  stateMachines:
    users-tasks-processing:
      name: UsersTasksProcessing
      definition:
        Comment: Step function to processing generic tasks
        StartAt: StartTaskProcessingState
        States:
          # function which invokes state machine should pass `{"taskId":"<task id value>", "submitterId":"<id of user who submitted task>"}`
          # this stage should return `{"taskId": "<task id value>", "nextStage":"<mandatory task type>", "batches":[{"from": 0, "to": 10}]}
          # StartTaskProcessingState and FinishTaskProcessingState can be reused for other task-processing step functions
          StartTaskProcessingState:
            Type: Task
            Resource: arn:aws:lambda:${self:provider.region}:${self:custom.file.provider.account}:function:${self:service}-${opt:stage}-start-task-processing
            Next: ChooseProcessorState
            Catch:
              - ErrorEquals: [ "States.ALL" ]
                Next: FinishTaskProcessingState
                ResultPath: "$.error" # store error as `error` field along with input fields. That allows us to pass `taskId` to the failure handler
          ChooseProcessorState:
            Type: Choice
            # this choice can be extended with other state for different processors
            Choices:
              - Variable: $.nextStage
                StringEquals: com.simonmarkets.users.common.batch.UsersTasksStages$UsersBatchUpload$
                Next: UsersBatchUploadState
            Default: CannotFindProcessorState
          UsersBatchUploadState:
            Type: Map
            ItemsPath: "$.batches"
            MaxConcurrency: 1
            Parameters:
              "id.$": "$.id"
              "initiator.$": "$.initiator"
              "networkId.$": "$.networkId"
              "batch.$": "$$.Map.Item.Value"
            Iterator:
              StartAt: UsersBatchProcessingState
              States:
                UsersBatchProcessingState:
                  Type: Task
                  # calling a function defined in service-users
                  Resource: arn:aws:lambda:${self:provider.region}:${self:custom.file.provider.account}:function:${self:custom.service-users}-${opt:stage}-process-users-upload
                  End: true
            Catch: # if lambda throws in exception this considered as fatal error and will mark task as failed
              - ErrorEquals: [ "States.ALL" ]
                Next: FinishTaskProcessingState
                # store error as `error` field along with input fields. That allows us to pass `taskId` to the failure handler
                ResultPath: "$.error"
            ResultPath: "$.results"
            Next: FinishTaskProcessingState
          # handles errors and success. If `error` field is present with value that means that task will be marked as failed
          FinishTaskProcessingState:
            Type: Task
            Resource: arn:aws:lambda:${self:provider.region}:${self:custom.file.provider.account}:function:${self:service}-${opt:stage}-finish-task-processing
            End: true
          CannotFindProcessorState:
            Type: Pass
            Result:
              Error: "Cannot find processor state"
              Cause: "Cannot find processor state"
            ResultPath: "$.error"
            Next: FinishTaskProcessingState
