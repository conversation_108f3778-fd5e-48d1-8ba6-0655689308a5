### How to deploy step functions from the local environment


1. Create a file with name `config` in `~/.aws` folder with the following content
```
[profile alpha_local_dev]
role_arn = arn:aws:iam::350128884696:role/okta-admin-iam-role
role_session_name = learning-portal-service-alpha-role
source_profile = alpha
```
2. Check that file is working, invoke following command:
```
aws sts get-caller-identity --profile alpha_local_dev
```
Output should contain following string:
```
"Arn": "arn:aws:sts::350128884696:assumed-role/okta-admin-iam-role/learning-portal-service-alpha-role"
```
3. In order to make it work with serverless framework you need to set following environment variable:
```
export AWS_SDK_LOAD_CONFIG="true"
```
4. After that I can use `aws-profile` parameter to deploy serverless using particular role, like that:
```
serverless deploy -v --stage alpha --region us-east-1 --aws-profile alpha_local_dev
```
That way aws cli use assume-role internally without explicit call. 

### Useful links:

https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-role.html
https://www.serverless.com/framework/docs/providers/aws/guide/credentials#using-the-aws-profile-option
https://github.com/serverless/serverless/issues/5048



