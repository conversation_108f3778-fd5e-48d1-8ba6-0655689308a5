<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>users-networks</artifactId>
        <groupId>com.simonmarkets</groupId>
        <version>sandbox-SNAPSHOT</version>
    </parent>

    <artifactId>service-tasks</artifactId>
    <version>sandbox-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-useracl-directive-resteasy</artifactId>
            <version>sandbox-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.mongodb.scala</groupId>
            <artifactId>mongo-scala-driver_${scala.version.short}</artifactId>
        </dependency>
        <dependency>
            <groupId>io.circe</groupId>
            <artifactId>circe-generic-extras_${scala.version.short}</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-lambda-java-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-stepfunctions</artifactId>
            <!-- todo: add to thirdparty bom-->
            <version>1.11.934</version>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-circe-extensions</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-task-processing</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-task-processing-mongo-scala</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-acl-client-resteasy</artifactId>
            <version>sandbox-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-users-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.simonmarkets</groupId>
                    <artifactId>lib-okta</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.goldmansachs.marquee</groupId>
            <artifactId>simon-entitlements</artifactId>
            <version>sandbox-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>io.simon</groupId>
            <artifactId>lib-openapi-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_${scala.version.short}</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets.utils</groupId>
            <artifactId>scala-mongo-embedded-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
                <configuration>
                    <compilerPlugins>
                        <compilerPlugin>
                            <groupId>org.scalamacros</groupId>
                            <artifactId>paradise_${scala.version}</artifactId>
                            <version>2.1.1</version>
                        </compilerPlugin>
                    </compilerPlugins>
                    <args>
                        <arg>-Xmacro-settings:resourcePath=${openapi.resourcePath},templatePath=${openapi.template},genOkteto=${openapi.genOkteto}</arg>
                        <arg>-deprecation</arg>
                        <arg>-feature</arg>
                        <arg>-language:experimental.macros</arg>
                        <arg>-language:higherKinds</arg>
                        <arg>-Xmacro-settings:materialize-derivations</arg>
                        <arg>-unchecked</arg>
                        <arg>-Xfatal-warnings</arg>
                        <arg>-Xlint:adapted-args</arg>
                        <arg>-Xlint:by-name-right-associative</arg>
                        <arg>-Xlint:constant</arg>
                        <arg>-Xlint:delayedinit-select</arg>
                        <arg>-Xlint:doc-detached</arg>
                        <arg>-Xlint:inaccessible</arg>
                        <arg>-Xlint:infer-any</arg>
                        <arg>-Xlint:missing-interpolator</arg>
                        <arg>-Xlint:nullary-override</arg>
                        <arg>-Xlint:nullary-unit</arg>
                        <arg>-Xlint:option-implicit</arg>
                        <arg>-Xlint:package-object-classes</arg>
                        <arg>-Xlint:poly-implicit-overload</arg>
                        <arg>-Xlint:private-shadow</arg>
                        <arg>-Xlint:stars-align</arg>
                        <arg>-Xlint:type-parameter-shadow</arg>
                        <arg>-Xlint:unsound-match</arg>
                        <arg>-Yno-adapted-args</arg>
                        <arg>-Ypartial-unification</arg>
                        <arg>-Ywarn-dead-code</arg>
                        <arg>-Ywarn-extra-implicit</arg>
                        <arg>-Ywarn-inaccessible</arg>
                        <arg>-Ywarn-infer-any</arg>
                        <arg>-Ywarn-nullary-override</arg>
                        <arg>-Ywarn-nullary-unit</arg>
                        <arg>-Ywarn-unused:implicits</arg>
                        <arg>-Ywarn-unused:imports</arg>
                        <arg>-Ywarn-unused:locals</arg>
                        <arg>-Ywarn-unused:params</arg>
                        <arg>-Ywarn-unused:patvars</arg>
                        <arg>-Ywarn-unused:privates</arg>
                        <arg>-Ywarn-macros:after</arg>
                    </args>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
                <configuration>
                    <parallel>false</parallel>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>${maven-shade-plugin.version}</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <minimizeJar>true</minimizeJar>
                            <shadedArtifactAttached>true</shadedArtifactAttached>
                            <shadedClassifierName>uber</shadedClassifierName>
                            <filters combine.children="append">
                                <filter>
                                    <artifact>org.scala-lang:scala-library</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>com.typesafe.akka:akka-testkit_${scala.version.short}</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>com.typesafe.akka:akka-actor_${scala.version.short}</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>com.typesafe.akka:akka-stream_${scala.version.short}</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>ch.qos.logback:*</artifact>
                                    <includes>**</includes>
                                </filter>
                                <filter>
                                    <artifact>ch.qos.logback.contrib:*</artifact>
                                    <includes>**</includes>
                                </filter>
                                <filter>
                                    <artifact>com.simonmarkets:lib-logging-core</artifact>
                                    <includes>**</includes>
                                </filter>
                                <filter>
                                    <artifact>com.simonmarkets.logging:*</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>commons-logging:commons-logging</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>org.mongodb:mongo-java-driver</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>com.fasterxml.jackson.core:*</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>org.apache.httpcomponents:httpclient</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>org.apache.httpcomponents:httpcore</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>com.amazonaws:aws-java-sdk-core</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>com.amazonaws:aws-lambda-java-core</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                            </filters>
                            <!-- There are multiple reference.conf files in separate jars. The following should merge them -->
                            <!-- Source: https://doc.akka.io/docs/akka/current/additional/packaging.html#maven-jarjar-onejar-or-assembly -->
                            <artifactSet>
                                <includes>
                                    <include>*:*</include>
                                </includes>
                            </artifactSet>
                            <transformers>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                    <resource>reference.conf</resource>
                                </transformer>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <manifestEntries>
                                        <Main-Class>
                                            com.simonmarkets.tasks.StartTaskProcessingApp
                                        </Main-Class>
                                    </manifestEntries>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.coderplus.maven.plugins</groupId>
                <artifactId>copy-rename-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>de.smartics.maven.plugin</groupId>
                <artifactId>buildmetadata-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>io.github.git-commit-id</groupId>
                <artifactId>git-commit-id-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>*.conf</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <excludes>
                    <exclude>*.conf</exclude>
                </excludes>
            </resource>
        </resources>
    </build>

</project>
