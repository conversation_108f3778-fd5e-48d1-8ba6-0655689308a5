package com.simonmarkets.tasks.api

import com.goldmansachs.marquee.pipg
import com.goldmansachs.marquee.pipg.UserRole
import com.simonmarkets.tasks.api.rest.request.BatchUserJobDetail
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.users.common.batch.BatchOperation
import io.circe.Decoder
import org.scalatest.{EitherValues, Matchers, WordSpecLike}

import scala.io.Source

class JobDetailCirceEncoderSpec extends WordSpecLike with Matchers with EitherValues {

  "JobDetailCirceEncoder" should {
    "decode Add user" in {
      val addUser = decodeJsonUnsafe[BatchUserJobDetail]("addUser.json")
      addUser shouldBe BatchUserJobDetail(
        email = "<EMAIL>",
        firstName = "Test Users",
        lastName = "Alex",
        roles = Set(UserRole.EqPIPGFA),
        customRoles = Set("EqPIPGFA"),
        network = "SIMON Admin",
        externalIds = Map.empty,
        locations = Set.empty,
        faNumbers = Set.empty,
        tradewebEligible = false,
        regSEligible = false,
        newEmail = Some(""),
        npn = Some("npn1"),
        crd = Some("crd1"),
        operation = BatchOperation.Add,
        loginMode = None,
        firmId = None,
        whiteLabelPartnerId = None,
        iCapitalUserId = None,
        secondaryEmail = None
      )
    }

    "decode Edit user" in {
      val parsedModifyUser = decodeJsonUnsafe[BatchUserJobDetail]("modifyUser.json")
      parsedModifyUser shouldBe BatchUserJobDetail(
        email = "<EMAIL>",
        firstName = "Test Users",
        lastName = "Alex",
        roles = Set(pipg.UserRole.EqPIPGFA),
        customRoles = Set("EqPIPGFA"),
        network = "SIMON Admin",
        externalIds = Map.empty,
        locations = Set.empty,
        faNumbers = Set.empty,
        tradewebEligible = false,
        regSEligible = false,
        newEmail = Some("<EMAIL>"),
        npn = Some("npn2"),
        crd = Some("crd2"),
        operation = BatchOperation.Edit,
        loginMode = Some(LoginMode.SSOAndUsernamePassword),
        firmId = Some("someFirmId"),
        whiteLabelPartnerId = Some("someWhiteLabelPartnerId"),
        iCapitalUserId = Some("someICapitalUserId"),
        secondaryEmail = Some("someSecondaryEmail"),
      )
    }

    "decode Deactivate user" in {
      val deactivateUser = decodeJsonUnsafe[BatchUserJobDetail]("deactivateUser.json")
      deactivateUser shouldBe BatchUserJobDetail(
        email = "<EMAIL>",
        firstName = "Test Users",
        lastName = "Alex",
        roles = Set(UserRole.EqPIPGFA),
        customRoles = Set("EqPIPGFA"),
        network = "SIMON Admin",
        externalIds = Map.empty,
        locations = Set.empty,
        faNumbers = Set.empty,
        tradewebEligible = false,
        regSEligible = false,
        newEmail = Some(""),
        npn = Some("npn1"),
        crd = Some("crd1"),
        operation = BatchOperation.Deactivate,
        loginMode = None,
        firmId = None,
        whiteLabelPartnerId = None,
        iCapitalUserId = None,
        secondaryEmail = None
      )
    }


    "decode Reset-Password user" in {
      val resetPassswordUser = decodeJsonUnsafe[BatchUserJobDetail]("resetPasswordUser.json")
      resetPassswordUser shouldBe BatchUserJobDetail(
        email = "<EMAIL>",
        firstName = "Test Users",
        lastName = "Alex",
        roles = Set(UserRole.EqPIPGFA),
        customRoles = Set("EqPIPGFA"),
        network = "SIMON Admin",
        externalIds = Map.empty,
        locations = Set.empty,
        faNumbers = Set.empty,
        tradewebEligible = false,
        regSEligible = false,
        newEmail = Some(""),
        npn = Some("npn1"),
        crd = Some("crd1"),
        operation = BatchOperation.`Reset-Password`,
        loginMode = None,
        firmId = None,
        whiteLabelPartnerId = None,
        iCapitalUserId = None,
        secondaryEmail = None
      )
    }

  }

  private def decodeJsonUnsafe[T: Decoder](fileName: String): T =
    decode[T](Source.fromResource(fileName).mkString).right.get

}
