package com.simonmarkets.tasks.api

import org.scalatest.WordSpecLike

class StartTaskOutputSpec extends WordSpecLike {

  "StartTaskOutput" should {
    "split inputs onto batches" in {
      import StartTaskOutput._

      assert(calculateBatches(0, 1) == Seq(BatchParameters(1, 1))) // this means an empty batch
      assert(calculateBatches(1, 1) == Seq(BatchParameters(1, 2)))
      assert(calculateBatches(1, 5) == Seq(BatchParameters(1, 2)))
      assert(calculateBatches(5, 5) == Seq(BatchParameters(1, 6)))
      assert(calculateBatches(6, 5) == Seq(BatchParameters(1, 6), BatchParameters(6, 7)))

      assert(calculateBatches(29, 10) ==
        Seq(BatchParameters(1, 11), BatchParameters(11, 21), BatchParameters(21, 30)))

      assert(calculateBatches(30, 10) ==
        Seq(BatchParameters(1, 11), BatchParameters(11, 21), BatchParameters(21, 31)))

      assert(calculateBatches(31, 10) ==
        Seq(BatchParameters(1, 11), BatchParameters(11, 21), BatchParameters(21, 31), BatchParameters(31, 32)))

      assert(calculateBatches(32, 10) ==
        Seq(BatchParameters(1, 11), BatchParameters(11, 21), BatchParameters(21, 31), BatchParameters(31, 33)))

      assert(calculateBatches(33, 10) ==
        Seq(BatchParameters(1, 11), BatchParameters(11, 21), BatchParameters(21, 31), BatchParameters(31, 34)))

    }
  }

}
