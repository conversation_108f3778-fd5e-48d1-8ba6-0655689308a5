include classpath("common.conf")

run-mode {
  type = lambda-mode
}

acl-client-config {
  http-client {
    auth {
      type = OAuth
      client-id = "0oa2h4re4ym0Fh3s82p7"
      client-secret-file = ""
      site = "sm:applicationconfig-issuer-uri"
      authorization-path = ""
      token-path = "/v1/token"
      scope = "read_product_data"
      token-type {
        type = Cookie
        name = SimSSO
      }
      client-secret = "sm:applicationconfig-oktaclientsecret"
      proxy {
        port = 3128
        address = "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
      }
    }
    proxy {
      port = 3128
      address = "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
    }
  }
  base-url = "https://origin-dc1.api.simonmarkets.com/simon/api"
  cache-config {
    enabled = true
    config {
      service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
      signin-region = "us-east-1"
    }
  }
}

system-user-id = "c388df581a28404a94bbc9885c1650fd"

authentication {
  type = "jwt"
  jwt {
    source {
      type = "cookieOrHeader"
      name = "SimSSO:X-Simon-AccessToken"
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clock-offset-in-seconds = 0
      proxy-host = "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
      proxy-port = 3128
    }
  }
  user-info {
    source {
      type = "header"
      name = "x-userinfo"
    }
  }
}

step-functions {
  tasks-step-function-arn = "arn:aws:states:us-east-1:090020729433:stateMachine:UsersTasksProcessing"
  region = "us-east-1"
}
