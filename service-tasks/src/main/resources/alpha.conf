include classpath("common.conf")

run-mode {
  type = lambda-mode
}

system-user-id = "0oaevl54gplnFvyx70h7"

acl-client-config {
  http-client {
    auth {
      type = OAuth
      client-id = "0oaevl54gplnFvyx70h7"
      client-secret-file = ""
      site = "sm:applicationconfig-issuer-uri"
      authorization-path = ""
      token-path = "/v1/token"
      scope = "read_product_data"
      token-type {
        type = Cookie
        name = SimSSO
      }
      client-secret = "sm:Okta-secret"
      proxy {
        port = 3128
        address = "internal-alpha-squid-proxy-elb-1264059358.us-east-1.elb.amazonaws.com"
      }
    }
    proxy {
        port = 3128
        address = "internal-alpha-squid-proxy-elb-1264059358.us-east-1.elb.amazonaws.com"
    }
  }
  base-url = "https://origin-a.dev.simonmarkets.com/simon/api"
  cache-config {
    enabled = true
    config {
      service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
      signin-region = "us-east-1"
    }
  }
}

authentication {
  type = "jwt"
  jwt {
    source {
      type = "cookieOrHeader"
      name = "SimSSO:X-Simon-AccessToken"
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      keys-url = "sm:applicationconfig-okta-validator"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clock-offset-in-seconds = 0
      proxy-host = "internal-alpha-squid-proxy-elb-1264059358.us-east-1.elb.amazonaws.com"
      proxy-port = 3128
    }
  }

  user-info {
    source {
      type = "header"
      name = "x-userinfo"
    }
  }
}

step-functions {
  tasks-step-function-arn = "arn:aws:states:us-east-1:************:stateMachine:UsersTasksProcessing"
  region = "us-east-1"
}



