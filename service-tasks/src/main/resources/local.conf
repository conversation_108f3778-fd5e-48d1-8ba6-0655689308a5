include classpath("common.conf")

run-mode {
  type = "server-mode"
  http-server-config {
    port = 1984
    interface = "0.0.0.0"
    ssl {
      enabled = false
      keystore {
        type = "JKS"
        file = ""
        password = ""
      }
    }
  }
}

system-user-id = "0oaevl54gplnFvyx70h7"

acl-client-config {
  http-client {
    auth {
      type = OAuth
      client-id = "0oaevl54gplnFvyx70h7"
      client-secret-file = ""
      site = "sm:applicationconfig-issuer-uri"
      authorization-path = ""
      token-path = "/v1/token"
      scope = "read_product_data"
      token-type {
        type = Cookie
        name = SimSSO
      }
      client-secret = "sm:Okta-secret"
    }
  }
  base-url = "https://origin-a.dev.simonmarkets.com/simon/api"
  cache-config {
    enabled = true
    config {
      service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
      signin-region = "us-east-1"
    }
  }
}

authentication {
  type = "jwt"
  jwt {
    source {
      type = "bearer"
      name = ""
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      keys-url = "sm:applicationconfig-okta-validator"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clock-offset-in-seconds = 0
    }
  }
  user-info {
    source {
      type = "header"
      name = "x-userinfo"
    }
  }
}


mongo-db {
  client {
    app-name = "service-tasks"
    connection {
      url = "mongodb://cluster0-shard-00-00-pvlpe.mongodb.net:27017,cluster0-shard-00-01-pvlpe.mongodb.net:27017,cluster0-shard-00-02-pvlpe.mongodb.net:27017/?ssl=true&replicaSet=Cluster0-shard-0&sslInvalidHostNameAllowed=true&authSource=admin&retryWrites=true&w=majority&readPreference=secondaryPreferred"
      authentication {
        type = None
      }
    }
  }
}

step-functions {
  tasks-step-function-arn = "arn:aws:states:us-east-1:350128884696:stateMachine:UsersTasksProcessing"
  region = "us-east-1"
}
