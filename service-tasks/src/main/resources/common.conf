info {
  name = "Network Service Tasks"
  description = "A service responsible for scheduling and retrieving batch jobs"
  repository = "networks"
  module = "service-tasks"
  version = "${project.version}"
  revision = "${git.commit.id.abbrev}"
  build-timestamp = "${build.timestamp.millis}"
  product-line = "CrossProduct"
  used-by = ["CrossProduct", "Storefront", "Spectrum", "SMA", "Annuity", "ETF", "StructuredInvestment"]
  owner = "CrossProduct"
  support-distro = ["<EMAIL>"]
  metrics-url = "",
  documentation-url = "https://simonmarkets.atlassian.net/wiki/spaces/ENG/pages/445349961/REST+Easy+Framework"
}

config.resolvers {
  sm {
    region = "us-east-1"
  }
  file {
    root = ""
  }
}

mongo-db {
  users {
    collection = "users"
    database = "pipg"
  }
  networks {
    collection = "networks_new"
    database = "pipg"
  }
  tasks {
    collection ="users.tasks",
    database = "pipg"
  }
  inputs {
    collection ="users.tasks.inputs",
    database = "pipg"
  }
  outputs {
    collection ="users.tasks.outputs",
    database = "pipg"
  }
  client {
    app-name = "service-tasks"
    connection = "sm:config:applicationconfig-mongo-auth-pipg"
  }
}

system-routes {
  service-up {
    path = "simon/api/v3/batch-jobs/uptime"
  }
  service-info {
    path = "simon/api/v3/batch-jobs/info"
  }
  health-check {
    path = "simon/api/v3/batch-jobs/healthcheck"
  }
}
