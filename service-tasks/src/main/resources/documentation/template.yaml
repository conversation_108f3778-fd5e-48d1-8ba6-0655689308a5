openapi: 3.0.0
info:
  title: Batch jobs service V3
  version: 1.0.0
x-basepath: /simon/api
x-kong-service-defaults:
  read_timeout: 30000
tags:
  - name: service-tasks
    description: Batch jobs service
x-kong-plugin-aws-lambda:
  config:
    function_name: service-tasks-${STAGE}-api
    aws_region: us-east-1
    invocation_type: RequestResponse
    awsgateway_compatible: true
    forward_request_body: true
    forward_request_headers: true
    forward_request_method: true
    forward_request_uri: true
    is_proxy_integration: true
    skip_large_bodies: false
    proxy_url: ${HTTP_PROXY}
    proxy_scheme: https
    log_type: None
paths:
  /v3/batch-jobs/uptime:
    get:
      x-scopes:
        - admin
      tags:
        - batch-jobs
      summary: Retrieve service uptime
      responses:
        200:
          description: Success
  /v3/batch-jobs/info:
    get:
      x-scopes:
        - admin
      tags:
        - batch-jobs
      summary: Retrieve service info
      responses:
        200:
          description: Success
  /v3/batch-jobs/healthcheck:
    get:
      x-scopes:
        - admin
      tags:
        - batch-jobs
      summary: Retrieve service healthcheck
      responses:
        200:
          description: Success
  /v3/batch-jobs:
    get:
      x-scopes:
        - admin
      tags:
        - batch-jobs
      summary: Retrieve list of batch jobs
      parameters:
        - name: _
          in: query
          required: false
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.CacheBustParameter}
        - name: limit
          in: query
          required: false
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.StringInt}
        - name: offset
          in: query
          required: false
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.StringInt}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.tasks.api.rest.response.BatchJobList}
    post:
      x-scopes:
        - admin
      tags:
        - batch-jobs
      summary: Schedule new batch job
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.tasks.api.rest.request.ScheduleJob}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.tasks.api.rest.response.BatchJobInfo}
  /v3/batch-jobs/{id}:
    get:
      x-scopes:
        - admin
      summary: Retrieve specified Batch Job
      tags:
        - batch-jobs
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.tasks.api.rest.response.BatchJobInfo}
  /v3/batch-jobs/{id}/outputs:
    get:
      x-scopes:
        - admin
      summary: Retrieve specified Batch Job
      tags:
        - batch-jobs
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
        - name: limit
          in: query
          required: false
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.StringInt}
        - name: offset
          in: query
          required: false
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.StringInt}
      responses:
        200:
          description: Successful operation
