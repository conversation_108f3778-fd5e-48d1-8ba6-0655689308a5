package com.simonmarkets.tasks.service

import akka.http.scaladsl.model.{HttpEntity, HttpResponse, StatusCodes}
import com.amazonaws.services.stepfunctions.AWSStepFunctions
import com.amazonaws.services.stepfunctions.model.StartExecutionRequest
import com.simonmarkets.http.HttpError
import com.simonmarkets.http.HttpError.{emptyAttributes, httpProtocol}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.tasks.service.StepFunctionsService.ExecutionId

import scala.concurrent.Future
import scala.util.{Failure, Success, Try}

trait StepFunctionsService {

  def invoke(arn: String, input: String)(implicit traceId: TraceId): Future[ExecutionId]
}

object StepFunctionsService {
  type ExecutionId = String

  class Aws(stepFunctionsClient: AWSStepFunctions) extends StepFunctionsService with TraceLogging {

    override def invoke(arn: String, input: String)(implicit traceId: TraceId): Future[ExecutionId] = {
      val request = new StartExecutionRequest()
      request.setStateMachineArn(arn)
      request.setTraceHeader(traceId.toString)
      request.setInput(input)

      Try(stepFunctionsClient.startExecution(request)) match {
        case Failure(exception) =>
          log.error(exception,"Error on step function invoke", input)
          Future.failed(HttpError(new HttpResponse(StatusCodes.InternalServerError, Nil, emptyAttributes, HttpEntity.Empty, httpProtocol)))
        case Success(value) =>
          log.info(s"Step function successfully triggered for input: $input. Execution ARN: ${value.getExecutionArn}")
          Future.successful(value.getExecutionArn)
      }
    }
  }

}

