package com.simonmarkets.tasks.service

import akka.actor.ActorSystem
import akka.stream.Materializer
import com.amazonaws.services.stepfunctions.{AWSStepFunctions, AWSStepFunctionsClientBuilder}
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.api.users.HttpACLClient
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.mongodb.Client
import com.simonmarkets.mongodb.bson.AsyncFormat
import com.simonmarkets.resteasy.authn.User
import com.simonmarkets.resteasy.framework.RestEasyCore
import com.simonmarkets.resteasy.requests.AuthorizedDirectives
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.taskprocessing.TasksManager.{SystemTimer, TaskOutput, Timer}
import com.simonmarkets.taskprocessing.TasksRepository
import com.simonmarkets.taskprocessing.mongoscala.driver.TasksRepositoryMongoScalaDriver
import com.simonmarkets.taskprocessing.mongoscala.driver.format.ProcessingTaskFormat
import com.simonmarkets.tasks.config.AppConfiguration
import com.simonmarkets.useracl.UserAclAuthorizedDirective
import com.simonmarkets.users.common.repository.encoder.{UserUploadTaskInputFormat, UserUploadTaskOutputFormat}
import org.mongodb.scala.MongoDatabase
import org.mongodb.scala.bson.collection.Document

import scala.concurrent.ExecutionContext
import scala.concurrent.duration.Duration

class ServiceLocator(config: AppConfiguration)(implicit val system: ActorSystem, mat: Materializer) extends TraceLogging {
  private implicit val traceId: TraceId = TraceId("service-di-init")

  private implicit lazy val executionContext: ExecutionContext = system.dispatcher

  log.info("Connecting to mongo")
  private lazy val mongoDB: MongoDatabase = Client.create(config.mongoDB.client)
    .getDatabase(config.mongoDB.tasks.database)

  private lazy val tasksCollection = mongoDB.getCollection[Document](config.mongoDB.tasks.collection)
  private lazy val taskInputsCollection = mongoDB.getCollection[Document](config.mongoDB.inputs.collection)
  private lazy val taskOutputsCollection = mongoDB.getCollection[Document](config.mongoDB.outputs.collection)

  private val timer: Timer = SystemTimer

  private lazy val tasksRepository: TasksRepository = new TasksRepositoryMongoScalaDriver(
    tasksCollection,
    taskFormat = ProcessingTaskFormat,
    inputs = taskInputsCollection,
    inputFormat = UserUploadTaskInputFormat,
    outputs = taskOutputsCollection,
    outputFormat = UserUploadTaskOutputFormat.asInstanceOf[AsyncFormat[TaskOutput]]
  )

  private lazy val stepFunctionsClient: AWSStepFunctions = AWSStepFunctionsClientBuilder.standard()
    .withRegion(config.stepFunctions.region)
    .build()

  val httpACLClient: HttpACLClient = HttpACLClient(config.aclClientConfig)
  private val systemUser = httpACLClient.getUserACL(config.systemUserId).await(Duration.Inf)
  private val userACLDirective: AuthorizedDirectives[UserACL] = UserAclAuthorizedDirective(httpACLClient)
  val authDirective: AuthorizedDirectives[UserACL] = RestEasyCore.allowSystemCalls(userACLDirective, User(systemUser.userId, "", None, Nil), systemUser)

  final lazy val stepFunctionsService = new StepFunctionsService.Aws(stepFunctionsClient)

  final lazy val tasksService: TasksService = new TasksService.Impl(
    tasksRepository, timer, stepFunctionsService, tasksStepFunctionARN = config.stepFunctions.tasksStepFunctionARN)
}
