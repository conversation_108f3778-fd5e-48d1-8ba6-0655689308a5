package com.simonmarkets.tasks.service

import com.goldmansachs.marquee.pipg.UserACL
import com.gs.marquee.util.UuidCreator
import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.circe.CirceEncoders
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.taskprocessing.TasksManager.{ProcessingTask, TaskInput, TaskOutput, TaskStatus, Timer}
import com.simonmarkets.taskprocessing.TasksRepository
import com.simonmarkets.tasks.api.rest.request.BatchUserJobDetail
import com.simonmarkets.tasks.api.{ExecutionError, StartTaskInput}
import com.simonmarkets.users.common.batch.{UserUploadTaskInput, UsersTasksStages}
import simon.Id.ProcessingTaskId

import java.time.{LocalDateTime, ZoneId}

import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}

trait TasksService {

  def startProcessing(user: UserAC<PERSON>, taskId: ProcessingTaskId)(implicit traceId: TraceId): Future[ProcessingTask]

  def createTask(user: UserACL, name: String, group: String, inputs: Seq[BatchUserJobDetail])
    (implicit traceId: TraceId): Future[ProcessingTask]

  def finishProcessing(user: UserACL, taskId: ProcessingTaskId, error: Option[ExecutionError])
    (implicit traceId: TraceId): Future[ProcessingTask]

  def getTask(user: UserACL, taskId: ProcessingTaskId)(implicit traceId: TraceId): Future[Option[ProcessingTask]]

  def getTaskOutputs(user: UserACL, taskId: ProcessingTaskId, offset: Int, limit: Int)
    (implicit traceId: TraceId): Future[List[TaskOutput]]

  def listTasks(user: UserACL, offset: Long, limit: Long)(implicit traceId: TraceId): Future[Seq[ProcessingTask]]
}

object TasksService {

  class Impl(tasksRepository: TasksRepository, timer: Timer, stepFunctionsService: StepFunctionsService,
      tasksStepFunctionARN: String)
    (implicit ec: ExecutionContext) extends TasksService with TraceLogging with CirceEncoders {

    private final val BatchSize = 1

    override def createTask(user: UserACL, name: String, group: String, inputs: Seq[BatchUserJobDetail])
      (implicit traceId: TraceId): Future[ProcessingTask] = {
      log.trace(s"Creating a task with name: $name")

      val now = LocalDateTime.now(ZoneId.systemDefault())
      for {
        _ <- if (!user.capabilities.contains(Capabilities.Admin)) Future.failed(HttpError.unauthorized("User does not have " +
          "permissions to create task")) else Future.successful(Unit)

        _ <- if (inputs.isEmpty) Future.failed(HttpError.badRequest("Cannot schedule task with empty input"))
        else Future.successful(Unit)

        task = ProcessingTask(
          id = ProcessingTaskId(UuidCreator.newId),
          group = group,
          initiator = user.userId,
          networkId = Some(user.networkId),
          currentBatches = 0,
          totalBatches = 0,
          batchSize = BatchSize,
          status = TaskStatus.Pending,
          stages = List(UsersTasksStages.UsersBatchUpload),
          initialStages = List(UsersTasksStages.UsersBatchUpload),
          createdDatetime = now,
          updatedDatetime = now,
          startedDatetime = None,
          finishedDatetime = None,
          pricingDate = None,
          inputTimeout = 0.seconds, // not applicable
          processingTimeout = 0.seconds,
          timeoutTimestamp = 0,
          currentRow = 0,
          totalRows = inputs.size,
          retries = 0,
          errors = List.empty,
          entitlements = if (user.isAdmin) Set(Capabilities.Admin) else Set.empty,
          name = Some(name)
        )
        insertedTask <- tasksRepository.saveTask(task)

        taskInputs: Seq[TaskInput] = inputs.zipWithIndex.map {
          case (input: BatchUserJobDetail, row: Int) =>
            log.trace(s"Saving task input for task: ${task.id}")

            UserUploadTaskInput(
              taskId = insertedTask.id,
              row = row + 1,
              email = input.email,
              firstName = input.firstName,
              lastName = input.lastName,
              network = input.network,
              externalIds = input.externalIds,
              roles = input.roles,
              locations = input.locations,
              faNumbers = input.faNumbers,
              customRoles = input.customRoles,
              tradewebEligible = input.tradewebEligible,
              regSEligible = input.regSEligible,
              newEmail = input.newEmail.filter(_.trim.nonEmpty),
              npn = input.npn,
              crd = input.crd,
              operation = input.operation,
              loginMode = input.loginMode,
              whiteLabelPartnerId = input.whiteLabelPartnerId,
              firmId = input.firmId,
              iCapitalUserId = input.iCapitalUserId,
              secondaryEmail = input.secondaryEmail
            )
        }

        _ <- tasksRepository.saveTaskInput(taskInputs.toList)

        stepFunctionInput = StartTaskInput(id = task.id, initiator = user.userId, networkId = user.networkId).asJsonStr
        _ = log.trace(s"Invoking step functions for task: ${task.id}")
        _ <- stepFunctionsService.invoke(tasksStepFunctionARN, stepFunctionInput)
      } yield insertedTask
    }

    override def startProcessing(user: UserACL, taskId: ProcessingTaskId)
      (implicit traceId: TraceId): Future[ProcessingTask] = {

      for {
        _ <- if (!user.capabilities.contains(Capabilities.Admin)) Future.failed(HttpError.unauthorized("User does not have " +
          "permissions to create task")) else Future.successful(Unit)

        taskOpt <- tasksRepository.findTask(taskId, user.capabilities.filter(_ == Capabilities.Admin))

        _ <- if (taskOpt.isEmpty) Future.failed(HttpError.badRequest(s"Cannot find task with id: $taskId")) else Future.successful(Unit)
        task = taskOpt.get
        _ <- if (task.status != TaskStatus.Pending)
          Future.failed(HttpError.conflict(s"Cannot start task with status != Pending. TaskId: ${task.id}, status: ${task.status}"))
        else Future.successful(Unit)

        updatedTask = task.copy(
          status = TaskStatus.InProgress,
          startedDatetime = Some(timer.currentDateTime),
        )
        result <- tasksRepository.saveTask(updatedTask)
      } yield result
    }

    override def finishProcessing(user: UserACL, taskId: ProcessingTaskId, error: Option[ExecutionError])
      (implicit traceId: TraceId): Future[ProcessingTask] = {

      for {
        _ <- if (!user.capabilities.contains(Capabilities.Admin))
          Future.failed(HttpError.unauthorized("User does not have permissions to create task"))
        else Future.successful(Unit)

        taskOpt <- tasksRepository.findTask(taskId, user.capabilities.filter(_ == Capabilities.Admin))

        _ <- if (taskOpt.isEmpty) Future.failed(HttpError.badRequest(s"Cannot find task with id: $taskId")) else Future.successful(Unit)
        task = taskOpt.get
        _ <- if (task.status != TaskStatus.InProgress)
          Future.failed(HttpError.conflict(s"Cannot finish task with status != InProgress. TaskId: ${task.id}, status: ${task.status}"))
        else Future.successful(Unit)

        updatedTask = task.copy(
          status = if (error.nonEmpty) TaskStatus.Failed else TaskStatus.Complete,
          errors = error.toList.map(_.cause),
          stages = if (error.nonEmpty) task.stages else List.empty,
          finishedDatetime = Some(timer.currentDateTime)
        )
        result <- tasksRepository.saveTask(updatedTask)
      } yield result
    }

    override def getTask(user: UserACL, taskId: ProcessingTaskId)
      (implicit traceId: TraceId): Future[Option[ProcessingTask]] = {
      tasksRepository.findTask(taskId, user.capabilities.filter(_ == Capabilities.Admin))
    }

    override def listTasks(user: UserACL, offset: Long, limit: Long)
      (implicit traceId: TraceId): Future[Seq[ProcessingTask]] = {
      tasksRepository
        .findTasks(user.capabilities.filter(_ == Capabilities.Admin), offset = offset, limit = limit)
        .map(_.toSeq.reverse)
    }

    override def getTaskOutputs(user: UserACL, taskId: ProcessingTaskId, offset: Int, limit: Int)
      (implicit traceId: TraceId): Future[List[TaskOutput]] = {

      for {
        _ <- if (!user.capabilities.contains(Capabilities.Admin)) Future.failed(HttpError.unauthorized("User does not have " +
          "permissions to list task outputs")) else Future.successful(Unit)
        outputs <- tasksRepository.findTaskOutputs(taskId, startRow = offset + 1, endRow = offset + limit + 1)
      } yield outputs.toList
    }
  }

}

