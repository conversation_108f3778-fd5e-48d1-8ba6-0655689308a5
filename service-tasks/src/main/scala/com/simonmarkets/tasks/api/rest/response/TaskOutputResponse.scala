package com.simonmarkets.tasks.api.rest.response

import com.simonmarkets.taskprocessing.TasksManager.TaskOutput
import com.simonmarkets.users.common.batch._
import simon.Id.ProcessingTaskId

case class TaskOutputResponse(
    taskId: ProcessingTaskId,
    email: String,
    operation: BatchOperation,
    row: Long,
    warning: Option[String],
    error: Option[String],
    `type`: UserUploadTaskOutputType
)

object TaskOutputResponse {

  def apply(output: TaskOutput): TaskOutputResponse = output match {
    case UserUploadSucceed(taskId, row, warning, t, email, operation) => TaskOutputResponse(
      taskId = taskId,
      email = email,
      operation = operation,
      row = row,
      warning = Some(warning),
      error = None,
      `type` = t,
    )
    case UserUploadFailed(taskId, row, error, t, email, operation) => TaskOutputResponse(
      taskId = taskId,
      email = email,
      operation = operation,
      row = row,
      warning = None,
      error = Some(error),
      `type` = t,
    )
    case _ => throw new IllegalStateException("Type should always conform to user bulk types")
  }
}
