package com.simonmarkets.tasks.api

import com.simonmarkets.taskprocessing.TasksManager.{ProcessingTask, TaskStage}
import simon.Id.{NetworkId, ProcessingTaskId}
import scala.language.postfixOps


case class StartTaskOutput(
    id: ProcessingTaskId,
    initiator: String,
    networkId: Option[NetworkId],
    nextStage: Option[TaskStage],
    batches: Seq[BatchParameters]
)

object StartTaskOutput {
  def apply(task: ProcessingTask): StartTaskOutput = StartTaskOutput(
    id = task.id,
    initiator = task.initiator,
    networkId = task.networkId,
    nextStage = task.stages.headOption,
    batches = calculateBatches(task.totalRows, task.batchSize)
  )

  object RangeExtensions {
    implicit class RichRange(value: Range) {
      def withEnd: IndexedSeq[Int] = {
        if (value.last != value.end) value :+ value.end
        else value
      }
    }
  }

  def calculateBatches(total: Long, batchSize: Long): Seq[BatchParameters] = {
    import RangeExtensions._
    val ranges = (1 to total.toInt + 1 by batchSize.toInt).withEnd sliding 2

    ranges.map { range =>
      BatchParameters(from = range.head, to = range.tail.headOption.getOrElse(total.toInt + 1))
    } toList
  }
}
