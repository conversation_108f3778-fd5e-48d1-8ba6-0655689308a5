package com.simonmarkets.tasks.api.rest.response

import com.simonmarkets.taskprocessing.TasksManager.{ProcessingTask, TaskStatus}
import io.simon.openapi.annotation.Field.Ref
import io.simon.openapi.annotation.{ClassReference, Reference}
import simon.Id.ProcessingTaskId

import java.time.LocalDateTime

case class BatchJobInfo(
    id: String,
    name: Option[String],
    createdBy: String,
    createdAt: LocalDateTime,
    jobCount: Int,
    status: TaskStatus,
    group: String)

object BatchJobInfo {
  def apply(task: ProcessingTask): BatchJobInfo =
    BatchJobInfo(
      id = ProcessingTaskId.unwrap(task.id),
      name = task.name,
      createdBy = task.initiator,
      createdAt = task.createdDatetime,
      jobCount = task.totalRows.toInt,
      status = task.status,
      group = task.group
    )
}



@Ref(ClassReference(classOf[List[BatchJobInfo]]))
case object BatchJobList extends Reference

