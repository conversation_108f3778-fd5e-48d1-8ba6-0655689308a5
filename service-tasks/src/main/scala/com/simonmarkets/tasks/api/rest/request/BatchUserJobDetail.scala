package com.simonmarkets.tasks.api.rest.request

import com.goldmansachs.marquee.pipg.UserRole
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.users.common.batch.BatchOperation
import io.simon.openapi.annotation.Field.{Ref, Type, TypeArgRef}
import io.simon.openapi.annotation.{ClassReference, OpenApiType}
import io.simon.openapi.definitions.CommonDefinitions
import io.simon.openapi.definitions.CommonDefinitions.ArbitraryMessage

case class BatchUserJobDetail(
    @Ref(CommonDefinitions.EmailAddress)
    email: String,

    @Ref(CommonDefinitions.HumanName)
    firstName: String,

    @Ref(CommonDefinitions.HumanName)
    lastName: String,

    @Ref(CommonDefinitions.Symbol)
    network: String,

    @Type(OpenApiType.Any)
    @TypeArgRef(ArbitraryMessage)
    externalIds: Map[String, String],

    roles: Set[UserRole],

    @TypeArgRef(CommonDefinitions.Location)
    locations: Set[String],

    @TypeArgRef(CommonDefinitions.FaNumber)
    faNumbers: Set[String],

    @TypeArgRef(CommonDefinitions.CustomRole)
    customRoles: Set[String],

    tradewebEligible: Boolean,
    regSEligible: Boolean,

    @Ref(CommonDefinitions.EmailAddress)
    newEmail: Option[String],

    @TypeArgRef(ClassReference(classOf[LoginMode]))
    loginMode: Option[LoginMode],

    @TypeArgRef(CommonDefinitions.LicenseNumber)
    npn: Option[String],

    @TypeArgRef(CommonDefinitions.LicenseNumber)
    crd: Option[String],

    @Ref(ClassReference(classOf[BatchOperation]))
    operation: BatchOperation,

    @Ref(CommonDefinitions.Symbol)
    firmId: Option[String],

    @Ref(CommonDefinitions.Symbol)
    whiteLabelPartnerId: Option[String],

    @Ref(CommonDefinitions.Symbol)
    iCapitalUserId: Option[String],

    @Ref(CommonDefinitions.EmailAddress)
    secondaryEmail: Option[String]
)
