package com.simonmarkets.tasks

import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.taskprocessing.TasksManager.{TaskStage, TaskStatus}
import io.circe.{Decoder, Encoder, <PERSON><PERSON>}

import scala.reflect.runtime.universe
import scala.util.Try

package object api extends JsonCodecs {

  private final val runtimeMirror: universe.Mirror = universe.runtimeMirror(getClass.getClassLoader)

  // implementation similar to BsonEncoder (see TaskRepository in utils)
  implicit val taskStageEncoder: Encoder[TaskStage] =
    Encoder.encodeString.contramap[TaskStage](_.getClass.getName)

  implicit val taskStageDecoder: Decoder[TaskStage] =
    Decoder.decodeString.emapTry(rawStage =>
      Try(runtimeMirror.reflectModule(runtimeMirror.staticModule(rawStage)).instance.asInstanceOf[TaskStage])
    )

  implicit val taskStatusEncoder: Encoder[TaskStatus] =
    taskStatus => Json.fromString(taskStatus.toString)

}
