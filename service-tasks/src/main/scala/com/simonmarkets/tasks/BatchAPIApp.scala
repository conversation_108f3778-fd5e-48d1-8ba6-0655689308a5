package com.simonmarkets.tasks


import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.resteasy.framework.{Environment, RestEasyModule}
import com.simonmarkets.resteasy.framework.RestEasyModule.RestEasyLambda
import com.simonmarkets.tasks.config.AppConfiguration
import com.simonmarkets.tasks.service.ServiceLocator
import io.simon.openapi.generator.OpenApiGenerator
import pureconfig.generic.auto._

object BatchAPIApp extends RestEasyModule[AppConfiguration] with App with TraceLogging {
  override def serviceName: String = "tasks"
  override def servicePath: String = "simon/api/v3/batch-jobs"
  override def init(environment: Environment): Unit = {
    val serviceLocator = new ServiceLocator(config)

    val batchJobsRoutes = BatchJobsRoutes(serviceLocator.tasksService, serviceLocator.authDirective)
    environment.addRoutes(batchJobsRoutes.routes)
  }

  OpenApiGenerator.generateOpenApiDocumentation()
  RestEasy.start

  object EntryPoint extends RestEasyLambda
}
