package com.simonmarkets.tasks.config

import com.simonmarkets.api.users.config.AclClientConfig
import com.simonmarkets.mongodb.config.MongoClientConfig
import com.simonmarkets.resteasy.framework.RunMode
import com.simonmarkets.resteasy.framework.config.RestEasyAppConfiguration
import com.simonmarkets.resteasy.framework.config.RestEasyAppConfiguration.SystemRoutesConfig
import com.simonmarkets.resteasy.framework.config.authn.AuthenticationConfiguration
import com.simonmarkets.resteasy.framework.info.InfoConfig
import com.typesafe.config.Config

case class AppConfiguration(
    mongoDB: MongoConfiguration,
    aclClientConfig: AclClientConfig,
    authentication: Option[AuthenticationConfiguration],
    runMode: RunMode,
    stepFunctions: StepFunctionsConfig,
    systemRoutes: Option[SystemRoutesConfig],
    systemUserId: String,
    info: Option[InfoConfig]
) extends RestEasyAppConfiguration

case class MongoConfiguration(
    users: DbConfig,
    networks: DbConfig,
    tasks: DbConfig,
    inputs: DbConfig,
    outputs: DbConfig,
    client: MongoClientConfig
)

case class DbConfig(
    collection: String,
    database: String
)

case class StepFunctionsConfig(
    tasksStepFunctionARN: String,
    region: String
)

object AppConfiguration {
  def apply(config: Config): AppConfiguration = {
    import pureconfig.generic.auto._
    pureconfig.loadConfigOrThrow[AppConfiguration](config)
  }
}