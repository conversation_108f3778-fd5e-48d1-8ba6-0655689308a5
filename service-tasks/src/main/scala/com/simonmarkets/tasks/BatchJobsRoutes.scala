package com.simonmarkets.tasks


import akka.http.scaladsl.server.{Directive0, Directive1, Route}
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.logging.TraceId
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.resteasy.requests.AuthorizedDirectives
import com.simonmarkets.tasks.api.rest.request.ScheduleJob
import com.simonmarkets.tasks.api.rest.response.{BatchJobInfo, TaskOutputResponse}
import com.simonmarkets.tasks.service.TasksService
import simon.Id.ProcessingTaskId
import com.simonmarkets.tasks.api._

import scala.concurrent.ExecutionContext

case class BatchJobsRoutes(taskService: TasksService,
    authDirectives: AuthorizedDirectives[UserACL])
  (implicit ec: ExecutionContext) extends DirectivesWithCirce {

  import authDirectives._

  lazy val routes: Route =
    authorized(Capabilities.Admin) { (trace, userACL: UserACL) =>
      implicit val traceId: TraceId = trace
      rejectEmptyResponse(

        V3_BATCH_JOBS {
          concat(
            `GET /:id/outputs` { taskId =>
              parameters("offset".as[Int].?, "limit".as[Int].?) {
                (offset: Option[Int], limit: Option[Int]) => {
                  complete(
                    taskService.getTaskOutputs(
                      userACL,
                      ProcessingTaskId(taskId),
                      offset = offset.getOrElse(0),
                      limit = limit.getOrElse(10)
                    ).map(_.map(TaskOutputResponse.apply))
                  )
                }
              }
            },
            `GET /:id` { taskId =>
              complete(taskService.getTask(userACL, ProcessingTaskId(taskId)).map(taskOpt => taskOpt.map(BatchJobInfo(_))))
            },
            `GET /` {
              parameters("offset".as[Long].?, "limit".as[Long].?) {
                (offset: Option[Long], limit: Option[Long]) => {
                  complete(taskService.listTasks(userACL, offset = offset.getOrElse(0), limit = limit.getOrElse(10)).map(tasks => tasks.map(BatchJobInfo(_))))
                }
              }
            },
            `POST /` {
              entity(as[ScheduleJob]) { request =>
                complete(taskService.createTask(userACL, request.name.getOrElse(traceId.toString), "users", request.jobs).map(BatchJobInfo(_)))
              }
            }
          )
        }
      )
    }

  val V3_BATCH_JOBS: Directive0 = pathPrefix("simon" / "api" / "v3" / "batch-jobs")

  def `GET /`: Directive0 = get

  def `POST /`: Directive0 = post

  def `GET /:id`: Directive1[String] = get & path(Segment) & pathEnd

  def `GET /:id/outputs`: Directive1[String] = get & path(Segment / "outputs") & pathEnd
}
