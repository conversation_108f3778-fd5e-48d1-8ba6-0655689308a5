package com.simonmarkets.tasks

import akka.actor.ActorSystem
import akka.stream.Materializer
import com.amazonaws.services.lambda.runtime.{Context, RequestStreamHandler}
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.tasks.api._
import com.simonmarkets.tasks.config.AppConfiguration
import com.simonmarkets.tasks.service.ServiceLocator
import com.simonmarkets.utils.config.Resolvers.SecretsConfigOps
import com.typesafe.config.ConfigFactory
import simon.Id.ProcessingTaskId

import java.io.{InputStream, OutputStream}

import scala.concurrent.duration.{Duration, _}
import scala.concurrent.{Await, ExecutionContext}

class StartTaskProcessingApp extends RequestStreamHandler with JsonCodecs with TraceLogging {
  implicit lazy val system: ActorSystem = ActorSystem("FinishTaskProcessingApp")
  implicit lazy val mat: Materializer = Materializer(system)
  implicit lazy val executionContext: ExecutionContext = system.dispatcher

  private final val timeout: Duration = Duration(10, SECONDS)

  private final val rawConfig = ConfigFactory.load().resolveSecrets()
  private final val config = AppConfiguration(rawConfig)

  private final val serviceLocator = new ServiceLocator(config)

  override def handleRequest(inputStream: InputStream, outputStream: OutputStream, context: Context): Unit = {
    implicit val traceId: TraceId = TraceId(context.getAwsRequestId)
    log.trace("Handling start processing request")

    val bufferedSource = scala.io.Source.fromInputStream(inputStream)
    val inputString = bufferedSource.mkString
    bufferedSource.close()

    decode[StartTaskInput](inputString) match {
      case Right(taskInput: StartTaskInput) =>
        implicit val traceId: TraceId = TraceId(ProcessingTaskId unwrap taskInput.id)
        log.trace("Handling start processing request")

        val resultString = Await.result(for {
          user <- serviceLocator.httpACLClient.getUserACL(taskInput.initiator)
          processingTask <- serviceLocator.tasksService.startProcessing(user, taskInput.id)
        } yield StartTaskOutput(processingTask).asJsonStr, timeout)

        outputStream.write(resultString.getBytes())
        outputStream.flush()
        outputStream.close()

      case Left(e) =>
        log.error(e, s"Cannot parse task event", inputString)
        throw HttpError.badRequest(s"Cannot parse task event: $inputString")
    }
  }
}
