package com.simonmarkets.api.users.compatibility

import com.goldmansachs.marquee.pipg.UserAclField
import org.scalatest.{Matchers, WordSpec}

class UserACLCompatibleSpec extends WordSpec with Matchers {

  case class CompatibleUser(userId: String, email: String)
  case class IncompatibleUser(id: String, email: String)

  def fields[T: UserACLCompatible]: Seq[UserAclField] = implicitly[UserACLCompatible[T]].fields

  "UserACLCompatible" should {
    "generate an instance for compatible class" in {
      fields[CompatibleUser] should contain theSameElementsAs Seq(UserAclField.userId, UserAclField.email)
    }
    "fail to generate an instance for incompatible class" in {
      an[IllegalArgumentException] should be thrownBy fields[IncompatibleUser]
    }
  }
}
