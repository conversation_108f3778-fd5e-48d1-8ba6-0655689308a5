package com.simonmarkets.api.users

import akka.actor.ActorSystem
import akka.event.LoggingAdapter
import akka.http.scaladsl.model.{ContentTypes, HttpEntity, HttpHeader, HttpRequest, HttpResponse, StatusCodes, Uri}
import akka.http.scaladsl.settings.ConnectionPoolSettings
import akka.http.scaladsl.{HttpExt, HttpsConnectionContext}
import akka.stream.Materializer
import com.goldmansachs.marquee.pipg.{User, UserACL, UserAclField, UserRole}
import com.simonmarkets.api.users.HttpACLClient.{GetPrincipalsAPIRequest, UserAclQueryRequest}
import com.simonmarkets.api.users.cache.UserAclRepository
import com.simonmarkets.api.users.codec.HttpACLClientCodecs
import com.simonmarkets.http.authentication.NoAuthCredentials
import com.simonmarkets.http.retry.RetryStrategy
import com.simonmarkets.http.{FutureHttpClient, HttpClientConfig, Http<PERSON><PERSON>oder, HttpEncoder}
import com.simonmarkets.logging.TraceId
import org.mockito.Mockito._
import org.mockito.{ArgumentMatcher, ArgumentMatchers => MM}
import org.scalatest.concurrent.{PatienceConfiguration, ScalaFutures}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.time.{Seconds, Span}
import org.scalatest.{Matchers, WordSpec}
import simon.Id

import java.time.LocalDateTime

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class HttpACLClientSpec extends WordSpec with Matchers with MockitoSugar with HttpACLClientCodecs {

  implicit val traceId: TraceId = TraceId.randomize
  implicit val retryStrategy = RetryStrategy()

  implicit val system: ActorSystem = ActorSystem("test")
  implicit val mat: Materializer = Materializer(system)

  val httpClient: FutureHttpClient = mock[FutureHttpClient]
  val cache = mock[UserAclRepository]

  val userId = "0oaevl54gplnFvyx70h7"

  val path = "https://origin-dc1.api.qa.simonmarkets.com/simon/api"


  val user = User(userId, Id.NetworkId("123"), LocalDateTime.now, "test", LocalDateTime.now, "test",
    None, None, None, "", "firstName", "lastName", None, None, roles = Set.empty[UserRole], idpLoginId = "idpLoginId")
  val userACL = UserACL(user)

  private val timeout: PatienceConfiguration.Timeout = PatienceConfiguration.Timeout(Span(120, Seconds))

  case class TestUser(userId: String, email: String)

  "HttpACLClient without Cache" should {

    val client = new HttpACLClient(httpClient, path, None)


    "Return userACL by id from the service" in {
      when(httpClient.get[UserACL](MM.eq[Uri](s"$path/v2/principals/${userId}"), MM.any[List[HttpHeader]])
        (MM.any[HttpDecoder[UserACL]](), MM.any[TraceId](), MM.any[RetryStrategy], MM.any[FutureHttpClient.RequestTag])) thenReturn Future.successful(userACL)

      val res = client.getUserACL(userId)
      ScalaFutures.whenReady(res, timeout) {
        userAcl => userAcl shouldBe userACL
      }
    }
    "Return impersonatedUserACL from the service" in {
      val impersonatedUserId = "01f92c4259664dc09739419367781035"
      when(httpClient.get[UserACL](MM.eq[Uri](s"$path/v2/principals/$impersonatedUserId?from=$userId"), MM.any[List[HttpHeader]])
        (MM.any[HttpDecoder[UserACL]](), MM.any[TraceId](), MM.any[RetryStrategy], MM.any[FutureHttpClient.RequestTag])) thenReturn Future.successful(userACL)

      val res = client.getImpersonatedUserACL(impersonatedUserId, userId)
      ScalaFutures.whenReady(res, timeout) {
        userAcl => userAcl shouldBe userACL
      }
    }
    "Return email from the service" in {
      val email = "<EMAIL>"
      when(httpClient.get[UserACL](MM.eq[Uri](s"$path/v2/principals/$email?principalIdType=email"), MM.any[List[HttpHeader]])
        (MM.any[HttpDecoder[UserACL]](), MM.any[TraceId](), MM.any[RetryStrategy], MM.any[FutureHttpClient.RequestTag])) thenReturn Future.successful(userACL)

      val res = client.getUserACLByEmail(email)
      ScalaFutures.whenReady(res, timeout) {
        userAcl => userAcl shouldBe userACL
      }
    }

    "GET UserACL with required fields" in {
      val expected = TestUser(userId = userACL.userId, email = userACL.email)
      when(httpClient.get[TestUser](MM.eq[Uri](s"$path/v2/principals/$userId?fields=userId,email"), MM.any[List[HttpHeader]])
        (MM.any[HttpDecoder[TestUser]](), MM.any[TraceId](), MM.any[RetryStrategy], MM.any[FutureHttpClient.RequestTag])).thenReturn(Future.successful(expected))

      val res = client.getSparseUserACL[TestUser](userId)
      ScalaFutures.whenReady(res, timeout) { actual =>
        actual shouldBe expected
      }
    }

    "Query UserACLs with required fields" in {
      val anotherId = "anotherId"

      val testUser = TestUser(userId = userACL.userId, email = userACL.email)
      val expected = List(testUser, testUser.copy(userId = anotherId))


      def requestWithSpecificFields: ArgumentMatcher[UserAclQueryRequest] = {
        case request: UserAclQueryRequest => request.fields.contains(Set(UserAclField.userId, UserAclField.email))
        case _ => false
      }

      when(
        httpClient.post[UserAclQueryRequest, List[TestUser]](
          MM.eq[Uri](s"$path/v2/principals/query"),
          MM.argThat(requestWithSpecificFields),
          MM.any[List[HttpHeader]])
          (MM.any[HttpEncoder[UserAclQueryRequest]](),
            MM.any[HttpDecoder[List[TestUser]]](),
            MM.any[TraceId](),
            MM.any[RetryStrategy],
            MM.any[FutureHttpClient.RequestTag])).thenReturn(Future.successful(expected))

      val res = client.getSparseUserACLsByIdType[TestUser](Set(userId, anotherId), from = None, principalIdType = None, networkId = None, action = None)
      ScalaFutures.whenReady(res, timeout) { actual =>
        actual shouldBe expected
      }
    }
  }

  "HttpAclClient with Cache" should {
    val client = new HttpACLClient(httpClient, path, Some(cache))

    "Return userACL from cache if its there" in {
      when(cache.get(userId)) thenReturn Some(userACL)

      val res = client.getUserACL(userId)
      ScalaFutures.whenReady(res, timeout) {
        userAcl =>
          verify(cache, times(1)).get(userId)
          userAcl shouldBe userACL
      }
    }
    "Return userACL from server if its not in the cache" in {
      val httpClient2: FutureHttpClient = mock[FutureHttpClient]
      val client = new HttpACLClient(httpClient2, path, Some(cache))

      when(cache.get(userId)) thenReturn None
      when(httpClient2.get[UserACL](MM.eq[Uri](s"$path/v2/principals/${userId}"), MM.any[List[HttpHeader]])
        (MM.any[HttpDecoder[UserACL]](), MM.any[TraceId](), MM.any[RetryStrategy], MM.any[FutureHttpClient.RequestTag])) thenReturn Future.successful(userACL)

      val res = client.getUserACL(userId)
      ScalaFutures.whenReady(res, timeout) {
        userAcl =>
          userAcl shouldBe userACL
          verify(httpClient2, times(1)).get[UserACL](s"$path/v2/principals/${userId}")

      }
    }
    "Return userACL from server if there is an exception on the cache query" in {
      val httpClient2: FutureHttpClient = mock[FutureHttpClient]
      val client = new HttpACLClient(httpClient2, path, Some(cache))

      when(cache.get(userId)) thenReturn None
      when(httpClient2.get[UserACL](MM.eq[Uri](s"$path/v2/principals/${userId}"), MM.any[List[HttpHeader]])
        (MM.any[HttpDecoder[UserACL]](), MM.any[TraceId](), MM.any[RetryStrategy], MM.any[FutureHttpClient.RequestTag])) thenReturn Future.successful(userACL)

      val res = client.getUserACL(userId)
      ScalaFutures.whenReady(res, timeout) {
        userAcl =>
          userAcl shouldBe userACL
          verify(httpClient2, times(1)).get[UserACL](s"$path/v2/principals/${userId}")

      }
    }
    "Return userAcls from cache if its there" in {

      val userId1 = "14512"
      val userId2 = "165161"
      val expected = List(userACL, userACL.copy(userId = userId1), userACL.copy(userId = userId2))
      val userIds = Set(userId, userId1, userId2)
      when(cache.getAll(userIds)) thenReturn expected

      val res = client.getUserACLs(userIds)
      ScalaFutures.whenReady(res, timeout) {
        userAcls => userAcls shouldBe expected
      }
    }

    "Query the missing userACLS from the server" in {
      val userId1 = "14512"
      val userId2 = "165161"
      val expected = List(userACL, userACL.copy(userId = userId1))
      val userIds = Set(userId, userId1, userId2)
      when(cache.getAll(userIds)) thenReturn expected
      when(httpClient.post[GetPrincipalsAPIRequest, List[UserACL]]
        (MM.eq[Uri](s"$path/v2/principals"),
          MM.eq[GetPrincipalsAPIRequest](GetPrincipalsAPIRequest(Set(userId2))),
          MM.any[List[HttpHeader]])
        (MM.any[HttpEncoder[GetPrincipalsAPIRequest]](), MM.any[HttpDecoder[List[UserACL]]](), MM.any[TraceId](),
          MM.any[RetryStrategy], MM.any[FutureHttpClient.RequestTag]))
        .thenReturn(Future(List(userACL.copy(userId = userId2))))


      val res = client.getUserACLs(userIds)
      ScalaFutures.whenReady(res, timeout) {
        userAcls => userAcls shouldBe userACL.copy(userId = userId2) :: expected
      }
    }

    "Query each of the userACls from server if none is in the cache" in {
      val httpClient2: FutureHttpClient = mock[FutureHttpClient]
      val client = new HttpACLClient(httpClient2, path, Some(cache))

      val userId1 = "14512"
      val userId2 = "165161"
      val expected = List(userACL, userACL.copy(userId = userId1), userACL.copy(userId = userId2))
      val userIds = Set(userId, userId1, userId2)

      when(cache.getAll(userIds)) thenReturn List()

      when(httpClient2.post[GetPrincipalsAPIRequest, List[UserACL]]
        (MM.eq[Uri](s"$path/v2/principals"),
          MM.eq[GetPrincipalsAPIRequest](GetPrincipalsAPIRequest(userIds)),
          MM.any[List[HttpHeader]])
        (MM.any[HttpEncoder[GetPrincipalsAPIRequest]](), MM.any[HttpDecoder[List[UserACL]]](), MM.any[TraceId](),
          MM.any[RetryStrategy], MM.any[FutureHttpClient.RequestTag]()))
        .thenReturn(Future(expected))


      val res = client.getUserACLs(userIds)
      ScalaFutures.whenReady(res, timeout) {
        userAcls =>
          userAcls shouldBe expected
          verify(httpClient2, times(1)).post[GetPrincipalsAPIRequest, List[UserACL]](s"$path/v2/principals", GetPrincipalsAPIRequest(userIds))
      }
    }

    "Query userACls from server if the cache query fails" in {
      val httpClient2: FutureHttpClient = mock[FutureHttpClient]
      val client = new HttpACLClient(httpClient2, path, Some(cache))

      val userId1 = "14512"
      val userId2 = "165161"
      val expected = List(userACL, userACL.copy(userId = userId1), userACL.copy(userId = userId2))
      val userIds = Set(userId, userId1, userId2)

      when(cache.getAll(userIds)) thenReturn List()

      when(httpClient2.post[GetPrincipalsAPIRequest, List[UserACL]]
        (MM.eq[Uri](s"$path/v2/principals"),
          MM.eq[GetPrincipalsAPIRequest](GetPrincipalsAPIRequest(userIds)),
          MM.any[List[HttpHeader]])
        (MM.any[HttpEncoder[GetPrincipalsAPIRequest]](), MM.any[HttpDecoder[List[UserACL]]](), MM.any[TraceId](),
          MM.any[RetryStrategy], MM.any[FutureHttpClient.RequestTag]()))
        .thenReturn(Future(expected))


      val res = client.getUserACLs(userIds)
      ScalaFutures.whenReady(res, timeout) {
        userAcls =>
          userAcls shouldBe expected
          verify(httpClient2, times(1)).post[GetPrincipalsAPIRequest, List[UserACL]](s"$path/v2/principals", GetPrincipalsAPIRequest(userIds))
      }
    }

    "UserACL decoding should work until circe bug is fixed" in {
      // We have a workaround, but it's easy to remove it by mistake, so here's a test to ensure that doesn't happen
      // https://github.com/circe/circe/issues/1453
      // The workaround is `HttpACLClient.ioiApproverSetDecoder`
      val http = mock[HttpExt]
      val httpClient = new FutureHttpClient(http, HttpClientConfig(None, NoAuthCredentials))

      val aclClient = new HttpACLClient(httpClient, path, None)

      val userAcl2 = userACL.copy(ioiApproverSet = Map("a" -> List(List("b"))))
      val userId1 = "14512"
      val userId2 = "165161"
      val expected = List(userAcl2, userAcl2.copy(userId = userId1), userAcl2.copy(userId = userId2))
      val userIds = Set(userId, userId1, userId2)

      val entity = HttpEntity(ContentTypes.`application/json`, expected.asJsonStr)
      when(http.singleRequest(MM.any[HttpRequest], MM.any[HttpsConnectionContext], MM.any[ConnectionPoolSettings], MM.any[LoggingAdapter]))
        .thenReturn(Future.successful(HttpResponse(StatusCodes.OK, Nil, entity)))

      ScalaFutures.whenReady(aclClient.getUserACLs(userIds), timeout) { res =>
        res should contain theSameElementsAs expected
      }
    }
  }
}
