package com.simonmarkets.api.users.cache

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.api.users.cache.dynamodb.CachedUserAcl
import com.simonmarkets.logging.TraceId

import scala.concurrent.Future

trait UserAclRepository {

  def get(id: String)(implicit traceId: TraceId): Option[UserACL]

  def getWithMetadata(id: String)(implicit traceId: TraceId): Future[Option[CachedUserAcl]]

  def getAll(ids: Set[String])(implicit traceId: TraceId): List[UserACL]

  def put(acl: UserACL)(implicit traceId: TraceId): Future[Unit]

  def putAll(acls: List[UserACL])(implicit traceId: TraceId): Future[Unit]

  def delete(id: String)(implicit traceId: TraceId): Future[Unit]

  def deleteAll(ids: List[String])(implicit traceId: TraceId): Future[Unit]

  def recreateTableWithUserAcls(acls: List[UserACL])(implicit traceId: TraceId): Future[Unit]
}

