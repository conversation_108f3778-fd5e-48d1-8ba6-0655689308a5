package com.simonmarkets.api.users.compatibility

import com.goldmansachs.marquee.pipg.UserAclField
import magnolia._

/**
 * Why do we need this class? <br>
 * We have an UserACL class that contains a lot of fields.
 * Most of the time services don't care about all existing fields and just want to take a subset of them for their domain.
 * We could have just make a generic version of the `getUserACL` method like
 * <pre>
 * def getUserACL[T](id: String): F[T]</pre>
 * But this approach has two problems
 * <lu>
 * <li> Someone can accidentally include a fields that is not present in a UserACL class </li>
 * <li> We have to fetch full UserACL model every time and then get rid of the data </li>
 * </lu>
 *
 * This typeclass proves that the fields of class `T` are compatible with `UserACL` fields.
 * At the same time `fields` method tells which fields we will need to fetch from UserACL to assemble a custom class.<br>
 */
trait UserACLCompatible[T] {
  def fields: Seq[UserAclField]
}

/**
 * Methods in this object allow to automatically generate a list of fields of the provided case class <br>
 * For example, if we have a case class<br>
 * <pre> case class AlmostAUser(userId: String, email: String, firstName: String) </pre>
 * then this method will return `Seq(userId, email, firstName)`<br>
 * However, if we have an incompatible class like<br>
 * <pre> case class NotAUser(notAUserId: String) </pre>
 * then this method will throw an exception since it won't be able to map string representation of a field to enum
 *
 * We *can* manually create an instance of `UserACLCompatible[Foo]` for any arbitrary class. This is just to get rid of boilerplate.<br>
 * For example, if for some reason you are not satisfied with automatic generation of the list of the fields, you can just create an instance of this typeclass in your companion object.
 * <pre>
 * case class User(userId: String, email: String)
 * object User {
 *     implicit val userAclCompatibleUser: UserACLCompatible[User] = new UserACLCompatible[User] {
 *       def fields: Seq[UserAclField] = Seq(UserAclField.userId, UserAclField.firstName)
 *     }
 * }
 * </pre>
 * This instance will override an instance generated by magnolia
 */
object UserACLCompatible extends LowPriorityToUserACLFields {

  // this typeconstrutor is required by magnolia
  type Typeclass[T] = UserACLCompatible[T]

  /**
   * This is where main magnolia magic happens
   * Effectively what this method does is <br>
   * - iterates over all parameters (fields) of the case class<br>
   * - takes their labels (names)<br>
   * - maps labels to enum instead of String for extra type safety<br>
   */
  def combine[T](ctx: CaseClass[Typeclass, T]): Typeclass[T] = new Typeclass[T] {
    override val fields: Seq[UserAclField] =
      ctx.parameters
        .map(_.label)
        .map {
          case UserAclField(field) => field
          case notAField =>
            val errMsg = s"case class [${ctx.typeName.full}] has a field [$notAField] which is not present in UserACL"
            throw new IllegalArgumentException(errMsg)
        }
  }

  // we don't need to support SealedTraits hierarchy (at least for now)
  def dispatch[T](ctx: SealedTrait[Typeclass, T]): Typeclass[T] = ???

  /**
   * This method allows the following syntaxt
   * <pre>
   * case class User(userId: String, email: String)
   * def getUser[T: UserACLCompatible](id: String): T = ???
   * val user = getUser[User]("id")</pre>
   * We don't need to create an instance of `UserACLCompatible[User]` at all. This method is available wherever `UserACLCompatible` is imported and it will take care of typeclass creation.
   */
  implicit def gen[T]: Typeclass[T] = macro Magnolia.gen[T]

}

/**
 * Magnolia doesn't know that we won't deeply analyze case class fields and requires typeclasses for every type of the field (like String, Boolean, etc.)
 * This fallback trick is required to make it compile
 */
trait LowPriorityToUserACLFields {
  implicit def fallback[T]: UserACLCompatible[T] = new UserACLCompatible[T] {
    override def fields: Seq[UserAclField] = Seq.empty
  }
}
