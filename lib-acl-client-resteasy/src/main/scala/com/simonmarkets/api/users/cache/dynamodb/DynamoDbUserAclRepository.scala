package com.simonmarkets.api.users.cache.dynamodb

import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.amazonaws.services.dynamodbv2.document._
import com.amazonaws.services.dynamodbv2.document.spec.GetItemSpec
import com.amazonaws.services.dynamodbv2.model._
import com.amazonaws.services.dynamodbv2.util.TableUtils
import com.goldmansachs.marquee.pipg.{ApproverMap, UserACL}
import com.simonmarkets.api.users.cache.UserAclRepository
import com.simonmarkets.api.users.cache.dynamodb.DynamoDbUserAclRepository._
import com.simonmarkets.circe.{CirceNullAsEmptyDecoders, JsonCodecs}
import com.simonmarkets.dynamodb.client.DynamoDbClientFactory
import com.simonmarkets.dynamodb.config.DynamoDbConfiguration
import com.simonmarkets.logging.{TraceId, TraceLogging}

import java.time.LocalDateTime

import scala.annotation.{nowarn, tailrec}
import scala.collection.JavaConverters._
import scala.collection.mutable
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

@nowarn("cat=deprecation")
class DynamoDbUserAclRepository(dynamoDbConfiguration: DynamoDbConfiguration)(implicit ec: ExecutionContext)
  extends UserAclRepository with JsonCodecs with CirceNullAsEmptyDecoders with TraceLogging {

  private val client = DynamoDbClientFactory.syncClient(dynamoDbConfiguration)

  private val dynamoDB = new DynamoDB(client)

  private val table: Table = dynamoDB.getTable(DynamoDbUserAclRepository.tableName)

  override def put(acl: UserACL)(implicit traceId: TraceId): Future[Unit] = {
    log.info("Add user acl to cache", acl.userId)
    Future {
      table.putItem(userAclToDynamoItem(acl))
    }.andThen {
      case Failure(ex) =>
        log.error("Couldn't put entry to userACL cache", ex, "id" -> acl.userId)
    }.map(_ => ())
  }

  override def putAll(acls: List[UserACL])(implicit traceId: TraceId): Future[Unit] = {
    Future {
      //dynamodb batchwrite has a limit of 25 items
      acls.grouped(25).foreach { groupedAcls =>
        safeBatchWriteAcls(groupedAcls)
      }
    }
  }

  override def getWithMetadata(id: String)(implicit traceId: TraceId): Future[Option[CachedUserAcl]] = {
    Future {
      Try {
        table.getItem(new GetItemSpec().withPrimaryKey(Fields.USER_ID, id))
      } match {
        case Success(null) =>
          log.info(s"No entry found in cache", id)
          None
        case Success(cacheHit) =>
          log.info(s"Found acl in cache", id)
          decodeUserAcl(cacheHit.getString(Fields.DATA)).map {
            acl => CachedUserAcl(acl, cacheHit.getString(Fields.TRACE_ID), cacheHit.getString(Fields.UPDATED_TIMESTAMP))
          }
        case Failure(ex) =>
          log.error(ex, s"Failed loading userACL from cache")
          throw ex
      }
    }
  }

  override def get(id: String)(implicit traceId: TraceId): Option[UserACL] = {
      Try {
        val startTime = System.currentTimeMillis()
        val result = table.getItem(new GetItemSpec().withPrimaryKey(Fields.USER_ID, id)).getString(Fields.DATA)
        log.info(s"Dynamodb getItem took ${System.currentTimeMillis() - startTime} ms")
        result
      } match {
        case Success(cacheHit) =>
          log.info(s"Found acl in cache", id)
          decodeUserAcl(cacheHit)
        case Failure(_: NullPointerException) =>
          log.info(s"No entry found in cache", id)
          None
        case Failure(ex) =>
          log.error(ex, s"Failed loading userACL from cache")
          None
      }
  }

  override def getAll(ids: Set[String])(implicit traceId: TraceId): List[UserACL] = {
    //batch get has limit of 100 items
    Try {
      ids.grouped(100).flatMap(batchGetAcls).toList
    } match {
      case Success(acls) => acls
      case Failure(ex) =>
        log.error(ex, "Failed getting userAcls in batch from dynamoDB")
        List()
    }
  }

  override def delete(id: String)(implicit traceId: TraceId): Future[Unit] = {
    log.info(s"Deleting acl entry for userId=$id")
    Future(table.deleteItem(Fields.USER_ID, id))
  }
  override def deleteAll(ids: List[String])(implicit traceId: TraceId): Future[Unit] = {
    Future(ids.grouped(25).foreach(batchDelete))
  }

  override def recreateTableWithUserAcls(acls: List[UserACL])(implicit traceId: TraceId): Future[Unit] = {
    log.info(s"Reloading DynamoDBUserACL cache with ${acls.size} acls")
    log.info("Deleting acl cache table")
    Try {
      table.delete()
      table.waitForDelete()
      log.info(s"$tableName has been successfully deleted")
    }
    log.info(s"Recreating table", tableName)
    createTableIfNotExists(client).flatMap{ _ =>
      putAll(acls)
    }
  }

  private def safeBatchWriteAcls(acls: List[UserACL])(implicit tid: TraceId): Unit = {

    Try(batchWriteAcls(acls)) match {
      case Success(_) => log.info("ACLs added into the cache", "userIds" -> acls.map(_.userId).mkString(","))
      case Failure(e) =>
        log.alert("One or more ACLs in the batch failed to be added to the cache", e, "userIds" -> acls.map(_.userId).mkString(","))
    }
  }

  private def batchWriteAcls(acls: List[UserACL])(implicit tid: TraceId): Unit = {
    val itemsToAdd = new TableWriteItems(DynamoDbUserAclRepository.tableName)
      .withItemsToPut(acls.map(acl => userAclToDynamoItem(acl)).asJava)

    val result = dynamoDB.batchWriteItem(itemsToAdd)
    writeUnprocessedBatchItems(result)
  }

  private def batchDelete(ids: List[String])(implicit tid: TraceId): Unit = {
    log.info(s"Deleting ${ids.size} items from acl cache")
    val itemsToDelete = new TableWriteItems(DynamoDbUserAclRepository.tableName)
      .withHashOnlyKeysToDelete(Fields.USER_ID, ids:_*)

    val result = dynamoDB.batchWriteItem(itemsToDelete)
    writeUnprocessedBatchItems(result)
  }

  @tailrec
  private def writeUnprocessedBatchItems(outcome: BatchWriteItemOutcome)(implicit tid: TraceId): Unit = {
    val unprocessedItems = outcome.getUnprocessedItems
    if (!unprocessedItems.isEmpty) {
      val unprocessedUserIds: mutable.Seq[AttributeValue] = unprocessedItems.getOrDefault(DynamoDbUserAclRepository.tableName, List.empty.asJava).asScala.flatMap(
        item => item.getPutRequest.getItem.asScala.get(DynamoDbUserAclRepository.Fields.USER_ID))
      log.info(s"Some ACLS are unprocessed", "userIds" -> unprocessedUserIds)
      val result = dynamoDB.batchWriteItemUnprocessed(unprocessedItems)
      writeUnprocessedBatchItems(result)
    } else {
      log.debug("Processed each item for BatchWrite")
    }
  }

  @tailrec
  private def readUnprocessedBatchItems(outcome: BatchGetItemOutcome, userAcls: List[UserACL])
    (implicit tid: TraceId): List[UserACL] = {
    val unprocessedItems = outcome.getUnprocessedKeys
    if (unprocessedItems.size() != 0) {
      log.debug(s"${unprocessedItems.size()} unprocessed item left from batch get")
      val result = dynamoDB.batchGetItemUnprocessed(unprocessedItems)
      readUnprocessedBatchItems(result, userAcls ::: readUserAclsFromBatchGetResult(result))
    } else {
      log.debug("Processed each item for BatchGet", "count" -> userAcls.size)
      userAcls
    }
  }

  private def userAclToDynamoItem(acl: UserACL)(implicit traceId: TraceId): Item = {
    new Item()
      .withPrimaryKey(Fields.USER_ID, acl.userId)
      .withString(Fields.DATA, acl.copy(
        approverMap = ApproverMap.empty,
        payoffCertificationRequirementsList = Seq.empty,
        videoTracksEntitlements = Map.empty,
        payoffEntitlementsV2 = Map.empty,
        dynamicRoles = Set.empty,
        learnTracks = Seq.empty,
        spCertificationRequirements = Seq.empty
      ).asJson.noSpaces)
      .withString(Fields.TRACE_ID, traceId.toString)
      .withString(Fields.UPDATED_TIMESTAMP, LocalDateTime.now().toString)
  }

  private def batchGetAcls(ids: Set[String])(implicit tid: TraceId): List[UserACL] = {
    log.info(s"Reading ${ids.size} items from cache")
    val tableKeysAndAttributes = new TableKeysAndAttributes(tableName)

    ids.foreach(id => tableKeysAndAttributes.addPrimaryKey(new PrimaryKey(Fields.USER_ID, id)))

    val result = dynamoDB.batchGetItem(tableKeysAndAttributes)

    val userAcls = readUserAclsFromBatchGetResult(result)

    readUnprocessedBatchItems(result, userAcls)
  }

  private def readUserAclsFromBatchGetResult(result: BatchGetItemOutcome)
    (implicit traceId: TraceId): List[UserACL] = {
    result.getBatchGetItemResult.getResponses.get(tableName).asScala.map {
      entry =>
        entry.asScala.get(Fields.DATA).flatMap { attr =>
          decodeUserAcl(attr.getS)
        }
    }.collect { case Some(acl) => acl }.toList
  }

  private def decodeUserAcl(json: String)(implicit traceId: TraceId) = {
    decode[UserACL](json) match {
      case Left(failure) =>
        log.error("Couldn't decode userACL from cache", failure)
        None
      case Right(value) => Some(value)
    }
  }


}

object DynamoDbUserAclRepository {

  val tableName = "user-acl-cache"

  def createTableIfNotExists(client: AmazonDynamoDB)(implicit ec: ExecutionContext): Future[Unit] = {

    val request =
      new CreateTableRequest()
        .withTableName(tableName)
        .withKeySchema(new KeySchemaElement(Fields.USER_ID, KeyType.HASH))
        .withAttributeDefinitions(
          new AttributeDefinition(Fields.USER_ID, ScalarAttributeType.S),
        )
        .withBillingMode("PAY_PER_REQUEST")

    Future {
      TableUtils.createTableIfNotExists(client, request)
      TableUtils.waitUntilActive(client, tableName)
    }
  }

  object Fields {
    val USER_ID = "userId"
    val DATA = "data"
    val UPDATED_TIMESTAMP = "updated"
    val TRACE_ID = "traceId"
  }

}
