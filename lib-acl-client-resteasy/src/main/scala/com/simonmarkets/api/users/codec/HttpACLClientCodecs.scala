package com.simonmarkets.api.users.codec

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.api.users.ACLClient.UserIdSSNResponse
import com.simonmarkets.api.users.HttpACLClient._
import com.simonmarkets.circe.{CirceNullAsEmptyDecoders, JsonCodecs}
import io.circe.Codec
import io.circe.generic.semiauto.deriveCodec

import scala.annotation.nowarn

@nowarn("cat=deprecation")
trait HttpACLClientCodecs extends JsonCodecs with CirceNullAsEmptyDecoders {

  implicit lazy val userACLCodec: Codec[UserACL] = deriveCodec

  implicit lazy val getPrincipalsAPIRequestCodec: Codec[GetPrincipalsAPIRequest] = deriveCodec

  implicit lazy val getPrincipalsForActionAPIRequestCodec: Codec[GetPrincipalsForActionAPIRequest] = deriveCodec

  implicit lazy val userAclQueryRequestCodec: Codec[UserAclQueryRequest] = deriveCodec

  implicit lazy val userIdSSNRequestCodec: Codec[UserIdSSNRequest] = deriveCodec

  implicit lazy val userIdSSNResponseCodec: Codec[UserIdSSNResponse] = deriveCodec

}
