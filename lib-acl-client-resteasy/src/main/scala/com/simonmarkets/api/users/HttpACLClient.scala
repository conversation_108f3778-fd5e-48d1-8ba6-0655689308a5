package com.simonmarkets.api.users

import akka.actor.ActorSystem
import akka.http.scaladsl.Http
import akka.http.scaladsl.model.Uri.Query
import akka.http.scaladsl.model.{Http<PERSON>ead<PERSON>, U<PERSON>}
import akka.stream.Materializer
import akka.stream.scaladsl.{Sink, Source}
import com.goldmansachs.marquee.pipg.{UserACL, UserAclField}
import com.simonmarkets.api.users.ACLClient.UserIdSSNResponse
import com.simonmarkets.api.users.HttpACLClient.{GetPrincipalsAPIRequest, GetPrincipalsForActionAPIRequest, UserAclQueryRequest, UserIdSSNRequest}
import com.simonmarkets.api.users.cache.UserAclRepository
import com.simonmarkets.api.users.cache.dynamodb.DynamoDbUserAclRepository
import com.simonmarkets.api.users.codec.HttpACLClientCodecs
import com.simonmarkets.api.users.compatibility.UserACLCompatible
import com.simonmarkets.api.users.config.{AclClientConfig, CacheConfig}
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.http.FutureHttpClient.Tag
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.users.common.{AttributeType, IdType}
import io.circe.Decoder

import java.net.URLEncoder

import scala.concurrent.{ExecutionContext, Future}

class HttpACLClient(httpClient: FutureHttpClient, path: String, cache: Option[UserAclRepository] = None)
  (implicit ec: ExecutionContext, mat: Materializer)
  extends ACLClient[Future] with HttpACLClientCodecs with TraceLogging {

  private implicit val tag: Tag = Tag("acl-client")
  private val pathPrincipals = s"$path/v2/principals"

  override def ping(implicit traceId: TraceId): Future[Unit] =
    httpClient.get[String](s"$pathPrincipals/healthcheck").map(_ => ())

  override def getUserACL(userId: String)(implicit traceId: TraceId): Future[UserACL] = {
    val startTime = System.currentTimeMillis()

    def getAclFromServer: Future[UserACL] = {
      val uri = s"$pathPrincipals/$userId"
      log.debug(s"Getting user ACL by userId=$userId from uri=$uri")

      val result = httpClient.get[UserACL](uri)
      result.foreach(_ => log.info(s"Get UserACL totalMillis='${System.currentTimeMillis() - startTime}', fromCache='false'"))

      result
    }

    cache match {
      case Some(aclRepo) =>
        aclRepo.get(userId) match {
          case Some(acl) =>
            log.info(s"Returning userACL from cache, totalMillis='${System.currentTimeMillis() - startTime}', fromCache='true'", userId)
            Future.successful(acl)
          case None =>
            log.info("Couldn't find userACL in cache", userId)
            getAclFromServer
        }
      case None => getAclFromServer
    }
  }

  override def getUserACLs(userIds: Set[String])(implicit traceId: TraceId): Future[List[UserACL]] = {

    def getAclsFromServer(ids: Set[String]) = {
      val uri = pathPrincipals
      log.debug(s"Getting user ACLs by userIds=$userIds from uri=$uri")
      httpClient.post[GetPrincipalsAPIRequest, List[UserACL]](uri = uri, data = GetPrincipalsAPIRequest(ids))
    }
    cache match {
      case Some(aclRepo) =>
        val userAcls = aclRepo.getAll(userIds)
        val missingUserAcls = userIds.filterNot(id => userAcls.exists(_.userId == id))
        if (missingUserAcls.nonEmpty) {
          getAclsFromServer(missingUserAcls).map(_ ++ userAcls)
        } else {
          Future.successful(userAcls)
        }
      case None => getAclsFromServer(userIds)
    }
  }

  override def getSparseUserACL[T: UserACLCompatible : Decoder](userId: String)(implicit traceId: TraceId): Future[T] = {
    val startTime = System.currentTimeMillis()

    val fields = implicitly[UserACLCompatible[T]].fields
    val fieldsQuery = fields.map(_.productPrefix).mkString(",")
    val uri = s"$pathPrincipals/$userId?fields=$fieldsQuery"
    log.debug(s"Getting user ACL by userId=$userId from uri=$uri")

    val result = httpClient.get[T](uri)
    result.foreach(_ => log.info(s"Get UserACL totalMillis='${System.currentTimeMillis() - startTime}'"))

    result
  }

  override def getSparseUserACLsByIdType[T: UserACLCompatible : Decoder](userIds: Set[String], from: Option[String],
      principalIdType: Option[IdType], networkId: Option[String], action: Option[String] = None, subject: Option[String] = None)
    (implicit traceId: TraceId): Future[List[T]] = {
    getSparseUserACLsByIdType(userIds, from, principalIdType, networkId, action, List.empty[HttpHeader], subject, None, Set.empty[String])
  }

  override def getSparseUserACLsByIdTypeWithView[T: UserACLCompatible : Decoder](userIds: Set[String], from: Option[String],
      principalIdType: Option[IdType], networkId: Option[String], subject: Option[String] = None)
    (implicit traceId: TraceId): Future[List[T]] = {
    getSparseUserACLsByIdType[T](userIds, from, principalIdType, networkId, Some("view"), List.empty[HttpHeader], subject, None, Set.empty[String])
  }

  override def getSparseUserACLsByIdTypeWithImpersonate[T: UserACLCompatible : Decoder](userIds: Set[String], from: Option[String],
      principalIdType: Option[IdType], networkId: Option[String], subject: Option[String] = None)
    (implicit traceId: TraceId): Future[List[T]] = {
    getSparseUserACLsByIdType[T](userIds, from, principalIdType, networkId, Some("impersonate"), List.empty[HttpHeader], subject, None, Set.empty[String])
  }

  override def getUserACLByEmail(email: String)(implicit traceId: TraceId): Future[UserACL] = {
    val uri = s"$pathPrincipals/$email?principalIdType=email"
    log.debug(s"Getting user ACL by email=$email from uri=$uri")
    httpClient.get[UserACL](uri)
  }

  override def getUserACLByNPN(npn: String)(implicit traceId: TraceId): Future[UserACL] = {
    val uri = s"$pathPrincipals/$npn?principalIdType=npn"
    log.debug(s"Getting user ACL by npn=$npn from uri=$uri")
    httpClient.get[UserACL](uri)
  }

  override def getUserByOmsAlias(oms: String)(implicit traceId: TraceId): Future[UserACL] = {
    val uri = s"$pathPrincipals/$oms?principalIdType=oms"
    log.debug(s"Getting user ACL by oms=$oms from uri=$uri")
    httpClient.get[UserACL](uri)
  }

  override def getUserByDistributorAlias(distributorAlias: String, networkId: String)
    (implicit traceId: TraceId): Future[UserACL] = {
    val uri = s"$pathPrincipals/$distributorAlias?principalIdType=distributorAlias&networkId=${URLEncoder.encode(networkId, "UTF-8")}"
    log.debug(s"Getting user ACL by distributorAlias=$distributorAlias networkId=$networkId from uri=$uri")
    httpClient.get[UserACL](uri)
  }

  override def getUsersForAction(ids: List[String], from: Option[String], principalIdType: Option[IdType],
      networkId: Option[String] = None, action: Option[String], batchSize: Int = 200, parallelism: Int = 4)
    (implicit traceId: TraceId): Future[Seq[UserACL]] = {
    if (ids.isEmpty) Future.successful(Seq.empty)
    else {
      val uri = s"$pathPrincipals/query"
      val requests = ids.grouped(batchSize)
        .map(batchedIds => GetPrincipalsForActionAPIRequest(
          batchedIds, from = from, principalIdType = principalIdType, networkId = networkId, action = action))

      Source.fromIterator(() => requests)
        .mapAsync(parallelism = parallelism)(request =>
          httpClient.post[GetPrincipalsForActionAPIRequest, List[UserACL]](uri = uri, data = request))
        .runWith(Sink.reduce[Seq[UserACL]](_ ++ _))
    }
  }

  override def getUsersWithView(ids: List[String], from: Option[String], principalIdType: Option[IdType],
      networkId: Option[String] = None, batchSize: Int = 200, parallelism: Int = 4)
    (implicit traceId: TraceId): Future[Seq[UserACL]] =
    getUsersForAction(ids, from, principalIdType, networkId, Some("view"), batchSize, parallelism)(traceId)

  override def getUsersWithImpersonation(ids: List[String], from: Option[String], principalIdType: Option[IdType],
      networkId: Option[String] = None, batchSize: Int = 200, parallelism: Int = 4)
    (implicit traceId: TraceId): Future[Seq[UserACL]] =
    getUsersForAction(ids, from, principalIdType, networkId, Some("impersonate"), batchSize, parallelism)(traceId)


  override def getUserIdSSNMap(ssns: List[String])(implicit traceId: TraceId): Future[List[UserIdSSNResponse]] = {
    val uri = s"$path/v1/user-ssn-maps/query"
    httpClient.post[UserIdSSNRequest, List[UserIdSSNResponse]](uri = uri, data = UserIdSSNRequest(ssns))
  }

  override def getImpersonatedUserACL(impersonatedUserId: String, authenticatedUserId: String)
    (implicit traceId: TraceId): Future[UserACL] = {
    log.debug("Requesting ACL", authenticatedUserId, "impersonatedUserId" -> impersonatedUserId)
    val uri = s"$pathPrincipals/$impersonatedUserId?from=$authenticatedUserId"
    httpClient.get[UserACL](uri)
  }

  override def getSparseUserACLsByIdType[T: UserACLCompatible : Decoder](userIds: Set[String], from: Option[String],
      principalIdType: Option[IdType], networkId: Option[String], action: Option[String],
      httpHeaders: List[HttpHeader], subject: Option[String], attributeType: Option[AttributeType],
      attributeValues: Set[String])(implicit traceId: TraceId): Future[List[T]] = {
    val fields = implicitly[UserACLCompatible[T]].fields.toSet
    val uri = s"$pathPrincipals/query"
    log.debug(s"Getting user ACLs", uri, principalIdType, fields, userIds)
    val request = UserAclQueryRequest(userIds, fields = Some(fields), from = from, principalIdType = principalIdType,
      networkId = networkId, subject = subject, action = action, attributeType = attributeType, attributeValues = attributeValues)
    httpClient.post[UserAclQueryRequest, List[T]](uri = uri, data = request, requestHeaders = httpHeaders)
  }

  override def getSparseUserACLByIdType[T: UserACLCompatible : Decoder](userId: String,
      principalIdType: Option[IdType], networkId: Option[String])(implicit traceId: TraceId): Future[T] = {
    val fields = implicitly[UserACLCompatible[T]].fields.toSet
    val fieldsQuery = fields.map(_.productPrefix).mkString(",")
    val queryMap = Map(
      "principalIdType" -> Some(principalIdType.getOrElse(IdType.UserId).asStr),
      "fields" -> Some(fieldsQuery),
      "networkId" -> networkId
    ).collect { case (k, Some(v)) => k -> v } // Ensure that network ID won't be present in query params if not sent
    val query = Query(queryMap)
    val uri = Uri(s"$pathPrincipals/$userId").withQuery(query)
    httpClient.get[T](uri)
  }

}

object HttpACLClient {

  case class GetPrincipalsAPIRequest(
      ids: Set[String],
      from: Option[String] = None,
      principalIdType: Option[IdType] = None,
      networkId: Option[String] = None,
      mod: Option[String] = None,
      fields: Option[Set[UserAclField]] = None)

  case class UserAclQueryRequest(
      ids: Set[String],
      from: Option[String] = None,
      principalIdType: Option[IdType] = None,
      networkId: Option[String] = None,
      subject: Option[String] = None,
      mod: Option[String] = None,
      fields: Option[Set[UserAclField]] = None,
      action: Option[String] = None,
      attributeType: Option[AttributeType] = None,
      attributeValues: Set[String] = Set.empty
  )

  case class GetPrincipalsForActionAPIRequest(
      ids: List[String],
      from: Option[String] = None,
      principalIdType: Option[IdType],
      networkId: Option[String] = None,
      action: Option[String] = None,
  )

  case class UserIdSSNRequest(ssns: List[String])

  def apply(client: FutureHttpClient, baseUrl: String, cacheConfigOpt: Option[CacheConfig] = None)
    (implicit ec: ExecutionContext, mat: Materializer): HttpACLClient = {

    val aclCache = cacheConfigOpt.flatMap { cacheConfig =>
      if (cacheConfig.enabled) {
        cacheConfig.config match {
          case Some(dynamoDbConfiguration) => Some(new DynamoDbUserAclRepository(dynamoDbConfiguration))
          case None => throw new Exception("The cache is enabled for the userACL http client, but the cache config is missing!")
        }
      } else {
        None
      }
    }
    new HttpACLClient(client, baseUrl, aclCache)
  }

  def apply(config: AclClientConfig)
    (implicit ec: ExecutionContext, mat: Materializer, as: ActorSystem): HttpACLClient = {
    val client = new FutureHttpClient(Http(), config.httpClient)
    apply(client, config.baseUrl, config.cacheConfig)
  }

}
