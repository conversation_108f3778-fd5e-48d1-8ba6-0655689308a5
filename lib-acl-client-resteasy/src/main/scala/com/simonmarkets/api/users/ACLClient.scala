package com.simonmarkets.api.users

import akka.http.scaladsl.model.HttpHeader
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.api.users.ACLClient.UserIdSSNResponse
import com.simonmarkets.api.users.compatibility.UserACLCompatible
import com.simonmarkets.logging.TraceId
import com.simonmarkets.users.common.{AttributeType, IdType}
import io.circe.Decoder

import scala.concurrent.Future
import scala.language.higherKinds

/**
 * @tparam F can be implemented as Operation or as Future too.
 */
trait ACLClient[F[_]] {

  /**
   * To be used as warmup to establish connection to client
   * @param traceId trace
   * @return Unit
   */
  def ping(implicit traceId: TraceId): F[Unit]

  /** Retrieves the given user information from holdings service perspective
   *
   * @param userId  - id of the user to retrieve
   * @param traceId - trace id
   * @return - the user with a given id
   */
  def getUserACL(userId: String)(implicit traceId: TraceId): F[UserACL]

  /** Retrieves multiple users information in a batch
   *
   * @param userIds - list of ids of the users to retrieve
   * @param traceId - trace id
   * @return - list of users with given ids
   */
  def getUserACLs(userIds: Set[String])(implicit traceId: TraceId): F[List[UserACL]]

  /**
   * Similar to `getUserACL` but allows to restrict which fields will be fetched.<br>
   * You can create your own case class that contains only part of the `UserACL` fields.
   * This method will fetch only required fields for such class.<br>
   * NOTE: Due to some implementations issues this method cannot use repository cache and takes data directly from the server
   *
   * @param userId  id of the user to retrieve
   * @param traceId traceId
   * @tparam T target class to decode
   * @return the user with a given id
   */
  def getSparseUserACL[T: UserACLCompatible : Decoder](userId: String)(implicit traceId: TraceId): F[T]

  /**
   * Similar to `getUserACLs` but allows to restrict which fields will be fetched.<br>
   * You can create your own case class that contains only part of the `UserACL` fields.
   * This method will fetch only required fields for such class.<br>
   * NOTE: Due to some implementations issues this method cannot use repository cache and takes data directly from the server
   * @param userIds id of the user to retrieve
   * @param traceId traceId
   * @tparam T target class to decode
   * @return list of users with given ids
   */
  def getSparseUserACLsByIdType[T: UserACLCompatible : Decoder](userIds: Set[String], from: Option[String],
      principalIdType: Option[IdType], networkId: Option[String], action: Option[String] = None, subject: Option[String] = None)
    (implicit traceId: TraceId): F[List[T]]

  def getSparseUserACLsByIdTypeWithView[T: UserACLCompatible : Decoder](userIds: Set[String], from: Option[String],
      principalIdType: Option[IdType], networkId: Option[String], subject: Option[String] = None)
    (implicit traceId: TraceId): F[List[T]]

  def getSparseUserACLsByIdTypeWithImpersonate[T: UserACLCompatible : Decoder](userIds: Set[String], from: Option[String],
      principalIdType: Option[IdType], networkId: Option[String], subject: Option[String] = None)
    (implicit traceId: TraceId): F[List[T]]

  def getUserACLByEmail(email: String)(implicit traceId: TraceId): F[UserACL]

  def getUserACLByNPN(npn: String)(implicit traceId: TraceId): F[UserACL]

  def getUserByOmsAlias(oms: String)(implicit traceId: TraceId): F[UserACL]

  def getUserByDistributorAlias(distributorAlias: String, networkId: String)(implicit traceId: TraceId): F[UserACL]

  def getUsersForAction(ids: List[String], from: Option[String], principalIdType: Option[IdType],
      networkId: Option[String] = None, action: Option[String], batchSize: Int = 200, parallelism: Int = 4)
    (implicit traceId: TraceId): F[Seq[UserACL]]

  def getUsersWithView(ids: List[String], from: Option[String], principalIdType: Option[IdType],
      networkId: Option[String] = None, batchSize: Int = 200, parallelism: Int = 4)
    (implicit traceId: TraceId): F[Seq[UserACL]]

  def getUsersWithImpersonation(ids: List[String], from: Option[String], principalIdType: Option[IdType],
      networkId: Option[String] = None, batchSize: Int = 200, parallelism: Int = 4)
    (implicit traceId: TraceId): F[Seq[UserACL]]

  def getUserIdSSNMap(ssns: List[String])(implicit traceId: TraceId): F[List[UserIdSSNResponse]]

  /** Retrieves given user on behalf of authenticated user
   *
   * @param impersonatedUserId - id of the user to retrieve
   * @param authenticatedUserId - id of the user on behalf of which user to be retrieved
   * @return - UserACL
   * */
  def getImpersonatedUserACL(impersonatedUserId: String, authenticatedUserId: String)(implicit traceId: TraceId): F[UserACL]

  def getSparseUserACLsByIdType[T: UserACLCompatible : Decoder](userIds: Set[String], from: Option[String],
      principalIdType: Option[IdType], networkId: Option[String], action: Option[String],
      httpHeaders: List[HttpHeader], subject: Option[String], attributeType: Option[AttributeType],
      attributeValues: Set[String])(implicit traceId: TraceId): Future[List[T]]

  def getSparseUserACLByIdType[T: UserACLCompatible : Decoder](userId: String,
      principalIdType: Option[IdType], networkId: Option[String])(implicit traceId: TraceId): Future[T]

}

object ACLClient {
  case class UserIdSSNResponse(userId: String, ssn: String)
}
