# ACL Client can be setup to use the User ACL cache

## Setup

You will need these terraform changes for your Project:
 ```
 statement {
   effect = "Allow"
   actions = [
     "dynamodb:Batch*",
     "dynamodb:Get*",
     "dynamodb:Query",
     "dynamodb:Scan"
   ]
   resources = [
     "arn:aws:dynamodb:us-east-1:586465018333:table/user-acl-cache"
   ]
 }
 ```

1. Add the [AclClientConfig](https://gitlab.simon.io/services/networks/-/blob/master/lib-acl-client/src/main/scala/com/simonmarkets/api/users/config/AclClientConfig.scala) to your config and enable the cache
2. Initiate the AclHttpClient with the AclClientConfig

Example: https://gitlab.simon.io/services/notifications/-/merge_requests/53/diffs


## Workflow

The useracls are synced to a dynamodb table without expiration. Each time a network or user is created or updated, the acl updates as well.
When the cache is enabled in the HttpAclClient, the client's <i>getUserAcl</i> and <i>getUserAcls</i> methods will get the acl from the dynamodb.
For the other methods or when the dynamodb query fails, the client will query the acl from the principals endpoint.


You can check the cache stats in [Splunk](`https://splunkue1.mgmt.simon.io:8000/en-US/app/search/search?q=search%20index%3Dprod-*%20(%22Get%20UserACL%20totalMillis%3D*%22%20OR%20%22Returning%20userACL%20from%20cache%2C%20totalMillis%3D*%22)%20%7C%20eval%20Cache%3DfromCache%20%7C%20rex%20field%3DtotalMillis%20%22%5C%27(%3FP%3Cms%3E.%2B)%5C%27%22%20%7C%20eval%20spike%3Dif%20((ms%20%3E%202000)%2C%201%2C0)%20%7C%20stats%20count(ms)%20as%20%22Count%22%20sum(spike)%20as%20%22Spike%20Count%20(ms%3E2000)%22%20median(ms)%20as%20%22Median%22%20avg(ms)%20as%20%22Avg%20ms%22%20max(ms)%20as%20%22Max%22%20min(ms)%20as%20%22Min%22%20by%20Cache%2C%20sourcetype&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-3d%40d&latest=now&display.general.type=statistics&display.page.search.tab=statistics&sid=1594371213.1109193_ACEB1ACD-889F-4E8B-96EF-728E17C83CEF`)