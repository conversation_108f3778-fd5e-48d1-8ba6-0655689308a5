package com.simonmarkets.shared.ssnhash

import akka.actor.ActorSystem
import akka.http.scaladsl.Http
import akka.stream.Materializer
import com.simonmarkets.http.{FutureHttpClient, HttpClientConfig}
import com.simonmarkets.logging.TraceId
import com.simonmarkets.resteasy.authn.config.SourceConfig
import com.typesafe.config.Config
import org.joda.time.DateTime
import pureconfig.{CamelCase, ConfigFieldMapping}
import pureconfig.generic.{FieldCoproductHint, ProductHint}
import pureconfig.generic.auto._
import io.circe.generic.auto._
import io.circe.Decoder

import scala.concurrent.{ExecutionContext, Future}

case class UserIdSSNHashMap(
    userId: String,
    ssnHash: String,
    userCreated: String,
    userLastUpdated: String,
    timeUpdated: DateTime,
    timeCreated: DateTime
)

case class SSNHashRequest(userToHash: String, ssn: String)

case class SSNHashClientConfig(
    httpClient: HttpClientConfig,
    apiPrefix: String
)

object SSNHashClientConfig {
  implicit def hint[T]: ProductHint[T] = ProductHint[T](ConfigFieldMapping(CamelCase, CamelCase))
  implicit val sourceConfigHint: FieldCoproductHint[SourceConfig] = new FieldCoproductHint[SourceConfig](key = "type")

  def apply(config: Config): SSNHashClientConfig = pureconfig.loadConfigOrThrow[SSNHashClientConfig](config)

}

trait SSNHashClient {
  def create(userToHash: String, ssn: String)(implicit traceId: TraceId): Future[UserIdSSNHashMap]
}

object HttpSSNHashClient {
  def apply(config: SSNHashClientConfig)(implicit ec: ExecutionContext, mat: Materializer, as: ActorSystem): HttpSSNHashClient = {
    val client = new FutureHttpClient(Http(), config.httpClient)
    new HttpSSNHashClient(client, config.apiPrefix)
  }
}

class HttpSSNHashClient(client: FutureHttpClient, path: String)(implicit ec: ExecutionContext, mat: Materializer) extends SSNHashClient {
  implicit def dateTimeDecoder[A <: DateTime]: Decoder[DateTime] =
    Decoder.decodeString.map(DateTime.parse)

  override def create(userToHash: String, ssn: String)(implicit traceId: TraceId): Future[UserIdSSNHashMap] = {
    val url = s"$path/v1/user-ssn-maps"
    client.post[SSNHashRequest, UserIdSSNHashMap](url, SSNHashRequest(userToHash, ssn))
  }
}
