# Users and Networks service

## Package Structure
- `lib-xxx-common`: domain classes to be used only with this repo
- `om-xxx`: should have all dependencies an external service might need. should NOT have dependencies on any other repository classes
- `lib-xxx-client`: should be the only direct dependency taken by external service. should NOT pick up any transitive depencies except om-xxx
- `service-xxx`: service should use its own domain and the lib-xxx-common

## How to run it locally

> **Note: This is not necessary for making entitlements changes;**
> For those, build and test within the simon-entitlements sub-project.

Main class: `com.simonmarkets.networks.NetworkServiceApp`
VM Options: point to /dev/dev.conf `-Dconfig.file=D:\Users\apilin\Projects\networks\service-user-network\dev\dev.conf`
Env variables: `AWS_PROFILE=alpha`
 
1. Refresh/obtain your token from alpha (use this utility: https://gitlab.simon.io/engineering/data-strategy/credentials)
2. Make sure that you have access to Kinesis, DynamoDb, and CloudWatch in alpha environment
3. Run local mongodb restored from alpha dump (find instructions for local mongo setup in `apps` repository wiki)
4. Change `applicationName` in `kinesis` section in `dev/dev.conf` - append your okta name to it (so you will send 
events into your personal kinesis stream)
5. Go to `Intellij -> Settings -> Build, Execution, Deployment -> Compiler -> Scala Compile Server` and make sure that `JVM Option` has `-Xss12m` 
6. Run application

## Issues 

```
Failed to resolve configuration value: resolver="AwsSecretResolver", value="sm:Okta-secret"
The security token included in the request is expired (Service: AWSSecretsManager; Status Code: 400; Error Code: ExpiredTokenException;
```
* This issue can be resolved by rerunning ./open_sesame.sh <ALPHA/QA>
