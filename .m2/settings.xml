<?xml version="1.0" encoding="UTF-8"?>
<settings xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.1.0 http://maven.apache.org/xsd/settings-1.1.0.xsd" xmlns="http://maven.apache.org/SETTINGS/1.1.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <profiles>
    <profile>
      <repositories>
        <repository>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
          <id>central</id>
          <name>maven-proxy</name>
          <url>https://artifactory.simon.io:443/artifactory/maven-proxy</url>
        </repository>
        <repository>
          <snapshots />
          <id>snapshots</id>
          <name>maven-proxy</name>
          <url>https://artifactory.simon.io:443/artifactory/maven-proxy</url>
        </repository>
      </repositories>
      <pluginRepositories>
        <pluginRepository>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
          <id>central</id>
          <name>maven-proxy</name>
          <url>https://artifactory.simon.io:443/artifactory/maven-proxy</url>
        </pluginRepository>
        <pluginRepository>
          <snapshots />
          <id>snapshots</id>
          <name>maven-proxy</name>
          <url>https://artifactory.simon.io:443/artifactory/maven-proxy</url>
        </pluginRepository>
      </pluginRepositories>
      <id>artifactory</id>
    </profile>
  </profiles>
  <activeProfiles>
    <activeProfile>artifactory</activeProfile>
  </activeProfiles>
</settings>
