# Lambda for processing Network Hierarchy via SFTP
It's a maven module for storing aws lambdas

```
config/
    -alpha.yml #specific config per each environment 
    -qa.yml 
    -prod.yml 
src/main/scala/
    -lambda func1
    -lambda func2
serverless.yml #common part for all environments' parameteres
```
##Required environment variables, which should be stored in AWS SecretsManager:

|Variable|Description|
|---|---|
|NETWORK_LOCATION_UPLOAD_URL|Simon endpoint for uploading network hierarchy|
|CLIENT_ID|Okta auth clientId|  
|CLIENT_SECRET|Okta auth secret|  

##Environments
Each environment should be defined in `./config` folder.

##Build and deploy    
- Build an artifact `mvn clean package`

- Deploy into AWS (alpha env) `sls deploy -s alpha`

##Testing
*Requirements:*
1. AWS CLI https://aws.amazon.com/cli/
1. Credentials script for AWS access https://gitlab.simon.io/engineering/data-strategy/credentials

*Steps:*
1. Specify AWS credentials ex: `./open_sesame.ps1 alpha`
1. Send a file to S3 with a correct metadata property (networkId)
 `aws s3 cp orh_hierarchy.csv s3://serverless-org-upload-alpha  --profile alpha`
1. Check result in `AWS CloudWatch` logs

*NOTE:* delete file from S3 
`aws s3 rm s3://serverless-org-upload-alpha/orh_hierarchy.csv --profile alpha`