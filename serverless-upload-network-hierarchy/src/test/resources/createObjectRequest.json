{"Records": [{"awsRegion": "us-east-1", "eventName": "ObjectCreated:Put", "eventSource": "aws:s3", "eventTime": "2019-10-25T13:52:54.678Z", "eventVersion": "2.1", "requestParameters": {"sourceIPAddress": "**********"}, "responseElements": {"x-amz-id-2": "JIIf8o99yTOHvliM7+IK+QNkKlOsvlOETXWkZxcZX92S0u2SFlTes93N5rg8t2d0H/aXLSP+bvg=", "x-amz-request-id": "50FDC3E310A5A5DB"}, "s3": {"configurationId": "network-locations-upload-alpha-holdingsUpload-68a737133cefb25bff959852b8f04754", "bucket": {"name": "bucketName", "ownerIdentity": {"principalId": "A2ROWM4G17U4DY"}, "arn": "arn:aws:s3:::serverless-org-upload-alpha"}, "object": {"key": "test.csv", "size": 81, "eTag": "3851f647506506b69e2c739930941c16", "versionId": "", "sequencer": "005DB2FE368013366E"}, "s3SchemaVersion": "1.0"}, "userIdentity": {"principalId": "AWS:AROAVDBKOZPMKNXUUZVTM:anatolii.zhukov"}}]}