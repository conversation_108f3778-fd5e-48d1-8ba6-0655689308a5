config {
  resolvers {
    sm {
      region = "us-east-1"
    }
    file {
      root = "app-config/dev"
    }
  }
}

network-id = ${NETWORK_ID}
api-prefix = "https://origin-dc1.api.simonmarkets.com/simon/api"

http-client-config {
  auth {
    type: OAuth
    client-id: "0oa2h4re4ym0Fh3s82p7"
    client-secret-file: ""
    client-secret: "sm:applicationconfig-oktaclientsecret"
    site = "sm:applicationconfig-issuer-uri"
    authorization-path: ""
    token-path = "/v1/token"
    scope: "read_product_data"
    token-type = {
      type = BearerToken
    }
    proxy {
      port: 3128
      address: "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
    }
  }
  proxy {
    port: 3128
    address: "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
  }
}
