config {
  resolvers {
    sm {
      region = "us-east-1"
    }
    file {
      root = "app-config/dev"
    }
  }
}

network-id = ${NETWORK_ID}
api-prefix = "https://origin-dc1.api.qa.simonmarkets.com/simon/api"

http-client-config {
  auth {
    type: OAuth
    client-id: "0oaevl54gplnFvyx70h7"
    client-secret-file: ""
    client-secret: "sm:applicationconfig-oktaclientsecret"
    site = "sm:applicationconfig-issuer-uri"
    authorization-path: ""
    token-path = "/v1/token"
    scope: "read_product_data"
    token-type = {
      type = BearerToken
    }
    proxy: {
      address: "internal-qa-squid-elb-1135562419.us-east-1.elb.amazonaws.com"
      port: 3128
    }
  }
  proxy: {
    address: "internal-qa-squid-elb-1135562419.us-east-1.elb.amazonaws.com"
    port: 3128
  }
}
