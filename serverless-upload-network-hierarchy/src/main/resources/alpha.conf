config {
  resolvers {
    sm {
      region = "us-east-1"
    }
    file {
      root = "app-config/dev"
    }
  }
}

network-id = ${NETWORK_ID}
api-prefix = "https://origin-a.dev.simonmarkets.com/simon/api"

http-client-config {
  auth {
    type = OAuth
    client-id = "0oaevl54gplnFvyx70h7"
    client-secret-file = ""
    client-secret = "sm:Okta-secret"
    site = "sm:applicationconfig-issuer-uri"
    authorization-path = ""
    token-path = "/v1/token"
    scope = "read_product_data"
    token-type = {
      type = Cookie
      name = SimSSO
    }
  }
  proxy {
    port = 3128
    address = "internal-alpha-squid-proxy-elb-1264059358.us-east-1.elb.amazonaws.com"
  }
}
