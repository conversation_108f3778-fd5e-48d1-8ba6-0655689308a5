service: network-locations-upload

custom:
  localstack:
    stages:
      - dev-local
    host: http://localstack-svc.${env:OKTETO_NAMESPACE}
    edgePort: 4566
    debug: true
  file: ${file(./config/okteto.yml)}

provider:
  stage: ${self:custom.file.provider.environment}
  name: aws
  region: us-east-1
  profile: ${self:custom.file.provider.profile}
  runtime: java11
  versionFunctions: false
  timeout: 300
  deploymentBucket:
    name: simon-dev-local-serverless
  environment:
    JAVA_TOOL_OPTIONS: -Dconfig.file=okteto.conf

plugins:
  - serverless-localstack
  - serverless-deployment-bucket


package:
  artifact: target/serverless-upload-network-hierarchy-uber.jar

functions: ${self:custom.file.functions}
