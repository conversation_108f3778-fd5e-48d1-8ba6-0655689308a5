provider:
  profile: default
  environment: dev-local
  account: "************"

functions:
  - networkHierarchyUpload:
      handler: com.simonmarkets.S3EventHandler
      events:
        - s3:
            bucket: serverless-org-upload-okteto
            event: s3:ObjectCreated:*
            existing: true
      environment:
        NETWORK_ID: Test%20Network%203
  - networkHierarchyUploadTN1: ##Used for automation testing
      handler: com.simonmarkets.S3EventHandler
      events:
        - s3:
            bucket: automation-test-bucket-okteto
            event: s3:ObjectCreated:*
            rules:
              - prefix: network-location-upload-tn1/
              - suffix: .csv
            existing: true
      environment:
        NETWORK_ID: Test%20Network%201

newRelic:
  javaNewRelicHandler: handleRequest
  exclude:
    - networkHierarchyUpload
    - networkHierarchyUploadTN1
