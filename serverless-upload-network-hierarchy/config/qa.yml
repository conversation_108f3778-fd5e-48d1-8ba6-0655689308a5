proxySystemProperty: "-Dhttps.proxyHost=internal-qa-squid-elb-**********.us-east-1.elb.amazonaws.com -Dhttps.proxyPort=3128"

provider:
  profile: qa
  securityGroupIds:
    - "sg-3549b641"
  subnetIds:
    - "subnet-e61597c9"
    - "subnet-8d9016d0"
    - "subnet-e240a3ed"
  iam:
    role: arn:aws:iam::************:role/qa-user-networks-lambda-iam-role

# Don't edit this, it's provided by DevOps team
logRetentionInDays: 90
logSubscription:
  - enabled: true
    destinationArn: "arn:aws:firehose:us-east-1:************:deliverystream/cwl-splunk-firehose-stream-qa"
    roleArn: "arn:aws:iam::************:role/cwl-cloudwatch_logs_role"

functions:
  - networkHierarchyUploadRBC:
      handler: com.simonmarkets.S3EventHandler
      events:
        - s3:
            bucket: client-rbc-org-qa
            event: s3:ObjectCreated:*
            existing: true
      environment:
        NETWORK_ID: RBC

  - networkHierarchyUploadRBCCAS:
      handler: com.simonmarkets.S3EventHandler
      events:
        - s3:
            bucket: client-rbc-cas-org-qa
            event: s3:ObjectCreated:*
            existing: true
      environment:
        NETWORK_ID: adf55e67-5906-405f-b416-d2aacee019b3

  - networkHierarchyUploadTN1: ##Used for automation testing
      handler: com.simonmarkets.S3EventHandler
      events:
        - s3:
            bucket: automation-test-bucket-qa
            event: s3:ObjectCreated:*
            rules:
              - prefix: network-location-upload-tn1/
              - suffix: .csv
            existing: true
      environment:
        NETWORK_ID: Test%20Network%201

newRelic:
  javaNewRelicHandler: handleRequest
  exclude:
    - networkHierarchyUploadRBC
    - networkHierarchyUploadRBCCAS
    - networkHierarchyUploadTN1
