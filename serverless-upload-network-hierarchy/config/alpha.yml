proxySystemProperty: "-Dhttps.proxyHost=internal-alpha-squid-proxy-elb-**********.us-east-1.elb.amazonaws.com -Dhttps.proxyPort=3128"

# Don't edit this, it's provided by DevOps team
logRetentionInDays: 60
logSubscription:
  - enabled: true
    destinationArn: "arn:aws:firehose:us-east-1:************:deliverystream/cwl-splunk-firehose-stream"
    roleArn: "arn:aws:iam::************:role/cwl-cloudwatch_logs_role"

provider:
  profile: alpha
  securityGroupIds:
    - "sg-04a9f78088f2c60b3"
  subnetIds:
    - "subnet-092aa15246e226be2"
    - "subnet-0978a24297da4c96e"
    - "subnet-0c85b6c43be91a809"
  iam:
    role: arn:aws:iam::************:role/user-networks-service-alpha-role

functions:
  - networkHierarchyUpload:
      handler: com.simonmarkets.S3EventHandler
      events:
        - s3:
            bucket: serverless-org-upload-alpha
            event: s3:ObjectCreated:*
            existing: true
      environment:
        NETWORK_ID: Test%20Network%203
  - networkHierarchyUploadTN1: ##Used for automation testing
      handler: com.simonmarkets.S3EventHandler
      events:
        - s3:
            bucket: automation-test-bucket-alpha
            event: s3:ObjectCreated:*
            rules:
              - prefix: network-location-upload-tn1/
              - suffix: .csv
            existing: true
      environment:
        NETWORK_ID: Test%20Network%201


newRelic:
  javaNewRelicHandler: handleRequest
  exclude:
    - networkHierarchyUpload
    - networkHierarchyUploadTN1
