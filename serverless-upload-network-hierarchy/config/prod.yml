proxySystemProperty: "-Dhttps.proxyHost=internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com -Dhttps.proxyPort=3128"

provider:
  profile: prod
  securityGroupIds:
    - "sg-a56b1fd3"
  subnetIds:
    - "subnet-61ba8d4e"
    - "subnet-48665215"
    - "subnet-4066c14f"
  iam:
    role: arn:aws:iam::090020729433:role/prod-user-networks-lambda-iam-role

# Don't edit this, it's provided by DevOps team
logRetentionInDays: 365
logSubscription:
  - enabled: true
    destinationArn: "arn:aws:firehose:us-east-1:090020729433:deliverystream/cwl-splunk-firehose-stream-prd"
    roleArn: "arn:aws:iam::090020729433:role/cwl-cloudwatch_logs_role"

functions:
  - networkHierarchyUploadRBC:
      handler: com.simonmarkets.S3EventHandler
      events:
        - s3:
            bucket: client-rbc-org-prd
            event: s3:ObjectCreated:*
            existing: true
      environment:
        NETWORK_ID: RBC%20Capital

  - networkHierarchyUploadRBCCAS:
      handler: com.simonmarkets.S3EventHandler
      events:
        - s3:
            bucket: client-rbc-cas-org-prd
            event: s3:ObjectCreated:*
            existing: true
      environment:
        NETWORK_ID: 80e6720f-2b00-42e9-9d07-0f030e71807e

newRelic:
  javaNewRelicHandler: handleRequest
  exclude:
    - networkHierarchyUploadRBC
    - networkHierarchyUploadRBCCAS