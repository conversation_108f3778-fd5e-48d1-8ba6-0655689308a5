service: network-locations-upload

custom: ${file(./config/${self:provider.stage}.yml)}

provider:
  stage: ${opt:stage, 'sandbox'}
  name: aws
  region: us-east-1
  profile: ${self:custom.provider.profile}
  runtime: java11
  versionFunctions: false
  timeout: 300
  deploymentBucket:
    name: simon-${self:provider.stage}-serverless
  vpc:
    securityGroupIds: ${self:custom.provider.securityGroupIds}
    subnetIds: ${self:custom.provider.subnetIds}
  iam:
    role: ${self:custom.provider.iam.role}
  environment:
    JAVA_TOOL_OPTIONS: -Dconfig.file=${opt:stage}.conf ${self:custom.proxySystemProperty, ''}
  logRetentionInDays: ${self:custom.logRetentionInDays}

plugins:
  - serverless-plugin-log-subscription

package:
  artifact: target/serverless-upload-network-hierarchy-uber.jar

functions: ${self:custom.functions}
