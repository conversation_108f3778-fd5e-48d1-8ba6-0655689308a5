<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.simonmarkets</groupId>
        <artifactId>users-networks</artifactId>
        <version>sandbox-SNAPSHOT</version>
    </parent>

    <artifactId>serverless-upload-network-hierarchy</artifactId>
    <version>sandbox-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-logging-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-lambda-java-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-secretsmanager</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-scala_${scala.version.short}</artifactId>
        </dependency>

        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-http-client</artifactId>
            <version>${simon.utils.version}</version>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-networks-common</artifactId>
            <version>sandbox-SNAPSHOT</version>
        </dependency>

        <!--        Test      -->
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_${scala.version.short}</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
                <configuration>
                    <args>
                        <arg>-deprecation</arg>
                        <arg>-feature</arg>
                        <arg>-language:experimental.macros</arg>
                        <arg>-language:higherKinds</arg>
                        <arg>-unchecked</arg>
                        <arg>-Xfatal-warnings</arg>
                        <arg>-Xlint:adapted-args</arg>
                        <arg>-Xlint:by-name-right-associative</arg>
                        <arg>-Xlint:constant</arg>
                        <arg>-Xlint:delayedinit-select</arg>
                        <arg>-Xlint:doc-detached</arg>
                        <arg>-Xlint:inaccessible</arg>
                        <arg>-Xlint:infer-any</arg>
                        <arg>-Xlint:missing-interpolator</arg>
                        <arg>-Xlint:nullary-override</arg>
                        <arg>-Xlint:nullary-unit</arg>
                        <arg>-Xlint:option-implicit</arg>
                        <arg>-Xlint:package-object-classes</arg>
                        <arg>-Xlint:poly-implicit-overload</arg>
                        <arg>-Xlint:private-shadow</arg>
                        <arg>-Xlint:stars-align</arg>
                        <arg>-Xlint:type-parameter-shadow</arg>
                        <arg>-Xlint:unsound-match</arg>
                        <arg>-Yno-adapted-args</arg>
                        <arg>-Ypartial-unification</arg>
                        <arg>-Ywarn-dead-code</arg>
                        <arg>-Ywarn-extra-implicit</arg>
                        <arg>-Ywarn-inaccessible</arg>
                        <arg>-Ywarn-infer-any</arg>
                        <arg>-Ywarn-nullary-override</arg>
                        <arg>-Ywarn-nullary-unit</arg>
                        <arg>-Ywarn-macros:after</arg>
                        <arg>-Ywarn-unused:implicits</arg>
                        <arg>-Ywarn-unused:imports</arg>
                        <arg>-Ywarn-unused:locals</arg>
                        <arg>-Ywarn-unused:params</arg>
                        <arg>-Ywarn-unused:patvars</arg>
                        <arg>-Ywarn-unused:privates</arg>
                    </args>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>${maven-shade-plugin.version}</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <minimizeJar>true</minimizeJar>
                            <shadedArtifactAttached>true</shadedArtifactAttached>
                            <shadedClassifierName>uber</shadedClassifierName>
                            <filters>
                                <filter>
                                    <artifact>org.scala-lang:scala-library</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>com.amazonaws:aws-java-sdk-secretsmanager</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>com.amazonaws:aws-java-sdk-core</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>com.amazonaws:aws-lambda-java-core</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>com.amazonaws:aws-java-sdk-s3</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>com.typesafe.akka:akka-actor_${scala.version.short}</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>com.typesafe.akka:akka-stream_${scala.version.short}</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>ch.qos.logback:*</artifact>
                                    <includes>**</includes>
                                </filter>
                                <filter>
                                    <artifact>ch.qos.logback.contrib:*</artifact>
                                    <includes>**</includes>
                                </filter>
                                <filter>
                                    <artifact>com.simonmarkets:lib-logging-core</artifact>
                                    <includes>**</includes>
                                </filter>
                                <filter>
                                    <artifact>com.simonmarkets.logging:*</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>commons-logging:commons-logging</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <includes>
                                        <artifact>org.apache.httpcomponents:httpclient</artifact>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>com.fasterxml.jackson.core:*</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                            </filters>
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                    <resource>reference.conf</resource>
                                </transformer>
                            </transformers>
                            <artifactSet>
                                <includes>
                                    <include>*:*</include>
                                </includes>
                            </artifactSet>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.coderplus.maven.plugins</groupId>
                <artifactId>copy-rename-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>simon-info.properties</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <excludes>
                    <exclude>simon-info.properties</exclude>
                </excludes>
            </resource>
        </resources>
    </build>
</project>
