package com.simonmarkets.sales.fee.rules.engine.routes

import akka.http.scaladsl.model.{ContentTypes, HttpEntity, StatusCodes}
import akka.http.scaladsl.server.Directives.tprovide
import akka.http.scaladsl.testkit.ScalatestRouteTest
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.SalesFeeCapabilities.{OverrideFeeFaCommission, ViewFeeFaCommission}
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.repository.MongoNetworkRepository
import com.simonmarkets.sales.fee.rules.common.{FeeSchedule, FeeType}
import com.simonmarkets.sales.fee.rules.common.repository.SalesFeeRuleRepository
import com.simonmarkets.sales.fee.rules.engine.models.{Calculation, GridRequest, GridResponse}
import com.simonmarkets.sales.fee.rules.engine.services.SalesFeeRulesEngineService
import com.simonmarkets.useracl.UserAclAuthorizedDirective
import org.mockito.Mockito.when
import org.mockito.ArgumentMatchers.any
import org.scalatest.{BeforeAndAfter, FunSuite, Matchers}
import org.scalatest.junit.JUnitRunner
import org.junit.runner.RunWith
import org.scalatest.mockito.MockitoSugar
import simon.Id.NetworkId

import scala.concurrent.Future

@RunWith(classOf[JUnitRunner])
class RulesEngineServiceRouteSpec extends FunSuite with MockitoSugar with Matchers with BeforeAndAfter with ScalatestRouteTest {
  implicit val traceId: TraceId = TraceId.randomize

  private val userACLDirective = mock[UserAclAuthorizedDirective]

  private val userAcl: UserACL = new UserACL(
    userId = "testUser",
    networkId = NetworkId("Test Network"),
    lastVisitedAt = None,
    email = "<EMAIL>",
    firstName = "John",
    lastName = "Doe",
    distributorId = None,
    isActive = None,
    omsId = None,
    tradewebEligible = false,
    regSEligible = true,
    capabilities = Set(ViewFeeFaCommission, OverrideFeeFaCommission)
  )

  val networkRepository: MongoNetworkRepository = mock[MongoNetworkRepository]
  val rulesRepository: SalesFeeRuleRepository = mock[SalesFeeRuleRepository]

  val adminUserAcl: UserACL = userAcl.copy(capabilities = userAcl.capabilities.union(Set(Admin)))
  when(userACLDirective.authorized()).thenReturn(tprovide((traceId, adminUserAcl)))

  val body =
    """
      |{"networkId" : "Test Raymond James",
      |"contract" : {
      |        "hasAbsoluteReturnJump": "No",
      |        "displayName": "Callable Market Linked Income Note",
      |        "endStub": "Short",
      |        "rangedParams": {
      |            "estimatedInitialValue": {
      |                "low": 0.93,
      |                "high": 0.98
      |            }
      |        },
      |        "couponUnderlierReturnType": "Simple Return",
      |        "rollDate": "2021-08-31",
      |        "maturityDate": "2024-08-30",
      |        "couponSchedule": [
      |            {
      |                "annualizedCoupon": 0.1765,
      |                "couponBarrierObservationType": "European",
      |                "coupon": 0.044125,
      |                "couponPaymentDate": "2021-11-30",
      |                "couponBarrier": 0.5,
      |                "couponBarrierTouchBreak": "Touch",
      |                "couponDeterminationDate": "2021-11-26",
      |                "memory": "No"
      |            },
      |            {
      |                "annualizedCoupon": 0.1765,
      |                "couponBarrierObservationType": "European",
      |                "coupon": 0.044125,
      |                "couponPaymentDate": "2022-02-28",
      |                "couponBarrier": 0.5,
      |                "couponBarrierTouchBreak": "Touch",
      |                "couponDeterminationDate": "2022-02-24",
      |                "memory": "No"
      |            },
      |            {
      |                "annualizedCoupon": 0.1765,
      |                "couponBarrierObservationType": "European",
      |                "coupon": 0.044125,
      |                "couponPaymentDate": "2022-05-31",
      |                "couponBarrier": 0.5,
      |                "couponBarrierTouchBreak": "Touch",
      |                "couponDeterminationDate": "2022-05-26",
      |                "memory": "No"
      |            },
      |            {
      |                "annualizedCoupon": 0.1765,
      |                "couponBarrierObservationType": "European",
      |                "coupon": 0.044125,
      |                "couponPaymentDate": "2022-08-31",
      |                "couponBarrier": 0.5,
      |                "couponBarrierTouchBreak": "Touch",
      |                "couponDeterminationDate": "2022-08-29",
      |                "memory": "No"
      |            },
      |            {
      |                "annualizedCoupon": 0.1765,
      |                "couponBarrierObservationType": "European",
      |                "coupon": 0.044125,
      |                "couponPaymentDate": "2022-11-30",
      |                "couponBarrier": 0.5,
      |                "couponBarrierTouchBreak": "Touch",
      |                "couponDeterminationDate": "2022-11-28",
      |                "memory": "No"
      |            },
      |            {
      |                "annualizedCoupon": 0.1765,
      |                "couponBarrierObservationType": "European",
      |                "coupon": 0.044125,
      |                "couponPaymentDate": "2023-02-28",
      |                "couponBarrier": 0.5,
      |                "couponBarrierTouchBreak": "Touch",
      |                "couponDeterminationDate": "2023-02-24",
      |                "memory": "No"
      |            },
      |            {
      |                "annualizedCoupon": 0.1765,
      |                "couponBarrierObservationType": "European",
      |                "coupon": 0.044125,
      |                "couponPaymentDate": "2023-05-31",
      |                "couponBarrier": 0.5,
      |                "couponBarrierTouchBreak": "Touch",
      |                "couponDeterminationDate": "2023-05-26",
      |                "memory": "No"
      |            },
      |            {
      |                "annualizedCoupon": 0.1765,
      |                "couponBarrierObservationType": "European",
      |                "coupon": 0.044125,
      |                "couponPaymentDate": "2023-08-31",
      |                "couponBarrier": 0.5,
      |                "couponBarrierTouchBreak": "Touch",
      |                "couponDeterminationDate": "2023-08-29",
      |                "memory": "No"
      |            },
      |            {
      |                "annualizedCoupon": 0.1765,
      |                "couponBarrierObservationType": "European",
      |                "coupon": 0.044125,
      |                "couponPaymentDate": "2023-11-30",
      |                "couponBarrier": 0.5,
      |                "couponBarrierTouchBreak": "Touch",
      |                "couponDeterminationDate": "2023-11-28",
      |                "memory": "No"
      |            },
      |            {
      |                "annualizedCoupon": 0.1765,
      |                "couponBarrierObservationType": "European",
      |                "coupon": 0.044125,
      |                "couponPaymentDate": "2024-02-29",
      |                "couponBarrier": 0.5,
      |                "couponBarrierTouchBreak": "Touch",
      |                "couponDeterminationDate": "2024-02-27",
      |                "memory": "No"
      |            },
      |            {
      |                "annualizedCoupon": 0.1765,
      |                "couponBarrierObservationType": "European",
      |                "coupon": 0.044125,
      |                "couponPaymentDate": "2024-05-31",
      |                "couponBarrier": 0.5,
      |                "couponBarrierTouchBreak": "Touch",
      |                "couponDeterminationDate": "2024-05-29",
      |                "memory": "No"
      |            },
      |            {
      |                "annualizedCoupon": 0.1765,
      |                "couponBarrierObservationType": "European",
      |                "coupon": 0.044125,
      |                "couponPaymentDate": "2024-08-30",
      |                "couponBarrier": 0.5,
      |                "couponBarrierTouchBreak": "Touch",
      |                "couponDeterminationDate": "2024-08-28",
      |                "memory": "No"
      |            }
      |        ],
      |        "denominated": "USD",
      |        "callableType": "Auto Call",
      |        "protectionLevel": 0.0,
      |        "annualizedCoupon": 0.1765,
      |        "endDate": "2024-08-30",
      |        "startStub": "Short",
      |        "tradeDate": "2021-08-27",
      |        "callSchedule": [
      |            {
      |                "callable": "No",
      |                "callDeterminationDate": "2021-11-26",
      |                "callDate": "2021-11-30",
      |                "autocallLevel": 1.0,
      |                "autocallTouchBreak": "Touch"
      |            },
      |            {
      |                "callable": "Yes",
      |                "callDeterminationDate": "2022-02-24",
      |                "callDate": "2022-02-28",
      |                "autocallLevel": 1.0,
      |                "autocallTouchBreak": "Touch"
      |            },
      |            {
      |                "callable": "Yes",
      |                "callDeterminationDate": "2022-05-26",
      |                "callDate": "2022-05-31",
      |                "autocallLevel": 1.0,
      |                "autocallTouchBreak": "Touch"
      |            },
      |            {
      |                "callable": "Yes",
      |                "callDeterminationDate": "2022-08-29",
      |                "callDate": "2022-08-31",
      |                "autocallLevel": 1.0,
      |                "autocallTouchBreak": "Touch"
      |            },
      |            {
      |                "callable": "Yes",
      |                "callDeterminationDate": "2022-11-28",
      |                "callDate": "2022-11-30",
      |                "autocallLevel": 1.0,
      |                "autocallTouchBreak": "Touch"
      |            },
      |            {
      |                "callable": "Yes",
      |                "callDeterminationDate": "2023-02-24",
      |                "callDate": "2023-02-28",
      |                "autocallLevel": 1.0,
      |                "autocallTouchBreak": "Touch"
      |            },
      |            {
      |                "callable": "Yes",
      |                "callDeterminationDate": "2023-05-26",
      |                "callDate": "2023-05-31",
      |                "autocallLevel": 1.0,
      |                "autocallTouchBreak": "Touch"
      |            },
      |            {
      |                "callable": "Yes",
      |                "callDeterminationDate": "2023-08-29",
      |                "callDate": "2023-08-31",
      |                "autocallLevel": 1.0,
      |                "autocallTouchBreak": "Touch"
      |            },
      |            {
      |                "callable": "Yes",
      |                "callDeterminationDate": "2023-11-28",
      |                "callDate": "2023-11-30",
      |                "autocallLevel": 1.0,
      |                "autocallTouchBreak": "Touch"
      |            },
      |            {
      |                "callable": "Yes",
      |                "callDeterminationDate": "2024-02-27",
      |                "callDate": "2024-02-29",
      |                "autocallLevel": 1.0,
      |                "autocallTouchBreak": "Touch"
      |            },
      |            {
      |                "callable": "Yes",
      |                "callDeterminationDate": "2024-05-29",
      |                "callDate": "2024-05-31",
      |                "autocallLevel": 1.0,
      |                "autocallTouchBreak": "Touch"
      |            },
      |            {
      |                "callable": "Yes",
      |                "callDeterminationDate": "2024-08-28",
      |                "callDate": "2024-08-30",
      |                "autocallLevel": 1.0,
      |                "autocallTouchBreak": "Touch"
      |            }
      |        ],
      |        "isRegS": "No",
      |        "couponBarrierObservationType": "European",
      |        "protectionBarrierObservationType": "European",
      |        "settlementType": "Cash",
      |        "protectionType": "Barrier",
      |        "isTraceEligible": "Yes",
      |        "maturity": 43.0,
      |        "hasSingleCallEvent": "No",
      |        "isFullyProtected": "No",
      |        "callPeriod": 3.0,
      |        "issuerSymbol": "CS",
      |        "issuerKey": "CS",
      |        "sponsor": "Credit Suisse",
      |        "dayCountConvention": "1/1 ISDA",
      |        "hasUpsideParticipation": "No",
      |        "contractState": "Issued",
      |        "estimatedInitialValue": 0.93,
      |        "couponFrequency": "Quarterly",
      |        "feeOrCommission": "Commission",
      |        "issuer": "CREDIT SUISSE AG",
      |        "coupon": 0.044125,
      |        "downsideCap": 1.0,
      |        "assetClass": "StructuredInvestment",
      |        "underlier": [
      |            {
      |                "assetId": "TWLO UN",
      |                "mqId": "MAXK0VC38VGY179N",
      |                "underlierAssetType": "Single Stock"
      |            },
      |            {
      |                "assetId": "PING UN",
      |                "mqId": "MA0PC8ZBDMFDWZTN",
      |                "underlierAssetType": "Single Stock"
      |            }
      |        ],
      |        "spotReferences": [
      |            {
      |                "assetId": "PING UN",
      |                "spot": 24.9
      |            },
      |            {
      |                "assetId": "TWLO UN",
      |                "spot": 355.3
      |            }
      |        ],
      |        "cusip": "22552XU76",
      |        "contractTypeWrapper": "Note",
      |        "couponBarrier": 0.5,
      |        "settlementDelay": "2b",
      |        "spotReferenceDate": "2021-08-27",
      |        "hasCumulativeReturn": "No",
      |        "downsideParticipation": 1.0,
      |        "underlierType": "Worst Of",
      |        "notionalPerContract": 1000.0,
      |        "settlementDate": "2021-08-31",
      |        "underlierAssetClass": "Equity",
      |        "callFrequency": "Quarterly",
      |        "quantoCompo": "Domestic",
      |        "observationCalendar": "NYSE",
      |        "is3a2": "No",
      |        "autocallLevel": 1.0,
      |        "filedWithFinra": "Yes",
      |        "settlementCalendar": "New York",
      |        "securityDescription": "36M_PING,TWLO_Auto Callable Notes_22552XU76_U6129",
      |        "rollConvention": "Payment",
      |        "hasAbsoluteReturn": "No",
      |        "couponBarrierTouchBreak": "Touch",
      |        "hasUpsideJump": "No",
      |        "couponType": "Contingent",
      |        "autocallTouchBreak": "Touch",
      |        "protectionLevelStrike": 1.0,
      |        "downsideCapStrike": 0.0,
      |        "protectionBarrierTouchBreak": "Break",
      |        "isRedeemable": "No",
      |        "isCallable": "No",
      |        "sourcedFromIssuer": "No",
      |        "cleanDirtyPriced": "Percent Dirty",
      |        "businessDayConvention": "Following",
      |        "hasSinglePayment": "No",
      |        "hasCallPremium": "No",
      |        "startDate": "2021-08-31",
      |        "isCustomSchedule": "Yes",
      |        "determinationDate": "2024-08-28",
      |        "couponPaymentType": "Periodic",
      |        "totalDistributionFee": 0.0235,
      |        "protectionBarrierLevel": 0.5,
      |        "memory": "No",
      |        "issuerShortName": "Credit Suisse"
      |    }
      |}""".stripMargin
  val mockService = mock[SalesFeeRulesEngineService]
  val salesFeeRulesEngineRoutes = ServiceFeeRulesEngineRoutes(userACLDirective, mockService).routes

  val gridResponseExample = GridResponse(
    Map(FeeType.Wholesaler -> FeeSchedule(24 -> 1.0), FeeType.FACommission -> FeeSchedule(12 -> 0.0125, 18 -> 0.015, 24 -> 0.02, 30 -> 0.025), FeeType.HomeOffice -> FeeSchedule(24 -> 1.0))
  )
  test("Grid post request succeeds") {
    when(mockService.handleGridRequest(any[GridRequest])(any[TraceId])).thenReturn(Future.successful(gridResponseExample))
    Post("/simon/api/v1/sales-fee-rules-engine", HttpEntity(ContentTypes.`application/json`, body)) ~> salesFeeRulesEngineRoutes ~> check {
      status shouldBe StatusCodes.OK
    }
  }

  val calculationExample = Calculation((FeeType.Wholesaler.name -> 1.0), (FeeType.FACommission.name -> 0.015), (FeeType.HomeOffice.name -> 1.0))
  test("Calculate post request succeeds") {
    when(mockService.solveGridForTerm(any[GridRequest])(any[TraceId])).thenReturn(Future.successful(calculationExample))
    Post("/simon/api/v1/sales-fee-rules-engine/calculate", HttpEntity(ContentTypes.`application/json`, body)) ~> salesFeeRulesEngineRoutes ~> check {
      status shouldBe StatusCodes.OK
    }
  }


}
