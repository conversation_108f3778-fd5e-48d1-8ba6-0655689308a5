package com.simonmarkets.sales.fee.rules.engine.models

import com.simonmarkets.quantcommon.contractparams.codecs.{CodecConfig, ContractParamMapCirceCodec, ContractParamsSharedCirceSerializer}
import com.simonmarkets.quantcommon.contractparams.{ContractParameters, NeoContractParams}
import com.simonmarkets.quantcommon.parammap.Param
import com.simonmarkets.sales.fee.rules.common.{FeeScheduleTrait, FeeType}
import io.circe.generic.semiauto.deriveDecoder
import io.circe.syntax.EncoderOps
import io.circe.{Decoder, Encoder, HCursor, Json}
import simon.Id.NetworkId

final case class GridRequest(contract: NeoContractParams, networkId: NetworkId)

final case class GridResponse(grid: Map[FeeType, FeeScheduleTrait])

final case class Calculation(results: (String, BigDecimal)*)

object Calculation {
  implicit val calculationEncoder: Encoder[Calculation] =
    (calculation: Calculation) => {
      // Convert the results into a List of key-value pairs (tuples)
      val keyValuePairs: List[(String, Json)] = calculation.results.toList.map {
        case (feeType, value) => feeType -> value.asJson
      }
      // Use the circe library to convert the List of tuples into JSON object
      Json.obj(keyValuePairs: _*)
    }
}

object GridResponse {
  implicit val gridResponseEncoder: Encoder[GridResponse] = (gridResponse: GridResponse) =>
    Json.obj("grid" -> gridResponse.grid.asJson)
}

object GridRequest extends ContractParamsSharedCirceSerializer {

  val codecConfig: SalesFeeContractCodecConfig =
    SalesFeeContractCodecConfig(yesNoBoolean = false, externalizedContractParams = true)

  implicit val neoContractParamsCirceCodec: ContractParamMapCirceCodec[NeoContractParams] = contractParamsWithYesNo

  implicit val networkIdDecoder: Decoder[NetworkId] = (c: HCursor) => c.as[String].map(NetworkId(_))

  implicit val gridRequestDecoder = deriveDecoder[GridRequest]
}

final case class SalesFeeContractCodecConfig(
    yesNoBoolean: Boolean,
    externalizedContractParams: Boolean
) extends CodecConfig {

  override def encodeBooleanAsYesNo: Boolean = yesNoBoolean

  def externalized: Boolean = externalizedContractParams

  override def allRequiredParams: Set[Param[_]] = Set(
    ContractParameters.maturity,
    ContractParameters.isFullyProtected,
    ContractParameters.isCallable,
    ContractParameters.hasSinglePayment,
    ContractParameters.issuerKey,
    ContractParameters.contractTypeWrapper
  )
}