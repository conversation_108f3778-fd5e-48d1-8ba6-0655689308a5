package com.simonmarkets.sales.fee.rules.engine.services

import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.service.networkservice.BasicNetworkService
import com.simonmarkets.sales.fee.rules.common.repository.SalesFeeRuleRepository
import com.simonmarkets.sales.fee.rules.common.{FeeScheduleFlatExtrapolator, SalesFeeRule}
import com.simonmarkets.sales.fee.rules.engine.models.{Calculation, GridRequest, GridResponse}
import com.simonmarkets.sales.fee.rules.enginelib.{Rules, SalesFeeMatchError}
import simon.Id.NetworkId

import scala.concurrent.{ExecutionContext, Future}

class SalesFeeRulesEngineService(
    networksService: BasicNetworkService,
    rulesRepository: SalesFeeRuleRepository
)(implicit ec: ExecutionContext) extends TraceLogging {

  /**
   * Returns full feeSchedules for matched salesFeeRules based on contractParams and networkId provided.
   * Retrieves salesFeeRules from mongo based on provided networkId, and creates and matches rules.
   *
   * @param req Contains the networkId and contract params.
   * @return Containing one feeSchedule per feeType.
   */
  def handleGridRequest(req: GridRequest)(implicit traceId: TraceId): Future[GridResponse] =
    getRules(req.networkId)
      .map(salesFeeRules => Rules.evaluateBinaryRules(req.contract, salesFeeRules.toList))
      .flatMap {
        case Right(m) =>
          Future.successful(GridResponse(m))
        case Left(e: SalesFeeMatchError) =>
          log.error(e.errorMsg)
          Future.failed(e.toHttpError)
        case Left(e) =>
          log.error(e.errorMsg)
          Future.failed(HttpError.internalServerError(s"Unknown error: $e"))
      }

  /**
   * Matches salesFeeRules and also solves for the exact term based on contractParams and networkId.
   *
   * @param req Contains the networkId and contract params.
   * @return Calculation which maps feeType to fee value.
   */
  def solveGridForTerm(req: GridRequest)(implicit traceId: TraceId): Future[Calculation] =
    for {
      maturity <- Future.fromTry(
        req.contract.maturity.toRight(HttpError.badRequest("No maturity found in contract")).toTry
      )
      calculation <- handleGridRequest(req)
      calculations = calculation.grid.map {
        case (feeType, schedule) => feeType.name -> FeeScheduleFlatExtrapolator.create(schedule.data).fee(maturity)
      }.toList
    } yield Calculation(calculations: _*)

  /**
   * Helper to grab salesFeeRules from mongo collection based on networkId
   * @param networkId NetworkId (String type alias) to pull in salesFeeRules for.
   * @return Future[Iterable[SalesFeeRule]]
   */
  def getRules(networkId: NetworkId)(implicit traceId: TraceId): Future[Iterable[SalesFeeRule]] = {
    for {
      networks <- networksService.unentitledGetNetworkById(networkId) // TODO: check if need to pass entitlement (who calls here?)
      salesFeeRules <- rulesRepository.findAllById(networks.salesFeeRuleIds)
    } yield salesFeeRules
  }
}
