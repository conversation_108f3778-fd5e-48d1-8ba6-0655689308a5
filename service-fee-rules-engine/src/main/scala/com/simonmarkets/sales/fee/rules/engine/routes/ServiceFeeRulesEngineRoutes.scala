package com.simonmarkets.sales.fee.rules.engine.routes

import akka.http.scaladsl.server.Route
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.sales.fee.rules.engine.models.GridRequest
import com.simonmarkets.sales.fee.rules.engine.services.SalesFeeRulesEngineService
import com.simonmarkets.useracl.{UserAclAuthorizedDirective, UserAclAuthorizedDirectives}

final case class ServiceFeeRulesEngineRoutes(
    authorizationDirective: UserAclAuthorizedDirective,
    serviceRulesEngine: SalesFeeRulesEngineService
) extends TraceLogging with DirectivesWithCirce with UserAclAuthorizedDirectives {

  val routes: Route = pathPrefix("simon" / "api" / "v1" / "sales-fee-rules-engine")(routesInner)
  val routesInner : Route = {
    authorized { (traceId, _) =>
        implicit val _traceId: TraceId = traceId
      pathEnd {
            (post & entity(as[GridRequest])) { request =>
              complete(serviceRulesEngine.handleGridRequest(request)(_traceId))
            }
          } ~
        path("calculate") {
          (post & entity(as[GridRequest]) ) { request =>
            complete(serviceRulesEngine.solveGridForTerm(request)(_traceId))
          }
        }

      }
    }

}
