openapi: 3.0.0
info:
  title: Sales fee rules engine service
  version: 1.0.0
x-basepath: /simon
x-kong-service-defaults:
  read_timeout: 300000
x-kong-plugin-aws-lambda:
  config:
    function_name: service-sales-fee-rules-engine-${STAGE}-api
    aws_region: us-east-1
    invocation_type: RequestResponse
    awsgateway_compatible: true
    forward_request_body: true
    forward_request_headers: true
    forward_request_method: true
    forward_request_uri: true
    is_proxy_integration: true
    skip_large_bodies: false
    proxy_url: ${HTTP_PROXY}
    proxy_scheme: https
    log_type: None
tags:
  - name: salesfeeengine
    description: Sales fee rules engine service

paths:
  /api/v1/sales-fee-rules-engine/info:
    x-kong-route-defaults:
      regex_priority: 999
    get:
      summary: Service Info
      responses:
        200:
          description: Service info

  /api/v1/sales-fee-rules-engine/healthcheck:
    x-kong-route-defaults:
      regex_priority: 999
    get:
      summary: Service healthcheck
      responses:
        200:
          description: Service healthcheck

  /api/v1/sales-fee-rules-engine/uptime:
    x-kong-route-defaults:
      regex_priority: 999
    get:
      summary: Service uptime
      responses:
        200:
          description: Time elapsed since the service was up and running

  /api/v1/sales-fee-rules-engine:

    x-disabled-plugins: [ request-validator ]
    x-kong-route-defaults:
      regex_priority: 998
    x-scopes:
      - admin
      - fa-manager
      - internal
    post:
      summary: Evaluate contract params to get the matched grid schedule for the provided networkId
      tags:
        - salesfeeengine
      responses:
        200:
          description: Sales fee grid


  /api/v1/sales-fee-rules-engine/calculate:
    x-disabled-plugins: [ request-validator ]
    x-kong-route-defaults:
      regex_priority: 997
    x-scopes: [admin]
    post:
      summary: Calculate exact fee for provided networkId and contract
      tags:
        - salesfeeengine
      responses:
        200:
          description: Sales Fee calculation