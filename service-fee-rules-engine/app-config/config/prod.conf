runMode {
  type = lambda-mode
}

akka.http {
    server {
        request-timeout = 60 s
    }
}

config.resolvers {
    file {
      root="/var/cv/creds"
    }
    sm {
      region="us-east-1"
      credentialsProvider = "webIdentityToken"
    }
    ssm {
      region="us-east-1"
      credentialsProvider = "webIdentityToken"
    }
}

authentication {
  type = "jwt"
  jwt {
    source {
      type = "cookieOrHeader"
      name = "SimSSO:X-Simon-AccessToken"
    }
    validator {
      issuerUrl = "sm:applicationconfig-issuer-uri"
      audience = "simonmarkets.com,0oa12xiv7qX7pWw3B2p7"
      clockOffsetInSeconds = 0
      proxyHost = "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
      proxyPort = 3128
    }
  }
  userInfo {
    source {
      type = ""
      name = ""
    }
  }
}

systemRoutes {
  healthCheck {
    path = "simon/api/v1/sales-fee-rules-engine/healthcheck"
  }
  serviceInfo {
    path = "simon/api/v1/pricing/valuations/overnight/info"
  }
  serviceUp {
    path = "simon/api/v1/pricing/valuations/overnight/uptime"
  }
}

mongoDB {
  salesFeeRulesSnapshots {
    collection = "SalesFeeRules.snapshots"
    database = "pipg"
  }
  salesFeeRules {
    collection = "SalesFeeRules"
    database = "pipg"
  }
  networks {
    collection = "networks_new"
    database = "pipg"
  }
  networksSnapshots {
    collection = "networks_new.snapshots"
    database = "pipg"
  }
  client {
    appName = "service-sales-fee-rules-engine"
    connection = "sm:config:applicationconfig-mongo-auth-pipg"
  }

}

aclClientConfig {
  httpClient {
    auth: {
      type: OAuth
      clientId: "0OA2H4RE4YM0fH3S82P7"
      clientSecret: "sm:applicationconfig-oktaclientsecret"
      clientSecretFile: ""
      site = "sm:applicationconfig-issuer-uri"
      authorizationPath: ""
      tokenPath = "/v1/token"
      scope: "read_product_data"
      tokenType: {
        type: Cookie
        name: SimSSO
      }
      proxy: {
        address: "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
        port: 3128
      }
    },
    proxy: {
      address: "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
      port: 3128
    }
  }
  baseUrl = "https://origin-dc1.api.simonmarkets.com/simon/api"
  cacheConfig {
    enabled = true
    config {
      serviceEndpoint = "https://dynamodb.us-east-1.amazonaws.com/"
      signinRegion = "us-east-1"
    }
  }
}


apiPrefix = "https://origin-dc1.api.simonmarkets.com/simon/api"
assetApiPath = "/v2/assets"
networkEntitiesPath = ""
