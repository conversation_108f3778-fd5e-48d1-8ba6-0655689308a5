runMode {
  type = lambda-mode
}

akka.http {
    server {
        request-timeout = 60 s
    }
}

config.resolvers {
    file {
      root=""
    }
    sm {
      region="us-east-1"
      credentialsProvider = "webIdentityToken"
    }
    ssm {
      region="us-east-1"
      credentialsProvider = "webIdentityToken"
    }
}

authentication {
  type = "jwt"
  jwt {
    source {
      type = "cookieOrHeader"
      name = "SimSSO:X-Simon-AccessToken"
    }
    validator {
      issuerUrl = "sm:applicationconfig-issuer-uri"
      keysUrl = "sm:applicationconfig-okta-validator"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clockOffsetInSeconds = 0
    }
  }
  userInfo {
    source {
      type = "header"
      name = "x-userinfo"
    }
  }
}

systemRoutes {
  healthCheck {
    path = "simon/api/v1/sales-fee-rules-engine/healthcheck"
  }
  serviceInfo {
    path = "simon/api/v1/sales-fee-rules-engine/info"
  }
  serviceUp {
    path = "simon/api/v1/sales-fee-rules-engine/uptime"
  }
}

mongoDB {
  salesFeeRulesSnapshots {
    collection = "SalesFeeRules.snapshots"
    database = "pipg"
  }
  salesFeeRules {
    collection = "SalesFeeRules"
    database = "pipg"
  }
  networks {
    collection = "networks_new"
    database = "pipg"
  }
  networksSnapshots {
    collection = "networks_new.snapshots"
    database = "pipg"
  }
  client {
    appName = "service-sales-fee-rules-engine"
    connection = "sm:config:applicationconfig-mongo-auth-pipg"
  }

}


aclClientConfig {
  httpClient {
    auth {
      type: OAuth
      clientId: "0oaevl54gplnFvyx70h7"
      clientSecret: "sm:Okta-secret"
      clientSecretFile: ""
      site = "sm:applicationconfig-issuer-uri"
      authorizationPath: ""
      tokenPath = "/v1/token"
      scope: "read_user_profile read_product_data"
      tokenType: {
        type: Cookie
        name: SimSSO
      }
    }
  }
  baseUrl = "https://origin-a.dev.simonmarkets.com/simon/api"
  cacheConfig {
    enabled = true
    config {
      serviceEndpoint = "https://dynamodb.us-east-1.amazonaws.com/"
      signinRegion = "us-east-1"
    }
  }
}

apiPrefix = "https://origin-a.dev.simonmarkets.com/simon/api"
assetApiPath = "/v2/assets"
networkEntitiesPath = ""
