package com.simonmarkets.sales.fee.rules.common

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.circe.KeyEncoder
import io.simon.openapi.annotation.Field.EnumValues

@EnumValues("wholesaler", "homeOffice", "simon", "max", "faCommission", "structuring")
sealed trait FeeType extends EnumEntry {

  def name: String

  override def productPrefix: String = name

  override def toString: String = name
}

object FeeType extends ProductEnums[FeeType] {

  case object Wholesaler extends FeeType { def name: String = "wholesaler" }

  case object HomeOffice extends FeeType { def name: String = "homeOffice" }

  case object Simon extends FeeType  { def name: String = "simon" }

  case object Max extends FeeType  { def name: String = "max" }

  case object FACommission extends FeeType  { def name: String = "faCommission" }

  case object Structuring extends FeeType { def name: String = "structuring"}

  lazy val Values: Seq[FeeType] = Seq(Wholesaler, HomeOff<PERSON>, <PERSON>, <PERSON>, FACommission, Structuring)

  implicit val feeTypeKeyEncoder: KeyEncoder[FeeType] = new KeyEncoder[FeeType] {
    override def apply(key: FeeType): String = key.name
  }

  override lazy val CodeMap: Map[String, FeeType] =
    Values.map { x => x.productPrefix.toLowerCase -> x}.toMap + ("home" -> HomeOffice)
}
