package com.simonmarkets.sales.fee.rules.common.api

import com.simonmarkets.sales.fee.rules.common.{FeeSchedule, FeeType, SalesFeeRule}
import io.simon.openapi.annotation.Field.{EnumValues, Pattern, Ref, Required, Type, TypeArgRef}
import io.simon.openapi.definitions.CommonDefinitions
import com.simonmarkets.sales.fee.rules.common.api.SalesFeeRulesOpenApiDefinitions.ContractTypeWrapper
import io.simon.openapi.annotation.{OpenApiType, Reference}

case class UpdateFeeRuleRequest(
    @Required
    @Ref(CommonDefinitions.Symbol)
    id: String,
    @Required
    version: Int,
    @Required
    @Ref(CommonDefinitions.Symbol)
    name: String,
    @Required
    @EnumValues("wholesaler", "homeOffice", "simon", "max", "faCommission", "structuring")
    feeType: FeeType,
    @TypeArgRef(CommonDefinitions.Symbol)
    @Type(OpenApiType.Array)
    issuerKeys: Option[List[String]],
    @TypeArgRef(CommonDefinitions.Symbol)
    @Type(OpenApiType.Array)
    contractTypes: Option[List[String]],
    @TypeArgRef(ContractTypeWrapper)
    @Type(OpenApiType.Array)
    contractTypeWrappers: Option[List[String]],
    @Required
    fees: FeeSchedule,
    @Ref(CommonDefinitions.Symbol)
    comment: Option[String],
    @Ref(CommonDefinitions.Boolean)
    callable: Option[Boolean],
    @Ref(CommonDefinitions.Boolean)
    fullyProtected: Option[Boolean],
    @Ref(CommonDefinitions.Boolean)
    hasSinglePayment: Option[Boolean],
    @Ref(CommonDefinitions.PositiveBigDecimal)
    nonCallPeriodInMonths: Option[BigDecimal]
                               ) {

      def asEntity: SalesFeeRule = SalesFeeRule(
        id = id,
        version = version,
        name = name,
        deleted = false,
        feeType = feeType,
        issuerKeys = issuerKeys getOrElse Nil,
        contractTypes = contractTypes getOrElse Nil,
        contractTypeWrappers = contractTypeWrappers getOrElse Nil,
        fees = fees,
        callable = callable,
        fullyProtected = fullyProtected,
        hasSinglePayment = hasSinglePayment,
        nonCallPeriodInMonths = nonCallPeriodInMonths
      )
    }

case class FeeRuleNotFoundException(message: String) extends Exception

object SalesFeeRulesOpenApiDefinitions {

  @Type(OpenApiType.String)
  @Pattern("^(Note|CD)$")
  case object ContractTypeWrapper extends Reference

}
