package com.simonmarkets.sales.fee.rules.common

final case class SalesFeeRule(
    id: String,
    version: Int,
    name: String,
    feeType: FeeType,
    deleted: Boolean,
    issuerKeys: List[String],
    contractTypes: List[String], // payoff, investment type
    contractTypeWrappers: List[String],
    fees: FeeSchedule,
    callable: Option[Boolean],
    fullyProtected: Option[Boolean],
    hasSinglePayment: Option[Boolean],
    nonCallPeriodInMonths: Option[BigDecimal]
) {

  def increaseVersion: SalesFeeRule = copy(version = version + 1)
}
