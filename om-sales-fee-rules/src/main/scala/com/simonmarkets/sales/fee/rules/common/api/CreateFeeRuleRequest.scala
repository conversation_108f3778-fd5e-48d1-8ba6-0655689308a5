package com.simonmarkets.sales.fee.rules.common.api

import com.simonmarkets.sales.fee.rules.common.{FeeSchedule, FeeType, SalesFeeRule}
import io.simon.openapi.annotation.Field.{Ref, Required, Type, TypeArgRef}
import io.simon.openapi.annotation.OpenApiType
import io.simon.openapi.definitions.CommonDefinitions
import com.simonmarkets.sales.fee.rules.common.api.SalesFeeRulesOpenApiDefinitions.ContractTypeWrapper

import java.util.UUID

case class CreateFeeRuleRequest(
    @Required
    @Ref(CommonDefinitions.Symbol)
    name: String,
    @Required
    feeType: FeeType,
    @Type(OpenApiType.Array)
    @TypeArgRef(CommonDefinitions.Symbol)
    issuerKeys: Option[List[String]],
    @Type(OpenApiType.Array)
    @TypeArgRef(CommonDefinitions.Symbol)
    contractTypes: Option[List[String]],
    @Type(OpenApiType.Array)
    @TypeArgRef(ContractTypeWrapper)
    contractTypeWrappers: Option[List[String]],
    @Required
    fees: FeeSchedule,
    @Ref(CommonDefinitions.Symbol)
    comment: Option[String],
    @Ref(CommonDefinitions.Boolean)
    callable: Option[Boolean],
    @Ref(CommonDefinitions.Boolean)
    fullyProtected: Option[Boolean],
    @Ref(CommonDefinitions.Boolean)
    hasSinglePayment: Option[Boolean],
    @Ref(CommonDefinitions.PositiveBigDecimal)
    nonCallPeriodInMonths: Option[BigDecimal]
) {
    def asEntity = SalesFeeRule(
        id = UUID.randomUUID().toString,
        version = 0,
        name = name,
        deleted = false,
        feeType = feeType,
        issuerKeys = issuerKeys getOrElse Nil,
        contractTypes = contractTypes getOrElse Nil,
        contractTypeWrappers = contractTypeWrappers getOrElse Nil,
        fees = fees,
        callable = callable,
        fullyProtected = fullyProtected,
        hasSinglePayment = hasSinglePayment,
        nonCallPeriodInMonths = nonCallPeriodInMonths
    )
}
