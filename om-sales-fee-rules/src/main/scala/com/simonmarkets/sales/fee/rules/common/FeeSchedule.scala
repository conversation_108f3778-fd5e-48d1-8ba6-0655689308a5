package com.simonmarkets.sales.fee.rules.common

import com.simonmarkets.quantcommon.numeric.grid.interp.{FlatInterpRoundingMode, InterpFlat1D, Interpolator1D}
import com.simonmarkets.quantcommon.numeric.grid.{GridWithAxes1D, OneDimArray, SortedVector}
import com.simonmarkets.sales.fee.rules.common.FeeSchedule.{Fee, Maturity}
import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}
import io.circe.generic.semiauto._
import io.circe.{Decoder, Encoder, HCursor, Json}
import io.simon.openapi.annotation.Field.TypeArgRef
import io.simon.openapi.definitions.CommonDefinitions

sealed trait FeeScheduleTrait {
  val data: Map[Maturity, Fee]
}

object FeeScheduleTrait {
  import FeeScheduleFlatExtrapolator._
  import FeeSchedule._
  implicit val encoder : Encoder[FeeScheduleTrait] = deriveEncoder[FeeScheduleTrait]
  implicit val decoder : Decoder[FeeScheduleTrait] = deriveDecoder[FeeScheduleTrait]
}

final case class FeeScheduleFlatExtrapolator(
    data: Map[Maturity, Fee]
)(
    roundingMode: FlatInterpRoundingMode = FlatInterpRoundingMode.Right,
    extrapolateLeft: Boolean = true,
    extrapolateRight: Boolean = true
) extends InterpFlat1D(roundingMode, extrapolateLeft, extrapolateRight) with FeeScheduleTrait {

  private def interpolator: Interpolator1D[Double] = {
    new InterpFlat1D(roundingMode, extrapolateLeft, extrapolateRight)(grid)
  }

  private def grid: GridWithAxes1D[Double] = {
    val x = data.toSeq.sortBy(_._1)
    new GridWithAxes1D(SortedVector(x.map(_._1)), OneDimArray[Double](x.map(_._2.toDouble) : _*))
  }

  def fee(maturity: BigDecimal): Fee = BigDecimal(interpolator.apply(maturity.toDouble))
}

object FeeScheduleFlatExtrapolator {

  def create(data: Map[Maturity, Fee]): FeeScheduleFlatExtrapolator = {
    new FeeScheduleFlatExtrapolator(data)(FlatInterpRoundingMode.Right, true, true)
  }

  implicit val maturityOrdering: Ordering[Maturity] = Ordering[Int]

  implicit val feeScheduleEncoder: Encoder[FeeScheduleFlatExtrapolator] =
    feeSchedule => {
      val feeScheduleJson = feeSchedule.data.toSeq.sortBy(_._1) map {
        case (key : Maturity, value : Fee) => key.toString -> Json.fromBigDecimal(value)
      }
      Json.obj(feeScheduleJson: _*)
    }

  implicit val feeScheduleDecoder: Decoder[FeeScheduleFlatExtrapolator] = (c: HCursor) => for {
    data <- c.as[Map[Maturity, Fee]]
  } yield {
   new FeeScheduleFlatExtrapolator(data)(roundingMode  =FlatInterpRoundingMode.Right, extrapolateLeft =true, extrapolateRight =true)
  }
}
/** Fee Schedule data structure aims to hold a map of
 * Maturity to Fee.
 *
 * Example:
 * ```
 * FeeSchedule(
 *   12 -> 2,
 *   24 -> 1.5)
 * ```
 *
 * This example means that we have defined fees on range from 12 months to 24 months and later.
 *
 * @see [[fee]] method for more details.
 * @param data Fee Schedule data
 */

case class FeeSchedule(
    @TypeArgRef(CommonDefinitions.SanitizedNumber)
    data: Map[Maturity, Fee]) extends FeeScheduleTrait {

  private lazy val monthsSorted: Seq[Int] = data.keys.toSeq.sorted

  private lazy val min = if (monthsSorted.nonEmpty) monthsSorted.min else -1

  private lazy val max = if (monthsSorted.nonEmpty) monthsSorted.max else -1

  /** Returns calculation of Fee based on provided Fee Schedule.
   * Expected results for
   * ```
   * FeeSchedule(
   *   12 -> 2,
   *   24 -> 1.5)
   * ```
   * should be as follows:
   * - 0                - 12 (inclusively) month -> Some(2)
   * - 12 (exclusively) - 24 (inclusively) month -> Some(1.5)
   * - 24 (exclusively) -> ∞ month               -> None
   *
   * @param maturity Disired maturity
   * @return
   */
  def fee(maturity: Maturity, roundOp: (Int, Int) => Boolean = _ >= _): Option[BigDecimal] = {
    def compute = monthsSorted collectFirst {
      case x if roundOp(x,maturity) => data(x)
    }
    if (roundOp(2, 1)) // if roundUp then default
      if (maturity <= min) data.get(min)
      else if (maturity > max) None
      else compute
    else {
      if (maturity <= min) data.get(min)
      else if (maturity > max) data.get(max)
      else compute
    }
  }

  /** Adds Fee specified by maturity to Fee Schedule
   *
   * @param rec tuple of maturity and fee
   * @return updated FeeSchedule
   */
  def updated(rec: (Maturity, Fee)): FeeSchedule = FeeSchedule(data + rec)

  /** Adds Fees specified by maturities (which is an essence of FeeSchedule) to this Fee Schedule
   *
   * @param other Fee Schedule
   * @return updated FeeSchedule
   */
  def updated(other: FeeSchedule): FeeSchedule = FeeSchedule(data ++ other.data)

  def withFeesIncremented(delta: Fee): FeeSchedule = FeeSchedule(data = data.mapValues(_ + delta))

  def months: Set[Maturity] = data.keySet
}

object FeeSchedule {
  type Maturity = Int // amount of month
  type Fee = BigDecimal

  lazy val empty: FeeSchedule = FeeSchedule()

  def apply(xs: (Maturity, Fee)*): FeeSchedule = new FeeSchedule(xs.toMap)

  def apply[T](x: (Int, T))(implicit num: Numeric[T]): FeeSchedule = {
    val (maturity, fee) = x
    new FeeSchedule(Map(maturity -> num.toDouble(fee)))
  }

  case class Entry(inclusiveMaxMaturityMonths: Double, fee: Double)

  implicit val feeScheduleDecoder: Decoder[FeeSchedule] = (c: HCursor) => for {
    data <- c.as[Map[Maturity, Fee]]
  } yield {
    FeeSchedule(data)
  }

  implicit val feeScheduleEncoder: Encoder[FeeSchedule] =
    feeSchedule => {
      val feeScheduleJson = feeSchedule.data.map {
        case (key, value) => key.toString -> Json.fromBigDecimal(value) }.toList
      Json.obj(feeScheduleJson: _*)
    }
}

